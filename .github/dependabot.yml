version: 2
updates:

  # Maintain dependencies for pnpm
  - package-ecosystem: "npm"
    directory: "/client"
    schedule:
      interval: "weekly"
    # Limit open pull requests
    open-pull-requests-limit: 1
    allow:
      # Allow both direct and indirect updates for all packages
      - dependency-type: "all"
    groups:
       # Specify a name for the group, which will be used in pull request titles
       # and branch names
       dev-dependencies:
          # Define patterns to include dependencies in the group (based on
          # dependency name)
          patterns:
            - "*"       # A wildcard that matches all dependencies in the package
                        # ecosystem. Note: using "*" may open a large pull request
          # Define patterns to exclude dependencies from the group (based on
          # dependency name)
          # exclude-patterns:
          #  - "gc_ruboconfig"
          #  - "gocardless-*"
    ignore:
      # ignore all major updates
      - dependency-name: "*"
        update-types: [ "version-update:semver-major" ]
    labels:
      - "skip-behat"

  # Maintain dependencies for Composer
  - package-ecosystem: "composer"
    directory: "/api"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 1
    allow:
      - dependency-type: "all"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-major" ]
      - dependency-name: "symfony/*"
        update-types: [ "version-update:semver-major", "version-update:semver-minor" ]
    groups:
      symfony:
        patterns:
          - "symfony/*"
      non-symfony:
        patterns:
          - "*"
    labels:
      - "skip-behat"

