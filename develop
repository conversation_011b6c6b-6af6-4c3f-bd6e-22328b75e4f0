#!/usr/bin/env bash
set -a

source ./api/.env
if [ -f ./api/.env.local ]; then
    source ./api/.env.local
fi

export COMPOSE_DOCKER_CLI_BUILD=1
export DOCKER_BUILDKIT=1

COMPOSE="docker compose -f compose.yml"

if [ "$APP_ENV" = "behat" ]; then
  COMPOSE="$COMPOSE -f compose.behat.yml"
fi

if [ "$RUN_STORYBOOK" = "true" ]; then
  COMPOSE="$COMPOSE -f compose.storybook.yml"
fi

echo "$COMPOSE"

if [ $# -gt 0 ]; then

    # "console" calls symfony console and passes any extra arguments
    if [ "$1" == "console" ]; then
        shift 1
        $COMPOSE exec \
            php \
            bin/console "$@"

    # "tenants_console" calls symfony console for each tenant and passes any extra arguments
    elif [ "$1" == "tenants_console" ]; then
        shift 1
        $COMPOSE exec \
            php \
            bin/tenants_console "$@"

    # "console" calls symfony console and passes any extra arguments
    elif [ "$1" == "tenants_console" ]; then
        shift 1
        $COMPOSE exec \
            php \
            ./bin/tenants_console "$@"

    # "composer" calls composer and passes any extra arguments
    elif [ "$1" == "composer" ]; then
        shift 1
        $COMPOSE exec \
            php \
            composer "$@"

    # "phpunit" calls phpunit and passes any extra arguments
    elif [ "$1" == "phpunit" ]; then
        shift 1
        $COMPOSE exec \
            php \
            ./vendor/bin/phpunit "$@"

    # "php-cs-fixer" calls php-cs-fixer and passes any extra arguments
    elif [ "$1" == "php-cs-fixer" ]; then
        shift 1
        $COMPOSE exec \
            php \
            sh scripts/dev/csfix.sh "$@"

    # "behat" calls behat and passes any extra arguments (uses full stack)
    elif [ "$1" == "behat" ]; then
        if [ "$APP_ENV" != "behat" ]; then
            echo "ERROR! You must set APP_ENV to \"behat\""
            exit 0
        fi
        shift 1
        $COMPOSE exec \
            php \
            ./vendor/bin/behat "$@"

    # "vitest" calls vitest and passes any extra arguments
    elif [ "$1" == "vitest" ]; then
        shift 1
        $COMPOSE exec \
            node \
            vitest "$@"

    elif [ "$1" == "pnpm" ]; then
        shift 1
        $COMPOSE exec \
            node \
            pnpm "$@"

    else
        $COMPOSE "$@"
    fi
else
    $COMPOSE ps
fi
