security:
  access_control:
    - { path: ^/api/auth/request-password-reset, roles: PUBLIC_ACCESS }
    - { path: ^/api/auth/reset-password, roles: PUBLIC_ACCESS }
    - { path: ^/api/about, roles: PUBLIC_ACCESS }
    - { path: ^/api/docs, roles: ROLE_API }
    - { path: ^/api/dynamic-assets/background-image, roles: PUBLIC_ACCESS }
    - { path: ^/api/dynamic-assets/login-logo, roles: PUBLIC_ACCESS }
    - { path: ^/api/app-status, roles: PUBLIC_ACCESS }
    - { path: ^/api/support-info, roles: PUBLIC_ACCESS }
    - { path: ^/api/system-messages, roles: PUBLIC_ACCESS }
    - { path: ^/api/tenant$, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/dynamic-assets, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/insecure, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/menu, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/refresh-jwt, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/security/two-factor, role: IS_AUTHENTICATED_2FA_IN_PROGRESS }
    - { path: ^/legacy/support, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/system-message-display, roles: PUBLIC_ACCESS }
    - { path: ^/legacy/translations, roles: PUBLIC_ACCESS }
    - { path: ^/, roles: IS_AUTHENTICATED_REMEMBERED }

  access_decision_manager:
    allow_if_all_abstain: false
    strategy: unanimous

  password_hashers:
    U2\Entity\User: 'auto'

  firewalls:
    api:
      pattern: ^/api(?!\/docs|\/about|\/login|\/logout)
      # TODO: Remove this environment variable when we do not have to override locally
      # You can set this to `false` in your .env.local for local development to avoid
      # the exception `Session was used while the request was declared stateless.`
      stateless: '%env(bool:API_STATELESS)%'
      provider: main
      user_checker: U2\Security\Authentication\UserChecker
      access_token:
        token_handler: U2\Security\Authentication\Api\ApiKeyTokenHandler
        token_extractors: u2.security.access_token_extractor.api_key
      jwt: ~
    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false

    secured_area:
      provider: main
      json_login:
        check_path: login
        # form_login options relevant to login success / failure need to be set
        # in the service definitions of our custom handlers to take effect.
        # Available options are defined in our service classes' parent constructors.
        failure_handler: U2\Security\Authentication\AuthenticationFailureHandler
        success_handler: U2\Security\Authentication\AuthenticationSuccessHandler
      logout:
        path: /api/logout
      pattern: ^/
      remember_me:
        secret: '%env(APP_SECRET)%'
        path: /
      user_checker: U2\Security\Authentication\UserChecker
      two_factor:
        authentication_required_handler: U2\Security\Authentication\TwoFactor\TwoFactorAuthenticationRequiredHandler
        failure_handler: U2\Security\Authentication\AuthenticationFailureHandler
        success_handler: U2\Security\Authentication\AuthenticationSuccessHandler
        auth_code_parameter_name: _auth_code
        auth_form_path: two_factor_authentication_login
        check_path: two_factor_authentication_login_check
        post_only: true
        prepare_on_login: true
        prepare_on_access_denied: true
  providers:
    main:
      entity:
        class: U2\Entity\User
        property: 'username'

  role_hierarchy:
    ROLE_ADMIN:
      - ROLE_API
      - ROLE_USER
      - ROLE_USER_GROUP_ADMIN
      - ROLE_PERIOD_MANAGER
      - ROLE_UNIT_MANAGER
    ROLE_USER_GROUP_ADMIN:
      - ROLE_USER
    ROLE_PERIOD_MANAGER:
      - ROLE_USER
    ROLE_API:
      - ROLE_USER

when@test:
  security:
    password_hashers:
      U2\Entity\User: 'plaintext' # This should make the setup easier and the tests faster
    firewalls:
      api:
        # Force the "api' firewall to be stateless otherwise the tests will fail
        stateless: true

when@test_security:
  security:
    password_hashers:
      U2\Entity\User: 'auto'

when@behat:
  security:
    password_hashers:
      U2\Entity\User: 'plaintext' # This should make the setup easier and the tests faster
