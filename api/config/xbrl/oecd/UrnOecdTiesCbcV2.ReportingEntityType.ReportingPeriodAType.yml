U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\ReportingEntityType\ReportingPeriodAType:
  properties:
    startDate:
      expose: true
      access_type: public_method
      serialized_name: StartDate
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getStartDate
        setter: setStartDate
      type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\Date
    endDate:
      expose: true
      access_type: public_method
      serialized_name: EndDate
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getEndDate
        setter: setEndDate
      type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\Date
