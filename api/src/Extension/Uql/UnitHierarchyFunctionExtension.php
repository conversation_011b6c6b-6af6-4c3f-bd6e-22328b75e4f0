<?php

declare(strict_types=1);
namespace U2\Extension\Uql;

use U2\DataSourcery\Extension\UqlExtensionInterface;
use U2\DataSourcery\Extension\UqlFunction;
use U2\Entity\Unit;
use U2\Repository\UnitHierarchyRepository;
use U2\Unit\Hierarchy\Snapshot;
use U2\Unit\Hierarchy\SnapshotFactory;

class UnitHierarchyFunctionExtension implements UqlExtensionInterface
{
    public const string SNAPSHOT_DATE_FORMAT = 'Y-m-d';

    public function __construct(private readonly SnapshotFactory $snapshotFactory, private readonly UnitHierarchyRepository $hierarchyRepository)
    {
    }

    public function getFunctions(): array
    {
        return [
            'hierarchyUnits' => new UqlFunction('hierarchyUnits', $this, 'hierarchyUnits'),
        ];
    }

    public function hierarchyUnits(string $hierarchyName, string $snapshotDate): array
    {
        $newSnapshotDate = \DateTime::createFromFormat(self::SNAPSHOT_DATE_FORMAT, trim($snapshotDate, '\'"'));
        if (false === $newSnapshotDate) {
            // support legacy dot format
            $newSnapshotDate = \DateTime::createFromFormat('Y.m.d', trim($snapshotDate, '\'"'));
            if (false === $newSnapshotDate) {
                throw new \BadFunctionCallException('The date provided to the hierarchyUnits() function must be formatted as: ' . self::SNAPSHOT_DATE_FORMAT);
            }
        }

        $hierarchy = $this->hierarchyRepository->findOneBy(['name' => trim($hierarchyName, '\'"')]);
        if (null === $hierarchy) {
            throw new \BadFunctionCallException("No hierarchy with name '$hierarchyName' was found for function hierarchyUnits()");
        }

        $snapshot = $this->snapshotFactory->create($hierarchy, $newSnapshotDate);
        $units = $this->extractFlatUnitList($snapshot);
        $unitRefIds = [];

        foreach ($units as $unit) {
            $unitRefIds[] = $unit->getRefId();
        }

        return $unitRefIds;
    }

    /**
     * @return Unit[]
     */
    private function extractFlatUnitList(Snapshot $snapshot): array
    {
        $rootUnits = $snapshot->getTopLevelUnits();
        $units = [];

        $walkNestedUnitArrayFunction = static function (Unit $unit) use ($snapshot, &$units, &$walkNestedUnitArrayFunction): void {
            $units[] = $unit;
            $children = $snapshot->getChildUnits($unit);
            foreach ($children as $child) {
                $walkNestedUnitArrayFunction($child);
            }
        };

        foreach ($rootUnits as $unit) {
            $walkNestedUnitArrayFunction($unit);
        }

        return array_unique($units);
    }
}
