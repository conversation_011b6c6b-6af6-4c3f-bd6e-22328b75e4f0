<?php

declare(strict_types=1);
namespace U2\Security\Authorization;

use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use U2\Entity\Task\Task;
use U2\Entity\Task\TaskType;
use U2\Entity\User;
use U2\Security\Permissions\Assignable\PermissionableEntity;
use U2\Security\Permissions\PermissionChecker;
use U2\Task\TaskTypeKnowledge;
use U2\Task\TaskTypeResolver;

readonly class AssignmentHelper
{
    public function __construct(
        private AuthorizationManager $authorizationManager,
        private PermissionChecker $permissionChecker,
        private TaskTypeResolver $taskTypeResolver,
    ) {
    }

    public function canTaskBeAssignedToUser(Task $task, User $user): bool
    {
        $taskType = $this->taskTypeResolver->resolve($task);

        return $this->canRecordBeAssignedToUser($taskType, $user);
    }

    public function canRecordBeAssignedToUser(TaskType $taskType, User $user): bool
    {
        if (false === $this->userHasAuthorisation($user, $taskType)) {
            return false;
        }

        if ($taskType instanceof PermissionableEntity && false === $this->userHasPermissions($user, $taskType)) {
            return false;
        }

        return false === (null !== $taskType->getUnit() && false === $this->userHasRightToUnit($user, $taskType));
    }

    private function userHasAuthorisation(User $user, TaskType $entity): bool
    {
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entity::class];

        return $this->authorizationManager->isAuthorized($user, $authorizationItem->value, AuthorizationRight::READ->value);
    }

    private function userHasPermissions(User $user, PermissionableEntity $entity): bool
    {
        return $this->permissionChecker->isUserGrantedTo($entity, $user, MaskBuilder::MASK_VIEW);
    }

    private function userHasRightToUnit(User $user, TaskType $issue): bool
    {
        return \in_array($issue->getUnit(), $user->getUnits()->toArray(), true)
            || \in_array($issue->getUnit(), $user->getGroupUnits(), true);
    }
}
