<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AccessDecisionManagerInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\User;
use U2\Security\Authorization\AuthorizationRight;
use U2\Table\Table;
use U2\Task\TaskTypeKnowledge;

/**
 * @extends Voter<VoterAttributes::read, Table>
 */
class TableVoter extends Voter
{
    public function __construct(private readonly AccessDecisionManagerInterface $accessDecisionManager)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        return VoterAttributes::read === $attribute;
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, Table::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    /**
     * @param Table $subject
     */
    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();
        if (!$user instanceof User) {
            return false;
        }

        $class = $subject->getDataSource()->getEntityClass();
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$class];

        return $this->accessDecisionManager->decide($token, ["$authorizationItem->value:" . AuthorizationRight::READ->value]);
    }
}
