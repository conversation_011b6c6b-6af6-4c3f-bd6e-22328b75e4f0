<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\User\CurrentUserProvider;

/**
 * @extends Voter<VoterAttributes::write, User>
 */
class UserVoter extends Voter
{
    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly Security $security,
    ) {
    }

    public function supportsAttribute(string $attribute): bool
    {
        return VoterAttributes::write === $attribute;
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, User::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        if ($this->loggedInUserIsUserAdmin()) {
            return true;
        }

        return $this->isLoggedInUser($subject);
    }

    public function isLoggedInUser(User $user): bool
    {
        return $user->getId() === $this->currentUserProvider->get()->getId();
    }

    public function loggedInUserIsUserAdmin(): bool
    {
        return $this->security->isGranted(UserRoles::UserGroupAdmin->value);
    }
}
