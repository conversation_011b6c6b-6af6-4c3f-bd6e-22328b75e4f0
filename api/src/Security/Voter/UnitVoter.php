<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\Unit\Assignment\UserUnitAssignmentChecker;

/**
 * @extends Voter<VoterAttributes::addAttachment|VoterAttributes::delete|VoterAttributes::read|VoterAttributes::removeAttachment|VoterAttributes::write, Unit|class-string<Unit>>
 */
class UnitVoter extends Voter
{
    public function __construct(
        private readonly Security $security,
        private readonly UserUnitAssignmentChecker $userUnitAssignmentChecker,
    ) {
    }

    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            VoterAttributes::addAttachment,
            VoterAttributes::removeAttachment,
            VoterAttributes::delete,
            VoterAttributes::read,
            VoterAttributes::write,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return is_a($subject, Unit::class, true);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        if ($this->security->isGranted(UserRoles::UnitManager->value)) {
            return true;
        }

        switch ($attribute) {
            case VoterAttributes::read:
                return $this->userUnitAssignmentChecker->check($user, [$subject]);
            case VoterAttributes::addAttachment:
            case VoterAttributes::removeAttachment:
            case VoterAttributes::write:
                if ($subject instanceof Unit && false === $subject->isNew()) {
                    return $this->userUnitAssignmentChecker->check($user, [$subject]);
                }

                return false;
            case VoterAttributes::delete:
                return false;
        }
    }
}
