<?php

declare(strict_types=1);
namespace U2\Security\Voter\Task;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\Task\Task;
use U2\Entity\Task\TaskType;
use U2\Entity\User;
use U2\Security\Voter\VoterAttributes;
use U2\Task\TaskTypeClassMap;

/**
 * @extends Voter<VoterAttributes::addAttachment|VoterAttributes::delete|VoterAttributes::read|VoterAttributes::removeAttachment|VoterAttributes::removeReview|VoterAttributes::review|VoterAttributes::write, Task>
 */
class TaskVoter extends Voter
{
    public function __construct(private readonly AuthorizationCheckerInterface $authorizationChecker, private readonly EntityManagerInterface $entityManager)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            VoterAttributes::assign,
            VoterAttributes::addAttachment,
            VoterAttributes::delete,
            VoterAttributes::read,
            VoterAttributes::removeAttachment,
            VoterAttributes::removeReview,
            VoterAttributes::review,
            VoterAttributes::write,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, Task::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        /** @var Task $subject */
        $repository = $this->entityManager->getRepository(TaskTypeClassMap::getClass($subject->getType()));

        $taskType = $repository->findOneBy(['task' => $subject]);
        \assert($taskType instanceof TaskType);

        // Delegate the authorization check to the task type
        return $this->authorizationChecker->isGranted($attribute, $taskType);
    }
}
