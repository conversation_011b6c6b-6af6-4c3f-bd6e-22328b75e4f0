<?php

declare(strict_types=1);
namespace U2\Security\Voter;

class VoterAttributes
{
    public const string addAttachment = 'VoterAttributes.ADD_ATTACHMENT';

    public const string removeAttachment = 'VoterAttributes.REMOVE_ATTACHMENT';

    public const string read = 'VoterAttributes.READ';
    public const string assign = 'VoterAttributes.ASSIGN';

    public const string write = 'VoterAttributes.WRITE';

    public const string delete = 'VoterAttributes.DELETE';

    public const string review = 'VoterAttributes.REVIEW';

    public const string removeReview = 'VoterAttributes.REMOVE_REVIEW';

    public const string manage = 'VoterAttributes.MANAGE';

    public const string import = 'VoterAttributes.IMPORT';
}
