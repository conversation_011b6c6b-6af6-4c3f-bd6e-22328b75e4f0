<?php

declare(strict_types=1);
namespace U2\DataSourcery\Query;

/**
 * Defines a sorting condition of a single column.
 */
class SortCondition
{
    public const string ASC = 'ASC';

    public const string DESC = 'DESC';

    /**
     * @param string $fieldName column identifier to sort by
     * @param string $direction sort direction: ASC or DESC
     */
    public function __construct(private readonly string $fieldName, private readonly string $direction)
    {
    }

    public function getFieldName(): string
    {
        return $this->fieldName;
    }

    public function getDirection(): string
    {
        return $this->direction;
    }
}
