<?php

declare(strict_types=1);
namespace U2\DataSourcery\Query;

class Pagination
{
    /**
     * @var int
     */
    public const int defaultCount = 20;

    private int $count;

    private int $offset;

    public function __construct(private readonly int $page = 0, int $count = self::defaultCount)
    {
        $this->count = $count > 0 ? $count : self::defaultCount;

        // The item offset form the beginning of the item collection
        $this->offset = $page * $count;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function withPage(int $page): self
    {
        return new self($page, $this->count);
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function withCount(int $count): self
    {
        return new self($this->page, $count);
    }
}
