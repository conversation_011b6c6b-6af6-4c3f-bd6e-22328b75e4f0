<?php

declare(strict_types=1);
namespace U2\DataSourcery\Query;

use U2\Util\ImmutableCollection;

class Filter implements \JsonSerializable, FilterInterface
{
    /**
     * The filters are concatenated AND-wise.
     */
    public const string CONDITION_TYPE_AND = 'AND';

    /**
     * The filters are concatenated OR-wise.
     */
    public const string CONDITION_TYPE_OR = 'OR';

    /**
     * The filters are concatenated XOR-wise
     * Note: This is not currently supported by <PERSON><PERSON>, but left in for completeness.
     */
    public const string CONDITION_TYPE_XOR = 'XOR';

    private string $uql;

    private ImmutableCollection $conditions;

    public function __construct(array $elements = [], private readonly string $conditionType = self::CONDITION_TYPE_AND, ?string $uql = null)
    {
        $this->conditions = new ImmutableCollection($elements);
        $this->uql = $uql ?? $this->toUQL();
    }

    public function getUql(): string
    {
        return $this->uql;
    }

    public function jsonSerialize(): array
    {
        return [
            'logic' => $this->getConditionType(),
            'elements' => $this->conditions
                ->map(fn ($condition) => $condition->jsonSerialize())
                ->toArray(),
        ];
    }

    public function getConditionType(): string
    {
        return $this->conditionType;
    }

    /**
     * @return FilterCondition[]
     */
    public function getAllFilterConditionsFlat(): array
    {
        $flatConditionsList = [];
        $flatten = static function (self $filterDefinition) use (&$flatConditionsList, &$flatten): void {
            foreach ($filterDefinition->getConditions() as $filter) {
                if ($filter instanceof self) {
                    $flatten($filter);
                } elseif ($filter instanceof FilterCondition) {
                    $flatConditionsList[] = $filter;
                }
            }
        };
        $flatten($this);

        return $flatConditionsList;
    }

    private function toUQL(): string
    {
        $parts = [];
        foreach ($this->conditions as $filter) {
            $subFilterUql = $filter->getUql();
            if ($filter instanceof self) {
                $subFilterUql = '(' . $subFilterUql . ')';
            }
            $parts[] = $subFilterUql;
        }

        return implode(' ' . strtolower($this->conditionType) . ' ', $parts);
    }

    public function getConditions(): ImmutableCollection
    {
        return $this->conditions;
    }
}
