<?php

declare(strict_types=1);
namespace U2\Subscription\SavedFilter;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Twig\Environment;
use U2\DataSourcery\DataSource\Configuration\DataSourceConfigurationInterface;
use U2\DataSourcery\Query\InvalidFilter;
use U2\Entity\SavedFilterSubscription;
use U2\Entity\User;
use U2\Exception\EmptySubscriptionException;
use U2\Exception\Exception;
use U2\Exception\InvalidSubscriptionException;
use U2\Exception\SubscriptionAuthorizationException;
use U2\Security\CurrentUserManipulator;
use U2\Security\Voter\VoterAttributes;
use U2\Subscription\SubscriptionInterface;
use U2\Subscription\SubscriptionRendererInterface;
use U2\Table\SavedFilter\RoutingHelper;
use U2\Table\SavedFilter\SavedFilterDefinitionsCollection;
use U2\Table\TableFactory;
use U2\Task\TaskTypeKnowledge;
use U2\User\CurrentUserProvider;

class SavedFilterSubscriptionRenderer implements SubscriptionRendererInterface
{
    private ?User $executingUser = null;

    public function __construct(
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly Environment $templatingEngine,
        private readonly SavedFilterDefinitionsCollection $savedFilterDefinitionsCollection,
        private readonly TableFactory $tableFactory,
        private readonly RoutingHelper $routingHelper,
        private readonly CurrentUserManipulator $currentUserManipulator,
        private readonly CurrentUserProvider $currentUserProvider,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function renderSubscriptionContent(SubscriptionInterface $subscription, User $recipient): string
    {
        if (!($subscription instanceof SavedFilterSubscription)) {
            throw new Exception('Invalid subscription type for this renderer');
        }

        // We change the security token to proceed to the render as if we were the recipient of the message.
        $this->switchSecurityContextToken($recipient);

        try {
            return $this->render($subscription, $recipient);
        } finally {
            // We *must* restore the original token before exiting, even if the render crashes hard.
            $this->resetSecurityContextToken();
        }
    }

    private function switchSecurityContextToken(User $recipient): void
    {
        if ($this->currentUserProvider->hasUser()) {
            $this->executingUser = $this->currentUserProvider->get();
        }

        $this->currentUserManipulator->change($recipient);
    }

    private function resetSecurityContextToken(): void
    {
        if (null !== $this->executingUser) {
            $this->currentUserManipulator->change($this->executingUser);

            return;
        }

        $this->currentUserManipulator->remove();
    }

    /**
     * @throws EmptySubscriptionException
     * @throws SubscriptionAuthorizationException
     * @throws InvalidSubscriptionException
     */
    private function render(SavedFilterSubscription $subscription, User $recipient): string
    {
        $savedFilter = $subscription->getSavedFilter();
        $table = $this->tableFactory->createFromSavedFilter($savedFilter);
        $filter = $table->getState()->getQuery()->getFilter();
        if ($filter instanceof InvalidFilter) {
            throw new InvalidSubscriptionException($filter->getError());
        }
        if (!$this->authorizationChecker->isGranted(VoterAttributes::read, $table)) {
            throw new SubscriptionAuthorizationException($recipient->getUsername() . ' is not allowed to view the table content.');
        }

        $state = $table->getState();
        $query = $state->getQuery();
        $pagination = $query->getPagination()->withCount(\PHP_INT_MAX);
        $query = $query->withPagination($pagination);

        $state->setQuery($query);

        if (0 === \count($table->getData())) {
            throw new EmptySubscriptionException();
        }

        $tableViewConfigurationFullyQualifiedClass = $savedFilter->getTableViewConfigurationFullyQualifiedClass();
        \assert(null !== $tableViewConfigurationFullyQualifiedClass);

        $readableLayoutName = $this
            ->savedFilterDefinitionsCollection
            ->tableViewConfigurationFullyQualifiedClassToReadableName(
                $tableViewConfigurationFullyQualifiedClass
            );

        $dataSourceConfiguration = $this->savedFilterDefinitionsCollection
            ->tableViewConfigurationFullyQualifiedClassToDataSourceConfiguration($tableViewConfigurationFullyQualifiedClass);
        \assert($dataSourceConfiguration instanceof DataSourceConfigurationInterface);

        $entityClass = $dataSourceConfiguration::getEntityClass();
        $routes = TaskTypeKnowledge::getAvailableRoutes($entityClass);

        return $this->templatingEngine->render(
            'email/saved_filter_subscription.email.html.twig',
            [
                'subscription' => $subscription,
                'table' => $table->getTwigView(),
                'recipient' => $recipient,
                'edit_route' => $routes['edit'],
                'saved_filter_layout_readable' => $readableLayoutName,
                'link' => $this->routingHelper->generateUrlToSavedFilterTable($savedFilter, true),
            ]
        );
    }
}
