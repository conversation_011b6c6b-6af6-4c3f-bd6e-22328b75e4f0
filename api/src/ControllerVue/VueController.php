<?php

declare(strict_types=1);
namespace U2\ControllerVue;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\Routing\Attribute\Route;

class VueController
{
    #[Route(path: '/apm/transaction.{format}', name: 'u2_apm_apmtransaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/contract-management/contract.{format}', name: 'u2_contract_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt1.{format}', name: 'u2_igt_igt1transaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt2.{format}', name: 'u2_igt_igt2transaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt3.{format}', name: 'u2_igt_igt3transaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt4.{format}', name: 'u2_igt_igt4transaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt5.{format}', name: 'u2_igt_igt5transaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/income-tax-planning.{format}', name: 'u2_incometaxplanning_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/loss-carry-forward.{format}', name: 'u2_losscarryforward_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-assessment-status.{format}', name: 'u2_taxassessmentstatus_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-audit-risk.{format}', name: 'u2_taxauditrisk_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-consulting-fee.{format}', name: 'u2_taxconsultingfee_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-credit.{format}', name: 'u2_taxcredit_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-litigation.{format}', name: 'u2_taxlitigation_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-rate.{format}', name: 'u2_taxrate_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-relevant-restriction.{format}', name: 'u2_taxrelevantrestriction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/transfer-pricing.{format}', name: 'u2_transferpricing_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/other-deadline.{format}', name: 'u2_otherdeadline_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-assessment-monitor.{format}', name: 'u2_taxassessmentmonitor_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-authority-audit-objection.{format}', name: 'u2_taxauthorityauditobjection_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-filing-monitor.{format}', name: 'u2_taxfilingmonitor_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/country-by-country-report.{format}', name: 'u2_countrybycountryreport_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/financial-data.{format}', name: 'u2_financialdata_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/local-file.{format}', name: 'u2_localfile_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/main-business-activity.{format}', name: 'u2_mainbusinessactivity_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/master-file.{format}', name: 'u2_masterfile_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/transaction.{format}', name: 'u2_transaction_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tax-accounting/unit-period.{format}', name: 'u2_unitperiod_list', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
    public function list(): void
    {
    }

    #[Route(path: '/apm/transaction/{id}/edit', name: 'u2_apm_apmtransaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/contract-management/contract/{id}/edit', name: 'u2_contract_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt1/{id}/edit', name: 'u2_igt_igt1transaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt2/{id}/edit', name: 'u2_igt_igt2transaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt3/{id}/edit', name: 'u2_igt_igt3transaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt4/{id}/edit', name: 'u2_igt_igt4transaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt5/{id}/edit', name: 'u2_igt_igt5transaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/income-tax-planning/{id}/edit', name: 'u2_incometaxplanning_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/loss-carry-forward/{id}/edit', name: 'u2_losscarryforward_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-assessment-status/{id}/edit', name: 'u2_taxassessmentstatus_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-audit-risk/{id}/edit', name: 'u2_taxauditrisk_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-consulting-fee/{id}/edit', name: 'u2_taxconsultingfee_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-credit/{id}/edit', name: 'u2_taxcredit_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-litigation/{id}/edit', name: 'u2_taxlitigation_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-rate/{id}/edit', name: 'u2_taxrate_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-relevant-restriction/{id}/edit', name: 'u2_taxrelevantrestriction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/transfer-pricing/{id}/edit', name: 'u2_transferpricing_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/other-deadline/{id}/edit', name: 'u2_otherdeadline_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-assessment-monitor/{id}/edit', name: 'u2_taxassessmentmonitor_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-authority-audit-objection/{id}/edit', name: 'u2_taxauthorityauditobjection_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-filing-monitor/{id}/edit', name: 'u2_taxfilingmonitor_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/country-by-country-report/{id}/edit', name: 'u2_countrybycountryreport_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/financial-data/{id}/edit', name: 'u2_financialdata_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/local-file/{id}/edit', name: 'u2_localfile_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/main-business-activity/{id}/edit', name: 'u2_mainbusinessactivity_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/master-file/{id}/edit', name: 'u2_masterfile_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/transaction/{id}/edit', name: 'u2_transaction_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tax-accounting/unit-period/{id}/edit', name: 'u2_unitperiod_edit', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    public function edit(): void
    {
    }

    #[Route(path: '/apm/transaction/new', name: 'u2_apm_apmtransaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/contract-management/contract/new', name: 'u2_contract_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt1/new', name: 'u2_igt_igt1transaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt2/new', name: 'u2_igt_igt2transaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt3/new', name: 'u2_igt_igt3transaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt4/new', name: 'u2_igt_igt4transaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt5/new', name: 'u2_igt_igt5transaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/income-tax-planning/new', name: 'u2_incometaxplanning_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/loss-carry-forward/new', name: 'u2_losscarryforward_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-assessment-status/new', name: 'u2_taxassessmentstatus_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-audit-risk/new', name: 'u2_taxauditrisk_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-consulting-fee/new', name: 'u2_taxconsultingfee_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-credit/new', name: 'u2_taxcredit_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-litigation/new', name: 'u2_taxlitigation_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-rate/new', name: 'u2_taxrate_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-relevant-restriction/new', name: 'u2_taxrelevantrestriction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/transfer-pricing/new', name: 'u2_transferpricing_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/other-deadline/new', name: 'u2_otherdeadline_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-assessment-monitor/new', name: 'u2_taxassessmentmonitor_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-authority-audit-objection/new', name: 'u2_taxauthorityauditobjection_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-filing-monitor/new', name: 'u2_taxfilingmonitor_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/country-by-country-report/new', name: 'u2_countrybycountryreport_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/financial-data/new', name: 'u2_financialdata_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/local-file/new', name: 'u2_localfile_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/main-business-activity/new', name: 'u2_mainbusinessactivity_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/master-file/new', name: 'u2_masterfile_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/transaction/new', name: 'u2_transaction_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tax-accounting/unit-period/new', name: 'u2_unitperiod_new', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    public function new(): void
    {
    }

    #[Route(path: '/apm/transaction/{id}/edit-document', name: 'u2_apm_apmtransaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/contract-management/contract/{id}/edit-document', name: 'u2_contract_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt1/{id}/edit-document', name: 'u2_igt_igt1transaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt2/{id}/edit-document', name: 'u2_igt_igt2transaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt3/{id}/edit-document', name: 'u2_igt_igt3transaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt4/{id}/edit-document', name: 'u2_igt_igt4transaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/igt/transaction/igt5/{id}/edit-document', name: 'u2_igt_igt5transaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/income-tax-planning/{id}/edit-document', name: 'u2_incometaxplanning_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/loss-carry-forward/{id}/edit-document', name: 'u2_losscarryforward_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-assessment-status/{id}/edit-document', name: 'u2_taxassessmentstatus_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-audit-risk/{id}/edit-document', name: 'u2_taxauditrisk_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-consulting-fee/{id}/edit-document', name: 'u2_taxconsultingfee_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-credit/{id}/edit-document', name: 'u2_taxcredit_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-litigation/{id}/edit-document', name: 'u2_taxlitigation_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-rate/{id}/edit-document', name: 'u2_taxrate_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/tax-relevant-restriction/{id}/edit-document', name: 'u2_taxrelevantrestriction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tam/transfer-pricing/{id}/edit-document', name: 'u2_transferpricing_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/other-deadline/{id}/edit-document', name: 'u2_otherdeadline_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-assessment-monitor/{id}/edit-document', name: 'u2_taxassessmentmonitor_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-authority-audit-objection/{id}/edit-document', name: 'u2_taxauthorityauditobjection_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tcm/tax-filing-monitor/{id}/edit-document', name: 'u2_taxfilingmonitor_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/country-by-country-report/{id}/edit-document', name: 'u2_countrybycountryreport_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/financial-data/{id}/edit-document', name: 'u2_financialdata_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/local-file/{id}/edit-document', name: 'u2_localfile_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/main-business-activity/{id}/edit-document', name: 'u2_mainbusinessactivity_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/master-file/{id}/edit-document', name: 'u2_masterfile_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tpm/transaction/{id}/edit-document', name: 'u2_transaction_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    #[Route(path: '/tax-accounting/unit-period/{id}/edit-document', name: 'u2_unitperiod_editdocument', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    public function editDocument(): void
    {
    }
}
