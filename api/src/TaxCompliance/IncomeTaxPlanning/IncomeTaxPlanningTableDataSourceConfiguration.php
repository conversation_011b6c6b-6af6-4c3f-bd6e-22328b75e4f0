<?php

declare(strict_types=1);
namespace U2\TaxCompliance\IncomeTaxPlanning;

use Symfony\Bundle\SecurityBundle\Security;
use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\Event\DataSourcery\PostGenerateQueryBuilderEvent;
use U2\Repository\UnitRepository;
use U2\Task\DataSource\AbstractTaskTypeDataSourceConfiguration;
use U2\Task\DataSource\PeriodFieldAdder;
use U2\Task\DataSource\UnitCurrencyAsCurrencyFieldAdder;
use U2\User\CurrentUserProvider;
use U2\Workflow\WorkflowManager;

class IncomeTaxPlanningTableDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    public function __construct(
        Security $security,
        CurrentUserProvider $currentUserProvider,
        WorkflowManager $workflowManager,
        UnitRepository $unitRepository,
        private readonly IncomeTaxPlanningProfitBeforeTaxCalculatedTransformer $incomeTaxPlanningProfitBeforeTaxCalculatedTransformer,
    ) {
        parent::__construct($currentUserProvider, $workflowManager, $unitRepository, $security);
    }

    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);

        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                static function (PostGenerateQueryBuilderEvent $event): void {
                    $event->queryBuilder->addSelect('0 profit_before_tax_calculated');
                }
            );

        $builder
            ->addTransformer($this->incomeTaxPlanningProfitBeforeTaxCalculatedTransformer);

        PeriodFieldAdder::add($builder);
        UnitCurrencyAsCurrencyFieldAdder::add($builder);

        $builder
            ->addField(
                'TaxType',
                'string',
                'taxType.name',
                [
                    'choices' => [
                        'repository' => TaxType::class,
                        'field' => 'name',
                    ],
                ]
            )
            ->addField(
                'TaxRate',
                'percent',
                'taxRate'
            )
            ->addField(
                'PlanningPeriod',
                'number',
                'planningPeriod'
            )
            ->addField(
                'ProfitBeforeTax',
                'number',
                'profitBeforeTax'
            )
            ->addField(
                'TaxableIncome',
                'number',
                'taxableIncome'
            )
            ->addField(
                'IncomeTax',
                'number',
                'incomeTax'
            )
            ->addField(
                'Etr',
                'percent',
                'etr'
            )
            ->addNativeField(
                'ProfitBeforeTaxCalculated',
                'number',
                'profit_before_tax_calculated'
            );
    }

    /**
     * Fully qualified class name for the main entity handled by this data source (the FROM statement).
     */
    public static function getEntityClass(): string
    {
        return IncomeTaxPlanning::class;
    }
}
