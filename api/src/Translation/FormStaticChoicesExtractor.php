<?php

declare(strict_types=1);
namespace U2\Translation;

use <PERSON><PERSON>fony\Component\DependencyInjection\Attribute\AutoconfigureTag;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Translation\Extractor\ExtractorInterface;
use Symfony\Component\Translation\MessageCatalogue;

#[AutoconfigureTag(name: 'translation.extractor', attributes: ['alias' => 'FormStaticChoicesExtractor'])]
class FormStaticChoicesExtractor implements ExtractorInterface
{
    private bool $extracted = false;

    private string $prefix = '';

    public function __construct(private readonly ?string $projectDir = null)
    {
    }

    /**
     * @param string|iterable<string> $resource
     */
    public function extract(string|iterable $resource, MessageCatalogue $catalogue): void
    {
        // HACK: Ensure this extractor runs once
        // Symfony 4.3 does not collect translations from the `templates` folder anymore but also from other locations and files. This causes our extractors to
        // run for each of the located files and folders. This slows down the extraction process and causes our extractors to fail because they are expecting
        // the `templates` directory and are not able to work with single files. We should consider to switch to to a cleaner translation solution which allows
        // to extend the extractors as we want. (See: https://jira.universalunits.net/browse/UU-4691)
        if (true === $this->extracted) {
            return;
        }
        $this->extracted = true;

        $staticChoicesMessages = [];

        foreach ($this->extractFiles($this->manipulateTheDirectory($resource)) as $formTypeFile) {
            $staticChoicesMessages[] = $this->getStaticChoicesMessagesFromFormTypeFile($formTypeFile);
        }

        $this->addStaticChoicesMessagesToCatalogue($catalogue, array_merge([], ...$staticChoicesMessages)); // the empty array covers cases when no loops were made
    }

    public function setPrefix(string $prefix): void
    {
        $this->prefix = $prefix;
    }

    private function addStaticChoicesMessagesToCatalogue(MessageCatalogue $catalogue, array $staticChoicesMessages): void
    {
        foreach (array_unique($staticChoicesMessages) as $message) {
            $catalogue->set($message, $this->prefix . $message);
        }
    }

    private function getStaticChoicesMessagesFromFormTypeFile(\SplFileInfo $formTypeFile): array
    {
        $fileContent = file_get_contents($formTypeFile->getPathname());
        $choicesListArray = $this->getArrayOfChoiceListsFromFormTypeContent($fileContent);

        $staticChoicesMessages = [];
        foreach ($choicesListArray as $choiceList) {
            $staticChoicesMessages[] = $this->getStaticChoicesFromChoiceList($choiceList);
        }

        return array_merge([], ...$staticChoicesMessages); // the empty array covers cases when no loops were made;
    }

    private function extractFiles(array|string $directory): array|Finder
    {
        $formFolders = new Finder();

        $formFolders->directories()
            ->in($directory)
            ->name('Form')
            ->exclude(
                [
                    'Behat',
                ]
            );

        $files = new Finder();

        $files->files()->name('*.php');

        if (0 === $formFolders->count()) {
            return [];
        }
        $files->in(array_map(static fn (\SplFileInfo $dir): string => (string) $dir->getRealPath(), iterator_to_array($formFolders->getIterator())));

        return $files;
    }

    /**
     * @param string|iterable<string> $directory
     */
    private function manipulateTheDirectory(string|iterable $directory): string
    {
        if (\is_string($directory) && str_contains($directory, 'Fixtures')) {
            return $directory;
        }

        return $this->projectDir . \DIRECTORY_SEPARATOR . 'src';
    }

    private function getArrayOfChoiceListsFromFormTypeContent(string $formTypeContent): array
    {
        $choiceListArray = [];
        $patternToFindChoicesBlocks = '
            ~
                    \[
                    \s*[^\]]*   # Stop if it finds character ]
                        [\'"]   # Find opening quotes
                            choices # Find choices
                        [\'"]   # Find closing quotes
                        \s*
                        =>  # Find arrow
                        \s*
                       \[ # Find [
                            (.*?)   # This is our pattern
                        \], # Find ]
            ~sx
        ';
        if (false !== preg_match_all($patternToFindChoicesBlocks, $formTypeContent, $matches, \PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $choiceListArray[] = $match[1];
            }
        }

        return $choiceListArray;
    }

    private function getStaticChoicesFromChoiceList(string $choiceList): array
    {
        $staticChoicesMessages = [];
        $patternToFindStaticChoicesFromChoiceList = '
            ~
                [\'"]   # Find opening quotes
                  (.*?) # This is our pattern
                [\'"]   # Find closing quotes
                \s*
                    =>  # Find arrow
                \s*
                [^,]* # Until it finds the character ,
            ~sx
        ';
        if (false !== preg_match_all($patternToFindStaticChoicesFromChoiceList, $choiceList, $matches, \PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $staticChoicesMessages[] = $match[1];
            }
        }

        return $staticChoicesMessages;
    }
}
