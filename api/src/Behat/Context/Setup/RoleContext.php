<?php

declare(strict_types=1);
namespace U2\Behat\Context\Setup;

use Behat\MinkExtension\Context\RawMinkContext;
use Behat\Step\Given;
use Doctrine\ORM\EntityManagerInterface;
use U2\Entity\User;

class RoleContext extends RawMinkContext
{
    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
    }

    /**
     * @Given :user has the role :role
     * @Given /^(I) have the role (.*)$/
     */
    public function userHasTheRole(User $user, string $role): void
    {
        $user->addUserRole($role);
        $this->entityManager->flush();
    }
}
