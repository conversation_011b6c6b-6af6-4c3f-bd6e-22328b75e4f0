<?php

declare(strict_types=1);
namespace U2\Behat\Context\Transform;

use Behat\Gherkin\Node\TableNode;
use Behat\MinkExtension\Context\RawMinkContext;
use U2\Datasheets\Item\ExchangeMethods;
use U2\Datasheets\Item\ItemTypes;

class ItemTableContext extends RawMinkContext
{
    /**
     * @Transform table:Ref Id,Type,Editable,Exchange Method
     * @Transform table:Name,Ref Id,Type,Editable,Exchange Method
     */
    public function transformFileAccessType(TableNode $itemFormulaTable): TableNode
    {
        $table[] = $itemFormulaTable->getRow(0);

        $hash = $itemFormulaTable->getHash();
        array_walk(
            $hash,
            function (array $row) use (&$table): void {
                $row['Type'] = $this->transformItemType($row['Type']);
                $row['Exchange Method'] = $this->transformExchangeMethod($row['Exchange Method']);
                $table[] = $row;
            }
        );

        return new TableNode($table);
    }

    private function transformExchangeMethod(string $exchangeMethodReadable): int
    {
        $exchangeMethodConstant = strtoupper(str_replace(' ', '_', $exchangeMethodReadable));
        $class = ExchangeMethods::class;

        if (\defined("$class::$exchangeMethodConstant")) {
            return \constant("$class::$exchangeMethodConstant");
        }

        throw new \InvalidArgumentException("Constant '$class::$exchangeMethodConstant' does not exists.");
    }

    private function transformItemType(string $typeHumanReadable): string
    {
        $typeConstant = strtoupper($typeHumanReadable);
        $class = ItemTypes::class;

        if (\defined("$class::$typeConstant")) {
            return \constant("$class::$typeConstant");
        }

        throw new \InvalidArgumentException("Constant '$class::$typeConstant' does not exists.");
    }
}
