<?php

declare(strict_types=1);
namespace U2\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType as BaseMoneyType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use U2\Form\DataTransformer\MoneyStringToLocalizedStringTransformer;

class MoneyType extends AbstractType
{
    public const int SCALE = 0;
    public const int DIVISOR = 1;
    public const true GROUPING = true;
    public const false CURRENCY = false;

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->resetViewTransformers();
        /** @var int $scale */
        $scale = $options['scale'];
        /** @var bool|null $grouping */
        $grouping = $options['grouping'];
        /** @var int $divisor */
        $divisor = $options['divisor'];
        $builder->addViewTransformer(new MoneyStringToLocalizedStringTransformer($scale, $grouping, null, $divisor));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'scale' => self::SCALE,
                'grouping' => self::GROUPING,
                'divisor' => self::DIVISOR,
                'currency' => self::CURRENCY,
            ]
        );
    }

    public function getParent(): string
    {
        return BaseMoneyType::class;
    }

    public function getBlockPrefix(): string
    {
        return 'u2_money';
    }
}
