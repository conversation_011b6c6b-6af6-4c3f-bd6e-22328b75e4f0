<?php

declare(strict_types=1);
namespace U2\Form\Type\FieldAdder;

use Symfony\Component\Form\FormBuilderInterface;
use U2\Entity\Configuration\Field\LineOfBusiness;
use U2\Form\Type\TaskChoiceFieldType;

class LineOfBusinessFieldAdder
{
    /**
     * @param array<string, mixed> $options
     */
    public static function add(FormBuilderInterface $builder, array $options = []): void
    {
        $builder->add(
            'lineOfBusiness',
            TaskChoiceFieldType::class,
            [
                'class' => LineOfBusiness::class,
                'placeholder' => 'Select a line of business',
                ...$options,
            ]
        );
    }
}
