<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2;

use U2\TransferPricing\CountryByCountryReport\ReportingRoles;
use U2\Xbrl\Oecd\DocRefIdResolver;
use U2\Xbrl\Oecd\Enum\CbcReportingRole;
use U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcstfV5\DocSpecType;
use U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\ReportingEntityType\ReportingPeriodAType;
use U2\Xbrl\XbrlDataInterface;

class CorrectableReportingEntityType extends ReportingEntityType
{
    private ?DocSpecType $docSpec = null;

    private const array cbcToXbrlReportingRolesMap = [
        ReportingRoles::ultimateParentEntity => CbcReportingRole::ULTIMATE_PARENT_ENTITY,
        ReportingRoles::surrogateParentEntity => CbcReportingRole::SURROGATE_PARENT_ENTITY,
        ReportingRoles::localFiling => CbcReportingRole::LOCAL_FILING,
    ];

    public static function createFromXbrlData(XbrlDataInterface $xbrlData): self
    {
        $correctableReportingEntityType = new self();

        $correctableReportingEntityType->setEntity(
            OrganisationPartyType::createFromUnit($xbrlData->getCountryByCountryReport()->getReportingCompany())
        );
        $correctableReportingEntityType->setReportingRole(
            self::cbcToXbrlReportingRolesMap[$xbrlData->getCountryByCountryReport()->getReportingRole()]
        );
        $correctableReportingEntityType->setDocSpec(
            new DocSpecType(DocRefIdResolver::resolveForReportingEntity($xbrlData))
        );

        $period = $xbrlData->getCountryByCountryReport()->getPeriod();
        \assert(null !== $period);

        $correctableReportingEntityType->setReportingPeriod(
            new ReportingPeriodAType(
                $period->getStartDate(),
                $period->getEndDate()
            )
        );

        return $correctableReportingEntityType;
    }

    public function getDocSpec(): ?DocSpecType
    {
        return $this->docSpec;
    }

    public function setDocSpec(DocSpecType $docSpec): self
    {
        $this->docSpec = $docSpec;

        return $this;
    }
}
