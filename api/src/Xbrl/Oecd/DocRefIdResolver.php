<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd;

use U2\Entity\Country;
use U2\Xbrl\At\NationalData;
use U2\Xbrl\XbrlDataInterface;

class DocRefIdResolver
{
    private const string TYPE_REPORTING_ENTITY = 'RE';

    private const string TYPE_CBC_REPORTS = 'CR';

    private const string TYPE_ADDITIONAL_INFO = 'AI';

    private const string VERSION = '1';

    public static function resolveForReportingEntity(XbrlDataInterface $xbrlData): string
    {
        return self::getFormattedDocRefId(
            $xbrlData,
            self::TYPE_REPORTING_ENTITY . self::VERSION
        );
    }

    public static function resolveForCbcReports(XbrlDataInterface $xbrlData, Country $country): string
    {
        return self::getFormattedDocRefId(
            $xbrlData,
            self::TYPE_CBC_REPORTS . $country->getIso3166code() . self::VERSION
        );
    }

    public static function resolveForAdditionalInfo(XbrlDataInterface $xbrlData, int $position): string
    {
        return self::getFormattedDocRefId(
            $xbrlData,
            self::TYPE_ADDITIONAL_INFO . \sprintf('%02d', $position) . self::VERSION
        );
    }

    private static function getFormattedDocRefId(XbrlDataInterface $xbrlData, string $suffix): string
    {
        $countryByCountryReport = $xbrlData->getCountryByCountryReport();

        $legalUnit = $countryByCountryReport->getReportingCompany();
        \assert(null !== $legalUnit);

        $country = $legalUnit->getCountry();
        \assert(null !== $country);

        $period = $countryByCountryReport->getPeriod();
        \assert(null !== $period);

        $refId = $country->getIso3166code() . $period->getEndDate()->format('Y');

        if ($xbrlData instanceof NationalData) {
            $refId .= $xbrlData->getCountry()->getIso3166code();
        }

        return \sprintf(
            '%s-%s%d%s',
            $refId,
            $legalUnit->getDefaultTaxNumber(),
            $countryByCountryReport->getId(),
            $suffix
        );
    }
}
