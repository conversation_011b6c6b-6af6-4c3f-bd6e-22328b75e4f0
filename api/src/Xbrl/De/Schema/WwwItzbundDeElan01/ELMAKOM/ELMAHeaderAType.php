<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\WwwItzbundDeElan01\ELMAKOM;

use U2\Xbrl\De\Schema\WwwItzbundDeElan01\AccountID;
use U2\Xbrl\De\Schema\WwwItzbundDeElan01\AuthSteuernummer;
use U2\Xbrl\De\Schema\WwwItzbundDeElan01\ErstellungsDatum;
use U2\Xbrl\De\Schema\WwwItzbundDeElan01\KundeneigeneID;
use U2\Xbrl\De\Schema\WwwItzbundDeElan01\UUID;
use U2\Xbrl\De\Schema\WwwItzbundDeElan01\Verarbeitungslauf;

class ELMAHeaderAType
{
    public const string dataType = 'ELMA_CC';

    public function __construct(
        private string $datenArt,
        private AuthSteuernummer $authSteuernummer,
        private AccountID $accountID,
        private ErstellungsDatum $erstellungsDatum,
        private KundeneigeneID $kundeneigeneID,
        private UUID $uUID,
        private Verarbeitungslauf $verarbeitungslauf,
    ) {
    }

    public function getDatenArt(): ?string
    {
        return $this->datenArt;
    }

    public function setDatenArt(string $datenArt): self
    {
        $this->datenArt = $datenArt;

        return $this;
    }

    public function getAuthSteuernummer(): ?AuthSteuernummer
    {
        return $this->authSteuernummer;
    }

    public function setAuthSteuernummer(AuthSteuernummer $authSteuernummer): self
    {
        $this->authSteuernummer = $authSteuernummer;

        return $this;
    }

    public function getAccountID(): ?AccountID
    {
        return $this->accountID;
    }

    public function setAccountID(AccountID $accountID): self
    {
        $this->accountID = $accountID;

        return $this;
    }

    public function getErstellungsDatum(): ?ErstellungsDatum
    {
        return $this->erstellungsDatum;
    }

    public function setErstellungsDatum(ErstellungsDatum $erstellungsDatum): self
    {
        $this->erstellungsDatum = $erstellungsDatum;

        return $this;
    }

    public function getKundeneigeneID(): ?KundeneigeneID
    {
        return $this->kundeneigeneID;
    }

    public function setKundeneigeneID(KundeneigeneID $kundeneigeneID): self
    {
        $this->kundeneigeneID = $kundeneigeneID;

        return $this;
    }

    public function getUUID(): ?UUID
    {
        return $this->uUID;
    }

    public function setUUID(UUID $uUID): self
    {
        $this->uUID = $uUID;

        return $this;
    }

    public function getVerarbeitungslauf(): ?Verarbeitungslauf
    {
        return $this->verarbeitungslauf;
    }

    public function setVerarbeitungslauf(Verarbeitungslauf $verarbeitungslauf): self
    {
        $this->verarbeitungslauf = $verarbeitungslauf;

        return $this;
    }
}
