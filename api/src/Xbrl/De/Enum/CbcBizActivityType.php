<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Enum;

class CbcBizActivityType
{
    public const string RESEARCH_AND_DEVELOPMENT = 'CBC501';

    public const string HOLDING_OR_MANAGING_INTELLECTUAL_PROPERTY = 'CBC502';

    public const string PURCHASING_OR_PROCUREMENT = 'CBC503';

    public const string MANUFACTURING_OR_PRODUCTION = 'CBC504';

    public const string SALES_MARKETING_OR_DISTRIBUTION = 'CBC505';

    public const string ADMINISTRATIVE_MANAGEMENT_OR_SUPPORT_SERVICES = 'CBC506';

    public const string PROVISION_OF_SERVICES_TO_UNRELATED_PARTIES = 'CBC507';

    public const string INTERNAL_GROUP_FINANCE = 'CBC508';

    public const string REGULATED_FINANCIAL_SERVICES = 'CBC509';

    public const string INSURANCE = 'CBC510';

    public const string HOLDING_SHARES_OR_OTHER_EQUITY_INSTRUMENTS = 'CBC511';

    public const string DORMANT = 'CBC512';

    public const string OTHER = 'CBC513';

    public const array NAME_TO_CODE_MAP = [
        'Research and Development' => self::RESEARCH_AND_DEVELOPMENT,
        'Holding or Managing intellectual property' => self::HOLDING_OR_MANAGING_INTELLECTUAL_PROPERTY,
        'Purchasing or Procurement' => self::PURCHASING_OR_PROCUREMENT,
        'Manufacturing or Production' => self::MANUFACTURING_OR_PRODUCTION,
        'Sales, Marketing or Distribution' => self::SALES_MARKETING_OR_DISTRIBUTION,
        'Administrative, Management or Support Services' => self::ADMINISTRATIVE_MANAGEMENT_OR_SUPPORT_SERVICES,
        'Provision of Services to unrelated parties' => self::PROVISION_OF_SERVICES_TO_UNRELATED_PARTIES,
        'Internal Group Finance' => self::INTERNAL_GROUP_FINANCE,
        'Regulated Financial Services' => self::REGULATED_FINANCIAL_SERVICES,
        'Insurance' => self::INSURANCE,
        'Holding shares or other equity instruments' => self::HOLDING_SHARES_OR_OTHER_EQUITY_INSTRUMENTS,
        'Dormant' => self::DORMANT,
        'Other' => self::OTHER,
    ];
}
