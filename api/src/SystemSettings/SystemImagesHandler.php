<?php

declare(strict_types=1);
namespace U2\SystemSettings;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use U2\Exception\Exception;
use U2\FileSystem\TenantFilesystemOperator;

class SystemImagesHandler
{
    public const string APPLICATION_BACKGROUND_IMAGE = 'application-background';
    public const string APPLICATION_CORP_LOGO = 'corp-logo';
    public const string APPLICATION_DOCUMENTATION_COVER_PICTURE = 'documentation-cover-picture';
    public const string APPLICATION_DOCUMENTATION_CORP_LOGO = 'documentation-corp-logo';
    public const string APPLICATION_LOGIN_LOGO = 'application-logo';

    public function __construct(
        private readonly TenantFilesystemOperator $tenantFilesystem,
    ) {
    }

    public function saveUploadedSystemImages(
        ?UploadedFile $backgroundImage = null,
        ?UploadedFile $documentationCoverPicture = null,
        ?UploadedFile $documentationCorpLogo = null,
        ?UploadedFile $loginLogo = null,
        ?UploadedFile $corpLogo = null,
    ): void {
        if (null !== $backgroundImage) {
            $this->storeBackgroundImage($backgroundImage);
        }

        if (null !== $documentationCoverPicture) {
            $this->storeDocumentationCoverPicture($documentationCoverPicture);
        }

        if (null !== $documentationCorpLogo) {
            $this->storeDocumentationCorpLogo($documentationCorpLogo);
        }

        if (null !== $loginLogo) {
            $this->storeLoginLogo($loginLogo);
        }

        if (null !== $corpLogo) {
            $this->storeCorpLogo($corpLogo);
        }
    }

    public function storeBackgroundImage(?UploadedFile $backgroundImage): void
    {
        if (null === $backgroundImage) {
            $this->removeImage(self::APPLICATION_BACKGROUND_IMAGE);

            return;
        }
        $this->tenantFilesystem->storeFile(
            $backgroundImage->getRealPath(),
            self::APPLICATION_BACKGROUND_IMAGE,
            ['ContentType' => $backgroundImage->getMimeType()]
        );
    }

    public function storeDocumentationCoverPicture(?UploadedFile $documentationCoverPicture): void
    {
        if (null === $documentationCoverPicture) {
            $this->removeImage(self::APPLICATION_DOCUMENTATION_COVER_PICTURE);

            return;
        }
        $this->tenantFilesystem->storeFile(
            $documentationCoverPicture->getRealPath(),
            self::APPLICATION_DOCUMENTATION_COVER_PICTURE,
            ['ContentType' => $documentationCoverPicture->getMimeType()]
        );
    }

    public function storeDocumentationCorpLogo(?UploadedFile $documentationCorpLogo): void
    {
        if (null === $documentationCorpLogo) {
            $this->removeImage(self::APPLICATION_DOCUMENTATION_CORP_LOGO);

            return;
        }
        $this->tenantFilesystem->storeFile(
            $documentationCorpLogo->getRealPath(),
            self::APPLICATION_DOCUMENTATION_CORP_LOGO,
            ['ContentType' => $documentationCorpLogo->getMimeType()]
        );
    }

    public function storeLoginLogo(?UploadedFile $loginLogo = null): void
    {
        if (null === $loginLogo) {
            $this->removeImage(self::APPLICATION_LOGIN_LOGO);

            return;
        }

        $this->tenantFilesystem->storeFile(
            $loginLogo->getRealPath(),
            self::APPLICATION_LOGIN_LOGO,
            ['ContentType' => $loginLogo->getMimeType()]
        );
    }

    public function storeCorpLogo(?UploadedFile $corpLogo): void
    {
        if (null === $corpLogo) {
            $this->removeImage(self::APPLICATION_CORP_LOGO);

            return;
        }

        $this->tenantFilesystem->storeFile(
            $corpLogo->getRealPath(),
            self::APPLICATION_CORP_LOGO,
            ['ContentType' => $corpLogo->getMimeType()]
        );
    }

    /**
     * @throws Exception
     */
    public function removeImage(string $fileName): void
    {
        $isSystemImage = \in_array(
            $fileName,
            [
                self::APPLICATION_BACKGROUND_IMAGE,
                self::APPLICATION_DOCUMENTATION_COVER_PICTURE,
                self::APPLICATION_DOCUMENTATION_CORP_LOGO,
                self::APPLICATION_LOGIN_LOGO,
                self::APPLICATION_CORP_LOGO,
            ],
            true
        );
        if (!$isSystemImage) {
            throw new Exception("File $fileName is not a valid system image file.");
        }

        if (!$this->tenantFilesystem->fileExists($fileName)) {
            throw new Exception("Cannot remove $fileName, because it does not exists in data directory.");
        }

        $this->tenantFilesystem->delete($fileName);
    }
}
