<?php

declare(strict_types=1);
namespace U2\Table\View;

use U2\DataSourcery\DataSource\Configuration\Field;
use U2\DataSourcery\Extension\UqlExtensionContainer;
use U2\Entity\SavedFilter;
use U2\Exception\Exception;
use U2\Repository\SavedFilterRepository;
use U2\Table\State;
use U2\Table\Table;
use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\Table\View\Configuration\TwigTableViewConfigurationInterface;
use U2\Task\SavedFilter\SavedFilterInformation;

/**
 * A reference implementation of a table view.
 * This object will be passed to the rendering engine as the table.
 */
class TwigTableView
{
    /**
     * Range of pages in either direction to show in the pager.
     */
    public const int PAGE_RANGE_SPAN = 5;

    /**
     * The variables assigned to this view. This mimics the functionality
     * of the forms.
     */
    public array $vars = [
        'value' => null,
        'attr' => [],
    ];

    /**
     * Cache of the data source elements.
     *
     * @var Field[]
     */
    private ?array $fieldsUsedByTable = null;

    /**
     * Is the form attached to this renderer rendered?
     *
     * Rendering happens when either the widget or the row method was called.
     * Row implicitly includes widget, however certain rendering mechanisms
     * have to skip widget rendering when a row is rendered.
     */
    private bool $rendered = false;

    /**
     * The pagination information.
     */
    private ?array $pagination = null;

    /**
     * An array of AJAX action endpoints for using in the templates.
     *
     * @var string[]
     */
    private array $endpoints = [];

    /**
     * @param Table $table a reference to the associated Table object
     */
    public function __construct(
        private readonly UqlExtensionContainer $extensionContainer,
        private readonly SavedFilterRepository $savedFilterRepository,
        private Table $table,
    ) {
        $configuration = $this->table->getViewConfiguration();
        $name = $configuration->getName();
        $data = $this->table->getData();
        $this->vars = array_merge(
            $this->vars,
            [
                'table' => $this,
                'columns' => $this->getColumnsUsedInSelect(),
                'data' => $data,
                'config' => $configuration,
                'id' => ltrim($name, '_0123456789'),  // Remove underscores and digits from the beginning of the ID (why???)
                'fieldId' => $name,
                'attr' => [],
                'unique_block_prefix' => '_' . $name,
                'cache_key' => '_' . $name . '_' . bin2hex(random_bytes(16)),
            ]
        );
    }

    public function getExtensions(): UqlExtensionContainer
    {
        return $this->extensionContainer;
    }

    public function isRendered(): bool
    {
        if (true === $this->rendered) {
            return $this->rendered;
        }

        return false;
    }

    public function setRendered(): self
    {
        $this->rendered = true;

        return $this;
    }

    public function getPagination(): array
    {
        // We cache the pagination so it's calculated once per page/table
        if (null === $this->pagination) {
            $totalElements = $this->table->getRecordCount();
            $pagination = $this->table->getState()->getPagination();
            $pagesCount = ceil($totalElements / $pagination->getCount());
            $currentPage = $pagination->getPage();
            $lastItemCurrentPage = $pagination->getOffset() + $pagination->getCount();
            if ($lastItemCurrentPage > $totalElements) {
                $lastItemCurrentPage = $totalElements;
            }
            $pageRangeFirst = $currentPage - self::PAGE_RANGE_SPAN;
            if ($pageRangeFirst < 0) {
                $pageRangeFirst = 0;
            }

            $pageRangeLast = $currentPage + self::PAGE_RANGE_SPAN;
            if ($pageRangeLast > $pagesCount - 1) {
                $pageRangeLast = ($pagesCount - 1) > 0 ? $pagesCount - 1 : 0;
            }

            $paginationInformation = [
                'previous' => $currentPage,
                'current' => $currentPage,
                'next' => $currentPage,
                'first' => 0,
                'last' => ($pagesCount - 1 > 0) ? $pagesCount - 1 : 0,
                'first_item' => $totalElements ? $pagination->getOffset() + 1 : 0,
                'last_item' => $lastItemCurrentPage,
                'total_items' => $totalElements,
                'pages' => $pagesCount,
                'pages_in_range' => range($pageRangeFirst, $pageRangeLast),
                'page_range' => self::PAGE_RANGE_SPAN,
                'count' => $pagination->getCount(),
            ];

            if ($currentPage + 1 < $pagesCount) {
                $paginationInformation['next'] = $currentPage + 1;
            }
            if ($currentPage > 0) {
                $paginationInformation['previous'] = $currentPage - 1;
            }

            $this->pagination = $paginationInformation;
        }

        return $this->pagination;
    }

    public function getName(): string
    {
        return $this->table->getViewConfiguration()->getName();
    }

    public function getStateAsJson(): string
    {
        return json_encode($this->table->getState()->jsonSerialize());
    }

    public function isPaginationEnabled(): bool
    {
        return $this->table->getViewConfiguration()->isPaginationEnabled();
    }

    public function isSelectionEnabled(): bool
    {
        return $this->table->getViewConfiguration()->isSelectionEnabled();
    }

    public function isSortingEnabled(): bool
    {
        return $this->table->getViewConfiguration()->isSortingEnabled();
    }

    public function getSelectionKey(): ?string
    {
        return $this->table->getViewConfiguration()->getSelectionKey();
    }

    /**
     * @return string[]
     */
    public function getEndpoints(): array
    {
        return $this->endpoints;
    }

    /**
     * @param string[] $endpoints
     */
    public function setEndpoints(array $endpoints): void
    {
        $this->endpoints = $endpoints;
    }

    public function addEndpoint(string $endpoint, string $url): void
    {
        $this->endpoints[$endpoint] = $url;
    }

    public function getTable(): Table
    {
        return $this->table;
    }

    public function setTable(Table $table): void
    {
        $this->table = $table;
    }

    public function getState(): State
    {
        return $this->table->getState();
    }

    /**
     * @return Field[]
     */
    public function getDataSourceFields(): array
    {
        if (null === $this->fieldsUsedByTable) {
            $this->fieldsUsedByTable = $this
                ->table
                ->getViewConfiguration()
                ->getColumnDefinitions()
                ->map(
                    function (ColumnDefinition $columnDefinition): Field {
                        $field = $this->getField($columnDefinition);
                        $field->setMetadata(
                            [
                                'filterable' => $columnDefinition->isFilterable(),
                                'required' => $columnDefinition->isRequired(),
                                'selectedByDefault' => $columnDefinition->isSelectedByDefault(),
                                'sortable' => $columnDefinition->isSortable(),
                                'hidden' => $columnDefinition->isHidden(),
                                'name' => $columnDefinition->getName(),
                            ]
                        );

                        return $field;
                    }
                )
                ->all();
        }

        return $this->fieldsUsedByTable;
    }

    public function getConfiguration(): TwigTableViewConfigurationInterface
    {
        return $this->table->getViewConfiguration();
    }

    public function getSavedFilter(): ?SavedFilter
    {
        $savedFilterId = $this->getState()->getSavedFilterId();
        if (null === $savedFilterId) {
            return null;
        }

        return $this->savedFilterRepository->find($savedFilterId);
    }

    public function getSavedFilterInformation(): SavedFilterInformation
    {
        $savedFilter = $this->getSavedFilter();
        if (!$savedFilter) {
            return new SavedFilterInformation(
                false,
                null
            );
        }

        $currentUql = $this->getState()->getQuery()->getFilter()->getUql();

        return new SavedFilterInformation(
            $currentUql !== $savedFilter->getUql(),
            $savedFilter,
        );
    }

    /**
     * @throws Exception
     */
    public function getField(ColumnDefinition $columnDefinition): Field
    {
        foreach ($this->table->getDataSource()->getFields() as $field) {
            if ($field->getUniqueName() === $columnDefinition->getId()) {
                return $field;
            }
        }
        throw new Exception("Field \"{$columnDefinition->getId()}\" not found");
    }

    /**
     * @throws Exception
     */
    private function getColumnsUsedInSelect(): ColumnDefinitionCollection
    {
        $select = $this->table->getState()->getQuery()->getSelect();
        if (0 === \count($select)) {
            throw new Exception('The select is not allowed to be empty');
        }

        $columns = new ColumnDefinitionCollection();
        foreach ($this->table->getViewConfiguration()->getColumnDefinitions() as $column) {
            foreach ($select as $selectedColumn) {
                if ($column->getId() === $selectedColumn) {
                    $columns[] = $column;
                    break;
                }
            }
        }

        return $columns;
    }
}
