<?php

declare(strict_types=1);
namespace U2\Import;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;

class Upload
{
    #[Assert\Choice(choices: [DelimiterTypes::COMMA, DelimiterTypes::SEMICOLON])]
    #[Assert\NotBlank]
    #[Assert\Type('string')]
    private ?string $delimiter = null;

    #[Assert\Type(type: 'bool')]
    private bool $dryRun = false;

    #[Assert\File(maxSize: '2048k', mimeTypes: ['application/csv', 'text/csv', 'text/plain'])]
    #[Assert\NotNull]
    private ?UploadedFile $uploadedFile = null;

    public function __construct(#[Assert\Type('string')] #[Assert\NotBlank] private readonly string $configurationKey)
    {
    }

    public function getDelimiter(): ?string
    {
        return $this->delimiter;
    }

    public function setDelimiter(string $delimiter): void
    {
        $this->delimiter = $delimiter;
    }

    public function getUploadedFile(): ?UploadedFile
    {
        return $this->uploadedFile;
    }

    public function setUploadedFile(UploadedFile $uploadedFile): void
    {
        $this->uploadedFile = $uploadedFile;
    }

    public function isDryRun(): bool
    {
        return $this->dryRun;
    }

    public function setDryRun(bool $dryRun): void
    {
        $this->dryRun = $dryRun;
    }

    public function getConfigurationKey(): string
    {
        return $this->configurationKey;
    }
}
