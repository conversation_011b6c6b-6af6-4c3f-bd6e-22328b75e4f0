<?php

declare(strict_types=1);
namespace U2\Import;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class StatusTypes extends AbstractConstantChoiceBag
{
    public const string QUEUED = 'queued';

    public const string IN_PROGRESS = 'in_progress';

    public const string COMPLETED = 'completed';

    public static function getReadableMap(): array
    {
        return [
            self::QUEUED => 'u2.import.status_types.queued',
            self::IN_PROGRESS => 'u2.import.status_types.in_progress',
            self::COMPLETED => 'u2.import.status_types.completed',
        ];
    }
}
