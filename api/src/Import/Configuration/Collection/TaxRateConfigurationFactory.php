<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Collection;

use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Period;
use U2\Entity\Task\TaskType\TaxRate;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Import\Configuration\Field\DateTimeFieldConfiguration;
use U2\Import\Configuration\Field\LiteralFieldConfiguration;
use U2\Import\Configuration\Field\LookupFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class TaxRateConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: TaxRate::class,
            fields: [
                new LookupFieldConfiguration(
                    id: 'task.assignee',
                    label: 'Assignee',
                    class: User::class,
                    lookupField: 'username',
                ),
                new DateTimeFieldConfiguration(
                    id: 'task.dueDate',
                    label: 'Due Date',
                ),
                new LookupFieldConfiguration(
                    id: 'task.reporter',
                    label: 'Reporter',
                    class: User::class,
                    lookupField: 'username',
                ),
                new LookupFieldConfiguration(
                    id: 'period',
                    label: 'Period',
                    class: Period::class,
                    lookupField: 'name',
                ),
                new LookupFieldConfiguration(
                    id: 'unit',
                    label: 'Unit',
                    class: Unit::class,
                    lookupField: 'refId',
                ),
                new LookupFieldConfiguration(
                    id: 'taxType',
                    label: 'Tax Type',
                    class: TaxType::class,
                    lookupField: 'name',
                ),
                new LiteralFieldConfiguration(
                    id: 'taxRate',
                    label: 'Tax Rate',
                ),
                new LiteralFieldConfiguration(
                    id: 'task.description',
                    label: 'Description',
                ),
                new LookupFieldConfiguration(
                    id: 'status',
                    label: 'Status',
                    class: Status::class,
                    lookupField: 'name',
                ),
            ],
            updateMatchFields: [],
            factory: 'U2\Task\TaskTypeFactory:createWithDefaults',
            factoryArguments: [TaxRate::class],
            help: null
        );
    }
}
