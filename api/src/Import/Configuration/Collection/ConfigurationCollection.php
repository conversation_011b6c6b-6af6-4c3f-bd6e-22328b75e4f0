<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Collection;

use U2\EntityMetadata\MetadataProvider;
use U2\Exception\UndefinedIndexImportConfigurationException;
use U2\Import\Configuration\ImportConfig;
use U2\Util\StringManipulator;

/**
 * @implements \IteratorAggregate<ImportConfig>
 */
class ConfigurationCollection implements \IteratorAggregate, \Countable
{
    public const array configurationFactories = [
        ApmTransactionConfigurationFactory::class,
        ContractConfigurationFactory::class,
        ExchangeRateConfigurationFactory::class,
        FieldConfigurationFactory::class,
        FinancialDataConfigurationFactory::class,
        Igt1TransactionConfigurationFactory::class,
        Igt2TransactionConfigurationFactory::class,
        Igt3TransactionConfigurationFactory::class,
        Igt4TransactionConfigurationFactory::class,
        Igt5TransactionConfigurationFactory::class,
        IncomeTaxPlanningConfigurationFactory::class,
        ItemConfigurationFactory::class,
        ItemFormulaConfigurationFactory::class,
        ItemUnitValueConfigurationFactory::class,
        DatasheetConfigurationFactory::class,
        LegalUnitConfigurationFactory::class,
        LossCarryForwardConfigurationFactory::class,
        MainBusinessActivityConfigurationFactory::class,
        OrganisationalGroupConfigurationFactory::class,
        OtherDeadlineConfigurationFactory::class,
        PermanentEstablishmentConfigurationFactory::class,
        TaxAssessmentMonitorConfigurationFactory::class,
        TaxAssessmentStatusConfigurationFactory::class,
        TaxAuditRiskConfigurationFactory::class,
        TaxAuthorityAuditObjectionConfigurationFactory::class,
        TaxConsultingFeeConfigurationFactory::class,
        TaxCreditConfigurationFactory::class,
        TaxFilingMonitorConfigurationFactory::class,
        TaxLitigationConfigurationFactory::class,
        TaxRateConfigurationFactory::class,
        TaxRelevantRestrictionConfigurationFactory::class,
        TransactionConfigurationFactory::class,
        TransferPricingConfigurationFactory::class,
        UnitConfigurationFactory::class,
        UnitPeriodConfigurationFactory::class,
    ];

    /**
     * @var array<string, ImportConfig>
     */
    private array $configurationCollection = [];

    public function __construct(MetadataProvider $metadataProvider)
    {
        foreach (self::configurationFactories as $configurationFactory) {
            \assert(class_exists($configurationFactory));
            $configurationFactory = $configurationFactory::create();
            $this->configurationCollection[
                StringManipulator::dashSeparatedToUnderscoreSeparated($metadataProvider->getShortName($configurationFactory->getClass()))
            ] = $configurationFactory;
        }
    }

    /**
     * @throws UndefinedIndexImportConfigurationException
     */
    public function get(string $configurationId): ImportConfig
    {
        if (\array_key_exists($configurationId, $this->configurationCollection)) {
            return $this->configurationCollection[$configurationId];
        }

        throw new UndefinedIndexImportConfigurationException("No configuration for $configurationId found");
    }

    public function add(string $configurationId, ImportConfig $configuration): void
    {
        $this->configurationCollection[$configurationId] = $configuration;
    }

    /**
     * @return array<string, ImportConfig>
     */
    public function all(): array
    {
        return $this->configurationCollection;
    }

    /**
     * @return array<int, string>
     */
    public function getConfigurationIds(): array
    {
        return array_keys($this->configurationCollection);
    }

    /**
     * @return \Traversable<ImportConfig>
     */
    public function getIterator(): \Traversable
    {
        return new \ArrayIterator($this->configurationCollection);
    }

    public function count(): int
    {
        return \count($this->configurationCollection);
    }
}
