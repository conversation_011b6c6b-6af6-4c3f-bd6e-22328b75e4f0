<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Collection;

use U2\Entity\Configuration\Field\LineOfBusiness;
use U2\Entity\Configuration\Field\PricingMethod;
use U2\Entity\Configuration\Field\TraceId;
use U2\Entity\Country;
use U2\Entity\Currency;
use U2\Entity\Period;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Import\Configuration\Field\BooleanFieldConfiguration;
use U2\Import\Configuration\Field\DateFieldConfiguration;
use U2\Import\Configuration\Field\DateTimeFieldConfiguration;
use U2\Import\Configuration\Field\LiteralFieldConfiguration;
use U2\Import\Configuration\Field\LookupFieldConfiguration;
use U2\Import\Configuration\Field\MoneyAmountFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class Igt4TransactionConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: Igt4Transaction::class,
            fields: [
                new LookupFieldConfiguration(
                    id: 'task.assignee',
                    label: 'Assignee',
                    class: User::class,
                    lookupField: 'username',
                ),
                new DateTimeFieldConfiguration(
                    id: 'task.dueDate',
                    label: 'Due Date',
                ),
                new LookupFieldConfiguration(
                    id: 'task.reporter',
                    label: 'Reporter',
                    class: User::class,
                    lookupField: 'username',
                ),
                new LookupFieldConfiguration(
                    id: 'period',
                    label: 'Period',
                    class: Period::class,
                    lookupField: 'name',
                ),
                new LiteralFieldConfiguration(
                    id: 'type',
                    label: 'Type',
                ),
                new LookupFieldConfiguration(
                    id: 'unit',
                    label: 'Unit',
                    class: Unit::class,
                    lookupField: 'refId',
                ),
                new BooleanFieldConfiguration(
                    id: 'partnerIsThirdParty',
                    label: 'Partner is Third Party',
                    help: 'Boolean',
                ),
                new LookupFieldConfiguration(
                    id: 'partnerUnit',
                    label: 'Partner Unit',
                    class: Unit::class,
                    lookupField: 'refId',
                ),
                new LiteralFieldConfiguration(
                    id: 'thirdPartyName',
                    label: 'Third Party Name',
                ),
                new LookupFieldConfiguration(
                    id: 'thirdPartyCountry',
                    label: 'Third Party Country',
                    class: Country::class,
                    lookupField: 'iso3166code',
                ),
                new LiteralFieldConfiguration(
                    id: 'task.description',
                    label: 'Description',
                ),
                new LookupFieldConfiguration(
                    id: 'status',
                    label: 'Status',
                    class: Status::class,
                    lookupField: 'name',
                ),
                new BooleanFieldConfiguration(
                    id: 'armsLength',
                    label: 'Arms Length',
                    help: "Confirm whether transaction is at arm's length"
                ),
                new LookupFieldConfiguration(
                    id: 'transactionCurrency',
                    label: 'Transaction Currency',
                    class: Currency::class,
                    lookupField: 'iso4217code',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'transactionValue.baseValue',
                    label: 'Amount in Transaction Currency',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'claimsExpenses.baseValue',
                    label: 'Claims Expenses',
                ),
                new BooleanFieldConfiguration(
                    id: 'indirectTransactions',
                    label: 'Indirect Transactions',
                    help: 'Boolean',
                ),
                new LiteralFieldConfiguration(
                    id: 'internalId',
                    label: 'Internal ID of Transaction',
                    help: 'Enter the contract number or other internal code of the transaction',
                ),
                new BooleanFieldConfiguration(
                    id: 'singleEconomicOperation',
                    label: 'Single Economic Operation',
                    help: 'Boolean',
                ),
                new LookupFieldConfiguration(
                    id: 'traceId',
                    label: 'Trace ID',
                    help: 'Select the Trace ID according to Group Policy',
                    class: TraceId::class,
                    lookupField: 'name'
                ),
                new LookupFieldConfiguration(
                    id: 'transferPricingMethod',
                    label: 'Transfer Pricing Method',
                    class: PricingMethod::class,
                    lookupField: 'name',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'interestOnDeposit.baseValue',
                    label: 'Interest on deposit',
                ),
                new LookupFieldConfiguration(
                    id: 'lineOfBusiness',
                    label: 'Line of Business',
                    class: LineOfBusiness::class,
                    lookupField: 'name',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'maxCoverByReinsurer.baseValue',
                    label: 'Maximum Cover by Reinsurer',
                ),
                new DateFieldConfiguration(
                    id: 'validityPeriodStartDate',
                    label: 'Validity period start date',
                ),
                new DateFieldConfiguration(
                    id: 'validityPeriodExpiryDate',
                    label: 'Validity period expiry date',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'netReceivables.baseValue',
                    label: 'Net Receivables',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'totalReinsuranceRecoverable.baseValue',
                    label: 'Total reinsurance Recoverable at Reporting date',
                ),
                new MoneyAmountFieldConfiguration(
                    id: 'reinsuranceResult.baseValue',
                    label: 'Reinsurance Result(for Reinsured Entity)',
                ),
            ],
            updateMatchFields: [],
            factory: 'U2\Task\TaskTypeFactory:createWithDefaults',
            factoryArguments: [Igt4Transaction::class],
            help: null
        );
    }
}
