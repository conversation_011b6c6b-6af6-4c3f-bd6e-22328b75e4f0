<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Collection;

use U2\Entity\Item;
use U2\Entity\ItemFormula;
use U2\Import\Configuration\Field\FormulaFieldConfiguration;
use U2\Import\Configuration\Field\LookupFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class ItemFormulaConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: ItemFormula::class,
            fields: [
                new LookupFieldConfiguration(
                    id: 'item',
                    label: 'Item',
                    class: Item::class,
                    lookupField: 'refId',
                ),
                new FormulaFieldConfiguration(
                    id: 'formulaString',
                    label: 'Formula',
                ),
            ],
            updateMatchFields: ['item'],
            factory: null,
            factoryArguments: ['item'],
            help: null
        );
    }
}
