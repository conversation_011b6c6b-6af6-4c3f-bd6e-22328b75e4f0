<?php

declare(strict_types=1);
namespace U2\Import;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class ResultTypes extends AbstractConstantChoiceBag
{
    public const string SUCCESS = 'success';

    public const string FAIL = 'fail';

    public static function getReadableMap(): array
    {
        return [
            self::SUCCESS => 'u2.import.result_types.success',
            self::FAIL => 'u2.import.result_types.fail',
        ];
    }
}
