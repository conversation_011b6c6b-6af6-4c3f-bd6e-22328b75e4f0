<?php

declare(strict_types=1);
namespace U2\Encoder;

use Symfony\Component\Serializer\Encoder\ContextAwareEncoderInterface;

final class BinaryDecoder implements ContextAwareEncoderInterface
{
    public const string FORMAT = 'binary';

    public function supportsEncoding(string $format, array $context = []): bool
    {
        return self::FORMAT === $format;
    }

    public function encode($data, string $format, array $context = []): string
    {
        return $data;
    }
}
