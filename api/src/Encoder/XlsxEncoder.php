<?php

declare(strict_types=1);
namespace U2\Encoder;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Shared\File;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Symfony\Component\Serializer\Encoder\DecoderInterface;
use Symfony\Component\Serializer\Encoder\EncoderInterface;
use Symfony\Component\Serializer\Exception\UnexpectedValueException;

class XlsxEncoder implements EncoderInterface, DecoderInterface
{
    public const string FORMAT = 'xlsx';
    public const string KEY_SEPARATOR_KEY = 'xlsx_key_separator';
    public const string HEADERS_KEY = 'xlsx_headers';
    public const string ESCAPE_FORMULAS_KEY = 'xlsx_escape_formulas';
    public const string NO_HEADERS_KEY = 'no_headers';
    public const string OUTPUT_UTF8_BOM_KEY = 'output_utf8_bom';
    public const string AS_COLLECTION_KEY = 'as_collection';

    private const string UTF8_BOM = "\xEF\xBB\xBF";

    private const array FORMULAS_START_CHARACTERS = ['=', '-', '+', '@'];

    /**
     * @var array<mixed>
     */
    private array $defaultContext = [
        self::KEY_SEPARATOR_KEY => '.',
        self::HEADERS_KEY => [],
        self::ESCAPE_FORMULAS_KEY => false,
        self::NO_HEADERS_KEY => false,
        self::OUTPUT_UTF8_BOM_KEY => false,
        self::AS_COLLECTION_KEY => true,
    ];

    /**
     * @param array<mixed> $defaultContext
     */
    public function __construct(array $defaultContext = [])
    {
        $this->defaultContext = array_merge($this->defaultContext, $defaultContext);
    }

    public function encode($data, string $format, array $context = []): string
    {
        if (!is_iterable($data)) {
            $data = [[$data]];
        } elseif (empty($data)) {
            $data = [[]];
        } else {
            // Sequential arrays of arrays are considered as collections
            $i = 0;
            foreach ($data as $key => $value) {
                if ($i !== $key || !\is_array($value)) {
                    $data = [$data];
                    break;
                }

                ++$i;
            }
        }

        [$keySeparator, $headers, $escapeFormulas, $noHeaders, $outputBom] = $this->getXlsxOptions($context);

        foreach ($data as &$value) {
            $flattened = [];
            $this->flatten($value, $flattened, $keySeparator, '', $escapeFormulas);
            $value = $flattened;
        }

        unset($value);

        $spreadsheet = new Spreadsheet();

        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->setUseDiskCaching(true);

        $worksheet = $spreadsheet->getActiveSheet();
        $rowIndex = 1;

        if (empty($headers)) {
            $headers = $this->extractHeaders($data);
        }

        if (!$noHeaders) {
            $columnIndex = 1;

            foreach ($headers as $header) {
                $worksheet->setCellValue([$columnIndex++, $rowIndex], $header);
            }

            ++$rowIndex;
        }

        if ($headers !== array_values($headers)) {
            $headers = array_keys($headers);
        }

        $headers = array_fill_keys($headers, null);

        foreach ($data as $row) {
            $cells = array_intersect_key($row, $headers);
            $columnIndex = 1;

            foreach ($headers as $header => $fakeValue) {
                $worksheet->setCellValue([$columnIndex++, $rowIndex], $cells[$header] ?? null);
            }

            ++$rowIndex;
        }

        $tempFileName = tempnam(File::sysGetTempDir(), 'symfony-serializer-xlsx-');
        \assert(false !== $tempFileName);
        $writer->save($tempFileName);

        $handle = fopen($tempFileName, 'rb+');
        \assert(false !== $handle);
        $value = stream_get_contents($handle);
        \assert(false !== $value);
        fclose($handle);

        unlink($tempFileName);

        if ($outputBom) {
            $match = preg_match('//u', $value);
            if (false === $match || 0 === $match) {
                throw new UnexpectedValueException('You are trying to add a UTF-8 BOM to a non UTF-8 text.');
            }

            $value = self::UTF8_BOM . $value;
        }

        return $value;
    }

    public function supportsEncoding(string $format): bool
    {
        return self::FORMAT === $format;
    }

    /**
     * @return array<mixed>
     */
    public function decode(string $data, string $format, array $context = []): array
    {
        if (empty($data)) {
            return [];
        }

        $tempFileName = tempnam(File::sysGetTempDir(), 'symfony-serializer-xlsx-');
        \assert(false !== $tempFileName);
        $handle = fopen($tempFileName, 'wb+');
        \assert(false !== $handle);
        fwrite($handle, $data);
        fclose($handle);

        [, $headers, , $noHeaders, , $asCollection] = $this->getXlsxOptions($context);

        $reader = new Xlsx();
        $spreadSheet = $reader->load($tempFileName);

        unlink($tempFileName);

        $result = $spreadSheet->getActiveSheet()->toArray();

        if ($noHeaders) {
            $headers = [];
            $firstRowCount = \count($result[0]);

            for ($i = 0; $i < $firstRowCount; ++$i) {
                $headers[] = $i;
            }
        } elseif (empty($headers) && isset($result[0])) {
            $headers = array_shift($result);
        }

        $headerCount = \count($headers);

        $result = array_map(static function (array $row) use ($headers, $headerCount): array {
            $rowCount = \count($row);

            if ($headerCount < $rowCount) {
                $row = \array_slice($row, 0, $headerCount);
            }

            if ($headerCount > $rowCount) {
                $row = array_pad($row, $headerCount, null);
            }

            return array_combine($headers, $row);
        }, $result);

        if ($asCollection) {
            return $result;
        }

        if (empty($result) || isset($result[1])) {
            return $result;
        }

        return $result[0];
    }

    public function supportsDecoding(string $format): bool
    {
        return self::FORMAT === $format;
    }

    /**
     * @param iterable<int, mixed> $array
     * @param array<string, mixed> $result
     */
    private function flatten(iterable $array, array &$result, string $keySeparator, string $parentKey = '', bool $escapeFormulas = false): void
    {
        foreach ($array as $key => $value) {
            if (is_iterable($value)) {
                $this->flatten($value, $result, $keySeparator, $parentKey . $key . $keySeparator, $escapeFormulas);
            } elseif ($escapeFormulas && \is_string($value) && \in_array($value[0], self::FORMULAS_START_CHARACTERS, true)) {
                $result[$parentKey . $key] = "\t" . $value;
            } elseif (false === $value) {
                $result[$parentKey . $key] = 0;
            } elseif (true === $value) {
                $result[$parentKey . $key] = 1;
            } else {
                $result[$parentKey . $key] = $value;
            }
        }
    }

    /**
     * @param array<mixed> $context
     *
     * @return array{string,string,bool,bool,bool,bool}
     */
    private function getXlsxOptions(array $context): array
    {
        /** @var string $keySeparator */
        $keySeparator = $context[self::KEY_SEPARATOR_KEY] ?? $this->defaultContext[self::KEY_SEPARATOR_KEY];
        /** @var string $headers */
        $headers = $context[self::HEADERS_KEY] ?? $this->defaultContext[self::HEADERS_KEY];
        /** @var bool $escapeFormulas */
        $escapeFormulas = $context[self::ESCAPE_FORMULAS_KEY] ?? $this->defaultContext[self::ESCAPE_FORMULAS_KEY];
        /** @var bool $noHeaders */
        $noHeaders = $context[self::NO_HEADERS_KEY] ?? $this->defaultContext[self::NO_HEADERS_KEY];
        /** @var bool $outputBom */
        $outputBom = $context[self::OUTPUT_UTF8_BOM_KEY] ?? $this->defaultContext[self::OUTPUT_UTF8_BOM_KEY];
        /** @var bool $asCollection */
        $asCollection = $context[self::AS_COLLECTION_KEY] ?? $this->defaultContext[self::AS_COLLECTION_KEY];

        return [$keySeparator, $headers, $escapeFormulas, $noHeaders, $outputBom, $asCollection];
    }

    /**
     * @param array<int, array<string,mixed >> $data
     *
     * @return array<int, int|string>
     */
    private function extractHeaders(iterable $data): array
    {
        /** @var array<int, int|string> $headers */
        $headers = [];
        $flippedHeaders = [];

        foreach ($data as $row) {
            $previousHeader = null;

            foreach ($row as $header => $_) {
                if (isset($flippedHeaders[$header])) {
                    $previousHeader = $header;
                    continue;
                }

                if (null === $previousHeader) {
                    $n = \count($headers);
                } else {
                    $n = $flippedHeaders[$previousHeader] + 1;

                    for ($j = \count($headers); $j > $n; --$j) {
                        ++$flippedHeaders[$headers[$j] = $headers[$j - 1]];
                    }
                }

                $headers[$n] = $header;
                $flippedHeaders[$header] = $n;
                $previousHeader = $header;
            }
        }

        return $headers;
    }
}
