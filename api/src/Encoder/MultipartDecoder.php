<?php

declare(strict_types=1);
namespace U2\Encoder;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Serializer\Encoder\ContextAwareDecoderInterface;

final class MultipartDecoder implements ContextAwareDecoderInterface
{
    public const string FORMAT = 'multipart';

    public function __construct(private RequestStack $requestStack)
    {
    }

    public function decode(string $data, string $format, array $context = []): ?array
    {
        $request = $this->requestStack->getCurrentRequest();

        if (null === $request) {
            return null;
        }

        return array_map(static function ($element) {
            if (\is_string($element)) {
                // Multipart form values will be encoded in JSON
                $decoded = json_decode($element, true);

                return \is_array($decoded) ? $decoded : $element;
            }

            return $element;
        }, $request->request->all()) + $request->files->all();
    }

    public function supportsDecoding(string $format, array $context = []): bool
    {
        return self::FORMAT === $format;
    }
}
