<?php

declare(strict_types=1);
namespace U2\Controller;

use <PERSON><PERSON>fony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Twig\Environment;
use U2\Util\FlashMessageHandler;

class VueHelper
{
    private Environment $templating;

    public function __construct(
        Environment $templateEngine,
        private readonly FlashMessageHandler $flashMessageHandler,
        private readonly SerializerInterface $serializer,
    ) {
        $this->templating = $templateEngine;
    }

    /**
     * @param array<string, mixed> $formVariables
     * @param array<string, mixed> $extras
     */
    public function createFormJsonResponse(FormInterface $form, string $template, array $formVariables = [], array $extras = []): JsonResponse
    {
        return new JsonResponse(
            [
                'html' => $this->templating->render(
                    $template,
                    array_merge([
                        'form' => $form->createView(),
                    ], $formVariables)
                ),
                'disabled' => $form->isDisabled(),
                'messages' => \array_key_exists('messages', $extras) ? $extras['messages'] : $this->flashMessageHandler->all(),
                ...$extras,
            ],
            Response::HTTP_OK
        );
    }

    /**
     * @param array<string, mixed> $extras
     */
    public function createFormJsonResponseForCreated(FormInterface $form, ?string $redirectPath = null, array $extras = []): JsonResponse
    {
        return new JsonResponse(
            [
                'redirect' => $redirectPath,
                'disabled' => $form->isDisabled(),
                ...$extras,
                'messages' => \array_key_exists('messages', $extras) ? $extras['messages'] : $this->flashMessageHandler->all(),
            ],
            Response::HTTP_CREATED
        );
    }

    /**
     * @param array<string, mixed> $formVariables
     * @param array<string, mixed> $extras
     */
    public function createFormJsonResponseForError(FormInterface $form, string $template, array $formVariables = [], array $extras = []): JsonResponse
    {
        return new JsonResponse(
            [
                'html' => $this->templating->render(
                    $template,
                    array_merge([
                        'form' => $form->createView(),
                    ], $formVariables)
                ),
                'disabled' => $form->isDisabled(),
                ...$extras,
                'messages' => \array_key_exists('messages', $extras) ? $extras['messages'] : $this->flashMessageHandler->all(),
            ],
            Response::HTTP_UNPROCESSABLE_ENTITY
        );
    }

    public function createJsonResponseForFormError(FormInterface $form): JsonResponse
    {
        $constraintViolationList = new ConstraintViolationList();
        foreach ($form->getErrors(true) as $error) {
            $constraintViolationList->add(
                new ConstraintViolation(
                    $error->getMessage(),
                    null,
                    [],
                    $form->getData(),
                    $error->getOrigin()?->getName(),
                    null
                )
            );
        }

        return new JsonResponse($this->serializer->serialize($constraintViolationList, 'jsonld', ['api_error_resource' => true]), Response::HTTP_UNPROCESSABLE_ENTITY, [], true);
    }
}
