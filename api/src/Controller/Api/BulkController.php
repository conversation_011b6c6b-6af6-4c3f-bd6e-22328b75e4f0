<?php

declare(strict_types=1);
namespace U2\Controller\Api;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Controller\VueHelper;
use U2\EntityMetadata\Entity\ReadableNameTranslator;
use U2\FeatureFlags\FeatureFlagsTrait;
use U2\Security\Authorization\AuthorizationManager;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\BulkAction\Delete\BulkDeleter;
use U2\Task\BulkAction\Edit\AuthorizationHelper as BulkEditAuthorizationHelper;
use U2\Task\BulkAction\Edit\BulkChange;
use U2\Task\BulkAction\Edit\BulkEditor;
use U2\Task\BulkAction\Edit\FormRecordValidator;
use U2\Task\TaskTypeKnowledge;
use U2\User\CurrentUserProvider;
use U2\Util\StringManipulator;

#[Route(path: '/api/bulk/{shortName}')]
class BulkController
{
    use FeatureFlagsTrait;

    public const string formName = 'bulk_edit_form';

    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly RouterInterface $router,
        private readonly FormFactoryInterface $formFactory,
        private readonly TranslatorInterface $translator,
        private readonly ReadableNameTranslator $readableNameTranslator,
        private readonly EntityManagerInterface $entityManager,
        private readonly AuthorizationManager $authorizationManager,
        private readonly BulkDeleter $bulkDeleter,
        private readonly BulkEditor $bulkEditor,
        private readonly BulkEditAuthorizationHelper $bulkEditAuthorizationHelper,
        private readonly FormRecordValidator $recordsValidator,
        private readonly VueHelper $vueHelper,
    ) {
    }

    #[Route(path: '/delete', name: 'u2_bulk_delete', options: ['expose' => true], methods: [HttpOperation::METHOD_DELETE])]
    public function delete(Request $request, string $shortName): JsonResponse
    {
        /** @var string $requestParameter */
        $requestParameter = $request->query->get('selection', '');
        $ids = StringManipulator::commaSeparatedIdStringToIntegerArray($requestParameter);

        /*
         * TODO: Use an api to handle these deletions
         * Setting the request format will ensure that any error while deleting is returned with hydra data.
         */
        $request->setRequestFormat('jsonld');
        $this->bulkDeleter->deleteById(TaskTypeKnowledge::resolveEntityClassByShortName($shortName), $ids);

        return new JsonResponse(
            [],
            Response::HTTP_NO_CONTENT
        );
    }

    #[Route(path: '/edit/form', name: 'u2_bulk_editform', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    public function editForm(Request $request, string $shortName): JsonResponse
    {
        /** @var string $selectedRecordIds */
        $selectedRecordIds = $request->query->get('selection', '');

        $recordIds = StringManipulator::commaSeparatedIdStringToIntegerArray($selectedRecordIds);

        if (0 === \count($recordIds)) {
            throw new BadRequestHttpException($this->translator->trans('u2.no_records_selected_delete'));
        }

        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        if (!$this->authorizationManager->isAuthorized(
            $this->currentUserProvider->get(),
            $authorizationItem->value,
            AuthorizationRight::UPDATE->value
        )) {
            throw new AccessDeniedHttpException('You don\'t have permission to edit those records.');
        }

        $routes = TaskTypeKnowledge::getAvailableRoutes($entityClass);

        $bulkFormType = TaskTypeKnowledge::taskTypeClassToBulkFormClassMap[$entityClass];

        return $this->vueHelper->createFormJsonResponse(
            $this->formFactory->createNamed(self::formName, $bulkFormType, null, [
                'records' => $selectedRecordIds,
                'action' => $this->router->generate(
                    'u2_bulk_submiteditform',
                    [
                        'shortName' => $shortName,
                        'records' => $recordIds,
                    ]
                ),
                'blue_print_class_name' => $entityClass,
            ]),
            'task/bulk_edit.html.twig',
            [],
            [
                'recordCount' => \count($recordIds),
                'authorisationItem' => $authorizationItem->value,
                'itemPluralName' => $this->readableNameTranslator->translateClass($entityClass),
                'listPath' => $this->router->generate($routes['list']),
                'newPath' => $this->router->generate($routes['new']),
            ]
        );
    }

    #[Route(path: '/edit', name: 'u2_bulk_submiteditform', options: ['expose' => true], methods: [HttpOperation::METHOD_POST])]
    public function submitEditForm(Request $request, string $shortName): JsonResponse
    {
        /** @var array{"_bulk_edit_selected_ids": string} $formData */
        $formData = $request->get(self::formName);
        $overrideFieldConfigurations = $request->get('override_field_configurations', false);
        $selectedRecordIds = $formData['_bulk_edit_selected_ids'];
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);

        $form = $this->formFactory->createNamed(
            self::formName,
            TaskTypeKnowledge::taskTypeClassToBulkFormClassMap[$entityClass],
            null,
            [
                'records' => $selectedRecordIds,
                'override_field_configurations' => $overrideFieldConfigurations,
                'action' => $this->router->generate(
                    'u2_bulk_submiteditform',
                    [
                        'shortName' => $shortName,
                        'records' => $selectedRecordIds,
                    ]
                ),
                'blue_print_class_name' => $entityClass,
            ]
        );

        $recordIds = StringManipulator::commaSeparatedIdStringToIntegerArray($selectedRecordIds);
        $recordCollection = $this->entityManager->getRepository($entityClass)->findBy(['id' => $recordIds]);

        if (!$this->bulkEditAuthorizationHelper->isUserAuthorisedToAll($recordCollection)) {
            return new JsonResponse(['messages' => [
                'error' => [
                    $this->translator->trans('u2_core.bulk_update.error_no_permission_to_edit_the_selected_records'),
                ],
            ]]);
        }

        $form->handleRequest($request);
        /** @var BulkChange $bulkChange */
        $bulkChange = $form->getData();
        $this->bulkEditor->bulkEdit($bulkChange->getChangeSet(), $recordCollection);
        $this->recordsValidator->validate($form, $recordCollection);

        if ($form->isValid()) {
            $this->entityManager->flush();

            return new JsonResponse(['messages' => [
                'success' => [
                    $this->translator->trans('u2_core.bulk_update.success'),
                ],
            ]]);
        }

        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        $routes = TaskTypeKnowledge::getAvailableRoutes($entityClass);

        return $this->vueHelper->createFormJsonResponseForError($form,
            'task/bulk_edit.html.twig', [],
            [
                'listPath' => $this->router->generate($routes['list']),
                'newPath' => $this->router->generate($routes['new']),
                'formName' => $form->getName(),
                'recordCount' => \count($recordIds),
                'authorisationItem' => $authorizationItem->value,
                'itemPluralName' => $this->readableNameTranslator->translateClassPlural($entityClass),
                'messages' => [
                    'error' => [
                        $this->translator->trans('u2_core.error_could_not_save_check_the_highlighted_fields'),
                    ],
                ],
            ]
        );
    }
}
