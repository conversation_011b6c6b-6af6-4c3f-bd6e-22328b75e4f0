<?php

declare(strict_types=1);
namespace U2\Controller\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Psr\Log\LoggerInterface;

use function Sentry\captureException;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Controller\Helper;
use U2\Exception\DataSourcery\UQLException;
use U2\Security\Authorization\AuthorizationRight;
use U2\Table\TableFactory;
use U2\Task\TaskTypeKnowledge;
use U2\Util\FlashMessageHandler;

#[Route(path: '/api/tasktype/{shortName}/table', name: 'u2_tasktype_table', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
readonly class TaskTypeTable
{
    public function __construct(
        private FlashMessageHandler $flashMessageHandler,
        private Helper $controllerHelper,
        private TableFactory $tableFactory,
        private SerializerInterface $serializer,
        private FlashMessageHandler $messenger,
        private TranslatorInterface $translator,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(string $shortName): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::READ->value, null, 'You do not have permission to view this entry.');

        $table = $this->tableFactory->createForTaskType($entityClass);
        $tableView = $table->getTwigView();

        if ($table->hasErrors()) {
            foreach ($table->getErrors() as $error) {
                $exception = $error->getCausingException();

                if ($exception instanceof UQLException) {
                    continue;
                }

                $errorMessage = $this->translator->trans('u2_core.filter_table.error_occurred_filters_have_been_reset');
                $this->messenger->addError($errorMessage);
                captureException($exception);
                $this->logger->error($error->getMessage(), [
                    'error' => $exception->getMessage(),
                    'file' => $error->getFile(),
                    'line' => $error->getLine(),
                ]);
            }
        }

        $data = [
            'name' => $tableView->getName(),
            'state' => $tableView->getState(),
            'fields' => $tableView->getDataSourceFields(),
            'functions' => $tableView->getExtensions()->getFunctions(),
            'columns' => $tableView->getConfiguration()->getColumnDefinitions()->all(),
            'config' => [
                'hasData' => (\count($tableView->vars['data']) > 0),
                'pagination' => $tableView->getPagination(),
                'paginationEnabled' => $tableView->isPaginationEnabled(),
                'selectionKey' => $tableView->getSelectionKey(),
                'tableName' => $tableView->getName(),
            ],
            'records' => $table->getData(),
            'messages' => $this->flashMessageHandler->all(),
        ];

        return new JsonResponse($this->serializer->serialize($data, 'json', [DateTimeNormalizer::FORMAT_KEY => \DateTimeInterface::ATOM]), Response::HTTP_OK, [], true);
    }
}
