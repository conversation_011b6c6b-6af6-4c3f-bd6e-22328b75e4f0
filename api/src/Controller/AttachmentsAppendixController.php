<?php

declare(strict_types=1);
namespace U2\Controller;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Document\Appendix\ArchiveFactory;
use U2\Entity\Task\TaskType\AbstractDocument;
use U2\Exception\EmptyArchiveException;
use U2\Http\DownloadFilenameSanitizer;
use U2\Security\Voter\DocumentVoterAttributes;
use U2\Task\TaskTypeKnowledge;

#[Route(path: '/structured-document/appendix/attachments')]
class AttachmentsAppendixController
{
    public function __construct(
        private readonly TranslatorInterface $translator,
        private readonly EntityManagerInterface $entityManager,
        private readonly ArchiveFactory $archiveFactory,
        private readonly Helper $controllerHelper,
        private readonly DownloadFilenameSanitizer $downloadFilenameSanitizer,
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    #[Route(path: '/{shortName}/{documentId}/download-archive', name: 'u2_attachmentsappendix_downloadarchive', requirements: ['documentId' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_POST])]
    public function downloadArchive(Request $request, string $shortName, int $documentId): Response
    {
        $document = $this->entityManager->find(TaskTypeKnowledge::resolveEntityClassByShortName($shortName), $documentId);

        if (null === $document) {
            throw new NotFoundHttpException('Task not found');
        }

        \assert($document instanceof AbstractDocument);
        $this->controllerHelper->denyAccessUnlessGranted(DocumentVoterAttributes::viewContent, $document, 'You do not have rights to download attachment archive of this document');

        try {
            $archiveFileName = $this->archiveFactory->create($document);
            $downloadFileName = $this->downloadFilenameSanitizer->sanitize(\sprintf('%d - %s - attachments.zip', $document->getId(), $document->getName()));

            $response = new BinaryFileResponse($archiveFileName);
            $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, $downloadFileName, 'document_attachments.zip');
            $response->headers->set('filename', $downloadFileName);
        } catch (EmptyArchiveException) {
            return new JsonResponse([
                'title' => 'Unable  to generate zip archive',
                'detail' => $this->translator->trans('u2_structureddocument.attachments_archive_is_empty'),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return $response;
    }
}
