<?php

declare(strict_types=1);
namespace U2\Controller;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Twig\Environment;
use U2\Datasheets\DatasheetExcelResponseFactory;
use U2\Datasheets\TemplateProvider;
use U2\Datasheets\View\Unit\UnitViewFactory;
use U2\Entity\AuthorizationItem;
use U2\Form\Type\UnitViewFormType;
use U2\Repository\DatasheetRepository;
use U2\Repository\PeriodRepository;
use U2\Repository\UnitRepository;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\VoterAttributes;

#[Route(path: '/tax-accounting/unit-view/export', name: 'u2_datasheets_unitview_export', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
#[IsGranted(AuthorizationItem::UnitPeriod->value . ':' . AuthorizationRight::READ->value)]
class DatasheetUnitViewXls
{
    public function __construct(
        private readonly Environment $templatingEngine,
        private readonly UnitViewFactory $unitViewFactory,
        private readonly FormFactoryInterface $formFactory,
        private readonly Helper $helper,
        private readonly TemplateProvider $templateProvider,
        private readonly DatasheetRepository $datasheetRepository,
        private readonly PeriodRepository $periodRepository,
        private readonly UnitRepository $unitRepository,
    ) {
    }

    public function __invoke(
        #[MapQueryParameter('datasheet')] int $datasheetId,
        #[MapQueryParameter('period')] int $periodId,
        #[MapQueryParameter('unit')] int $unitId,
    ): StreamedResponse {
        $datasheet = $this->datasheetRepository->find($datasheetId);
        if (null === $datasheet) {
            throw new UnprocessableEntityHttpException('Invalid datasheet provided');
        }
        $period = $this->periodRepository->find($periodId);
        if (null === $period) {
            throw new UnprocessableEntityHttpException('Invalid period provided');
        }
        $unit = $this->unitRepository->find($unitId);
        if (null === $unit) {
            throw new UnprocessableEntityHttpException('Invalid unit provided');
        }

        $unitView = $this->unitViewFactory->create($datasheet, $unit, $period);

        $this->helper->denyAccessUnlessGranted(VoterAttributes::read, $unitView);

        return DatasheetExcelResponseFactory::create(
            $this->templatingEngine->render(
                'datasheet/unit_view_excel_export.html.twig',
                [
                    'form' => $this->formFactory->create(UnitViewFormType::class, $unitView)->createView(),
                    'unit' => $unit,
                    'period' => $period,
                    'datasheet_name' => $datasheet->getName(),
                    'datasheet_template' => $this->templateProvider->getContent($datasheet),
                ]
            ),
            $datasheet->getName()
        );
    }
}
