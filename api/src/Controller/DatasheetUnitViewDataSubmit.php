<?php

declare(strict_types=1);
namespace U2\Controller;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Datasheets\View\Unit\UnitView;
use U2\Datasheets\View\Unit\UnitViewFactory;
use U2\Datasheets\View\Unit\UnitViewManager;
use U2\Entity\AuthorizationItem;
use U2\Form\Type\UnitViewFormType;
use U2\Repository\DatasheetCollectionRepository;
use U2\Repository\DatasheetRepository;
use U2\Repository\PeriodRepository;
use U2\Repository\UnitPeriodRepository;
use U2\Repository\UnitRepository;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\VoterAttributes;
use U2\Unit\Period\ErrorHandler;

#[Route(path: '/tax-accounting/unit-view/submit', name: 'u2_layout_unitviewsubmit', options: ['expose' => true], methods: [HttpOperation::METHOD_POST], format: 'json')]
#[IsGranted(AuthorizationItem::UnitPeriod->value . ':' . AuthorizationRight::READ->value)]
class DatasheetUnitViewDataSubmit
{
    public function __construct(
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly ErrorHandler $errorHandler,
        private readonly UnitViewFactory $unitViewFactory,
        private readonly FormFactoryInterface $formFactory,
        private readonly Helper $helper,
        private readonly UnitViewManager $manager,
        private readonly TranslatorInterface $translator,
        private readonly DatasheetRepository $datasheetRepository,
        private readonly PeriodRepository $periodRepository,
        private readonly UnitRepository $unitRepository,
        private readonly DatasheetCollectionRepository $datasheetCollectionRepository,
        private readonly UnitPeriodRepository $unitPeriodRepository,
    ) {
    }

    public function __invoke(
        Request $request,
        #[MapQueryParameter('layout')] int $layoutId,
        #[MapQueryParameter('period')] int $periodId,
        #[MapQueryParameter('unit')] int $unitId,
        #[MapQueryParameter('layoutCollection')] string $layoutCollectionId,
    ): JsonResponse {
        $layout = $this->datasheetRepository->findEager($layoutId);
        if (null === $layout) {
            throw new UnprocessableEntityHttpException('Invalid layout provided');
        }
        $period = $this->periodRepository->find($periodId);
        if (null === $period) {
            throw new UnprocessableEntityHttpException('Invalid period provided');
        }
        $unit = $this->unitRepository->find($unitId);
        if (null === $unit) {
            throw new UnprocessableEntityHttpException('Invalid unit provided');
        }

        $layoutCollection = $this->datasheetCollectionRepository->find($layoutCollectionId);
        if (null === $layoutCollection) {
            throw new UnprocessableEntityHttpException('Invalid unit provided');
        }

        $unitPeriods = $this->unitPeriodRepository->findByLayoutCollectionPeriodAndUnit(
            $unit,
            $period,
            $layoutCollection
        );
        $unitPeriod = 1 === \count($unitPeriods) ? $unitPeriods[0] : null;

        $unitView = $this->unitViewFactory->create($layout, $unit, $period, $unitPeriod?->getTask());
        $this->helper->denyAccessUnlessGranted(VoterAttributes::read, $unitView);

        /** @var FormInterface<UnitView> $form */
        $form = $this->formFactory->create(
            UnitViewFormType::class,
            $unitView,
            [
                'disabled' => false === $this->authorizationChecker->isGranted(VoterAttributes::write, $unitView),
            ]
        );

        if ($form->isDisabled()) {
            return new JsonResponse(
                [
                    'messages' => ['error' => [$this->translator->trans('u2.datasheets.unit_view.form_is_disabled')]],
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        // get map of original field data
        $originalFieldData = [];
        foreach ($unitView->getData() as $fieldData) {
            $originalFieldData[$fieldData->getField()->getName()] = $fieldData->getData()->getValue();
        }

        $form->handleRequest($request);
        if ($form->isValid()) {
            /** @var UnitView $unitView */
            $unitView = $form->getData();
            $changedValues = [];
            foreach ($unitView->getData() as $fieldData) {
                $fieldName = $fieldData->getField()->getName();
                if (isset($originalFieldData[$fieldName])) {
                    $originalValue = $originalFieldData[$fieldName];
                    $itemUnitValue = $fieldData->getData();
                    if ($itemUnitValue->getValue() !== $originalValue) {
                        $changedValues[] = $itemUnitValue;
                    }
                }
            }

            $previousLimit = (int) \ini_get('max_execution_time');
            set_time_limit(60);
            try {
                $this->manager->save($changedValues);

                /*
                 * Recreate the form because dependent values that were updated
                 * are not automatically set back to the form
                 */
                $form = $this->formFactory->create(
                    UnitViewFormType::class,
                    $unitView,
                    [
                        'disabled' => false === $this->authorizationChecker->isGranted(VoterAttributes::write, $unitView),
                    ]
                );

                return new JsonResponse(
                    [
                        'messages' => ['success' => [$this->translator->trans('u2_core.success_saved')]],
                    ],
                    Response::HTTP_OK
                );
            } catch (\Exception $exception) {
                $error = $this->errorHandler->extractError($exception);
                if (null === $error) {
                    throw $exception;
                }

                return new JsonResponse(
                    [
                        'error' => $error,
                    ],
                    Response::HTTP_BAD_REQUEST
                );
            } finally {
                set_time_limit($previousLimit);
            }
        }

        return new JsonResponse(
            [
                'disabled' => $form->isDisabled(),
                'messages' => [],
                'violations' => $this->extractAndTransformFormErrorsToViolations($form),
            ],
            Response::HTTP_UNPROCESSABLE_ENTITY
        );
    }

    /**
     * @param FormInterface<UnitView> $form
     *
     * @return array<array{message: string, propertyPath: string}>
     */
    private function extractAndTransformFormErrorsToViolations(FormInterface $form): array
    {
        $violations = [];
        foreach ($form->getErrors(true) as $error) {
            /** @var ConstraintViolation $cause */
            $cause = $error->getCause();

            $propertyPath = $cause->getPropertyPath();
            if (1 === preg_match('/children\[(.*?)\]/', $propertyPath, $matches)) {
                $propertyPath = $matches[1];
            }

            $violations[] = [
                'message' => $error->getMessage(),
                'propertyPath' => $propertyPath,
            ];
        }

        return $violations;
    }
}
