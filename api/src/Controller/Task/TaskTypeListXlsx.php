<?php

declare(strict_types=1);
namespace U2\Controller\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use U2\Controller\Helper;
use U2\EntityMetadata\MetadataProvider;
use U2\Http\Response\XlsxResponse;
use U2\Security\Authorization\AuthorizationRight;
use U2\Table\TableFactory;
use U2\Task\TaskTypeKnowledge;
use U2\Util\DateTime;

#[Route(path: '/tasktype/{shortName}/list.xlsx', name: 'u2_tasktype_listxlsx', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
class TaskTypeListXlsx extends AbstractController
{
    public function __construct(
        private readonly TableFactory $tableFactory,
        private readonly Helper $controllerHelper,
        private readonly MetadataProvider $metadataProvider,
    ) {
    }

    public function __invoke(string $shortName): Response
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::READ->value, null, 'You do not have permission to view this entry.');

        $shortName = $this->metadataProvider->getShortName($entityClass);

        return new XlsxResponse(
            $this->tableFactory->createForTaskType($entityClass, true)->getDataNormalized(),
            null,
            $shortName . ' ' . DateTime::createNow()->format(\DATE_W3C) . '.xlsx'
        );
    }
}
