<?php

declare(strict_types=1);
namespace U2\Controller\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use U2\Controller\Helper;
use U2\Security\Authorization\AuthorizationRight;
use U2\Table\TableFactory;
use U2\Task\TaskTypeKnowledge;

#[Route(path: '/tasktype/{shortName}/list.json', name: 'u2_tasktype_listjson', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
class TaskTypeListJson extends AbstractController
{
    public function __construct(
        private readonly TableFactory $tableFactory,
        private readonly Helper $controllerHelper,
    ) {
    }

    public function __invoke(string $shortName): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::READ->value, null, 'You do not have permission to view this entry.');

        return new JsonResponse(
            $this->tableFactory->createForTaskType($entityClass, true)->getData()
        );
    }
}
