<?php

declare(strict_types=1);
namespace U2\Controller\Task;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Controller\Helper;
use U2\Controller\VueHelper;
use U2\Entity\Interfaces\TaskTypeWithUnitHierarchy;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType;
use U2\Exception\WorkflowException;
use U2\Form\NonAutoAffirmativeDisabledFormValidator;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\DocumentVoterAttributes;
use U2\Security\Voter\VoterAttributes;
use U2\Task\Interfaces\Typeable;
use U2\Task\TaskTypeFactory;
use U2\Task\TaskTypeKnowledge;
use U2\Util\FlashMessageHandler;

class TaskTypeForm extends AbstractController
{
    public function __construct(
        private readonly FlashMessageHandler $flashMessageHandler,
        private readonly RequestStack $requestStack,
        private readonly Helper $controllerHelper,
        private readonly Security $security,
        private readonly RouterInterface $router,
        private readonly NonAutoAffirmativeDisabledFormValidator $nonAutoAffirmativeDisabledFormValidator,
        private readonly EntityManagerInterface $entityManager,
        private readonly VueHelper $vueHelper,
        private readonly TranslatorInterface $translator,
        private readonly TaskTypeFactory $taskTypeFactory,
    ) {
    }

    #[Route(path: '/tasktype/{shortName}/new/form', name: 'u2_tasktype_newform', options: ['expose' => true], methods: [HttpOperation::METHOD_GET], condition: 'request.isXmlHttpRequest()')]
    public function newForm(string $shortName): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.');

        return $this->vueHelper->createFormJsonResponse($this->getForm($entityClass), TaskTypeKnowledge::resolveFormTemplateByEntityClass($entityClass));
    }

    #[Route(path: '/tasktype/{shortName}/new/form', name: 'u2_tasktype_submitnewform', options: ['expose' => true], methods: [HttpOperation::METHOD_POST], condition: 'request.isXmlHttpRequest()')]
    public function submitNewForm(string $shortName, Request $request): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.');

        $form = $this->getForm($entityClass);
        $form->handleRequest($request);
        $taskType = $form->getData();
        \assert($taskType instanceof TaskType);

        if ($this->nonAutoAffirmativeDisabledFormValidator->isValid($form)) {
            try {
                $this->entityManager->persist($taskType);
                $this->entityManager->flush();
                $this->flashMessageHandler->addSavedMessage();

                return $this->vueHelper->createFormJsonResponseForCreated($form, $this->router->generate(TaskTypeKnowledge::getAvailableRoutes($entityClass)['edit'], ['id' => $taskType->getId()]));
            } catch (WorkflowException) {
                $this->flashMessageHandler->addError($this->translator->trans('u2.workflow.configuration_error'));
            }
        }

        return $this->vueHelper->createFormJsonResponseForError($form, TaskTypeKnowledge::resolveFormTemplateByEntityClass($taskType::class));
    }

    #[Route(path: '/tasktype/{shortName}/{id}/edit/form', name: 'u2_tasktype_editform', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_GET], condition: 'request.isXmlHttpRequest()')]
    public function editForm(string $shortName, int $id): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $taskType = $this->entityManager->find($entityClass, $id);
        if (!$taskType instanceof TaskType) {
            throw new NotFoundHttpException();
        }

        if ($taskType instanceof StructuredDocumentInterface) {
            $this->controllerHelper->denyAccessUnlessGranted(DocumentVoterAttributes::viewConfiguration, $taskType, 'You do not have rights to configure this entity');
        } else {
            $this->controllerHelper->denyAccessUnlessGranted(VoterAttributes::read, $taskType, 'You do not have permission to view this entry.');
        }

        $form = $this->getForm($entityClass, $taskType);

        return $this->vueHelper->createFormJsonResponse($form, TaskTypeKnowledge::resolveFormTemplateByEntityClass($entityClass));
    }

    #[Route(path: '/tasktype/{shortName}/{id}/edit/form', name: 'u2_tasktype_submiteditform', requirements: ['id' => '\d+'], options: ['expose' => true], methods: [HttpOperation::METHOD_POST], condition: 'request.isXmlHttpRequest()')]
    public function submitEditForm(string $shortName, int $id, Request $request): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $taskType = $this->entityManager->find($entityClass, $id);
        if (!$taskType instanceof TaskType) {
            throw new NotFoundHttpException();
        }

        if ($taskType instanceof StructuredDocumentInterface) {
            $this->controllerHelper->denyAccessUnlessGranted(DocumentVoterAttributes::editConfiguration, $taskType, 'You do not have rights to configure this entity');
        } else {
            $this->controllerHelper->denyAccessUnlessGranted(VoterAttributes::write, $taskType, 'You do not have permission to view this entry.');
        }

        $form = $this->getForm($entityClass, $taskType);
        $form->handleRequest($request);
        if ($this->nonAutoAffirmativeDisabledFormValidator->isValid($form)) {
            if ($taskType instanceof TaskTypeWithUnitHierarchy) {
                $taskType->setUnitHierarchyDefinitionsChanged(false);
            }
            $this->entityManager->flush();
            $this->flashMessageHandler->addSavedMessage();

            return $this->vueHelper->createFormJsonResponse($form, TaskTypeKnowledge::resolveFormTemplateByEntityClass($entityClass));
        }

        return $this->vueHelper->createFormJsonResponseForError($form, TaskTypeKnowledge::resolveFormTemplateByEntityClass($entityClass));
    }

    /**
     * @param class-string<TaskType> $entityClass
     */
    private function getForm(string $entityClass, ?TaskType $entity = null): FormInterface
    {
        $request = $this->requestStack->getCurrentRequest();
        \assert(null !== $request);

        if (null === $entity) {
            $entity = $this->createEntity($entityClass, $request);
        }

        // A "new" form
        if (null === $entity->getId()) {
            return $this->createForm(
                TaskTypeKnowledge::resolveFormClassByEntityClass($entityClass),
                $entity,
                ['disabled' => false]
            );
        }

        if ($entity instanceof StructuredDocumentInterface) {
            $disabled = false === $this->security->isGranted(DocumentVoterAttributes::editConfiguration, $entity);
        } else {
            $disabled = false === $this->security->isGranted(VoterAttributes::write, $entity);
        }

        // An "edit" form
        return $this->createForm(
            TaskTypeKnowledge::resolveFormClassByEntityClass($entityClass),
            $entity,
            ['disabled' => $disabled]
        );
    }

    /**
     * @param class-string<TaskType> $taskTypeClass
     */
    private function createEntity(string $taskTypeClass, Request $request): TaskType
    {
        $taskType = $this->taskTypeFactory->createWithDefaults($taskTypeClass);

        if (!($taskType instanceof Typeable)) {
            return $taskType;
        }

        /** @var string $type */
        $type = $request->query->get('type');

        if (!$taskType->isTypeSupported($type)) {
            throw new BadRequestHttpException('Type not supported');
        }

        $taskType->setType($type);

        return $taskType;
    }
}
