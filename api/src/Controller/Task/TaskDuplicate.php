<?php

declare(strict_types=1);
namespace U2\Controller\Task;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Controller\Helper;
use U2\Entity\Task\Task;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\TaskTypeCopier;
use U2\Task\TaskTypeKnowledge;
use U2\Task\TaskTypeResolver;
use U2\Util\FlashMessageHandler;

#[Route(path: '/task/{id}/duplicate', name: 'u2_tasktype_duplicate', options: ['expose' => true], methods: [HttpOperation::METHOD_POST])]
class TaskDuplicate
{
    public function __construct(
        private readonly FlashMessageHandler $flashMessageHandler,
        private readonly Helper $controllerHelper,
        private readonly RouterInterface $router,
        private readonly EntityManagerInterface $entityManager,
        private readonly TaskTypeCopier $taskTypeCopier,
        private readonly TranslatorInterface $translator,
        private readonly TaskTypeResolver $taskTypeResolver,
        private readonly ValidatorInterface $validator,
    ) {
    }

    public function __invoke(Task $task): JsonResponse
    {
        $taskType = $this->taskTypeResolver->resolve($task);

        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$taskType::class];

        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.');

        $copy = $this->taskTypeCopier->copy($taskType);

        $violations = $this->validator->validate($copy);
        if ($violations->count() > 0) {
            /** @var ConstraintViolation $violation */
            foreach ($violations as $violation) {
                $this->flashMessageHandler->addError((string) $violation->getMessage());
            }

            return new JsonResponse(
                [
                    'messages' => $this->flashMessageHandler->all(),
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
        $this->entityManager->persist($copy);
        $this->entityManager->flush();

        $this->flashMessageHandler->addSuccess($this->translator->trans('u2.task.duplicate.success'));

        return new JsonResponse(
            [
                'redirect' => $this->router->generate(
                    TaskTypeKnowledge::getAvailableRoutes($taskType::class)['edit'],
                    [
                        'id' => $copy->getId(),
                    ]
                ),
                'messages' => $this->flashMessageHandler->all(),
            ],
            Response::HTTP_CREATED
        );
    }
}
