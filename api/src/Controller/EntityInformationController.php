<?php

declare(strict_types=1);
namespace U2\Controller;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Serializer\SerializerInterface;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType;
use U2\EntityMetadata\Entity\ReadableNameTranslator;
use U2\Security\Authorization\AssignmentHelper;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\VoterAttributes;
use U2\Task\Interfaces\Typeable;
use U2\Task\TaskListInfoFactory;
use U2\Task\TaskTypeKnowledge;

class EntityInformationController
{
    public function __construct(
        private readonly Helper $controllerHelper,
        private readonly AssignmentHelper $authorizationHelper,
        private readonly RouterInterface $router,
        private readonly TaskListInfoFactory $taskListInfoFactory,
        private readonly SerializerInterface $serializer,
        private readonly ReadableNameTranslator $readableNameTranslator,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[Route(path: '/{shortName}/list-info', name: 'u2_entityinformation_listinfo', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    public function listInfo(string $shortName): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::READ->value, null, 'You do not have permission to view this entry.');

        return new JsonResponse(
            $this->serializer->serialize(
                $this->taskListInfoFactory->createForEntity($entityClass),
                'jsonld',
                [
                    'groups' => [
                        'task-list-info:read',
                        'saved-filter:read',
                    ],
                ],
            ),
            Response::HTTP_OK,
            [],
            true
        );
    }

    #[Route(path: '/{shortName}/{id}/entity-information', name: 'u2_entityinformation_entityinformation', options: ['expose' => true], methods: [HttpOperation::METHOD_GET], condition: 'request.isXmlHttpRequest()')]
    public function entityInformation(string $shortName, int $id): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);

        /** @var TaskType|null $entity */
        $entity = $this->entityManager->find($entityClass, $id);

        if (null === $entity) {
            throw new NotFoundHttpException('The requested task does not exist.');
        }

        $this->controllerHelper->denyAccessUnlessGranted(VoterAttributes::read, $entity, 'You do not have permission to view this entity.');

        $task = $entity->getTask();
        $assignee = $task->getAssignee();

        return new JsonResponse([
            'id' => $entity->getId(),
            'assignee' => null === $assignee ? null : [
                'id' => $assignee->getId(),
                'valid' => $this->authorizationHelper->canRecordBeAssignedToUser($entity, $assignee),
            ],
            'readableName' => $this->readableNameTranslator->translateClass($entityClass),
            'shortName' => $shortName,
            'taskId' => $task->getId()->toRfc4122(),
            'optionsForNew' => is_a($entityClass, Typeable::class, true) ? $entityClass::getTypesReadable() : null,
        ]);
    }

    #[Route(path: '/{shortName}/entity-metatdata', name: 'u2_entityinformation_entitymetadata', options: ['expose' => true], methods: [HttpOperation::METHOD_GET], condition: 'request.isXmlHttpRequest()')]
    public function entityMetaData(string $shortName): JsonResponse
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);

        return new JsonResponse([
            'readableName' => $this->readableNameTranslator->translateClass($entityClass),
            'shortName' => $shortName,
            'extra' => $this->gatherTaskTypeExtras($entityClass),
        ]);
    }

    /**
     * @param class-string<TaskType> $entityClass
     *
     * @return array<string,string>
     */
    private function gatherTaskTypeExtras(string $entityClass): array
    {
        $extras = [];
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        $routes = TaskTypeKnowledge::getAvailableRoutes($entityClass);
        $extras['listPath'] = $this->router->generate($routes['list']);

        if (is_a($entityClass, StructuredDocumentInterface::class, true)) {
            $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have rights to configure this entity');

            return $extras;
        }

        $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::UPDATE->value, null, 'You do not have permission to view this entry.');

        return $extras;
    }
}
