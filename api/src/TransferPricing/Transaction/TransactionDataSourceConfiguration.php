<?php

declare(strict_types=1);
namespace U2\TransferPricing\Transaction;

use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\AuthorizationItem;
use U2\Entity\Currency;
use U2\Entity\Task\TaskType\Transaction;
use U2\Event\DataSourcery\PostGenerateQueryBuilderEvent;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\DataSource\AbstractTaskTypeDataSourceConfiguration;
use U2\Task\DataSource\PeriodFieldAdder;

/**
 * Implements a data source for the transaction table.
 */
class TransactionDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);

        PeriodFieldAdder::add($builder);
        TransactionFieldAdder::add($builder);

        $builder
            ->addField(
                'PreviousPeriodBookValue',
                'number',
                'previousPeriodBookValue'
            )
            ->addField(
                'PreviousPeriodBookValueCurrencyIso',
                'string',
                'previousPeriodBookValueCurrency.iso4217code'
            )
            ->addField(
                'PreviousPeriodBookValueCurrencyName',
                'string',
                'previousPeriodBookValueCurrency.name'
            )
            ->addVectorField(
                'PreviousPeriodBookValueCurrency',
                'string',
                'previousPeriodBookValueCurrency.iso4217code',
                [
                    'iso' => 'PreviousPeriodBookValueCurrencyIso',
                    'name' => 'PreviousPeriodBookValueCurrencyName',
                ],
                [
                    'choices' => [
                        'repository' => Currency::class,
                        'field' => 'iso4217code',
                    ],
                ]
            )
            ->addField(
                'CurrentPeriodInterestExpenses',
                'number',
                'currentPeriodInterestExpenses'
            )
            ->addField(
                'CurrentPeriodInterestExpensesCurrencyIso',
                'string',
                'currentPeriodInterestExpensesCurrency.iso4217code'
            )
            ->addField(
                'CurrentPeriodInterestExpensesCurrencyName',
                'string',
                'currentPeriodInterestExpensesCurrency.name'
            )
            ->addVectorField(
                'CurrentPeriodInterestExpensesCurrency',
                'string',
                'currentPeriodInterestExpensesCurrency.iso4217code',
                [
                    'iso' => 'CurrentPeriodInterestExpensesCurrencyIso',
                    'name' => 'CurrentPeriodInterestExpensesCurrencyName',
                ],
                [
                    'choices' => [
                        'repository' => Currency::class,
                        'field' => 'iso4217code',
                    ],
                ]
            )
            ->addField(
                'CurrentPeriodBookValue',
                'number',
                'currentPeriodBookValue'
            )
            ->addField(
                'CurrentPeriodBookValueCurrencyIso',
                'string',
                'currentPeriodBookValueCurrency.iso4217code'
            )
            ->addField(
                'CurrentPeriodBookValueCurrencyName',
                'string',
                'currentPeriodBookValueCurrency.name'
            )
            ->addVectorField(
                'CurrentPeriodBookValueCurrency',
                'string',
                'currentPeriodBookValueCurrency.iso4217code',
                [
                    'iso' => 'CurrentPeriodBookValueCurrencyIso',
                    'name' => 'CurrentPeriodBookValueCurrencyName',
                ],
                [
                    'choices' => [
                        'repository' => Currency::class,
                        'field' => 'iso4217code',
                    ],
                ]
            )
            ->addField(
                'GuaranteeFeeCurrencyIso',
                'string',
                'guaranteeFeeCurrency.iso4217code'
            )
            ->addField(
                'GuaranteeFeeCurrencyName',
                'string',
                'guaranteeFeeCurrency.name'
            )
            ->addVectorField(
                'GuaranteeFeeCurrency',
                'string',
                'guaranteeFeeCurrency.iso4217code',
                [
                    'iso' => 'GuaranteeFeeCurrencyIso',
                    'name' => 'GuaranteeFeeCurrencyName',
                ],
                [
                    'choices' => [
                        'repository' => Currency::class,
                        'field' => 'iso4217code',
                    ],
                ]
            )
            ->addField(
                'GuaranteeFeeAmount',
                'number',
                'guaranteeFeeAmount'
            );
    }

    protected function addAuthorisationRestrictions(DataSourceBuilder $builder): void
    {
        if ($this->security->isGranted(AuthorizationItem::Transaction->value . ':' . AuthorizationRight::SUPERVISE->value)) {
            return;
        }

        $user = $this->currentUserProvider->get();
        $userAssignedUnits = $this->unitRepository->findUserAssigned($user);

        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                static function (PostGenerateQueryBuilderEvent $event) use ($userAssignedUnits): void {
                    $fromAlias = $event->fromAlias;
                    $queryBuilder = $event->queryBuilder;

                    $queryBuilder->andWhere(
                        $queryBuilder->expr()->orX(
                            $fromAlias . '.unit IN (:units)',
                            $fromAlias . '.partnerUnit IN (:units)'
                        )
                    );
                    $queryBuilder->setParameter(':units', $userAssignedUnits);
                });
    }

    public static function getEntityClass(): string
    {
        return Transaction::class;
    }
}
