<?php

declare(strict_types=1);
namespace U2\TransferPricing\LocalFile;

use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\AbstractTaskCalendar;

class LocalFileCalendar extends AbstractTaskCalendar
{
    public function getAuthorizationNameToWrite(): string
    {
        return AuthorizationItem::LocalFile->value . ':' . AuthorizationRight::CREATE->value;
    }

    public function getAuthorizationNameToRead(): string
    {
        return AuthorizationItem::LocalFile->value . ':' . AuthorizationRight::READ->value;
    }

    protected function getClassName(): string
    {
        return LocalFile::class;
    }
}
