<?php

declare(strict_types=1);
namespace U2\TransferPricing;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\UnitHierarchy;
use U2\Event\DataSourcery\PostGenerateQueryBuilderEvent;
use U2\Repository\UnitRepository;
use U2\Security\Permissions\Assignable\QueryBuilderHelper;
use U2\Task\DataSource\AbstractTaskTypeDataSourceConfiguration;
use U2\Task\DataSource\PeriodFieldAdder;
use U2\User\CurrentUserProvider;
use U2\Workflow\Traits\WorkflowableAwareDataSourceConfigurationTrait;
use U2\Workflow\WorkflowManager;

abstract class AbstractDocumentDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    use WorkflowableAwareDataSourceConfigurationTrait;

    public function __construct(
        Security $security,
        CurrentUserProvider $currentUserProvider,
        WorkflowManager $workflowManager,
        UnitRepository $unitRepository,
        private readonly QueryBuilderHelper $queryBuilderHelper,
        protected readonly AuthorizationCheckerInterface $authorizationChecker,
    ) {
        parent::__construct($currentUserProvider, $workflowManager, $unitRepository, $security);
    }

    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);

        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                function (PostGenerateQueryBuilderEvent $event): void {
                    $event->queryBuilder->leftJoin($event->fromAlias . '.units', 'unit');
                    $event->queryBuilder->addSelect(
                        [
                            'COUNT(DISTINCT unit.id) unit_count',
                        ]
                    );
                }
            );

        PeriodFieldAdder::add($builder);
        $builder

            ->addField(
                'Name',
                'string',
                'name'
            )
            ->addField(
                'Hierarchy',
                'string',
                'unitHierarchy.name',
                [
                    'choices' => [
                        'repository' => UnitHierarchy::class,
                        'field' => 'name',
                    ],
                ]
            )
            ->addNativeField(
                'UnitCount',
                'number',
                'unit_count'
            );
    }

    protected function getWorkflowManager(): WorkflowManager
    {
        return $this->workflowManager;
    }

    protected function addFileCount(DataSourceBuilder $builder): void
    {
        $builder
            ->addNativeField(
                'Files',
                'number',
                'count_files'
            )
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                static function (PostGenerateQueryBuilderEvent $event): void {
                    $event->queryBuilder->leftJoin($event->fromAlias . '.sections', 'section');
                    $event->queryBuilder->leftJoin('section.files', 'sectionFiles');
                    $event->queryBuilder->addSelect(
                        [
                            'COUNT(DISTINCT sectionFiles) count_files',
                        ]
                    );
                });
    }

    protected function addAuthorisationRestrictions(DataSourceBuilder $builder): void
    {
        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                function (PostGenerateQueryBuilderEvent $event): void {
                    if ($this->ignorePermissions()) {
                        return;
                    }
                    $this->queryBuilderHelper->addSecurityCondition($event->queryBuilder, $event->fromAlias);
                });
    }
}
