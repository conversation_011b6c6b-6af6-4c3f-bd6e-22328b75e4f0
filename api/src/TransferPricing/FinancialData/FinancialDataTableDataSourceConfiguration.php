<?php

declare(strict_types=1);
namespace U2\TransferPricing\FinancialData;

use Symfony\Bundle\SecurityBundle\Security;
use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Repository\UnitRepository;
use U2\Task\DataSource\AbstractTaskTypeDataSourceConfiguration;
use U2\Task\DataSource\DataSourceConfigurationBaseLocalGroupMoneyFieldAdder;
use U2\Task\DataSource\PeriodFieldAdder;
use U2\User\CurrentUserProvider;
use U2\Workflow\WorkflowManager;

class FinancialDataTableDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    public function __construct(
        Security $security,
        CurrentUserProvider $currentUserProvider,
        WorkflowManager $workflowManager,
        UnitRepository $unitRepository,
        private readonly DataSourceConfigurationBaseLocalGroupMoneyFieldAdder $baseLocalGroupMoneyDataSourceConfigurationFieldAdder,
    ) {
        parent::__construct($currentUserProvider, $workflowManager, $unitRepository, $security);
    }

    public static function getEntityClass(): string
    {
        return FinancialData::class;
    }

    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);

        $this->baseLocalGroupMoneyDataSourceConfigurationFieldAdder->add($builder, self::getEntityClass());

        PeriodFieldAdder::add($builder);

        $builder
            ->addField(
                'CarryForward',
                'boolean',
                'carryForward'
            )->addField(
                'NumberOfEmployees',
                'number',
                'numberOfEmployees'
            );
    }
}
