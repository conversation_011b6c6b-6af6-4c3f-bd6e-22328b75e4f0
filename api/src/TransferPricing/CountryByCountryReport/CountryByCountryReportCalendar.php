<?php

declare(strict_types=1);
namespace U2\TransferPricing\CountryByCountryReport;

use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\AbstractTaskCalendar;

class CountryByCountryReportCalendar extends AbstractTaskCalendar
{
    public function getAuthorizationNameToWrite(): string
    {
        return AuthorizationItem::CountryByCountryReport->value . ':' . AuthorizationRight::CREATE->value;
    }

    public function getAuthorizationNameToRead(): string
    {
        return AuthorizationItem::CountryByCountryReport->value . ':' . AuthorizationRight::READ->value;
    }

    protected function getClassName(): string
    {
        return CountryByCountryReport::class;
    }
}
