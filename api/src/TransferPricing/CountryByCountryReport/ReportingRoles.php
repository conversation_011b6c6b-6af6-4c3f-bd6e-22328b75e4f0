<?php

declare(strict_types=1);
namespace U2\TransferPricing\CountryByCountryReport;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class ReportingRoles extends AbstractConstantChoiceBag
{
    public const string ultimateParentEntity = 'ultimate';

    public const string surrogateParentEntity = 'surrogate';

    public const string localFiling = 'local';

    public static function getReadableMap(): array
    {
        return [
            self::ultimateParentEntity => 'u2.reporting_roles.ultimateparententity',
            self::surrogateParentEntity => 'u2.reporting_roles.surrogateparententity',
            self::localFiling => 'u2.reporting_roles.localfiling',
        ];
    }
}
