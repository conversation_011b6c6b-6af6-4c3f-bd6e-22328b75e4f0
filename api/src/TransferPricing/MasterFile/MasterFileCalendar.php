<?php

declare(strict_types=1);
namespace U2\TransferPricing\MasterFile;

use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\AbstractTaskCalendar;

class MasterFileCalendar extends AbstractTaskCalendar
{
    public function getAuthorizationNameToWrite(): string
    {
        return AuthorizationItem::MasterFile->value . ':' . AuthorizationRight::CREATE->value;
    }

    public function getAuthorizationNameToRead(): string
    {
        return AuthorizationItem::MasterFile->value . ':' . AuthorizationRight::READ->value;
    }

    protected function getClassName(): string
    {
        return MasterFile::class;
    }
}
