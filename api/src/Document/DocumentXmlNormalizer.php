<?php

declare(strict_types=1);
namespace U2\Document;

use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use U2\Entity\Task\TaskType\AbstractDocument;

class DocumentXmlNormalizer implements NormalizerInterface, SerializerAwareInterface
{
    use SerializerAwareTrait;

    public const string FORMAT = 'xml';

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        return $data instanceof AbstractDocument && self::FORMAT === $format;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return self::FORMAT === $format ? [
            AbstractDocument::class => true,
        ] : [];
    }

    /**
     * @param array<mixed> $context
     *
     * @return array<string, mixed|string>
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        return [
            '@xmlns' => 'http://universalunits.com/schema/document',
            '@xmlns:xsi' => 'http://www.w3.org/2001/XMLSchema-instance',
            '@xsi:schemaLocation' => 'http://universalunits.com/schema/document document-template-1.0.xsd',
            'name' => $object->getName(),
            'description' => $object->getTask()->getDescription(),
            'section' => array_map(
                function ($object) use ($format, $context): string|int|float|bool|\ArrayObject|array|null {
                    \assert($this->serializer instanceof NormalizerInterface);

                    return $this->serializer->normalize($object, $format, $context);
                },
                $object->getSections()->toArray()
            ),
        ];
    }
}
