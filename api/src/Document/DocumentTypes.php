<?php

declare(strict_types=1);
namespace U2\Document;

use U2\Entity\Task\TaskType\AbstractDocument;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\EntityMetadata\Entity\ReadableNameTranslator;

class DocumentTypes
{
    public const string localFile = 'local-file';

    public const string masterFile = 'master-file';

    public const string countryByCountryReport = 'country-by-country-report';

    /**
     * @var array<string, string>
     */
    public const array typeToClassMap = [
        self::localFile => LocalFile::class,
        self::masterFile => MasterFile::class,
        self::countryByCountryReport => CountryByCountryReport::class,
    ];

    public function __construct(
        private readonly ReadableNameTranslator $nameTranslator,
    ) {
    }

    /**
     * @throws \Exception
     *
     * @return class-string<AbstractDocument>
     */
    public function getClassByType(string $documentType): string
    {
        if (\array_key_exists($documentType, self::typeToClassMap)) {
            return self::typeToClassMap[$documentType];
        }

        throw new \Exception(\sprintf("Document type '%s' does not exist", $documentType));
    }

    /**
     * @throws \Exception
     */
    public function getTypeByClass(string $documentClass): string
    {
        foreach (self::typeToClassMap as $type => $class) {
            if ($documentClass === $class) {
                return $type;
            }
        }

        throw new \Exception("No document type exists for $documentClass");
    }

    public function getReadableName(string $type): string
    {
        return $this->nameTranslator->translateClass($this->getClassByType($type));
    }
}
