<?php

declare(strict_types=1);
namespace U2\Document\Structure\SectionListManipulation;

class SectionPlacement
{
    public const string AFTER = 'after';

    public const string BEFORE = 'before';

    public const string SUBSECTION_OF = 'subsection-of';

    public static function isAfter(string $placement): bool
    {
        return self::BEFORE !== $placement;
    }

    public static function isAsSubsection(string $placement): bool
    {
        return self::SUBSECTION_OF === $placement;
    }
}
