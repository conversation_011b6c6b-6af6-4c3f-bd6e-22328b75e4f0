<?php

declare(strict_types=1);
namespace U2\Document\Structure;

class Node
{
    public const string ARRAY_DATA_KEY = 'section';

    public const string ARRAY_CHILDREN_KEY = 'subSections';

    private ?Node $parent = null;

    /**
     * @var Node[]
     */
    private array $children = [];

    /**
     * @param mixed $data generic container for node data
     */
    public function __construct(private readonly int $level, private readonly mixed $data)
    {
    }

    public function getLevel(): int
    {
        return $this->level;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function toArray(): array
    {
        $array = [];
        $array[self::ARRAY_DATA_KEY] = $this->data;
        $array[self::ARRAY_CHILDREN_KEY] = [];
        foreach ($this->children as $child) {
            $array[self::ARRAY_CHILDREN_KEY][] = $child->toArray();
        }

        return $array;
    }

    public function addChild(self $child): void
    {
        $child->setParent($this);
        $this->children[] = $child;
    }

    private function setParent(self $parent): void
    {
        $this->parent = $parent;
    }
}
