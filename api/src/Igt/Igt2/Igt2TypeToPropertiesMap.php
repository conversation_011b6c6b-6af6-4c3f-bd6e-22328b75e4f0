<?php

declare(strict_types=1);
namespace U2\Igt\Igt2;

use U2\Igt\TypeToPropertyMapInterface;
use U2\Igt\TypeToPropertyMapTrait;

class Igt2TypeToPropertiesMap implements TypeToPropertyMapInterface
{
    use TypeToPropertyMapTrait;

    public const array typeToPropertiesMap = [
        'derivatives-forwards' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'assetOrLiabilityUnderlyingDerivative',
            'assetOrLiabilityUnderlyingDerivativeIdType',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'derivatives-futures' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'assetOrLiabilityUnderlyingDerivative',
            'assetOrLiabilityUnderlyingDerivativeIdType',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'derivatives-options' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'assetOrLiabilityUnderlyingDerivative',
            'assetOrLiabilityUnderlyingDerivativeIdType',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'derivatives-others' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'assetOrLiabilityUnderlyingDerivative',
            'assetOrLiabilityUnderlyingDerivativeIdType',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'guarantees-credit-protection' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'counterPartyName',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'guarantees-others' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'counterPartyName',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'swaps-credit-default' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'swaps-others' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'revenuesFromDerivatives',
            'armsLength',
            'transferPricingMethod',
        ],
        'swaps-currency' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'revenuesFromDerivatives',
            'swapDeliveredCurrency',
            'swapReceivedCurrency',
            'armsLength',
            'transferPricingMethod',
        ],
        'swaps-interest-rate' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'instrumentId',
            'instrumentIdType',
            'contractDate',
            'transactionTradeDate',
            'maturityDate',
            'notionalAmount',
            's2CollateralValue',
            'carryingAmount',
            'typeOfProtection',
            'purposeOfInstrument',
            'revenuesFromDerivatives',
            'swapDeliveredInterestRate',
            'swapReceivedInterestRate',
            'armsLength',
            'transferPricingMethod',
        ],
    ];

    /**
     * @return array<string, array<int, string>>
     */
    public static function getTypeToPropertiesMap(): array
    {
        return self::typeToPropertiesMap;
    }
}
