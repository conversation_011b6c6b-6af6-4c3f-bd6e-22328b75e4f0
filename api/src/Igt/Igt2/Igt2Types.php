<?php

declare(strict_types=1);
namespace U2\Igt\Igt2;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class Igt2Types extends AbstractConstantChoiceBag
{
    public const string derivativesFutures = 'derivatives-futures';
    public const string derivativesForwards = 'derivatives-forwards';
    public const string derivativesOptions = 'derivatives-options';
    public const string derivativesOthers = 'derivatives-others';
    public const string guaranteesCreditProtection = 'guarantees-credit-protection';
    public const string guaranteesOthers = 'guarantees-others';
    public const string swapsCreditDefault = 'swaps-credit-default';
    public const string swapsOthers = 'swaps-others';
    public const string swapsInterestRate = 'swaps-interest-rate';
    public const string swapsCurrency = 'swaps-currency';

    public static function getReadableMap(): array
    {
        return [
            self::derivativesFutures => 'Derivatives - Futures',
            self::derivativesForwards => 'Derivatives - Forwards',
            self::derivativesOptions => 'Derivatives - Options',
            self::derivativesOthers => 'Derivatives - Others',
            self::guaranteesCreditProtection => 'Guarantees - Credit Protection',
            self::guaranteesOthers => 'Guarantees - Others',
            self::swapsCreditDefault => 'Swaps - Credit Default',
            self::swapsOthers => 'Swaps - Others',
            self::swapsCurrency => 'Swaps - Currency',
            self::swapsInterestRate => 'Swaps - Interest Rate',
        ];
    }
}
