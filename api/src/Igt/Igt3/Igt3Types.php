<?php

declare(strict_types=1);
namespace U2\Igt\Igt3;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class Igt3Types extends AbstractConstantChoiceBag
{
    // TODO: Remove or re-enable these commented types dependent on whether they are not needed.

    // public const guarantees = 'guarantees';
    // public const commitment = 'commitment';
    // public const letterOfCredit = 'letter-of-credit';0
    public const string undrawnCreditFacilities = 'undrawn-credit-facilities';
    // public const assetsPurchasedUnderOutrightForwardPurchaseAgreements = 'assets-purchased-under-outright-forward-purchase-agreements';
    // public const assetSaleAndRepurchaseAgreementsAsPerArticle12 = 'asset-sale-and-repurchase-agreements-as-per-article12';
    public const string contingentLiabilities = 'contingent-liabilities';
    // public const other = 'other';

    public static function getReadableMap(): array
    {
        return [
            // self::guarantees => 'Guarantees',
            // self::commitment => 'Commitment',
            // self::letterOfCredit => 'Letter of Credit',
            self::undrawnCreditFacilities => 'Undrawn credit facilities',
            // self::assetsPurchasedUnderOutrightForwardPurchaseAgreements => 'Assets purchased under outright forward purchase agreements',
            // self::assetSaleAndRepurchaseAgreementsAsPerArticle12 => 'Asset sale and repurchase agreements as referred to in Article 12(3) and (5) of Directive 86/635/EEC',
            self::contingentLiabilities => 'Contingent Liabilities',
            // self::other => 'Other',
        ];
    }
}
