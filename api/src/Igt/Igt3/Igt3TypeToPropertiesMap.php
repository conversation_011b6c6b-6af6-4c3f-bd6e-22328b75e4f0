<?php

declare(strict_types=1);
namespace U2\Igt\Igt3;

use U2\Igt\TypeToPropertyMapInterface;
use U2\Igt\TypeToPropertyMapTrait;

class Igt3TypeToPropertiesMap implements TypeToPropertyMapInterface
{
    use TypeToPropertyMapTrait;

    public const array typeToPropertiesMap = [
        'guarantees' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'commitment' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'letter-of-credit' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'undrawn-credit-facilities' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'assets-purchased-under-outright-forward-purchase-agreements' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'asset-sale-and-repurchase-agreements-as-per-article12' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'contingent-liabilities' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
        'other' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'valueAtStartDate',
            'valueAtReportingDate',
            'maxContingentLiabilitiesValue',
            'guaranteedAssetsValue',
            'revenuesFromOffBalanceSheetItems',
            'triggerEvent',
            'armsLength',
            'transferPricingMethod',
        ],
    ];

    /**
     * @return array<string, array<int, string>>
     */
    public static function getTypeToPropertiesMap(): array
    {
        return self::typeToPropertiesMap;
    }
}
