<?php

declare(strict_types=1);
namespace U2\Igt\Igt1;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class Igt1Types extends AbstractConstantChoiceBag
{
    public const string bondCollateralized = 'bond-collateralized';
    public const string bondUncollateralized = 'bond-uncollateralized';
    public const string loanCollateralized = 'loan-collateralized';
    public const string loanUncollateralized = 'loan-uncollateralized';
    public const string promissoryNoteCollateralized = 'promissory-note-collateralized';
    public const string promissoryNoteUncollateralized = 'promissory-note-uncollateralized';
    public const string equityTypesDividends = 'equity-types-dividends';
    public const string equityTypesOthers = 'equity-types-others';
    public const string equityTypesSharesParticipation = 'equity-types-shares-participation';
    public const string otherAssetTransferProperties = 'other-asset-transfer-properties';
    public const string otherAssetTransferOthers = 'other-asset-transfer-others';

    public static function getReadableMap(): array
    {
        return [
            self::bondCollateralized => 'Bond - Collateralized',
            self::bondUncollateralized => 'Bond - Uncollateralized',
            self::loanCollateralized => 'Loan - Collateralized',
            self::loanUncollateralized => 'Loan - Uncollateralized',
            self::promissoryNoteCollateralized => 'Promissory Note - Collateralized',
            self::promissoryNoteUncollateralized => 'Promissory Note - Uncollateralized',
            self::equityTypesDividends => 'Equity Type - Dividends',
            self::equityTypesOthers => 'Equity Type - Others',
            self::equityTypesSharesParticipation => 'Equity Type - Shares/Participation',
            self::otherAssetTransferProperties => 'Other Asset Transfer - Properties',
            self::otherAssetTransferOthers => 'Other Asset Transfer - Others',
        ];
    }
}
