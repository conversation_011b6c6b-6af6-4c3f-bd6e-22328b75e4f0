<?php

declare(strict_types=1);
namespace U2\Igt\Igt1;

use U2\Igt\TypeToPropertyMapInterface;
use U2\Igt\TypeToPropertyMapTrait;

class Igt1TypeToPropertiesMap implements TypeToPropertyMapInterface
{
    use TypeToPropertyMapTrait;

    public const array typeToPropertiesMap = [
        'bond-collateralized' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'couponInterestRateType',
            'couponInterestRate',
            'previousPeriodBookValue',
            'couponsAmount',
            'otherPayment',
            'currentPeriodBookValue',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'bond-uncollateralized' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'couponInterestRateType',
            'couponInterestRate',
            'previousPeriodBookValue',
            'couponsAmount',
            'otherPayment',
            'currentPeriodBookValue',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'loan-collateralized' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'couponInterestRateType',
            'couponInterestRate',
            'previousPeriodBookValue',
            'interestPaymentsAmount',
            'otherPayment',
            'currentPeriodBookValue',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'loan-uncollateralized' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'couponInterestRateType',
            'couponInterestRate',
            'previousPeriodBookValue',
            'interestPaymentsAmount',
            'otherPayment',
            'currentPeriodBookValue',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'promissory-note-collateralized' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'couponInterestRateType',
            'couponInterestRate',
            'previousPeriodBookValue',
            'interestPaymentsAmount',
            'otherPayment',
            'currentPeriodBookValue',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'promissory-note-uncollateralized' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'couponInterestRateType',
            'couponInterestRate',
            'previousPeriodBookValue',
            'interestPaymentsAmount',
            'otherPayment',
            'currentPeriodBookValue',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'equity-types-shares-participation' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
        ],
        'equity-types-dividends' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
        ],
        'equity-types-others' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
        ],
        'other-asset-transfer-properties' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'other-asset-transfer-others' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'instrumentId',
            'instrumentIdType',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'contractualAmount',
            'interestPaymentsAmount',
            's2CollateralValue',
            'armsLength',
            'transferPricingMethod',
        ],
    ];

    /**
     * @return array<string, array<int, string>>
     */
    public static function getTypeToPropertiesMap(): array
    {
        return self::typeToPropertiesMap;
    }
}
