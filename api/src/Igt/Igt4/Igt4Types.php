<?php

declare(strict_types=1);
namespace U2\Igt\Igt4;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class Igt4Types extends AbstractConstantChoiceBag
{
    public const string insurance = 'insurance';
    public const string quotaShare = 'quota-share';
    public const string variableQuotaShare = 'variable-quota-share';
    public const string surplus = 'surplus';
    public const string excessOfLossPerEventAndPerRisk = 'excess-of-loss-per-event-and-per-risk';
    public const string excessOfLossPerRisk = 'excess-of-loss-per-risk';
    public const string excessOfLossPerEvent = 'excess-of-loss-per-event';
    public const string excessOfLossBackUp = 'excess-of-loss-per-back-up';
    public const string excessOfLossWithBasisRisk = 'excess-of-loss-with-basis-risk';
    public const string reinstatementCover = 'reinstatement-cover';
    public const string aggregateExcessOfLoss = 'aggregate-excess-of-loss';
    public const string unlimitedExcessOfLoss = 'unlimited-excess-of-loss';
    public const string stopLoss = 'stop-loss';
    public const string otherProportionalTreaties = 'other-proportional-treaties';
    public const string otherNonProportionalTreaties = 'other-non-proportional-treaties';
    public const string financialReinsurance = 'financial-reinsurance';
    public const string facultativeProportional = 'facultative-proportional';
    public const string facultativeNonProportional = 'facultative-non-proportional';

    public static function getReadableMap(): array
    {
        return [
            self::insurance => 'Insurance',
            self::quotaShare => 'Quota Share',
            self::variableQuotaShare => 'Variable Quota Share',
            self::surplus => 'Surplus',
            self::excessOfLossPerEventAndPerRisk => 'Excess of Loss (per Event and per Risk)',
            self::excessOfLossPerRisk => 'Excess of Loss (per Risk)',
            self::excessOfLossPerEvent => 'Excess of Loss (per Event)',
            self::excessOfLossBackUp => 'Excess of Loss (per Back-Up)',
            self::excessOfLossWithBasisRisk => 'Excess of Loss with Basis Risk',
            self::reinstatementCover => 'Reinstatement Cover',
            self::aggregateExcessOfLoss => 'Aggregate Excess of Loss',
            self::unlimitedExcessOfLoss => 'Unlimited Excess of Loss',
            self::stopLoss => 'Stop Loss',
            self::otherProportionalTreaties => 'Other Proportional Treaties',
            self::otherNonProportionalTreaties => 'Other Non-Proportional Treaties',
            self::financialReinsurance => 'Financial Reinsurance',
            self::facultativeProportional => 'Facultative Proportional',
            self::facultativeNonProportional => 'Facultative Non-Proportional',
        ];
    }
}
