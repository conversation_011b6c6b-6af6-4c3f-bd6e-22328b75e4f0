<?php

declare(strict_types=1);
namespace U2\Igt\Igt4;

use U2\Igt\TypeToPropertyMapInterface;
use U2\Igt\TypeToPropertyMapTrait;

class Igt4TypeToPropertiesMap implements TypeToPropertyMapInterface
{
    use TypeToPropertyMapTrait;

    public const array typeToPropertiesMap = [
        'insurance' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'claimsExpenses',
            'armsLength',
            'transferPricingMethod',
        ],
        'quota-share' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'variable-quota-share' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'surplus' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'excess-of-loss-per-event-and-per-risk' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'excess-of-loss-per-risk' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'excess-of-loss-per-event' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'excess-of-loss-per-back-up' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'excess-of-loss-with-basis-risk' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'reinstatement-cover' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'aggregate-excess-of-loss' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'unlimited-excess-of-loss' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'stop-loss' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'other-proportional-treaties' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'other-non-proportional-treaties' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'financial-reinsurance' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'facultative-proportional' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
        'facultative-non-proportional' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'internalId',
            'validityPeriodStartDate',
            'validityPeriodExpiryDate',
            'transactionValue',
            'lineOfBusiness',
            'interestOnDeposit',
            'netReceivables',
            'totalReinsuranceRecoverable',
            'maxCoverByReinsurer',
            'reinsuranceResult',
            'armsLength',
            'transferPricingMethod',
        ],
    ];

    /**
     * @return array<string, array<int, string>>
     */
    public static function getTypeToPropertiesMap(): array
    {
        return self::typeToPropertiesMap;
    }
}
