<?php

declare(strict_types=1);
namespace U2\Igt\Igt5;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class Igt5Types extends AbstractConstantChoiceBag
{
    public const string fees = 'fees';
    public const string commission = 'commission';
    public const string interest = 'interest';
    public const string dividends = 'dividends';
    public const string costOrRevenue = 'cost-or-revenue';
    public const string others = 'others';

    public static function getReadableMap(): array
    {
        return [
            self::fees => 'Fees',
            self::commission => 'Commission',
            self::interest => 'Interest',
            self::dividends => 'Dividends',
            self::costOrRevenue => 'Cost or Revenue',
            self::others => 'Others',
        ];
    }
}
