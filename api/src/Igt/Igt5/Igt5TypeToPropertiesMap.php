<?php

declare(strict_types=1);
namespace U2\Igt\Igt5;

use U2\Igt\TypeToPropertyMapInterface;
use U2\Igt\TypeToPropertyMapTrait;

class Igt5TypeToPropertiesMap implements TypeToPropertyMapInterface
{
    use TypeToPropertyMapTrait;

    public const array typeToPropertiesMap = [
        'fees' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'transactionValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'commission' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'transactionValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'interest' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'transactionValue',
            'armsLength',
            'transferPricingMethod',
        ],
        'dividends' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'transactionValue',
        ],
        'cost-or-revenue' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'transactionValue',
            'serviceType',
            'armsLength',
            'transferPricingMethod',
        ],
        'others' => [
            'indirectTransactions',
            'singleEconomicOperation',
            'traceId',
            'transactionDate',
            'contractDate',
            'contractExpiryDate',
            'transactionValue',
            'armsLength',
            'transferPricingMethod',
        ],
    ];

    /**
     * @return array<string, array<int, string>>
     */
    public static function getTypeToPropertiesMap(): array
    {
        return self::typeToPropertiesMap;
    }
}
