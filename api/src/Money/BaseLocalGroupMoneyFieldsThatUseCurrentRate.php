<?php

declare(strict_types=1);
namespace U2\Money;

class BaseLocalGroupMoneyFieldsThatUseCurrentRate
{
    /**
     * These fields are calculated using the current exchange rate.
     * They are hard coded here because there is not really a nice place to put them.
     * A nicer solution would be to have field configurations for every task field and set
     * some sort of metadata on the field to indicate certain properties and functionality like this.
     * Then it would be central and we can re-usable.
     */
    public const array fields = [
        'contractualAmount',
        'notionalAmount',
        'valueAtStartDate',
        'previousPeriodBookValue',
        'otherPayment',
        'currentPeriodBookValue',
        's2CollateralValue',
        'carryingAmount',
        'valueAtReportingDate',
        'maxContingentLiabilitiesValue',
        'guaranteedAssetsValue',
        'netReceivables',
        'totalReinsuranceRecoverable',
        'maxCoverByReinsurer',
    ];
}
