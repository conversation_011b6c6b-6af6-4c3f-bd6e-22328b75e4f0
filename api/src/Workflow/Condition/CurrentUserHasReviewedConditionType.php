<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserHasReviewedConditionType implements ConditionType
{
    public const string type = 'currentuserhasreviewedcondition';

    public static function getName(): string
    {
        return 'User has reviewed';
    }

    public static function getHelp(): string
    {
        return 'This transition can only be performed by a user who has reviewed the entity.';
    }

    /**
     * @return array<string,string>
     */
    public static function getParameters(): array
    {
        return [];
    }

    public static function getType(): string
    {
        return self::type;
    }
}
