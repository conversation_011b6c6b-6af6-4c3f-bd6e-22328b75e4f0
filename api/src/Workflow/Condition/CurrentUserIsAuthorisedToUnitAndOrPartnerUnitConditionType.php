<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType implements ConditionType
{
    public const string type = 'currentuserisauthorisedtounitandorpartnerunitcondition';

    public static function getName(): string
    {
        return 'User is authorised to unit and/or partner unit';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed by users that have authorisation to the unit or partner unit of the entity as defined in this condition.';
    }

    public static function getParameters(): CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionTypeParameter
    {
        return new CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionTypeParameter();
    }

    public static function getType(): string
    {
        return self::type;
    }
}
