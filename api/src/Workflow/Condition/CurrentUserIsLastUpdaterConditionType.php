<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserIsLastUpdaterConditionType implements ConditionType
{
    public const string type = 'currentuserislastupdatercondition';

    public static function getName(): string
    {
        return 'User is last updater';
    }

    public static function getHelp(): string
    {
        return 'This transition can only be performed by a user who has performed the last update.';
    }

    /**
     * @return array<string,string>
     */
    public static function getParameters(): array
    {
        return [];
    }

    public static function getType(): string
    {
        return self::type;
    }
}
