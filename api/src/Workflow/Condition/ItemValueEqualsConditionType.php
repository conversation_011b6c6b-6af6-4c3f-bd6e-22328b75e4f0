<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class ItemValueEqualsConditionType implements ConditionType
{
    public const string type = 'itemvalueequalscondition';

    public static function getName(): string
    {
        return 'Has a layout item with given value';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed when a layout item value equals the value defined in this condition.';
    }

    public static function getParameters(): ItemValueEqualsConditionTypeParameter
    {
        return new ItemValueEqualsConditionTypeParameter(
            new ItemValueEqualsConditionTypeItemParameter(),
            new ItemValueEqualsConditionTypeValueParameter(),
        );
    }

    public static function getType(): string
    {
        return self::type;
    }
}
