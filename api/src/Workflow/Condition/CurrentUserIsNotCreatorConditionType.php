<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserIsNotCreatorConditionType implements ConditionType
{
    public const string type = 'currentuserisnotcreatorcondition';

    public static function getName(): string
    {
        return 'User is not creator';
    }

    public static function getHelp(): string
    {
        return 'This transition can only be performed by a user who is not the creator.';
    }

    /**
     * @return array<string,string>
     */
    public static function getParameters(): array
    {
        return [];
    }

    public static function getType(): string
    {
        return self::type;
    }
}
