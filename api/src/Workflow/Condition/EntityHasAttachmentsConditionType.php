<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class EntityHasAttachmentsConditionType implements ConditionType
{
    public const string type = 'entityhasattachmentscondition';

    public static function getName(): string
    {
        return 'Has a number of attachments';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed when the entity has at least the number of attachments defined in this condition.';
    }

    public static function getParameters(): EntityHasAttachmentsConditionTypeParameter
    {
        return new EntityHasAttachmentsConditionTypeParameter();
    }

    public static function getType(): string
    {
        return self::type;
    }
}
