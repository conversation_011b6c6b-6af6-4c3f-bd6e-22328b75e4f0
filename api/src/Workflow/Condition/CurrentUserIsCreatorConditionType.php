<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserIsCreatorConditionType implements ConditionType
{
    public const string type = 'currentuseriscreatorcondition';

    public static function getName(): string
    {
        return 'User is creator';
    }

    public static function getHelp(): string
    {
        return 'This transition can only be performed by a user who has created the record.';
    }

    /**
     * @return array<string,string>
     */
    public static function getParameters(): array
    {
        return [];
    }

    public static function getType(): string
    {
        return self::type;
    }
}
