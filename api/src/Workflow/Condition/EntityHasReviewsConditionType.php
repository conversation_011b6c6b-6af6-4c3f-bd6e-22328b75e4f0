<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class EntityHasReviewsConditionType implements ConditionType
{
    public const string type = 'entityhasreviewscondition';

    public static function getName(): string
    {
        return 'Has a number of reviews';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed when the entity has been reviewed at least the number of times defined in this condition.';
    }

    public static function getParameters(): EntityHasReviewsConditionTypeParameter
    {
        return new EntityHasReviewsConditionTypeParameter();
    }

    public static function getType(): string
    {
        return self::type;
    }
}
