<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserIsInGroupConditionType implements ConditionType
{
    public const string type = 'currentuserisingroupcondition';

    public static function getName(): string
    {
        return 'User is in group';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed by the users in any of the groups defined in this condition. It allows the selection of one or more groups.';
    }

    public static function getParameters(): CurrentUserIsInGroupConditionTypeParameter
    {
        return new CurrentUserIsInGroupConditionTypeParameter();
    }

    public static function getType(): string
    {
        return self::type;
    }
}
