<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserHasRoleConditionType implements ConditionType
{
    public const string type = 'currentuserhasrolecondition';

    public static function getName(): string
    {
        return 'User has role(s)';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed by users that have any of the roles defined in this condition. It allows the selection of one or more roles.';
    }

    public static function getParameters(): CurrentUserHasRoleConditionTypeParameter
    {
        return new CurrentUserHasRoleConditionTypeParameter();
    }

    public static function getType(): string
    {
        return self::type;
    }
}
