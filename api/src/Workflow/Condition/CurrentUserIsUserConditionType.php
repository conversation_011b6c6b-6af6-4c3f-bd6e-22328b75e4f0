<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

class CurrentUserIsUserConditionType implements ConditionType
{
    public const string type = 'currentuserisusercondition';

    public static function getName(): string
    {
        return 'User is user';
    }

    public static function getHelp(): string
    {
        return 'The transition can be performed by the users defined in this condition. It allows the selection of one or more users.';
    }

    public static function getParameters(): CurrentUserIsUserConditionTypeParameter
    {
        return new CurrentUserIsUserConditionTypeParameter();
    }

    public static function getType(): string
    {
        return self::type;
    }
}
