<?php

declare(strict_types=1);
namespace U2\Workflow\FieldConfiguration;

use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Entity\Workflow\FieldState;
use U2\Task\FieldCollector;

class FieldConfigurationItemNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    public const string ALREADY_CALLED = 'FIELD_CONFIGURATION_TASK_TYPES_ADDED';

    public function __construct(private readonly FieldCollector $fieldCollector)
    {
    }

    /**
     * @param array<mixed> $context
     *
     * @return array<string, mixed>
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $context[self::ALREADY_CALLED] = true;

        \assert($object instanceof FieldState);

        $data = $this->normalizer->normalize($object, $format, $context);

        \assert(\is_array($data));

        $fieldToTaskTypeMap = $this->fieldCollector->get();
        if (\array_key_exists($object->getField(), $fieldToTaskTypeMap)) {
            $data['taskTypes'] = $fieldToTaskTypeMap[$object->getField()];
        }

        return $data;
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof FieldState;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            FieldState::class => false,
        ];
    }
}
