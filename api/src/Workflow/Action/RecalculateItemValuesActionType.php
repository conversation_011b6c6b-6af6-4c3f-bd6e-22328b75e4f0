<?php

declare(strict_types=1);
namespace U2\Workflow\Action;

class RecalculateItemValuesActionType implements ActionType
{
    public const string type = 'recalculateitemvaluessaction';

    public static function getDescription(): string
    {
        return 'Recalculate item values';
    }

    public static function getName(): string
    {
        return 'Recalculate Item Values';
    }

    public static function getHelp(): string
    {
        return 'All items values will be recalculated after the transition.';
    }

    public static function getType(): string
    {
        return self::type;
    }

    /**
     * @return array<mixed>
     */
    public static function getParameters(): array
    {
        return [];
    }
}
