<?php

declare(strict_types=1);
namespace U2\Workflow\Action;

class SetDefaultItemValuesActionType implements ActionType
{
    public const string type = 'setdefaultitemvaluessaction';

    public static function getDescription(): string
    {
        return 'Set item with a default formula to their default values';
    }

    public static function getName(): string
    {
        return 'Set Item Defaults';
    }

    public static function getHelp(): string
    {
        return 'All items that are assigned a default formula will be calculated and set after the transition.';
    }

    public static function getType(): string
    {
        return self::type;
    }

    /**
     * @return array<mixed>
     */
    public static function getParameters(): array
    {
        return [];
    }
}
