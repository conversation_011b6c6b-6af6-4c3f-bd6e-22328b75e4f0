<?php

declare(strict_types=1);
namespace U2\Workflow\Action;

class ResetReviewsActionType implements ActionType
{
    public const string type = 'resetreviewsaction';

    public static function getDescription(): string
    {
        return 'Remove all reviews for entity after making a transition';
    }

    public static function getName(): string
    {
        return 'Reset reviews';
    }

    public static function getHelp(): string
    {
        return 'After transition all entity reviews will be removed.';
    }

    public static function getType(): string
    {
        return self::type;
    }

    /**
     * @return array<mixed>
     */
    public static function getParameters(): array
    {
        return [];
    }
}
