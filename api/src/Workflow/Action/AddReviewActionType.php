<?php

declare(strict_types=1);
namespace U2\Workflow\Action;

class AddReviewActionType implements ActionType
{
    public const string type = 'addreviewaction';

    public static function getDescription(): string
    {
        return 'Perform a review for current user on the entity after making a transition';
    }

    public static function getName(): string
    {
        return 'Add review';
    }

    public static function getHelp(): string
    {
        return 'After transition, a review for current user on the entity will be performed.';
    }

    public static function getType(): string
    {
        return self::type;
    }

    /**
     * @return array<mixed>
     */
    public static function getParameters(): array
    {
        return [];
    }
}
