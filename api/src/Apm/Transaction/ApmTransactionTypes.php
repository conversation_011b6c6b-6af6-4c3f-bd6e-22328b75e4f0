<?php

declare(strict_types=1);
namespace U2\Apm\Transaction;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class ApmTransactionTypes extends AbstractConstantChoiceBag
{
    // Types
    public const string bond = 'bond';
    public const string loan = 'loan';
    public const string promissoryNote = 'promissory-note';
    public const string equityTypeSharesParticipation = 'equity-type-shares-participation';
    public const string equityTypeSharesParticipationRealEstateShareDeal = 'equity-type-shares-participation-real-estate-share-deal';
    public const string otherAssetTransferPropertiesRealEstateAssetDeal = 'other-asset-transfer-properties-real-estate-asset-deal';
    public const string assumptionOfLiabilities = 'assumption-of-liabilities';

    public static function getReadableMap(): array
    {
        return [
            self::bond => 'Bond',
            self::loan => 'Loan',
            self::promissoryNote => 'Promissory Note',
            self::equityTypeSharesParticipation => 'Equity Type - Shares/Participation',
            self::equityTypeSharesParticipationRealEstateShareDeal => 'Equity Type - Shares/Participation (real estate share deal)',
            self::otherAssetTransferPropertiesRealEstateAssetDeal => 'Other Asset Transfer - Properties (real estate asset deal)',
            self::assumptionOfLiabilities => 'Assumption of Liabilities',
        ];
    }
}
