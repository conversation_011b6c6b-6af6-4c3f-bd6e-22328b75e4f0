<?php

declare(strict_types=1);
namespace U2\Unit\Serializer;

use S<PERSON>fony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Entity\Unit;

class UnitItemNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    public const string ALREADY_CALLED = 'UNIT_MAX_DEPTH_HANDLED';

    public function __construct(private readonly UnitExtrasProvider $unitExtrasProvider)
    {
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[self::ALREADY_CALLED] = true;

        \assert($object instanceof Unit);

        if (\array_key_exists('operation_name', $context) && Unit::collectionEndpointName !== $context['operation_name']) {
            $object->extra = $this->unitEx<PERSON>Provider->get($object);
        }

        return $this->normalizer->normalize($object, $format, $context);
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof Unit;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Unit::class => false,
        ];
    }
}
