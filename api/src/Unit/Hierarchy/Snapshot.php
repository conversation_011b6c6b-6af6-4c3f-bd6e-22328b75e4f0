<?php

declare(strict_types=1);
namespace U2\Unit\Hierarchy;

use U2\Entity\Country;
use U2\Entity\Unit;
use U2\Entity\UnitHierarchyDefinition;
use U2\Exception\Exception;

class Snapshot
{
    public const string ROOT_ID = 'root';

    private array $relationships = [];

    private array $selectedUnitIds = [];

    /**
     * Snapshot constructor.
     */
    public function __construct(array $definitions = [])
    {
        $this->extractFromDefinitions($definitions);
    }

    /**
     * @return Unit[]
     */
    public function getChildUnits(?Unit $unit = null): array
    {
        $id = $this->getUnitLookupId($unit);

        if (\array_key_exists($id, $this->relationships) && isset($this->relationships[$id]['children'])) {
            return $this->relationships[$id]['children'];
        }

        return [];
    }

    /**
     * This is meant to be used by the twig template for the unit_hierarchy_snapshot_unit_selector_widget.
     */
    public function getNestedArray(?Unit $unit = null): array
    {
        $nestedArray = array_map(
            function (Unit $childUnit): array {
                $unitData = [
                    'id' => $childUnit->getId(),
                    'label' => $childUnit->getRefIdAndName(),
                    'countryId' => null,
                    'className' => (new \ReflectionClass($childUnit))->getShortName(),
                    'children' => $this->getNestedArray($childUnit),
                ];

                if (method_exists($childUnit, 'getCountry')) {
                    $country = $childUnit->getCountry();
                    if ($country instanceof Country) {
                        $unitData['countryId'] = $country->getId();
                    }
                }

                return $unitData;
            },
            $this->getChildUnits($unit)
        );

        // sort the nested array
        usort(
            $nestedArray,
            static function (array $a, array $b): int {
                $result = strnatcmp($a['label'], $b['label']);
                if (0 !== $result) {
                    return $result;
                }

                return strnatcmp((string) $a['id'], (string) $b['id']);
            }
        );

        return $nestedArray;
    }

    public function getParentUnit(Unit $unit): ?Unit
    {
        if (!\array_key_exists($unit->getId(), $this->relationships)) {
            return null;
        }

        return $this->relationships[$unit->getId()]['parent'];
    }

    public function getSelectedUnitIds(): array
    {
        return $this->selectedUnitIds;
    }

    public function setSelectedUnitIds(array $selectedUnitIds): self
    {
        $this->selectedUnitIds = $selectedUnitIds;

        return $this;
    }

    /**
     * @return Unit[]
     */
    public function getTopLevelUnits(): array
    {
        $childrenKeyExists = \array_key_exists(self::ROOT_ID, $this->relationships)
            && isset($this->relationships[self::ROOT_ID]['children']);

        if ($childrenKeyExists) {
            return $this->relationships[self::ROOT_ID]['children'];
        }

        return [];
    }

    public function getUsedUnitIds(): array
    {
        $relationships = $this->relationships;
        unset($relationships[self::ROOT_ID]);

        return array_keys($relationships);
    }

    private function getUnitLookupId(?Unit $unit = null): int|string
    {
        if (null === $unit) {
            return self::ROOT_ID;
        }

        return $unit->getId();
    }

    /**
     * @param UnitHierarchyDefinition[] $definitions
     *
     * @throws Exception
     */
    private function extractFromDefinitions(array $definitions): void
    {
        $unitIds = array_map(
            static function ($definition): int {
                $unit = $definition->getUnit();
                if (null === $unit) {
                    throw new Exception('All units referenced in all definitions must exist. Unit not set in definition: ' . $definition->getId());
                }

                return $unit->getId();
            },
            $definitions
        );

        if ($unitIds !== array_unique($unitIds)) {
            throw new Exception('Ids of units inside definitions for a certain snapshot need to be unique.');
        }

        // build the new tree structure from the ground up, i.e.
        // start with the top level nodes and then add new levels one after another
        while (\count($definitions) > 0) {
            $skippedDefinitions = [];

            foreach ($definitions as $definition) {
                \assert($definition->getUnit() instanceof Unit);
                try {
                    $this->addUnit($definition->getUnit(), $definition->getParentUnit());
                } catch (Exception $e) {
                    $skippedDefinitions[] = $definition;
                    if ($definitions === $skippedDefinitions) {
                        throw new Exception(\sprintf('All remaining definitions (count %s) were skipped. Stopping to avoid infinite loop.', \count($definitions)), 0, $e);
                    }
                }
            }
            $definitions = $skippedDefinitions;
        }
    }

    /**
     * @throws Exception
     */
    private function addUnit(Unit $childUnit, ?Unit $parentUnit): void
    {
        if (null !== $parentUnit && !\array_key_exists($parentUnit->getId(), $this->relationships)) {
            throw new Exception(\sprintf('The unit "%s" must be added before unit "%s" can be added as it\'s child.', $parentUnit->getRefId(), $childUnit->getRefId()));
        }

        // assign parent relationships
        $id = $this->getUnitLookupId($childUnit);
        $this->relationships[$id]['parent'] = $parentUnit;

        // assign child relationships
        $id = $this->getUnitLookupId($parentUnit);
        $this->relationships[$id]['children'][] = $childUnit;
    }
}
