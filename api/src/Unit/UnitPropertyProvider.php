<?php

declare(strict_types=1);
namespace U2\Unit;

use U2\Api\Resource\UnitProperty;

class UnitPropertyProvider
{
    /**
     * @var array<string,UnitProperty>
     */
    private array $unitProperties = [];

    public function __construct(
        private readonly FieldSecurity $fieldSecurity,
    ) {
    }

    /**
     * @return array<string,UnitProperty>
     */
    public function all(): array
    {
        /*
         * Build the types only once per request.
         * This needs to be swapped with a real cache that can also be invalidated if we decide to add new once dynamically.
         */
        if (0 === \count($this->unitProperties)) {
            $unitProperties = [];
            foreach (UnitProperties::readableMap as $field => $translationKey) {
                $unitProperties[$field] = new UnitProperty(
                    $field,
                    $this->fieldSecurity->canEdit($field),
                );
            }
            $this->unitProperties = $unitProperties;
        }

        return $this->unitProperties;
    }

    public function get(string $type): UnitProperty
    {
        return $this->all()[$type];
    }

    public function has(string $type): bool
    {
        return \array_key_exists($type, $this->all());
    }
}
