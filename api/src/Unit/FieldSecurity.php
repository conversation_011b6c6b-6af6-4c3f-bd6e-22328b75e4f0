<?php

declare(strict_types=1);
namespace U2\Unit;

use Symfony\Bundle\SecurityBundle\Security;
use U2\Security\UserRoles;
use U2\SystemSettings\SystemSettings;

readonly class FieldSecurity
{
    public function __construct(
        private Security $security,
        private SystemSettings $systemSettings,
    ) {
    }

    public function canEdit(string $field): bool
    {
        if ($this->security->isGranted(UserRoles::UnitManager->value)) {
            return true;
        }

        return \in_array($field, $this->systemSettings->getSecurityUnitEditFieldWhitelist(), true);
    }
}
