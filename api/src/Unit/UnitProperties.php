<?php

declare(strict_types=1);
namespace U2\Unit;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class UnitProperties extends AbstractConstantChoiceBag
{
    public const string auditor = 'auditor';
    public const string billingAddress = 'billingAddress';
    public const string branch = 'branch';
    public const string contactUser = 'contactUser';
    public const string country = 'country';
    public const string countryFounded = 'countryFounded';
    public const string currency = 'currency';
    public const string description = 'description';
    public const string legalForm = 'legalForm';
    public const string legalName = 'legalName';
    public const string name = 'name';
    public const string parentLegalUnit = 'parentLegalUnit';
    public const string parentLegalUnitIncomeTax = 'parentLegalUnitIncomeTax';
    public const string parentLegalUnitVat = 'parentLegalUnitVat';
    public const string postalAddress = 'postalAddress';
    public const string refId = 'refId';
    public const string registerNumber = 'registerNumber';
    public const string registryPlace = 'registryPlace';
    public const string taxAdvisor = 'taxAdvisor';
    public const string taxNumber = 'taxNumber';
    public const string validFrom = 'validFrom';
    public const string validTo = 'validTo';
    public const string vatNumber = 'vatNumber';
    public const string verified = 'verified';

    /**
     * @return array<string,string>
     */
    public static function getReadableMap(): array
    {
        return self::readableMap;
    }

    public const array readableMap = [
        self::auditor => 'u2_core.auditor',
        self::billingAddress => 'u2_core.billing_address',
        self::branch => 'u2_core.branch',
        self::contactUser => 'u2_core.contact_user',
        self::country => 'u2.country',
        self::countryFounded => 'u2.country_founded',
        self::currency => 'u2_core.currency',
        self::description => 'u2.description',
        self::legalForm => 'u2_core.legal_form',
        self::legalName => 'u2_core.legal_name',
        self::name => 'u2_core.name',
        self::parentLegalUnit => 'u2_core.parent_legal_unit',
        self::parentLegalUnitIncomeTax => 'u2_core.group_parent_income_tax',
        self::parentLegalUnitVat => 'u2_core.group_parent_vat',
        self::postalAddress => 'u2_core.postal_address',
        self::refId => 'u2_core.ref_id',
        self::registerNumber => 'u2_core.register_number',
        self::registryPlace => 'u2_core.registry_place',
        self::taxAdvisor => 'u2_core.tax_advisor',
        self::taxNumber => 'u2_core.tax_number',
        self::validFrom => 'u2_core.valid_from',
        self::validTo => 'u2_core.valid_to',
        self::vatNumber => 'u2.vat_number',
        self::verified => 'u2_core.verified',
    ];
}
