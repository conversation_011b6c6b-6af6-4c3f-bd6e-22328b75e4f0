<?php

declare(strict_types=1);
namespace U2\Command\Maintenance;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use U2\Exception\Exception;

#[AsCommand(name: 'u2:maintenance:lock', description: 'Creates a lock to perform an administrative tasks')]
class LockCommand extends Command
{
    private const int EXIT_CODE_SUCCESS = 0;

    private const int EXIT_CODE_ERROR = 1;

    public function __construct(private readonly Lock $lock)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($input->isInteractive() && !$this->isConfirmed($input, $output)) {
            $output->writeln('Aborted!');

            return self::EXIT_CODE_ERROR;
        }

        try {
            $this->lock->lock();
        } catch (Exception $e) {
            $output->writeln('Unable to create a lock: ' . $e->getMessage());

            return self::EXIT_CODE_ERROR;
        }

        $output->writeln('The lock has been successfully created');

        return self::EXIT_CODE_SUCCESS;
    }

    private function isConfirmed(InputInterface $input, OutputInterface $output): bool
    {
        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion('Are you sure you want to create a lock [y/N]? ', false);

        return $helper->ask($input, $output, $question);
    }
}
