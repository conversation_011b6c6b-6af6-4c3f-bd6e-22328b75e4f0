<?php

declare(strict_types=1);
namespace U2\Command\Maintenance;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(name: 'u2:maintenance:status', description: 'Outputs whether the lock or maintenance mode are enabled')]
class MaintenanceModeStatusCommand extends Command
{
    private const int EXIT_CODE_SUCCESS = 0;

    public function __construct(
        private readonly MaintenanceMode $maintenanceMode,
        private readonly Lock $lock,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        if ($this->maintenanceMode->isEnabled()) {
            $io->error('Maintenance mode enabled.');
        } else {
            $io->success('Maintenance mode disabled.');
        }

        if ($this->lock->isLocked()) {
            $io->error('Lock enabled.');
        } else {
            $io->success('Lock disabled.');
        }

        return self::EXIT_CODE_SUCCESS;
    }
}
