<?php

declare(strict_types=1);
namespace U2\Command\Datasheets;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Attribute\Option;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Contracts\Cache\TagAwareCacheInterface;
use U2\Datasheets\Item\Formula\FormulaWithElementsProvider;
use U2\Datasheets\Item\Validation\Validator as FormulaValidator;
use U2\Form\DataTransformer\ReadableFormulaTransformer;
use U2\Repository\ItemRepository;

#[AsCommand(name: 'u2:item:validate', description: 'Validates all items available in the system')]
class ValidateItemsCommand
{
    public function __construct(
        private readonly FormulaValidator $formulaValidator,
        private readonly FormulaWithElementsProvider $formulaProvider,
        private readonly ReadableFormulaTransformer $readableFormulaTransformer,
        private readonly TagAwareCacheInterface $tenantCache,
        private readonly ItemRepository $itemRepository,
    ) {
    }

    public function __invoke(
        SymfonyStyle $io,
        #[Option(description: 'Includes valid formulas when exporting to stdout', name: 'with-valid')]
        bool $withValid,
        #[Option(description: 'Exports invalid formulas to stdout', name: 'hide-output')]
        bool $hideOutput,
        #[Option(description: 'Counts the number of validated formulas')]
        bool $count,
        OutputInterface $output): int
    {
        $this->tenantCache->invalidateTags(['formula']);

        $formulaValidationReport = $this->formulaValidator->validate($this->itemRepository->findAll());

        if ($count) {
            $itemIdToErrorArray = $formulaValidationReport->getItemIdToErrorArray();
            if (!$withValid) {
                $itemIdToErrorArray = array_filter($itemIdToErrorArray, fn ($errors): bool => \count($errors) > 0);
            }
            $io->block(\sprintf('There are %d invalid items', \count($itemIdToErrorArray)), 'error');

            return 0;
        }

        if (!$hideOutput) {
            $itemFormulaRefIdToErrorArray = $formulaValidationReport->getItemIdToErrorArray();
            $counter = 0;
            foreach ($itemFormulaRefIdToErrorArray as $itemId => $errors) {
                if (!$withValid && 0 === \count($errors)) {
                    continue;
                }
                ++$counter;
                $item = $formulaValidationReport->getItem($itemId);

                $refId = $item->getRefId();

                $formula = 'No Formula!';
                if ($this->formulaProvider->has($item)) {
                    $formulaString = $item->getFormula()?->getFormulaString();
                    \assert(null !== $formulaString);

                    $formula = $this->readableFormulaTransformer->transform($formulaString);
                }

                $io->title("$counter: $refId = $formula");
                if (0 === \count($errors)) {
                    $io->block('No error occurred.', 'info');
                } else {
                    $io->block($errors, 'error');
                }
                $io->writeln("\n");
            }
        }

        if ($formulaValidationReport->hasErrors()) {
            $io->text('There are invalid formulas.');

            return 1;
        }
        $io->text('All formulas are valid.');

        return 0;
    }
}
