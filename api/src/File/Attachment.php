<?php

declare(strict_types=1);
namespace U2\File;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Uuid;
use U2\Api\Property\AttachmentLinks;
use U2\Api\Provider\AttachmentDownloadProvider;
use U2\Api\Provider\AttachmentProvider;
use U2\Api\Provider\AttachmentsProvider;
use U2\Api\Provider\LinkFileMessageProvider;
use U2\Api\Provider\UnlinkFileMessageProvider;
use U2\Entity\CountryByCountryReportSection;
use U2\Entity\DocumentTemplateSection;
use U2\Entity\File;
use U2\Entity\FileType;
use U2\Entity\GroupPermission;
use U2\Entity\Interfaces\FileAttachable;
use U2\Entity\LocalFileSection;
use U2\Entity\MasterFileSection;
use U2\Entity\Task\Task;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\UserPermission;
use U2\Messenger\File\LinkFileMessage;
use U2\Messenger\File\UnlinkFileMessage;
use U2\Security\Voter\VoterAttributes;

#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/{linkedResourceName}/{resourceId}/attachments/{id}',
            uriVariables: [
                'id' => new Link(fromClass: self::class, identifiers: ['id'], compositeIdentifier: false),
                'resourceId' => new Link(fromClass: self::class, identifiers: ['resourceId'], compositeIdentifier: false),
                'linkedResourceName' => new Link(fromClass: self::class, identifiers: ['linkedResourceName'], compositeIdentifier: false),
            ],
            security: 'is_granted("' . VoterAttributes::read . '", object)',
            provider: AttachmentProvider::class
        ),
        new Get(
            uriTemplate: '/{linkedResourceName}/{resourceId}/attachments/{id}/download',
            formats: ['binary' => 'application/octet-stream'],
            uriVariables: [
                'id' => new Link(fromClass: self::class, identifiers: ['id'], compositeIdentifier: false),
                'resourceId' => new Link(fromClass: self::class, identifiers: ['resourceId'], compositeIdentifier: false),
                'linkedResourceName' => new Link(fromClass: self::class, identifiers: ['linkedResourceName'], compositeIdentifier: false),
            ],
            openapi: new Operation(
                responses: [
                    Response::HTTP_OK => [
                        'description' => 'Downloads the attachment',
                        'content' => [
                            'application/octet-stream' => ['schema' => ['type' => 'string', 'format' => 'binary']],
                        ],
                    ],
                ],
                description: 'Download an attachment from a resource',
            ),
            provider: AttachmentDownloadProvider::class
        ),
        new GetCollection(
            uriTemplate: '/{linkedResourceName}/{resourceId}/attachments',
            uriVariables: [
                'resourceId' => new Link(fromClass: self::class, identifiers: ['resourceId'], compositeIdentifier: false),
                'linkedResourceName' => new Link(fromClass: self::class, identifiers: ['linkedResourceName'], compositeIdentifier: false),
            ],
            openapi: new Operation(description: 'Retrieves a collection of attachments for a given resource'),
            paginationEnabled: false,
            security: 'is_granted("' . VoterAttributes::read . '", request.getRequestUri())',
            provider: AttachmentsProvider::class
        ),
        new Post(
            uriTemplate: '/{linkedResourceName}/{resourceId}/attachments',
            uriVariables: [
                'resourceId' => new Link(fromClass: self::class, identifiers: ['resourceId'], compositeIdentifier: false),
                'linkedResourceName' => new Link(fromClass: self::class, identifiers: ['linkedResourceName'], compositeIdentifier: false),
            ],
            status: Response::HTTP_NO_CONTENT,
            openapi: new Operation(description: 'Links a file to a resource'),
            denormalizationContext: ['groups' => ['file:link']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::addAttachment . '", object.linkedResource) && is_granted("' . VoterAttributes::read . '", object.file)',
            input: LinkFileMessage::class,
            output: false,
            messenger: 'input',
            provider: LinkFileMessageProvider::class
        ),
        new Delete(
            uriTemplate: '/{linkedResourceName}/{resourceId}/attachments/{id}',
            uriVariables: [
                'id' => new Link(fromClass: self::class, identifiers: ['id'], compositeIdentifier: false),
                'resourceId' => new Link(fromClass: self::class, identifiers: ['resourceId'], compositeIdentifier: false),
                'linkedResourceName' => new Link(fromClass: self::class, identifiers: ['linkedResourceName'], compositeIdentifier: false),
            ],
            status: Response::HTTP_NO_CONTENT,
            openapi: new Operation(description: 'Detaches a file from a resource'),
            denormalizationContext: ['groups' => ['file:unlink']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::removeAttachment . '", object.linkedResource)',
            input: UnlinkFileMessage::class,
            output: false,
            messenger: 'input',
            provider: UnlinkFileMessageProvider::class
        ),
    ],
    normalizationContext: ['groups' => ['attachment:read']],
)]
class Attachment
{
    public const array linkedResourceNames = [
        Task::class => 'tasks',
        Unit::class => 'units',
        DocumentTemplateSection::class => 'document-template-sections',
        LocalFileSection::class => 'local-file-sections',
        MasterFileSection::class => 'master-file-sections',
        CountryByCountryReportSection::class => 'country-by-country-report-sections',
    ];

    #[ApiProperty(identifier: true)]
    public readonly int|string $id;

    #[ApiProperty(identifier: true)]
    public readonly string $resourceId;

    #[ApiProperty(identifier: true)]
    public readonly string $linkedResourceName;

    #[Groups(groups: ['attachment:read'])]
    public readonly User $createdBy;

    #[Groups(groups: ['attachment:read'])]
    public readonly \DateTime $createdAt;

    #[Groups(groups: ['attachment:read'])]
    public readonly User $updatedBy;

    #[Groups(groups: ['attachment:read'])]
    public readonly \DateTime $updatedAt;

    /**
     * @var array<int, UserPermission>
     */
    #[Groups(groups: ['attachment:read'])]
    public readonly array $userPermissions;

    /**
     * @var array<int, GroupPermission>
     */
    #[Groups(groups: ['attachment:read'])]
    public readonly array $groupPermissions;

    #[Groups(groups: ['attachment:read'])]
    public readonly string $accessType;

    /**
     * @var array<int,  FileType>
     */
    #[Groups(groups: ['attachment:read'])]
    public readonly array $types;

    #[Groups(groups: ['attachment:read'])]
    public ?string $description = null;

    public function __construct(
        #[Groups(groups: ['attachment:read'])]
        public readonly File $file,

        #[Groups(groups: ['attachment:read'])]
        private readonly FileAttachable $entity,

        #[Groups(groups: ['attachment:read'])]
        public ?string $name,

        #[Groups(groups: ['attachment:read'])]
        public AttachmentLinks $links,
    ) {
        /** @var int $id */
        $id = $file->getId();
        $this->id = $id;

        $id = $entity->getId();
        $this->resourceId = $id instanceof Uuid ? $id->toRfc4122() : (string) $id;

        $linkedResourceName = current(
            array_filter(self::linkedResourceNames, static fn ($class): bool => is_a($entity, $class), \ARRAY_FILTER_USE_KEY)
        );

        \assert(false !== $linkedResourceName);
        $this->linkedResourceName = $linkedResourceName;

        /** @var User $createdBy */
        $createdBy = $file->getCreatedBy();
        $this->createdBy = $createdBy;
        $this->createdAt = $file->getCreatedAt();

        $updatedBy = $file->getUpdatedBy();
        $this->updatedBy = $updatedBy;
        $this->updatedAt = $file->getUpdatedAt();

        $this->userPermissions = $file->getPermissions()->getUserPermissions()->getValues();
        $this->groupPermissions = $file->getPermissions()->getGroupPermissions()->getValues();
        $this->types = $file->getTypes()->getValues();

        $this->accessType = $file->getAccessType();
    }

    public function getFile(): File
    {
        return $this->file;
    }

    /*
     * The explicit return types allows api platform to detect that here is an api resource is returned.
     * With that information it is able to normalize the record to just an IRI instead of treating it as a unknown object
     * which would be normalized to an array.
     */
    public function getEntity(): Task|Unit|DocumentTemplateSection|LocalFileSection|MasterFileSection|CountryByCountryReportSection
    {
        \assert($this->entity instanceof Task
            || $this->entity instanceof Unit
            || $this->entity instanceof DocumentTemplateSection
            || $this->entity instanceof LocalFileSection
            || $this->entity instanceof MasterFileSection
            || $this->entity instanceof CountryByCountryReportSection);

        return $this->entity;
    }
}
