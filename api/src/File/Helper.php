<?php

declare(strict_types=1);
namespace U2\File;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use U2\Entity\File;
use U2\FileSystem\TenantFilesystemOperator;
use U2\Http\DownloadFilenameSanitizer;

class Helper
{
    public const string attachmentsSubdirectory = 'attachments';

    public function __construct(
        private readonly TenantFilesystemOperator $tenantFilesystem,
        private readonly DownloadFilenameSanitizer $downloadFilenameSanitizer,
    ) {
    }

    /**
     * @throws NotFoundHttpException
     */
    public function createDownloadResponse(File $file): Response
    {
        $filePath = self::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . $file->getPath();
        if (!$this->tenantFilesystem->fileExists($filePath)) {
            throw new NotFoundHttpException("The file “{$file->getName()}” was not found.");
        }

        $content = $this->tenantFilesystem->read($filePath);
        $response = new Response(
            $content,
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/octet-stream',
            ]
        );
        $response->headers->set(
            'Content-Disposition',
            $response->headers->makeDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $this->downloadFilenameSanitizer->sanitize($file->getName()),
                $this->downloadFilenameSanitizer->sanitizeFallback($file->getName())
            )
        );

        return $response;
    }

    /**
     * @throws NotFoundHttpException
     */
    public function createViewResponse(File $file): Response
    {
        $filePath = self::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . $file->getPath();
        if (!$this->tenantFilesystem->fileExists($filePath)) {
            throw new NotFoundHttpException("The file “{$file->getName()}” was not found.");
        }

        $content = $this->tenantFilesystem->read($filePath);

        return new Response(
            $content,
            Response::HTTP_OK,
            [
                'Content-Type' => $this->tenantFilesystem->mimeType($filePath),
            ]
        );
    }

    public function getFileSizeInBytes(File $file): ?int
    {
        $filePath = self::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . $file->getPath();
        if (!$this->tenantFilesystem->fileExists($filePath)) {
            return null;
        }

        return $this->tenantFilesystem->fileSize($filePath);
    }

    public function getFileMimeType(File $file): ?string
    {
        $filePath = self::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . $file->getPath();
        if (!$this->tenantFilesystem->fileExists($filePath)) {
            return null;
        }

        return $this->tenantFilesystem->mimeType($filePath);
    }
}
