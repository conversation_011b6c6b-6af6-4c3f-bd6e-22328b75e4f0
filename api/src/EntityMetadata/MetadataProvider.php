<?php

declare(strict_types=1);
namespace U2\EntityMetadata;

use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use U2\Entity\Interfaces\Entity;
use U2\Exception\MetadataException;

class MetadataProvider
{
    private const string cacheKey = 'u2-metadata-collection';

    public function __construct(
        private readonly MetadataExtractor $metadataExtractor,
        private readonly CacheInterface $staticCache,
    ) {
    }

    /**
     * @template T of Entity
     *
     * @param class-string<T> $class
     *
     * @throws \Exception
     *
     * @return Metadata<T>
     */
    public function getForClass(string $class): Metadata
    {
        /** @var Metadata<T>[] $foundMetadata */
        $foundMetadata = array_filter(
            $this->all(),
            static fn (Metadata $metadata): bool => $class === $metadata->getClass()
        );

        $metadataCount = \count($foundMetadata);
        if ($metadataCount < 1) {
            throw new \Exception("No metadata available for $class.");
        }

        if ($metadataCount > 1) {
            throw new \Exception("More than one metadata available for $class. Unable to determine which to choose.");
        }

        return current($foundMetadata);
    }

    /**
     * @throws \Exception
     *
     * @return Metadata<Entity>
     */
    public function get(string $shortName): Metadata
    {
        foreach ($this->all() as $metadata) {
            if ($shortName === $metadata->getShortName()) {
                return $metadata;
            }
        }

        throw new MetadataException("The shortname $shortName does not exist");
    }

    /**
     * @param class-string<Entity> $className
     */
    public function getShortName(string $className): string
    {
        return $this->getForClass($className)->getShortName();
    }

    /**
     * @return class-string<Entity>
     */
    public function getClass(string $shortName): string
    {
        return $this->get($shortName)->getClass();
    }

    /**
     * @return array<int, Metadata<Entity>>
     */
    public function all(): array
    {
        return $this->staticCache->get(self::cacheKey, fn (ItemInterface $item): array => $this->metadataExtractor->extractMetadata());
    }

    public function warmup(): void
    {
        $this->staticCache->delete(self::cacheKey);
        $this->all();
    }
}
