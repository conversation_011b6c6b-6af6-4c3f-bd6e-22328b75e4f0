<?php

declare(strict_types=1);
namespace U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

use function Symfony\Component\Translation\t;

use U2\Entity\Dashboard;
use U2\Event\Menu\ConfigureRootExtraMenuEvent;
use U2\Event\Menu\ConfigureRootMenuEvent;
use U2\Event\Menu\ConfigureToolsMenuEvent;
use U2\Repository\DashboardRepository;
use U2\Security\UserRoles;

class MainMenuBuilder
{
    public function __construct(
        private readonly FactoryInterface $factory,
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly DashboardRepository $dashboardRepository,
        private readonly SeparatorBuilder $separatorBuilder,
        private readonly Security $security,
        private readonly RouterInterface $router,
    ) {
    }

    public function create(): ItemInterface
    {
        $menu = $this->factory->createItem('main-menu');
        if (!$this->security->isGranted('IS_AUTHENTICATED_REMEMBERED')) {
            return $menu;
        }
        $menu->addChild($this->createDashboardMenu());
        $this->eventDispatcher->dispatch(new ConfigureRootMenuEvent($this->factory, $menu));
        $menu->addChild($this->createToolsMenu());
        $menu->addChild($this->createHelpMenu());
        $this->eventDispatcher->dispatch(new ConfigureRootExtraMenuEvent($this->factory, $menu));

        return $menu;
    }

    private function createDashboardMenu(): ItemInterface
    {
        /** @var Dashboard[] $dashboards */
        $dashboards = $this->dashboardRepository->findCurrentUserAssigned();
        if (1 === \count($dashboards)) {
            $dashboard = reset($dashboards);

            return $this->factory->createItem('Dashboard', [
                'label' => t('u2.dashboard'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'AppDashboard', 'params' => ['slug' => $dashboard->getSlug(), 'id' => $dashboard->getId()]]],
            ]);
        }

        $menu = $this->factory->createItem('Dashboard', [
            'label' => t('u2.dashboard'),
        ]);
        foreach ($dashboards as $dashboard) {
            $menu->addChild($dashboard->getSlug(), [
                'label' => $dashboard->getTitle(),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'AppDashboard', 'params' => ['slug' => $dashboard->getSlug(), 'id' => $dashboard->getId()]]],
            ]);
        }

        return $menu;
    }

    private function createHelpMenu(): ItemInterface
    {
        $menu = $this->factory->createItem('Help', [
            'label' => t('u2_core.help'),
        ]);

        $menu->addChild('Support',
            [
                'label' => t('u2_core.support'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'SupportInfo']],
            ]);

        $menu
            ->addChild('About', [
                'label' => t('u2_core.about'),
                'uri' => '#',
                'extras' => [
                    'dialog' => 'about',
                ],
            ])
            ->setLinkAttributes([
                '@click.prevent' => 'showAboutDialog',
            ]);

        if ($this->security->isGranted(UserRoles::Api->value)) {
            $menu
                ->addChild('ApiDocs', [
                    'label' => t('u2_core.api_docs'),
                    'uri' => '#',
                    'extras' => [
                        'vueRoute' => $this->router->generate('api_doc', [], UrlGeneratorInterface::ABSOLUTE_URL),
                    ],
                ]);
        }

        return $menu;
    }

    private function createToolsMenu(): ItemInterface
    {
        $menu = $this->factory->createItem('Tools', [
            'label' => t('u2_core.tools'),
        ]);

        $menu->addChild('Units', [
            'label' => t('u2.unit.plural'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'UnitList']],
        ]);

        if ($this->security->isGranted(UserRoles::UnitManager->value)) {
            $menu->addChild('Unit Hierarchies', [
                'label' => t('u2.unit_hierarchies'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'HierarchyList']],
            ]);
        }

        if ($this->security->isGranted(UserRoles::UserGroupAdmin->value)) {
            $menu->addChild($this->separatorBuilder->create());

            $menu->addChild('Users', [
                'label' => t('u2_core.users'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'UserList']],
            ]);

            $menu->addChild('User Groups', [
                'label' => t('u2_core.user_groups'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'UserGroupList']],
            ]);

            $menu->addChild('Authorisation', [
                'label' => t('u2_core.authorisation.authorisation'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'AuthorizationList']],
            ]);
        }

        $menu->addChild($this->separatorBuilder->create());

        $menu->addChild('Files', [
            'label' => t('u2_core.files'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'FileList']],
        ]);

        $this->eventDispatcher->dispatch(new ConfigureToolsMenuEvent($this->factory, $menu));

        $menu->addChild('Periods', [
            'label' => t('u2.periods'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'PeriodList']],
        ]);

        $menu->addChild('Imports', [
            'label' => t('u2.import.imports'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'ImportList']],
        ]);

        $menu->addChild('Filters', [
            'label' => t('u2.saved_filters'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'SavedFilterList']],
        ]);

        if ($this->security->isGranted(UserRoles::Admin->value)) {
            $menu->addChild('Subscriptions', [
                'label' => t('u2_core.subscriptions'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'SavedFilterSubscriptionList']],
            ]);

            $menu->addChild($this->separatorBuilder->create());

            $menu->addChild('Administration', [
                'label' => t('u2_core.administration'),
                'uri' => '#',
                'extras' => ['vueRoute' => ['name' => 'ConfigurationData']],
            ]);
        }

        return $menu;
    }
}
