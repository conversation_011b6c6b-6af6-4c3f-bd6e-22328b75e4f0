<?php

declare(strict_types=1);
namespace U2\Menu;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

use function Symfony\Component\Translation\t;

use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\Transaction;
use U2\Event\Menu\ConfigureRootMenuEvent;
use U2\Module\Module;
use U2\Module\ModuleStatusChecker;
use U2\Security\Authorization\AuthorizationRight;
use U2\TransferPricing\CountryByCountryReport\CountryByCountryReportTableType;
use U2\TransferPricing\FinancialData\FinancialDataTableType;
use U2\TransferPricing\LocalFile\LocalFileTableType;
use U2\TransferPricing\MainBusinessActivity\MainBusinessActivityTableType;
use U2\TransferPricing\MasterFile\MasterFileTableType;
use U2\TransferPricing\Transaction\TransactionTableType;

class TpmMenuSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly TaskTypeMenuBuilder $genericMenuLayoutBuilder,
        private readonly ModuleStatusChecker $enabledStatusChecker,
        private readonly SeparatorBuilder $separatorBuilder,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConfigureRootMenuEvent::class => ['onMenuConfigureRoot', 10],
        ];
    }

    public function onMenuConfigureRoot(ConfigureRootMenuEvent $event): void
    {
        if (!$this->enabledStatusChecker->isEnabled(Module::tpm)) {
            return;
        }

        $factory = $event->getFactory();

        $menu = $factory->createItem('TPM', [
            'label' => t('u2_tpm.module_name.short'),
        ]);

        if ($this->authorizationChecker->isGranted(AuthorizationItem::MasterFile->value . ':' . AuthorizationRight::READ->value)) {
            $masterFileMenu = $this->genericMenuLayoutBuilder->init($factory, MasterFile::class)
                ->setMenuTitle('u2_tpm.master_file.plural')
                ->setTableViewConfigurationFullyQualifiedClass(MasterFileTableType::class)
                ->build();

            $menu->addChild($masterFileMenu);
        }

        if ($this->authorizationChecker->isGranted(AuthorizationItem::LocalFile->value . ':' . AuthorizationRight::READ->value)) {
            $localFileMenu = $this->genericMenuLayoutBuilder->init($factory, LocalFile::class)
                ->setMenuTitle('u2_tpm.local_file.plural')
                ->setTableViewConfigurationFullyQualifiedClass(LocalFileTableType::class)
                ->build();

            $menu->addChild($localFileMenu);
        }

        if ($this->authorizationChecker->isGranted(AuthorizationItem::Transaction->value . ':' . AuthorizationRight::READ->value)) {
            $transactionsMenu = $this->genericMenuLayoutBuilder->init($factory, Transaction::class)
                ->setMenuTitle('u2_tpm.transaction.plural')
                ->setTableViewConfigurationFullyQualifiedClass(TransactionTableType::class)
                ->build();

            $menu->addChild($transactionsMenu);
        }

        if ($this->hasRightToAnyOfAuthorizationItems(AuthorizationRight::READ->value, [AuthorizationItem::MainBusinessActivity->value, AuthorizationItem::FinancialData->value, AuthorizationItem::CountryByCountryReport->value, AuthorizationItem::MasterFile->value, AuthorizationItem::LocalFile->value, AuthorizationItem::Transaction->value])) {
            $menu->addChild($this->separatorBuilder->create());
        }

        if ($this->authorizationChecker->isGranted(AuthorizationItem::CountryByCountryReport->value . ':' . AuthorizationRight::READ->value)) {
            $countryByCountryReportMenu = $this->genericMenuLayoutBuilder->init($factory, CountryByCountryReport::class)
                ->setMenuTitle('u2_tpm.country_by_country_report.plural')
                ->setTableViewConfigurationFullyQualifiedClass(CountryByCountryReportTableType::class)
                ->build();

            $menu->addChild($countryByCountryReportMenu);
        }

        if ($this->authorizationChecker->isGranted(AuthorizationItem::FinancialData->value . ':' . AuthorizationRight::READ->value)) {
            $financialDataMenu = $this->genericMenuLayoutBuilder->init($factory, FinancialData::class)
                ->setMenuTitle('u2_tpm.financial_data.plural')
                ->setTableViewConfigurationFullyQualifiedClass(FinancialDataTableType::class)
                ->build();

            $menu->addChild($financialDataMenu);
        }

        if ($this->authorizationChecker->isGranted(AuthorizationItem::MainBusinessActivity->value . ':' . AuthorizationRight::READ->value)) {
            $mainBusinessActivityMenu = $this->genericMenuLayoutBuilder->init($factory, MainBusinessActivity::class)
                ->setMenuTitle('u2_tpm.main_business_activity.plural')
                ->setTableViewConfigurationFullyQualifiedClass(MainBusinessActivityTableType::class)
                ->build();

            $menu->addChild($mainBusinessActivityMenu);
        }

        if (\count($menu->getChildren()) > 0) {
            $parentMenu = $event->getMenu();
            $parentMenu->addChild($menu);
        }
    }

    /**
     * @param array<int, string> $authorizationItems
     */
    private function hasRightToAnyOfAuthorizationItems(string $right, array $authorizationItems): bool
    {
        foreach ($authorizationItems as $authorizationItem) {
            if ($this->authorizationChecker->isGranted($authorizationItem . ':' . $right)) {
                return true;
            }
        }

        return false;
    }
}
