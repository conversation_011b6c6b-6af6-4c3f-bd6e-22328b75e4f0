<?php

declare(strict_types=1);
namespace U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use Symfony\Component\Routing\RouterInterface;

use function Symfony\Component\Translation\t;

use U2\Entity\SavedFilter;
use U2\Entity\Task\TaskType;
use U2\EntityMetadata\Entity\ReadableNameTranslator;
use U2\Table\SavedFilter\RoutingHelper;
use U2\Task\TaskTypeKnowledge;
use U2\User\CurrentUserProvider;

class TaskTypeMenuBuilder
{
    /**
     * @var class-string<TaskType>
     */
    private string $taskTypeClass;

    private FactoryInterface $menuFactory;

    private ?string $menuTitle = null;

    private ?string $tableViewConfigurationFullyQualifiedClass = null;

    private bool|string $translationDomain = 'messages';

    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly RoutingHelper $routingHelper,
        private readonly ReadableNameTranslator $nameTranslator,
        private readonly RouterInterface $router,
        private readonly SeparatorBuilder $separatorBuilder,
    ) {
    }

    /**
     * @param class-string<TaskType> $taskTypeClass
     *
     * @return $this
     */
    public function init(FactoryInterface $menuFactory, string $taskTypeClass): self
    {
        $this->taskTypeClass = $taskTypeClass;
        $this->menuFactory = $menuFactory;

        $this
            ->setMenuTitle(null)
            ->setTableViewConfigurationFullyQualifiedClass(null)
            ->setTranslationDomain('messages');

        return $this;
    }

    public function build(): ItemInterface
    {
        $routes = TaskTypeKnowledge::getAvailableRoutes($this->taskTypeClass);

        $menuLayout = $this->menuFactory->createItem($this->nameTranslator->translateClass($this->taskTypeClass), [
            'uri' => '#',
            'extras' => ['vueRoute' => $this->router->generate($routes['list'])],
        ]);

        if (null !== $this->menuTitle) {
            $menuLayout->setLabel($this->menuTitle);
        }

        $menuLayout->setExtra('translation_domain', $this->translationDomain);

        $filters = [];
        if (null !== $this->tableViewConfigurationFullyQualifiedClass) {
            $filters = array_filter(
                $this->currentUserProvider->get()->getFavouriteSavedFilters(),
                fn (SavedFilter $favourite): bool => $favourite->getTableViewConfigurationFullyQualifiedClass() === $this->tableViewConfigurationFullyQualifiedClass
            );
        }

        if (\count($filters) > 0) {
            $menuLayout->addChild('List', [
                'label' => t('u2_core.view'),
                'uri' => '#',
                'extras' => ['vueRoute' => $this->router->generate($routes['list'])],
            ]);

            $menuLayout->addChild($this->separatorBuilder->create(t('u2_core.filters')));

            foreach ($filters as $filter) {
                $menuLayout->addChild($filter->getName(), [
                    'uri' => '#',
                    'extras' => [
                        'safe_label' => true,
                        'vueRoute' => $this->routingHelper->generateUrlToSavedFilterTable($filter),
                    ],
                ]);
            }
        }

        return $menuLayout;
    }

    public function setMenuTitle(?string $menuTitle): self
    {
        $this->menuTitle = $menuTitle;

        return $this;
    }

    public function setTableViewConfigurationFullyQualifiedClass(?string $tableViewConfigurationFullyQualifiedClass): self
    {
        $this->tableViewConfigurationFullyQualifiedClass = $tableViewConfigurationFullyQualifiedClass;

        return $this;
    }

    public function setTranslationDomain(bool|string $translationDomain): self
    {
        $this->translationDomain = $translationDomain;

        return $this;
    }
}
