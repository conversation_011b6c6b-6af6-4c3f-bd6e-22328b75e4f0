<?php

declare(strict_types=1);
namespace U2\Validator;

use Symfony\Component\PropertyAccess\Exception\NoSuchPropertyException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\ValidDateRange;
use U2\Exception\Exception;
use U2\Util\DateTime;

class PeriodEndDateInValidDateRangeOfFieldValidator extends ConstraintValidator
{
    /**
     * @var string
     */
    private const string dateFormat = 'd.m.Y';

    public function __construct(private readonly PropertyAccessorInterface $propertyAccessor)
    {
    }

    /**
     * @param object $subject
     *
     * @throws \Exception
     * @throws NoSuchPropertyException
     */
    public function validate($subject, Constraint $constraint): void
    {
        if (!$constraint instanceof PeriodEndDateInValidDateRangeOfField) {
            throw new Exception(\sprintf('This validator supports only "%s" constraints', PeriodEndDateInValidDateRangeOfField::class));
        }

        if (false === property_exists($subject, $constraint->field)) {
            return;
        }

        $fieldValue = $this->propertyAccessor->getValue($subject, $constraint->field);
        if (!$fieldValue instanceof ValidDateRange) {
            return;
        }

        if ($subject instanceof Periodable) {
            $period = $subject->getPeriod();
            if (null === $period) {
                return;
            }

            if (DateTime::isInDateRange($fieldValue, $period->getEndDate())) {
                return;
            }

            $this->context
                ->buildViolation('u2.value_is_not_valid_for_the_end_date_of_the_selected_period')
                ->setParameters(['%date%' => $period->getEndDate()->format(self::dateFormat)])
                ->atPath($constraint->field)
                ->addViolation();

            return;
        }

        if (DateTime::isInDateRange($fieldValue, new \DateTime())) {
            return;
        }

        $this->context
            ->buildViolation('u2.value_is_not_currently_in_its_valid_date_range')
            ->atPath($constraint->field)
            ->addViolation();
    }
}
