<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Task\Task;
use U2\Entity\Task\TaskType;
use U2\Entity\Workflow\Status;

/**
 * @extends ServiceEntityRepository<Status>
 */
class StatusRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Status::class);
    }

    /**
     * Retrieves a list of pairs (status => Status, uses => # of uses in the DB) for all entities of the
     * given class.
     */
    public function getStatusesInUseForWorkflowableClassName(string $className): array
    {
        \assert(class_exists($className));
        \assert(is_a($className, TaskType::class, true));
        $taskClass = Task::class;
        $taskType = $className::getWorkflowBindingId();

        return $this
            ->getEntityManager()
            ->createQuery(
                <<<DQL
                    SELECT statusEntity status
                    FROM U2\Entity\Workflow\Status AS statusEntity
                    JOIN $taskClass as task WITH task.type = '$taskType' and task.status = statusEntity
                    DQL
            )
            ->getResult();
    }
}
