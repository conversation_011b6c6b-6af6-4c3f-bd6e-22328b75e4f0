<?php

declare(strict_types=1);
namespace U2\EventListener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use U2\Command\Maintenance\MaintenanceMode;

class MaintenanceSubscriber implements EventSubscriberInterface
{
    private const int priority = 100000;

    public function __construct(
        private readonly string $projectDir,
        private readonly MaintenanceMode $maintenanceMode,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                'enableMaintenanceOnRequest',
                self::priority,
            ],
        ];
    }

    public function enableMaintenanceOnRequest(RequestEvent $e): void
    {
        if (!$this->maintenanceMode->isEnabled()) {
            return;
        }

        $e->setResponse(
            new Response(
                file_get_contents($this->projectDir . '/public/maintenance.htm'),
                Response::HTTP_SERVICE_UNAVAILABLE
            )
        );
    }
}
