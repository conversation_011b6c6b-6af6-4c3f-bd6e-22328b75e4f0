<?php

declare(strict_types=1);
namespace U2\EventListener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Event\BulkAction\PostBulkEditEvent;
use U2\Money\LinkedBaseLocalGroupMoneyContainingEntityUpdater;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;

class LinkedBaseLocalGroupMoneyPropertiesBulkEditSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly LinkedBaseLocalGroupMoneyContainingEntityUpdater $baseLocalGroupMoneyUpdater)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PostBulkEditEvent::class => ['updateLinkedBaseLocalGroupMoneyEntities', 10],
        ];
    }

    public function updateLinkedBaseLocalGroupMoneyEntities(PostBulkEditEvent $event): void
    {
        $entities = $event->getEntities();
        if (!(reset($entities) instanceof LinkedBaseLocalGroupMoneyInterface)) {
            return;
        }

        foreach ($entities as $entity) {
            if ($entity instanceof LinkedBaseLocalGroupMoneyInterface) {
                $this->baseLocalGroupMoneyUpdater->update($entity);
            }
        }
    }
}
