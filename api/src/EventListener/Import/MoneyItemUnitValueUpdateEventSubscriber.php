<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Form\Exception\TransformationFailedException;
use U2\Entity\Currency;
use U2\Entity\MoneyItemUnitValue;
use U2\Event\Import\PreBindDataImportEvent;
use U2\Exception\ImportInterpreterException;
use U2\Exception\ImportInvalidValueException;
use U2\Form\DataTransformer\MoneyStringToLocalizedStringTransformer;

class MoneyItemUnitValueUpdateEventSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            PreBindDataImportEvent::class => 'updateMoneyItemUnitValue',
        ];
    }

    /**
     * @throws ImportInterpreterException
     */
    public function updateMoneyItemUnitValue(PreBindDataImportEvent $event): void
    {
        $itemUnitValue = $event->getEntity();
        if (!($itemUnitValue instanceof MoneyItemUnitValue)) {
            return;
        }

        $interpretedData = $event->getInterpretedData();
        if (!\array_key_exists('value', $interpretedData)) {
            throw new ImportInterpreterException('Unable to import an item unit value without a value.');
        }

        $currency = $itemUnitValue->getUnit()->getCurrency();
        \assert($currency instanceof Currency);

        $scale = $currency->getScale();
        $dataTransformer = new MoneyStringToLocalizedStringTransformer($scale, true, null, 1);

        /** @var string|null $value */
        $value = $interpretedData['value'];
        try {
            $interpretedData['localCurrencyValue'] = (string) $dataTransformer->reverseTransform($value);
        } catch (TransformationFailedException $e) {
            throw new ImportInvalidValueException($value);
        }

        unset($interpretedData['value']);

        $event->setInterpretedData($interpretedData);
    }
}
