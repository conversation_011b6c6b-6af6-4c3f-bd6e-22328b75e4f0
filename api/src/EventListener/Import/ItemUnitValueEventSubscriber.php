<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Datasheets\Item\UnitValue\FormulaRecalculator;
use U2\Entity\ItemUnitValue;
use U2\Event\Import\PostInterpretImportEvent;
use U2\Exception\ExchangeRateNotFoundException;
use U2\Exception\MissingPreviousPeriodException;

readonly class ItemUnitValueEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private FormulaRecalculator $formulaRecalculator,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PostInterpretImportEvent::class => 'calculateUnitPeriods',
        ];
    }

    public function calculateUnitPeriods(PostInterpretImportEvent $postInterpretImportEvent): void
    {
        $entities = $postInterpretImportEvent->getLog()->getEntityLookupCache()->all();
        if (\count($entities) < 1) {
            return;
        }
        $firstEntity = reset($entities);

        if (!($firstEntity instanceof ItemUnitValue)) {
            return;
        }

        $period = $firstEntity->getPeriod();
        $units = [];

        /** @var ItemUnitValue $entity */
        foreach ($entities as $entity) {
            if ($entity->getPeriod() !== $period) {
                $postInterpretImportEvent->getLog()->addGeneralError('It is not possible to import values for more than one period at a time.');

                return;
            }
            $units[] = $entity->getUnit();
        }

        try {
            foreach (array_unique($units) as $unit) {
                $this->formulaRecalculator->recalculateUnitInPeriod($unit, $period);
            }
        } catch (ExchangeRateNotFoundException $e) {
            $message = \sprintf(
                'Exchange rate from %s to %s (%d) in period "%s" not found.',
                $e->getSource()->getIso4217code(),
                $e->getDestination()->getIso4217code(),
                $e->getType(),
                $e->getPeriod()->getName()
            );
            $postInterpretImportEvent->getLog()->addGeneralError($message);
            $postInterpretImportEvent->stopPropagation();
        } catch (MissingPreviousPeriodException $e) {
            $message = \sprintf(
                'Previous period for period "%s" not found.',
                $e->getPeriod()->getName()
            );
            $postInterpretImportEvent->getLog()->addGeneralError($message);
            $postInterpretImportEvent->stopPropagation();
        }
    }
}
