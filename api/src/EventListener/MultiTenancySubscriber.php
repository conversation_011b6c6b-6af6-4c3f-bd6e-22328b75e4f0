<?php

declare(strict_types=1);
namespace U2\EventListener;

use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\Console\Event\ConsoleCommandEvent;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use U2\Exception\TenantException;
use U2\MultiTenancy\ApplicationConfigurator;
use U2\MultiTenancy\Tenant;
use U2\MultiTenancy\TenantRepositoryInterface;

class MultiTenancySubscriber implements EventSubscriberInterface
{
    private const int priority = 200000;

    public function __construct(
        private readonly TenantRepositoryInterface $tenantRepository,
        private readonly ApplicationConfigurator $applicationConfigurator,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                'configureApplicationOnRequest',
                self::priority,
            ],
            ConsoleEvents::COMMAND => [
                'configureApplicationOnCommand',
                self::priority,
            ],
        ];
    }

    public function configureApplicationOnRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        try {
            $this->applicationConfigurator->configure(
                $this->tenantRepository->getByHostname($event->getRequest()->getHost())
            );
        } catch (TenantException $e) {
            if (!$event->getRequest()->hasSession()) {
                $event->getRequest()->setSession(new Session());
            }
            $event->setResponse(new Response('Tenant does not exists.', Response::HTTP_MISDIRECTED_REQUEST));
        }
    }

    public function configureApplicationOnCommand(ConsoleCommandEvent $event): void
    {
        $tenant = $this->resolveTenantFromCliOption($event->getInput());
        if (null === $tenant) {
            return;
        }

        $this->applicationConfigurator->configure($tenant);
    }

    private function resolveTenantFromCliOption(InputInterface $input): ?Tenant
    {
        $tenantName = $input->getOption('tenant');

        if (!\is_string($tenantName)) {
            return null;
        }

        return $this->tenantRepository->getByName($tenantName);
    }
}
