<?php

declare(strict_types=1);
namespace U2\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\TaxCompliance\TaxAuditRisk\TaxAuditRiskUpdater;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class TaxAuditRiskCalculatedFieldsListener
{
    private TaxAuditRiskUpdater $updater;

    public function __construct(TaxAuditRiskUpdater $taxAuditRiskUpdater)
    {
        $this->updater = $taxAuditRiskUpdater;
    }

    public function prePersist(PrePersistEventArgs $arguments): void
    {
        $this->update($arguments);
    }

    public function preUpdate(PreUpdateEventArgs $arguments): void
    {
        $this->update($arguments);
    }

    private function update(PrePersistEventArgs|PreUpdateEventArgs $arguments): void
    {
        $entity = $arguments->getObject();
        if ($entity instanceof TaxAuditRisk) {
            $this->updater->update($entity);
        }
    }
}
