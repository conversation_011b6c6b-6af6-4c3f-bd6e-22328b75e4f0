<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\IriConverterInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use U2\Entity\ApiKey;
use U2\User\CurrentUserProvider;

final class ApiKeyDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'API_KEY_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly IriConverterInterface $iriConverter,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): ApiKey
    {
        $context[self::ALREADY_CALLED] = true;

        \assert(\is_array($data));

        $data['createdBy'] = $this->iriConverter->getIriFromResource($this->currentUserProvider->get());
        $context['groups'][] = 'api-key:create:extra';

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        if (ApiKey::class !== $type) {
            return false;
        }

        return \is_array($data);
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            ApiKey::class => false,
        ];
    }
}
