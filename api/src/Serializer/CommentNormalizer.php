<?php

declare(strict_types=1);
namespace U2\Serializer;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Api\Property\CommentExtra;
use U2\Entity\Comment;
use U2\Security\Voter\CommentPermissionsVoter;

class CommentNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    private const string ALREADY_CALLED = 'COMMENT_ATTRIBUTE_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly AuthorizationCheckerInterface $authorizationChecker,
    ) {
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[self::ALREADY_CALLED] = true;
        \assert($object instanceof Comment);

        $object->setCommentExtra(new CommentExtra(
            $this->authorizationChecker->isGranted(CommentPermissionsVoter::ATTRIBUTE_WRITE, $object),
            $this->authorizationChecker->isGranted(CommentPermissionsVoter::ATTRIBUTE_DELETE, $object),
        ));

        if (false === $object->isDeleted()) {
            $context['groups'][] = 'comment:granted';
        }

        return $this->normalizer->normalize($object, $format, $context);
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof Comment;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Comment::class => false,
        ];
    }
}
