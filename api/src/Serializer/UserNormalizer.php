<?php

declare(strict_types=1);
namespace U2\Serializer;

use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Entity\User;
use U2\Security\Authorization\AuthorizationManager;

class UserNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    private const string ALREADY_CALLED = 'USER_ATTRIBUTE_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly AuthorizationManager $authorizationManager,
        private readonly RoleHierarchyInterface $roleHierarchy,
    ) {
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[self::ALREADY_CALLED] = true;

        /** @var array<string,mixed>|string $normalizedUser */
        $normalizedUser = $this->normalizer->normalize($object, $format, $context);
        if (false === \is_array($normalizedUser)) {
            return $normalizedUser;
        }

        \assert($object instanceof User);

        if (\array_key_exists('groups', $context) && \in_array('current-user:read', $context['groups'], true)) {
            $normalizedUser['authorizations'] = $this->authorizationManager->getAuthorizations($object);
        }

        if (\array_key_exists('roles', $normalizedUser)) {
            $normalizedUser['roles'] = $this->roleHierarchy->getReachableRoleNames($object->getRoles());
        }

        return $normalizedUser;
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof User;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            User::class => false,
        ];
    }
}
