<?php

declare(strict_types=1);
namespace U2\Serializer;

use S<PERSON>fony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Datasheets\Item\Formula\ReadableFormulaProvider;
use U2\Entity\Item;

final class ItemNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    public function __construct(
        private readonly ReadableFormulaProvider $readableFormulaProvider,
    ) {
    }
    private const string ALREADY_CALLED = 'ITEM_ATTRIBUTE_NORMALIZER_ALREADY_CALLED';

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[self::ALREADY_CALLED] = true;

        \assert($object instanceof Item);

        $normalizedItem = $this->normalizer->normalize($object, $format, $context);
        if (!\is_array($normalizedItem)) {
            return $normalizedItem;
        }

        if (\array_key_exists('formula', $normalizedItem) && null !== $normalizedItem['formula']) {
            $normalizedItem['formulaReadable'] = $this->readableFormulaProvider->get($object);
            $normalizedItem['formula'] = $object->getFormula()?->getFormulaString();
        }

        return $normalizedItem;
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof Item;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Item::class => false,
        ];
    }
}
