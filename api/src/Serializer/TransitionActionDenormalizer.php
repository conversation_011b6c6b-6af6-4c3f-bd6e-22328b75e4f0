<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\IriConverterInterface;
use ApiPlatform\Validator\Exception\ValidationException;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use U2\Entity\Workflow\Action\Action;
use U2\Entity\Workflow\Action\AddReviewAction;
use U2\Entity\Workflow\Action\RecalculateItemValuesAction;
use U2\Entity\Workflow\Action\ResetReviewsAction;
use U2\Entity\Workflow\Action\SetDefaultItemValuesAction;
use U2\Entity\Workflow\Transition;
use U2\Repository\TransitionRepository;
use U2\Workflow\Action\AddReviewActionType;
use U2\Workflow\Action\RecalculateItemValuesActionType;
use U2\Workflow\Action\ResetReviewsActionType;
use U2\Workflow\Action\SetDefaultItemValuesActionType;

final class TransitionActionDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'TRANSITION_ACTION_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly IriConverterInterface $iriConverter,
        private readonly TransitionRepository $transitionRepository,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): Action
    {
        $context[self::ALREADY_CALLED] = true;
        \assert(
            \is_array($data)
            && \array_key_exists('type', $data)
            && \array_key_exists('uri_variables', $context)
            && \array_key_exists('transitionId', $context['uri_variables'])
        );

        // Add the transition to the data so that the action can be created
        /** @var Transition $transition */
        $transition = $this->transitionRepository->find($context['uri_variables']['transitionId']);
        $data['transition'] = $this->iriConverter->getIriFromResource($transition);

        // Ensure the denormalizer can read the transition we just added
        $context['groups'][] = 'workflow_transition_action:create:extra';

        return $this->denormalizer->denormalize(
            $data,
            match ($data['type']) {
                AddReviewActionType::type => AddReviewAction::class,
                RecalculateItemValuesActionType::type => RecalculateItemValuesAction::class,
                ResetReviewsActionType::type => ResetReviewsAction::class,
                SetDefaultItemValuesActionType::type => SetDefaultItemValuesAction::class,
                default => throw new ValidationException(new ConstraintViolationList([new ConstraintViolation('Invalid type provided "' . $data['type'] . '". Valid types are: ' . implode(',', [AddReviewActionType::type, RecalculateItemValuesActionType::type, ResetReviewsActionType::type, SetDefaultItemValuesActionType::type]), null, [], null, 'type', null)])),
            },
            $format,
            $context
        );
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        if (Action::class !== $type) {
            return false;
        }

        return \is_array($data) && \array_key_exists('operation_name', $context) && '_api_/transitions/{transitionId}/actions_post' === $context['operation_name'];
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Action::class => false,
        ];
    }
}
