<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\IriConverterInterface;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use U2\Entity\Datasheet;
use U2\Entity\Field;
use U2\Repository\DatasheetRepository;

final class FieldDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'FIELD_CONDITION_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly IriConverterInterface $iriConverter,
        private readonly DatasheetRepository $datasheetRepository,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): Field
    {
        $context[self::ALREADY_CALLED] = true;
        \assert(
            \is_array($data)
            && \array_key_exists('uri_variables', $context)
            && \array_key_exists('layoutId', $context['uri_variables'])
        );

        /** @var Datasheet $layout */
        $layout = $this->datasheetRepository->find($context['uri_variables']['layoutId']);
        $data['layout'] = $this->iriConverter->getIriFromResource($layout);

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        if (Field::class !== $type) {
            return false;
        }

        return \is_array($data) && \array_key_exists('operation_name', $context) && 'u2_layout_fields_create' === $context['operation_name'];
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Field::class => false,
        ];
    }
}
