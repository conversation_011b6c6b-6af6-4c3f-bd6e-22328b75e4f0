<?php

declare(strict_types=1);
namespace U2\Serializer;

use Doctrine\ORM\EntityManagerInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use U2\Entity\Item;
use U2\Form\DataTransformer\ReadableFormulaTransformer;

final class ItemDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'ITEM_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function __construct(
        private ReadableFormulaTransformer $readableFormulaTransformer,
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): Item
    {
        $context[self::ALREADY_CALLED] = true;

        \assert(\is_array($data));

        if (!\array_key_exists('formulaReadable', $data)) {
            return $this->denormalizer->denormalize($data, $type, $format, $context);
        }

        $formulaString = $this->readableFormulaTransformer->reverseTransform($data['formulaReadable'] ?? null);
        unset($data['formulaReadable']);

        /** @var Item $denormalizedItem */
        $denormalizedItem = $this->denormalizer->denormalize($data, $type, $format, $context);

        $itemFormula = $denormalizedItem->getFormula();
        if (null === $formulaString || '' === trim($formulaString)) {
            if (null !== $itemFormula) {
                $this->entityManager->remove($itemFormula);
            }
            $denormalizedItem->setFormula(null);
        } else {
            $denormalizedItem->setFormulaString($formulaString);
        }

        return $denormalizedItem;
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        if (!\is_array($data)) {
            return false;
        }

        return Item::class === $type;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Item::class => false,
        ];
    }
}
