<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\State\Pagination\PaginatorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Entity\Unit;
use U2\Repository\UnitRepository;

class UnitExportNormalizer implements NormalizerInterface
{
    public const string FORMAT = 'xlsx';

    public const array headers = [
        'id',
        'refId',
        'name',
        'description',
        'createdAt',
        'updatedAt',
        'verified',
        'taxNumbers',
        'validFrom',
        'validTo',
        'createdBy',
        'updatedBy',
        'unitCountry',
        'unitCurrency',
        'countryFounded',
        'branch',
        'legalForm',
        'auditor',
        'legalName',
        'vatNumber',
        'taxAdvisor',
        'contactUser',
        'parentLegalUnitIncomeTaxRefId',
        'parentLegalUnitVatRefId',
        'parentLegalUnitRefId',
        'registerNumber',
        'registryPlace',
        'billingAddressLine1',
        'billingAddressLine2',
        'billingAddressLine3',
        'billingAddressCity',
        'billingAddressState',
        'billingAddressPostcode',
        'billingAddressCountry',
        'postalAddressLine1',
        'postalAddressLine2',
        'postalAddressLine3',
        'postalAddressCity',
        'postalAddressState',
        'postalAddressPostcode',
        'postalAddressCountry',
    ];

    public function __construct(
        private readonly UnitRepository $unitRepository,
    ) {
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        $operation = $context['operation'] ?? null;
        if (static::FORMAT !== $format || !($operation instanceof GetCollection)) {
            return false;
        }
        if (!(\is_array($data) || $data instanceof PaginatorInterface)) {
            return false;
        }

        return is_a($operation->getClass() ?? '', Unit::class, true);
    }

    /**
     * @param array<mixed> $context
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        /** @var array<int, Unit> $units */
        $units = $object instanceof PaginatorInterface ? iterator_to_array($object) : $object;

        if (0 === \count($units)) {
            return array_fill_keys(self::headers, null);
        }

        return $this->unitRepository->fetchUnitExportData($units);
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return self::FORMAT === $format ? [
            'object' => null,
            '*' => false,
            PaginatorInterface::class => false,
        ] : [];
    }
}
