<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\IriConverterInterface;
use ApiPlatform\Validator\Exception\ValidationException;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use U2\Entity\Workflow\Condition\Condition;
use U2\Entity\Workflow\Condition\CurrentUserHasNotReviewedCondition;
use U2\Entity\Workflow\Condition\CurrentUserHasReviewedCondition;
use U2\Entity\Workflow\Condition\CurrentUserHasRoleCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsAuthorisedToUnitAndOrPartnerUnitCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsCreatorCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsInGroupCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsLastUpdaterCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsNotCreatorCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsNotLastUpdaterCondition;
use U2\Entity\Workflow\Condition\CurrentUserIsUserCondition;
use U2\Entity\Workflow\Condition\EntityHasAttachmentsCondition;
use U2\Entity\Workflow\Condition\EntityHasReviewsCondition;
use U2\Entity\Workflow\Condition\ItemValueEqualsCondition;
use U2\Entity\Workflow\Transition;
use U2\Repository\TransitionRepository;
use U2\Workflow\Condition\CurrentUserHasNotReviewedConditionType;
use U2\Workflow\Condition\CurrentUserHasReviewedConditionType;
use U2\Workflow\Condition\CurrentUserHasRoleConditionType;
use U2\Workflow\Condition\CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType;
use U2\Workflow\Condition\CurrentUserIsCreatorConditionType;
use U2\Workflow\Condition\CurrentUserIsInGroupConditionParameterFactory;
use U2\Workflow\Condition\CurrentUserIsInGroupConditionType;
use U2\Workflow\Condition\CurrentUserIsLastUpdaterConditionType;
use U2\Workflow\Condition\CurrentUserIsNotCreatorConditionType;
use U2\Workflow\Condition\CurrentUserIsNotLastUpdaterConditionType;
use U2\Workflow\Condition\CurrentUserIsUserConditionParameterFactory;
use U2\Workflow\Condition\CurrentUserIsUserConditionType;
use U2\Workflow\Condition\EntityHasAttachmentsConditionType;
use U2\Workflow\Condition\EntityHasReviewsConditionType;
use U2\Workflow\Condition\ItemValueEqualsConditionParameterFactory;
use U2\Workflow\Condition\ItemValueEqualsConditionType;

final class TransitionConditionDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'TRANSITION_CONDITION_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly IriConverterInterface $iriConverter,
        private readonly TransitionRepository $transitionRepository,
        private readonly CurrentUserIsUserConditionParameterFactory $currentUserIsUserConditionParameterFactory,
        private readonly CurrentUserIsInGroupConditionParameterFactory $currentUserIsInGroupConditionParameterFactory,
        private readonly ItemValueEqualsConditionParameterFactory $itemValueEqualsConditionParameterFactory,
    ) {
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): Condition
    {
        $context[self::ALREADY_CALLED] = true;
        \assert(
            \is_array($data)
            && \array_key_exists('type', $data)
            && \array_key_exists('uri_variables', $context)
            && \array_key_exists('transitionId', $context['uri_variables'])
        );

        // Add the transition to the data so that the condition can be created
        /** @var Transition $transition */
        $transition = $this->transitionRepository->find($context['uri_variables']['transitionId']);
        $data['transition'] = $this->iriConverter->getIriFromResource($transition);

        // Ensure the denormalizer can read the transition we just added
        $context['groups'][] = 'workflow_transition_condition:create:extra';

        $matchedConditionClass = match ($data['type']) {
            CurrentUserHasNotReviewedConditionType::type => CurrentUserHasNotReviewedCondition::class,
            CurrentUserHasReviewedConditionType::type => CurrentUserHasReviewedCondition::class,
            CurrentUserHasRoleConditionType::type => CurrentUserHasRoleCondition::class,
            CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType::type => CurrentUserIsAuthorisedToUnitAndOrPartnerUnitCondition::class,
            CurrentUserIsCreatorConditionType::type => CurrentUserIsCreatorCondition::class,
            CurrentUserIsInGroupConditionType::type => CurrentUserIsInGroupCondition::class,
            CurrentUserIsLastUpdaterConditionType::type => CurrentUserIsLastUpdaterCondition::class,
            CurrentUserIsNotCreatorConditionType::type => CurrentUserIsNotCreatorCondition::class,
            CurrentUserIsNotLastUpdaterConditionType::type => CurrentUserIsNotLastUpdaterCondition::class,
            CurrentUserIsUserConditionType::type => CurrentUserIsUserCondition::class,
            EntityHasAttachmentsConditionType::type => EntityHasAttachmentsCondition::class,
            EntityHasReviewsConditionType::type => EntityHasReviewsCondition::class,
            ItemValueEqualsConditionType::type => ItemValueEqualsCondition::class,
            default => throw new ValidationException(new ConstraintViolationList([new ConstraintViolation('Invalid type provided "' . $data['type'] . '". Valid types are: ' . implode(',', [CurrentUserHasNotReviewedConditionType::type, CurrentUserHasReviewedConditionType::type, CurrentUserHasRoleConditionType::type, CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType::type, CurrentUserIsCreatorConditionType::type, CurrentUserIsInGroupConditionType::type, CurrentUserIsLastUpdaterConditionType::type, CurrentUserIsNotCreatorConditionType::type, CurrentUserIsNotLastUpdaterConditionType::type, CurrentUserIsUserConditionType::type, EntityHasAttachmentsConditionType::type, EntityHasReviewsConditionType::type, ItemValueEqualsConditionType::type]), null, [], null, 'type', null)])),
        };

        return match ($matchedConditionClass) {
            CurrentUserIsInGroupCondition::class => $this->denormalizer->denormalize(
                $this->currentUserIsInGroupConditionParameterFactory->create($data),
                CurrentUserIsInGroupCondition::class,
                $format,
                $context
            ),
            CurrentUserIsUserCondition::class => $this->denormalizer->denormalize(
                $this->currentUserIsUserConditionParameterFactory->create($data),
                CurrentUserIsUserCondition::class,
                $format,
                $context
            ),
            ItemValueEqualsCondition::class => $this->denormalizer->denormalize(
                $this->itemValueEqualsConditionParameterFactory->create($data),
                ItemValueEqualsCondition::class,
                $format,
                $context
            ),
            default => $this->denormalizer->denormalize(
                $data,
                $matchedConditionClass,
                $format,
                $context
            ),
        };
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        if (Condition::class !== $type) {
            return false;
        }

        return \is_array($data);
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Condition::class => false,
        ];
    }
}
