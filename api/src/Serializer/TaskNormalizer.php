<?php

declare(strict_types=1);
namespace U2\Serializer;

use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Dto\Task\ExtrasProvider;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Task\Task;
use U2\Task\TaskTypeResolver;

class TaskNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    private const string ALREADY_CALLED = 'TASK_ATTRIBUTE_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly ExtrasProvider $extrasProvider,
        private readonly TaskTypeResolver $taskTypeResolver,
    ) {
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[self::ALREADY_CALLED] = true;

        \assert($object instanceof Task);

        $taskType = $this->taskTypeResolver->resolve($object);
        $object->taskExtra = $this->extrasProvider->get($taskType);
        $object->taskType = $taskType::getWorkflowBindingId();
        if ($taskType instanceof Periodable) {
            $object->period = $taskType->getPeriod();
        }

        $object->unit = $taskType->getUnit();

        return $this->normalizer->normalize($object, $format, $context);
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof Task;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            Task::class => false,
        ];
    }
}
