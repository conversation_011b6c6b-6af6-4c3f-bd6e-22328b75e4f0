<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\CollectionOperationInterface;
use <PERSON><PERSON>fony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use U2\Entity\File;
use U2\File\Helper;
use U2\Security\Voter\VoterAttributes;

class FileNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    private const string ALREADY_CALLED = 'FILE_ATTRIBUTE_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        private readonly Helper $fileManager,
        private readonly RouterInterface $router,
        private readonly AuthorizationCheckerInterface $authorizationChecker,
    ) {
    }

    /**
     * @return array<string, mixed>|string
     */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string
    {
        $context[self::ALREADY_CALLED] = true;

        /** @var array<string,mixed>|string $normalizedFile */
        $normalizedFile = $this->normalizer->normalize($object, $format, $context);
        if (!\is_array($normalizedFile)) {
            return $normalizedFile;
        }

        \assert($object instanceof File);

        $fileSize = $object->getFileSize();
        $mimeType = $object->getMimeType();

        /**
         * TODO: remove this collection override
         * The collection endpoint is slow on production. We believe it might be because we get the
         * mime type and file size from the filesystem (s3) which is slow. If this override improves the
         * performance then we need to save the values in the db to make getting this data faster.
         *
         * @see https://universalunits.atlassian.net/browse/UU-6226
         */
        $isCollection = \in_array('operation', $context, true) && $context['operation'] instanceof CollectionOperationInterface;
        if (!$isCollection && null === $mimeType) {
            $fileSize = $this->fileManager->getFileSizeInBytes($object);
            $mimeType = $this->fileManager->getFileMimeType($object);
        }
        $normalizedFile['contentType'] = $mimeType;
        $normalizedFile['sizeInBytes'] = $fileSize;

        $name = null;
        if ($this->authorizationChecker->isGranted(VoterAttributes::read, $object)) {
            $name = $object->getName();
        }
        $normalizedFile['name'] = $name;

        $normalizedFile['downloadPath'] = $this->router->generate('u2_file_download', ['id' => $object->getId()]);

        return $normalizedFile;
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return $data instanceof File;
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            File::class => false,
        ];
    }
}
