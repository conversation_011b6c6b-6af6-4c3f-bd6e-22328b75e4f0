<?php

declare(strict_types=1);
namespace U2\Serializer;

use <PERSON><PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use U2\Api\Resource\EntityDto;

class EntityDtoDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    private const string ALREADY_CALLED = 'ENTITY_DTO_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): EntityDto
    {
        $context[self::ALREADY_CALLED] = true;
        /** @var EntityDto $entityDto */
        $entityDto = $this->denormalizer->denormalize($data, $type, $format, $context);
        if (!\array_key_exists('uri_variables', $context) || !\array_key_exists('id', $context['uri_variables'])) {
            return $entityDto;
        }

        $entityDto->setId($context['uri_variables']['id']);

        return $entityDto;
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return is_a($type, EntityDto::class, true);
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            EntityDto::class => false,
        ];
    }
}
