<?php

declare(strict_types=1);
namespace U2\Datasheets\View\UnitHierarchy;

use U2\Entity\Datasheet;
use U2\Entity\DatasheetCollection;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Period;
use U2\Entity\UnitHierarchy;

class UnitHierarchyView implements Periodable
{
    /**
     * @param UnitHierarchyViewFieldData[] $data
     */
    public function __construct(
        private readonly Datasheet $layout,
        private readonly DatasheetCollection $layoutCollection,
        private readonly Period $period,
        private readonly UnitHierarchy $unitHierarchy,
        private readonly array $data = [],
    ) {
    }

    public function getLayoutCollection(): DatasheetCollection
    {
        return $this->layoutCollection;
    }

    public function getUnitHierarchy(): UnitHierarchy
    {
        return $this->unitHierarchy;
    }

    public function getLayout(): Datasheet
    {
        return $this->layout;
    }

    public function getPeriod(): Period
    {
        return $this->period;
    }

    /**
     * @return UnitHierarchyViewFieldData[]
     */
    public function getData(): array
    {
        return $this->data;
    }
}
