<?php

declare(strict_types=1);
namespace U2\Datasheets\Item\Formula;

use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;
use U2\Entity\Item;
use U2\Form\DataTransformer\ReadableFormulaTransformer;

class ReadableFormulaProvider
{
    private const string cacheKey = 'formula-readable-';

    public function __construct(
        private readonly TagAwareCacheInterface $tenantCache,
        private readonly ReadableFormulaTransformer $readableFormulaTransformer,
    ) {
    }

    public function get(Item $item): ?string
    {
        return $this->tenantCache->get(self::cacheKey . $item->getId(), function (ItemInterface $cacheItem) use ($item): string|null {
            $cacheItem->tag('formula');

            return $this->readableFormulaTransformer->transform($item->getFormula()?->getFormulaString());
        });
    }
}
