<?php

declare(strict_types=1);
namespace U2\Datasheets\Item\Formula\Element;

use U2\Entity\Item;

class PreviousPeriodItemValue implements ElementInterface
{
    public const string IDENTIFIER = 'P';

    public function __construct(private readonly string $id, private readonly Item $item)
    {
    }

    public function getItem(): Item
    {
        return $this->item;
    }

    public function getId(): string
    {
        return $this->id;
    }
}
