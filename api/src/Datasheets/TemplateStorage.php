<?php

declare(strict_types=1);
namespace U2\Datasheets;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\Cache\TagAwareCacheInterface;
use U2\Entity\Datasheet;
use U2\Exception\Exception;
use U2\FileSystem\TenantFilesystemOperator;

class TemplateStorage
{
    public const string layoutTemplatesSubdirectory = 'layout-templates';

    public function __construct(
        private readonly TenantFilesystemOperator $tenantFilesystem,
        private readonly TagAwareCacheInterface $tenantCache,
    ) {
    }

    /**
     * @throws Exception
     */
    public function getContent(Datasheet $layout): ?string
    {
        return $this->tenantCache->get($this->getCacheKey($layout), function (ItemInterface $cacheItem) use ($layout): ?string {
            $filePath = self::getFilepath($layout);
            if (!$this->tenantFilesystem->fileExists($filePath)) {
                return null;
            }

            return $this->tenantFilesystem->read($filePath);
        });
    }

    public function store(Datasheet $layout, UploadedFile $templateFile): void
    {
        $this->tenantCache->delete($this->getCacheKey($layout));
        $realPath = $templateFile->getRealPath();
        \assert(false !== $realPath);

        $this->tenantFilesystem->storeFile($realPath, self::getFilepath($layout));
    }

    /**
     * @throws Exception
     */
    public function delete(Datasheet $layout): bool
    {
        $this->tenantCache->delete($this->getCacheKey($layout));
        $this->tenantFilesystem->delete(self::getFilepath($layout));

        return true;
    }

    public static function getFilepath(Datasheet $layout): string
    {
        return self::layoutTemplatesSubdirectory . \DIRECTORY_SEPARATOR . "{$layout->getId()}.html.twig";
    }

    private function getCacheKey(Datasheet $layout): string
    {
        return "layout-template-{$layout->getId()}";
    }
}
