<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250619151026 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove dueDate from task types.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
                ALTER TABLE tcm_other_deadline DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tcm_tax_assessment_monitor DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tcm_tax_authority_audit_objection DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tcm_tax_filing_monitor DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tpm_country_by_country_report DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tpm_local_file DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tpm_master_file DROP due_date
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE tpm_transaction DROP due_date
            SQL);
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
