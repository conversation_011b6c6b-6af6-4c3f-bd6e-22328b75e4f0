<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250602085138 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add authorization items `READ`, `UPDATE` and `DELETE` to all authorizations containing `ACCESS` right';
    }

    public function up(Schema $schema): void
    {
        // First add READ to all authorizations with ACCESS
        $this->addSql(<<<SQL
                UPDATE authorization
                    set rights = JSON_ARRAY_APPEND(rights, '$', 'READ')
                    where JSON_CONTAINS(rights, '"ACCESS"')
                      AND (item = 'TPM_COUNTRY_BY_COUNTRY_REPORT' OR item = 'TPM_LOCAL_FILE' OR item = 'TPM_MASTER_FILE')
                      AND NOT JSON_CONTAINS(rights, '"READ"')
            SQL
        );

        // Then add UPDATE if it doesn't exist
        $this->addSql(<<<SQL
                UPDATE authorization
                    set rights = JSON_ARRAY_APPEND(rights, '$', 'UPDATE')
                    where JSO<PERSON>_CONTAINS(rights, '"ACCESS"')
                      AND (item = 'TPM_COUNTRY_BY_COUNTRY_REPORT' OR item = 'TPM_LOCAL_FILE' OR item = 'TPM_MASTER_FILE')
                      AND NOT JSON_CONTAINS(rights, '"UPDATE"')
            SQL
        );

        // Finally add DELETE if it doesn't exist
        $this->addSql(<<<SQL
                UPDATE authorization
                    set rights = JSON_ARRAY_APPEND(rights, '$', 'DELETE')
                    where JSON_CONTAINS(rights, '"ACCESS"')
                      AND (item = 'TPM_COUNTRY_BY_COUNTRY_REPORT' OR item = 'TPM_LOCAL_FILE' OR item = 'TPM_MASTER_FILE')
                      AND NOT JSON_CONTAINS(rights, '"DELETE"')
            SQL
        );
    }
}
