<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;

final class Version20230912131653 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove the booking date field from apm';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE apm_transaction DROP booking_date');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE apm_transaction ADD booking_date DATE DEFAULT NULL');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
