<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250602085139 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add authorization item `ASSIGN` to all authorizations containing `READ` right';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<SQL
                UPDATE authorization
                    set rights = JSON_ARRAY_APPEND(rights, '$', 'ASSIGN')
                    where JSON_CONTAINS(rights, '"READ"')
                    or JSON_CONTAINS(rights, '"UPDATE"')
            SQL
        );
    }
}
