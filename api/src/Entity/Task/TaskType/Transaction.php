<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Entity\Configuration\Field\BillingType;
use U2\Entity\Configuration\Field\PricingMethod;
use U2\Entity\Configuration\Field\TransactionType;
use U2\Entity\Currency;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\Entity\Unit;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Repository\TransactionRepository;
use U2\Task\TaskType\TransactionInterface;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[ORM\Entity(repositoryClass: TransactionRepository::class)]
#[ORM\Table(name: 'tpm_transaction')]
#[ReadableName(value: 'u2_tpm.transaction')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
class Transaction extends TaskType implements TransactionInterface, Periodable, Transferable
{
    use PeriodableTrait;

    #[ORM\ManyToOne(targetEntity: Unit::class)]
    protected ?Unit $partnerUnit = null;

    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected ?Currency $transactionCurrency = null;

    #[Assert\Range(min: '-99999999999999', max: '99999999999999')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::DECIMAL, precision: 14, scale: 0, nullable: false)]
    protected ?string $transactionAmount = null;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: TransactionType::class)]
    private ?TransactionType $type = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN)]
    protected bool $armsLength = true;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $unitRequiresDocumentation = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $partnerUnitRequiresDocumentation = null;

    #[Assert\NotBlank]
    #[ORM\Column(type: Types::STRING, length: 255, nullable: false)]
    private ?string $name = null;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: BillingType::class)]
    private ?BillingType $billingType = null;

    #[ORM\Column(nullable: true)]
    private ?string $subType = null;

    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: PricingMethod::class)]
    private ?PricingMethod $transferPricingMethod = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN)]
    private bool $carryForward = false;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $assetLiabilityId = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTime $contractDate = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTime $contractExpiryDate = null;

    #[Assert\Range(notInRangeMessage: 'u2.rate.not_in_range', min: 0, max: 1)]
    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 6, nullable: true)]
    private ?string $couponInterestRate = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $forwardRate = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    protected ?string $couponInterestRateType = null;

    #[Assert\Range(min: '-99999999999999', max: '99999999999999')]
    #[ORM\Column(type: Types::DECIMAL, precision: 14, scale: 0, nullable: true)]
    protected ?string $previousPeriodBookValue = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Currency $previousPeriodBookValueCurrency = null;

    #[Assert\Range(min: '-99999999999999', max: '99999999999999')]
    #[ORM\Column(type: Types::DECIMAL, precision: 14, scale: 0, nullable: true)]
    protected ?string $currentPeriodInterestExpenses = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Currency $currentPeriodInterestExpensesCurrency = null;

    #[Assert\Range(min: '-99999999999999', max: '99999999999999')]
    #[ORM\Column(type: Types::DECIMAL, precision: 14, scale: 0, nullable: true)]
    protected ?string $currentPeriodBookValue = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Currency $currentPeriodBookValueCurrency = null;

    #[Assert\Range(min: '-99999999999999', max: '99999999999999')]
    #[ORM\Column(type: Types::DECIMAL, precision: 14, scale: 0, nullable: true)]
    private ?string $guaranteeFeeAmount = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Currency $guaranteeFeeCurrency = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTime $maturityDate = null;

    #[Assert\Length(max: 50)]
    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    private ?string $transactionVolume = null;

    #[Assert\Length(max: 50)]
    #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
    private ?string $underlyingContract = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $unitStandardTaxationApplicable = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN, nullable: true)]
    private ?bool $partnerUnitStandardTaxationApplicable = null;

    public function getBillingType(): ?BillingType
    {
        return $this->billingType;
    }

    public function setBillingType(?BillingType $billingType): void
    {
        $this->billingType = $billingType;
    }

    public function getSubType(): ?string
    {
        return $this->subType;
    }

    public function setSubType(?string $subType): void
    {
        $this->subType = $subType;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getUnitRequiresDocumentation(): ?bool
    {
        return $this->unitRequiresDocumentation;
    }

    public function setUnitRequiresDocumentation(bool $unitRequiresDocumentation): void
    {
        $this->unitRequiresDocumentation = $unitRequiresDocumentation;
    }

    public function getPartnerUnitRequiresDocumentation(): ?bool
    {
        return $this->partnerUnitRequiresDocumentation;
    }

    public function setPartnerUnitRequiresDocumentation(bool $partnerUnitRequiresDocumentation): void
    {
        $this->partnerUnitRequiresDocumentation = $partnerUnitRequiresDocumentation;
    }

    public function getTransferPricingMethod(): ?PricingMethod
    {
        return $this->transferPricingMethod;
    }

    public function setTransferPricingMethod(?PricingMethod $transferPricingMethod): void
    {
        $this->transferPricingMethod = $transferPricingMethod;
    }

    public function isArmsLength(): bool
    {
        return $this->armsLength;
    }

    public function setArmsLength(bool $armsLength): void
    {
        $this->armsLength = $armsLength;
    }

    public function isCarryForward(): ?bool
    {
        return $this->carryForward;
    }

    public function setCarryForward(bool $carryForward): void
    {
        $this->carryForward = $carryForward;
    }

    public function getAssetLiabilityId(): ?string
    {
        return $this->assetLiabilityId;
    }

    public function setAssetLiabilityId(?string $assetLiabilityId): void
    {
        $this->assetLiabilityId = $assetLiabilityId;
    }

    public function getContractDate(): ?\DateTime
    {
        return $this->contractDate;
    }

    public function setContractDate(?\DateTime $contractDate): void
    {
        $this->contractDate = $contractDate;
    }

    public function getContractExpiryDate(): ?\DateTime
    {
        return $this->contractExpiryDate;
    }

    public function setContractExpiryDate(?\DateTime $contractExpiryDate): void
    {
        $this->contractExpiryDate = $contractExpiryDate;
    }

    public function getCouponInterestRate(): ?string
    {
        return $this->couponInterestRate;
    }

    public function setCouponInterestRate(?string $couponInterestRate): void
    {
        $this->couponInterestRate = $couponInterestRate;
    }

    public function getCouponInterestRateType(): ?string
    {
        return $this->couponInterestRateType;
    }

    public function setCouponInterestRateType(?string $couponInterestRateType): void
    {
        $this->couponInterestRateType = $couponInterestRateType;
    }

    public function getPreviousPeriodBookValue(): ?string
    {
        return $this->previousPeriodBookValue;
    }

    public function setPreviousPeriodBookValue(?string $previousPeriodBookValue): void
    {
        $this->previousPeriodBookValue = $previousPeriodBookValue;
    }

    public function getPreviousPeriodBookValueCurrency(): ?Currency
    {
        return $this->previousPeriodBookValueCurrency;
    }

    public function setPreviousPeriodBookValueCurrency(?Currency $previousPeriodBookValueCurrency): void
    {
        $this->previousPeriodBookValueCurrency = $previousPeriodBookValueCurrency;
    }

    public function getCurrentPeriodInterestExpenses(): ?string
    {
        return $this->currentPeriodInterestExpenses;
    }

    public function setCurrentPeriodInterestExpenses(?string $currentPeriodInterestExpenses): void
    {
        $this->currentPeriodInterestExpenses = $currentPeriodInterestExpenses;
    }

    public function getCurrentPeriodInterestExpensesCurrency(): ?Currency
    {
        return $this->currentPeriodInterestExpensesCurrency;
    }

    public function setCurrentPeriodInterestExpensesCurrency(?Currency $currentPeriodInterestExpensesCurrency): void
    {
        $this->currentPeriodInterestExpensesCurrency = $currentPeriodInterestExpensesCurrency;
    }

    public function getCurrentPeriodBookValue(): ?string
    {
        return $this->currentPeriodBookValue;
    }

    public function setCurrentPeriodBookValue(?string $currentPeriodBookValue): void
    {
        $this->currentPeriodBookValue = $currentPeriodBookValue;
    }

    public function getCurrentPeriodBookValueCurrency(): ?Currency
    {
        return $this->currentPeriodBookValueCurrency;
    }

    public function setCurrentPeriodBookValueCurrency(?Currency $currentPeriodBookValueCurrency): void
    {
        $this->currentPeriodBookValueCurrency = $currentPeriodBookValueCurrency;
    }

    public function getForwardRate(): ?string
    {
        return $this->forwardRate;
    }

    public function setForwardRate(?string $forwardRate): void
    {
        $this->forwardRate = $forwardRate;
    }

    public function getGuaranteeFeeAmount(): ?string
    {
        return $this->guaranteeFeeAmount;
    }

    public function setGuaranteeFeeAmount(?string $guaranteeFeeAmount): void
    {
        $this->guaranteeFeeAmount = $guaranteeFeeAmount;
    }

    public function getGuaranteeFeeCurrency(): ?Currency
    {
        return $this->guaranteeFeeCurrency;
    }

    public function setGuaranteeFeeCurrency(?Currency $guaranteeFeeCurrency): void
    {
        $this->guaranteeFeeCurrency = $guaranteeFeeCurrency;
    }

    public function getMaturityDate(): ?\DateTime
    {
        return $this->maturityDate;
    }

    public function setMaturityDate(?\DateTime $maturityDate): void
    {
        $this->maturityDate = $maturityDate;
    }

    public function getPartnerUnit(): ?Unit
    {
        return $this->partnerUnit;
    }

    public function setPartnerUnit(?Unit $partnerUnit): void
    {
        $this->partnerUnit = $partnerUnit;
    }

    public function getTransactionCurrency(): ?Currency
    {
        return $this->transactionCurrency;
    }

    public function setTransactionCurrency(?Currency $transactionCurrency): void
    {
        $this->transactionCurrency = $transactionCurrency;
    }

    public function getTransactionAmount(): ?string
    {
        return $this->transactionAmount;
    }

    public function setTransactionAmount(?string $transactionAmount): void
    {
        $this->transactionAmount = $transactionAmount;
    }

    #[Assert\Callback]
    public function validatePartner(ExecutionContextInterface $context): void
    {
        if (null === $this->partnerUnit) {
            $context
                ->buildViolation('u2_financial.partner.partner_unit_is_required')
                ->atPath('partnerUnit')
                ->addViolation();
        }
    }

    #[Assert\Callback]
    public function isUnitAndPartnerUnitNotTheSame(?ExecutionContextInterface $context = null): bool
    {
        $unit = $this->getUnit();
        if (null !== $unit && $unit === $this->getPartnerUnit()) {
            if ($context instanceof ExecutionContextInterface) {
                $context
                    ->buildViolation('u2_financial.partner.unit_and_partner_unit_cannot_be_the_same')
                    ->atPath('partnerUnit')
                    ->addViolation();
            }

            return false;
        }

        return true;
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tpm_transaction';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TPM Transactions';
    }

    public static function getTaskType(): string
    {
        return 'tpm_transaction';
    }

    public function getType(): ?TransactionType
    {
        return $this->type;
    }

    public function setType(?TransactionType $type): void
    {
        $this->type = $type;
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->name}";
    }

    public function getTransactionVolume(): ?string
    {
        return $this->transactionVolume;
    }

    public function setTransactionVolume(?string $transactionVolume): void
    {
        $this->transactionVolume = $transactionVolume;
    }

    public function getUnderlyingContract(): ?string
    {
        return $this->underlyingContract;
    }

    public function setUnderlyingContract(?string $underlyingContract): void
    {
        $this->underlyingContract = $underlyingContract;
    }

    public function getUnitStandardTaxationApplicable(): ?bool
    {
        return $this->unitStandardTaxationApplicable;
    }

    public function setUnitStandardTaxationApplicable(?bool $unitStandardTaxationApplicable): void
    {
        $this->unitStandardTaxationApplicable = $unitStandardTaxationApplicable;
    }

    public function getPartnerUnitStandardTaxationApplicable(): ?bool
    {
        return $this->partnerUnitStandardTaxationApplicable;
    }

    public function setPartnerUnitStandardTaxationApplicable(?bool $partnerUnitStandardTaxationApplicable): void
    {
        $this->partnerUnitStandardTaxationApplicable = $partnerUnitStandardTaxationApplicable;
    }
}
