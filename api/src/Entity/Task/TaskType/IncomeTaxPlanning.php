<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\TaxAssessmentTaskType;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[ORM\Entity]
#[ORM\Table(name: 'tam_income_tax_planning')]
#[ReadableName(value: 'u2_tam.income_tax_planning')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
class IncomeTaxPlanning extends TaskType implements Transferable, Periodable, TaxAssessmentTaskType
{
    use PeriodableTrait;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: TaxType::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?TaxType $taxType = null;

    /**
     * @var numeric-string|null
     */
    #[Assert\Range(notInRangeMessage: 'u2.rate.not_in_range', min: 0, max: 1)]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 6, nullable: false)]
    private ?string $taxRate = null;

    #[Assert\Length(max: 4)]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: false)]
    private ?int $planningPeriod = null;

    #[Assert\NotNull]
    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $profitBeforeTax = null;

    #[Assert\NotBlank]
    #[ORM\Column(type: Types::BIGINT, nullable: false)]
    private int $taxableIncome = 0;

    #[Assert\NotBlank]
    #[ORM\Column(type: Types::BIGINT, nullable: false)]
    private int $incomeTax = 0;

    #[Assert\Range(notInRangeMessage: 'u2.rate.not_in_range', min: 0, max: 1)]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 6, nullable: false)]
    private string $etr = '0';

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $tempDiff = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $tempDiffComment = null;

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $permDiff = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $permDiffComment = null;

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $compOfTaxLossesAccruedDta = null;

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $compOfTaxLossesNotAccruedDta = null;

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $compOfTaxCreditsAccruedDta = null;

    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $compOfTaxCreditsNotAccruedDta = null;

    public function getTaxType(): ?TaxType
    {
        return $this->taxType;
    }

    public function setTaxType(?TaxType $taxType): void
    {
        $this->taxType = $taxType;
    }

    /**
     * @return numeric-string|null
     */
    public function getTaxRate(): ?string
    {
        return $this->taxRate;
    }

    /**
     * @param numeric-string $taxRate
     */
    public function setTaxRate(string $taxRate): void
    {
        $this->taxRate = $taxRate;
    }

    public function getProfitBeforeTax(): ?int
    {
        return $this->profitBeforeTax;
    }

    public function setProfitBeforeTax(int $profitBeforeTax): void
    {
        $this->profitBeforeTax = $profitBeforeTax;
    }

    public function getTaxableIncome(): int
    {
        return $this->taxableIncome;
    }

    public function setTaxableIncome(int $taxableIncome): void
    {
        $this->taxableIncome = $taxableIncome;
    }

    public function getIncomeTax(): int
    {
        return $this->incomeTax;
    }

    public function setIncomeTax(int $incomeTax): void
    {
        $this->incomeTax = $incomeTax;
    }

    public function getEtr(): string
    {
        return $this->etr;
    }

    public function setEtr(string $etr): void
    {
        $this->etr = $etr;
    }

    public function getTempDiff(): ?int
    {
        return $this->tempDiff;
    }

    public function setTempDiff(?int $tempDiff): void
    {
        $this->tempDiff = $tempDiff;
    }

    public function getTempDiffComment(): ?string
    {
        return $this->tempDiffComment;
    }

    public function setTempDiffComment(?string $tempDiffComment): void
    {
        $this->tempDiffComment = $tempDiffComment;
    }

    public function getPermDiff(): ?int
    {
        return $this->permDiff;
    }

    public function setPermDiff(?int $permDiff): void
    {
        $this->permDiff = $permDiff;
    }

    public function getPermDiffComment(): ?string
    {
        return $this->permDiffComment;
    }

    public function setPermDiffComment(?string $permDiffComment): void
    {
        $this->permDiffComment = $permDiffComment;
    }

    #[Assert\Callback]
    public function isPlanningPeriodValid(?ExecutionContextInterface $context = null): bool
    {
        if (null !== $this->getPlanningPeriod() && null !== (null === $this->getPeriod())) {
            if ($this->getPlanningPeriod() < $this->getPeriod()->getStartDate()->format('Y')) {
                if ($context instanceof ExecutionContextInterface) {
                    $context
                        ->buildViolation('u2_tam.planning_period.planning_year_after_start_year')
                        ->atPath('planningPeriod')
                        ->addViolation();
                }

                return false;
            }

            if ($this->getPlanningPeriod() > (int) $this->getPeriod()->getEndDate()->format('Y') + 10) {
                if ($context instanceof ExecutionContextInterface) {
                    $context
                        ->buildViolation('u2_tam.planning_period.max_message')
                        ->atPath('planningPeriod')
                        ->addViolation();
                }

                return false;
            }

            return true;
        }

        return false;
    }

    public function getPlanningPeriod(): ?int
    {
        return $this->planningPeriod;
    }

    public function setPlanningPeriod(int $planningPeriod): void
    {
        $this->planningPeriod = $planningPeriod;
    }

    public function getCompOfTaxLossesAccruedDta(): ?int
    {
        return $this->compOfTaxLossesAccruedDta;
    }

    public function setCompOfTaxLossesAccruedDta(?int $compOfTaxLossesAccruedDta): void
    {
        $this->compOfTaxLossesAccruedDta = $compOfTaxLossesAccruedDta;
    }

    public function getCompOfTaxLossesNotAccruedDta(): ?int
    {
        return $this->compOfTaxLossesNotAccruedDta;
    }

    public function setCompOfTaxLossesNotAccruedDta(?int $compOfTaxLossesNotAccruedDta): void
    {
        $this->compOfTaxLossesNotAccruedDta = $compOfTaxLossesNotAccruedDta;
    }

    public function getCompOfTaxCreditsAccruedDta(): ?int
    {
        return $this->compOfTaxCreditsAccruedDta;
    }

    public function setCompOfTaxCreditsAccruedDta(?int $compOfTaxCreditsAccruedDta): void
    {
        $this->compOfTaxCreditsAccruedDta = $compOfTaxCreditsAccruedDta;
    }

    public function getCompOfTaxCreditsNotAccruedDta(): ?int
    {
        return $this->compOfTaxCreditsNotAccruedDta;
    }

    public function setCompOfTaxCreditsNotAccruedDta(?int $compOfTaxCreditsNotAccruedDta): void
    {
        $this->compOfTaxCreditsNotAccruedDta = $compOfTaxCreditsNotAccruedDta;
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tam_income_tax_planning';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TAM Income Tax Planning';
    }

    public static function getTaskType(): string
    {
        return 'tam_income_tax_planning';
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->taxType?->getName()}: {$this->unit?->getRefId()}, {$this->period?->getName()}";
    }
}
