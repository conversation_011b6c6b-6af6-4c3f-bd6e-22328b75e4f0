<?php

declare(strict_types=1);
namespace U2\Entity\Task;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Order;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use JetBrains\PhpStorm\Pure;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Api\Processor\AddDatasheetCollectionToTaskProcessor;
use U2\Api\Processor\RemoveDatasheetCollectionFromTaskProcessor;
use U2\Api\Property\TaskExtra;
use U2\Dto\Task\AddDatasheetCollectionToTask;
use U2\Entity\Comment;
use U2\Entity\DatasheetCollection;
use U2\Entity\File;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Interfaces\FileAttachable;
use U2\Entity\Period;
use U2\Entity\Review;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Entity\Workflow\StatusTransitionLog;
use U2\Messenger\Task\AddReviewTaskMessage;
use U2\Messenger\Task\AssignUserToTaskMessage;
use U2\Messenger\Task\RemoveReviewTaskMessage;
use U2\Messenger\Task\TransitionTaskMessage;
use U2\Repository\Task\TaskRepository;
use U2\Security\UserRoles;
use U2\Security\Voter\VoterAttributes;
use U2\Util\DateTime;
use U2\Validator as U2Assert;
use U2\Workflow\StatusTypes;

#[ORM\Entity(repositoryClass: TaskRepository::class)]
#[U2Assert\AssignableToUser(groups: ['user_assignment'])]
#[ApiResource(
    operations: [
        new Get(
            security: 'is_granted("' . VoterAttributes::read . '", object)',
        ),
        new Post(
            uriTemplate: '/tasks/{id}/assign-user',
            status: Response::HTTP_NO_CONTENT,
            openapi: new Operation(description: 'Sets the assignee of a task'),
            denormalizationContext: ['groups' => ['task:assign-user']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::assign . '", object.task)',
            input: AssignUserToTaskMessage::class,
            output: false,
            messenger: 'input',
            name: 'api_task_post_assign_user',
        ),
        new Post(
            uriTemplate: '/tasks/{id}/add-review',
            status: Response::HTTP_NO_CONTENT,
            openapi: new Operation(description: 'Adds a review for the current user to a task'),
            denormalizationContext: ['groups' => ['task:add-review']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::read . '", object.task)',
            input: AddReviewTaskMessage::class,
            messenger: 'input',
            name: 'api_task_post_add_review',
        ),
        new Post(
            uriTemplate: '/tasks/{id}/remove-review',
            status: Response::HTTP_NO_CONTENT,
            openapi: new Operation(description: 'Remove the review of the current user from a task'),
            denormalizationContext: ['groups' => ['task:remove-review']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::read . '", object.task)',
            input: RemoveReviewTaskMessage::class,
            messenger: 'input',
            name: 'api_task_post_remove_review',
        ),
        new Post(
            uriTemplate: '/tasks/{id}/transition',
            status: Response::HTTP_NO_CONTENT,
            openapi: new Operation(description: 'Transitions a task from one to another state'),
            denormalizationContext: ['groups' => ['task:transition']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::read . '", object.task)',
            input: TransitionTaskMessage::class,
            output: false,
            messenger: 'input',
            name: 'api_task_post_transition',
        ),
    ],
    normalizationContext: [
        'groups' => ['task:read'],
    ]
)]
#[Post(
    uriTemplate: '/tasks/{id}/layout-collections',
    uriVariables: [
        'id' => new Link(fromClass: Task::class, property: 'id'),
    ],
    openapi: false,
    security: 'is_granted("' . UserRoles::Admin->value . '")',
    input: AddDatasheetCollectionToTask::class,
    processor: AddDatasheetCollectionToTaskProcessor::class
)]
#[Delete(
    uriTemplate: '/tasks/{id}/layout-collections/{layoutCollectionId}',
    uriVariables: [
        'id' => new Link(fromClass: Task::class),
        'layoutCollectionId' => new Link(fromClass: DatasheetCollection::class),
    ],
    openapi: false,
    security: 'is_granted("' . UserRoles::Admin->value . '")',
    read: false,
    processor: RemoveDatasheetCollectionFromTaskProcessor::class
)]
class Task implements Entity, FileAttachable
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[Groups(groups: ['task:read', 'file-linked-entity:read'])]
    private Uuid $id;

    /**
     * @var Collection<int, CheckState>
     */
    #[ORM\OneToMany(mappedBy: 'task', targetEntity: CheckState::class, cascade: ['remove'])]
    private Collection $checkStates;

    /**
     * @var Collection<int, Comment>
     */
    #[ORM\OneToMany(mappedBy: 'task', targetEntity: Comment::class, cascade: ['remove', 'persist'])]
    private Collection $comments;

    /**
     * @var Collection<int,StatusTransitionLog>
     */
    #[ORM\OneToMany(mappedBy: 'task', targetEntity: StatusTransitionLog::class, cascade: ['remove'])]
    private Collection $statusTransitionLog;

    /**
     * @var Collection<int, File>
     */
    #[ORM\ManyToMany(targetEntity: File::class, cascade: ['persist'])]
    #[ORM\JoinTable(name: 'task_to_file')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    #[ORM\InverseJoinColumn(onDelete: 'RESTRICT')]
    #[ORM\OrderBy(value: ['name' => Order::Ascending->value])]
    private Collection $files;

    /**
     * @var Collection<int,Review>
     */
    #[ORM\OneToMany(mappedBy: 'task', targetEntity: Review::class, cascade: ['remove', 'persist'])]
    #[Groups(groups: ['task:read'])]
    private Collection $reviews;

    /**
     * @var Collection<int,User>
     */
    #[ORM\ManyToMany(targetEntity: User::class)]
    #[ORM\JoinTable(name: 'task_to_watcher')]
    #[Groups(groups: ['task:read'])]
    private Collection $watchers;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Groups(groups: ['task:read'])]
    private ?User $assignee = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[Gedmo\Blameable(on: 'create')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $reporter = null;

    #[Assert\NotNull]
    #[Assert\Type(type: 'bool')]
    #[ORM\Column(type: Types::BOOLEAN)]
    private bool $imported = false;

    #[Gedmo\Blameable(on: 'create')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $updatedBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTime $createdAt;

    #[Gedmo\Timestampable(on: 'update')]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTime $updatedAt;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $sourceId = null;

    /**
     * @internal
     */
    #[Groups(groups: ['task:read', 'file-linked-entity:read'])]
    #[SerializedName('u2:extra')]
    public TaskExtra $taskExtra;

    /**
     * @internal This is currently keeping the value of the workflowBindingId which will be later replaced by the task type id
     */
    #[Groups(groups: ['task:read', 'file-linked-entity:read'])]
    public string $taskType;

    #[Groups(groups: ['task:read', 'file-linked-entity:read'])]
    public ?Unit $unit;

    #[Groups(groups: ['task:read', 'file-linked-entity:read'])]
    public ?Period $period;

    /**
     * @var Collection<int, DatasheetCollection>
     */
    #[ORM\ManyToMany(targetEntity: DatasheetCollection::class, fetch: 'EXTRA_LAZY')]
    #[ORM\JoinTable(name: 'task_layout_collection')]
    #[ORM\InverseJoinColumn(name: 'layout_collection_id', onDelete: 'CASCADE')]
    private Collection $layoutCollections;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    #[Groups(groups: ['task:read'])]
    private ?\DateTime $dueDate = null;

    public function __construct(
        #[ORM\Column(type: Types::STRING, length: 255)]
        #[Groups(groups: ['task:read'])]
        private string $type,

        #[ORM\ManyToOne(targetEntity: Status::class)]
        #[ORM\JoinColumn(nullable: false)]
        #[Groups(groups: ['task:read'])]
        private Status $status,
    ) {
        $this->id = Uuid::v4();
        $this->layoutCollections = new ArrayCollection();
        $this->checkStates = new ArrayCollection();
        $this->comments = new ArrayCollection();
        $this->statusTransitionLog = new ArrayCollection();
        $this->files = new ArrayCollection();
        $this->reviews = new ArrayCollection();
        $this->watchers = new ArrayCollection();

        $createdAt = DateTime::createNow();

        $this->updatedAt = $createdAt;
        $this->createdAt = $createdAt;
    }

    public function getId(): Uuid
    {
        return $this->id;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function addCheckState(CheckState $checkState): void
    {
        if ($this->checkStates->contains($checkState)) {
            return;
        }

        $this->checkStates->add($checkState);
    }

    /**
     * @return Collection<int, CheckState>
     */
    public function getCheckStates(): Collection
    {
        return $this->checkStates;
    }

    public function addStatusTransitionLog(StatusTransitionLog $checkState): void
    {
        if ($this->statusTransitionLog->contains($checkState)) {
            return;
        }

        $this->statusTransitionLog->add($checkState);
    }

    /**
     * @return Collection<int, StatusTransitionLog>
     */
    public function getStatusTransitionLogs(): Collection
    {
        return $this->statusTransitionLog;
    }

    /**
     * @return Collection<int, Comment>
     */
    public function getComments(): Collection
    {
        return $this->comments;
    }

    /**
     * @param Collection<int, Comment> $comments
     */
    public function setComments(Collection $comments): void
    {
        $this->comments = new ArrayCollection();
        foreach ($comments as $comment) {
            $this->addComment($comment);
        }
    }

    public function addComment(Comment $comment): void
    {
        if ($this->comments->contains($comment)) {
            return;
        }

        $this->comments->add($comment);
    }

    public function addFile(File $file): void
    {
        if (false === $this->files->contains($file)) {
            $this->files->add($file);
        }
    }

    public function removeFile(File $file): void
    {
        $this->files->removeElement($file);
    }

    /**
     * @return Collection<int, File>
     */
    public function getFiles(): Collection
    {
        return $this->files;
    }

    public function review(User $user): void
    {
        $this->reviews->add(new Review($this, $user));
    }

    public function removeReview(Review $review): void
    {
        $this->reviews->removeElement($review);
    }

    /**
     * @return Collection<int, Review>
     */
    public function getReviews(): Collection
    {
        return $this->reviews;
    }

    public function getReviewByUser(User $user): ?Review
    {
        foreach ($this->reviews as $review) {
            if ($review->getUser()->getId() === $user->getId()) {
                return $review;
            }
        }

        return null;
    }

    public function reviewExistsForUser(User $user): bool
    {
        return null !== $this->getReviewByUser($user);
    }

    public function setAssignee(?User $assignee): void
    {
        $this->assignee = $assignee;
    }

    public function getAssignee(): ?User
    {
        return $this->assignee;
    }

    /**
     * @return Collection<int,User>
     */
    public function getWatchers(): Collection
    {
        return $this->watchers;
    }

    public function watch(User $watcher): void
    {
        if ($this->watchers->contains($watcher)) {
            return;
        }
        $this->watchers->add($watcher);
    }

    public function unwatch(User $watcher): void
    {
        $this->watchers->removeElement($watcher);
    }

    public function isUserWatching(User $user): bool
    {
        return $this->watchers->contains($user);
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getReporter(): ?User
    {
        return $this->reporter;
    }

    public function setReporter(?User $reporter): void
    {
        $this->reporter = $reporter;
    }

    public function getImported(): bool
    {
        return $this->imported;
    }

    public function setImported(bool $imported): void
    {
        $this->imported = $imported;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy): void
    {
        $this->updatedBy = $updatedBy;
    }

    public function setCreatedAt(\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(\DateTime $newDate): void
    {
        if ($newDate->getTimestamp() > $this->updatedAt->getTimestamp()) {
            $this->updatedAt = $newDate;
        }
    }

    public function getUpdatedAt(): \DateTime
    {
        return $this->updatedAt;
    }

    public function getSourceId(): ?int
    {
        return $this->sourceId;
    }

    public function setSourceId(?int $sourceId): void
    {
        $this->sourceId = $sourceId;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): void
    {
        $this->status = $status;
    }

    #[Pure]
    public function isComplete(): bool
    {
        return StatusTypes::TYPE_COMPLETE === $this->status->getType();
    }

    // TODO: Remove when the name is located on the task. See: https://universalunits.atlassian.net/browse/UU-5885
    public function getDisplayName(): string
    {
        throw new \BadMethodCallException('Please call `getDisplayName` on the task type owning this task instead.');
    }

    /**
     * @return Collection<int, DatasheetCollection>
     */
    public function getLayoutCollections(): Collection
    {
        return $this->layoutCollections;
    }

    /**
     * @param Collection<int, DatasheetCollection> $layoutCollections
     */
    public function setLayoutCollections(Collection $layoutCollections): void
    {
        $this->layoutCollections = $layoutCollections;
    }

    public function addLayoutCollection(DatasheetCollection $layoutCollection): void
    {
        if ($this->layoutCollections->contains($layoutCollection)) {
            return;
        }

        $this->layoutCollections->add($layoutCollection);
    }

    public function removeLayoutCollection(DatasheetCollection $layoutCollection): void
    {
        $this->layoutCollections->removeElement($layoutCollection);
    }

    #[Groups(groups: ['task:read'])]
    public function getLayoutCollectionCount(): int
    {
        return $this->layoutCollections->count();
    }

    public function getDueDate(): ?\DateTime
    {
        return $this->dueDate;
    }

    public function setDueDate(?\DateTime $dueDate): void
    {
        $this->dueDate = $dueDate;
    }
}
