<?php

declare(strict_types=1);
namespace U2\Entity;

use U2\Entity\Interfaces\Blameable;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Interfaces\FileAttachable;
use U2\Entity\Interfaces\Timestampable;

interface DocumentSection extends Entity, FileAttachable, Blameable, Timestampable
{
    public const array sectionShortNameToClassMap = [
        'tpm-local-file-section' => LocalFileSection::class,
        'tpm-master-file-section' => MasterFileSection::class,
        'tpm-country-by-country-report-section' => CountryByCountryReportSection::class,
    ];

    public function getContent(): string;

    public function setContent(string $content): void;

    public function getDocument(): ?StructuredDocumentInterface;

    public function setDocument(?StructuredDocumentInterface $document): void;

    public function getEditable(): bool;

    public function setEditable(bool $editable): void;

    public function getInclude(): bool;

    public function setInclude(bool $include): void;

    public function getLevel(): int;

    public function setLevel(int $level): void;

    public function getName(): string;

    public function setName(string $name): void;

    public function getOrderPosition(): int;

    public function setOrderPosition(int $order): void;

    public function getRequired(): bool;

    public function setRequired(bool $required): void;
}
