<?php

declare(strict_types=1);
namespace U2\Entity\Traits;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Unit;

trait TaxComplianceFieldsTrait
{
    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: Unit::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected ?Unit $unit = null;

    #[Assert\Range(min: 1980, max: 2100)]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: false)]
    protected ?int $taxYear = null;

    #[Assert\Range(min: 1, max: 12)]
    #[ORM\Column(type: Types::INTEGER, length: 2, nullable: true)]
    protected ?int $taxMonth = null;

    public function getTaxYear(): ?int
    {
        return $this->taxYear;
    }

    public function setTaxYear(int $taxYear): void
    {
        $this->taxYear = $taxYear;
    }

    public function getTaxMonth(): ?int
    {
        return $this->taxMonth;
    }

    public function setTaxMonth(?int $taxMonth): void
    {
        $this->taxMonth = $taxMonth;
    }
}
