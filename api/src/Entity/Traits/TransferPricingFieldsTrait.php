<?php

declare(strict_types=1);
namespace U2\Entity\Traits;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\UnitHierarchy;
use U2\Unit\Hierarchy\Snapshot;

trait TransferPricingFieldsTrait
{
    /**
     * This is a so called virtual field to assist in setting and persisting $units via snapshots and lifecycle events.
     */
    private ?Snapshot $snapshot = null;

    #[Assert\NotBlank]
    #[ORM\ManyToOne(targetEntity: UnitHierarchy::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?UnitHierarchy $unitHierarchy = null;

    /**
     * This is set by the UnitHierarchyDefinitionListener to indicate that definitions
     * of the hierarchy selected for this document have changed. At this point in time
     * we only check for deletion and update of definitions, but not for new insertions.
     * When this flag is set it is necessary to make sure that units that may have been
     * selected in this document but are no longer part of the hierarchy are unselected.
     *
     * TODO: This creates problem in our logic of not updating entities when not changing anything on the forms.
     * We set this value to null when we create a new document, but on AbstractDocumentController::updateConfigurationAction
     * we set this value to false. This creates a change in the entityChangeSet of unitOfWork which breaks our logic. For
     * now we have changed the constructor to set this value to false on construct. This fixes only the case when we add new
     * entities. If we change a unit hierarchy, then this value will be true (see UnitHierarchyDefinitionListener::onFlush), and then on
     * updating without changing anything on the form, this value will become false and will break our logic. Consider replacing this field
     * with another table. Theoretically, the fact that the unit hierarchy has changed, should not affect the abstract documents.
     */
    #[Assert\Type(type: 'bool')]
    #[ORM\Column(type: Types::BOOLEAN, nullable: false, options: ['default' => false])]
    private bool $unitHierarchyDefinitionsChanged = false;

    public function getSnapshot(): ?Snapshot
    {
        return $this->snapshot;
    }

    public function setSnapshot(?Snapshot $snapshot): void
    {
        $this->snapshot = $snapshot;
    }

    public function getUnitHierarchy(): ?UnitHierarchy
    {
        return $this->unitHierarchy;
    }

    public function setUnitHierarchy(?UnitHierarchy $unitHierarchy): self
    {
        $this->unitHierarchy = $unitHierarchy;

        return $this;
    }

    public function getUnitHierarchyDefinitionsChanged(): bool
    {
        return $this->unitHierarchyDefinitionsChanged;
    }

    public function setUnitHierarchyDefinitionsChanged(bool $unitHierarchyDefinitionsChanged): void
    {
        $this->unitHierarchyDefinitionsChanged = $unitHierarchyDefinitionsChanged;
    }
}
