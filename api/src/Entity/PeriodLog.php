<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use JetBrains\PhpStorm\Pure;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\AuditLog\Change\Change;
use U2\AuditLog\Change\ChangesNormalizer;
use U2\AuditLog\LogEntry;
use U2\Entity\Interfaces\Entity;
use U2\Repository\PeriodLogRepository;
use U2\Security\UserRoles;
use U2\Util\DateTime;

#[ORM\Entity(repositoryClass: PeriodLogRepository::class)]
#[ORM\Table(name: 'period_audit')]
#[Get(
    routePrefix: '/audit-log',
    normalizationContext: ['groups' => ['period_log:read']],
    security: 'is_granted("' . UserRoles::PeriodManager->value . '")',
)]
#[GetCollection(
    uriTemplate: '/periods/{id}/logs',
    uriVariables: [
        'id' => new Link(
            fromProperty: 'logs',
            fromClass: Period::class
        ),
    ],
    status: Response::HTTP_OK,
    paginationMaximumItemsPerPage: 10,
    paginationClientEnabled: false,
    order: ['timestamp' => 'DESC'],
    normalizationContext: ['groups' => ['period_log:read']],
    security: 'is_granted("' . UserRoles::PeriodManager->value . '")'
)]
class PeriodLog implements Entity, LogEntry
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['period_log:read'])]
    private ?int $id = null;

    #[ORM\Column(name: 'audit_timestamp', type: Types::DATETIME_MUTABLE, nullable: false)]
    #[Groups(groups: ['period_log:read'])]
    private \DateTime $timestamp;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    #[Groups(groups: ['period_log:read'])]
    private ?string $username;

    /**
     * @param Change[] $changes
     */
    public function __construct(
        #[Groups(groups: ['period_log:read'])]
        #[ORM\Column(type: Types::JSON, nullable: false)]
        #[Assert\NotBlank]
        private readonly array $changes,

        #[Groups(groups: ['period_log:read'])]
        #[ORM\JoinColumn(onDelete: 'CASCADE')]
        #[ORM\ManyToOne(targetEntity: Period::class, inversedBy: 'logs')]
        private readonly Period $auditedEntity,

        #[Groups(groups: ['period_log:read'])]
        #[ORM\JoinColumn(name: 'audit_user_id', nullable: true, onDelete: 'SET NULL')]
        #[ORM\ManyToOne(targetEntity: User::class)]
        private readonly ?User $user = null,
    ) {
        $this->timestamp = DateTime::createNow();
        $this->username = $this->user?->getUsername();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getAuditedEntity(): Period
    {
        return $this->auditedEntity;
    }

    public function getTimestamp(): \DateTime
    {
        return $this->timestamp;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    /**
     * @return Change[]
     */
    public function getChanges(): array
    {
        return ChangesNormalizer::normalize($this->changes);
    }

    #[Pure]
    public function getUsername(): ?string
    {
        return $this->user?->getUsername() ?? $this->username;
    }
}
