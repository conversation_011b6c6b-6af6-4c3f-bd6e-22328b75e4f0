<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Context;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Api\Filter\SearchFilter;
use U2\AuditLog\Attribute\Audit;
use U2\AuditLog\AuditedEntity;
use U2\AuditLog\LogEntry;
use U2\AuditLog\LogValue;
use U2\Entity\Interfaces\Entity;
use U2\Repository\PeriodRepository;
use U2\Security\UserRoles;

#[Audit]
#[ORM\Entity(repositoryClass: PeriodRepository::class)]
#[ORM\Table(name: 'period')]
#[UniqueEntity(fields: ['name'], message: 'u2_core.period.name_not_unique')]
#[ApiFilter(
    filterClass: OrderFilter::class,
    properties: ['closed', 'endDate', 'name', 'previousPeriod.name', 'startDate', 'description'],
    arguments: ['orderParameterName' => 'sort']
)]
#[ApiFilter(
    filterClass: SearchFilter::class,
    properties: ['name' => 'ipartial', 'previousPeriod.name' => 'ipartial', 'description' => 'ipartial']
)]
#[ApiResource(
    operations: [
        new Get(),
        new Delete(security: 'is_granted("' . UserRoles::PeriodManager->value . '")'),
        new Patch(security: 'is_granted("' . UserRoles::PeriodManager->value . '")'),
        new GetCollection(),
        new Post(security: 'is_granted("' . UserRoles::PeriodManager->value . '")'),
    ],
    normalizationContext: ['groups' => ['period:read']],
    denormalizationContext: ['groups' => ['period:write']]
)]
class Period implements Entity, AuditedEntity, LogValue
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER, length: 8)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['period:read'])]
    private int $id;

    /**
     * @var Collection<int, PeriodLog>
     */
    #[ORM\OneToMany(mappedBy: 'auditedEntity', targetEntity: PeriodLog::class, orphanRemoval: true)]
    private Collection $logs;

    #[Gedmo\Blameable(on: 'create')]
    #[Groups(groups: ['period:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[Groups(groups: ['period:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $updatedBy = null;

    #[Gedmo\Timestampable(on: 'create')]
    #[Groups(groups: ['period:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTime $createdAt = null;

    #[Gedmo\Timestampable(on: 'update')]
    #[Groups(groups: ['period:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTime $updatedAt = null;

    public function __construct(
        #[Assert\NotBlank]
        #[Audit]
        #[ORM\Column(type: Types::STRING)]
        #[Groups(groups: ['period:read', 'period:write'])]
        private string $name,

        #[Assert\NotBlank]
        #[Audit]
        #[ORM\Column(type: Types::DATE_MUTABLE, nullable: false)]
        #[Groups(groups: ['period:read', 'period:write'])]
        #[Context(normalizationContext: [DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
        private \DateTime $startDate,

        #[Assert\NotBlank]
        #[Audit]
        #[ORM\Column(type: Types::DATE_MUTABLE, nullable: false)]
        #[Groups(groups: ['period:read', 'period:write'])]
        #[Context(normalizationContext: [DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
        private \DateTime $endDate,

        #[Audit]
        #[ORM\Column(type: Types::TEXT, nullable: true)]
        #[Groups(groups: ['period:read', 'period:write'])]
        private ?string $description = null,

        #[Audit]
        #[ORM\ManyToOne(targetEntity: self::class)]
        #[ORM\JoinColumn(nullable: true)]
        #[Groups(groups: ['period:read', 'period:write'])]
        #[ApiProperty(readableLink: false)]
        private ?self $previousPeriod = null,

        #[Assert\Type(type: 'bool')]
        #[Audit]
        #[ORM\Column(type: Types::BOOLEAN)]
        #[Groups(groups: ['period:read', 'period:write'])]
        private bool $closed = false,
    ) {
        $this->logs = new ArrayCollection();
    }

    public function getAuditLogValue(): string
    {
        return $this->name;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getStartDate(): \DateTime
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTime $startDate): void
    {
        // Compare timestamps since setting the same date again triggers a revision even if the old and new value are the same
        // See: https://universalunits.atlassian.net/browse/UU-6061
        if ($this->startDate->getTimestamp() === $startDate->getTimestamp()) {
            return;
        }

        $this->startDate = $startDate;
    }

    public function getEndDate(): \DateTime
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTime $endDate): void
    {
        // Compare timestamps since setting the same date again triggers a revision even if the old and new value are the same
        // See: https://universalunits.atlassian.net/browse/UU-6061
        if ($this->endDate->getTimestamp() === $endDate->getTimestamp()) {
            return;
        }

        $this->endDate = $endDate;
    }

    public function getPreviousPeriod(): ?self
    {
        return $this->previousPeriod;
    }

    public function setPreviousPeriod(?self $previousPeriod): void
    {
        $this->previousPeriod = $previousPeriod;
    }

    #[Assert\Callback]
    public function validateStartsBeforeItEnds(ExecutionContextInterface $context): void
    {
        if ($this->getStartDate() >= $this->getEndDate()) {
            $context
                ->buildViolation('u2_core.start_date_needs_to_be_before_end_date')
                ->atPath('startDate')
                ->addViolation();
        }
    }

    #[Assert\Callback]
    public function validatePreviousPeriodIsNotThisPeriod(ExecutionContextInterface $context): void
    {
        if (isset($this->id) && null !== $this->getPreviousPeriod() && $this->getId() === $this->getPreviousPeriod()->getId()) {
            $context
                ->buildViolation('u2.period.period_cannot_be_its_previous_period')
                ->atPath('previousPeriod')
                ->addViolation();
        }
    }

    public function isClosed(): bool
    {
        return $this->closed;
    }

    public function setClosed(bool $closed): void
    {
        $this->closed = $closed;
    }

    /**
     * @return Collection<int,PeriodLog>
     */
    public function getLogs(): Collection
    {
        return $this->logs;
    }

    public function addLog(LogEntry $logEntry): void
    {
        \assert($logEntry instanceof PeriodLog);

        if (!$this->logs->contains($logEntry)) {
            $this->logs[] = $logEntry;
        }
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }

    public function setCreatedAt(?\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }
}
