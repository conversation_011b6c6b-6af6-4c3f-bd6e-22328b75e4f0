<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use ApiPlatform\Metadata\QueryParameter;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Api\Filter\SearchFilter;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Interfaces\Periodable;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Money\ExchangeRate\ExchangeRateTypes;
use U2\Repository\ExchangeRateRepository;
use U2\Security\UserRoles;
use U2\Validator as U2Assert;

#[U2Assert\NoChangeIfPeriodIsClosed]
#[U2Assert\ExchangeRateValueIsOneIfInputAndOutputCurrencyAreEqual(
    inputCurrencyFieldName: 'inputCurrency',
    outputCurrencyFieldName: 'outputCurrency',
    exchangeRateValueFieldName: 'directRate'
)]
#[UniqueEntity(fields: ['exchangeRateType', 'period', 'inputCurrency', 'outputCurrency'])]
#[ORM\Entity(repositoryClass: ExchangeRateRepository::class)]
#[ORM\Table(name: 'exchange_rate')]
#[ORM\UniqueConstraint(name: 'unique_row_exchange_rate', columns: ['exchange_rate_type', 'period_id', 'input_currency_id', 'output_currency_id'])]
#[ReadableName(value: 'Exchange Rate', translationDomain: false)]
#[ShortName(value: 'exchange-rate')]
#[ApiFilter(
    filterClass: OrderFilter::class,
    properties: ['inputCurrency.iso4217code', 'outputCurrency.iso4217code', 'directRate', 'exchangeRateType'],
    arguments: ['orderParameterName' => 'sort']
)]
#[ApiFilter(
    filterClass: SearchFilter::class,
    properties: ['inputCurrency.iso4217code', 'outputCurrency.iso4217code']
)]
#[ApiResource(
    operations: [
        new Get(),
        new Patch(),
        new Delete(),
        new GetCollection(
            parameters: [
                'periodId' => new QueryParameter(schema: ['type' => 'integer'], description: 'Parent Period ID', required: true),
            ],
        ),
        new Post(),
    ],
    normalizationContext: ['groups' => ['exchange_rate:read']],
    denormalizationContext: ['groups' => ['exchange_rate:write']],
    security: 'is_granted("' . UserRoles::PeriodManager->value . '")',
)]
class ExchangeRate implements Periodable, Entity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['exchange_rate:read'])]
    private ?int $id = null;

    #[Assert\Choice(choices: [ExchangeRateTypes::AVERAGE, ExchangeRateTypes::CURRENT])]
    #[ORM\Column(type: Types::INTEGER, nullable: false)]
    #[Groups(groups: ['exchange_rate:read', 'exchange_rate:write'])]
    private ?int $exchangeRateType = null;

    #[ORM\ManyToOne(targetEntity: Period::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(groups: ['exchange_rate:read', 'exchange_rate:write'])]
    private ?Period $period = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(groups: ['exchange_rate:read', 'exchange_rate:write'])]
    private ?Currency $inputCurrency = null;

    #[ORM\ManyToOne(targetEntity: Currency::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(groups: ['exchange_rate:read', 'exchange_rate:write'])]
    private ?Currency $outputCurrency = null;

    /**
     * @var numeric-string
     */
    #[Assert\Range(min: '0.0000000001', max: '9999999999')]
    #[Assert\NotBlank]
    #[ORM\Column(name: 'value', type: Types::DECIMAL, precision: 20, scale: 10, nullable: false)]
    #[Groups(groups: ['exchange_rate:read', 'exchange_rate:write'])]
    private string $directRate;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Gedmo\Blameable(on: 'create')]
    #[Groups(groups: ['exchange_rate:read'])]
    protected ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[Groups(groups: ['exchange_rate:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    protected ?User $updatedBy = null;

    #[Gedmo\Timestampable(on: 'create')]
    #[Groups(groups: ['exchange_rate:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    protected ?\DateTime $createdAt = null;

    #[Gedmo\Timestampable(on: 'update')]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(groups: ['exchange_rate:read'])]
    protected ?\DateTime $updatedAt = null;

    public function setIndirectRate(string $directRate): void
    {
        \assert(is_numeric($directRate));

        if (0.0 !== (float) $directRate) {
            $directRate = bcdiv('1', $directRate, 10);
        }

        $this->directRate = $directRate;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return numeric-string
     */
    public function getDirectRate(): string
    {
        return $this->directRate;
    }

    /**
     * @param numeric-string $directRate
     */
    public function setDirectRate(string $directRate): void
    {
        $this->directRate = $directRate;
    }

    public function getExchangeRateType(): ?int
    {
        return $this->exchangeRateType;
    }

    public function setExchangeRateType(int $exchangeRateType): void
    {
        $this->exchangeRateType = $exchangeRateType;
    }

    public function getPeriod(): ?Period
    {
        return $this->period;
    }

    public function setPeriod(?Period $period): void
    {
        $this->period = $period;
    }

    public function getInputCurrency(): ?Currency
    {
        return $this->inputCurrency;
    }

    public function setInputCurrency(?Currency $inputCurrency): void
    {
        $this->inputCurrency = $inputCurrency;
    }

    public function getOutputCurrency(): ?Currency
    {
        return $this->outputCurrency;
    }

    public function setOutputCurrency(?Currency $outputCurrency): void
    {
        $this->outputCurrency = $outputCurrency;
    }

    #[Assert\Callback]
    public function validateExchangeRate(ExecutionContextInterface $context): void
    {
        if (false === \in_array($this->exchangeRateType, ExchangeRateTypes::all(), true)) {
            $context
                ->buildViolation('u2.exchange_rate_type.given_exchange_rate_type_not_supported')
                ->atPath('exchangeRateType')
                ->setParameters(
                    [
                        '%exchange_rate_type%' => $this->exchangeRateType ? (string) $this->exchangeRateType : 'null',
                        '%exchange_rate_types%' => implode(', ', array_unique(ExchangeRateTypes::all())),
                    ]
                )
                ->addViolation();
        }
    }

    public function setCreatedAt(?\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }
}
