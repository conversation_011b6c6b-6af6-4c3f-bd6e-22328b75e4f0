<?php

declare(strict_types=1);
namespace U2\Entity\Workflow\Condition;

use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Link;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Workflow\Transition;
use U2\Repository\ConditionRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Workflow\Condition\CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType;

#[ORM\Entity(repositoryClass: ConditionRepository::class)]
#[Get(
    uriTemplate: '/transitions/{transitionId}/conditions/{id}',
    uriVariables: [
        'transitionId' => new Link(fromProperty: 'conditions', fromClass: Transition::class),
        'id' => new Link(fromClass: self::class),
    ],
    openapi: false,
    normalizationContext: ['groups' => ['workflow_transition_condition:read']],
    security: 'is_granted("' . VoterAttributes::write . '", object.getTransition())'
)]
class CurrentUserIsAuthorisedToUnitAndOrPartnerUnitCondition extends Condition implements Entity
{
    public const int AUTHORISED_UNIT = 1;
    public const int AUTHORISED_PARTNER_UNIT = 2;

    public function __construct(
        Transition $transition,
        #[ORM\Column(type: Types::SMALLINT)]
        private int $requiresAuthorizationTo = self::AUTHORISED_UNIT,
    ) {
        parent::__construct($transition);
    }

    public static function options(): array
    {
        return [
            'Unit' => self::AUTHORISED_UNIT,
            'Partner Unit' => self::AUTHORISED_PARTNER_UNIT,
            'Either Unit or Partner Unit' => self::AUTHORISED_UNIT | self::AUTHORISED_PARTNER_UNIT,
        ];
    }

    public function getDescription(): string
    {
        return match ($this->requiresAuthorizationTo) {
            self::AUTHORISED_UNIT => 'The current user must be authorised to the unit',
            self::AUTHORISED_PARTNER_UNIT => 'The current user must be authorised to the partner unit',
            self::AUTHORISED_UNIT | self::AUTHORISED_PARTNER_UNIT => 'The current user must be authorised to either the unit or the partner unit',
            default => 'Unknown condition',
        };
    }

    public function canBeAuthorizedToUnit(): bool
    {
        return self::AUTHORISED_UNIT === (self::AUTHORISED_UNIT & $this->requiresAuthorizationTo);
    }

    public function canBeAuthorizedToPartnerUnit(): bool
    {
        return self::AUTHORISED_PARTNER_UNIT === (self::AUTHORISED_PARTNER_UNIT & $this->requiresAuthorizationTo);
    }

    public static function getType(): string
    {
        return CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType::type;
    }

    public static function getName(): string
    {
        return CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType::getName();
    }

    public static function getHelp(): string
    {
        return CurrentUserIsAuthorisedToUnitAndOrPartnerUnitConditionType::getHelp();
    }

    public function getRequiresAuthorizationTo(): int
    {
        return $this->requiresAuthorizationTo;
    }

    public function setRequiresAuthorizationTo(int $requiresAuthorizationTo): void
    {
        $this->requiresAuthorizationTo = $requiresAuthorizationTo;
    }

    /**
     * @return array<mixed>
     */
    #[Groups(groups: ['workflow_transition:read', 'workflow_transition_condition:read'])]
    public function getParameters(): array
    {
        return [
            'requiresAuthorizationTo' => $this->requiresAuthorizationTo,
        ];
    }

    /**
     * @param array<mixed> $parameters
     */
    public function setParameters(array $parameters): void
    {
        \assert(\array_key_exists('requiresAuthorizationTo', $parameters));
        $this->requiresAuthorizationTo = $parameters['requiresAuthorizationTo'];
    }
}
