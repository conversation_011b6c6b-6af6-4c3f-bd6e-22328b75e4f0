<?php

declare(strict_types=1);
namespace U2\Entity\Workflow\Condition;

use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Link;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use U2\Entity\Interfaces\Entity;
use U2\Entity\User;
use U2\Entity\Workflow\Transition;
use U2\Repository\ConditionRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Workflow\Condition\CurrentUserIsUserConditionType;

#[ORM\Entity(repositoryClass: ConditionRepository::class)]
#[Get(
    uriTemplate: '/transitions/{transitionId}/conditions/{id}',
    uriVariables: [
        'transitionId' => new Link(fromProperty: 'conditions', fromClass: Transition::class),
        'id' => new Link(fromClass: self::class),
    ],
    openapi: false,
    normalizationContext: ['groups' => ['workflow_transition_condition:read']],
    security: 'is_granted("' . VoterAttributes::write . '", object.getTransition())'
)]
class CurrentUserIsUserCondition extends Condition implements Entity
{
    /**
     * Array of user IDs that will validate as allowed.
     *
     * @var Collection<int, User>
     */
    #[ORM\ManyToMany(targetEntity: User::class)]
    private Collection $users;

    public function __construct(
        Transition $transition,
    ) {
        parent::__construct($transition);
        $this->users = new ArrayCollection();
    }

    public function getDescription(): string
    {
        $userNames = [];
        foreach ($this->getUsers() as $user) {
            $userNames[] = $user->getUserIdentifier();
        }

        return 'User must be one of the following users: ' . implode(', ', $userNames);
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    /**
     * @param Collection|User[] $allowedUsers
     */
    public function setUsers(Collection $allowedUsers): void
    {
        $this->users = $allowedUsers;
    }

    public static function getType(): string
    {
        return CurrentUserIsUserConditionType::type;
    }

    public static function getName(): string
    {
        return CurrentUserIsUserConditionType::getName();
    }

    public static function getHelp(): string
    {
        return CurrentUserIsUserConditionType::getHelp();
    }

    /**
     * @return array<mixed>
     */
    public function getParameters(): array
    {
        /** @var array<int,User> $users */
        $users = $this->users->getValues();

        return [
            'users' => array_map(static fn (User $user): ?int => $user->getId(), $users),
        ];
    }

    /**
     * @param array<mixed> $parameters
     */
    public function setParameters(array $parameters): void
    {
        /** @var array<int, User> $users */
        $users = $parameters['users'];
        $this->users = new ArrayCollection($users);
    }
}
