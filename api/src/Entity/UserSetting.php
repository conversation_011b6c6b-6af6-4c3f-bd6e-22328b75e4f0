<?php

declare(strict_types=1);
namespace U2\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use U2\Entity\Interfaces\Entity;

#[UniqueEntity(['name', 'user'])]
#[ORM\Entity]
#[ORM\Table(name: 'user_setting')]
class UserSetting implements Entity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    public function __construct(
        #[ORM\Column(type: Types::STRING, nullable: false)]
        private string $name,

        #[ORM\Column(type: Types::STRING, nullable: true)]
        private ?string $value,

        /**
         * TODO: Find a way to use this as a readonly properties again.
         *
         * This is only a temporary fix. We should use readonly properties again in the future when possible.
         * At the moment it causes issues with doctrine proxies and the support of readonly properties.
         *
         * See: https://universalunits.atlassian.net/browse/UU-6496.
         */
        #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'settings')]
        #[ORM\JoinColumn(nullable: false)]
        private User $user,
    ) {
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): void
    {
        $this->value = $value;
    }

    public function getUser(): User
    {
        return $this->user;
    }
}
