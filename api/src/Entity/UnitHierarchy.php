<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Api\Filter\SearchFilter;
use U2\Entity\Interfaces\Entity;
use U2\Repository\UnitHierarchyRepository;
use U2\Security\UserRoles;

#[UniqueEntity('name')]
#[ORM\Entity(repositoryClass: UnitHierarchyRepository::class)]
#[ORM\Table(name: 'unit_hierarchy')]
#[ApiFilter(
    filterClass: OrderFilter::class,
    properties: ['name', 'description'],
    arguments: ['orderParameterName' => 'sort']
)]
#[ApiFilter(
    filterClass: SearchFilter::class,
    properties: ['name' => 'ipartial', 'description' => 'ipartial']
)]
#[ApiResource(
    operations: [
        new Get(),
        new GetCollection(),
        new Post(
            security: 'is_granted("' . UserRoles::UnitManager->value . '")'
        ),
        new Patch(
            security: 'is_granted("' . UserRoles::UnitManager->value . '")'
        ),
        new Delete(
            security: 'is_granted("' . UserRoles::UnitManager->value . '")'
        ),
    ],
    normalizationContext: ['groups' => ['unit-hierarchy:read']],
    denormalizationContext: ['groups' => ['unit-hierarchy:write']],
)]
class UnitHierarchy implements Entity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['unit-hierarchy:read'])]
    private ?int $id = null;

    /**
     * @var Collection<int, UnitHierarchyDefinition>
     */
    #[ORM\OneToMany(mappedBy: 'unitHierarchy', targetEntity: UnitHierarchyDefinition::class)]
    private Collection $definitions;

    public function __construct(
        #[Assert\Length(max: 255)]
        #[Assert\NotBlank]
        #[ORM\Column(length: 255, unique: true, nullable: false)]
        #[Groups(groups: ['unit-hierarchy:read', 'unit-hierarchy:write'])]
        private ?string $name = null,

        #[ORM\Column(type: Types::TEXT, nullable: true)]
        #[Groups(groups: ['unit-hierarchy:read', 'unit-hierarchy:write'])]
        private ?string $description = null,
    ) {
        $this->definitions = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function addDefinition(UnitHierarchyDefinition $definitions): void
    {
        $this->definitions[] = $definitions;
    }

    public function removeDefinition(UnitHierarchyDefinition $definitions): void
    {
        $this->definitions->removeElement($definitions);
    }

    /**
     * @return Collection<int, UnitHierarchyDefinition>
     */
    public function getDefinitions(): Collection
    {
        return $this->definitions;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }
}
