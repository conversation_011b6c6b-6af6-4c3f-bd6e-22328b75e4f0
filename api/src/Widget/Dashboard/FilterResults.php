<?php

declare(strict_types=1);
namespace U2\Widget\Dashboard;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\DataSourcery\Query\Sort;
use U2\DataSourcery\Query\SortCondition;
use U2\Entity\SavedFilter as SavedFilterEntity;
use U2\Entity\Task\TaskType;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Exception\InvalidArgumentValueWidgetException;
use U2\Exception\MissingArgumentWidgetException;
use U2\Repository\SavedFilterRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Table\Query\RequestQueryGenerator;
use U2\Table\SavedFilter\RoutingHelper;

class FilterResults extends AbstractDashboardWidget
{
    public const int MAX_NUMBER_OF_COLUMNS = 6;

    public const int MAX_NUMBER_OF_RECORDS_PER_PAGE = 10;

    public const int MIN_NUMBER_OF_RECORDS_PER_PAGE = 5;

    /**
     * @var array<string>
     */
    private array $columns = [];

    private int $recordsPerPage = self::MIN_NUMBER_OF_RECORDS_PER_PAGE;

    private string $sort = 'Id ' . SortCondition::ASC;

    /**
     * TODO: Find out if this is used and either fix phpstan or remove it.
     *
     * @phpstan-ignore-next-line property.unusedType
     */
    private ?string $title = null;

    private ?int $filterId = null;

    public function __construct(
        private readonly RoutingHelper $routingHelper,
        private readonly SavedFilterRepository $repository,
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly FilterResultsTableFactory $factory,
    ) {
    }

    public function getParameters(): array
    {
        return [
            'columns' => $this->columns,
            'records_per_page' => $this->recordsPerPage,
            'sort' => $this->sort,
            'title' => $this->title,
            'filter_id' => $this->filterId,
        ];
    }

    public function setParameters(array $parameters): void
    {
        if (\array_key_exists('columns', $parameters)) {
            /** @var array<string> $columns */
            $columns = $parameters['columns'];
            $this->columns = $columns;
        }

        if (\array_key_exists('records_per_page', $parameters)) {
            /** @var int $recordsPerPage */
            $recordsPerPage = $parameters['records_per_page'];
            $this->recordsPerPage = $recordsPerPage;
        }

        if (\array_key_exists('sort', $parameters)) {
            /** @var string $sort */
            $sort = $parameters['sort'];
            $this->sort = $sort;
        }

        if (\array_key_exists('filter_id', $parameters)) {
            /** @var int $filterId */
            $filterId = $parameters['filter_id'];
            $this->filterId = $filterId;
        }
    }

    public function getName(): string
    {
        return 'filter-results';
    }

    public function isConfigurable(): bool
    {
        return false;
    }

    public function getColumns(): array
    {
        $columns = $this->columns;
        array_unshift($columns, 'Id');

        $columns = array_unique($columns);
        if (\count($columns) > self::MAX_NUMBER_OF_COLUMNS) {
            $columns = \array_slice($columns, 0, self::MAX_NUMBER_OF_COLUMNS);
        }

        return $columns;
    }

    public function getSort(): Sort
    {
        $sortData = explode(' ', $this->sort);
        $direction = $sortData[1] ?? SortCondition::ASC;

        return new Sort([new SortCondition($sortData[0], $direction)]);
    }

    public function getRecordsPerPageLimit(): int
    {
        $recordsPerPageLimit = $this->recordsPerPage;
        if ($recordsPerPageLimit > self::MAX_NUMBER_OF_RECORDS_PER_PAGE) {
            return self::MAX_NUMBER_OF_RECORDS_PER_PAGE;
        }

        if ($recordsPerPageLimit < self::MIN_NUMBER_OF_RECORDS_PER_PAGE) {
            return self::MIN_NUMBER_OF_RECORDS_PER_PAGE;
        }

        return $recordsPerPageLimit;
    }

    /**
     * @param array{page: int} $options
     *
     * @throws MissingArgumentWidgetException
     * @throws InsufficientPermissionsWidgetException
     * @throws InvalidArgumentValueWidgetException
     *
     * @return array<string,mixed>
     */
    public function getData(array $options = ['page' => 1]): array
    {
        $savedFilter = $this->getSavedFilter();

        $table = $this->factory->create($this, $savedFilter);
        if (!$this->authorizationChecker->isGranted(VoterAttributes::read, $table)) {
            throw new InsufficientPermissionsWidgetException($this);
        }

        /** @var class-string<TaskType> $entityClass */
        $entityClass = $table->getDataSource()->getEntityClass();

        $data = [];
        $data['taskType'] = $entityClass::getTaskType();

        $data['listPath'] = $this->routingHelper->generateUrlToSavedFilterTable($savedFilter, false, [
            RequestQueryGenerator::QUERY_PARAMETER_SELECT => implode(',', $this->columns),
            RequestQueryGenerator::QUERY_PARAMETER_SORT => str_replace(' ', ':', $this->sort),
        ]);

        $data['title'] = $this->title ?? $savedFilter->getName();

        $state = $table->getState();
        $query = $state->getQuery();
        $state->setQuery($query->withPagination(
            $query->getPagination()->withPage($options['page'] - 1),
        ));

        $table->setStatus($state);

        $tableView = $table->getTwigView();
        $data['data'] = [
            'name' => $tableView->getName(),
            'state' => $tableView->getState(),
            'fields' => $tableView->getDataSourceFields(),
            'functions' => $tableView->getExtensions()->getFunctions(),
            'columns' => $tableView->getConfiguration()->getColumnDefinitions()->all(),
            'config' => [
                'hasData' => (\count($tableView->vars['data']) > 0),
                'pagination' => $tableView->getPagination(),
                'paginationEnabled' => $tableView->isPaginationEnabled(),
                'selectionKey' => $tableView->getSelectionKey(),
                'tableName' => $tableView->getName(),
            ],
            'records' => $table->getData(),
            'totalItems' => $table->getRecordCount(),
        ];

        return $data;
    }

    /**
     * @throws InvalidArgumentValueWidgetException
     * @throws MissingArgumentWidgetException
     */
    private function getSavedFilter(): SavedFilterEntity
    {
        if (null === $this->filterId) {
            throw new MissingArgumentWidgetException($this, 'filter_id');
        }

        $savedFilter = $this->repository->find($this->filterId);
        if (!$savedFilter instanceof SavedFilterEntity) {
            throw new InvalidArgumentValueWidgetException($this, 'filter_id');
        }

        return $savedFilter;
    }
}
