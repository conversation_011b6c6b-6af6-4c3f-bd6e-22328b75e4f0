<?php

declare(strict_types=1);
namespace U2\Widget\Dashboard;

use Psr\Cache\CacheItemPoolInterface;
use U2\FileSystem\TenantFilesystemOperator;
use U2\Util\StringManipulator;

class HtmlWidgetStorage
{
    public const string dashboardSubdirectory = 'dashboard';
    public const string dashboardWidgetSubdirectory = self::dashboardSubdirectory . \DIRECTORY_SEPARATOR . 'widget';

    public function __construct(
        private readonly TenantFilesystemOperator $tenantFilesystem,
        private readonly CacheItemPoolInterface $tenantCache,
    ) {
    }

    public function store(HtmlWidgetContent $htmlWidgetContent): void
    {
        $id = $htmlWidgetContent->getId();
        $fileName = $this->getFileName($id);

        $content = $htmlWidgetContent->getContent();
        \assert(null !== $content);

        $this->tenantFilesystem->write($fileName, $content);
        $item = $this->tenantCache->getItem($this->getKey($id));
        $item->set($content);
        $this->tenantCache->save($item);
    }

    public function get(string $id): HtmlWidgetContent
    {
        $item = $this->tenantCache->getItem($this->getKey($id));
        if ($item->isHit()) {
            $content = $item->get();
        } else {
            $fileName = $this->getFileName($id);
            $content = $this->getContentFromFile($fileName);

            $item->set($content);
            $this->tenantCache->save($item);
        }

        \assert(\is_string($content));

        return new HtmlWidgetContent($id, $content);
    }

    public function remove(HtmlWidgetContent $htmlWidgetContent): void
    {
        $id = $htmlWidgetContent->getId();
        $this->tenantCache->deleteItem($this->getKey($id));
        $fileName = $this->getFileName($id);

        if ($this->tenantFilesystem->fileExists($fileName)) {
            $this->tenantFilesystem->delete($fileName);
        }
    }

    private function getKey(string $id): string
    {
        return 'htmldashboardwidget-' . md5($id);
    }

    private function getContentFromFile(string $fileName): string
    {
        if ($this->tenantFilesystem->fileExists($fileName)) {
            return trim($this->tenantFilesystem->read($fileName));
        }

        return '';
    }

    private function getFileName(string $id): string
    {
        $name = StringManipulator::lowerStringAndReplaceSpaceWithUnderscore($id);

        return self::dashboardWidgetSubdirectory . \DIRECTORY_SEPARATOR . $name . '.html';
    }
}
