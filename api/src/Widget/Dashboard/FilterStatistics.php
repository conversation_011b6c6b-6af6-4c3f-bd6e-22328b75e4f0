<?php

declare(strict_types=1);
namespace U2\Widget\Dashboard;

use U2\DataSourcery\Query\Pagination;
use U2\DataSourcery\Query\Query;
use U2\DataSourcery\Query\Sort;
use U2\DataSourcery\Query\SortCondition;
use U2\Entity\SavedFilter;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Exception\InvalidArgumentValueWidgetException;
use U2\Exception\MissingArgumentWidgetException;
use U2\Repository\SavedFilterRepository;
use U2\Table\SavedFilter\RoutingHelper;
use U2\Table\TableFactory;

class FilterStatistics extends AbstractDashboardWidget
{
    /**
     * @var array<int, FilterStatisticEntry>
     */
    private array $entries = [];
    private ?string $title = null;
    private int $chartSize = 1;

    public function __construct(
        private readonly SavedFilterRepository $repository,
        private readonly RoutingHelper $routingHelper,
        private readonly TableFactory $tableFactory,
    ) {
    }

    public function getParameters(): array
    {
        return [
            'entries' => $this->entries,
            'title' => $this->title,
            'chartSize' => $this->chartSize,
        ];
    }

    public function setParameters(array $parameters): void
    {
        if (\array_key_exists('entries', $parameters)) {
            /** @var array<int, array{ savedFilterId: int, label?: string, color: string }> $entries */
            $entries = $parameters['entries'];
            foreach ($entries as $entry) {
                $this->entries[] = new FilterStatisticEntry(
                    savedFilterId: $entry['savedFilterId'],
                    label: \array_key_exists('label', $entry) ? $entry['label'] : '',
                    color: $entry['color'],
                );
            }
        }

        if (\array_key_exists('chartSize', $parameters)) {
            /** @var int $recordsPerPage */
            $recordsPerPage = $parameters['chartSize'];
            $this->chartSize = $recordsPerPage;
        }

        if (\array_key_exists('title', $parameters)) {
            /** @var string $title */
            $title = $parameters['title'];
            $this->title = $title;
        }
    }

    public function getName(): string
    {
        return 'filter-statistics';
    }

    public function isConfigurable(): bool
    {
        return true;
    }

    /**
     * @throws MissingArgumentWidgetException
     * @throws InsufficientPermissionsWidgetException
     * @throws InvalidArgumentValueWidgetException
     */
    public function getData(): array
    {
        $savedFilters = $this->repository->findBy(['id' => array_map(fn (FilterStatisticEntry $entry): int => $entry->savedFilterId, $this->entries)]);

        return [
            'data' => array_map(function (FilterStatisticEntry $entry) use ($savedFilters): array {
                $savedFilter = current(array_filter($savedFilters, fn (SavedFilter $savedFilter): bool => $savedFilter->getId() === $entry->savedFilterId));
                if (false === $savedFilter) {
                    throw new InvalidArgumentValueWidgetException($this, 'savedFilterId');
                }

                $table = $this->tableFactory->createFromSavedFilter($savedFilter);
                $table
                    ->getState()
                    ->setQuery(
                        new Query(
                            [],
                            new Sort([new SortCondition('Id', 'ASC')]),
                            $table->getState()->getQuery()->getFilter(),
                            new Pagination(0, 0)
                        )
                    );

                return [
                    'label' => '' === $entry->label ? $savedFilter->getName() : $entry->label,
                    'color' => $entry->color,
                    'savedFilterId' => $entry->savedFilterId,
                    'result' => $table->getRecordCount(),
                    'listPath' => $this->routingHelper->generateUrlToSavedFilterTable($savedFilter),
                ];
            }, $this->entries),
        ];
    }
}
