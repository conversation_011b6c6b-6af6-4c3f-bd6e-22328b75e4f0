<?php

declare(strict_types=1);
namespace U2\Widget\Document;

use Twig\Environment;

class PlaceholderRenderer
{
    public const string placeholderTemplate = 'widget/document/placeholder/default.widget_placeholder.html.twig';

    public function __construct(
        private readonly Environment $templatingEngine,
        private readonly Encoder $encoder,
    ) {
    }

    /**
     * @param array<string, mixed> $options
     */
    public function renderContent(DocumentWidgetInterface $widget, array $options): string
    {
        if (!\array_key_exists('editable', $options)) {
            $options['editable'] = true;
        }

        if (!\array_key_exists('section_context', $options)) {
            $options['section_context'] = null;
        }

        return $this->templatingEngine->render(
            self::placeholderTemplate,
            array_merge(
                $options,
                [
                    'widget' => $widget,
                    'encoded_widget' => $this->encoder->encode($widget),
                ]
            )
        );
    }
}
