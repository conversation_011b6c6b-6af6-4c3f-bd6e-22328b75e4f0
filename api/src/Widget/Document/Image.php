<?php

declare(strict_types=1);
namespace U2\Widget\Document;

use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Twig\Environment;
use U2\Api\Property\AttachmentLinks;
use U2\Entity\DocumentSection;
use U2\Entity\StructuredDocumentInterface;
use U2\EntityMetadata\MetadataProvider;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Exception\InvalidArgumentValueWidgetException;
use U2\File\Attachment;
use U2\File\Helper;
use U2\FileSystem\TenantFilesystemOperator;
use U2\Security\Voter\VoterAttributes;

class Image extends AbstractDocumentWidget
{
    public const array IMAGE_MIME_TYPES = [
        'image/jpeg',
        'image/pjpeg',
        'image/png',
    ];

    private ?int $id = null;

    private string $width = 'auto';

    public function __construct(
        Environment $templatingEngine,
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly MetadataProvider $metadataProvider,
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly TenantFilesystemOperator $tenantFilesystem,
    ) {
        parent::__construct($templatingEngine);
    }

    public function getParameters(): array
    {
        return [
            'id' => $this->id,
            'width' => $this->width,
        ];
    }

    public function setParameters(array $parameters): void
    {
        if (\array_key_exists('id', $parameters)) {
            /** @var string $id */
            $id = $parameters['id'];
            $this->id = (int) $id;
        }
        if (\array_key_exists('width', $parameters)) {
            /** @var string $width */
            $width = $parameters['width'];
            $this->width = $width;
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getWidth(): string
    {
        return $this->width;
    }

    public function getName(): string
    {
        return 'image';
    }

    public function isConfigurable(): bool
    {
        return true;
    }

    public function supportsDocument(StructuredDocumentInterface $document): bool
    {
        return true;
    }

    protected function getTemplate(): string
    {
        return 'widget/document/image.widget.html.twig';
    }

    protected function getData(array $options): array
    {
        $section = $this->getDocumentSection($options);
        if (!$this->userHasPermissions($section)) {
            throw new InsufficientPermissionsWidgetException($this);
        }

        /** @var string $format */
        $format = $options['format'];
        $fileSrc = $this->getFileSrc($section, $format);

        return [
            'file_src' => $fileSrc,
            'width' => $this->width,
        ];
    }

    private function userHasPermissions(DocumentSection $section): bool
    {
        $files = $section->getFiles();

        $fileId = $this->id;
        if (!isset($files[$fileId])) {
            throw new InvalidArgumentValueWidgetException($this, 'file');
        }

        $attachment = new Attachment($files[$fileId], $section, $files[$fileId]->getName(), new AttachmentLinks(null, null, null));

        return $this->authorizationChecker->isGranted(VoterAttributes::read, $attachment);
    }

    private function getFileSrc(DocumentSection $section, string $format): string
    {
        $file = $section->getFiles()[$this->id];
        \assert(null !== $file);

        if ('pdf' === $format) {
            $content = $this->tenantFilesystem->read(Helper::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . $file->getPath());

            return '@' . base64_encode($content);
        }

        return $this->urlGenerator->generate(
            'u2_attachment_view',
            [
                'shortName' => $this->metadataProvider->getShortName($section::class),
                'entityId' => $section->getId(),
                'id' => $file->getId(),
            ]
        );
    }
}
