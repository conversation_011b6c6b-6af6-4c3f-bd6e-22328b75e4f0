<?php

declare(strict_types=1);
namespace U2\AuditLog;

use Doctrine\Common\Util\ClassUtils;
use U2\Entity\Unit;
use U2\Entity\UnitLog;

class Namer
{
    private const string LOG_ENTRY_CLASS_SUFFIX = 'Log';

    /**
     * @param class-string $className
     *
     * @return class-string
     */
    public static function generateLogEntryNameFromAuditedClassName(string $className): string
    {
        /**
         * TODO: Do not generate the log class name
         * Generating the class name is not really safe and can get really complex as you can see below.
         * We need to find a solution that is easy to maintain and preferably not rely on third party code
         * in order to reduce technical debt. A solution could be to use php attributes.
         */
        $realClass = ClassUtils::getRealClass($className);
        if (is_a($realClass, Unit::class, true)) {
            return UnitLog::class;
        }

        $logClass = $realClass . self::LOG_ENTRY_CLASS_SUFFIX;
        \assert(class_exists($logClass));

        return $logClass;
    }
}
