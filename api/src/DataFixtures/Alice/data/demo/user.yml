U2\Entity\User:

  user_admin:
    __factory:
      '@U2\DataFixtures\Example\UserFactory::getObject':
        - username: "admin"
          contact: "@contact_admin"
          userRoles:
            - "ROLE_ADMIN"

  test_user (template):
    __factory: { '@U2\DataFixtures\Example\UserFactory::getObject': [ { password: "init12345" } ] }
    groups:
      - "@group_test_users"
    units: "<numberBetween(5, 50)>x @unit_*"
    contact.nameLast: 'Mustermann'
    contact.company: 'Universal Units GmbH'
    contact.country: '@country_*'
    contact.email: '<EMAIL>'
    contact.telephone: '+49 30 3101 41 90'
    contact.fax: '+49 30 3101 41 99'

  user_cm (extends test_user):
    contact.nameFirst: "Contract"
    username: "cm.mustermann"
    authorizationProfiles:
      - "@authorization_profile_contract_full_access"

  user_dtm (extends test_user):
    contact.nameFirst: "Dtm"
    username: "dtm.mustermann"
    authorizationProfiles:
      - "@authorization_profile_datasheet_monitor_full_access"

  user_tam (extends test_user):
    contact.nameFirst: "<PERSON>"
    username: "tam.mustermann"
    authorizationProfiles:
      - "@authorization_profile_tam_full_access"

  user_tcm (extends test_user):
    contact.nameFirst: "Tcm"
    username: "tcm.mustermann"
    authorizationProfiles:
      - "@authorization_profile_tcm_full_access"

  user_tpm (extends test_user):
    contact.nameFirst: "Tpm"
    username: "tpm.mustermann"
    authorizationProfiles:
      - "@authorization_profile_tpm_full_access"

  user_igt (extends test_user):
    contact.nameFirst: "Igt"
    username: "igt.mustermann"
    authorizationProfiles:
      - "@authorization_profile_igt_full_access"

  user_apm (extends test_user):
    contact.nameFirst: "Apm"
    username: "apm.mustermann"
    authorizationProfiles:
      - "@authorization_profile_apm_full_access"

  user_demo_{1..30}:
    __factory: { '@U2\DataFixtures\Example\UserFactory::getObject': [ { password: "<password()>" } ] }
    parentUser: "10%? @user_*"
    accountExpires: "40%? <dateTimeBetween('-1 month', '+2 years')>"
    groups: "<numberBetween(1, 4)>x @group_demo_users_*"
    authorizationProfiles: "<numberBetween(1, 2)>x @authorization_profile_*"
    units: "<numberBetween(5, 50)>x @unit_*"
    contact.country: '@country_*'
