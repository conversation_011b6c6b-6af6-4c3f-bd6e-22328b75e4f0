<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use Tests\U2\TestUtils;
use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Datasheets\Item\ExchangeMethods;
use U2\Datasheets\Item\ItemTypes;
use U2\Entity\Item;

/**
 * @extends ModelFactory<Item>
 */
final class ItemFactory extends ModelFactory
{
    protected function defaults(): array
    {
        $defaultName = self::faker()->unique()->name();

        return [
            'refId' => $defaultName,
            'name' => $defaultName,
            'type' => ItemTypes::NUMBER,
            'description' => self::faker()->paragraph(2),
        ];
    }

    protected function initialize(): static
    {
        return $this->afterInstantiate(function (Item $item, array $attributes): void {
            /** @var array{
             *      "id"?: int,
             *  } $attributes
             */
            if (\array_key_exists('id', $attributes)) {
                TestUtils::setId($item, $attributes['id']);
            }
        });
    }

    public function withId(?int $id = null): self
    {
        return $this->with(fn (): array => ['id' => $id ?? self::faker()->unique()->numberBetween(0, 10000)]);
    }

    public function withFormula(string $formula): self
    {
        return $this->with(['formulaString' => $formula]);
    }

    public function checkbox(): self
    {
        return $this->with(['type' => ItemTypes::CHECKBOX]);
    }

    public function diff(): self
    {
        return $this->with(['type' => ItemTypes::DIFF, 'editable' => false]);
    }

    public function money(): self
    {
        return $this->with(['type' => ItemTypes::MONEY, 'exchangeMethod' => ExchangeMethods::AVERAGE]);
    }

    public function number(): self
    {
        return $this->with(['type' => ItemTypes::NUMBER]);
    }

    public function percent(): self
    {
        return $this->with(['type' => ItemTypes::PERCENT]);
    }

    public function text(): self
    {
        return $this->with(['type' => ItemTypes::TEXT]);
    }

    public function editable(): self
    {
        return $this->with(['editable' => true]);
    }

    public function readonly(): self
    {
        return $this->with(['editable' => false]);
    }

    public static function class(): string
    {
        return Item::class;
    }
}
