<?php

declare(strict_types=1);
namespace U2\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\AbstractFilter;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class UnitRequiredFilter extends AbstractFilter
{
    public const string propertyUnitId = 'unit.id';
    public const string propertyUnitRefId = 'unit.refId';

    public function getDescription(string $resourceClass): array
    {
        $description = [];
        $description[self::propertyUnitId] = [
            'type' => 'int',
            'required' => false,
            'allowEmptyValue' => false,
            'property' => self::propertyUnitId,
            'is_collection' => false,
            'description' => 'Either the unit "Id" or "RefId" is required.',
        ];

        $description[self::propertyUnitRefId] = [
            'type' => 'string',
            'required' => false,
            'allowEmptyValue' => false,
            'property' => self::propertyUnitRefId,
            'is_collection' => false,
            'description' => 'Either the unit "RefId" or "Id" is required.',
        ];

        return $description;
    }

    /**
     * @param array<mixed> $context
     */
    public function apply(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if (!isset($context['filters']) || !\is_array($context['filters'])) {
            throw new BadRequestHttpException('It is required to provide a filter for the unit.');
        }

        if (
            (!isset($context['filters'][self::propertyUnitRefId]) && !isset($context['filters'][self::propertyUnitId]))
            || (isset($context['filters'][self::propertyUnitRefId], $context['filters'][self::propertyUnitId]))
        ) {
            throw new BadRequestHttpException('A filter for either "' . self::propertyUnitId . '" or "' . self::propertyUnitRefId . '" is required');
        }

        parent::apply($queryBuilder, $queryNameGenerator, $resourceClass, $operation, $context);
    }

    /**
     * @param mixed        $value
     * @param array<mixed> $context
     */
    protected function filterProperty(string $property, $value, QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if (self::propertyUnitId !== $property && self::propertyUnitRefId !== $property) {
            return;
        }

        $alias = $queryBuilder->getRootAliases()[0];
        $field = $property;

        if ($this->isPropertyNested($property, $resourceClass)) {
            [$alias, $field] = $this->addJoinsForNestedProperty($property, $alias, $queryBuilder, $queryNameGenerator, $resourceClass, Join::INNER_JOIN);
        }

        $valueParameter = $queryNameGenerator->generateParameterName($field);

        $queryBuilder
            ->andWhere(\sprintf('%s.%s = :%s', $alias, $field, $valueParameter))
            ->setParameter($valueParameter, $value);
    }
}
