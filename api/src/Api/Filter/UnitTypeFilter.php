<?php

declare(strict_types=1);
namespace U2\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\FilterInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use U2\Entity\LegalUnit;
use U2\Entity\OrganisationalGroup;
use U2\Entity\PermanentEstablishment;
use U2\Entity\Unit;

class UnitTypeFilter implements FilterInterface
{
    private const string unitTypeQueryParameter = 'type';

    public function getDescription(string $resourceClass): array
    {
        return [
            self::unitTypeQueryParameter => [
                'type' => 'string',
                'required' => false,
                'property' => self::unitTypeQueryParameter,
                'description' => \sprintf(
                    'A comma separated list of unit types to select. Possible values are "%s", "%s", "%s", "%s"',
                    Unit::typeUnit,
                    Unit::typeLegalUnit,
                    Unit::typePermanentEstablishment,
                    Unit::typeOrganisationalGroup,
                ),
            ],
        ];
    }

    public function apply(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if (!isset($context['filters']) || !\is_array($context['filters'])) {
            return;
        }

        if (!isset($context['filters'][self::unitTypeQueryParameter])) {
            return;
        }

        $types = explode(',', $context['filters'][self::unitTypeQueryParameter]);

        if (0 === \count($types)) {
            return;
        }

        /** @var literal-string $rootAlias */
        $rootAlias = $queryBuilder->getRootAliases()[0];

        $expressions = [];
        foreach ($types as $type) {
            if (!\in_array($type, [Unit::typeUnit, Unit::typeLegalUnit, Unit::typePermanentEstablishment, Unit::typeOrganisationalGroup], true)) {
                throw new BadRequestHttpException($type . ' is not a supported unit type.');
            }
            switch ($type) {
                case Unit::typeUnit:
                    $expressions[] = $queryBuilder->expr()->isInstanceOf($rootAlias, Unit::class);
                    break;
                case Unit::typeLegalUnit:
                    $expressions[] = $queryBuilder->expr()->isInstanceOf($rootAlias, LegalUnit::class);
                    break;
                case Unit::typePermanentEstablishment:
                    $expressions[] = $queryBuilder->expr()->isInstanceOf($rootAlias, PermanentEstablishment::class);
                    break;
                case Unit::typeOrganisationalGroup:
                    $expressions[] = $queryBuilder->expr()->isInstanceOf($rootAlias, OrganisationalGroup::class);
                    break;
            }
        }

        $queryBuilder
            ->andWhere(
                $queryBuilder->expr()->orX(
                    ...$expressions
                )
            );
    }
}
