<?php

declare(strict_types=1);
namespace U2\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\FilterInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use U2\DataSourcery\DataSource\Configuration\Field;
use U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder\Filterer;
use U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder\JoinAliases;
use U2\DataSourcery\DataType\SearchTextDataType;
use U2\DataSourcery\Query\InvalidFilter;
use U2\DataSourcery\Query\SearchTextFieldHandler;
use U2\DataSourcery\UQL\Interpreter;
use U2\Table\Query\RequestQueryGenerator;
use U2\Util\ImmutableCollection;

class UqlFilter implements FilterInterface
{
    private const string UQL_QUERY_PARAMETER = RequestQueryGenerator::QUERY_PARAMETER_UQL;

    public function __construct(
        private readonly Filterer $filterer,
        private readonly SearchTextFieldHandler $searchTextFieldHandler,
        private readonly Interpreter $interpreter,
        private readonly array $properties = [],
    ) {
    }

    public function getDescription(string $resourceClass): array
    {
        return [
            self::UQL_QUERY_PARAMETER => [
                'type' => 'string',
                'required' => false,
                'property' => self::UQL_QUERY_PARAMETER,
                'description' => 'U² uql',
            ],
        ];
    }

    public function apply(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if (!isset($context['filters']) || !\is_array($context['filters'])) {
            return;
        }

        if (!isset($context['filters'][self::UQL_QUERY_PARAMETER])) {
            return;
        }

        $fields = $this->createFields();

        $filter = $this->interpreter->interpret(
            $context['filters'][self::UQL_QUERY_PARAMETER],
            new ImmutableCollection($fields),
            $resourceClass
        );

        $filter = $this->searchTextFieldHandler->handle($filter, $fields);

        if ($filter instanceof InvalidFilter) {
            throw new BadRequestHttpException('Invalid UQL Filter');
        }

        $this->filterer->filter(
            $queryBuilder,
            $filter,
            new JoinAliases($queryBuilder->getRootAliases()[0], $fields, [])
        );
    }

    private function createFields(): array
    {
        foreach ($this->properties as $fieldName => $fieldConfiguration) {
            $fields[$fieldName] = new Field(
                $fieldConfiguration['name'],
                $fieldConfiguration['name'],
                '',
                new $fieldConfiguration['type'](),
                $fieldName
            );
        }

        $fields['SearchText'] = new Field('SearchText', 'search_text', '', new SearchTextDataType());

        return $fields;
    }
}
