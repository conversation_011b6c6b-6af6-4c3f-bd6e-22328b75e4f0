<?php

declare(strict_types=1);
namespace U2\Api\Filter;

use ApiPlatform\Doctrine\Orm\Filter\FilterInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use U2\Entity\Datasheet;
use U2\Entity\DatasheetCollection;
use U2\Security\Permissions\Assignable\PermissionableEntity;
use U2\Security\Permissions\Assignable\QueryBuilderHelper;
use U2\Security\UserRoles;

class PermissionFilter implements FilterInterface
{
    public const string permissionParameter = 'permission';
    public const array supportedMasks = [
        MaskBuilder::MASK_VIEW,
        MaskBuilder::MASK_EDIT,
        MaskBuilder::MASK_DELETE,
        MaskBuilder::MASK_OWNER,
    ];

    public function __construct(
        private QueryBuilderHelper $queryBuilderHelper,
        private Security $security,
    ) {
    }

    public function getDescription(string $resourceClass): array
    {
        return [
            self::permissionParameter => [
                'type' => 'integer',
                'property' => self::permissionParameter,
                'required' => false,
                'description' => vsprintf(
                    'One of the following permission masks "%s", "%s", "%s", "%s"',
                    self::supportedMasks
                ),
            ],
        ];
    }

    /**
     * @param array<mixed> $context
     */
    public function apply(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if (!is_a($resourceClass, PermissionableEntity::class, true)) {
            return;
        }

        /** @var literal-string $rootAlias */
        $rootAlias = $queryBuilder->getRootAliases()[0];

        $mask = MaskBuilder::MASK_VIEW;
        if (\is_array($context['filters']) && \array_key_exists(self::permissionParameter, $context['filters'])) {
            /** @var string $selectedPermission */
            $selectedPermission = $context['filters'][self::permissionParameter];
            $mask = (int) $selectedPermission;

            if (!\in_array($mask, self::supportedMasks, true)) {
                throw new BadRequestHttpException($mask . ' is not a supported when filtering by permission.');
            }
        } elseif ($this->security->isGranted(UserRoles::Admin->value)) {
            return;
        }

        $this->queryBuilderHelper->addSecurityCondition(
            $queryBuilder,
            $rootAlias,
            $mask,
            is_a($resourceClass, DatasheetCollection::class, true) || is_a($resourceClass, Datasheet::class, true) ?
                [
                    $rootAlias . '.public = 1',
                ] :
                []
        );
    }

    public static function isMaskValid(int $mask): bool
    {
        return \in_array($mask, self::supportedMasks, true);
    }

    /**
     * @param array<mixed> $filters
     */
    public static function extractMaskFromFilters(array $filters): ?int
    {
        if (!\array_key_exists(self::permissionParameter, $filters)) {
            return null;
        }

        $userProvidedMask = $filters[self::permissionParameter];
        if (!is_numeric($userProvidedMask)) {
            return null;
        }

        return (int) $userProvidedMask;
    }
}
