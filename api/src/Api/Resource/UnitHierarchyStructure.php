<?php

declare(strict_types=1);
namespace U2\Api\Resource;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Link;
use ApiPlatform\Metadata\Patch;
use Symfony\Component\Serializer\Attribute\Context;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use U2\Api\Processor\UnitHierarchyStructureProcessor;
use U2\Api\Provider\UnitHierarchyStructureProvider;
use U2\Entity\UnitHierarchy;
use U2\Security\UserRoles;
use U2\Unit\Hierarchy\SnapshotDiff;
use U2\Unit\Hierarchy\UnitHierarchyTreeNode;

#[ApiResource(
    operations: [
        new Patch(
            uriTemplate: '/unit-hierarchies/{id}/structure/{date}',
            uriVariables: [
                'id' => new Link(fromClass: self::class, identifiers: ['id'], compositeIdentifier: false),
                'date' => new Link(fromClass: self::class, identifiers: ['datePathRepresentation'], compositeIdentifier: false),
            ],
            security: 'is_granted("' . UserRoles::UnitManager->value . '")',
            processor: UnitHierarchyStructureProcessor::class
        ),
        new Get(
            uriTemplate: '/unit-hierarchies/{id}/structure/{date}',
            uriVariables: [
                'id' => new Link(fromClass: self::class, identifiers: ['id'], compositeIdentifier: false),
                'date' => new Link(fromClass: self::class, identifiers: ['datePathRepresentation'], compositeIdentifier: false),
            ],
        ),
    ],
    normalizationContext: ['groups' => ['unit-hierarchy:structure:read']],
    denormalizationContext: ['groups' => ['unit-hierarchy:structure:write']],
    provider: UnitHierarchyStructureProvider::class
)]
class UnitHierarchyStructure
{
    #[ApiProperty(identifier: true)]
    public readonly int $id;

    /**
     * \ApiPlatform\Api\IdentifiersExtractor::resolveIdentifierValue() uses `instanceof \Stringable` to determine
     * whether it can resolve the value. \DateTime does not implement \Stringable, so we need a separat property
     * to be able to use the date as path variable.
     */
    #[ApiProperty(identifier: true)]
    public readonly string $datePathRepresentation;

    /**
     * @param array<UnitHierarchyTreeNode> $tree
     */
    public function __construct(
        #[Groups(groups: ['unit-hierarchy:structure:read'])]
        public UnitHierarchy $unitHierarchy,

        #[Groups(groups: ['unit-hierarchy:structure:read'])]
        #[Context([DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
        public \DateTime $date,

        #[Groups(groups: ['unit-hierarchy:structure:read'])]
        public SnapshotDiff $changeList,

        #[Groups(groups: ['unit-hierarchy:structure:read'])]
        #[Context([DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
        public ?\DateTime $lastChangeDate,

        #[Groups(groups: ['unit-hierarchy:structure:read'])]
        #[Context([DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
        public ?\DateTime $nextChangeDate,

        #[Groups(groups: ['unit-hierarchy:structure:read'])]
        public array $tree = [],
    ) {
        $unitHierarchyId = $this->unitHierarchy->getId();
        \assert(null !== $unitHierarchyId);

        $this->id = $unitHierarchyId;
        $this->datePathRepresentation = $this->date->format('Y-m-d');
    }
}
