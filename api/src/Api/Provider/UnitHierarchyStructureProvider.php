<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use U2\Api\Resource\UnitHierarchyStructure;
use U2\Repository\UnitHierarchyRepository;
use U2\Unit\Hierarchy\UnitHierarchyStructureFactory;

/**
 * @implements ProviderInterface<UnitHierarchyStructure>
 */
readonly class UnitHierarchyStructureProvider implements ProviderInterface
{
    public function __construct(
        private UnitHierarchyRepository $unitHierarchyRepository,
        private UnitHierarchyStructureFactory $unitHierarchyStructureFactory,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): UnitHierarchyStructure
    {
        \assert(\array_key_exists('uri_variables', $context));

        /** @var array<string, mixed> $contextUriVariables */
        $contextUriVariables = $context['uri_variables'];
        \assert(\array_key_exists('id', $contextUriVariables));
        \assert(\array_key_exists('date', $contextUriVariables));

        /** @var int $id */
        $id = $contextUriVariables['id'];
        $unitHierarchy = $this->unitHierarchyRepository->find($id);
        if (null === $unitHierarchy) {
            throw new NotFoundHttpException(\sprintf('Unable to find unit hierarchy with id "%s"', $id));
        }

        $date = $contextUriVariables['date'];
        \assert(\is_string($date));

        return $this->unitHierarchyStructureFactory->create(
            $unitHierarchy,
            new \DateTime($date)
        );
    }
}
