<?php

declare(strict_types=1);
namespace U2\Api\Extension;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Extension\QueryItemExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bundle\SecurityBundle\Security;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\UserRoles;

readonly class UnitAssignedUnitsExtension implements QueryCollectionExtensionInterface, QueryItemExtensionInterface
{
    public function __construct(private Security $security)
    {
    }

    /**
     * @param array<mixed> $context
     */
    public function applyToCollection(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        $this->addWhere($queryBuilder, $resourceClass, $operation);
    }

    /**
     * @param array<mixed> $context
     * @param array<mixed> $identifiers
     */
    public function applyToItem(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, array $identifiers, ?Operation $operation = null, array $context = []): void
    {
        $this->addWhere($queryBuilder, $resourceClass, $operation);
    }

    private function addWhere(QueryBuilder $queryBuilder, string $resourceClass, ?Operation $operation = null): void
    {
        if (Unit::collectionEndpointName !== $operation?->getName()) {
            return;
        }

        $user = $this->security->getUser();
        if (!is_a($resourceClass, Unit::class, true) || !($user instanceof User)) {
            return;
        }

        if ($this->security->isGranted(UserRoles::UserGroupAdmin->value) || $this->security->isGranted(UserRoles::UnitManager->value)) {
            return;
        }

        $groupUnitsSubQueryBuilder = $queryBuilder->getEntityManager()->createQueryBuilder();
        $groupUnitsSubQueryBuilder->select('groups_units.id')
            ->from(User::class, 'ufug')
            ->join('ufug.groups', 'user_groups')
            ->join('user_groups.units', 'groups_units')
            ->where('ufug = :user');

        $userUnitsSubQueryBuilder = $queryBuilder->getEntityManager()->createQueryBuilder();
        $userUnitsSubQueryBuilder->select('user_units.id')
            ->from(User::class, 'ufu')
            ->join('ufu.units', 'user_units')
            ->where('ufu = :user');

        /** @var literal-string $rootAlias */
        $rootAlias = $queryBuilder->getRootAliases()[0];

        $queryBuilder->andWhere($rootAlias . '.id IN(' . $groupUnitsSubQueryBuilder->getDQL() . ')');
        $queryBuilder->orWhere($rootAlias . '.id IN(' . $userUnitsSubQueryBuilder->getDQL() . ')');
        $queryBuilder->setParameter(':user', $user);
    }
}
