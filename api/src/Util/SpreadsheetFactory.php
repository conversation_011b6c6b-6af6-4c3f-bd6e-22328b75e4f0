<?php

declare(strict_types=1);
namespace U2\Util;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Html;
use PhpOffice\PhpSpreadsheet\Reader\IReader;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xml;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\IWriter;

final class SpreadsheetFactory
{
    private const array readers = [
        IOFactory::READER_XLSX => Xlsx::class,
        IOFactory::READER_XLS => Xls::class,
        IOFactory::READER_XML => Xml::class,
        IOFactory::READER_HTML => Html::class,
        IOFactory::READER_CSV => Csv::class,
    ];
    private const array writers = [
        IOFactory::WRITER_XLS => Xls::class,
        IOFactory::WRITER_XLSX => Xlsx::class,
        IOFactory::WRITER_CSV => Csv::class,
        IOFactory::WRITER_HTML => Html::class,
    ];

    /**
     * @param string|null $filename if set, uses the IOFactory to return the spreadsheet located at $filename
     *                              using automatic type resolution per \PhpOffice\PhpSpreadsheet\IOFactory
     */
    public static function createSpreadsheet(?string $filename = null): Spreadsheet
    {
        return null === $filename ? new Spreadsheet() : IOFactory::load($filename);
    }

    public static function createWriter(Spreadsheet $spreadsheet, string $type, array $writerOptions = []): IWriter
    {
        if (!\array_key_exists($type, self::writers)) {
            throw new \InvalidArgumentException('The writer [' . $type . '] does not exist or is not supported by PhpSpreadsheet.');
        }

        $writer = IOFactory::createWriter($spreadsheet, $type);

        foreach ($writerOptions as $method => $arguments) {
            if (method_exists($writer, $method)) {
                if (!\is_array($arguments)) {
                    $arguments = [$arguments];
                }

                $thing = [$writer, $method];
                \assert(\is_callable($thing));

                \call_user_func_array($thing, $arguments);
            }
        }

        return $writer;
    }

    public static function createReader(string $type): IReader
    {
        if (!\array_key_exists($type, self::readers)) {
            throw new \InvalidArgumentException('The reader [' . $type . '] does not exist or is not supported by PhpSpreadsheet.');
        }

        return new (self::readers[$type])();
    }
}
