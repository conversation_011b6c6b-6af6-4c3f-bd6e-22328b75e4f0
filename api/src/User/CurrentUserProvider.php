<?php

declare(strict_types=1);
namespace U2\User;

use <PERSON>ymfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use U2\Entity\User;
use U2\Exception\Exception;

readonly class CurrentUserProvider
{
    public function __construct(
        private TokenStorageInterface $tokenStorage,
    ) {
    }

    public function hasUser(): bool
    {
        return $this->tokenStorage->getToken()?->getUser() instanceof User;
    }

    /**
     * @throws Exception
     */
    public function get(): User
    {
        $user = $this->tokenStorage->getToken()?->getUser();

        if (!$user instanceof User) {
            throw new Exception('There is no current user');
        }

        return $user;
    }
}
