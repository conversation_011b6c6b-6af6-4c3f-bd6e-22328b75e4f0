<?php

declare(strict_types=1);
namespace U2\Task\Field;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class TypeOfProtectionTypes extends AbstractConstantChoiceBag
{
    public const string creditDefault = 'credit-default';

    public const string interestRate = 'interestRate';

    public const string currency = 'currency';

    public const string others = 'others';

    public static function getReadableMap(): array
    {
        return [
            self::creditDefault => 'Credit Default',
            self::interestRate => 'Interest Rate',
            self::currency => 'Currency',
            self::others => 'Others',
        ];
    }
}
