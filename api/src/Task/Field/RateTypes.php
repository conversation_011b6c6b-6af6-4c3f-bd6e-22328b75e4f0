<?php

declare(strict_types=1);
namespace U2\Task\Field;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class RateTypes extends AbstractConstantChoiceBag
{
    public const string interest = 'Interest';

    public const string fx = 'FX';

    public static function getReadableMap(): array
    {
        return [
            self::fx => 'FX',
            self::interest => 'Interest',
        ];
    }
}
