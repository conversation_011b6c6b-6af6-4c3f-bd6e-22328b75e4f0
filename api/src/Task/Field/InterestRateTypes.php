<?php

declare(strict_types=1);
namespace U2\Task\Field;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class InterestRateTypes extends AbstractConstantChoiceBag
{
    public const string FIXED = 'fixed';

    public const string MIXED = 'mixed';

    public const string VARIABLE = 'variable';

    public static function getReadableMap(): array
    {
        return [
            self::FIXED => 'Fixed',
            self::MIXED => 'Mixed',
            self::VARIABLE => 'Variable',
        ];
    }
}
