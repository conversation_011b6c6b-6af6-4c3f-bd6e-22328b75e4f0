<?php

declare(strict_types=1);
namespace U2\Task\Field;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class SeniorityTypes extends AbstractConstantChoiceBag
{
    public const string SENIOR = 'senior';

    public const string SUBORDINATE = 'subordinate';

    public static function getReadableMap(): array
    {
        return [
            self::SENIOR => 'Senior',
            self::SUBORDINATE => 'Subordinate',
        ];
    }
}
