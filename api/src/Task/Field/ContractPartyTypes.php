<?php

declare(strict_types=1);
namespace U2\Task\Field;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class ContractPartyTypes extends AbstractConstantChoiceBag
{
    public const string BORROWER = 'borrower';

    public const string CREDITOR = 'creditor';

    public static function getReadableMap(): array
    {
        return [
            self::BORROWER => 'Borrower',
            self::CREDITOR => 'Creditor',
        ];
    }
}
