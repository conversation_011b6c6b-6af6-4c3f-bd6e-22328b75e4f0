<?php

declare(strict_types=1);
namespace U2\Task\Field;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class PurposeOfInstrumentTypes extends AbstractConstantChoiceBag
{
    public const string MI = 'mi';

    public const string MA = 'ma';

    public const string MALCF = 'malcf';

    public const string EPM = 'epm';

    public static function getReadableMap(): array
    {
        return [
            self::MI => 'Micro Hedge',
            self::MA => 'Macro Hedge',
            self::MALCF => 'Matching Assets and Liabilities Cash Flows',
            self::EPM => 'Efficient Portfolio Management',
        ];
    }
}
