<?php

declare(strict_types=1);
namespace U2\Task;

class TaskTypeFieldConfigMap
{
    /**
     * @var array<string, array{required: bool, label: string, help: string|null }>
     */
    public const array igtApmPropertiesToConfigurationMap = [
        'indirectTransactions' => [
            'required' => true,
            'label' => 'Indirect Transactions',
            'help' => null,
        ],

        'singleEconomicOperation' => [
            'required' => true,
            'label' => 'Single Economic Operation',
            'help' => null,
        ],

        'traceId' => [
            'required' => true,
            'label' => 'Trace ID',
            'help' => 'Choose the Trace ID from the dropdown list ',
        ],

        'internalId' => [
            'required' => true,
            'label' => 'Internal ID of Transaction',
            'help' => 'Enter the contract number or other internal code of the transaction. ',
        ],

        'instrumentId' => [
            'required' => true,
            'label' => 'ID code of Instrument',
            'help' => ' If an instrument code (e.g. ISIN) exists, enter this instrument ID Code, otherwise please enter your Internal Code',
        ],

        'instrumentIdType' => [
            'required' => true,
            'label' => 'ID Code Type of the Instrument',
            'help' => 'Choose the code type from the dropdown list',
        ],

        'transactionDate' => [
            'required' => true,
            'label' => 'Transaction Date',
            'help' => 'Enter the effective booking date',
        ],

        'reportingDate' => [
            'required' => true,
            'label' => 'Reporting Date',
            'help' => null,
        ],

        'contractDate' => [
            'required' => true,
            'label' => 'Contract Date',
            'help' => 'Signing date of contract / effective beginning date of agreement',
        ],

        'contractExpiryDate' => [
            'required' => true,
            'label' => 'Contract Expiry Date',
            'help' => 'Maturity date/end date of contract (if no maturity enter 31.12.9999)',
        ],

        'validityPeriodStartDate' => [
            'required' => true,
            'label' => 'Validity period start date',
            'help' => 'Signing date of contract / effective beginning date of agreement',
        ],

        'validityPeriodExpiryDate' => [
            'required' => true,
            'label' => 'Validity period expiry date',
            'help' => 'Maturity date/end date of contract (if no maturity enter 31.12.9999)',
        ],

        'transactionTradeDate' => [
            'required' => true,
            'label' => 'Transaction Trade Date (starting date)',
            'help' => null,
        ],

        'maturityDate' => [
            'required' => true,
            'label' => 'Maturity Date',
            'help' => null,
        ],

        'transactionValue' => [
            'required' => true,
            'label' => 'Value of the Transaction',
            'help' => null,
        ],

        'contractualAmount' => [
            'required' => true,
            'label' => 'Contractual amount of transaction at transaction date',
            'help' => null,
        ],

        'notionalAmount' => [
            'required' => true,
            'label' => 'Notional amount at transaction date',
            'help' => null,
        ],

        'valueAtStartDate' => [
            'required' => true,
            'label' => 'Value of transaction at starting date',
            'help' => null,
        ],

        'contractParty' => [
            'required' => true,
            'label' => 'Contract Party',
            'help' => null,
        ],

        'couponInterestRateType' => [
            'required' => true,
            'label' => 'Coupon/Interest Rate Type',
            'help' => 'Agreed type of interest rate e.g. fix, variable, mixed',
        ],

        'couponInterestRate' => [
            'required' => true,
            'label' => 'Coupon/Interest Rate',
            'help' => 'Enter effective percentage of interest rate or 0 if not applicable',
        ],

        'previousPeriodBookValue' => [
            'required' => true,
            'label' => 'Book Value Previous Period',
            'help' => 'Booked amount as of 31.12. of previous year. Enter 0 if transaction starts in current year.',
        ],

        'interestPaymentsAmount' => [
            'required' => true,
            'label' => 'Amount of Interest Payments',
            'help' => 'Booked cumulative interest income (+) as of 1.1. of current year',
        ],

        'couponsAmount' => [
            'required' => true,
            'label' => 'Coupon Payments',
            'help' => 'Booked cumulative coupon income (+) as of 1.1. of current year',
        ],

        'otherPayment' => [
            'required' => true,
            'label' => 'Amount of Other Payments',
            'help' => 'Amount of total tops-ups if applicable, i.e. total additional money invested during the reporting period such as an additional payments on partly paid shares or increasing loan amount during the period ',
        ],

        'currentPeriodBookValue' => [
            'required' => true,
            'label' => 'Book Value Current Period',
            'help' => 'Amount booked as of the end of current period. Enter 0 if transaction ends in current period.',
        ],

        's2CollateralValue' => [
            'required' => true,
            'label' => 'S2 Value of Collateral',
            'help' => 'Solvency II value of collateral (e.g. real estate appraisal by independent expert). Enter 0 if not applicable.',
        ],

        'carryingAmount' => [
            'required' => true,
            'label' => 'Carrying Amount',
            'help' => 'Enter the value of the derivative at the reporting date as reported in the balance sheet of the entity. ',
        ],

        'conditionInterestRate' => [
            'required' => false,
            'label' => 'Condition/Interest Rate',
            'help' => null,
        ],

        'conditionDetails' => [
            'required' => false,
            'label' => 'Condition Details',
            'help' => null,
        ],

        'typeOfProtection' => [
            'required' => true,
            'label' => 'Type of protection',
            'help' => 'Choose the type of protection from the dropdown list',
        ],

        'purposeOfInstrument' => [
            'required' => true,
            'label' => 'Purpose of the instrument',
            'help' => 'Choose the purpose of the instrument from the dropdown list',
        ],

        'assetOrLiabilityUnderlyingDerivative' => [
            'required' => true,
            'label' => 'ID Code of Asset/Liability Underlying the Derivative',
            'help' => 'Enter the ID Code of the asset or liability underlying the derivative contract.',
        ],

        'assetOrLiabilityUnderlyingDerivativeIdType' => [
            'required' => true,
            'label' => 'ID Code Type of Asset/Liability Underlying the Derivative',
            'help' => 'Choose the code type from the dropdown list',
        ],

        'counterPartyName' => [
            'required' => true,
            'label' => 'Counterparty Name for which Credit Protection is Purchased',
            'help' => null,
        ],

        'revenuesFromDerivatives' => [
            'required' => true,
            'label' => 'Revenues stemming from derivatives',
            'help' => 'Net revenues stemming from the investment or the purchase of derivatives. Following the IFRS based P&L, both realized and unrealized results are expected here. ',
        ],

        'swapDeliveredInterestRate' => [
            'required' => true,
            'label' => 'Swap delivered Interest Rate (for Buyer)',
            'help' => null,
        ],

        'swapReceivedInterestRate' => [
            'required' => true,
            'label' => 'Swap recieved Interest Rate (for Buyer)',
            'help' => null,
        ],

        'swapDeliveredCurrency' => [
            'required' => true,
            'label' => 'Swap delivered currency (for Buyer)',
            'help' => null,
        ],

        'swapReceivedCurrency' => [
            'required' => true,
            'label' => 'Swap recieved currency (for Buyer)',
            'help' => null,
        ],

        'valueAtReportingDate' => [
            'required' => true,
            'label' => 'Value of Transaction at Reporting Date',
            'help' => null,
        ],

        'maxContingentLiabilitiesValue' => [
            'required' => true,
            'label' => 'Maximum Value of Contingent Liabilities',
            'help' => 'Sum of all possible cash flows related to guarantees',
        ],

        'guaranteedAssetsValue' => [
            'required' => true,
            'label' => 'Value of Guaranteed Assets',
            'help' => 'Value of the guaranteed asset for which the guarantees are received.',
        ],

        'revenuesFromOffBalanceSheetItems' => [
            'required' => true,
            'label' => 'Revenues stemming from the off-balance sheet items',
            'help' => 'Revenues associated to the provisions of the off-balance sheet transaction',
        ],

        'triggerEvent' => [
            'required' => true,
            'label' => 'Trigger Event',
            'help' => 'Description of event that would trigger the transaction/payment/liability…etc. Note: Enter N/A if not applicable',
        ],

        'lineOfBusiness' => [
            'required' => true,
            'label' => 'Line of Business',
            'help' => 'Identify the line of business by choosing the drop down menu ',
        ],

        'claimsExpenses' => [
            'required' => true,
            'label' => 'Claims Expenses',
            'help' => 'Total amount of gross claims paid during the year, including claims management expenses',
        ],

        'interestOnDeposit' => [
            'required' => true,
            'label' => 'Interest on deposit',
            'help' => 'Enter the interest on deposit which is related to reinsurance contract. Note: Enter 0 if such a contractual agreement does not exist',
        ],

        'netReceivables' => [
            'required' => true,
            'label' => 'Net Recievables',
            'help' => 'The amount resulting from: claims paid by the (re)insurer but not yet reimbursed by the (re)insurer + commissions to be paid by the (re)insurer + other receivables minus debts to the (re)insurer. ',
        ],

        'totalReinsuranceRecoverable' => [
            'required' => true,
            'label' => 'Total reinsurance Recoverable at Reporting date',
            'help' => 'Enter the total amount due from the reinsurer which includes: Premium Provision; Claims Provision and/or Technical Provision',
        ],

        'maxCoverByReinsurer' => [
            'required' => true,
            'label' => 'Maximum Cover by Reinsurer',
            'help' => 'Please note that for Quota Share or Surplus treaty, 100% of the Maximum Amount that has been set for the entire contract is stated here. In case of unlimited cover "-1" shall be filled in.',
        ],

        'reinsuranceResult' => [
            'required' => true,
            'label' => 'Reinsurance Result (for Reinsured Entity)',
            'help' => 'Enter the Reinsurance Result which includes: Total Reinsurance Commissions - Gross Reinsurance Premiums + Claims + Total Reinsursance Recoverables - Total Reinsurance Recoverables',
        ],

        'serviceType' => [
            'required' => true,
            'label' => 'Service Type',
            'help' => null,
        ],

        'financingPurpose' => [
            'required' => true,
            'label' => 'Financing Purpose',
            'help' => null,
        ],

        'repaymentConditions' => [
            'required' => true,
            'label' => 'Repayment Conditions',
            'help' => null,
        ],

        'seniority' => [
            'required' => true,
            'label' => 'Seniority',
            'help' => null,
        ],

        'collateralisationType' => [
            'required' => true,
            'label' => 'Collateralisation Type',
            'help' => null,
        ],

        'businessCase' => [
            'required' => true,
            'label' => 'Business Case',
            'help' => null,
        ],

        'armsLength' => [
            'required' => true,
            'label' => 'Arms Length',
            'help' => 'Confirm whether transaction is at arm\'s length',
        ],

        'transferPricingMethod' => [
            'required' => true,
            'label' => 'Transfer Pricing Method',
            'help' => 'Choose the appropriate transfer pricing method from the drop down list',
        ],
    ];
}
