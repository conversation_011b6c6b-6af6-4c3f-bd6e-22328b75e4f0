<?php

declare(strict_types=1);
namespace U2\Task;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use U2\DataSourcery\DataSource\DataSourceFactory;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType;
use U2\EntityMetadata\Entity\ReadableNameTranslator;
use U2\Security\Authorization\AuthorizationRight;
use U2\Table\StateRequestResolver;
use U2\Table\TableFactory;
use U2\Task\Interfaces\Typeable;

class TaskListInfoFactory
{
    public function __construct(
        private readonly RequestStack $requestStack,
        private readonly RouterInterface $router,
        private readonly ReadableNameTranslator $readableNameTranslator,
        private readonly Security $security,
        private readonly DataSourceFactory $dataSourceFactory,
        private readonly DataSourceConfigurationCollection $dataSourceConfigurationCollection,
        private readonly TableViewConfigurationCollection $tableViewConfigurationCollection,
        private readonly TableFactory $tableFactory,
        private readonly StateRequestResolver $stateRequestResolver,
    ) {
    }

    /**
     * @param class-string<TaskType> $entityClass
     */
    public function createForEntity(string $entityClass): TaskListInfo
    {
        $request = $this->requestStack->getCurrentRequest();
        \assert(null !== $request);

        $table = $this->tableFactory->create(
            $this->stateRequestResolver->resolve($this->tableViewConfigurationCollection->get($entityClass), $this->dataSourceFactory->createFromConfiguration(
                $this->dataSourceConfigurationCollection->get(
                    $entityClass
                )
            )),
            $this->dataSourceFactory->createFromConfiguration(
                $this->dataSourceConfigurationCollection->get(
                    $entityClass
                )
            ),
            $this->tableViewConfigurationCollection->get($entityClass)
        );
        $tableView = $table->getTwigView();

        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $savedFilterInformation = $tableView->getSavedFilterInformation();

        $hasMultipleOptionsForNew = is_a($entityClass, Typeable::class, true);
        if ($hasMultipleOptionsForNew) {
            /** @var array<string, string> $optionsForNew */
            $optionsForNew = $entityClass::getTypesReadable();
        }

        $hasDocument = is_a($entityClass, StructuredDocumentInterface::class, true);

        return new TaskListInfo(
            $this->readableNameTranslator->translateClassPlural($entityClass),
            $this->security->isGranted($authorizationItem->value . ':' . AuthorizationRight::DELETE->value),
            $this->security->isGranted($authorizationItem->value . ':' . AuthorizationRight::UPDATE->value),
            $this->security->isGranted($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null),
            $hasDocument,
            $hasMultipleOptionsForNew,
            '' !== $tableView->getState()->getQuery()->getFilter()->getUql(),
            TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass],
            $savedFilterInformation,
            $this->router->generate(TaskTypeKnowledge::getAvailableRoutes($entityClass)['new']),
            $optionsForNew ?? [],
        );
    }
}
