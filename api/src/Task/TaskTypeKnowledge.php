<?php

declare(strict_types=1);
namespace U2\Task;

use U2\Apm\Transaction\TransactionTableType as ApmTransactionTableType;
use U2\Contract\ContractTableType;
use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Entity\Task\TaskType\Contract;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Task\TaskType\Igt1Transaction;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use U2\Entity\Task\TaskType\TaxAssessmentStatus;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Entity\Task\TaskType\TaxAuthorityAuditObjection;
use U2\Entity\Task\TaskType\TaxConsultingFee;
use U2\Entity\Task\TaskType\TaxCredit;
use U2\Entity\Task\TaskType\TaxFilingMonitor;
use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Entity\Task\TaskType\TaxRate;
use U2\Entity\Task\TaskType\TaxRelevantRestriction;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Task\TaskType\TransferPricing;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Exception\Exception;
use U2\Form\Type\Task\AbstractTaskTypeFormType;
use U2\Form\Type\Task\ApmIgtTransactionBulkEditFormType;
use U2\Form\Type\Task\ApmIgtTransactionFormType;
use U2\Form\Type\Task\ContractBulkEditFormType;
use U2\Form\Type\Task\ContractFormType;
use U2\Form\Type\Task\CountryByCountryReportBulkEditFormType;
use U2\Form\Type\Task\CountryByCountryReportFormType;
use U2\Form\Type\Task\FinancialDataBulkEditFormType;
use U2\Form\Type\Task\FinancialDataFormType;
use U2\Form\Type\Task\IncomeTaxPlanningBulkEditFormType;
use U2\Form\Type\Task\IncomeTaxPlanningFormType;
use U2\Form\Type\Task\LocalFileBulkEditFormType;
use U2\Form\Type\Task\LocalFileFormType;
use U2\Form\Type\Task\LossCarryForwardBulkEditFormType;
use U2\Form\Type\Task\LossCarryForwardFormType;
use U2\Form\Type\Task\MainBusinessActivityBulkEditFormType;
use U2\Form\Type\Task\MainBusinessActivityFormType;
use U2\Form\Type\Task\MasterFileBulkEditFormType;
use U2\Form\Type\Task\MasterFileFormType;
use U2\Form\Type\Task\OtherDeadlineBulkEditFormType;
use U2\Form\Type\Task\OtherDeadlineFormType;
use U2\Form\Type\Task\TaskBulkChangeFormType;
use U2\Form\Type\Task\TaxAssessmentMonitorBulkEditFormType;
use U2\Form\Type\Task\TaxAssessmentMonitorFormType;
use U2\Form\Type\Task\TaxAssessmentStatusBulkEditFormType;
use U2\Form\Type\Task\TaxAssessmentStatusFormType;
use U2\Form\Type\Task\TaxAuditRiskBulkEditFormType;
use U2\Form\Type\Task\TaxAuditRiskFormType;
use U2\Form\Type\Task\TaxAuthorityAuditObjectionBulkEditFormType;
use U2\Form\Type\Task\TaxAuthorityAuditObjectionFormType;
use U2\Form\Type\Task\TaxConsultingFeeBulkEditFormType;
use U2\Form\Type\Task\TaxConsultingFeeFormType;
use U2\Form\Type\Task\TaxCreditBulkEditFormType;
use U2\Form\Type\Task\TaxCreditFormType;
use U2\Form\Type\Task\TaxFilingMonitorBulkEditFormType;
use U2\Form\Type\Task\TaxFilingMonitorFormType;
use U2\Form\Type\Task\TaxLitigationBulkEditFormType;
use U2\Form\Type\Task\TaxLitigationFormType;
use U2\Form\Type\Task\TaxRateBulkEditFormType;
use U2\Form\Type\Task\TaxRateFormType;
use U2\Form\Type\Task\TaxRelevantRestrictionBulkEditFormType;
use U2\Form\Type\Task\TaxRelevantRestrictionFormType;
use U2\Form\Type\Task\TransactionBulkEditFormType;
use U2\Form\Type\Task\TransactionFormType;
use U2\Form\Type\Task\TransferPricingBulkEditFormType;
use U2\Form\Type\Task\TransferPricingFormType;
use U2\Form\Type\Task\UnitPeriodFormType;
use U2\Igt\Igt1\Igt1TransactionTableType;
use U2\Igt\Igt2\Igt2TransactionTableType;
use U2\Igt\Igt3\Igt3TransactionTableType;
use U2\Igt\Igt4\Igt4TransactionTableType;
use U2\Igt\Igt5\Igt5TransactionTableType;
use U2\Task\TableType\AbstractTaskTypeTableType;
use U2\Task\TaskType\UnitPeriod\UnitPeriodTableType;
use U2\TaxCompliance\IncomeTaxPlanning\IncomeTaxPlanningTableType;
use U2\TaxCompliance\LossCarryForward\LossCarryForwardTableType;
use U2\TaxCompliance\OtherDeadline\OtherDeadlineTableType;
use U2\TaxCompliance\TaxAssessmentMonitor\TaxAssessmentMonitorTableType;
use U2\TaxCompliance\TaxAssessmentStatus\TaxAssessmentStatusTableType;
use U2\TaxCompliance\TaxAuditRisk\TaxAuditRiskTableType;
use U2\TaxCompliance\TaxAuthorityAuditObjection\TaxAuthorityAuditObjectionTableType;
use U2\TaxCompliance\TaxConsultingFee\TaxConsultingFeeTableType;
use U2\TaxCompliance\TaxCredit\TaxCreditTableType;
use U2\TaxCompliance\TaxFilingMonitor\TaxFilingMonitorTableType;
use U2\TaxCompliance\TaxLitigation\TaxLitigationTableType;
use U2\TaxCompliance\TaxRate\TaxRateTableType;
use U2\TaxCompliance\TaxRelevantRestriction\TaxRelevantRestrictionTableType;
use U2\TaxCompliance\TransferPricing\TransferPricingTableType;
use U2\TransferPricing\AbstractDocumentTableType;
use U2\TransferPricing\CountryByCountryReport\CountryByCountryReportTableType;
use U2\TransferPricing\FinancialData\FinancialDataTableType;
use U2\TransferPricing\LocalFile\LocalFileTableType;
use U2\TransferPricing\MainBusinessActivity\MainBusinessActivityTableType;
use U2\TransferPricing\MasterFile\MasterFileTableType;
use U2\TransferPricing\Transaction\TransactionTableType;

class TaskTypeKnowledge
{
    /**
     * @var array<class-string<TaskType>, AuthorizationItem>
     */
    public const array taskTypeClassToAuthorizationItem = [
        ApmTransaction::class => AuthorizationItem::ApmTransaction,
        Contract::class => AuthorizationItem::Contract,
        CountryByCountryReport::class => AuthorizationItem::CountryByCountryReport,
        FinancialData::class => AuthorizationItem::FinancialData,
        Igt1Transaction::class => AuthorizationItem::Igt1Transaction,
        Igt2Transaction::class => AuthorizationItem::Igt2Transaction,
        Igt3Transaction::class => AuthorizationItem::Igt3Transaction,
        Igt4Transaction::class => AuthorizationItem::Igt4Transaction,
        Igt5Transaction::class => AuthorizationItem::Igt5Transaction,
        IncomeTaxPlanning::class => AuthorizationItem::IncomeTaxPlanning,
        LocalFile::class => AuthorizationItem::LocalFile,
        LossCarryForward::class => AuthorizationItem::LossCarryForward,
        MainBusinessActivity::class => AuthorizationItem::MainBusinessActivity,
        MasterFile::class => AuthorizationItem::MasterFile,
        OtherDeadline::class => AuthorizationItem::OtherDeadline,
        TaxAssessmentMonitor::class => AuthorizationItem::TaxAssessmentMonitor,
        TaxAssessmentStatus::class => AuthorizationItem::TaxAssessmentStatus,
        TaxAuditRisk::class => AuthorizationItem::TaxAuditRisk,
        TaxAuthorityAuditObjection::class => AuthorizationItem::TaxAuthorityAuditObjection,
        TaxConsultingFee::class => AuthorizationItem::TaxConsultingFee,
        TaxCredit::class => AuthorizationItem::TaxCredit,
        TaxFilingMonitor::class => AuthorizationItem::TaxFilingMonitor,
        TaxLitigation::class => AuthorizationItem::TaxLitigation,
        TaxRate::class => AuthorizationItem::TaxRate,
        TaxRelevantRestriction::class => AuthorizationItem::TaxRelevantRestriction,
        Transaction::class => AuthorizationItem::Transaction,
        TransferPricing::class => AuthorizationItem::TransferPricing,
        UnitPeriod::class => AuthorizationItem::UnitPeriod,
    ];

    /**
     * @var array<class-string<AbstractTaskTypeTableType|AbstractDocumentTableType>,string>
     */
    public const array taskTypeTableTypeClassToShortNameMap = [
        ApmTransactionTableType::class => 'apm-transaction',
        ContractTableType::class => 'cm-contract',
        CountryByCountryReportTableType::class => 'tpm-country-by-country-report',
        FinancialDataTableType::class => 'tpm-financial-data',
        Igt1TransactionTableType::class => 'igt-igt1-transaction',
        Igt2TransactionTableType::class => 'igt-igt2-transaction',
        Igt3TransactionTableType::class => 'igt-igt3-transaction',
        Igt4TransactionTableType::class => 'igt-igt4-transaction',
        Igt5TransactionTableType::class => 'igt-igt5-transaction',
        IncomeTaxPlanningTableType::class => 'tam-income-tax-planning',
        LocalFileTableType::class => 'tpm-local-file',
        LossCarryForwardTableType::class => 'tam-loss-carry-forward',
        MainBusinessActivityTableType::class => 'tpm-main-business-activity',
        MasterFileTableType::class => 'tpm-master-file',
        OtherDeadlineTableType::class => 'tcm-other-deadline',
        TaxAssessmentMonitorTableType::class => 'tcm-tax-assessment-monitor',
        TaxAssessmentStatusTableType::class => 'tam-tax-assessment-status',
        TaxAuditRiskTableType::class => 'tam-tax-audit-risk',
        TaxAuthorityAuditObjectionTableType::class => 'tcm-tax-authority-audit-objection',
        TaxConsultingFeeTableType::class => 'tam-tax-consulting-fee',
        TaxCreditTableType::class => 'tam-tax-credit',
        TaxFilingMonitorTableType::class => 'tcm-tax-filing-monitor',
        TaxLitigationTableType::class => 'tam-tax-litigation',
        TaxRateTableType::class => 'tam-tax-rate',
        TaxRelevantRestrictionTableType::class => 'tam-tax-relevant-restriction',
        TransactionTableType::class => 'tpm-transaction',
        TransferPricingTableType::class => 'tam-transfer-pricing',
        UnitPeriodTableType::class => 'unit-period',
    ];

    /**
     * @var array<class-string<TaskType>,string>
     */
    public const array taskTypeClassToShortNameMap = [
        ApmTransaction::class => 'apm-transaction',
        Contract::class => 'cm-contract',
        CountryByCountryReport::class => 'tpm-country-by-country-report',
        FinancialData::class => 'tpm-financial-data',
        Igt1Transaction::class => 'igt-igt1-transaction',
        Igt2Transaction::class => 'igt-igt2-transaction',
        Igt3Transaction::class => 'igt-igt3-transaction',
        Igt4Transaction::class => 'igt-igt4-transaction',
        Igt5Transaction::class => 'igt-igt5-transaction',
        IncomeTaxPlanning::class => 'tam-income-tax-planning',
        LocalFile::class => 'tpm-local-file',
        LossCarryForward::class => 'tam-loss-carry-forward',
        MainBusinessActivity::class => 'tpm-main-business-activity',
        MasterFile::class => 'tpm-master-file',
        OtherDeadline::class => 'tcm-other-deadline',
        TaxAssessmentMonitor::class => 'tcm-tax-assessment-monitor',
        TaxAssessmentStatus::class => 'tam-tax-assessment-status',
        TaxAuditRisk::class => 'tam-tax-audit-risk',
        TaxAuthorityAuditObjection::class => 'tcm-tax-authority-audit-objection',
        TaxConsultingFee::class => 'tam-tax-consulting-fee',
        TaxCredit::class => 'tam-tax-credit',
        TaxFilingMonitor::class => 'tcm-tax-filing-monitor',
        TaxLitigation::class => 'tam-tax-litigation',
        TaxRate::class => 'tam-tax-rate',
        TaxRelevantRestriction::class => 'tam-tax-relevant-restriction',
        Transaction::class => 'tpm-transaction',
        TransferPricing::class => 'tam-transfer-pricing',
        UnitPeriod::class => 'unit-period',
    ];

    /**
     * @var array<int,class-string<TaskType>>
     */
    public const array taskTypeClasses = [
        ApmTransaction::class,
        Contract::class,
        CountryByCountryReport::class,
        FinancialData::class,
        Igt1Transaction::class,
        Igt2Transaction::class,
        Igt3Transaction::class,
        Igt4Transaction::class,
        Igt5Transaction::class,
        IncomeTaxPlanning::class,
        LocalFile::class,
        LossCarryForward::class,
        MainBusinessActivity::class,
        MasterFile::class,
        OtherDeadline::class,
        TaxAssessmentMonitor::class,
        TaxAssessmentStatus::class,
        TaxAuditRisk::class,
        TaxAuthorityAuditObjection::class,
        TaxConsultingFee::class,
        TaxCredit::class,
        TaxFilingMonitor::class,
        TaxLitigation::class,
        TaxRate::class,
        TaxRelevantRestriction::class,
        Transaction::class,
        TransferPricing::class,
        UnitPeriod::class,
    ];

    /**
     * @var array<string, class-string<TaskType>>
     */
    private const array routePrefixToTaskTypeMap = [
        'u2_apm_apmtransaction_' => ApmTransaction::class,
        'u2_contract_' => Contract::class,
        'u2_countrybycountryreport_' => CountryByCountryReport::class,
        'u2_financialdata_' => FinancialData::class,
        'u2_igt_igt1transaction_' => Igt1Transaction::class,
        'u2_igt_igt2transaction_' => Igt2Transaction::class,
        'u2_igt_igt3transaction_' => Igt3Transaction::class,
        'u2_igt_igt4transaction_' => Igt4Transaction::class,
        'u2_igt_igt5transaction_' => Igt5Transaction::class,
        'u2_incometaxplanning_' => IncomeTaxPlanning::class,
        'u2_localfile_' => LocalFile::class,
        'u2_losscarryforward_' => LossCarryForward::class,
        'u2_mainbusinessactivity_' => MainBusinessActivity::class,
        'u2_masterfile_' => MasterFile::class,
        'u2_otherdeadline_' => OtherDeadline::class,
        'u2_taxassessmentmonitor_' => TaxAssessmentMonitor::class,
        'u2_taxassessmentstatus_' => TaxAssessmentStatus::class,
        'u2_taxauditrisk_' => TaxAuditRisk::class,
        'u2_taxauthorityauditobjection_' => TaxAuthorityAuditObjection::class,
        'u2_taxconsultingfee_' => TaxConsultingFee::class,
        'u2_taxcredit_' => TaxCredit::class,
        'u2_taxfilingmonitor_' => TaxFilingMonitor::class,
        'u2_taxlitigation_' => TaxLitigation::class,
        'u2_taxrate_' => TaxRate::class,
        'u2_taxrelevantrestriction_' => TaxRelevantRestriction::class,
        'u2_transaction_' => Transaction::class,
        'u2_transferpricing_' => TransferPricing::class,
        'u2_unitperiod_' => UnitPeriod::class,
    ];

    /**
     * @var array<class-string<TaskType>, string>
     */
    private const array taskTypeClassToFormTemplateMap = [
        ApmTransaction::class => 'task/type/apm-igt_form.html.twig',
        Contract::class => 'task/type/contract_form.html.twig',
        CountryByCountryReport::class => 'task/type/document_configure_form.html.twig',
        FinancialData::class => 'task/type/financial_data_form.html.twig',
        Igt1Transaction::class => 'task/type/apm-igt_form.html.twig',
        Igt2Transaction::class => 'task/type/apm-igt_form.html.twig',
        Igt3Transaction::class => 'task/type/apm-igt_form.html.twig',
        Igt4Transaction::class => 'task/type/apm-igt_form.html.twig',
        Igt5Transaction::class => 'task/type/apm-igt_form.html.twig',
        IncomeTaxPlanning::class => 'task/type/income_tax_planning_form.html.twig',
        LocalFile::class => 'task/type/document_configure_form.html.twig',
        LossCarryForward::class => 'task/type/loss_carry_forward_form.html.twig',
        MainBusinessActivity::class => 'task/type/main_business_activity_form.html.twig',
        MasterFile::class => 'task/type/document_configure_form.html.twig',
        OtherDeadline::class => 'task/type/other_deadline_form.html.twig',
        TaxAssessmentMonitor::class => 'task/type/tax_assessment_monitor_form.html.twig',
        TaxAssessmentStatus::class => 'task/type/tax_assessment_status_form.html.twig',
        TaxAuditRisk::class => 'task/type/tax_audit_risk_form.html.twig',
        TaxAuthorityAuditObjection::class => 'task/type/tax_authority_audit_objection_form.html.twig',
        TaxConsultingFee::class => 'task/type/tax_consulting_fee_form.html.twig',
        TaxCredit::class => 'task/type/tax_credit_form.html.twig',
        TaxFilingMonitor::class => 'task/type/tax_filing_monitor_form.html.twig',
        TaxLitigation::class => 'task/type/tax_litigation_form.html.twig',
        TaxRate::class => 'task/type/tax_rate_form.html.twig',
        TaxRelevantRestriction::class => 'task/type/tax_relevant_restriction_form.html.twig',
        Transaction::class => 'task/type/transaction_form.html.twig',
        TransferPricing::class => 'task/type/transfer_pricing_form.html.twig',
        UnitPeriod::class => 'task/type/unit_period_form.html.twig',
    ];

    /**
     * @var array<class-string<TaskType>, class-string<AbstractTaskTypeFormType>>
     */
    public const array taskTypeClassToFormClassMap = [
        ApmTransaction::class => ApmIgtTransactionFormType::class,
        Contract::class => ContractFormType::class,
        CountryByCountryReport::class => CountryByCountryReportFormType::class,
        FinancialData::class => FinancialDataFormType::class,
        Igt1Transaction::class => ApmIgtTransactionFormType::class,
        Igt2Transaction::class => ApmIgtTransactionFormType::class,
        Igt3Transaction::class => ApmIgtTransactionFormType::class,
        Igt4Transaction::class => ApmIgtTransactionFormType::class,
        Igt5Transaction::class => ApmIgtTransactionFormType::class,
        IncomeTaxPlanning::class => IncomeTaxPlanningFormType::class,
        LocalFile::class => LocalFileFormType::class,
        LossCarryForward::class => LossCarryForwardFormType::class,
        MainBusinessActivity::class => MainBusinessActivityFormType::class,
        MasterFile::class => MasterFileFormType::class,
        OtherDeadline::class => OtherDeadlineFormType::class,
        TaxAssessmentMonitor::class => TaxAssessmentMonitorFormType::class,
        TaxAssessmentStatus::class => TaxAssessmentStatusFormType::class,
        TaxAuditRisk::class => TaxAuditRiskFormType::class,
        TaxAuthorityAuditObjection::class => TaxAuthorityAuditObjectionFormType::class,
        TaxConsultingFee::class => TaxConsultingFeeFormType::class,
        TaxCredit::class => TaxCreditFormType::class,
        TaxFilingMonitor::class => TaxFilingMonitorFormType::class,
        TaxLitigation::class => TaxLitigationFormType::class,
        TaxRate::class => TaxRateFormType::class,
        TaxRelevantRestriction::class => TaxRelevantRestrictionFormType::class,
        Transaction::class => TransactionFormType::class,
        TransferPricing::class => TransferPricingFormType::class,
        UnitPeriod::class => UnitPeriodFormType::class,
    ];

    /**
     * @var array<class-string<TaskType>, class-string<TaskBulkChangeFormType>>
     */
    public const array taskTypeClassToBulkFormClassMap = [
        ApmTransaction::class => ApmIgtTransactionBulkEditFormType::class,
        Contract::class => ContractBulkEditFormType::class,
        CountryByCountryReport::class => CountryByCountryReportBulkEditFormType::class,
        FinancialData::class => FinancialDataBulkEditFormType::class,
        Igt1Transaction::class => ApmIgtTransactionBulkEditFormType::class,
        Igt2Transaction::class => ApmIgtTransactionBulkEditFormType::class,
        Igt3Transaction::class => ApmIgtTransactionBulkEditFormType::class,
        Igt4Transaction::class => ApmIgtTransactionBulkEditFormType::class,
        Igt5Transaction::class => ApmIgtTransactionBulkEditFormType::class,
        IncomeTaxPlanning::class => IncomeTaxPlanningBulkEditFormType::class,
        LocalFile::class => LocalFileBulkEditFormType::class,
        LossCarryForward::class => LossCarryForwardBulkEditFormType::class,
        MainBusinessActivity::class => MainBusinessActivityBulkEditFormType::class,
        MasterFile::class => MasterFileBulkEditFormType::class,
        OtherDeadline::class => OtherDeadlineBulkEditFormType::class,
        TaxAssessmentMonitor::class => TaxAssessmentMonitorBulkEditFormType::class,
        TaxAssessmentStatus::class => TaxAssessmentStatusBulkEditFormType::class,
        TaxAuditRisk::class => TaxAuditRiskBulkEditFormType::class,
        TaxAuthorityAuditObjection::class => TaxAuthorityAuditObjectionBulkEditFormType::class,
        TaxConsultingFee::class => TaxConsultingFeeBulkEditFormType::class,
        TaxCredit::class => TaxCreditBulkEditFormType::class,
        TaxFilingMonitor::class => TaxFilingMonitorBulkEditFormType::class,
        TaxLitigation::class => TaxLitigationBulkEditFormType::class,
        TaxRate::class => TaxRateBulkEditFormType::class,
        TaxRelevantRestriction::class => TaxRelevantRestrictionBulkEditFormType::class,
        Transaction::class => TransactionBulkEditFormType::class,
        TransferPricing::class => TransferPricingBulkEditFormType::class,
        UnitPeriod::class => TaskBulkChangeFormType::class,
    ];

    /**
     * @param class-string<TaskType> $entityClass
     *
     * @return array<string,string>
     */
    public static function getAvailableRoutes(string $entityClass): array
    {
        $routePrefix = array_flip(self::routePrefixToTaskTypeMap)[$entityClass];

        return [
            'list' => $routePrefix . 'list',
            'table' => $routePrefix . 'table',
            'parseUql' => $routePrefix . 'parseUql',
            'listXls' => $routePrefix . 'listxls',
            'listCsv' => $routePrefix . 'listcsv',
            'listJson' => $routePrefix . 'listjson',
            'edit' => $routePrefix . 'edit',
            'new' => $routePrefix . 'new',
            'delete' => $routePrefix . 'delete',
            'editDocument' => $routePrefix . 'editdocument',
            'xmlDownload' => $routePrefix . 'xmldownload',
            'pdfDownload' => $routePrefix . 'pdfdownload',
            'typeFilterShortcut' => $routePrefix . 'typeFilterShortcut',
            'editForm' => $routePrefix . 'editForm',
            'submitEditForm' => $routePrefix . 'submitEditForm',
            'newForm' => $routePrefix . 'newForm',
            'submitNewForm' => $routePrefix . 'submitNewForm',
        ];
    }

    /**
     * @param class-string<TaskType> $entityClass
     *
     * @return class-string<AbstractTaskTypeFormType>
     */
    public static function resolveFormClassByEntityClass(string $entityClass): string
    {
        return self::taskTypeClassToFormClassMap[$entityClass];
    }

    /**
     * @throws Exception
     *
     * @return class-string<TaskType>
     */
    public static function resolveEntityClassByRouteName(string $routeName): string
    {
        foreach (self::routePrefixToTaskTypeMap as $routePrefix => $entityClass) {
            if (str_contains($routeName, $routePrefix)) {
                return $entityClass;
            }
        }

        throw new Exception(\sprintf('No class found for route with name "%s"', $routeName));
    }

    /**
     * @return class-string<TaskType>
     */
    public static function resolveEntityClassByShortName(string $shortName): string
    {
        return array_flip(self::taskTypeClassToShortNameMap)[$shortName];
    }

    /**
     * @return class-string<AbstractTaskTypeTableType|AbstractDocumentTableType>
     */
    public static function resolveTableClassByShortName(string $shortName): string
    {
        return array_flip(self::taskTypeTableTypeClassToShortNameMap)[$shortName];
    }

    /**
     * @param class-string<TaskType> $entityClass
     */
    public static function resolveFormTemplateByEntityClass(string $entityClass): string
    {
        return self::taskTypeClassToFormTemplateMap[$entityClass];
    }

    /**
     * @throws Exception
     */
    public static function getWorkflowBindingName(string $workflowBindingId): string
    {
        foreach (self::taskTypeClasses as $taskTypeClass) {
            if ($taskTypeClass::getWorkflowBindingId() === $workflowBindingId) {
                return $taskTypeClass::getWorkflowBindingName();
            }
        }

        throw new Exception(\sprintf('No class found for binding id "%s"', $workflowBindingId));
    }
}
