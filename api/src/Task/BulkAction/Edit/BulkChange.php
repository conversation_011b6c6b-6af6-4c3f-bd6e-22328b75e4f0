<?php

declare(strict_types=1);
namespace U2\Task\BulkAction\Edit;

use U2\Entity\Task\TaskType;
use U2\Util\EntityPropertyMappingHelper;

class BulkChange
{
    public const string PROPERTY_SEPARATAOR = '-';

    /**
     * @param class-string<TaskType> $bluePrintClassName
     */
    public function __construct(
        private readonly string $bluePrintClassName,
        private readonly EntityPropertyMappingHelper $propertyMappingHelper,
        private readonly ChangeSet $changeSet,
        private readonly ChangeFactory $changeFactory,
    ) {
    }

    public function __get(string $propertyPath): NoChange
    {
        return $this->changeFactory->createNoChange();
    }

    public function __isset(string $name): bool
    {
        $reflectionClass = new \ReflectionClass($this);

        return !$reflectionClass->hasProperty($name);
    }

    public function __set(string $propertyPath, mixed $value): void
    {
        $propertyPath = str_replace(self::PROPERTY_SEPARATAOR, '.', $propertyPath);

        if (
            null !== $value
            && $this->propertyMappingHelper->isAssociativeProperty($propertyPath, $this->bluePrintClassName)
        ) {
            $value = $this->propertyMappingHelper->findAssociatedEntityByIdentifier(
                $propertyPath,
                $value,
                $this->bluePrintClassName
            );
        }

        $this->addChange($this->changeFactory->create($propertyPath, $value));
    }

    public function getChangeSet(): ChangeSet
    {
        return $this->changeSet;
    }

    public function removeChange(string $property): void
    {
        $this->changeSet->removeChange(str_replace(self::PROPERTY_SEPARATAOR, '.', $property));
    }

    public function addChange(Change $change): void
    {
        $this->changeSet->addChange($change);
    }
}
