<?php

declare(strict_types=1);
namespace U2\Task\DataSource;

use Symfony\Bundle\SecurityBundle\Security;
use U2\DataSourcery\DataSource\Configuration\DataSourceConfigurationInterface;
use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\User;
use U2\Event\DataSourcery\PostGenerateQueryBuilderEvent;
use U2\Repository\UnitRepository;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\TaskTypeKnowledge;
use U2\User\CurrentUserProvider;
use U2\Workflow\Traits\WorkflowableAwareDataSourceConfigurationTrait;
use U2\Workflow\WorkflowManager;

abstract class AbstractTaskTypeDataSourceConfiguration implements DataSourceConfigurationInterface
{
    use WorkflowableAwareDataSourceConfigurationTrait;

    public function __construct(
        protected readonly CurrentUserProvider $currentUserProvider,
        protected readonly WorkflowManager $workflowManager,
        protected readonly UnitRepository $unitRepository,
        protected readonly Security $security,
    ) {
    }

    public function buildDataSource(DataSourceBuilder $builder): void
    {
        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                static function (PostGenerateQueryBuilderEvent $event): void {
                    $event->queryBuilder->groupBy($event->fromAlias . '.id');
                    $event->queryBuilder->addSelect(
                        [
                            'COUNT(DISTINCT reviews.id) review_count',
                        ]
                    );
                    $event->queryBuilder->leftJoin("$event->fromAlias.task", 'task');
                    $event->queryBuilder->leftJoin('task.reviews', 'reviews');
                    $event->queryBuilder->leftJoin('task.reporter', 'reporter');
                }
            );

        $this->addAuthorisationRestrictions($builder);
        $this->addFileCount($builder);

        UnitFieldAdder::add($builder);
        SourceIdFieldAdder::add($builder);
        TaskIdFieldAdder::add($builder);
        TaskAssigneeFieldAdder::add($builder);
        TaskCreatedByFieldAdder::add($builder);
        TaskUpdatedByByFieldAdder::add($builder);

        $builder
            ->addSearchField(
                'SearchText',
                'search_text'
            )
            ->addField(
                'Id',
                'number',
                'id'
            )
            ->addField(
                'DueDate',
                'date',
                'task.dueDate'
            )
            ->addField(
                'Imported',
                'boolean',
                'task.imported'
            )
            ->addField(
                'StatusName',
                'string',
                'task.status.name',
                [
                    'choices' => $this->getStatusChoices(),
                ]
            )
            ->addField(
                'StatusType',
                'string',
                'task.status.type',
                [
                    'choices' => $this->getStatusTypes(),
                ]
            )
            ->addVectorField(
                'Status',
                'string',
                'task.status.name',
                [
                    'name' => 'StatusName',
                    'type' => 'StatusType',
                ],
                [
                    'choices' => $this->getStatusChoices(),
                ]
            )
            ->addField(
                'CreatedAt',
                'date',
                'task.createdAt'
            )
            ->addField(
                'ReporterId',
                'string',
                'task.reporter.id',
                [
                    'choices' => [
                        'repository' => User::class,
                        'field' => 'id',
                    ],
                ]
            )
            ->addField(
                'ReporterUsername',
                'string',
                'task.reporter.username',
                [
                    'choices' => [
                        'repository' => User::class,
                        'field' => 'username',
                    ],
                ]
            )
            ->addVectorField(
                'Reporter',
                'string',
                'task.reporter.username',
                [
                    'username' => 'ReporterUsername',
                    'id' => 'ReporterId',
                ],
                [
                    'choices' => [
                        'repository' => User::class,
                        'field' => 'username',
                    ],
                ]
            )
            ->addField(
                'UpdatedAt',
                'date',
                'task.updatedAt'
            )
            ->addField(
                'Description',
                'string',
                'task.description'
            )
            ->addNativeField(
                'ReviewCount',
                'number',
                'review_count'
            );
    }

    protected function getWorkflowManager(): WorkflowManager
    {
        return $this->workflowManager;
    }

    protected function addAuthorisationRestrictions(DataSourceBuilder $builder): void
    {
        if ($this->ignorePermissions()) {
            return;
        }

        $builder
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                function (PostGenerateQueryBuilderEvent $event): void {
                    $user = $this->currentUserProvider->get();
                    $userAssignedUnits = $this->unitRepository->findUserAssigned($user);

                    $event->queryBuilder->andWhere($event->fromAlias . '.unit IN (:units)');
                    $event->queryBuilder->setParameter(':units', $userAssignedUnits);
                });
    }

    protected function addFileCount(DataSourceBuilder $builder): void
    {
        $builder
            ->addNativeField(
                'Files',
                'number',
                'count_files'
            )
            ->addEventListener(
                PostGenerateQueryBuilderEvent::class,
                static function (PostGenerateQueryBuilderEvent $event): void {
                    $event->queryBuilder->leftJoin('task.files', 'files');
                    $event->queryBuilder->addSelect(
                        [
                            'COUNT(DISTINCT files.id) count_files',
                        ]
                    );
                });
    }

    protected function ignorePermissions(): bool
    {
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[static::getEntityClass()];

        return $this->security->isGranted($authorizationItem->value . ':' . AuthorizationRight::SUPERVISE->value);
    }
}
