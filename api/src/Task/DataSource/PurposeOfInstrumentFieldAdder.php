<?php

declare(strict_types=1);
namespace U2\Task\DataSource;

use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Task\Field\PurposeOfInstrumentTypes;

class PurposeOfInstrumentFieldAdder
{
    public static function add(DataSourceBuilder $builder): void
    {
        $builder->addField(
            'PurposeOfInstrument',
            'string',
            'purposeOfInstrument',
            [
                'choices' => array_flip(PurposeOfInstrumentTypes::getReadableMap()),
            ]
        );
    }
}
