default:
  calls:
    error_reporting: 14335 # E_ALL & ~E_USER_DEPRECATED
  gherkin:
    filters:
      tags: '~@disabled'
  extensions:
    <PERSON><PERSON><PERSON><PERSON><PERSON>\MinkPhpWebdriverExtension: ~
    Behat\MinkExtension:
      base_url: 'https://test.u2.web'
      files_path: '%paths.base%/tests/behat/fixtures/'
      default_session: chrome
      sessions:
        chrome:
          webdriver:
            wd_host: 'http://selenium:4444/wd/hub'
            browser: chrome
            capabilities:
              browser: chrome
              acceptSslCerts: true
              chrome:
              extra_capabilities:
                acceptInsecureCerts: true
                goog:chromeOptions:
                  # see https://sites.google.com/a/chromium.org/chromedriver/capabilities
                  # see https://peter.sh/experiments/chromium-command-line-switches/
                  # for some reason the order of these args matter. 🤷
                  args:
                    #- start-maximized=true #if you use this you will need to remove "disable-gpu"
                    - disable-gpu=true
                    - window-size=1366,768
                    - disable-dev-shm-usage=true
                    - headless=true
                    - no-sandbox=true
                    #- auto-open-devtools-for-tabs=true
                    #- content-shell-host-window-size=1920,1080
                    #- app-shell-host-window-size=1920,1080
        firefox:
          webdriver:
            wd_host: 'http://selenium:4444/wd/hub'
            browser: firefox
            capabilities:
              firefox: ~
              browser: firefox
              acceptSslCerts: true
              extra_capabilities:
                moz:firefoxOptions:
                  # for some reason we need the empty string to make headless work. 🤷‍
                  args: [ '--headless', "" ]
                acceptInsecureCerts: true

    Behatch\Extension: ~
    Bex\Behat\StepTimeLoggerExtension:
      output_directory: /app/api/var/logs/behat_statistics
      output: [ csv ]
      enabled_always: false
    Chekote\BehatRetryExtension:
      strictKeywords: false
      #timeout: 60
    FriendsOfBehat\SymfonyExtension:
      bootstrap: src/bootstrap.php
      kernel:
        environment: behat
        debug: true
        class: U2\Kernel
    Bex\Behat\SkipTestsExtension:
      skip_tags:
        # If a feature flag is "on" by default, we must skip the steps where the feature is tagged as "off"
        # This is because there is currently no way to disable a flag that is off by default.
        # To do this add the tag "feature-off-<feature_name>" here and to the scenario
  suites:
    core:
      paths: [ '%paths.base%/tests/behat/core' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\AuditLogContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\Hook\CacheContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\PermissionableEntityContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\Setup\RoleContext
        - U2\Behat\Context\Setup\UserContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\Transform\ExchangeRateTableContext
        - U2\Behat\Context\Transform\FileTableContext
        - U2\Behat\Context\Transform\ItemFormulaTableContext
        - U2\Behat\Context\Transform\ItemTableContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\Transform\SystemSettingsTableContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\UnitHierarchyContext
    contractmanagement:
      paths: [ '%paths.base%/tests/behat/cm' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\Setup\UserContext
    apm:
      paths: [ '%paths.base%/tests/behat/apm' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Transform\ExchangeRateTableContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\Setup\UserContext
    dtm:
      filters:
        tags: '~@disabled'
      paths: [ '%paths.base%/tests/behat/dtm' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Hook\CacheContext
        - U2\Behat\Context\Transform\ExchangeRateTableContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\PermissionableEntityContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\Transform\ItemTableContext
        - U2\Behat\Context\Transform\ItemFormulaTableContext
        - U2\Behat\Context\Transform\UserContext
    igt:
      paths: [ '%paths.base%/tests/behat/igt' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Transform\ExchangeRateTableContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\Setup\UserContext
    tam:
      paths: [ '%paths.base%/tests/behat/tam' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\Setup\UserContext
    tcm:
      paths: [ '%paths.base%/tests/behat/tcm' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\Setup\UserContext
    tpm:
      paths: [ '%paths.base%/tests/behat/tpm' ]
      contexts:
        - Behatch\Context\BrowserContext
        - Behatch\Context\DebugContext
        - U2\Behat\Context\Hook\DoctrineOrmContext
        - U2\Behat\Context\Transform\ExchangeRateTableContext
        - U2\Behat\Context\Transform\UserContext
        - U2\Behat\Context\Transform\RoleContext
        - U2\Behat\Context\Transform\SystemSettingsTableContext
        - U2\Behat\Context\CoreContext
        - U2\Behat\Context\Transform\FileTableContext
        - U2\Behat\Context\FeatureContext
        - U2\Behat\Context\FormContext
        - U2\Behat\Context\PermissionableEntityContext
        - U2\Behat\Context\ReviewContext
        - U2\Behat\Context\TableContext
        - U2\Behat\Context\UnitContext
        - U2\Behat\Context\EntityContext
        - U2\Behat\Context\StructuredDocumentSectionContext
        - U2\Behat\Context\Setup\UserContext
