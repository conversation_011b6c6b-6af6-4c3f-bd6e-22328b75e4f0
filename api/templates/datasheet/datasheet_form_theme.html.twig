{% extends 'default.datasheet_form_theme.html.twig' %}

{% block form_widget_simple %}
  {{- value -}}
{% endblock form_widget_simple %}

{% block checkbox_widget %}
  {{- checked -}}
{% endblock checkbox_widget %}

{% block textarea_widget %}
  {{- value -}}
{% endblock textarea_widget %}

{% block percent_widget %}
  {{- data -}} %
{% endblock percent_widget %}

{% block u2_money_widget %}
  {{- data -}}
{% endblock u2_money_widget %}

{% block number_widget %}
  {{- data -}}
{% endblock number_widget %}

{% block checkbox_item_value_widget %}
  {{- form_widget(form.isChecked, {'attr': {
    'class': attr.class|default(''),
    'data-item-refid': data.item.refId,
    'errors': form.isChecked.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
  }}) -}}
{% endblock checkbox_item_value_widget %}

{% block diff_item_value_widget %}
  {{- form_widget(form.diff, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.diff.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder':'0',
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock diff_item_value_widget %}

{% block money_item_value_widget %}
  {{- form_widget(form.localCurrencyValue, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.localCurrencyValue.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder': (disabled ? '': '0'),
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock money_item_value_widget %}

{% block number_item_value_widget %}
  {{- form_widget(form.value, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.value.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder': (disabled ? '': '0'),
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock number_item_value_widget %}

{% block percent_item_value_widget %}
  {{- form_widget(form.value, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.value.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder':'0',
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock percent_item_value_widget %}

{% block text_item_value_widget %}
  {{- form_widget(form.comment, {'attr': {
    'class': attr.class|default(''),
    'data-item-refid': data.item.refId,
    'errors': form.comment.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder': (disabled ? '': 'u2.form.text_field_placeholder'|trans)
  }}) -}}
{% endblock text_item_value_widget %}
