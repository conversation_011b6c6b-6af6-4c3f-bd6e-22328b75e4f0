{"name": "universalunits/u2", "type": "project", "license": "proprietary", "description": "UniversalUnits", "require": {"php": ">=8.4", "ext-bcmath": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-pdo": "*", "ext-redis": "*", "ext-zip": "*", "api-platform/core": "^4.1.17", "bex/behat-step-time-logger": "^2.0", "doctrine/doctrine-bundle": "^2.15.0", "doctrine/common": "^3.5.0", "doctrine/doctrine-fixtures-bundle": "^4.1.0", "doctrine/doctrine-migrations-bundle": "^3.4.2", "dragonmantank/cron-expression": "^3.4", "egulias/email-validator": "^4.0.4", "exercise/htmlpurifier-bundle": "^5.1", "flagception/flagception-bundle": "^6.0.0", "friendsofsymfony/jsrouting-bundle": "^3.5.2", "gedmo/doctrine-extensions": "^3.20.0", "goetas-webservices/xsd2php-runtime": "^0.2.17", "hautelook/alice-bundle": "^2.15.1", "jms/serializer-bundle": "^5.5.1", "knplabs/knp-menu-bundle": "^3.6", "league/flysystem": "^3.29.1", "league/flysystem-aws-s3-v3": "^3.29.0", "lexik/jwt-authentication-bundle": "^3.1.1", "oneup/flysystem-bundle": "^4.12.4", "oro/doctrine-extensions": "^3.0-alpha3", "phpdocumentor/reflection-docblock": "^5.6.2", "phpoffice/phpspreadsheet": "^4.3.1", "roave/better-reflection": "^6.59", "scheb/2fa-bundle": "^7.10.0", "scheb/2fa-email": "^7.10.0", "scheb/2fa-trusted-device": "^7.10.0", "sentry/sentry-symfony": "^5.2", "stof/doctrine-extensions-bundle": "^1.14.0", "symfony/asset": "^7.3", "symfony/dependency-injection": "^7.3.0", "symfony/dotenv": "^7.3", "symfony/expression-language": "^7.3", "symfony/flex": "^2.7.1", "symfony/form": "^7.3.0", "symfony/framework-bundle": "^7.3.0", "symfony/http-foundation": "^7.3.0", "symfony/lock": "^7.3.0", "symfony/mailer": "^7.3.0", "symfony/messenger": "^7.3.0", "symfony/mime": "^7.3.0", "symfony/monolog-bundle": "^3.10", "symfony/process": "^7.3.0", "symfony/property-info": "^7.3.0", "symfony/redis-messenger": "^7.3.0", "symfony/requirements-checker": "^2.0.3", "symfony/runtime": "^7.3.0", "symfony/security-acl": "^3.3.4", "symfony/security-core": "^7.3.0", "symfony/security-http": "^7.3.0", "symfony/stopwatch": "^7.3.0", "symfony/translation": "^7.3.0", "symfony/twig-bundle": "^7.3", "symfony/uid": "^7.3", "symfony/validator": "^7.3.0", "symfonycasts/micro-mapper": "^0.2.1", "tecnickcom/tcpdf": "^6.10.0", "theofidry/alice-data-fixtures": "^1.9.0", "twig/extra-bundle": "^3.21", "twig/intl-extra": "^3.21", "willdurand/js-translation-bundle": "^7.0.0", "zenstruck/foundry": "^2.6.0"}, "require-dev": {"behat/behat": "^3.22.0", "behat/mink": "^1.12", "behat/mink-selenium2-driver": "^1.7", "bex/behat-skip-tests": "^1.2", "chekote/behat-retry-extension": "^1.1.1", "cweagans/composer-patches": "^1.7.3", "ergebnis/phpunit-slow-test-detector": "^2.19.1", "fakerphp/faker": "^1.24.1", "friends-of-behat/mink-extension": "^2.7.5", "friends-of-behat/symfony-extension": "^2.6.2", "friendsofphp/php-cs-fixer": "^3.75.0", "jetbrains/phpstorm-attributes": "^1.2", "justinrainbow/json-schema": "^6.4.2", "mikey179/vfsstream": "^1.6.12", "oleg-andreyev/mink-phpwebdriver-extension": "^1.0", "php-webdriver/webdriver": "^1.15.2", "phpstan/phpstan": "^2.1.17", "phpstan/phpstan-deprecation-rules": "^2.0.3", "phpstan/phpstan-doctrine": "^2.0.3", "phpstan/phpstan-phpunit": "^2.0.6", "phpstan/phpstan-strict-rules": "^2.0.4", "phpstan/phpstan-symfony": "^2.0.6", "phpunit/phpunit": "^12.2.3", "pyrech/composer-changelogs": "^2.1", "rector/rector": "^2.0.18", "soyuka/contexts": "^3.3.13", "symfony/browser-kit": "^7.3.0", "symfony/css-selector": "^7.3", "symfony/debug-bundle": "^7.3", "symfony/http-client": "^7.3.0", "symfony/maker-bundle": "^1.63.0", "symfony/phpunit-bridge": "^7.3.0", "symfony/web-profiler-bundle": "^7.3.0", "symplify/vendor-patches": "^11.4.1", "zenstruck/mailer-test": "^1.4.2", "zenstruck/messenger-test": "^1.11"}, "config": {"audit": {"ignore": {"CVE-2025-31481": "We do not use graphQL so we not affected by this vulnerability. See https://github.com/advisories/GHSA-cg3c-245w-728m", "CVE-2025-31485": "We do not use graphQL so we not affected by this vulnerability. See https://github.com/advisories/GHSA-428q-q3vv-3fq3"}, "abandoned": "report"}, "github-protocols": ["https"], "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "pyrech/composer-changelogs": true, "symfony/flex": true, "symfony/runtime": true, "php-http/discovery": true, "cweagans/composer-patches": true}}, "autoload": {"psr-4": {"U2\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\U2\\": "tests/phpunit", "Tests\\Functional\\U2\\": "tests/phpunit/functional", "Tests\\Integration\\U2\\": "tests/phpunit/integration", "Tests\\Migration\\U2\\": "tests/phpunit/migration", "Tests\\Unit\\U2\\": "tests/phpunit/unit"}}, "replace": {"symfony/polyfill-iconv": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "auto-scripts": {"requirements-checker": "script", "composer audit": "script", "cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "phpstan": "phpstan analyse -v", "phpstan-baseline": "phpstan analyse --generate-baseline", "php-cs-fixer": "PHP_CS_FIXER_IGNORE_ENV=true php-cs-fixer fix", "rector-fix": "rector", "rector-preview": "rector --dry-run", "rector-ci": "rector --dry-run --no-progress-bar"}, "scripts-descriptions": {"rector-fix": "Fixing the code according to the Rector Rules", "rector-preview": "Showing the suggested changes from <PERSON>", "rector-ci": "Executing Rector in a CI workflow"}, "conflict": {"symfony/symfony": "*"}, "extra": {"patches": {"gedmo/doctrine-extensions": ["patches/gedmo-doctrine-extensions-src-timestampable-mapping-event-adapter-orm-php.patch", "patches/gedmo-doctrine-extensions-src-abstracttrackinglistener-php.patch"]}, "enable-patching": true, "symfony": {"allow-contrib": false, "require": "7.3.*"}, "composer-exit-on-patch-failure": true}}