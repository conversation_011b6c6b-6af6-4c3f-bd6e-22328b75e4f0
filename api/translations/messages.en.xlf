<?xml version="1.0" encoding="utf-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:2.0" version="2.0" srcLang="en" trgLang="en">
  <file id="messages.en">
    <unit id="dYSSMoM" name="u2.results_per_page">
      <segment>
        <source>u2.results_per_page</source>
        <target>Records per page</target>
      </segment>
    </unit>
    <unit id="N6Z.32N" name="u2.reference">
      <segment>
        <source>u2.reference</source>
        <target>Reference</target>
      </segment>
    </unit>
    <unit id="ygKFrLe" name="u2.clear_all">
      <segment>
        <source>u2.clear_all</source>
        <target>Clear all</target>
      </segment>
    </unit>
    <unit id="nyrZY5U" name="u2.breakdown">
      <segment>
        <source>u2.breakdown</source>
        <target>Breakdown</target>
      </segment>
    </unit>
    <unit id="VpLZ5lU" name="u2.search.refine_search">
      <segment>
        <source>u2.search.refine_search</source>
        <target>More entries were found than can be displayed. Try refining your search if you can't find what you're looking for.</target>
      </segment>
    </unit>
    <unit id="vvo1Ucg" name="u2.http_401">
      <segment>
        <source>u2.http_401</source>
        <target>You need to login in order to view this page or perform this action.</target>
      </segment>
    </unit>
    <unit id="PwHfSi8" name="u2.session_expired">
      <segment>
        <source>u2.session_expired</source>
        <target>Session expired.</target>
      </segment>
    </unit>
    <unit id="W6SFStU" name="APM Transaction">
      <segment>
        <source>APM Transaction</source>
        <target>APM Transaction</target>
      </segment>
    </unit>
    <unit id="CqWXoak" name="u2.max_system_upload_size_exceeded">
      <segment>
        <source>u2.max_system_upload_size_exceeded</source>
        <target>The defined "Max upload Size" for the application exceeds the maximum upload size defined by the system (%systemMaxUploadSize%B).</target>
      </segment>
    </unit>
    <unit id="Jm_wxQk" name="u2.regenerate">
      <segment>
        <source>u2.regenerate</source>
        <target>Regenerate</target>
      </segment>
    </unit>
    <unit id="Q1yp.4R" name="Aggregate Excess of Loss">
      <segment>
        <source>Aggregate Excess of Loss</source>
        <target>Aggregate Excess of Loss</target>
      </segment>
    </unit>
    <unit id="EXEeroI" name="u2_core.api_key.copy_warning">
      <segment>
        <source>u2_core.api_key.copy_warning</source>
        <target>Make sure to copy your personal access token now as you will not be able to see this again.</target>
      </segment>
    </unit>
    <unit id="etXBgVN" name="Assumption of Liabilities">
      <segment>
        <source>Assumption of Liabilities</source>
        <target>Assumption of Liabilities</target>
      </segment>
    </unit>
    <unit id="yN68sn6" name="Bond">
      <segment>
        <source>Bond</source>
        <target>Bond</target>
      </segment>
    </unit>
    <unit id="YabHT3d" name="Bond - Collateralized">
      <segment>
        <source>Bond - Collateralized</source>
        <target>Bond - Collateralized</target>
      </segment>
    </unit>
    <unit id="kbz_NIu" name="Bond - Uncollateralized">
      <segment>
        <source>Bond - Uncollateralized</source>
        <target>Bond - Uncollateralized</target>
      </segment>
    </unit>
    <unit id="LxT9uVW" name="Borrower">
      <segment>
        <source>Borrower</source>
        <target>Borrower</target>
      </segment>
    </unit>
    <unit id="9eimhH3" name="Contingent Liabilities">
      <segment>
        <source>Contingent Liabilities</source>
        <target>Contingent Liabilities</target>
      </segment>
    </unit>
    <unit id="lcNV0WA" name="Creditor">
      <segment>
        <source>Creditor</source>
        <target>Creditor</target>
      </segment>
    </unit>
    <unit id="6aQIzln" name="Derivatives - Forwards">
      <segment>
        <source>Derivatives - Forwards</source>
        <target>Derivatives - Forwards</target>
      </segment>
    </unit>
    <unit id="IF4ei1p" name="Derivatives - Futures">
      <segment>
        <source>Derivatives - Futures</source>
        <target>Derivatives - Futures</target>
      </segment>
    </unit>
    <unit id="vayEqAi" name="Derivatives - Options">
      <segment>
        <source>Derivatives - Options</source>
        <target>Derivatives - Options</target>
      </segment>
    </unit>
    <unit id="Umk2Imq" name="Derivatives - Others">
      <segment>
        <source>Derivatives - Others</source>
        <target>Derivatives - Others</target>
      </segment>
    </unit>
    <unit id="FQMD8RS" name="Efficient Portfolio Management">
      <segment>
        <source>Efficient Portfolio Management</source>
        <target>Efficient Portfolio Management</target>
      </segment>
    </unit>
    <unit id="FCyBv6_" name="Equity Type - Dividends">
      <segment>
        <source>Equity Type - Dividends</source>
        <target>Equity Type - Dividends</target>
      </segment>
    </unit>
    <unit id="0e9NwUr" name="Equity Type - Others">
      <segment>
        <source>Equity Type - Others</source>
        <target>Equity Type - Others</target>
      </segment>
    </unit>
    <unit id="4qXVNjo" name="Equity Type - Shares/Participation">
      <segment>
        <source>Equity Type - Shares/Participation</source>
        <target>Equity Type - Shares/Participation</target>
      </segment>
    </unit>
    <unit id="Gup.tQM" name="Excess of Loss (per Back-Up)">
      <segment>
        <source>Excess of Loss (per Back-Up)</source>
        <target>Excess of Loss (per Back-Up)</target>
      </segment>
    </unit>
    <unit id="tHt0xbq" name="Excess of Loss (per Event and per Risk)">
      <segment>
        <source>Excess of Loss (per Event and per Risk)</source>
        <target>Excess of Loss (per Event and per Risk)</target>
      </segment>
    </unit>
    <unit id="fJytWg8" name="Excess of Loss (per Event)">
      <segment>
        <source>Excess of Loss (per Event)</source>
        <target>Excess of Loss (per Event)</target>
      </segment>
    </unit>
    <unit id="9..2m3m" name="Excess of Loss (per Risk)">
      <segment>
        <source>Excess of Loss (per Risk)</source>
        <target>Excess of Loss (per Risk)</target>
      </segment>
    </unit>
    <unit id="88Qpfx5" name="Excess of Loss with Basis Risk">
      <segment>
        <source>Excess of Loss with Basis Risk</source>
        <target>Excess of Loss with Basis Risk</target>
      </segment>
    </unit>
    <unit id="Ye6qiX6" name="FX">
      <segment>
        <source>FX</source>
        <target>FX</target>
      </segment>
    </unit>
    <unit id="M3HMSpU" name="Facultative Non-Proportional">
      <segment>
        <source>Facultative Non-Proportional</source>
        <target>Facultative Non-Proportional</target>
      </segment>
    </unit>
    <unit id="S_i4if1" name="Facultative Proportional">
      <segment>
        <source>Facultative Proportional</source>
        <target>Facultative Proportional</target>
      </segment>
    </unit>
    <unit id="yX15nFX" name="Financial Reinsurance">
      <segment>
        <source>Financial Reinsurance</source>
        <target>Financial Reinsurance</target>
      </segment>
    </unit>
    <unit id="fpPMmb." name="Fixed">
      <segment>
        <source>Fixed</source>
        <target>Fixed</target>
      </segment>
    </unit>
    <unit id="FeDi5aC" name="Guarantees - Credit Protection">
      <segment>
        <source>Guarantees - Credit Protection</source>
        <target>Guarantees - Credit Protection</target>
      </segment>
    </unit>
    <unit id="P4R7Wti" name="Guarantees - Others">
      <segment>
        <source>Guarantees - Others</source>
        <target>Guarantees - Others</target>
      </segment>
    </unit>
    <unit id="9NHAtx1" name="Interest">
      <segment>
        <source>Interest</source>
        <target>Interest</target>
      </segment>
    </unit>
    <unit id="jpPcQGp" name="Loan">
      <segment>
        <source>Loan</source>
        <target>Loan</target>
      </segment>
    </unit>
    <unit id="kSGHkVF" name="Loan - Collateralized">
      <segment>
        <source>Loan - Collateralized</source>
        <target>Loan - Collateralized</target>
      </segment>
    </unit>
    <unit id="owCbAVq" name="Loan - Uncollateralized">
      <segment>
        <source>Loan - Uncollateralized</source>
        <target>Loan - Uncollateralized</target>
      </segment>
    </unit>
    <unit id="nwAKE.C" name="Macro Hedge">
      <segment>
        <source>Macro Hedge</source>
        <target>Macro Hedge</target>
      </segment>
    </unit>
    <unit id="CUeGHvq" name="Matching Assets and Liabilities Cash Flows">
      <segment>
        <source>Matching Assets and Liabilities Cash Flows</source>
        <target>Matching Assets and Liabilities Cash Flows</target>
      </segment>
    </unit>
    <unit id="mZWcmvy" name="Micro Hedge">
      <segment>
        <source>Micro Hedge</source>
        <target>Micro Hedge</target>
      </segment>
    </unit>
    <unit id="ws0gZru" name="Mixed">
      <segment>
        <source>Mixed</source>
        <target>Mixed</target>
      </segment>
    </unit>
    <unit id="QSlyidX" name="Other Asset Transfer - Others">
      <segment>
        <source>Other Asset Transfer - Others</source>
        <target>Other Asset Transfer - Others</target>
      </segment>
    </unit>
    <unit id="oVEGMi_" name="Other Asset Transfer - Properties">
      <segment>
        <source>Other Asset Transfer - Properties</source>
        <target>Other Asset Transfer - Properties</target>
      </segment>
    </unit>
    <unit id="C6TGMPL" name="Other Non-Proportional Treaties">
      <segment>
        <source>Other Non-Proportional Treaties</source>
        <target>Other Non-Proportional Treaties</target>
      </segment>
    </unit>
    <unit id="vR9lf0Z" name="Other Proportional Treaties">
      <segment>
        <source>Other Proportional Treaties</source>
        <target>Other Proportional Treaties</target>
      </segment>
    </unit>
    <unit id="sT5mEGg" name="Others">
      <segment>
        <source>Others</source>
        <target>Others</target>
      </segment>
    </unit>
    <unit id="FhIyxXe" name="Promissory Note">
      <segment>
        <source>Promissory Note</source>
        <target>Promissory Note</target>
      </segment>
    </unit>
    <unit id="mUoITxb" name="Promissory Note - Collateralized">
      <segment>
        <source>Promissory Note - Collateralized</source>
        <target>Promissory Note - Collateralized</target>
      </segment>
    </unit>
    <unit id="S80sCih" name="Promissory Note - Uncollateralized">
      <segment>
        <source>Promissory Note - Uncollateralized</source>
        <target>Promissory Note - Uncollateralized</target>
      </segment>
    </unit>
    <unit id="YIvTxx8" name="Quota Share">
      <segment>
        <source>Quota Share</source>
        <target>Quota Share</target>
      </segment>
    </unit>
    <unit id="a0JEHDn" name="Reinstatement Cover">
      <segment>
        <source>Reinstatement Cover</source>
        <target>Reinstatement Cover</target>
      </segment>
    </unit>
    <unit id="foTGnzI" name="Senior">
      <segment>
        <source>Senior</source>
        <target>Senior</target>
      </segment>
    </unit>
    <unit id="ECFtMy9" name="Stop Loss">
      <segment>
        <source>Stop Loss</source>
        <target>Stop Loss</target>
      </segment>
    </unit>
    <unit id="eIrgoXV" name="Subordinate">
      <segment>
        <source>Subordinate</source>
        <target>Subordinate</target>
      </segment>
    </unit>
    <unit id="3uCJKBL" name="Surplus">
      <segment>
        <source>Surplus</source>
        <target>Surplus</target>
      </segment>
    </unit>
    <unit id="d8XWhaP" name="Swaps - Credit Default">
      <segment>
        <source>Swaps - Credit Default</source>
        <target>Swaps - Credit Default</target>
      </segment>
    </unit>
    <unit id="snKc4M0" name="Swaps - Currency">
      <segment>
        <source>Swaps - Currency</source>
        <target>Swaps - Currency</target>
      </segment>
    </unit>
    <unit id="1nCTjv6" name="Swaps - Interest Rate">
      <segment>
        <source>Swaps - Interest Rate</source>
        <target>Swaps - Interest Rate</target>
      </segment>
    </unit>
    <unit id="NESlrIK" name="Swaps - Others">
      <segment>
        <source>Swaps - Others</source>
        <target>Swaps - Others</target>
      </segment>
    </unit>
    <unit id="74kMEzS" name="Unlimited Excess of Loss">
      <segment>
        <source>Unlimited Excess of Loss</source>
        <target>Unlimited Excess of Loss</target>
      </segment>
    </unit>
    <unit id="NzlRMnV" name="Variable">
      <segment>
        <source>Variable</source>
        <target>Variable</target>
      </segment>
    </unit>
    <unit id="JIO.DGg" name="Variable Quota Share">
      <segment>
        <source>Variable Quota Share</source>
        <target>Variable Quota Share</target>
      </segment>
    </unit>
    <unit id="iUJg6Sb" name="u2.access_denied">
      <segment>
        <source>u2.access_denied</source>
        <target>Access Denied</target>
      </segment>
    </unit>
    <unit id="1vHTtB3" name="u2.access_type.public">
      <segment>
        <source>u2.access_type.public</source>
        <target>Public</target>
      </segment>
    </unit>
    <unit id="VUAZ7wc" name="u2.not_public">
      <segment>
        <source>u2.not_public</source>
        <target>Not public</target>
      </segment>
    </unit>
    <unit id="lPj_LvD" name="u2.access_type.public.help">
      <segment>
        <source>u2.access_type.public.help</source>
        <target>All users will be able to view this file</target>
      </segment>
    </unit>
    <unit id="uUvOlZZ" name="u2.add">
      <segment>
        <source>u2.add</source>
        <target>Add</target>
      </segment>
    </unit>
    <unit id="rIFUJZh" name="u2.add_comment_successful">
      <segment>
        <source>u2.add_comment_successful</source>
        <target>Comment added.</target>
      </segment>
    </unit>
    <unit id="_6SB5rx" name="u2.address.city">
      <segment>
        <source>u2.address.city</source>
        <target>City</target>
      </segment>
    </unit>
    <unit id="8voRMPS" name="u2.address.line_1">
      <segment>
        <source>u2.address.line_1</source>
        <target>Line 1</target>
      </segment>
    </unit>
    <unit id="fdyKO_G" name="u2.address.line_2">
      <segment>
        <source>u2.address.line_2</source>
        <target>Line 2</target>
      </segment>
    </unit>
    <unit id="kPr51Ro" name="u2.address.line_3">
      <segment>
        <source>u2.address.line_3</source>
        <target>Line 3</target>
      </segment>
    </unit>
    <unit id="1SVm9Rl" name="u2.address.post_code">
      <segment>
        <source>u2.address.post_code</source>
        <target>Postcode</target>
      </segment>
    </unit>
    <unit id="97wDvRv" name="u2.address.state">
      <segment>
        <source>u2.address.state</source>
        <target>State</target>
      </segment>
    </unit>
    <unit id="ynxxvN1" name="u2.all">
      <segment>
        <source>u2.all</source>
        <target>All</target>
      </segment>
    </unit>
    <unit id="K03.HZv" name="u2.application_currency">
      <segment>
        <source>u2.application_currency</source>
        <target>Application currency</target>
      </segment>
    </unit>
    <unit id="ta9gkKL" name="u2.application_currency.warning">
      <segment>
        <source>u2.application_currency.warning</source>
        <target>Changing this value will cause major changes to the functionality and data of the application. Do not change unless you are sure about the consequences.</target>
      </segment>
    </unit>
    <unit id="RfOSoDY" name="u2.are_you_sure">
      <segment>
        <source>u2.are_you_sure</source>
        <target>Are you sure?</target>
      </segment>
    </unit>
    <unit id="qf17vBi" name="u2.assign">
      <segment>
        <source>u2.assign</source>
        <target>Assign</target>
      </segment>
    </unit>
    <unit id="k_0YSUj" name="u2.authentication.error.account_expired">
      <segment>
        <source>u2.authentication.error.account_expired</source>
        <target>Account has expired.</target>
      </segment>
    </unit>
    <unit id="0BXVJgw" name="u2.authentication.error.account_locked">
      <segment>
        <source>u2.authentication.error.account_locked</source>
        <target>Your account is locked. Please reset your password.</target>
      </segment>
    </unit>
    <unit id="YwMB2Wv" name="u2.authentication.error.bad_credentials">
      <segment>
        <source>u2.authentication.error.bad_credentials</source>
        <target>Invalid credentials.</target>
      </segment>
    </unit>
    <unit id="MacANj." name="u2.authentication.error.password_expired">
      <segment>
        <source>u2.authentication.error.password_expired</source>
        <target>Your password expired. Please click the “Password forgotten” link (below) to request a password reset.</target>
      </segment>
    </unit>
    <unit id="N.k1znR" name="u2.authentication.password_reset.enter_username_or_email">
      <segment>
        <source>u2.authentication.password_reset.enter_username_or_email</source>
        <target>Please enter your username or email address:</target>
      </segment>
    </unit>
    <unit id="naKTjEq" name="u2.authentication.password_reset.error.email_sending_failed">
      <segment>
        <source>u2.authentication.password_reset.error.email_sending_failed</source>
        <target>An error occurred while sending the email. Please try again.</target>
      </segment>
    </unit>
    <unit id="tC.9sZz" name="u2.authentication.password_reset.error.password_reset_link_is_no_longer_valid">
      <segment>
        <source>u2.authentication.password_reset.error.password_reset_link_is_no_longer_valid</source>
        <target>The password reset link you clicked is no longer valid. Please try again.</target>
      </segment>
    </unit>
    <unit id="hIYvSC5" name="u2.authentication.password_reset.error.user_could_not_be_found">
      <segment>
        <source>u2.authentication.password_reset.error.user_could_not_be_found</source>
        <target>This user could not be found.</target>
      </segment>
    </unit>
    <unit id="HRsjA_w" name="u2.authentication.password_reset.notice.click_on_reset_password_to_send_an_email">
      <segment>
        <source>u2.authentication.password_reset.notice.click_on_reset_password_to_send_an_email</source>
        <target>Click on “Reset Password” to send the user an email to set his password.</target>
      </segment>
    </unit>
    <unit id="s7dNM9u" name="u2.authentication.password_reset.reset_password_for_given_username">
      <segment>
        <source>u2.authentication.password_reset.reset_password_for_given_username</source>
        <target>Reset Password: “%username%”</target>
      </segment>
    </unit>
    <unit id="WKMu5hW" name="u2.authentication.password_reset.success.email_has_been_sent">
      <segment>
        <source>u2.authentication.password_reset.success.email_has_been_sent</source>
        <target>The password reset request was successful. An email will be sent to the account if it exists.</target>
      </segment>
    </unit>
    <unit id="sExJrhM" name="u2.authentication.password_reset.success.reset_password_email_sent">
      <segment>
        <source>u2.authentication.password_reset.success.reset_password_email_sent</source>
        <target>An email with a reset password link was sent to %email_address%.</target>
      </segment>
    </unit>
    <unit id="kh0myy5" name="u2.authentication.two_factor.invalid_code">
      <segment>
        <source>u2.authentication.two_factor.invalid_code</source>
        <target>Invalid code</target>
      </segment>
    </unit>
    <unit id="C5SO4Wr" name="u2.authorization.rights.select_an_item_first">
      <segment>
        <source>u2.authorization.rights.select_an_item_first</source>
        <target>Select an authorization item to show the associated rights.</target>
      </segment>
    </unit>
    <unit id="twWqGln" name="u2.authorization.select_authorizations">
      <segment>
        <source>u2.authorization.select_authorizations</source>
        <target>Select authorizations</target>
      </segment>
    </unit>
    <unit id="gzeq925" name="u2.authorization.select_rights">
      <segment>
        <source>u2.authorization.select_rights</source>
        <target>Select rights</target>
      </segment>
    </unit>
    <unit id="cHZ6_Pu" name="u2.bulk_transition">
      <segment>
        <source>u2.bulk_transition</source>
        <target>Bulk Transition</target>
      </segment>
    </unit>
    <unit id="P1tzxWJ" name="u2.calendar.empty">
      <segment>
        <source>u2.calendar.empty</source>
        <target>Your calendar is clear for the next month.</target>
      </segment>
    </unit>
    <unit id="lCM5535" name="u2.cancel">
      <segment>
        <source>u2.cancel</source>
        <target>Cancel</target>
      </segment>
    </unit>
    <unit id="A58PgvZ" name="u2.change_parameters">
      <segment>
        <source>u2.change_parameters</source>
        <target>Change Parameters</target>
      </segment>
    </unit>
    <unit id="TNeEN3f" name="u2.checked">
      <segment>
        <source>u2.checked</source>
        <target>Checked</target>
      </segment>
    </unit>
    <unit id="EzaWOe4" name="u2.clear">
      <segment>
        <source>u2.clear</source>
        <target>Clear</target>
      </segment>
    </unit>
    <unit id=".xKIj1X" name="u2.close">
      <segment>
        <source>u2.close</source>
        <target>Close</target>
      </segment>
    </unit>
    <unit id="oAJ4Rix" name="u2.closed">
      <segment>
        <source>u2.closed</source>
        <target>Closed</target>
      </segment>
    </unit>
    <unit id="i2rHIkr" name="u2.comment.add_comment_field_placeholder">
      <segment>
        <source>u2.comment.add_comment_field_placeholder</source>
        <target>Add a comment here...</target>
      </segment>
    </unit>
    <unit id="15xWlIk" name="u2.comment.add_quote">
      <segment>
        <source>u2.comment.add_quote</source>
        <target>Add this text as a quote in a new comment.</target>
      </segment>
    </unit>
    <unit id="HfEwQSu" name="u2.configuration">
      <segment>
        <source>u2.configuration</source>
        <target>Configuration</target>
      </segment>
    </unit>
    <unit id="ZTGK1RW" name="u2.confirm">
      <segment>
        <source>u2.confirm</source>
        <target>Confirm</target>
      </segment>
    </unit>
    <unit id="o4kQEDV" name="u2.confirm_deletion">
      <segment>
        <source>u2.confirm_deletion</source>
        <target>Confirm deletion</target>
      </segment>
    </unit>
    <unit id="6ieHj1Z" name="u2.confirm_reset">
      <segment>
        <source>u2.confirm_reset</source>
        <target>Confirm reset</target>
      </segment>
    </unit>
    <unit id="tR.wBLZ" name="u2.confirm_user_unlock">
      <segment>
        <source>u2.confirm_user_unlock</source>
        <target>Confirm user unlocking</target>
      </segment>
    </unit>
    <unit id="pGNWXBp" name="u2.confirmation">
      <segment>
        <source>u2.confirmation</source>
        <target>Confirmation</target>
      </segment>
    </unit>
    <unit id="z099_6j" name="u2.content">
      <segment>
        <source>u2.content</source>
        <target>Content</target>
      </segment>
    </unit>
    <unit id="Enyq._M" name="u2.contract_date">
      <segment>
        <source>u2.contract_date</source>
        <target>Contract Date</target>
      </segment>
    </unit>
    <unit id="PJqgdMn" name="u2.contract_details">
      <segment>
        <source>u2.contract_details</source>
        <target>Contract Details</target>
      </segment>
    </unit>
    <unit id="SCtuDSH" name="u2.contract_expiry_date">
      <segment>
        <source>u2.contract_expiry_date</source>
        <target>Contract Expiry Date</target>
      </segment>
    </unit>
    <unit id=".ZXZIM9" name="u2.country">
      <segment>
        <source>u2.country</source>
        <target>Country</target>
      </segment>
    </unit>
    <unit id="7Le519Z" name="u2.country.plural">
      <segment>
        <source>u2.country.plural</source>
        <target>Countries</target>
      </segment>
    </unit>
    <unit id="Tc.K4FS" name="u2.country_by_country_report.reporting_role">
      <segment>
        <source>u2.country_by_country_report.reporting_role</source>
        <target>Reporting Role</target>
      </segment>
    </unit>
    <unit id="BTdJneT" name="u2.country_founded">
      <segment>
        <source>u2.country_founded</source>
        <target>Country Founded</target>
      </segment>
    </unit>
    <unit id="Q1IvFyQ" name="u2.create_new_record">
      <segment>
        <source>u2.create_new_record</source>
        <target>Create a new record to get going</target>
      </segment>
    </unit>
    <unit id=".P1voIw" name="u2.data_transfer.status_types.completed">
      <segment>
        <source>u2.data_transfer.status_types.completed</source>
        <target>Completed</target>
      </segment>
    </unit>
    <unit id="Og4AEci" name="u2.data_transfer.status_types.in_progress">
      <segment>
        <source>u2.data_transfer.status_types.in_progress</source>
        <target>In progress</target>
      </segment>
    </unit>
    <unit id="z8NOGC4" name="u2.data_transfer.status_types.queued">
      <segment>
        <source>u2.data_transfer.status_types.queued</source>
        <target>Queued</target>
      </segment>
    </unit>
    <unit id="vVHCYT9" name="u2.date.last_day">
      <segment>
        <source>u2.date.last_day</source>
        <target>Last %day%</target>
      </segment>
    </unit>
    <unit id="rP.8Sn_" name="u2.date.today">
      <segment>
        <source>u2.date.today</source>
        <target>Today</target>
      </segment>
    </unit>
    <unit id="Ae37ZCb" name="u2.date.tomorrow">
      <segment>
        <source>u2.date.tomorrow</source>
        <target>Tomorrow</target>
      </segment>
    </unit>
    <unit id="g3aM9xF" name="u2.date.yesterday">
      <segment>
        <source>u2.date.yesterday</source>
        <target>Yesterday</target>
      </segment>
    </unit>
    <unit id="D9vHyb3" name="u2.datetime.last_day">
      <segment>
        <source>u2.datetime.last_day</source>
        <target>Last %day% at %time%</target>
      </segment>
    </unit>
    <unit id="LNsMclQ" name="u2.datetime.next_week">
      <segment>
        <source>u2.datetime.next_week</source>
        <target>%day% at %time%</target>
      </segment>
    </unit>
    <unit id="38ZcuwN" name="u2.datetime.now">
      <segment>
        <source>u2.datetime.now</source>
        <target>Today at %time%</target>
      </segment>
    </unit>
    <unit id="uL0qPIz" name="u2.datetime.tomorrow">
      <segment>
        <source>u2.datetime.tomorrow</source>
        <target>Tomorrow at %time%</target>
      </segment>
    </unit>
    <unit id="8_Q5_VQ" name="u2.datetime.yesterday">
      <segment>
        <source>u2.datetime.yesterday</source>
        <target>Yesterday at %time%</target>
      </segment>
    </unit>
    <unit id="ish9g_B" name="u2.deactivate">
      <segment>
        <source>u2.deactivate</source>
        <target>Deactivate</target>
      </segment>
    </unit>
    <unit id="TyI.P7S" name="u2.default_group_permissions">
      <segment>
        <source>u2.default_group_permissions</source>
        <target>Default Group Permissions</target>
      </segment>
    </unit>
    <unit id="KffQN3o" name="u2.default_user_permissions">
      <segment>
        <source>u2.default_user_permissions</source>
        <target>Default User Permissions</target>
      </segment>
    </unit>
    <unit id="EsQe_dx" name="u2.delete">
      <segment>
        <source>u2.delete</source>
        <target>Delete</target>
      </segment>
    </unit>
    <unit id="6fSwIiS" name="u2.delete_currency_with_given_name">
      <segment>
        <source>u2.delete_currency_with_given_name</source>
        <target>Delete Currency (%currency_name%)</target>
      </segment>
    </unit>
    <unit id="oqu3oEm" name="u2.delete_currency_with_given_name.confirmation">
      <segment>
        <source>u2.delete_currency_with_given_name.confirmation</source>
        <target>Are you sure you want to delete this Currency (%currency_name%)?</target>
      </segment>
    </unit>
    <unit id="hE1iMWl" name="u2.delete_given_selected_records.confirmation">
      <segment>
        <source>u2.delete_given_selected_records.confirmation</source>
        <target>Are you sure you want to delete the selected %entity_type% (%list%)?</target>
      </segment>
    </unit>
    <unit id="YoC5LBA" name="u2.delete_section_from_document.confirm">
      <segment>
        <source>u2.delete_section_from_document.confirm</source>
        <target>Are you sure you want to delete the section “%section_name%”?</target>
      </segment>
    </unit>
    <unit id="ycZ_O52" name="u2.delete_selected_records">
      <segment>
        <source>u2.delete_selected_records</source>
        <target>Delete Selected Record(s)</target>
      </segment>
    </unit>
    <unit id="fZuCfm1" name="u2.delete_successful">
      <segment>
        <source>u2.delete_successful</source>
        <target>Successfully deleted</target>
      </segment>
    </unit>
    <unit id="78zvzGJ" name="u2.delete_unsuccessful">
      <segment>
        <source>u2.delete_unsuccessful</source>
        <target>The delete was unsuccessful</target>
      </segment>
    </unit>
    <unit id="VBi.7Yb" name="u2.description">
      <segment>
        <source>u2.description</source>
        <target>Description</target>
      </segment>
    </unit>
    <unit id="BD15EHG" name="u2.deselect_all">
      <segment>
        <source>u2.deselect_all</source>
        <target>Deselect all</target>
      </segment>
    </unit>
    <unit id="ste3FpY" name="u2.deselect_filtered">
      <segment>
        <source>u2.deselect_filtered</source>
        <target>Deselect filtered</target>
      </segment>
    </unit>
    <unit id="jp_D_cE" name="u2.disabled">
      <segment>
        <source>u2.disabled</source>
        <target>Disabled</target>
      </segment>
    </unit>
    <unit id="Z8zcPrI" name="u2.document.widget.transaction_table.help">
      <segment>
        <source>u2.document.widget.transaction_table.help</source>
        <target>Grouping results will ignore the selected columns.</target>
      </segment>
    </unit>
    <unit id="TeNr0fG" name="u2.document_widget.transaction_table.group_results">
      <segment>
        <source>u2.document_widget.transaction_table.group_results</source>
        <target>Group results</target>
      </segment>
    </unit>
    <unit id="wX5IlUn" name="u2.document_widget.transaction_table.group_results.help">
      <segment>
        <source>u2.document_widget.transaction_table.group_results.help</source>
        <target>Groups the results by Unit, Partner Unit and Type.</target>
      </segment>
    </unit>
    <unit id="FnaRko_" name="u2.duplicate">
      <segment>
        <source>u2.duplicate</source>
        <target>Duplicate</target>
      </segment>
    </unit>
    <unit id="vrmoONS" name="u2.duplicate.dialog.text">
      <segment>
        <source>u2.duplicate.dialog.text</source>
        <target>Are you sure you want to duplicate "%document_name%"?</target>
      </segment>
    </unit>
    <unit id="7kno.FM" name="u2.duplicate.dialog.title">
      <segment>
        <source>u2.duplicate.dialog.title</source>
        <target>Confirm duplication</target>
      </segment>
    </unit>
    <unit id="wfwckXt" name="u2.edit">
      <segment>
        <source>u2.edit</source>
        <target>Edit</target>
      </segment>
    </unit>
    <unit id="Jgussfa" name="u2.edit_comment_successful">
      <segment>
        <source>u2.edit_comment_successful</source>
        <target>Comment changes saved.</target>
      </segment>
    </unit>
    <unit id=".8X18Pf" name="u2.edit_configuration">
      <segment>
        <source>u2.edit_configuration</source>
        <target>Edit Configuration</target>
      </segment>
    </unit>
    <unit id="3vuYSV1" name="u2.edit_content">
      <segment>
        <source>u2.edit_content</source>
        <target>Edit Content</target>
      </segment>
    </unit>
    <unit id="KXug2d5" name="u2.edit_currency_with_given_name">
      <segment>
        <source>u2.edit_currency_with_given_name</source>
        <target>Edit Currency (%currency_name%)</target>
      </segment>
    </unit>
    <unit id="G09aKIS" name="u2.edit_document.warning_unit_hierarchy_changed">
      <segment>
        <source>u2.edit_document.warning_unit_hierarchy_changed</source>
        <target>The unit hierarchy used in this document has been changed. Please open the configuration page to update.</target>
      </segment>
    </unit>
    <unit id="zV5lM0O" name="u2.edit_entity_type_name">
      <segment>
        <source>u2.edit_entity_type_name</source>
        <target>Edit %entity_type_name%</target>
      </segment>
    </unit>
    <unit id="XL7ORob" name="u2.edit_group_permissions">
      <segment>
        <source>u2.edit_group_permissions</source>
        <target>Edit Group Permissions</target>
      </segment>
    </unit>
    <unit id="nQAxsBU" name="u2.edit_selected_records">
      <segment>
        <source>u2.edit_selected_records</source>
        <target>Edit Selected Record(s)</target>
      </segment>
    </unit>
    <unit id="DD54s02" name="u2.edit_user_permissions">
      <segment>
        <source>u2.edit_user_permissions</source>
        <target>Edit User Permissions</target>
      </segment>
    </unit>
    <unit id="9LQmIXY" name="u2.edited">
      <segment>
        <source>u2.edited</source>
        <target>edited</target>
      </segment>
    </unit>
    <unit id="I3zsbzi" name="u2.email.password_reset">
      <segment>
        <source>u2.email.password_reset</source>
        <target>Password Reset</target>
      </segment>
    </unit>
    <unit id="PNKfsGp" name="u2.email.password_reset.choose_account">
      <segment>
        <source>u2.email.password_reset.choose_account</source>
        <target>There are multiple accounts associated with your email. Select the account you wish to reset below.</target>
      </segment>
    </unit>
    <unit id="9rkvRss" name="u2.email.greeting">
      <segment>
        <source>u2.email.greeting</source>
        <target><![CDATA[Hi <strong>%username_or_email%</strong>,]]></target>
      </segment>
    </unit>
    <unit id="kYO7pOq" name="u2.email.password_reset.help">
      <segment>
        <source>u2.email.password_reset.help</source>
        <target>If you didn’t request this, you can ignore this email. Your password won’t change until you create a new one.</target>
      </segment>
    </unit>
    <unit id="mAibShS" name="u2.email.password_reset.request_received">
      <segment>
        <source>u2.email.password_reset.request_received</source>
        <target>We received a request to change your password.</target>
      </segment>
    </unit>
    <unit id="1lM4alt" name="u2.email.password_reset.username_button">
      <segment>
        <source>u2.email.password_reset.username_button</source>
        <target>Reset Password for %username%</target>
      </segment>
    </unit>
    <unit id="vWgTqrG" name="u2.email.two_factor.finish_signin">
      <segment>
        <source>u2.email.two_factor.finish_signin</source>
        <target><![CDATA[We noticed you recently tried to sign in to your Universal Units account from a new device. <br> You can finish signing in by clicking the button:]]></target>
      </segment>
    </unit>
    <unit id="XruVaC9" name="u2.email.two_factor.greeting">
      <segment>
        <source>u2.email.two_factor.greeting</source>
        <target><![CDATA[Hi <strong>%username_or_email%</strong>,]]></target>
      </segment>
    </unit>
    <unit id="yPDhDaN" name="u2.email.two_factor.help">
      <segment>
        <source>u2.email.two_factor.help</source>
        <target><![CDATA[If you're having trouble signing in, please <a href="%contact_url%">get in touch</a>.]]></target>
      </segment>
    </unit>
    <unit id=".CbjJEX" name="u2.email.two_factor.redirection">
      <segment>
        <source>u2.email.two_factor.redirection</source>
        <target>Or copy and paste this code to the confirmation page you were redirected to after logging in:</target>
      </segment>
    </unit>
    <unit id="5ZWNnGM" name="u2.email.two_factor.warning">
      <segment>
        <source>u2.email.two_factor.warning</source>
        <target>If you don’t recognize this sign-in, we recommend that you change your password immediately to secure your account.</target>
      </segment>
    </unit>
    <unit id="DQX9GC." name="u2.empty">
      <segment>
        <source>u2.empty</source>
        <target>Empty</target>
      </segment>
    </unit>
    <unit id="sSaJ7pJ" name="u2.enabled">
      <segment>
        <source>u2.enabled</source>
        <target>Enabled</target>
      </segment>
    </unit>
    <unit id="iCeIRzF" name="u2.error">
      <segment>
        <source>u2.error</source>
        <target>Error</target>
      </segment>
    </unit>
    <unit id="8rr3Esk" name="u2.error.locale_not_synced">
      <segment>
        <source>u2.error.locale_not_synced</source>
        <target>Your language setting has changed since you last visited this tab. Please reload the page.</target>
      </segment>
    </unit>
    <unit id="f216F46" name="u2.export.item_values.csv">
      <segment>
        <source>u2.export.item_values.csv</source>
        <target>Item Values (CSV)</target>
      </segment>
    </unit>
    <unit id="Tr0AL2Q" name="u2.field">
      <segment>
        <source>u2.field</source>
        <target>Field</target>
      </segment>
    </unit>
    <unit id="53gpSN9" name="u2.datasheets.field_count">
      <segment>
        <source>u2.datasheets.field_count</source>
        <target>%count% field is assigned to this datasheet.|%count% fields are assigned to this datasheet.</target>
      </segment>
    </unit>
    <unit id="bTggLHr" name="u2.datasheets.no_fields">
      <segment>
        <source>u2.datasheets.no_fields</source>
        <target>There are no fields assigned to this datasheet.</target>
      </segment>
    </unit>
    <unit id="QvXRt9t" name="u2.field_configuration">
      <segment>
        <source>u2.field_configuration</source>
        <target>Field Configuration Collection</target>
      </segment>
    </unit>
    <unit id="a22QMh0" name="u2.field_configuration.delete.confirm">
      <segment>
        <source>u2.field_configuration.delete.confirm</source>
        <target>Are you sure you want to delete field configuration "%field_configuration_collection%"?</target>
      </segment>
    </unit>
    <unit id="EL4cTAw" name="u2.field_configuration.edit">
      <segment>
        <source>u2.field_configuration.edit</source>
        <target>Edit Field Configuration</target>
      </segment>
    </unit>
    <unit id="GBTBcvO" name="u2.field_configuration.new">
      <segment>
        <source>u2.field_configuration.new</source>
        <target>New Field Configuration</target>
      </segment>
    </unit>
    <unit id="jy17OZ0" name="u2.field_configuration.plural">
      <segment>
        <source>u2.field_configuration.plural</source>
        <target>Field Configurations</target>
      </segment>
    </unit>
    <unit id="_vxpYvH" name="u2.field_configuration.tooltip.delete">
      <segment>
        <source>u2.field_configuration.tooltip.delete</source>
        <target>Delete field configuration "%field_configuration_collection%"</target>
      </segment>
    </unit>
    <unit id="Yhlz_v9" name="u2.field_configuration.tooltip.edit">
      <segment>
        <source>u2.field_configuration.tooltip.edit</source>
        <target>Edit field configuration "%field_configuration_collection%"</target>
      </segment>
    </unit>
    <unit id="pHGroZ." name="u2.field_configuration_statuses.plural">
      <segment>
        <source>u2.field_configuration_statuses.plural</source>
        <target>Status Field Configurations</target>
      </segment>
    </unit>
    <unit id="VxL6_zK" name="u2.file">
      <segment>
        <source>u2.file</source>
        <target>File</target>
      </segment>
    </unit>
    <unit id="YQlznB1" name="u2.file_already_linked">
      <segment>
        <source>u2.file_already_linked</source>
        <target>This file has been already linked to this record.</target>
      </segment>
    </unit>
    <unit id="ec7Dcox" name="u2.file_required_link">
      <segment>
        <source>u2.file_required_link</source>
        <target>A file must be selected to link.</target>
      </segment>
    </unit>
    <unit id="wjPX28w" name="u2.file_size">
      <segment>
        <source>u2.file_size</source>
        <target>File Size</target>
      </segment>
    </unit>
    <unit id="mjdrW8s" name="u2.file_type">
      <segment>
        <source>u2.file_type</source>
        <target>File Type</target>
      </segment>
    </unit>
    <unit id="gUriG9b" name="u2.file_type.plural">
      <segment>
        <source>u2.file_type.plural</source>
        <target>File Types</target>
      </segment>
    </unit>
    <unit id="pUPDKVO" name="u2.file_upload_whitelist">
      <segment>
        <source>u2.file_upload_whitelist</source>
        <target>File upload whitelist</target>
      </segment>
    </unit>
    <unit id="yaAsV6c" name="u2.file_upload_whitelist.help">
      <segment>
        <source>u2.file_upload_whitelist.help</source>
        <target>
          This whitelist defines the mime types of files that may be uploaded to U². Files that do not have a mime type in this whitelist are not accepted as valid and cannot be uploaded.
        </target>
      </segment>
    </unit>
    <unit id="jSbV2rA" name="u2.file_upload_whitelist.warning">
      <segment>
        <source>u2.file_upload_whitelist.warning</source>
        <target>Certain file types could be a security risk and this list should be edited with caution.</target>
      </segment>
    </unit>
    <unit id="Wjg3J6p" name="u2.filter">
      <segment>
        <source>u2.filter</source>
        <target>Filter</target>
      </segment>
    </unit>
    <unit id="nD3ylcC" name="u2.filter_the_results_to_save">
      <segment>
        <source>u2.filter_the_results_to_save</source>
        <target>Filter the results to save</target>
      </segment>
    </unit>
    <unit id="oMhCtP0" name="u2.form.text_field_placeholder">
      <segment>
        <source>u2.form.text_field_placeholder</source>
        <target>Enter text…</target>
      </segment>
    </unit>
    <unit id="0wCKXpV" name="u2.group_permissions">
      <segment>
        <source>u2.group_permissions</source>
        <target>Group Permissions</target>
      </segment>
    </unit>
    <unit id="XCzMKgp" name="u2.headquarters">
      <segment>
        <source>u2.headquarters</source>
        <target>Headquarters</target>
      </segment>
    </unit>
    <unit id="h4QbhC2" name="u2.hidden">
      <segment>
        <source>u2.hidden</source>
        <target>Hidden</target>
      </segment>
    </unit>
    <unit id="8c9UsD2" name="u2.history">
      <segment>
        <source>u2.history</source>
        <target>History</target>
      </segment>
    </unit>
    <unit id="Nm9jG_t" name="u2.http_400">
      <segment>
        <source>u2.http_400</source>
        <target>Bad request. The request could not be processed.</target>
      </segment>
    </unit>
    <unit id="SLTfZ.B" name="u2.http_403">
      <segment>
        <source>u2.http_403</source>
        <target>You do not have permissions to view this page or perform this action. Please contact your permissions administrator to request access.</target>
      </segment>
    </unit>
    <unit id="6r68Tlj" name="u2.http_404">
      <segment>
        <source>u2.http_404</source>
        <target>The resource you are looking for cannot be found or does not exist. It might have been removed, had its name changed, or is temporarily unavailable.</target>
      </segment>
    </unit>
    <unit id="kOfg4JB" name="u2.http_405">
      <segment>
        <source>u2.http_405</source>
        <target>The request method used is not supported by this resource.</target>
      </segment>
    </unit>
    <unit id="ihCCSiP" name="u2.http_408">
      <segment>
        <source>u2.http_408</source>
        <target>The server timed out waiting for the request. Please try again.</target>
      </segment>
    </unit>
    <unit id="2REvXtN" name="u2.http_409">
      <segment>
        <source>u2.http_409</source>
        <target>The request could not be completed because the current state of the resource conflicts with the request.</target>
      </segment>
    </unit>
    <unit id="MHSiSyQ" name="u2.http_422">
      <segment>
        <source>u2.http_422</source>
        <target>The resource could not be processed.</target>
      </segment>
    </unit>
    <unit id="nlF3H4n" name="u2.http_500">
      <segment>
        <source>u2.http_500</source>
        <target>Application Error.</target>
      </segment>
    </unit>
    <unit id="iFO9ngR" name="u2.http_503">
      <segment>
        <source>u2.http_503</source>
        <target>The system is currently under maintenance.</target>
      </segment>
    </unit>
    <unit id="PARfvkL" name="u2.import.import">
      <segment>
        <source>u2.import.import</source>
        <target>Import</target>
      </segment>
    </unit>
    <unit id="Hwsiz_E" name="u2.import.import_units">
      <segment>
        <source>u2.import.import_units</source>
        <target>Import Units</target>
      </segment>
    </unit>
    <unit id="D6vsfGH" name="u2.import.imported">
      <segment>
        <source>u2.import.imported</source>
        <target>Imported</target>
      </segment>
    </unit>
    <unit id="Hw67Drr" name="u2.import.importing_given_item">
      <segment>
        <source>u2.import.importing_given_item</source>
        <target>Importing %item%</target>
      </segment>
    </unit>
    <unit id="21YBrAb" name="u2.import.imports">
      <segment>
        <source>u2.import.imports</source>
        <target>Imports</target>
      </segment>
    </unit>
    <unit id="eCHch8F" name="u2.import.record_was_imported">
      <segment>
        <source>u2.import.record_was_imported</source>
        <target>This record was imported</target>
      </segment>
    </unit>
    <unit id="a5TeSjM" name="u2.import.record_was_imported_from_an_external_source">
      <segment>
        <source>u2.import.record_was_imported_from_an_external_source</source>
        <target>This record was imported from an external source and not entered manually.</target>
      </segment>
    </unit>
    <unit id="i8zhN5a" name="u2.import.result_types.fail">
      <segment>
        <source>u2.import.result_types.fail</source>
        <target>Failed</target>
      </segment>
    </unit>
    <unit id="zxMc6Mh" name="u2.import.result_types.success">
      <segment>
        <source>u2.import.result_types.success</source>
        <target>Successful</target>
      </segment>
    </unit>
    <unit id="IR9Idw2" name="u2.import.status_types.completed">
      <segment>
        <source>u2.import.status_types.completed</source>
        <target>Completed</target>
      </segment>
    </unit>
    <unit id="VoBAiYQ" name="u2.import.status_types.in_progress">
      <segment>
        <source>u2.import.status_types.in_progress</source>
        <target>In progress</target>
      </segment>
    </unit>
    <unit id="tszRx2u" name="u2.import.status_types.queued">
      <segment>
        <source>u2.import.status_types.queued</source>
        <target>Queued</target>
      </segment>
    </unit>
    <unit id="2X8Ago7" name="u2.information">
      <segment>
        <source>u2.information</source>
        <target>Information</target>
      </segment>
    </unit>
    <unit id="fbrQDnB" name="u2.insert_file_reference">
      <segment>
        <source>u2.insert_file_reference</source>
        <target>Insert File Reference</target>
      </segment>
    </unit>
    <unit id=".esGb2C" name="u2.insert_image">
      <segment>
        <source>u2.insert_image</source>
        <target>Insert Image</target>
      </segment>
    </unit>
    <unit id="nkjFH1P" name="u2.insert_widget">
      <segment>
        <source>u2.insert_widget</source>
        <target>Insert Widget</target>
      </segment>
    </unit>
    <unit id="vu8fYXJ" name="u2.insufficient_permissions">
      <segment>
        <source>u2.insufficient_permissions</source>
        <target>Insufficient permissions</target>
      </segment>
    </unit>
    <unit id="DH3NkFZ" name="u2.insufficient_user_permissions_to_view_entity">
      <segment>
        <source>u2.insufficient_user_permissions_to_view_entity</source>
        <target>The user has insufficient permissions to view this %entity_name%.</target>
      </segment>
    </unit>
    <unit id="e7bjNcE" name="u2.datasheets.item.types.checkbox">
      <segment>
        <source>u2.datasheets.item.types.checkbox</source>
        <target>Checkbox</target>
      </segment>
    </unit>
    <unit id="b87Z71U" name="u2.datasheets.item.types.diff">
      <segment>
        <source>u2.datasheets.item.types.diff</source>
        <target>Difference</target>
      </segment>
    </unit>
    <unit id="h3yTl3h" name="u2.datasheets.item.types.money">
      <segment>
        <source>u2.datasheets.item.types.money</source>
        <target>Money</target>
      </segment>
    </unit>
    <unit id="r8mK6kd" name="u2.datasheets.item.types.percent">
      <segment>
        <source>u2.datasheets.item.types.percent</source>
        <target>Rate</target>
      </segment>
    </unit>
    <unit id="d3P1.WY" name="u2.datasheets.item.types.text">
      <segment>
        <source>u2.datasheets.item.types.text</source>
        <target>Text</target>
      </segment>
    </unit>
    <unit id="UQkuvgW" name="u2.datasheets.item_value">
      <segment>
        <source>u2.datasheets.item_value</source>
        <target>Value</target>
      </segment>
    </unit>
    <unit id="ZCs.F3k" name="u2.datasheets.field.delete.confirm">
      <segment>
        <source>u2.datasheets.field.delete.confirm</source>
        <target>Are you sure you want to delete field "%fieldId%" of datasheet "%layoutName%"?</target>
      </segment>
    </unit>
    <unit id="shWIKoD" name="u2.legal_unit">
      <segment>
        <source>u2.legal_unit</source>
        <target>Legal Unit</target>
      </segment>
    </unit>
    <unit id="VzuvumU" name="u2.legal_unit.plural">
      <segment>
        <source>u2.legal_unit.plural</source>
        <target>Legal Units</target>
      </segment>
    </unit>
    <unit id="_38eOOd" name="u2.linked_entities.not_allowed">
      <segment>
        <source>u2.linked_entities.not_allowed</source>
        <target>You do not have permissions to view these entities.</target>
      </segment>
    </unit>
    <unit id="XZB06p7" name="u2.list">
      <segment>
        <source>u2.list</source>
        <target>List</target>
      </segment>
    </unit>
    <unit id="fOhQ3CS" name="u2.loading">
      <segment>
        <source>u2.loading</source>
        <target>Loading…</target>
      </segment>
    </unit>
    <unit id="vV9Ohg7" name="u2.locked">
      <segment>
        <source>u2.locked</source>
        <target>Locked</target>
      </segment>
    </unit>
    <unit id="IrKVFmC" name="u2.login_color">
      <segment>
        <source>u2.login_color</source>
        <target>Login Colour</target>
      </segment>
    </unit>
    <unit id="tnn2f_q" name="u2.login_color.help">
      <segment>
        <source>u2.login_color.help</source>
        <target>Hexadecimal colour representation. Leave empty for the default colour. Example: #d22630 for U² Red.</target>
      </segment>
    </unit>
    <unit id="4TRTQxT" name="u2.maturity_date">
      <segment>
        <source>u2.maturity_date</source>
        <target>Maturity Date</target>
      </segment>
    </unit>
    <unit id="cVwUXbT" name="u2.max_upload_size">
      <segment>
        <source>u2.max_upload_size</source>
        <target>Max upload size in KB</target>
      </segment>
    </unit>
    <unit id="lZatdao" name="u2.money.exchange_rate.types.average">
      <segment>
        <source>u2.money.exchange_rate.types.average</source>
        <target>Average</target>
      </segment>
    </unit>
    <unit id="FTxZMdN" name="u2.money.exchange_rate.types.current">
      <segment>
        <source>u2.money.exchange_rate.types.current</source>
        <target>Current</target>
      </segment>
    </unit>
    <unit id="5G530tC" name="u2.n_a">
      <segment>
        <source>u2.n_a</source>
        <target>n/a</target>
      </segment>
    </unit>
    <unit id="teqY0ob" name="u2.name">
      <segment>
        <source>u2.name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="3kN.cM2" name="u2.new">
      <segment>
        <source>u2.new</source>
        <target>New</target>
      </segment>
    </unit>
    <unit id="c9d9dbf" name="u2.new_dashboard">
      <segment>
        <source>u2.new_dashboard</source>
        <target>New Dashboard</target>
      </segment>
    </unit>
    <unit id="0iGedXa" name="u2.new_entity_type_name">
      <segment>
        <source>u2.new_entity_type_name</source>
        <target>New %entity_type_name%</target>
      </segment>
    </unit>
    <unit id="R8aKq7M" name="u2.new_exchange_rate">
      <segment>
        <source>u2.new_exchange_rate</source>
        <target>New Exchange Rate</target>
      </segment>
    </unit>
    <unit id="gCx1ueA" name="u2.new_file">
      <segment>
        <source>u2.new_file</source>
        <target>New File</target>
      </segment>
    </unit>
    <unit id="wGJQSfc" name="u2.new_given_unit_type">
      <segment>
        <source>u2.new_given_unit_type</source>
        <target>New %unit_type%</target>
      </segment>
    </unit>
    <unit id="0H7.DXu" name="u2.new_item_with_given_name">
      <segment>
        <source>u2.new_item_with_given_name</source>
        <target>New %item_name%</target>
      </segment>
    </unit>
    <unit id="YHObSEO" name="u2.new_password">
      <segment>
        <source>u2.new_password</source>
        <target>New password</target>
      </segment>
    </unit>
    <unit id="21Mx9tk" name="u2.new_period">
      <segment>
        <source>u2.new_period</source>
        <target>New Period</target>
      </segment>
    </unit>
    <unit id="PoJ1Fym" name="u2.new_saved_filter">
      <segment>
        <source>u2.new_saved_filter</source>
        <target>New Saved Filter</target>
      </segment>
    </unit>
    <unit id="dIuSggE" name="u2.new_saved_filter_subscription">
      <segment>
        <source>u2.new_saved_filter_subscription</source>
        <target>New Saved Filter Subscription</target>
      </segment>
    </unit>
    <unit id="QOflteV" name="u2.new_status">
      <segment>
        <source>u2.new_status</source>
        <target>New Status</target>
      </segment>
    </unit>
    <unit id="RcfpaJF" name="u2.new_system_message">
      <segment>
        <source>u2.new_system_message</source>
        <target>New System Message</target>
      </segment>
    </unit>
    <unit id="FMhiMEq" name="u2.new_unit_hierarchy">
      <segment>
        <source>u2.new_unit_hierarchy</source>
        <target>New Unit Hierarchy</target>
      </segment>
    </unit>
    <unit id="JzFcMRq" name="u2.new_user">
      <segment>
        <source>u2.new_user</source>
        <target>New User</target>
      </segment>
    </unit>
    <unit id="LdbBKIX" name="u2.new_user_group">
      <segment>
        <source>u2.new_user_group</source>
        <target>New User Group</target>
      </segment>
    </unit>
    <unit id="y1edCJS" name="u2.no">
      <segment>
        <source>u2.no</source>
        <target>No</target>
      </segment>
    </unit>
    <unit id="a40Sq4g" name="u2.no_files_in_section">
      <segment>
        <source>u2.no_files_in_section</source>
        <target>There are no files available. Please attach a file to this section and try again.</target>
      </segment>
    </unit>
    <unit id="FPAK1DO" name="u2.no_images_in_section">
      <segment>
        <source>u2.no_images_in_section</source>
        <target>There are no images available. Please attach an image to this section and try again.</target>
      </segment>
    </unit>
    <unit id="dMLS814" name="u2.no_records_selected_delete">
      <segment>
        <source>u2.no_records_selected_delete</source>
        <target>No records selected to delete.</target>
      </segment>
    </unit>
    <unit id="_IE.kD5" name="u2.no_records_selected_edit">
      <segment>
        <source>u2.no_records_selected_edit</source>
        <target>No records selected to edit.</target>
      </segment>
    </unit>
    <unit id="9jgDTuB" name="u2.no_records_selected_transition">
      <segment>
        <source>u2.no_records_selected_transition</source>
        <target>No records selected to transition.</target>
      </segment>
    </unit>
    <unit id="2K_5r3k" name="u2.no_results">
      <segment>
        <source>u2.no_results</source>
        <target>No results</target>
      </segment>
    </unit>
    <unit id="JTayC6g" name="u2.open">
      <segment>
        <source>u2.open</source>
        <target>Open</target>
      </segment>
    </unit>
    <unit id="gAl3riA" name="u2.organisational_group">
      <segment>
        <source>u2.organisational_group</source>
        <target>Organisational Group</target>
      </segment>
    </unit>
    <unit id="liTKvcQ" name="u2.organisational_group.plural">
      <segment>
        <source>u2.organisational_group.plural</source>
        <target>Organisational Groups</target>
      </segment>
    </unit>
    <unit id="uFbklzK" name="u2.page_break">
      <segment>
        <source>u2.page_break</source>
        <target>Page Break</target>
      </segment>
    </unit>
    <unit id="8Hm2vfI" name="u2.page_has_unsaved_changes">
      <segment>
        <source>u2.page_has_unsaved_changes</source>
        <target>This page has unsaved changes.</target>
      </segment>
    </unit>
    <unit id=".JhblI7" name="u2.page_not_found">
      <segment>
        <source>u2.page_not_found</source>
        <target>Page not found</target>
      </segment>
    </unit>
    <unit id="ohar6pM" name="u2.password_good">
      <segment>
        <source>u2.password_good</source>
        <target>Good</target>
      </segment>
    </unit>
    <unit id="Ffxb.2X" name="u2.password_moderate">
      <segment>
        <source>u2.password_moderate</source>
        <target>Moderate</target>
      </segment>
    </unit>
    <unit id="35bgMRq" name="u2.password_strong">
      <segment>
        <source>u2.password_strong</source>
        <target>Strong</target>
      </segment>
    </unit>
    <unit id="h_PYw2u" name="u2.password_very_weak">
      <segment>
        <source>u2.password_very_weak</source>
        <target>Very weak</target>
      </segment>
    </unit>
    <unit id="nANkD5X" name="u2.password_weak">
      <segment>
        <source>u2.password_weak</source>
        <target>Weak</target>
      </segment>
    </unit>
    <unit id="ck0ZP.r" name="u2.periods">
      <segment>
        <source>u2.periods</source>
        <target>Periods</target>
      </segment>
    </unit>
    <unit id="jpsdBwE" name="u2.permanent_establishment">
      <segment>
        <source>u2.permanent_establishment</source>
        <target>Permanent Establishment</target>
      </segment>
    </unit>
    <unit id="3Te9w0B" name="u2.permanent_establishment.plural">
      <segment>
        <source>u2.permanent_establishment.plural</source>
        <target>Permanent Establishments</target>
      </segment>
    </unit>
    <unit id="l3ubcKs" name="u2.post_comment">
      <segment>
        <source>u2.post_comment</source>
        <target>Post comment</target>
      </segment>
    </unit>
    <unit id="SaaMRHb" name="u2.recalculate">
      <segment>
        <source>u2.recalculate</source>
        <target>Recalculate</target>
      </segment>
    </unit>
    <unit id="Bn1XSHI" name="u2.recalculate_unit_period.confirmation.text">
      <segment>
        <source>u2.recalculate_unit_period.confirmation.text</source>
        <target>This will recalculate all values for the unit "%unit%" in the period "%period%"</target>
      </segment>
    </unit>
    <unit id="WoOQl0J" name="u2.recalculate_unit_period.success">
      <segment>
        <source>u2.recalculate_unit_period.success</source>
        <target>Successfully recalculated the values for the unit "%unit%" in the period "%period%".</target>
      </segment>
    </unit>
    <unit id="zg79oRe" name="u2.record_was_transferred">
      <segment>
        <source>u2.record_was_transferred</source>
        <target>This record was transferred and not entered manually.</target>
      </segment>
    </unit>
    <unit id="wtAqgrY" name="u2.records">
      <segment>
        <source>u2.records</source>
        <target>Records</target>
      </segment>
    </unit>
    <unit id="TUrEhzh" name="u2.remember_me">
      <segment>
        <source>u2.remember_me</source>
        <target>Keep me logged in</target>
      </segment>
    </unit>
    <unit id="B3VFCkz" name="u2.removing_attachment_unsuccessful">
      <segment>
        <source>u2.removing_attachment_unsuccessful</source>
        <target>Removing the attachment failed.</target>
      </segment>
    </unit>
    <unit id="y4ozLha" name="u2.reporting_company">
      <segment>
        <source>u2.reporting_company</source>
        <target>Reporting Company</target>
      </segment>
    </unit>
    <unit id="DW00lt3" name="u2.reporting_company.country">
      <segment>
        <source>u2.reporting_company.country</source>
        <target>Parent Company Country</target>
      </segment>
    </unit>
    <unit id="IrcmdU3" name="u2.reporting_company.name">
      <segment>
        <source>u2.reporting_company.name</source>
        <target>Parent Company Name</target>
      </segment>
    </unit>
    <unit id="aI6dzXa" name="u2.reporting_company.ref_id">
      <segment>
        <source>u2.reporting_company.ref_id</source>
        <target>Parent Company Ref. ID</target>
      </segment>
    </unit>
    <unit id="9lKyyQU" name="u2.reporting_roles.localfiling">
      <segment>
        <source>u2.reporting_roles.localfiling</source>
        <target>Local</target>
      </segment>
    </unit>
    <unit id="4rzYkEa" name="u2.reporting_roles.surrogateparententity">
      <segment>
        <source>u2.reporting_roles.surrogateparententity</source>
        <target>Surrogate</target>
      </segment>
    </unit>
    <unit id="reseSRA" name="u2.reporting_roles.ultimateparententity">
      <segment>
        <source>u2.reporting_roles.ultimateparententity</source>
        <target>Headquarters</target>
      </segment>
    </unit>
    <unit id="eYuo_R3" name="u2.request">
      <segment>
        <source>u2.request</source>
        <target>Request</target>
      </segment>
    </unit>
    <unit id="8AtRVDM" name="u2.request_permissions">
      <segment>
        <source>u2.request_permissions</source>
        <target>Request Permissions</target>
      </segment>
    </unit>
    <unit id="vnS_wVl" name="u2.reset_trusted_devices">
      <segment>
        <source>u2.reset_trusted_devices</source>
        <target>Reset trusted devices</target>
      </segment>
    </unit>
    <unit id="9hXd4S8" name="u2.return_to_the_login_page">
      <segment>
        <source>u2.return_to_the_login_page</source>
        <target>Return to the login page.</target>
      </segment>
    </unit>
    <unit id="GnVULpw" name="u2.role_is_inherited">
      <segment>
        <source>u2.role_is_inherited</source>
        <target>This role is inherited from another role or from a group.</target>
      </segment>
    </unit>
    <unit id="zKT4yb1" name="u2.runtime">
      <segment>
        <source>u2.runtime</source>
        <target>Runtime</target>
      </segment>
    </unit>
    <unit id="Xz27LsB" name="u2.save">
      <segment>
        <source>u2.save</source>
        <target>Save</target>
      </segment>
    </unit>
    <unit id="pFH.c93" name="u2.save_as">
      <segment>
        <source>u2.save_as</source>
        <target>Save As</target>
      </segment>
    </unit>
    <unit id="8c_CzUg" name="u2.save_the_current_filter">
      <segment>
        <source>u2.save_the_current_filter</source>
        <target>Save the current filter</target>
      </segment>
    </unit>
    <unit id="DQvWoMx" name="u2.save_the_current_filter_as_new">
      <segment>
        <source>u2.save_the_current_filter_as_new</source>
        <target>Save the current filter as a new filter</target>
      </segment>
    </unit>
    <unit id="LRrdU2I" name="u2.save_unit_hierarchy_confirmation">
      <segment>
        <source>u2.save_unit_hierarchy_confirmation</source>
        <target>Are you sure you want to save this unit hierarchy configuration for the date %date%?</target>
      </segment>
    </unit>
    <unit id="cJPuIm9" name="u2.saved_filter.visibility_user_group_permissions_warning">
      <segment>
        <source>u2.saved_filter.visibility_user_group_permissions_warning</source>
        <target>Assigned groups have no effect because "Share with all users" is on</target>
      </segment>
    </unit>
    <unit id="fftT6fB" name="u2.saved_filter.visibility_user_permissions_warning">
      <segment>
        <source>u2.saved_filter.visibility_user_permissions_warning</source>
        <target>Assigned users have no effect because "Share with all users" is on</target>
      </segment>
    </unit>
    <unit id="o027DXM" name="u2.saved_filter_subscription.why_receiving">
      <segment>
        <source>u2.saved_filter_subscription.why_receiving</source>
        <target>You are receiving this information because you are subscribed to this filter.</target>
      </segment>
    </unit>
    <unit id="GlR6wnV" name="u2.saved_filter_subscriptions.confirm_send_text">
      <segment>
        <source>u2.saved_filter_subscriptions.confirm_send_text</source>
        <target>This action will run the subscription now and send emails to all recipients.</target>
      </segment>
    </unit>
    <unit id="8y5q2eh" name="u2.saved_filters">
      <segment>
        <source>u2.saved_filters</source>
        <target>Saved Filters</target>
      </segment>
    </unit>
    <unit id="Bk_nrd8" name="u2.saved_filters.no_subscriptions_to_this_filter">
      <segment>
        <source>u2.saved_filters.no_subscriptions_to_this_filter</source>
        <target>There are no subscriptions to this filter.</target>
      </segment>
    </unit>
    <unit id="wShbn84" name="u2.saved_filters_list">
      <segment>
        <source>u2.saved_filters_list</source>
        <target>Saved Filters List</target>
      </segment>
    </unit>
    <unit id="AypsMi3" name="u2.search">
      <segment>
        <source>u2.search</source>
        <target>Search</target>
      </segment>
    </unit>
    <unit id="eoUcQEO" name="u2.search.recent_searches">
      <segment>
        <source>u2.search.recent_searches</source>
        <target>Recent</target>
      </segment>
    </unit>
    <unit id="y6hjUJi" name="u2.security.confirm_login">
      <segment>
        <source>u2.security.confirm_login</source>
        <target>Confirm login</target>
      </segment>
    </unit>
    <unit id="cI74jVY" name="u2.security.finish_login">
      <segment>
        <source>u2.security.finish_login</source>
        <target>Finish login</target>
      </segment>
    </unit>
    <unit id="7dJO9y_" name="u2.security.roles.admin">
      <segment>
        <source>u2.security.roles.admin</source>
        <target>Admin</target>
      </segment>
    </unit>
    <unit id="KA7Htmo" name="u2.security.roles.user">
      <segment>
        <source>u2.security.roles.user</source>
        <target>User</target>
      </segment>
    </unit>
    <unit id="91KCTtA" name="u2.security.roles.user_group_admin">
      <segment>
        <source>u2.security.roles.user_group_admin</source>
        <target>User Group Admin</target>
      </segment>
    </unit>
    <unit id="YxZ4aMm" name="u2.security.two_factor">
      <segment>
        <source>u2.security.two_factor</source>
        <target>Enforce two factor authentication</target>
      </segment>
    </unit>
    <unit id="BLF0peh" name="u2.security.two_factor.code.label">
      <segment>
        <source>u2.security.two_factor.code.label</source>
        <target>Please enter the authentication code from the message that was sent to your email:</target>
      </segment>
    </unit>
    <unit id="RugIl8h" name="u2.security.two_factor.code.placeholder">
      <segment>
        <source>u2.security.two_factor.code.placeholder</source>
        <target>Code</target>
      </segment>
    </unit>
    <unit id="lqPw9M9" name="u2.security.two_factor.confirm_reset_of_trusted_devices">
      <segment>
        <source>u2.security.two_factor.confirm_reset_of_trusted_devices</source>
        <target>Are you sure you want to reset your trusted devices?</target>
      </segment>
    </unit>
    <unit id="C.grwUc" name="u2.security.two_factor.email">
      <segment>
        <source>u2.security.two_factor.email</source>
        <target>Two Factor Email Authentication</target>
      </segment>
    </unit>
    <unit id="zF0AdZA" name="u2.security.two_factor.reset_trusted_devices.success">
      <segment>
        <source>u2.security.two_factor.reset_trusted_devices.success</source>
        <target>Trusted devices have been successfully reset</target>
      </segment>
    </unit>
    <unit id="wWepOx1" name="u2.security.two_factor.trust_device.label">
      <segment>
        <source>u2.security.two_factor.trust_device.label</source>
        <target>I trust this device</target>
      </segment>
    </unit>
    <unit id="RNHaP68" name="u2.security.verify_login">
      <segment>
        <source>u2.security.verify_login</source>
        <target>Verify login</target>
      </segment>
    </unit>
    <unit id="B64IGLe" name="u2.select_a_task_type">
      <segment>
        <source>u2.select_a_task_type</source>
        <target>Select a Task Type</target>
      </segment>
    </unit>
    <unit id="1OhwIPA" name="u2.select_all">
      <segment>
        <source>u2.select_all</source>
        <target>Select all</target>
      </segment>
    </unit>
    <unit id="RvN8lM5" name="u2.select_country">
      <segment>
        <source>u2.select_country</source>
        <target>Select country</target>
      </segment>
    </unit>
    <unit id="QreM9RK" name="u2.select_currency">
      <segment>
        <source>u2.select_currency</source>
        <target>Select a currency</target>
      </segment>
    </unit>
    <unit id="ED4osNF" name="u2.select_date">
      <segment>
        <source>u2.select_date</source>
        <target>Select a date</target>
      </segment>
    </unit>
    <unit id="GiHF4NG" name="u2.select_filtered">
      <segment>
        <source>u2.select_filtered</source>
        <target>Select filtered</target>
      </segment>
    </unit>
    <unit id="xxbDsz4" name="u2.select_option">
      <segment>
        <source>u2.select_option</source>
        <target>Select an option</target>
      </segment>
    </unit>
    <unit id="UxOlr20" name="u2.service_provider">
      <segment>
        <source>u2.service_provider</source>
        <target>Service Provider</target>
      </segment>
    </unit>
    <unit id="iJQxNbs" name="u2.share">
      <segment>
        <source>u2.share</source>
        <target>Share</target>
      </segment>
    </unit>
    <unit id="ZqIFpfy" name="u2.share_filter">
      <segment>
        <source>u2.share_filter</source>
        <target>Share Filter</target>
      </segment>
    </unit>
    <unit id="GaDmNPJ" name="u2.share_filter.help">
      <segment>
        <source>u2.share_filter.help</source>
        <target>You can copy and paste this link and it will point to the current view of the table</target>
      </segment>
    </unit>
    <unit id="Ntp.PkG" name="u2.share_filter_link">
      <segment>
        <source>u2.share_filter_link</source>
        <target>Share Filter Link</target>
      </segment>
    </unit>
    <unit id="OOUdiQW" name="u2.share_link_to_the_currently_filtered_table">
      <segment>
        <source>u2.share_link_to_the_currently_filtered_table</source>
        <target>Share link to the currently filtered table</target>
      </segment>
    </unit>
    <unit id="fbn6rXL" name="u2.shared_with_all_users">
      <segment>
        <source>u2.shared_with_all_users</source>
        <target>Share with all users</target>
      </segment>
    </unit>
    <unit id="VP7xEY4" name="u2.simulated">
      <segment>
        <source>u2.simulated</source>
        <target>Simulated</target>
      </segment>
    </unit>
    <unit id="6xLp8tC" name="u2.simulated.help">
      <segment>
        <source>u2.simulated.help</source>
        <target>This is a simulation and no changes have been made.</target>
      </segment>
    </unit>
    <unit id="Q5KKoOL" name="u2.status.types.type_complete">
      <segment>
        <source>u2.status.types.type_complete</source>
        <target>Complete</target>
      </segment>
    </unit>
    <unit id="3DL83T4" name="u2.status.types.type_in_progress">
      <segment>
        <source>u2.status.types.type_in_progress</source>
        <target>In Progress</target>
      </segment>
    </unit>
    <unit id="2swQ07H" name="u2.status.types.type_open">
      <segment>
        <source>u2.status.types.type_open</source>
        <target>Open</target>
      </segment>
    </unit>
    <unit id="7zFy82F" name="u2.statuses_field_configuration.edit">
      <segment>
        <source>u2.statuses_field_configuration.edit</source>
        <target>Edit Statuses Field Configuration</target>
      </segment>
    </unit>
    <unit id="0lNEwCb" name="u2.statuses_field_configuration.new">
      <segment>
        <source>u2.statuses_field_configuration.new</source>
        <target>New Statuses Field Configuration</target>
      </segment>
    </unit>
    <unit id="snbL5JP" name="u2.success_removed">
      <segment>
        <source>u2.success_removed</source>
        <target>Removed.</target>
      </segment>
    </unit>
    <unit id="rHITHj3" name="u2.system_message.types.type_info">
      <segment>
        <source>u2.system_message.types.type_info</source>
        <target>Info</target>
      </segment>
    </unit>
    <unit id="Ug5b2lT" name="u2.system_message.types.type_warning">
      <segment>
        <source>u2.system_message.types.type_warning</source>
        <target>Warning</target>
      </segment>
    </unit>
    <unit id="8pIl3S0" name="u2.table.count_records_found">
      <segment>
        <source>u2.table.count_records_found</source>
        <target>%count% record found|%count% records found</target>
      </segment>
    </unit>
    <unit id="OD_uzDm" name="u2.table.no_records">
      <segment>
        <source>u2.table.no_records</source>
        <target>No Records</target>
      </segment>
    </unit>
    <unit id="p58iYuJ" name="u2.table.selected_count">
      <segment>
        <source>u2.table.selected_count</source>
        <target>%count% selected</target>
      </segment>
    </unit>
    <unit id="su840mk" name="u2.task.choice_fields">
      <segment>
        <source>u2.task.choice_fields</source>
        <target>Choice Fields</target>
      </segment>
    </unit>
    <unit id="GnMW5ds" name="u2.task.duplicate.success">
      <segment>
        <source>u2.task.duplicate.success</source>
        <target>Task type successfully duplicated.</target>
      </segment>
    </unit>
    <unit id="9ReXVrc" name="u2.task.task_id">
      <segment>
        <source>u2.task.task_id</source>
        <target>Task Id</target>
      </segment>
    </unit>
    <unit id="iPm2.hR" name="u2.task_checklist.check_history_title">
      <segment>
        <source>u2.task_checklist.check_history_title</source>
        <target>Checklist history for "%title%"</target>
      </segment>
    </unit>
    <unit id="VjowHHS" name="u2.task_checklist.hide_checks">
      <segment>
        <source>u2.task_checklist.hide_checks</source>
        <target>Hide the checks that are not assigned to the current workflow status</target>
      </segment>
    </unit>
    <unit id="Co0btxQ" name="u2.task_checklist.no_checks">
      <segment>
        <source>u2.task_checklist.no_checks</source>
        <target>There are no checks for this task.</target>
      </segment>
    </unit>
    <unit id="Wn5Pa.T" name="u2.task_checklist.no_checks_in_current_status">
      <segment>
        <source>u2.task_checklist.no_checks_in_current_status</source>
        <target>There are no checks for the current status.</target>
      </segment>
    </unit>
    <unit id="D7Rrqm8" name="u2.task_checklist.no_history">
      <segment>
        <source>u2.task_checklist.no_history</source>
        <target>This checklist item has not yet been checked</target>
      </segment>
    </unit>
    <unit id="3pgrsgV" name="u2.task_checklist.show_checks">
      <segment>
        <source>u2.task_checklist.show_checks</source>
        <target>Show the checks that are not assigned to the current workflow status</target>
      </segment>
    </unit>
    <unit id="UTfXvLm" name="u2.task_field.coupon_interest_rate_type">
      <segment>
        <source>u2.task_field.coupon_interest_rate_type</source>
        <target>Coupon/Interest Rate Type</target>
      </segment>
    </unit>
    <unit id="M2dVIeT" name="u2.task_field.coupon_interest_rate_type.help">
      <segment>
        <source>u2.task_field.coupon_interest_rate_type.help</source>
        <target>Agreed type of interest rate e.g. fix, variable, mixed</target>
      </segment>
    </unit>
    <unit id="T9IlFwk" name="u2.task_field.current_period_book_value">
      <segment>
        <source>u2.task_field.current_period_book_value</source>
        <target>Book Value Current Period</target>
      </segment>
    </unit>
    <unit id="9b_12ei" name="u2.task_field.current_period_book_value.help">
      <segment>
        <source>u2.task_field.current_period_book_value.help</source>
        <target>Amount booked as of the end of current period. Enter 0 if transaction ends in current period.</target>
      </segment>
    </unit>
    <unit id="ShBCLYR" name="u2.task_field.current_period_book_value_currency">
      <segment>
        <source>u2.task_field.current_period_book_value_currency</source>
        <target>Book Value Current Period Currency</target>
      </segment>
    </unit>
    <unit id="fV6NAaa" name="u2.task_field.current_period_interest_expenses">
      <segment>
        <source>u2.task_field.current_period_interest_expenses</source>
        <target>Interest Payments Current Period</target>
      </segment>
    </unit>
    <unit id="roOmAhT" name="u2.task_field.current_period_interest_expenses.help">
      <segment>
        <source>u2.task_field.current_period_interest_expenses.help</source>
        <target>Booked cumulative interest income (+) / expenses (-) as of 1.1. of current year</target>
      </segment>
    </unit>
    <unit id="4ClfPGd" name="u2.task_field.current_period_interest_expenses_currency">
      <segment>
        <source>u2.task_field.current_period_interest_expenses_currency</source>
        <target>Interest Payments Current Period Currency</target>
      </segment>
    </unit>
    <unit id="BGommkC" name="u2.task_field.id_of_asset_liability_underlying_the_derivative">
      <segment>
        <source>u2.task_field.id_of_asset_liability_underlying_the_derivative</source>
        <target>ID Code of Asset/Liability Underlying the Derivative</target>
      </segment>
    </unit>
    <unit id="jeFKY9A" name="u2.task_field.period">
      <segment>
        <source>u2.task_field.period</source>
        <target>Period</target>
      </segment>
    </unit>
    <unit id="PGjxL8W" name="u2.task_field.previous_period_book_value">
      <segment>
        <source>u2.task_field.previous_period_book_value</source>
        <target>Book Value Previous Period</target>
      </segment>
    </unit>
    <unit id="yTMlB00" name="u2.task_field.previous_period_book_value.help">
      <segment>
        <source>u2.task_field.previous_period_book_value.help</source>
        <target>Booked amount as of 31.12. of previous year. Enter 0 if transaction starts in current year.</target>
      </segment>
    </unit>
    <unit id="2zC7qv6" name="u2.task_field.previous_period_book_value_currency">
      <segment>
        <source>u2.task_field.previous_period_book_value_currency</source>
        <target>Book Value Previous Period Currency</target>
      </segment>
    </unit>
    <unit id="wwjRlpX" name="u2.task_field.source_id">
      <segment>
        <source>u2.task_field.source_id</source>
        <target>Source Id</target>
      </segment>
    </unit>
    <unit id="OY648a1" name="u2.tax_accounting.formula_compile_error">
      <segment>
        <source>u2.tax_accounting.formula_compile_error</source>
        <target>Could not compile the formula "%formula%" or one of it's dependencies</target>
      </segment>
    </unit>
    <unit id="c.vFeD8" name="u2.tax_accounting.item_breakdown">
      <segment>
        <source>u2.tax_accounting.item_breakdown</source>
        <target>Item Breakdown:</target>
      </segment>
    </unit>
    <unit id="LNSgZJ3" name="u2.tax_number.add">
      <segment>
        <source>u2.tax_number.add</source>
        <target>Add tax number</target>
      </segment>
    </unit>
    <unit id="Z3f9JR6" name="u2.tax_numbers">
      <segment>
        <source>u2.tax_numbers</source>
        <target>Tax numbers</target>
      </segment>
    </unit>
    <unit id="sUesKF5" name="u2.transaction.asset_liability_id">
      <segment>
        <source>u2.transaction.asset_liability_id</source>
        <target>Asset/Liability ID</target>
      </segment>
    </unit>
    <unit id="4yjFXxu" name="u2.transaction.coupon_interest_rate">
      <segment>
        <source>u2.transaction.coupon_interest_rate</source>
        <target>Coupon/Interest Rate</target>
      </segment>
    </unit>
    <unit id="kEBmWql" name="u2.transaction.forward_rate">
      <segment>
        <source>u2.transaction.forward_rate</source>
        <target>Forward Rate</target>
      </segment>
    </unit>
    <unit id="HWZew8F" name="u2.transaction.guarantee_fee_amount">
      <segment>
        <source>u2.transaction.guarantee_fee_amount</source>
        <target>Guarantee Fee Amount</target>
      </segment>
    </unit>
    <unit id="Zj2FesK" name="u2.transaction.guarantee_fee_currency">
      <segment>
        <source>u2.transaction.guarantee_fee_currency</source>
        <target>Guarantee Fee Currency</target>
      </segment>
    </unit>
    <unit id="ZH0lHOQ" name="u2.transition">
      <segment>
        <source>u2.transition</source>
        <target>Transition</target>
      </segment>
    </unit>
    <unit id="yY6lC9c" name="u2.transition_required">
      <segment>
        <source>u2.transition_required</source>
        <target>You must select a transition.</target>
      </segment>
    </unit>
    <unit id="GDV.GH_" name="u2.transition_selected_records">
      <segment>
        <source>u2.transition_selected_records</source>
        <target>Transition Selected Record(s)</target>
      </segment>
    </unit>
    <unit id="WF.Efcr" name="u2.try_later">
      <segment>
        <source>u2.try_later</source>
        <target>Please try again later.</target>
      </segment>
    </unit>
    <unit id=".oBvgRg" name="u2.two_factor.email.subject">
      <segment>
        <source>u2.two_factor.email.subject</source>
        <target>Authentication Code: %authentication_code%</target>
      </segment>
    </unit>
    <unit id="dYTxN6Z" name="u2.unassigned">
      <segment>
        <source>u2.unassigned</source>
        <target>Unassigned</target>
      </segment>
    </unit>
    <unit id="1I0fvn0" name="u2.unauthorized">
      <segment>
        <source>u2.unauthorized</source>
        <target>Unauthorized</target>
      </segment>
    </unit>
    <unit id="HEB55zI" name="u2.unchecked">
      <segment>
        <source>u2.unchecked</source>
        <target>Unchecked</target>
      </segment>
    </unit>
    <unit id=".54v26d" name="u2.under_maintenance">
      <segment>
        <source>u2.under_maintenance</source>
        <target>Under maintenance</target>
      </segment>
    </unit>
    <unit id="p.tArjO" name="u2.unit">
      <segment>
        <source>u2.unit</source>
        <target>Unit</target>
      </segment>
    </unit>
    <unit id="EC62eIg" name="u2.unit.legal_name.inherit_from_name">
      <segment>
        <source>u2.unit.legal_name.inherit_from_name</source>
        <target>Inherit legal name from name</target>
      </segment>
    </unit>
    <unit id="_nWPuO7" name="u2.unit.plural">
      <segment>
        <source>u2.unit.plural</source>
        <target>Units</target>
      </segment>
    </unit>
    <unit id="8U_ilOl" name="u2.unit_edit_field_white_list">
      <segment>
        <source>u2.unit_edit_field_white_list</source>
        <target>Unit edit field white list</target>
      </segment>
    </unit>
    <unit id="iwTGkQN" name="u2.unit_hierarchies">
      <segment>
        <source>u2.unit_hierarchies</source>
        <target>Unit Hierarchies</target>
      </segment>
    </unit>
    <unit id="xvHs9wJ" name="u2.unit_hierarchy">
      <segment>
        <source>u2.unit_hierarchy</source>
        <target>Unit Hierarchy</target>
      </segment>
    </unit>
    <unit id="nFloo09" name="u2.unit_hierarchy.structure.latest_previous_change">
      <segment>
        <source>u2.unit_hierarchy.structure.latest_previous_change</source>
        <target>Previous Change (%lastChangeDate%)</target>
      </segment>
    </unit>
    <unit id="Q1f1RbQ" name="u2.unit_hierarchy.structure.next_change">
      <segment>
        <source>u2.unit_hierarchy.structure.next_change</source>
        <target>Next Change (%nextChangeDate%)</target>
      </segment>
    </unit>
    <unit id="FITugrL" name="u2.unit_hierarchy_list">
      <segment>
        <source>u2.unit_hierarchy_list</source>
        <target>Unit Hierarchy List</target>
      </segment>
    </unit>
    <unit id="7niMZUb" name="u2.unit_is_inherited">
      <segment>
        <source>u2.unit_is_inherited</source>
        <target>This unit is inherited from a group.</target>
      </segment>
    </unit>
    <unit id="uWkWwBN" name="u2.unit_list">
      <segment>
        <source>u2.unit_list</source>
        <target>Unit List</target>
      </segment>
    </unit>
    <unit id="K8D.oXR" name="u2.unit_pool">
      <segment>
        <source>u2.unit_pool</source>
        <target>Unit Pool</target>
      </segment>
    </unit>
    <unit id="NLpYSA2" name="u2.unknown">
      <segment>
        <source>u2.unknown</source>
        <target>Unknown</target>
      </segment>
    </unit>
    <unit id="gJigwj4" name="u2.unknown_error_occurred">
      <segment>
        <source>u2.unknown_error_occurred</source>
        <target>An unknown error occurred. Please contact an admin for further information.</target>
      </segment>
    </unit>
    <unit id="cduTwep" name="u2.unlock">
      <segment>
        <source>u2.unlock</source>
        <target>Unlock</target>
      </segment>
    </unit>
    <unit id="jdtPhJL" name="u2.unlock_user">
      <segment>
        <source>u2.unlock_user</source>
        <target>Unlock user</target>
      </segment>
    </unit>
    <unit id="cax0jht" name="u2.unlock_user_confirmation">
      <segment>
        <source>u2.unlock_user_confirmation</source>
        <target>Are you sure you want to unlock this user?</target>
      </segment>
    </unit>
    <unit id="dukvbOJ" name="u2.unlock_user_successful">
      <segment>
        <source>u2.unlock_user_successful</source>
        <target>User unlocked successfully.</target>
      </segment>
    </unit>
    <unit id="G954L_u" name="u2.unlock_user_unsuccessful">
      <segment>
        <source>u2.unlock_user_unsuccessful</source>
        <target>The user could not be unlocked.</target>
      </segment>
    </unit>
    <unit id="6vQgKQg" name="u2.update_filter_with_given_saved_filter">
      <segment>
        <source>u2.update_filter_with_given_saved_filter</source>
        <target>Update the filter "%saved_filter_name%"</target>
      </segment>
    </unit>
    <unit id="d.Ie5zM" name="u2.update_saved_filter">
      <segment>
        <source>u2.update_saved_filter</source>
        <target>Update Saved Filter</target>
      </segment>
    </unit>
    <unit id="5cv.sy." name="u2.user_already_unlocked">
      <segment>
        <source>u2.user_already_unlocked</source>
        <target>The user is already unlocked.</target>
      </segment>
    </unit>
    <unit id="CV8oEj4" name="u2.user_is_inherited">
      <segment>
        <source>u2.user_is_inherited</source>
        <target>This user is inherited from a group.</target>
      </segment>
    </unit>
    <unit id="gsUz_W2" name="u2.user_permissions">
      <segment>
        <source>u2.user_permissions</source>
        <target>User Permissions</target>
      </segment>
    </unit>
    <unit id="0J.WqhX" name="u2.user_requesting_permissions">
      <segment>
        <source>u2.user_requesting_permissions</source>
        <target>The user %user_name% is requesting permissions to the file %file_name% with the following message:</target>
      </segment>
    </unit>
    <unit id="1BIkhqj" name="u2.user_settings.two_factor.enforced_by_admin">
      <segment>
        <source>u2.user_settings.two_factor.enforced_by_admin</source>
        <target>The two-factor authentication has been activated by the system administrator and therefore cannot be deactivated</target>
      </segment>
    </unit>
    <unit id="PgK29zQ" name="u2.vat_number">
      <segment>
        <source>u2.vat_number</source>
        <target>VAT Number</target>
      </segment>
    </unit>
    <unit id="_GTGPbd" name="u2.visibility_group_permissions_warning">
      <segment>
        <source>u2.visibility_group_permissions_warning</source>
        <target>Assigned groups have no effect because "Visible to all users" is on</target>
      </segment>
    </unit>
    <unit id="o4H5wfz" name="u2.visibility_user_permissions_warning">
      <segment>
        <source>u2.visibility_user_permissions_warning</source>
        <target>Assigned users have no effect because "Visible to all users" is on</target>
      </segment>
    </unit>
    <unit id="qixvmvA" name="u2.visible_to_all_users">
      <segment>
        <source>u2.visible_to_all_users</source>
        <target>Visible to all users</target>
      </segment>
    </unit>
    <unit id="2Wj0IUE" name="u2.watch.start_watching">
      <segment>
        <source>u2.watch.start_watching</source>
        <target>Start watching</target>
      </segment>
    </unit>
    <unit id="fcHHYfj" name="u2.watch.stop_watching">
      <segment>
        <source>u2.watch.stop_watching</source>
        <target>Stop watching</target>
      </segment>
    </unit>
    <unit id=".yzXve0" name="u2.widget.incompatible_arguments">
      <segment>
        <source>u2.widget.incompatible_arguments</source>
        <target>The values of the following parameters are incompatible: %properties%. Unable to render widget “%widget_name%”.</target>
      </segment>
    </unit>
    <unit id="f99zXdN" name="u2.widget.transaction_table.sub_filter">
      <segment>
        <source>u2.widget.transaction_table.sub_filter</source>
        <target>Sub Filter</target>
      </segment>
    </unit>
    <unit id="4frnSES" name="u2.widget.transaction_table.sub_filter.help">
      <segment>
        <source>u2.widget.transaction_table.sub_filter.help</source>
        <target>The “Sub Filter” is incompatible with “Group results”. Use either “Group results” or “Sub Filter”</target>
      </segment>
    </unit>
    <unit id="7kFr0py" name="u2.workflow.configuration_error">
      <segment>
        <source>u2.workflow.configuration_error</source>
        <target>Please ensure the workflow is correctly configured</target>
      </segment>
    </unit>
    <unit id="cvkeoy1" name="u2.workflow.could_not_transition_status_changed">
      <segment>
        <source>u2.workflow.could_not_transition_status_changed</source>
        <target>Could not execute transition of %count% record because the record status has been already changed or the transition has been deleted.|Could not execute transition of %count% records because their status has been already
          changed or the transition has been deleted.
        </target>
      </segment>
    </unit>
    <unit id="2_AzUwq" name="u2.workflow.transition_confirm">
      <segment>
        <source>u2.workflow.transition_confirm</source>
        <target>You are about to run transition "%transition_name%"</target>
      </segment>
    </unit>
    <unit id="Yc9RXHi" name="u2.workflow_check.disabled">
      <segment>
        <source>u2.workflow_check.disabled</source>
        <target>This check has been disabled by your administrator.</target>
      </segment>
    </unit>
    <unit id="L8bQdFZ" name="u2.xbrl_generation_fail">
      <segment>
        <source>u2.xbrl_generation_fail</source>
        <target>The XBRL file could not be generated.</target>
      </segment>
    </unit>
    <unit id="Abq4NnG" name="u2.xml_file">
      <segment>
        <source>u2.xml_file</source>
        <target>XML file</target>
      </segment>
    </unit>
    <unit id="DqOi.tn" name="u2.yes">
      <segment>
        <source>u2.yes</source>
        <target>Yes</target>
      </segment>
    </unit>
    <unit id="Thc59bM" name="u2_apm.module_name">
      <segment>
        <source>u2_apm.module_name</source>
        <target><![CDATA[Asset & Participation Management]]></target>
      </segment>
    </unit>
    <unit id="oS9eiKD" name="u2_comment.comment">
      <segment>
        <source>u2_comment.comment</source>
        <target>Comment</target>
      </segment>
    </unit>
    <unit id="vqZCEt_" name="u2_comment.delete_comment">
      <segment>
        <source>u2_comment.delete_comment</source>
        <target>Delete Comment</target>
      </segment>
    </unit>
    <unit id="fMkzlIX" name="u2_comment.delete_comment.confirmation">
      <segment>
        <source>u2_comment.delete_comment.confirmation</source>
        <target>Are you sure you want to delete this comment?</target>
      </segment>
    </unit>
    <unit id="JV9DCg8" name="u2_comment.deleted_comment">
      <segment>
        <source>u2_comment.deleted_comment</source>
        <target>This comment has been deleted.</target>
      </segment>
    </unit>
    <unit id="OsZjnto" name="u2_comment.edit_comment">
      <segment>
        <source>u2_comment.edit_comment</source>
        <target>Edit Comment</target>
      </segment>
    </unit>
    <unit id="UFpy9es" name="u2_comment.new_comment">
      <segment>
        <source>u2_comment.new_comment</source>
        <target>New Comment</target>
      </segment>
    </unit>
    <unit id="l4m7aMG" name="u2_comment.unrestricted">
      <segment>
        <source>u2_comment.unrestricted</source>
        <target>Unrestricted</target>
      </segment>
    </unit>
    <unit id="DeHrkOE" name="u2_comment.quote">
      <segment>
        <source>u2_comment.quote</source>
        <target>Quote</target>
      </segment>
    </unit>
    <unit id="TeOktpp" name="u2_comment.restricted_to_given_group">
      <segment>
        <source>u2_comment.restricted_to_given_group</source>
        <target>This comment is restricted to group: %group_name%</target>
      </segment>
    </unit>
    <unit id="cKz8il." name="u2_contractmanagement.contract">
      <segment>
        <source>u2_contractmanagement.contract</source>
        <target>Contract</target>
      </segment>
    </unit>
    <unit id="DA1yjFe" name="u2_contractmanagement.contract.plural">
      <segment>
        <source>u2_contractmanagement.contract.plural</source>
        <target>Contracts</target>
      </segment>
    </unit>
    <unit id="i0_0wXv" name="u2_contractmanagement.contract_type">
      <segment>
        <source>u2_contractmanagement.contract_type</source>
        <target>Contract Type</target>
      </segment>
    </unit>
    <unit id="_YVA2co" name="u2_contractmanagement.contract_type.plural">
      <segment>
        <source>u2_contractmanagement.contract_type.plural</source>
        <target>Contract Types</target>
      </segment>
    </unit>
    <unit id="gAhguhB" name="u2_contractmanagement.date">
      <segment>
        <source>u2_contractmanagement.date</source>
        <target>Date</target>
      </segment>
    </unit>
    <unit id="AWcEx.w" name="u2_contractmanagement.days_until_automatic_renewal">
      <segment>
        <source>u2_contractmanagement.days_until_automatic_renewal</source>
        <target>Days Until Automatic Renewal</target>
      </segment>
    </unit>
    <unit id="JZML_hs" name="u2_contractmanagement.details">
      <segment>
        <source>u2_contractmanagement.details</source>
        <target>Details</target>
      </segment>
    </unit>
    <unit id="pkukxyK" name="u2_contractmanagement.expiry_date">
      <segment>
        <source>u2_contractmanagement.expiry_date</source>
        <target>Expiry Date</target>
      </segment>
    </unit>
    <unit id="NovIhGK" name="u2_contractmanagement.identification_code">
      <segment>
        <source>u2_contractmanagement.identification_code</source>
        <target>Identification Code</target>
      </segment>
    </unit>
    <unit id="tqj5B5h" name="u2_contractmanagement.name">
      <segment>
        <source>u2_contractmanagement.name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="Uwjgt2J" name="u2_contractmanagement.notice_period_in_days">
      <segment>
        <source>u2_contractmanagement.notice_period_in_days</source>
        <target>Notice Period in Days</target>
      </segment>
    </unit>
    <unit id="RO2.Bcv" name="u2_contractmanagement.parties">
      <segment>
        <source>u2_contractmanagement.parties</source>
        <target>Parties</target>
      </segment>
    </unit>
    <unit id="._bOKrT" name="u2_contractmanagement.partner_is_third_party">
      <segment>
        <source>u2_contractmanagement.partner_is_third_party</source>
        <target>Partner is Third Party</target>
      </segment>
    </unit>
    <unit id="cFhK_5c" name="u2_contractmanagement.partner_unit">
      <segment>
        <source>u2_contractmanagement.partner_unit</source>
        <target>Partner Unit</target>
      </segment>
    </unit>
    <unit id="W2McoO5" name="u2_contractmanagement.partner_unit_country">
      <segment>
        <source>u2_contractmanagement.partner_unit_country</source>
        <target>Partner Unit Country</target>
      </segment>
    </unit>
    <unit id=".BxwVr0" name="u2_contractmanagement.partner_unit_name">
      <segment>
        <source>u2_contractmanagement.partner_unit_name</source>
        <target>Partner Unit Name</target>
      </segment>
    </unit>
    <unit id="_8CoEfg" name="u2_contractmanagement.reminder_date">
      <segment>
        <source>u2_contractmanagement.reminder_date</source>
        <target>Reminder Date</target>
      </segment>
    </unit>
    <unit id="r9WWLqp" name="u2_contractmanagement.third_party_country">
      <segment>
        <source>u2_contractmanagement.third_party_country</source>
        <target>Third Party Country</target>
      </segment>
    </unit>
    <unit id="CA_lkEp" name="u2_contractmanagement.third_party_name">
      <segment>
        <source>u2_contractmanagement.third_party_name</source>
        <target>Third Party Name</target>
      </segment>
    </unit>
    <unit id="q3a_F3Z" name="u2_contractmanagement.unit_country">
      <segment>
        <source>u2_contractmanagement.unit_country</source>
        <target>Unit Country</target>
      </segment>
    </unit>
    <unit id="hBzk7.L" name="u2_core.about">
      <segment>
        <source>u2_core.about</source>
        <target>About</target>
      </segment>
    </unit>
    <unit id="ErZsOke" name="u2_core.access_type">
      <segment>
        <source>u2_core.access_type</source>
        <target>Access Type</target>
      </segment>
    </unit>
    <unit id="zszjnnp" name="u2_core.account">
      <segment>
        <source>u2_core.account</source>
        <target>Account</target>
      </segment>
    </unit>
    <unit id="AlaR8nk" name="u2_core.account_expires">
      <segment>
        <source>u2_core.account_expires</source>
        <target>Account Expires</target>
      </segment>
    </unit>
    <unit id="8yiGBZX" name="u2.expires">
      <segment>
        <source>u2.expires</source>
        <target>Expires at</target>
      </segment>
    </unit>
    <unit id="sv6i97S" name="u2.last_used_at">
      <segment>
        <source>u2.last_used_at</source>
        <target>Last used at</target>
      </segment>
    </unit>
    <unit id="AZnCmAa" name="u2_core.account_number">
      <segment>
        <source>u2_core.account_number</source>
        <target>Account number</target>
      </segment>
    </unit>
    <unit id="NhX9lLU" name="u2_core.account_number_help">
      <segment>
        <source>u2_core.account_number_help</source>
        <target>The BZSt Online Portal (BOP) account number of the account that will be used to transmit the CbCR XML file to the Bundeszentralamt für Steuern. When logged in, you find the number under the menu item "My BOP".</target>
      </segment>
    </unit>
    <unit id="pmCOnDP" name="u2_core.active">
      <segment>
        <source>u2_core.active</source>
        <target>Active</target>
      </segment>
    </unit>
    <unit id="PcQ16do" name="u2_core.add_a_review">
      <segment>
        <source>u2_core.add_a_review</source>
        <target>Add a Review</target>
      </segment>
    </unit>
    <unit id="NEWPgDG" name="u2.add_attachment">
      <segment>
        <source>u2.add_attachment</source>
        <target>Add Attachment</target>
      </segment>
    </unit>
    <unit id="dE6U2eX" name="u2_core.add_new_calendar_entry">
      <segment>
        <source>u2_core.add_new_calendar_entry</source>
        <target>Add New Calendar Entry</target>
      </segment>
    </unit>
    <unit id="Ec2Ahcw" name="u2_core.add_new_entry">
      <segment>
        <source>u2_core.add_new_entry</source>
        <target>Add New Entry</target>
      </segment>
    </unit>
    <unit id="7qnTsTf" name="u2_core.add_new_exchange_rate">
      <segment>
        <source>u2_core.add_new_exchange_rate</source>
        <target>Add New Exchange Rate</target>
      </segment>
    </unit>
    <unit id="eISe9pW" name="u2_core.add_new_given_entity_type">
      <segment>
        <source>u2_core.add_new_given_entity_type</source>
        <target>Add New %entity_type_name%</target>
      </segment>
    </unit>
    <unit id="N.Y4Ait" name="u2_core.add_new_hierarchy">
      <segment>
        <source>u2_core.add_new_hierarchy</source>
        <target>Add New Hierarchy</target>
      </segment>
    </unit>
    <unit id="g8sstfd" name="u2_core.add_new_period">
      <segment>
        <source>u2_core.add_new_period</source>
        <target>Add New Period</target>
      </segment>
    </unit>
    <unit id="zFgkEbk" name="u2_core.add_new_status">
      <segment>
        <source>u2_core.add_new_status</source>
        <target>Add New Status</target>
      </segment>
    </unit>
    <unit id="BfDj3XP" name="u2_core.add_new_system_message">
      <segment>
        <source>u2_core.add_new_system_message</source>
        <target>Add New System Message</target>
      </segment>
    </unit>
    <unit id="bf15d4J" name="u2_core.add_new_unit">
      <segment>
        <source>u2_core.add_new_unit</source>
        <target>Add New Unit</target>
      </segment>
    </unit>
    <unit id="wH.hJeA" name="u2_core.add_new_user">
      <segment>
        <source>u2_core.add_new_user</source>
        <target>Add New User</target>
      </segment>
    </unit>
    <unit id="weQWACO" name="u2_core.add_new_user.success">
      <segment>
        <source>u2_core.add_new_user.success</source>
        <target>The user was successfully created.</target>
      </segment>
    </unit>
    <unit id="p.GX3Vl" name="u2_core.add_new_user_group">
      <segment>
        <source>u2_core.add_new_user_group</source>
        <target>Add New User Group</target>
      </segment>
    </unit>
    <unit id="EJTZP7G" name="u2_core.add_subscription">
      <segment>
        <source>u2_core.add_subscription</source>
        <target>Add subscription</target>
      </segment>
    </unit>
    <unit id="KvxJdvZ" name="u2_core.add_unit.warning_selected_hierarchy_has_no_units_for_this_date">
      <segment>
        <source>u2_core.add_unit.warning_selected_hierarchy_has_no_units_for_this_date</source>
        <target>The selected hierarchy does not have any units for this date.</target>
      </segment>
    </unit>
    <unit id="l_GyBJQ" name="u2_core.added_on_given_date">
      <segment>
        <source>u2_core.added_on_given_date</source>
        <target>Added on %date%</target>
      </segment>
    </unit>
    <unit id="UNZLd47" name="u2_core.address">
      <segment>
        <source>u2_core.address</source>
        <target>Address</target>
      </segment>
    </unit>
    <unit id="ALZRmfj" name="u2_core.administration">
      <segment>
        <source>u2_core.administration</source>
        <target>Administration</target>
      </segment>
    </unit>
    <unit id="63LQmI2" name="u2_core.application">
      <segment>
        <source>u2_core.application</source>
        <target>Application</target>
      </segment>
    </unit>
    <unit id="FD80Gi4" name="u2_core.application_language">
      <segment>
        <source>u2_core.application_language</source>
        <target>Application Language</target>
      </segment>
    </unit>
    <unit id="UYC3nwA" name="u2_core.assign_extra_permissions">
      <segment>
        <source>u2_core.assign_extra_permissions</source>
        <target>Assign Extra Permissions</target>
      </segment>
    </unit>
    <unit id="PEb.MTO" name="u2_core.assign_to_me">
      <segment>
        <source>u2_core.assign_to_me</source>
        <target>Assign to me</target>
      </segment>
    </unit>
    <unit id="JmB8eRY" name="u2_core.assign_user_to_given_entity_name">
      <segment>
        <source>u2_core.assign_user_to_given_entity_name</source>
        <target>Assign User to “%entity_name%”</target>
      </segment>
    </unit>
    <unit id="BZbU4xy" name="u2_core.assigned">
      <segment>
        <source>u2_core.assigned</source>
        <target>Assigned</target>
      </segment>
    </unit>
    <unit id="xQjzyqD" name="u2_core.assigned_components">
      <segment>
        <source>u2_core.assigned_components</source>
        <target>Assigned Components</target>
      </segment>
    </unit>
    <unit id="O7Sw17S" name="u2.assigned_user_groups">
      <segment>
        <source>u2.assigned_user_groups</source>
        <target>Assigned Groups</target>
      </segment>
    </unit>
    <unit id="XV0xopl" name="u2_core.assigned_users">
      <segment>
        <source>u2_core.assigned_users</source>
        <target>Assigned Users</target>
      </segment>
    </unit>
    <unit id="5fBgiXk" name="u2_core.assignee">
      <segment>
        <source>u2_core.assignee</source>
        <target>Assignee</target>
      </segment>
    </unit>
    <unit id="V68YpPx" name="u2_core.associated_statuses">
      <segment>
        <source>u2_core.associated_statuses</source>
        <target>Associated statuses</target>
      </segment>
    </unit>
    <unit id="xdNsl9." name="u2_core.attachments">
      <segment>
        <source>u2_core.attachments</source>
        <target>Attachments</target>
      </segment>
    </unit>
    <unit id="v06MInx" name="u2_core.attributes">
      <segment>
        <source>u2_core.attributes</source>
        <target>Attributes</target>
      </segment>
    </unit>
    <unit id="aMiMzo9" name="u2_core.audit">
      <segment>
        <source>u2_core.audit</source>
        <target>Audit</target>
      </segment>
    </unit>
    <unit id="sXrg1Uw" name="u2_core.auditor">
      <segment>
        <source>u2_core.auditor</source>
        <target>Auditor</target>
      </segment>
    </unit>
    <unit id="Mf8vvLq" name="u2_core.auditor.plural">
      <segment>
        <source>u2_core.auditor.plural</source>
        <target>Auditors</target>
      </segment>
    </unit>
    <unit id="iOusbT1" name="u2_core.authorisation.add_authorisation">
      <segment>
        <source>u2_core.authorisation.add_authorisation</source>
        <target>Add Authorisation</target>
      </segment>
    </unit>
    <unit id="cY4YBtQ" name="u2_core.authorisation.add_authorisation_profile">
      <segment>
        <source>u2_core.authorisation.add_authorisation_profile</source>
        <target>Add Authorisation Profile</target>
      </segment>
    </unit>
    <unit id="4ObAn0u" name="u2_core.authorisation.add_new_authorisation">
      <segment>
        <source>u2_core.authorisation.add_new_authorisation</source>
        <target>Add New Authorisation</target>
      </segment>
    </unit>
    <unit id="srryaaq" name="u2_core.authorisation.add_new_profile">
      <segment>
        <source>u2_core.authorisation.add_new_profile</source>
        <target>Add New Profile</target>
      </segment>
    </unit>
    <unit id="YqzkplI" name="u2_core.authorisation.authorisation">
      <segment>
        <source>u2_core.authorisation.authorisation</source>
        <target>Authorisation</target>
      </segment>
    </unit>
    <unit id="EIMnfbH" name="u2_core.authorisation.authorisation_list">
      <segment>
        <source>u2_core.authorisation.authorisation_list</source>
        <target>Authorisation List</target>
      </segment>
    </unit>
    <unit id="XR31Q3W" name="u2_core.authorisation.authorisation_overview">
      <segment>
        <source>u2_core.authorisation.authorisation_overview</source>
        <target>Authorisation Overview</target>
      </segment>
    </unit>
    <unit id="XXn6zur" name="u2_core.authorisation.authorisation_profile">
      <segment>
        <source>u2_core.authorisation.authorisation_profile</source>
        <target>Authorisation Profile</target>
      </segment>
    </unit>
    <unit id="YvNwQt8" name="u2_core.authorisation.authorisations">
      <segment>
        <source>u2_core.authorisation.authorisations</source>
        <target>Authorisations</target>
      </segment>
    </unit>
    <unit id="gLckxy." name="u2_core.authorisation.authorised_groups">
      <segment>
        <source>u2_core.authorisation.authorised_groups</source>
        <target>Authorised Groups</target>
      </segment>
    </unit>
    <unit id="Cwzhp8k" name="u2_core.authorisation.authorised_users">
      <segment>
        <source>u2_core.authorisation.authorised_users</source>
        <target>Authorised Users</target>
      </segment>
    </unit>
    <unit id="Kwju_bL" name="u2_core.authorisation.create_authorisation.success">
      <segment>
        <source>u2_core.authorisation.create_authorisation.success</source>
        <target>Authorization created successfully.</target>
      </segment>
    </unit>
    <unit id="h3DO2Di" name="u2_core.authorisation.create_authorisation_profile.success">
      <segment>
        <source>u2_core.authorisation.create_authorisation_profile.success</source>
        <target>Authorization profile created successfully.</target>
      </segment>
    </unit>
    <unit id="q5HD2JQ" name="u2_core.authorisation.delete_authorisation_with_given_name">
      <segment>
        <source>u2_core.authorisation.delete_authorisation_with_given_name</source>
        <target>Delete authorisation (%authorization_name%)</target>
      </segment>
    </unit>
    <unit id="LPiF7V." name="u2_core.authorisation.delete_profile_with_given_name">
      <segment>
        <source>u2_core.authorisation.delete_profile_with_given_name</source>
        <target>Delete profile (%profile_name%)</target>
      </segment>
    </unit>
    <unit id="iLtuvt2" name="u2_core.authorisation.edit_authorisation">
      <segment>
        <source>u2_core.authorisation.edit_authorisation</source>
        <target>Edit Authorisation</target>
      </segment>
    </unit>
    <unit id="RQZ1XwD" name="u2_core.authorisation.edit_profile">
      <segment>
        <source>u2_core.authorisation.edit_profile</source>
        <target>Edit Profile</target>
      </segment>
    </unit>
    <unit id="9Vwv.AN" name="u2_core.authorisation.item">
      <segment>
        <source>u2_core.authorisation.item</source>
        <target>Item</target>
      </segment>
    </unit>
    <unit id="7T2E97F" name="u2_core.authorisation.profiles">
      <segment>
        <source>u2_core.authorisation.profiles</source>
        <target>Profiles</target>
      </segment>
    </unit>
    <unit id="M5.bnSy" name="u2_core.authorisation.requires_authorisation_to">
      <segment>
        <source>u2_core.authorisation.requires_authorisation_to</source>
        <target>Requires authorisation to</target>
      </segment>
    </unit>
    <unit id="U_ory5j" name="u2_core.authorisation.rights">
      <segment>
        <source>u2_core.authorisation.rights</source>
        <target>Rights</target>
      </segment>
    </unit>
    <unit id="Zm6ihnM" name="u2_core.authorisation.update_authorisation.success">
      <segment>
        <source>u2_core.authorisation.update_authorisation.success</source>
        <target>Authorization updated successfully.</target>
      </segment>
    </unit>
    <unit id="HSLaUhw" name="u2_core.authorisation.update_authorisation_profile.success">
      <segment>
        <source>u2_core.authorisation.update_authorisation_profile.success</source>
        <target>Authorization profile updated successfully.</target>
      </segment>
    </unit>
    <unit id="R7N_BGT" name="u2_core.available_options">
      <segment>
        <source>u2_core.available_options</source>
        <target>Available options</target>
      </segment>
    </unit>
    <unit id="yNQfGEI" name="u2_core.back_to_the_support_information_page">
      <segment>
        <source>u2_core.back_to_the_support_information_page</source>
        <target>Back to the support information page</target>
      </segment>
    </unit>
    <unit id="EuV216G" name="u2_core.back_to_transition">
      <segment>
        <source>u2_core.back_to_transition</source>
        <target>Back to Transition</target>
      </segment>
    </unit>
    <unit id="VCZab9u" name="u2_core.back_to_workflow">
      <segment>
        <source>u2_core.back_to_workflow</source>
        <target>Back to Workflow</target>
      </segment>
    </unit>
    <unit id="F3f36sM" name="u2_core.base">
      <segment>
        <source>u2_core.base</source>
        <target>Base</target>
      </segment>
    </unit>
    <unit id="F2sfm7." name="u2_core.billing_address">
      <segment>
        <source>u2_core.billing_address</source>
        <target>Billing Address</target>
      </segment>
    </unit>
    <unit id="pgvCcf0" name="u2_core.branch">
      <segment>
        <source>u2_core.branch</source>
        <target>Branch</target>
      </segment>
    </unit>
    <unit id="MLkP4dG" name="u2_core.branch.plural">
      <segment>
        <source>u2_core.branch.plural</source>
        <target>Branches</target>
      </segment>
    </unit>
    <unit id="7AR9wvO" name="u2_core.bulk_delete.error_no_permission">
      <segment>
        <source>u2_core.bulk_delete.error_no_permission</source>
        <target>You are not allowed to delete one or more of the selected entries.</target>
      </segment>
    </unit>
    <unit id="IP9fyJH" name="u2_core.bulk_delete.success_for_given_amount_of_entries">
      <segment>
        <source>u2_core.bulk_delete.success_for_given_amount_of_entries</source>
        <target>Entry removed successfully.|%count% entries removed successfully.</target>
      </segment>
    </unit>
    <unit id="kBveGLh" name="u2_core.bulk_edit.invalid_record">
      <segment>
        <source>u2_core.bulk_edit.invalid_record</source>
        <target>Record with ID %id% is invalid with the message: %violation_message%</target>
      </segment>
    </unit>
    <unit id="If6BL_s" name="u2_core.bulk_edit_given_amount_of_selected_records.confirmation">
      <segment>
        <source>u2_core.bulk_edit_given_amount_of_selected_records.confirmation</source>
        <target>Are you sure that you want change the %count% selected records?</target>
      </segment>
    </unit>
    <unit id="pdT6Gdt" name="u2_core.bulk_edit_given_item_with_given_count">
      <segment>
        <source>u2_core.bulk_edit_given_item_with_given_count</source>
        <target>Bulk Edit %item_name% (%count%)</target>
      </segment>
    </unit>
    <unit id="b8aM58j" name="u2_core.bulk_update.error_no_permission_to_edit_the_selected_records">
      <segment>
        <source>u2_core.bulk_update.error_no_permission_to_edit_the_selected_records</source>
        <target>You do not have permission to edit the selected records.</target>
      </segment>
    </unit>
    <unit id="TEszteI" name="u2_core.bulk_update.success">
      <segment>
        <source>u2_core.bulk_update.success</source>
        <target>You successfully updated the selected records.</target>
      </segment>
    </unit>
    <unit id="nTsTmQt" name="u2_core.bzst_number">
      <segment>
        <source>u2_core.bzst_number</source>
        <target>BZSt number</target>
      </segment>
    </unit>
    <unit id="WMxku6l" name="u2_core.bzst_number_help">
      <segment>
        <source>u2_core.bzst_number_help</source>
        <target>The BZSt Number that will be used to transmit the CbCR XML file on the BZSt online portal (BOP) to the Bundeszentralamt für Steuern.</target>
      </segment>
    </unit>
    <unit id="vSLgbgM" name="u2_core.calendar">
      <segment>
        <source>u2_core.calendar</source>
        <target>Calendar</target>
      </segment>
    </unit>
    <unit id="_Jv.CQ3" name="u2_core.calendar_week_with_given_week_number">
      <segment>
        <source>u2_core.calendar_week_with_given_week_number</source>
        <target>Calendar week %week_number%</target>
      </segment>
    </unit>
    <unit id="SPymmb4" name="u2_core.change_password">
      <segment>
        <source>u2_core.change_password</source>
        <target>Change Password</target>
      </segment>
    </unit>
    <unit id="XPYsMDW" name="u2_core.change_password.success">
      <segment>
        <source>u2_core.change_password.success</source>
        <target>Password changed.</target>
      </segment>
    </unit>
    <unit id="GXq1ERO" name="u2_core.change_password.success_please_login_using_your_new_password">
      <segment>
        <source>u2_core.change_password.success_please_login_using_your_new_password</source>
        <target>Password was changed successfully. Please login using your new password.</target>
      </segment>
    </unit>
    <unit id="HGSEyAB" name="u2_core.change_password_for_given_user">
      <segment>
        <source>u2_core.change_password_for_given_user</source>
        <target>Change Password for “%username%”</target>
      </segment>
    </unit>
    <unit id="13CS8C3" name="u2_core.changes">
      <segment>
        <source>u2_core.changes</source>
        <target>Changes</target>
      </segment>
    </unit>
    <unit id="lkmn6zw" name="u2_core.click_to_remove_review">
      <segment>
        <source>u2_core.click_to_remove_review</source>
        <target>Click to remove this review</target>
      </segment>
    </unit>
    <unit id="6TVno48" name="u2_core.click_to_review_this_given_entity_type">
      <segment>
        <source>u2_core.click_to_review_this_given_entity_type</source>
        <target>Click to review</target>
      </segment>
    </unit>
    <unit id="CGaBlgc" name="u2_core.click_to_show_help">
      <segment>
        <source>u2_core.click_to_show_help</source>
        <target>Click to show help</target>
      </segment>
    </unit>
    <unit id="cC1taGO" name="u2_core.client_information">
      <segment>
        <source>u2_core.client_information</source>
        <target>Client Information</target>
      </segment>
    </unit>
    <unit id="HAlfqEB" name="u2_core.client_system_information">
      <segment>
        <source>u2_core.client_system_information</source>
        <target>Client System Information</target>
      </segment>
    </unit>
    <unit id="D5Zii08" name="u2.close_menu">
      <segment>
        <source>u2.close_menu</source>
        <target>Close Menu</target>
      </segment>
    </unit>
    <unit id="0VzY4f8" name="u2_core.coloring_links_appropriately">
      <segment>
        <source>u2_core.coloring_links_appropriately</source>
        <target>Coloring Links appropriately</target>
      </segment>
    </unit>
    <unit id="TG4FSNc" name="u2_core.comments">
      <segment>
        <source>u2_core.comments</source>
        <target>Comments</target>
      </segment>
    </unit>
    <unit id="UnXHcxY" name="u2_core.company">
      <segment>
        <source>u2_core.company</source>
        <target>Company</target>
      </segment>
    </unit>
    <unit id="WuIqdO8" name="u2_core.completed_at">
      <segment>
        <source>u2_core.completed_at</source>
        <target>Completed at</target>
      </segment>
    </unit>
    <unit id="sjVgSFt" name="u2_core.component">
      <segment>
        <source>u2_core.component</source>
        <target>Component</target>
      </segment>
    </unit>
    <unit id="HQfYkU5" name="u2_core.configuration_data">
      <segment>
        <source>u2_core.configuration_data</source>
        <target>Configuration Data</target>
      </segment>
    </unit>
    <unit id="_oyUBYb" name="u2_core.configuration_information">
      <segment>
        <source>u2_core.configuration_information</source>
        <target>Configuration Information</target>
      </segment>
    </unit>
    <unit id="2EIcadt" name="u2_core.confirm_bulk_edit">
      <segment>
        <source>u2_core.confirm_bulk_edit</source>
        <target>Confirm Bulk Edit</target>
      </segment>
    </unit>
    <unit id="R61z9Y4" name="u2_core.confirm_new_password">
      <segment>
        <source>u2_core.confirm_new_password</source>
        <target>Confirm new password</target>
      </segment>
    </unit>
    <unit id="cN2.Ic3" name="u2_core.confirm_remove">
      <segment>
        <source>u2_core.confirm_remove</source>
        <target>Confirm removal</target>
      </segment>
    </unit>
    <unit id="hnn2u0S" name="u2_core.contact_user">
      <segment>
        <source>u2_core.contact_user</source>
        <target>Contact User</target>
      </segment>
    </unit>
    <unit id="cpZHA4U" name="u2_core.content">
      <segment>
        <source>u2_core.content</source>
        <target>Content</target>
      </segment>
    </unit>
    <unit id="cbiGs.S" name="u2_core.conversion">
      <segment>
        <source>u2_core.conversion</source>
        <target>Conversion</target>
      </segment>
    </unit>
    <unit id="nesvti5" name="u2_core.cookies_enabled">
      <segment>
        <source>u2_core.cookies_enabled</source>
        <target>Cookies Enabled</target>
      </segment>
    </unit>
    <unit id="Su64PmL" name="u2_core.corp_logo">
      <segment>
        <source>u2_core.corp_logo</source>
        <target>Logo</target>
      </segment>
    </unit>
    <unit id="rLHpwCz" name="u2_core.corp_logo.help">
      <segment>
        <source>u2_core.corp_logo.help</source>
        <target>
          • SVG
        </target>
      </segment>
    </unit>
    <unit id="H79Rgmd" name="u2_core.create_a_new_given_additional_name">
      <segment>
        <source>u2_core.create_a_new_given_additional_name</source>
        <target>Create a new %additional_name%</target>
      </segment>
    </unit>
    <unit id="BhMr2Bn" name="u2_core.create_file.success">
      <segment>
        <source>u2_core.create_file.success</source>
        <target>File saved.</target>
      </segment>
    </unit>
    <unit id="DI_g35h" name="u2_core.created">
      <segment>
        <source>u2_core.created</source>
        <target>Created</target>
      </segment>
    </unit>
    <unit id="xPV4uUI" name="u2_core.created_at">
      <segment>
        <source>u2_core.created_at</source>
        <target>Created at</target>
      </segment>
    </unit>
    <unit id="fnFFlBS" name="u2_core.created_by">
      <segment>
        <source>u2_core.created_by</source>
        <target>Created by</target>
      </segment>
    </unit>
    <unit id="gGiLgXE" name="u2_core.creating_new_filters">
      <segment>
        <source>u2_core.creating_new_filters</source>
        <target>Creating new filters</target>
      </segment>
    </unit>
    <unit id="fAGSypP" name="u2_core.creating_new_subscriptions">
      <segment>
        <source>u2_core.creating_new_subscriptions</source>
        <target>Creating new subscriptions</target>
      </segment>
    </unit>
    <unit id="DvJmHps" name="u2_core.cron_expression">
      <segment>
        <source>u2_core.cron_expression</source>
        <target>CRON Expression</target>
      </segment>
    </unit>
    <unit id="XxCUQeX" name="u2_core.currency">
      <segment>
        <source>u2_core.currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="wbchLi." name="u2_core.currency.missing">
      <segment>
        <source>u2_core.currency.missing</source>
        <target>The application currency has not been set. Please configure it in the system settings.</target>
      </segment>
    </unit>
    <unit id="I7YEOew" name="u2_core.currency.plural">
      <segment>
        <source>u2_core.currency.plural</source>
        <target>Currencies</target>
      </segment>
    </unit>
    <unit id="QqfmcfV" name="u2_core.current_password">
      <segment>
        <source>u2_core.current_password</source>
        <target>Current password</target>
      </segment>
    </unit>
    <unit id="D8_nmvc" name="u2_core.current_week_overview">
      <segment>
        <source>u2_core.current_week_overview</source>
        <target>Current week overview</target>
      </segment>
    </unit>
    <unit id="2Xza3yw" name="u2.dashboard">
      <segment>
        <source>u2.dashboard</source>
        <target>Dashboard</target>
      </segment>
    </unit>
    <unit id="ZWb3bSY" name="u2.dashboard.help">
      <segment>
        <source>u2.dashboard.help</source>
        <target>This is your dashboard. Here you can find tools and shortcuts to help you with your work.</target>
      </segment>
    </unit>
    <unit id="9DxWCpL" name="u2.dashboard.plural">
      <segment>
        <source>u2.dashboard.plural</source>
        <target>Dashboards</target>
      </segment>
    </unit>
    <unit id="gYj2Z1e" name="u2_core.deadline_type">
      <segment>
        <source>u2_core.deadline_type</source>
        <target>Deadline Type</target>
      </segment>
    </unit>
    <unit id="Y7x1vSN" name="u2_core.deadline_type.plural">
      <segment>
        <source>u2_core.deadline_type.plural</source>
        <target>Deadline Types</target>
      </segment>
    </unit>
    <unit id="ghl837K" name="u2_core.delete.cannot_delete_due_to_associated_data">
      <segment>
        <source>u2_core.delete.cannot_delete_due_to_associated_data</source>
        <target>It is not possible to complete this deletion due to associated data.</target>
      </segment>
    </unit>
    <unit id="wCphhPf" name="u2_core.delete_country_with_given_name">
      <segment>
        <source>u2_core.delete_country_with_given_name</source>
        <target>Delete Country (%country_name%)</target>
      </segment>
    </unit>
    <unit id="LNJwBmT" name="u2_core.delete_country_with_given_name.confirmation">
      <segment>
        <source>u2_core.delete_country_with_given_name.confirmation</source>
        <target>Are you sure you want to delete this Country (%country_name%)?</target>
      </segment>
    </unit>
    <unit id="cEGjeI3" name="u2_core.delete_current_picture.hint_use_default">
      <segment>
        <source>u2_core.delete_current_picture.hint_use_default</source>
        <target>Are you sure you want to delete the current picture and use the default one instead?</target>
      </segment>
    </unit>
    <unit id="tjXubgQ" name="u2_core.delete_entry.confirmation">
      <segment>
        <source>u2_core.delete_entry.confirmation</source>
        <target>Are you sure you want to delete this entry?</target>
      </segment>
    </unit>
    <unit id="N3w8k8c" name="u2_core.delete_exchange_rate_with_given_name">
      <segment>
        <source>u2_core.delete_exchange_rate_with_given_name</source>
        <target>Delete Exchange Rate (%exchange_rate_name%)</target>
      </segment>
    </unit>
    <unit id="FlAEOs5" name="u2_core.delete_exchange_rate_with_given_name.confirmation">
      <segment>
        <source>u2_core.delete_exchange_rate_with_given_name.confirmation</source>
        <target>Are you sure you want to delete this Exchange Rate (%exchange_rate_name%)?</target>
      </segment>
    </unit>
    <unit id="bPG7MvK" name="u2_core.delete_filter">
      <segment>
        <source>u2_core.delete_filter</source>
        <target>Delete Filter</target>
      </segment>
    </unit>
    <unit id="pP.pAdw" name="u2_core.delete_filter.confirmation">
      <segment>
        <source>u2_core.delete_filter.confirmation</source>
        <target>Are you sure you want to delete this Filter?</target>
      </segment>
    </unit>
    <unit id="PlNz2KB" name="u2_core.delete_given_entity_type">
      <segment>
        <source>u2_core.delete_given_entity_type</source>
        <target>Delete %entity_type_name%</target>
      </segment>
    </unit>
    <unit id="PV1Rblc" name="u2_core.delete_given_entity_type.confirmation">
      <segment>
        <source>u2_core.delete_given_entity_type.confirmation</source>
        <target>Are you sure you want to delete this %entity_type_name%?</target>
      </segment>
    </unit>
    <unit id="qAwD2E_" name="u2_core.delete_given_entity_type_with_given_name">
      <segment>
        <source>u2_core.delete_given_entity_type_with_given_name</source>
        <target>Delete %entity_type_name% (%entity_name%)</target>
      </segment>
    </unit>
    <unit id="pMvtN8f" name="u2_core.delete_given_entity_type_with_given_name.confirmation">
      <segment>
        <source>u2_core.delete_given_entity_type_with_given_name.confirmation</source>
        <target>Are you sure you want to delete this %entity_type_name% (%entity_name%)?</target>
      </segment>
    </unit>
    <unit id="TGR8u81" name="u2_core.delete_status">
      <segment>
        <source>u2_core.delete_status</source>
        <target>Delete Status</target>
      </segment>
    </unit>
    <unit id="h6FNsx4" name="u2_core.delete_status.confirmation">
      <segment>
        <source>u2_core.delete_status.confirmation</source>
        <target>Are you sure you want to delete this Status?</target>
      </segment>
    </unit>
    <unit id="NDQduO." name="u2_core.delete_status_with_given_name">
      <segment>
        <source>u2_core.delete_status_with_given_name</source>
        <target>Delete Status (%status_name%)</target>
      </segment>
    </unit>
    <unit id="VT_ICp0" name="u2_core.delete_status_with_given_name.confirmation">
      <segment>
        <source>u2_core.delete_status_with_given_name.confirmation</source>
        <target>Are you sure you want to delete this Status (%status_name%)?</target>
      </segment>
    </unit>
    <unit id="T1zUkZb" name="u2_core.delete_subscription.success">
      <segment>
        <source>u2_core.delete_subscription.success</source>
        <target>Subscription deleted successfully.</target>
      </segment>
    </unit>
    <unit id="6rn3gqi" name="u2_core.delete_system_message">
      <segment>
        <source>u2_core.delete_system_message</source>
        <target>Delete System Message</target>
      </segment>
    </unit>
    <unit id="SmNNJj_" name="u2_core.delete_system_message.confirmation">
      <segment>
        <source>u2_core.delete_system_message.confirmation</source>
        <target>Are you sure you want to delete this System Message?</target>
      </segment>
    </unit>
    <unit id=".f_IQmN" name="u2_core.delimiter">
      <segment>
        <source>u2_core.delimiter</source>
        <target>Delimiter</target>
      </segment>
    </unit>
    <unit id="OoQGh8T" name="u2_core.description">
      <segment>
        <source>u2_core.description</source>
        <target>Description</target>
      </segment>
    </unit>
    <unit id="wek9iM8" name="u2_core.details">
      <segment>
        <source>u2_core.details</source>
        <target>Details</target>
      </segment>
    </unit>
    <unit id="WTMOCfy" name="u2_core.direct_quotation">
      <segment>
        <source>u2_core.direct_quotation</source>
        <target>Direct Quotation</target>
      </segment>
    </unit>
    <unit id="1DwklVV" name="u2_core.direct_quotation_value">
      <segment>
        <source>u2_core.direct_quotation_value</source>
        <target>Direct Quotation Value</target>
      </segment>
    </unit>
    <unit id="_KwRkmj" name="u2_core.display_from">
      <segment>
        <source>u2_core.display_from</source>
        <target>Display from</target>
      </segment>
    </unit>
    <unit id="J0ttayw" name="u2_core.display_to">
      <segment>
        <source>u2_core.display_to</source>
        <target>Display to</target>
      </segment>
    </unit>
    <unit id="ezTNUDX" name="u2_core.documentation_corp_logo">
      <segment>
        <source>u2_core.documentation_corp_logo</source>
        <target>Documentation Corp. Logo</target>
      </segment>
    </unit>
    <unit id="IsHjHvZ" name="u2_core.documentation_corp_logo.help">
      <segment>
        <source>u2_core.documentation_corp_logo.help</source>
        <target><![CDATA[
            • The logo image must be squared<br>
            • Required size: 10 x 10 millimeters at 120 dpi (47 x 47 pixels)
        ]]></target>
      </segment>
    </unit>
    <unit id="UMeNnc3" name="u2_core.documentation_cover_picture">
      <segment>
        <source>u2_core.documentation_cover_picture</source>
        <target>Documentation Cover Picture</target>
      </segment>
    </unit>
    <unit id="EwpF0Kg" name="u2_core.documentation_cover_picture.help">
      <segment>
        <source>u2_core.documentation_cover_picture.help</source>
        <target><![CDATA[
            • Maximum size: 180 x 90 millimeters at 120 dpi (850 x 425 pixels)<br>
            • Recommended size: 90 x 45 millimeters at 120 dpi (425 x 213 pixels)
        ]]></target>
      </segment>
    </unit>
    <unit id="e9GMUxq" name="u2_core.download">
      <segment>
        <source>u2_core.download</source>
        <target>Download</target>
      </segment>
    </unit>
    <unit id="FZtF.6H" name="u2_core.download_file">
      <segment>
        <source>u2_core.download_file</source>
        <target>Download File</target>
      </segment>
    </unit>
    <unit id="_tIF869" name="u2_core.edit_attributes">
      <segment>
        <source>u2_core.edit_attributes</source>
        <target>Edit Attributes</target>
      </segment>
    </unit>
    <unit id="4zecaI7" name="u2_core.edit_attributes_of_given_unit_hierarchy">
      <segment>
        <source>u2_core.edit_attributes_of_given_unit_hierarchy</source>
        <target>Edit Unit Hierarchy Attributes for “%unit_hierarchy_name%”</target>
      </segment>
    </unit>
    <unit id="oZhc0QU" name="u2_core.edit_country_with_given_name">
      <segment>
        <source>u2_core.edit_country_with_given_name</source>
        <target>Edit Country (%country_name%)</target>
      </segment>
    </unit>
    <unit id="pnM1A6z" name="u2_core.edit_exchange_rate_with_given_name">
      <segment>
        <source>u2_core.edit_exchange_rate_with_given_name</source>
        <target>Edit Exchange Rate (%exchange_rate_name%)</target>
      </segment>
    </unit>
    <unit id="Oro.CVF" name="u2_core.edit_file">
      <segment>
        <source>u2_core.edit_file</source>
        <target>Edit File</target>
      </segment>
    </unit>
    <unit id="2LfDb5z" name="u2_core.edit_filter">
      <segment>
        <source>u2_core.edit_filter</source>
        <target>Edit Filter</target>
      </segment>
    </unit>
    <unit id="xW3rO5H" name="u2_core.edit_given_dashboard">
      <segment>
        <source>u2_core.edit_given_dashboard</source>
        <target>Edit “%dashboard_title%” Dashboard</target>
      </segment>
    </unit>
    <unit id="8T0kn33" name="u2_core.edit_given_entity_type_with_given_name">
      <segment>
        <source>u2_core.edit_given_entity_type_with_given_name</source>
        <target>Edit %entity_type_name% (%entity_name%)</target>
      </segment>
    </unit>
    <unit id="WqZOV7g" name="u2_core.edit_given_period">
      <segment>
        <source>u2_core.edit_given_period</source>
        <target>Edit Period (%period%)</target>
      </segment>
    </unit>
    <unit id="XadPrDw" name="u2_core.edit_given_subscription">
      <segment>
        <source>u2_core.edit_given_subscription</source>
        <target>Edit subscription (%subscription_name%)</target>
      </segment>
    </unit>
    <unit id="qxUC16h" name="u2_core.edit_status_with_given_name">
      <segment>
        <source>u2_core.edit_status_with_given_name</source>
        <target>Edit Status (%status_name%)</target>
      </segment>
    </unit>
    <unit id="FU0BP0S" name="u2_core.edit_structure">
      <segment>
        <source>u2_core.edit_structure</source>
        <target>Edit Structure</target>
      </segment>
    </unit>
    <unit id="slQEvF4" name="u2_core.edit_support_information">
      <segment>
        <source>u2_core.edit_support_information</source>
        <target>Edit Support Information</target>
      </segment>
    </unit>
    <unit id="JmhRKlS" name="u2_core.edit_system_message">
      <segment>
        <source>u2_core.edit_system_message</source>
        <target>Edit System Message</target>
      </segment>
    </unit>
    <unit id="LLW8soS" name="u2_core.edit_system_settings">
      <segment>
        <source>u2_core.edit_system_settings</source>
        <target>Edit System Settings</target>
      </segment>
    </unit>
    <unit id="Q5jglv7" name="u2_core.edit_the_support_information">
      <segment>
        <source>u2_core.edit_the_support_information</source>
        <target>Edit the Support Information</target>
      </segment>
    </unit>
    <unit id="auyjJvx" name="u2_core.edit_user_group">
      <segment>
        <source>u2_core.edit_user_group</source>
        <target>Edit User Group</target>
      </segment>
    </unit>
    <unit id="JYFsPVJ" name="u2_core.edit_user_settings">
      <segment>
        <source>u2_core.edit_user_settings</source>
        <target>Edit User Settings</target>
      </segment>
    </unit>
    <unit id=".ni2DtO" name="u2_core.elma5_help">
      <segment>
        <source>u2_core.elma5_help</source>
        <target><![CDATA[
        <span>Here you can generate and download ELMA compatible CbCR XML files that can be transmitted via the <a href="https://www.elster.de/bportal/meinelster">BZSt online portal</a> (BOP). Further information about ELMA and it's transmission can be found on the <a href="https://www.bzst.de/DE/Unternehmen/Intern_Informationsaustausch/CountryByCountryReporting/countrybycountryreporting_node.html">BZSt website</a>.</span>
        ]]></target>
      </segment>
    </unit>
    <unit id="ZohNnkF" name="u2_core.email">
      <segment>
        <source>u2_core.email</source>
        <target>Email</target>
      </segment>
    </unit>
    <unit id="rj2F6sy" name="u2_core.empty_widget">
      <segment>
        <source>u2_core.empty_widget</source>
        <target>Empty Widget.</target>
      </segment>
    </unit>
    <unit id="g9iMlxG" name="u2_core.empty_widget.help">
      <segment>
        <source>u2_core.empty_widget.help</source>
        <target>Please add some content.</target>
      </segment>
    </unit>
    <unit id="gwtdny4" name="u2_core.enable_manual_review">
      <segment>
        <source>u2_core.enable_manual_review</source>
        <target>Enable manual review</target>
      </segment>
    </unit>
    <unit id="pHMCiQW" name="u2_core.end">
      <segment>
        <source>u2_core.end</source>
        <target>End</target>
      </segment>
    </unit>
    <unit id="M.kYhUV" name="u2_core.end_date">
      <segment>
        <source>u2_core.end_date</source>
        <target>End Date</target>
      </segment>
    </unit>
    <unit id="fAE0DSF" name="u2_core.english">
      <segment>
        <source>u2_core.english</source>
        <target>English</target>
      </segment>
    </unit>
    <unit id="dKUvy32" name="u2_core.enter_filter_term">
      <segment>
        <source>u2_core.enter_filter_term</source>
        <target>Enter filter term</target>
      </segment>
    </unit>
    <unit id="yTARzP3" name="u2_core.entities_linked_with_given_file">
      <segment>
        <source>u2_core.entities_linked_with_given_file</source>
        <target>Entities linked with “%file_name%”</target>
      </segment>
    </unit>
    <unit id="8j58HiE" name="u2_core.error_could_not_save_check_the_highlighted_fields">
      <segment>
        <source>u2_core.error_could_not_save_check_the_highlighted_fields</source>
        <target>Could not save. Please check the highlighted fields below and try again.</target>
      </segment>
    </unit>
    <unit id="PmyBJoB" name="u2_core.exchange_rate">
      <segment>
        <source>u2_core.exchange_rate</source>
        <target>Exchange Rate</target>
      </segment>
    </unit>
    <unit id="sXYDzkh" name="u2_core.exchange_rate_unavailable">
      <segment>
        <source>u2_core.exchange_rate_unavailable</source>
        <target>Exchange rate unavailable</target>
      </segment>
    </unit>
    <unit id="tmQPH7t" name="u2_core.exchange_rates">
      <segment>
        <source>u2_core.exchange_rates</source>
        <target>Exchange Rates</target>
      </segment>
    </unit>
    <unit id="aOQB_O." name="u2_core.expand_sidebar">
      <segment>
        <source>u2_core.expand_sidebar</source>
        <target>Expand Sidebar</target>
      </segment>
    </unit>
    <unit id="_uzrcUx" name="u2_core.experimental">
      <segment>
        <source>u2_core.experimental</source>
        <target>Experimental</target>
      </segment>
    </unit>
    <unit id="x.d7aoY" name="u2_core.fax">
      <segment>
        <source>u2_core.fax</source>
        <target>Fax</target>
      </segment>
    </unit>
    <unit id="D8HStbr" name="u2_core.file.access_type_protected">
      <segment>
        <source>u2_core.file.access_type_protected</source>
        <target>Protected</target>
      </segment>
    </unit>
    <unit id="9EkqFhM" name="u2_core.file.access_type_protected.help">
      <segment>
        <source>u2_core.file.access_type_protected.help</source>
        <target>Only explicitly assigned users and/or groups will be able to view this file. The file name will not be revealed to other users.</target>
      </segment>
    </unit>
    <unit id="gzre2y9" name="u2_core.file.access_type_smart">
      <segment>
        <source>u2_core.file.access_type_smart</source>
        <target>Smart</target>
      </segment>
    </unit>
    <unit id="ehEvAAw" name="u2_core.file.access_type_smart.help">
      <segment>
        <source>u2_core.file.access_type_smart.help</source>
        <target>Users will be able to see this file if they are able to view the record to which it is attached. Additional users and groups may also be assigned explicitly to the file.</target>
      </segment>
    </unit>
    <unit id="R07CfjE" name="u2_core.file_mime_content_type">
      <segment>
        <source>u2_core.file_mime_content_type</source>
        <target>File MIME Content-Type</target>
      </segment>
    </unit>
    <unit id="CgI6gMj" name="u2_core.file_not_found">
      <segment>
        <source>u2_core.file_not_found</source>
        <target>File not found</target>
      </segment>
    </unit>
    <unit id="EqUOsO1" name="u2_core.files">
      <segment>
        <source>u2_core.files</source>
        <target>Files</target>
      </segment>
    </unit>
    <unit id="ysF3MFV" name="u2_core.filter">
      <segment>
        <source>u2_core.filter</source>
        <target>Filter</target>
      </segment>
    </unit>
    <unit id="gxhfyf_" name="u2_core.filter_information">
      <segment>
        <source>u2_core.filter_information</source>
        <target>Filter Information</target>
      </segment>
    </unit>
    <unit id="Mdcr9XE" name="u2_core.filter_query">
      <segment>
        <source>u2_core.filter_query</source>
        <target>Filter query</target>
      </segment>
    </unit>
    <unit id="0LMAOZs" name="u2_core.filter_table.error_occurred_filters_have_been_reset">
      <segment>
        <source>u2_core.filter_table.error_occurred_filters_have_been_reset</source>
        <target>An error has occurred when filtering the current table. The filters have been reset.</target>
      </segment>
    </unit>
    <unit id="UWjslK6" name="u2_core.filter_target">
      <segment>
        <source>u2_core.filter_target</source>
        <target>Filter Target</target>
      </segment>
    </unit>
    <unit id="gEzM6RX" name="u2_core.filters">
      <segment>
        <source>u2_core.filters</source>
        <target>Filters</target>
      </segment>
    </unit>
    <unit id="dnDq6pJ" name="u2.frequency">
      <segment>
        <source>u2.frequency</source>
        <target>Frequency</target>
      </segment>
    </unit>
    <unit id="UkWtkNl" name="u2_core.from_date">
      <segment>
        <source>u2_core.from_date</source>
        <target>From Date</target>
      </segment>
    </unit>
    <unit id="1m6NRHa" name="u2_core.full_text_search">
      <segment>
        <source>u2_core.full_text_search</source>
        <target>Full text search</target>
      </segment>
    </unit>
    <unit id="NdD7SJK" name="u2_core.further_browser_information">
      <segment>
        <source>u2_core.further_browser_information</source>
        <target>Further Browser Information</target>
      </segment>
    </unit>
    <unit id="Ehuhfb6" name="u2_core.general">
      <segment>
        <source>u2_core.general</source>
        <target>General</target>
      </segment>
    </unit>
    <unit id="TCGNrrt" name="u2_core.german">
      <segment>
        <source>u2_core.german</source>
        <target>Deutsch</target>
      </segment>
    </unit>
    <unit id="xllYUu9" name="u2_core.given_entity_type_list">
      <segment>
        <source>u2_core.given_entity_type_list</source>
        <target>%entity_type_name% List</target>
      </segment>
    </unit>
    <unit id="kS47RMA" name="u2_core.given_units_are_not_assigned_to_user">
      <segment>
        <source>u2_core.given_units_are_not_assigned_to_user</source>
        <target>You are not authorised to add the following units: %units%.</target>
      </segment>
    </unit>
    <unit id="zq1NFot" name="u2_core.given_user_requests_permission_to_given_file">
      <segment>
        <source>u2_core.given_user_requests_permission_to_given_file</source>
        <target>%user_name% requests permission to %file_name%</target>
      </segment>
    </unit>
    <unit id="mrQ8YFu" name="u2_core.given_version">
      <segment>
        <source>u2_core.given_version</source>
        <target>Version %version%</target>
      </segment>
    </unit>
    <unit id="gH_OHUq" name="u2_core.go_to_support_platform">
      <segment>
        <source>u2_core.go_to_support_platform</source>
        <target>Get in touch with us over our support platform:</target>
      </segment>
    </unit>
    <unit id="2wtXri_" name="u2_core.go_to_the_corresponding_list_page">
      <segment>
        <source>u2_core.go_to_the_corresponding_list_page</source>
        <target><![CDATA[
            <ol>
              <li>Go to the corresponding list page.</li>
              <li>Apply the filtering parameters as needed.</li>
              <li>Click the <span class="icon-save-alt"></span><strong>Save</strong> button.</li>
              <li>Enter name and description, and click save.</li>
            </ol>
        ]]></target>
      </segment>
    </unit>
    <unit id="TFeCOzB" name="u2_core.go_to_the_universal_units_website">
      <segment>
        <source>u2_core.go_to_the_universal_units_website</source>
        <target>Go to the Universal Units website</target>
      </segment>
    </unit>
    <unit id="mQ0HwFX" name="u2_core.group">
      <segment>
        <source>u2_core.group</source>
        <target>Group</target>
      </segment>
    </unit>
    <unit id="9oTUJwc" name="u2_core.group_parent_income_tax">
      <segment>
        <source>u2_core.group_parent_income_tax</source>
        <target>Group Parent - Income Tax</target>
      </segment>
    </unit>
    <unit id="MLCkNDK" name="u2_core.group_parent_vat">
      <segment>
        <source>u2_core.group_parent_vat</source>
        <target>Group Parent - VAT</target>
      </segment>
    </unit>
    <unit id="Ai_ewXZ" name="u2_core.groups">
      <segment>
        <source>u2_core.groups</source>
        <target>Groups</target>
      </segment>
    </unit>
    <unit id="qcuAQQK" name="u2_core.help">
      <segment>
        <source>u2_core.help</source>
        <target>Help</target>
      </segment>
    </unit>
    <unit id="JchUC4y" name="u2_core.hide">
      <segment>
        <source>u2_core.hide</source>
        <target>Hide</target>
      </segment>
    </unit>
    <unit id="rStfFZ3" name="u2_core.id">
      <segment>
        <source>u2_core.id</source>
        <target>ID</target>
      </segment>
    </unit>
    <unit id="NjteGFa" name="u2_core.import.confirm">
      <segment>
        <source>u2_core.import.confirm</source>
        <target>Confirm Import</target>
      </segment>
    </unit>
    <unit id="SAoHpnp" name="u2_core.import.default_delimiter.help">
      <segment>
        <source>u2_core.import.default_delimiter.help</source>
        <target>Default delimiter in CSV files used for imports.</target>
      </segment>
    </unit>
    <unit id="t4wow04" name="u2_core.import.email_content_fail">
      <segment>
        <source>u2_core.import.email_content_fail</source>
        <target>The import failed. Click on the link below to see the result:</target>
      </segment>
    </unit>
    <unit id="zw8jXBW" name="u2_core.import.email_content_success">
      <segment>
        <source>u2_core.import.email_content_success</source>
        <target>The import was successfully completed. Click on the link below to see the result:</target>
      </segment>
    </unit>
    <unit id="YAbvO9c" name="u2.import.file.help">
      <segment>
        <source>u2.import.file.help</source>
        <target>The file containing the data to be imported.</target>
      </segment>
    </unit>
    <unit id="L.KlMmb" name="u2.import.simulate.help">
      <segment>
        <source>u2.import.simulate.help</source>
        <target>When this option is selected then the import will only be simulated and no values will be imported. This may be used to determine if the file is valid or has errors.</target>
      </segment>
    </unit>
    <unit id=".hcYE_Q" name="u2_core.import.email_title_fail">
      <segment>
        <source>u2_core.import.email_title_fail</source>
        <target>Failed Import</target>
      </segment>
    </unit>
    <unit id="3ayfi.K" name="u2_core.import.email_title_success">
      <segment>
        <source>u2_core.import.email_title_success</source>
        <target>Successful Import</target>
      </segment>
    </unit>
    <unit id="dNF7xxz" name="u2_core.import.help">
      <segment>
        <source>u2_core.import.help</source>
        <target><![CDATA[
          <p>On this page you can import data via CSV or Excel. Select a CSV or Excel file containing the data you wish to import and press import.</p><p>The data in the CSV or Excel can be in any order. The CSV file must contain a header row that defines the format of the file and the order of the values you wish to import.</p>]]></target>
      </segment>
    </unit>
    <unit id="6i2C4aP" name="u2_core.import.help_values_contain_commas">
      <segment>
        <source>u2_core.import.help_values_contain_commas</source>
        <target><![CDATA[
            <p>If your values contain commas you will need to enclose them in double quotation marks:</p>
            <div class="data-group font-mono text-sm">
            Header One,Header Two,Header Three
            <br>
            value one,"However, value two contains a comma",value three
            </div>
        ]]></target>
      </segment>
    </unit>
    <unit id="NHS9CTd" name="u2_core.import.selection">
      <segment>
        <source>u2_core.import.selection</source>
        <target>What would you like to import?</target>
      </segment>
    </unit>
    <unit id="VvdS7yA" name="u2_core.import.list">
      <segment>
        <source>u2_core.import.list</source>
        <target>Import List</target>
      </segment>
    </unit>
    <unit id="UqU5y.i" name="u2_core.import.new">
      <segment>
        <source>u2_core.import.new</source>
        <target>New Import</target>
      </segment>
    </unit>
    <unit id="ksUYJz3" name="u2_core.import.not_simulated_confirmation">
      <segment>
        <source>u2_core.import.not_simulated_confirmation</source>
        <target>You are about to import the contents of the selected file into the application. Please cancel this operation and select the “Simulate” option if you would first like to test this file for errors and compatibility.</target>
      </segment>
    </unit>
    <unit id="sVNNLMZ" name="u2_core.import.possible_headers_data_import">
      <segment>
        <source>u2_core.import.possible_headers_data_import</source>
        <target>The following are the possible headers for this data import:</target>
      </segment>
    </unit>
    <unit id="qTA0bNi" name="u2_core.import.result">
      <segment>
        <source>u2_core.import.result</source>
        <target>Import Result</target>
      </segment>
    </unit>
    <unit id="sIx.0FM" name="u2_core.import.select_a_file_to_import">
      <segment>
        <source>u2_core.import.select_a_file_to_import</source>
        <target>Select a file to import</target>
      </segment>
    </unit>
    <unit id="VAwQfBT" name="u2_core.import.start_import">
      <segment>
        <source>u2_core.import.start_import</source>
        <target>Start Import</target>
      </segment>
    </unit>
    <unit id="UBktWCA" name="u2_core.import.uploaded_file">
      <segment>
        <source>u2_core.import.uploaded_file</source>
        <target>Uploaded File</target>
      </segment>
    </unit>
    <unit id="io4r.6L" name="u2_core.import_exchange_rates">
      <segment>
        <source>u2_core.import_exchange_rates</source>
        <target>Import Exchange Rates</target>
      </segment>
    </unit>
    <unit id="4u.gUBr" name="u2_core.import_was_completed">
      <segment>
        <source>u2_core.import_was_completed</source>
        <target>The Import was completed.</target>
      </segment>
    </unit>
    <unit id="M86lvtc" name="u2_core.indirect_quotation">
      <segment>
        <source>u2_core.indirect_quotation</source>
        <target>Indirect Quotation</target>
      </segment>
    </unit>
    <unit id="XqoeIHd" name="u2_core.indirect_quotation_value">
      <segment>
        <source>u2_core.indirect_quotation_value</source>
        <target>Indirect Quotation Value</target>
      </segment>
    </unit>
    <unit id="YL0Zhlj" name="u2_core.initial_status">
      <segment>
        <source>u2_core.initial_status</source>
        <target>Initial Status</target>
      </segment>
    </unit>
    <unit id="EM_k1zx" name="u2_core.input_currency">
      <segment>
        <source>u2_core.input_currency</source>
        <target>Input Currency</target>
      </segment>
    </unit>
    <unit id="piGpgzS" name="u2_core.is_closed">
      <segment>
        <source>u2_core.is_closed</source>
        <target>Is Closed</target>
      </segment>
    </unit>
    <unit id="UuG8Zx1" name="u2_core.iso_code">
      <segment>
        <source>u2_core.iso_code</source>
        <target>Iso Code</target>
      </segment>
    </unit>
    <unit id="ONaSNx." name="u2.item">
      <segment>
        <source>u2.item</source>
        <target>Item</target>
      </segment>
    </unit>
    <unit id="x3zArSF" name="u2_core.last_login">
      <segment>
        <source>u2_core.last_login</source>
        <target>Last Login</target>
      </segment>
    </unit>
    <unit id="VKBjnzp" name="u2_core.last_run">
      <segment>
        <source>u2_core.last_run</source>
        <target>Last Run</target>
      </segment>
    </unit>
    <unit id="7ITTcm7" name="u2_core.last_sent">
      <segment>
        <source>u2_core.last_sent</source>
        <target>Last Sent</target>
      </segment>
    </unit>
    <unit id="Bl5d.2A" name="u2.datasheets.datasheet">
      <segment>
        <source>u2.datasheets.datasheet</source>
        <target>Datasheet</target>
      </segment>
    </unit>
    <unit id="dPJmdJ0" name="u2_core.legal">
      <segment>
        <source>u2_core.legal</source>
        <target>Legal</target>
      </segment>
    </unit>
    <unit id="eqNB8ZG" name="u2_core.legal_form">
      <segment>
        <source>u2_core.legal_form</source>
        <target>Legal Form</target>
      </segment>
    </unit>
    <unit id="sNdCwAF" name="u2_core.legal_form.plural">
      <segment>
        <source>u2_core.legal_form.plural</source>
        <target>Legal Forms</target>
      </segment>
    </unit>
    <unit id="e5QF.Pr" name="u2_core.legal_name">
      <segment>
        <source>u2_core.legal_name</source>
        <target>Legal Name</target>
      </segment>
    </unit>
    <unit id="0UgKego" name="u2_core.link">
      <segment>
        <source>u2_core.link</source>
        <target>Link</target>
      </segment>
    </unit>
    <unit id="u.hgO7T" name="u2_core.link_an_existing_file">
      <segment>
        <source>u2_core.link_an_existing_file</source>
        <target>Link an existing file</target>
      </segment>
    </unit>
    <unit id="4olaStu" name="u2_core.link_file_to_entity.success">
      <segment>
        <source>u2_core.link_file_to_entity.success</source>
        <target>The file was successfully linked.</target>
      </segment>
    </unit>
    <unit id="iAwtjfk" name="u2_core.linked_entities">
      <segment>
        <source>u2_core.linked_entities</source>
        <target>Linked Entities</target>
      </segment>
    </unit>
    <unit id="0qp99uB" name="u2_core.login">
      <segment>
        <source>u2_core.login</source>
        <target>Login</target>
      </segment>
    </unit>
    <unit id="cbcWSua" name="u2_core.login_background">
      <segment>
        <source>u2_core.login_background</source>
        <target>Login Background</target>
      </segment>
    </unit>
    <unit id="1FXedYk" name="u2_core.login_background.help">
      <segment>
        <source>u2_core.login_background.help</source>
        <target><![CDATA[
            • Minimum size: 750 x 850 pixels<br>
            • Recommended size: 1125 x 1275 pixels or more
        ]]></target>
      </segment>
    </unit>
    <unit id="lZV8c.R" name="u2_core.login_logo">
      <segment>
        <source>u2_core.login_logo</source>
        <target>Login Logo</target>
      </segment>
    </unit>
    <unit id="vybh_P6" name="u2_core.login_logo.help">
      <segment>
        <source>u2_core.login_logo.help</source>
        <target><![CDATA[
            • SVG, or transparent PNG<br>
            • Recommended size: not bigger than 400 pixels wide and 250 pixels tall
        ]]></target>
      </segment>
    </unit>
    <unit id="mb2UKaV" name="u2_core.logout">
      <segment>
        <source>u2_core.logout</source>
        <target>Logout</target>
      </segment>
    </unit>
    <unit id="s4S7OrG" name="u2_core.manage">
      <segment>
        <source>u2_core.manage</source>
        <target>Manage</target>
      </segment>
    </unit>
    <unit id="DtUO0MN" name="u2_core.manually_run_subscription.success">
      <segment>
        <source>u2_core.manually_run_subscription.success</source>
        <target>Subscription manually run successfully.</target>
      </segment>
    </unit>
    <unit id="mvHrKj0" name="u2_core.max_login_attempts">
      <segment>
        <source>u2_core.max_login_attempts</source>
        <target>Maximum amount of login attempts</target>
      </segment>
    </unit>
    <unit id="umy1lsW" name="u2_core.maximum_file_size">
      <segment>
        <source>u2_core.maximum_file_size</source>
        <target>Maximum file size:</target>
      </segment>
    </unit>
    <unit id="DtDr.d_" name="u2_core.menu">
      <segment>
        <source>u2_core.menu</source>
        <target>Menu</target>
      </segment>
    </unit>
    <unit id="8oeBvqX" name="u2_core.message">
      <segment>
        <source>u2_core.message</source>
        <target>Message</target>
      </segment>
    </unit>
    <unit id="lGKNewZ" name="u2_core.mobile">
      <segment>
        <source>u2_core.mobile</source>
        <target>Mobile</target>
      </segment>
    </unit>
    <unit id="apIHMU5" name="u2_core.moved_on_given_date">
      <segment>
        <source>u2_core.moved_on_given_date</source>
        <target>Moved on %date%</target>
      </segment>
    </unit>
    <unit id="lfi86i5" name="u2_core.my_permissions">
      <segment>
        <source>u2_core.my_permissions</source>
        <target>My Permissions</target>
      </segment>
    </unit>
    <unit id="tzAXLUg" name="u2_core.n_a">
      <segment>
        <source>u2_core.n_a</source>
        <target>n/a</target>
      </segment>
    </unit>
    <unit id="IdOLrRp" name="u2_core.name">
      <segment>
        <source>u2_core.name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="eeAuplY" name="u2_core.name_first">
      <segment>
        <source>u2_core.name_first</source>
        <target>First Name</target>
      </segment>
    </unit>
    <unit id="qqPPWt8" name="u2_core.name_last">
      <segment>
        <source>u2_core.name_last</source>
        <target>Last Name</target>
      </segment>
    </unit>
    <unit id="_0jXEQ2" name="u2_core.name_long">
      <segment>
        <source>u2_core.name_long</source>
        <target>Name (long)</target>
      </segment>
    </unit>
    <unit id="WqNvKf9" name="u2_core.name_short">
      <segment>
        <source>u2_core.name_short</source>
        <target>Name (short)</target>
      </segment>
    </unit>
    <unit id="SaQSJp4" name="u2_core.nationality_long">
      <segment>
        <source>u2_core.nationality_long</source>
        <target>Nationality (long)</target>
      </segment>
    </unit>
    <unit id="9lxbAzS" name="u2_core.nationality_short">
      <segment>
        <source>u2_core.nationality_short</source>
        <target>Nationality (short)</target>
      </segment>
    </unit>
    <unit id="SNuYz9K" name="u2_core.never">
      <segment>
        <source>u2_core.never</source>
        <target>Never.</target>
      </segment>
    </unit>
    <unit id="6bQPhww" name="u2_core.next_calendar_week">
      <segment>
        <source>u2_core.next_calendar_week</source>
        <target>Next Calendar Week</target>
      </segment>
    </unit>
    <unit id="2.lvC8W" name="u2_core.next_run">
      <segment>
        <source>u2_core.next_run</source>
        <target>Next Run</target>
      </segment>
    </unit>
    <unit id="_FKz81." name="u2_core.next_sent">
      <segment>
        <source>u2_core.next_sent</source>
        <target>Next Sent</target>
      </segment>
    </unit>
    <unit id="B5mR.Tm" name="u2_core.no_changes_on_given_date">
      <segment>
        <source>u2_core.no_changes_on_given_date</source>
        <target>No changes on %date%</target>
      </segment>
    </unit>
    <unit id="LCgvv0u" name="u2.no_changes">
      <segment>
        <source>u2.no_changes</source>
        <target>No Changes</target>
      </segment>
    </unit>
    <unit id="tr_r5Dd" name="u2_core.no_period_selected">
      <segment>
        <source>u2_core.no_period_selected</source>
        <target>No period selected</target>
      </segment>
    </unit>
    <unit id="vg.YSEm" name="u2_core.no_permissions_assigned_with_given_permission_type">
      <segment>
        <source>u2_core.no_permissions_assigned_with_given_permission_type</source>
        <target>There are no %permission_type% permissions assigned.</target>
      </segment>
    </unit>
    <unit id="7kQUEEZ" name="u2_core.no_subscriptions">
      <segment>
        <source>u2_core.no_subscriptions</source>
        <target>No Subscriptions</target>
      </segment>
    </unit>
    <unit id="VBcQdlZ" name="u2_core.none">
      <segment>
        <source>u2_core.none</source>
        <target>None</target>
      </segment>
    </unit>
    <unit id="RA3E4Ic" name="u2_core.none_selected">
      <segment>
        <source>u2_core.none_selected</source>
        <target>None selected</target>
      </segment>
    </unit>
    <unit id="6ZVBMuL" name="u2_core.number_of_attachments">
      <segment>
        <source>u2_core.number_of_attachments</source>
        <target>Number of Attachments</target>
      </segment>
    </unit>
    <unit id="I4LSaKH" name="u2_core.number_of_reviews">
      <segment>
        <source>u2_core.number_of_reviews</source>
        <target>Number of Reviews</target>
      </segment>
    </unit>
    <unit id="gnga4Nr" name="u2.number_of_widgets">
      <segment>
        <source>u2.number_of_widgets</source>
        <target>Number of Widgets</target>
      </segment>
    </unit>
    <unit id="VpX8ojl" name="u2_core.open_beginning_of_year">
      <segment>
        <source>u2_core.open_beginning_of_year</source>
        <target>Open Beginning of Year</target>
      </segment>
    </unit>
    <unit id="2E.Txdv" name="u2_core.open_beginning_of_year.help">
      <segment>
        <source>u2_core.open_beginning_of_year.help</source>
        <target>Allow manual entry for beginning of year values</target>
      </segment>
    </unit>
    <unit id="Ern8TWF" name="u2_core.operating_system">
      <segment>
        <source>u2_core.operating_system</source>
        <target>Operating System</target>
      </segment>
    </unit>
    <unit id="RdG2LxW" name="u2_core.or">
      <segment>
        <source>u2_core.or</source>
        <target>or</target>
      </segment>
    </unit>
    <unit id="qy3J.52" name="u2_core.output_currency">
      <segment>
        <source>u2_core.output_currency</source>
        <target>Output Currency</target>
      </segment>
    </unit>
    <unit id="ICqj2mq" name="u2_core.owner">
      <segment>
        <source>u2_core.owner</source>
        <target>Owner</target>
      </segment>
    </unit>
    <unit id="2mPinqg" name="u2_core.parent_legal_unit">
      <segment>
        <source>u2_core.parent_legal_unit</source>
        <target>Parent Legal Unit</target>
      </segment>
    </unit>
    <unit id="uGF4QUu" name="u2_core.parent_user">
      <segment>
        <source>u2_core.parent_user</source>
        <target>Parent User</target>
      </segment>
    </unit>
    <unit id="tHsbrDk" name="u2.password">
      <segment>
        <source>u2.password</source>
        <target>Password</target>
      </segment>
    </unit>
    <unit id="Fs8ZPeP" name="u2.password_expires">
      <segment>
        <source>u2.password_expires</source>
        <target>Password Expires</target>
      </segment>
    </unit>
    <unit id="zkfaLg0" name="u2.password_forgotten">
      <segment>
        <source>u2.password_forgotten</source>
        <target>I forgot my password</target>
      </segment>
    </unit>
    <unit id="nJdPAke" name="u2.password_history_length">
      <segment>
        <source>u2.password_history_length</source>
        <target>Password history length</target>
      </segment>
    </unit>
    <unit id="7fKvGZm" name="u2.password_max_age_in_days">
      <segment>
        <source>u2.password_max_age_in_days</source>
        <target>Password max age in days</target>
      </segment>
    </unit>
    <unit id="FgovjBk" name="u2.password_must_have_at_least_one_lowercase_letter">
      <segment>
        <source>u2.password_must_have_at_least_one_lowercase_letter</source>
        <target>Must have at least one lowercase letter.</target>
      </segment>
    </unit>
    <unit id="4cWy337" name="u2.password_must_have_at_least_one_non_alphanumeric_character">
      <segment>
        <source>u2.password_must_have_at_least_one_non_alphanumeric_character</source>
        <target>Must have at least one non alphanumeric character.</target>
      </segment>
    </unit>
    <unit id="8NdCF77" name="u2.password_must_have_at_least_one_number">
      <segment>
        <source>u2.password_must_have_at_least_one_number</source>
        <target>Must have at least one number.</target>
      </segment>
    </unit>
    <unit id="9WYQcIf" name="u2.password_must_have_at_least_one_uppercase_letter">
      <segment>
        <source>u2.password_must_have_at_least_one_uppercase_letter</source>
        <target>Must have at least one uppercase letter.</target>
      </segment>
    </unit>
    <unit id="w5eUFPt" name="u2.password_must_meet_the_following_constraints">
      <segment>
        <source>u2.password_must_meet_the_following_constraints</source>
        <target>Passwords must meet the following constraints:</target>
      </segment>
    </unit>
    <unit id="Rcd9gL5" name="u2.password_requires_at_least_one_lowercase_letter">
      <segment>
        <source>u2.password_requires_at_least_one_lowercase_letter</source>
        <target>Password requires at least one lowercase letter</target>
      </segment>
    </unit>
    <unit id="vDmRsjL" name="u2.password_requires_at_least_one_non_alphanumeric_character">
      <segment>
        <source>u2.password_requires_at_least_one_non_alphanumeric_character</source>
        <target>Password requires at least one non alphanumeric character</target>
      </segment>
    </unit>
    <unit id="fliXH4C" name="u2.password_requires_at_least_one_number">
      <segment>
        <source>u2.password_requires_at_least_one_number</source>
        <target>Password requires at least one number</target>
      </segment>
    </unit>
    <unit id="z6akV84" name="u2.password_requires_at_least_one_uppercase_letter">
      <segment>
        <source>u2.password_requires_at_least_one_uppercase_letter</source>
        <target>Password requires at least one uppercase letter</target>
      </segment>
    </unit>
    <unit id="oEdh.75" name="u2.password_reset">
      <segment>
        <source>u2.password_reset</source>
        <target>Password Reset</target>
      </segment>
    </unit>
    <unit id="0WRq9uD" name="u2.password_reset_hours_valid">
      <segment>
        <source>u2.password_reset_hours_valid</source>
        <target>Password reset hours valid</target>
      </segment>
    </unit>
    <unit id="PMvZxCt" name="u2_core.period">
      <segment>
        <source>u2_core.period</source>
        <target>Period</target>
      </segment>
    </unit>
    <unit id="aMXtELs" name="u2_core.period_list">
      <segment>
        <source>u2_core.period_list</source>
        <target>Period List</target>
      </segment>
    </unit>
    <unit id="SZSD5xF" name="u2_core.period_management">
      <segment>
        <source>u2_core.period_management</source>
        <target>Period Management</target>
      </segment>
    </unit>
    <unit id="cHYBL4a" name="u2_core.permission_mask.1">
      <notes>
        <note>Name of 'view' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.1</source>
        <target>Read</target>
      </segment>
    </unit>
    <unit id="ET7RX5D" name="u2_core.permission_mask.1073741823">
      <notes>
        <note>Name of 'iddqd' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.1073741823</source>
        <target>God's Power</target>
      </segment>
    </unit>
    <unit id="LKNdxZU" name="u2_core.permission_mask.128">
      <notes>
        <note>Name of 'owner' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.128</source>
        <target>Manage</target>
      </segment>
    </unit>
    <unit id="y8QFjLt" name="u2_core.permission_mask.16">
      <notes>
        <note>Name of 'undelete' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.16</source>
        <target>Undelete</target>
      </segment>
    </unit>
    <unit id="1nN4vlS" name="u2_core.permission_mask.2">
      <notes>
        <note>Name of 'create' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.2</source>
        <target>Create</target>
      </segment>
    </unit>
    <unit id="u6Hf5Bm" name="u2_core.permission_mask.32">
      <notes>
        <note>Name of 'operator' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.32</source>
        <target>Operator</target>
      </segment>
    </unit>
    <unit id="ReLN.gJ" name="u2_core.permission_mask.4">
      <notes>
        <note>Name of 'edit' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.4</source>
        <target>Edit</target>
      </segment>
    </unit>
    <unit id="2hbXiBE" name="u2_core.permission_mask.64">
      <notes>
        <note>Name of 'master' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.64</source>
        <target>Master</target>
      </segment>
    </unit>
    <unit id="Su9hUbP" name="u2_core.permission_mask.8">
      <notes>
        <note>Name of 'delete' permission mask</note>
      </notes>
      <segment>
        <source>u2_core.permission_mask.8</source>
        <target>Delete</target>
      </segment>
    </unit>
    <unit id="94PeYzi" name="u2_core.permissions">
      <segment>
        <source>u2_core.permissions</source>
        <target>Permissions</target>
      </segment>
    </unit>
    <unit id="vB0hBrI" name="u2_core.permissions_request_sent.success">
      <segment>
        <source>u2_core.permissions_request_sent.success</source>
        <target>Permissions request has been successfully sent.</target>
      </segment>
    </unit>
    <unit id="pLr12OK" name="u2_core.personal_information">
      <segment>
        <source>u2_core.personal_information</source>
        <target>Personal Information</target>
      </segment>
    </unit>
    <unit id="Agrqhc7" name="u2_core.please_choose_a_new_password_for_given_user">
      <segment>
        <source>u2_core.please_choose_a_new_password_for_given_user</source>
        <target>Please choose a new password for “%username%”.</target>
      </segment>
    </unit>
    <unit id="xgDIC8V" name="u2_core.postal_address">
      <segment>
        <source>u2_core.postal_address</source>
        <target>Postal Address</target>
      </segment>
    </unit>
    <unit id="rEaX3gO" name="u2_core.previous_calendar_week">
      <segment>
        <source>u2_core.previous_calendar_week</source>
        <target>Previous Calendar Week</target>
      </segment>
    </unit>
    <unit id="Vjcgkm8" name="u2_core.previous_period">
      <segment>
        <source>u2_core.previous_period</source>
        <target>Previous Period</target>
      </segment>
    </unit>
    <unit id="rckbz7r" name="u2_core.profile">
      <segment>
        <source>u2_core.profile</source>
        <target>Profile</target>
      </segment>
    </unit>
    <unit id="EypZ_ub" name="u2_core.progress">
      <segment>
        <source>u2_core.progress</source>
        <target>Progress</target>
      </segment>
    </unit>
    <unit id="ylQbQBg" name="u2_core.read">
      <segment>
        <source>u2_core.read</source>
        <target>Read</target>
      </segment>
    </unit>
    <unit id="0H6UVlq" name="u2_core.ref_id">
      <segment>
        <source>u2_core.ref_id</source>
        <target>Ref. ID</target>
      </segment>
    </unit>
    <unit id="SPCyktR" name="u2_core.register_number">
      <segment>
        <source>u2_core.register_number</source>
        <target>Register Number</target>
      </segment>
    </unit>
    <unit id="DB4YncU" name="u2_core.registry_place">
      <segment>
        <source>u2_core.registry_place</source>
        <target>Registry Place</target>
      </segment>
    </unit>
    <unit id="y1qAjkr" name="u2_core.remove_file">
      <segment>
        <source>u2_core.remove_file</source>
        <target>Remove File</target>
      </segment>
    </unit>
    <unit id="py4Yjse" name="u2_core.remove_review">
      <segment>
        <source>u2_core.remove_review</source>
        <target>Remove review</target>
      </segment>
    </unit>
    <unit id="QLDh7mT" name="u2_core.remove_review.success">
      <segment>
        <source>u2_core.remove_review.success</source>
        <target>Successfully removed the review</target>
      </segment>
    </unit>
    <unit id="xJBPhFm" name="u2_core.removed_on_given_date">
      <segment>
        <source>u2_core.removed_on_given_date</source>
        <target>Removed on %date%</target>
      </segment>
    </unit>
    <unit id="9onzuDB" name="u2_core.reporter">
      <segment>
        <source>u2_core.reporter</source>
        <target>Reporter</target>
      </segment>
    </unit>
    <unit id="JqFmQyQ" name="u2_core.request_a_password_reset">
      <segment>
        <source>u2_core.request_a_password_reset</source>
        <target>Request a Password Reset</target>
      </segment>
    </unit>
    <unit id="SGlcn6q" name="u2_core.reset_password">
      <segment>
        <source>u2_core.reset_password</source>
        <target>Reset Password</target>
      </segment>
    </unit>
    <unit id="4DPyHj7" name="u2_core.result">
      <segment>
        <source>u2_core.result</source>
        <target>Result</target>
      </segment>
    </unit>
    <unit id="pu1su8q" name="u2_core.review.remove_review.confirm">
      <segment>
        <source>u2_core.review.remove_review.confirm</source>
        <target>Are you sure you want to remove this review?</target>
      </segment>
    </unit>
    <unit id="vC4RgYE" name="u2_core.review.submit_review.confirm">
      <segment>
        <source>u2_core.review.submit_review.confirm</source>
        <target>Are you sure you want to submit a review?</target>
      </segment>
    </unit>
    <unit id="QxVoR5R" name="u2_core.review.success">
      <segment>
        <source>u2_core.review.success</source>
        <target>Review successful.</target>
      </segment>
    </unit>
    <unit id="RcomhaQ" name="u2_core.reviews">
      <segment>
        <source>u2_core.reviews</source>
        <target>Reviews</target>
      </segment>
    </unit>
    <unit id="FVUmLRJ" name="u2_core.roles">
      <segment>
        <source>u2_core.roles</source>
        <target>Roles</target>
      </segment>
    </unit>
    <unit id="GI2IQEH" name="u2_core.run_now_this_given_subscription">
      <segment>
        <source>u2_core.run_now_this_given_subscription</source>
        <target>Run now this subscription (%subscription_name%)</target>
      </segment>
    </unit>
    <unit id="J9HSoSo" name="u2_core.save_groups">
      <segment>
        <source>u2_core.save_groups</source>
        <target>Save groups</target>
      </segment>
    </unit>
    <unit id="F.oxSIM" name="u2_core.save_roles">
      <segment>
        <source>u2_core.save_roles</source>
        <target>Save roles</target>
      </segment>
    </unit>
    <unit id="RS0sEeJ" name="u2_core.save_units">
      <segment>
        <source>u2_core.save_units</source>
        <target>Save units</target>
      </segment>
    </unit>
    <unit id="UOoF509" name="u2_core.save_users">
      <segment>
        <source>u2_core.save_users</source>
        <target>Save users</target>
      </segment>
    </unit>
    <unit id="CG1Y1ee" name="u2.saved_filter">
      <segment>
        <source>u2.saved_filter</source>
        <target>Saved Filter</target>
      </segment>
    </unit>
    <unit id="EpsDLGE" name="u2.saved_filter.frequency.help">
      <segment>
        <source>u2.saved_filter.frequency.help</source>
        <target><![CDATA[
            <p>A string comprising five fields separated by spaces:</p>
            <ul>
                <li>Minutes (0 to 59)</li>
                <li>Hours (0 to 23)</li>
                <li>Day of Month (1 to 31)</li>
                <li>Month (1 to 12 or JAN to DEC)</li>
                <li>Day of Week (0 to 6 or SUN to SAT, 1 = Monday)</li>
            </ul>
            <p>An asterisk (<kbd>*</kbd>) means “all”.</p>
            <p>Use commas (<kbd>,</kbd>) to separate items of a list.</p>
            <p>Use a hyphen (<kbd>-</kbd>) to define ranges.</p>
            <p>Use a slash (<kbd>/</kbd>) to specify increments. Only values that evenly divide their range are valid (Minutes and seconds: /2, /3, /4, /5, /6, /10, /12, /15, /20, /30. Hours: /2, /3, /4, /6, /8, /12). A number before the slash indicates where to start the increment (3/6 in hours means at 3, 9, 15, and 21), no value before the slash means 0.</p>
            <p>For more information check: <a href="https://en.wikipedia.org/wiki/Cron#CRON_expression" target="_blank">en.wikipedia.org/wiki/Cron#CRON_expression</a>.</p>
            <p>Examples:</p>
            <ul>
                <li><kbd>5 * * * *</kbd> Every day at each hour and 5 minutes (0:05, 1:05, […], 23:05)</li>
                <li><kbd>0 7 * * MON</kbd> Mondays at 7:00</li>
                <li><kbd>0 9 1,15 * *</kbd> The 1st and 15th day of the month at 9:00</li>
                <li><kbd>*/15 9-18 * * MON-FRI</kbd> Every 15 minutes on weekdays between 9:00 and 18:00</li>
            </ul>
        ]]></target>
      </segment>
    </unit>
    <unit id="_GMMjZs" name="u2.saved_filter.subscription_did_not_run">
      <segment>
        <source>u2.saved_filter.subscription_did_not_run</source>
        <target>Subscription is overdue.</target>
      </segment>
    </unit>
    <unit id=".wxd8dU" name="u2.saved_filter.success_the_given_filter_has_been_saved">
      <segment>
        <source>u2.saved_filter.success_the_given_filter_has_been_saved</source>
        <target>The filter “%saved_filter_name%” has been saved successfully.</target>
      </segment>
    </unit>
    <unit id="oWz3Ive" name="u2.saved_filter_subscription">
      <segment>
        <source>u2.saved_filter_subscription</source>
        <target>Saved Filter Subscription</target>
      </segment>
    </unit>
    <unit id="Qc2JPnj" name="u2.saved_filter_subscriptions">
      <segment>
        <source>u2.saved_filter_subscriptions</source>
        <target>Saved Filter Subscriptions</target>
      </segment>
    </unit>
    <unit id="Hj1uYzc" name="u2.saved_filter_subscriptions.help">
      <segment>
        <source>u2.saved_filter_subscriptions.help</source>
        <target>To create a new subscription, go to the edit page for a saved filter.</target>
      </segment>
    </unit>
    <unit id="81OKyGl" name="u2_core.security">
      <segment>
        <source>u2_core.security</source>
        <target>Security</target>
      </segment>
    </unit>
    <unit id="TjFhFkr" name="u2_core.see_all_saved_filters">
      <segment>
        <source>u2_core.see_all_saved_filters</source>
        <target>See all saved filters</target>
      </segment>
    </unit>
    <unit id="aAK95ba" name="u2.select_a_file">
      <segment>
        <source>u2.select_a_file</source>
        <target>Select a file</target>
      </segment>
    </unit>
    <unit id="S8GVrzw" name="u2.select_a_period">
      <segment>
        <source>u2.select_a_period</source>
        <target>Select a period</target>
      </segment>
    </unit>
    <unit id="poLBx2z" name="u2.select_a_unit">
      <segment>
        <source>u2.select_a_unit</source>
        <target>Select a unit</target>
      </segment>
    </unit>
    <unit id="JXff5wl" name="u2.select_a_user">
      <segment>
        <source>u2.select_a_user</source>
        <target>Select a user</target>
      </segment>
    </unit>
    <unit id="5JX2pv9" name="u2.select_an_item">
      <segment>
        <source>u2.select_an_item</source>
        <target>Select an item</target>
      </segment>
    </unit>
    <unit id="A3ghQYO" name="u2.select_entity_type_to_create">
      <segment>
        <source>u2.select_entity_type_to_create</source>
        <target>Select entity type to create</target>
      </segment>
    </unit>
    <unit id="FRaqI7p" name="u2.select_file_types">
      <segment>
        <source>u2.select_file_types</source>
        <target>Select file types</target>
      </segment>
    </unit>
    <unit id="llJPhCv" name="u2.select_unit_type">
      <segment>
        <source>u2.select_unit_type</source>
        <target>Select unit type</target>
      </segment>
    </unit>
    <unit id="m.D3YwI" name="u2.select_unit_type_to_create">
      <segment>
        <source>u2.select_unit_type_to_create</source>
        <target>Select unit type to create</target>
      </segment>
    </unit>
    <unit id="A2jLrDq" name="u2_core.send">
      <segment>
        <source>u2_core.send</source>
        <target>Send</target>
      </segment>
    </unit>
    <unit id="RkKDQci" name="u2_core.send_email">
      <segment>
        <source>u2_core.send_email</source>
        <target>Send email</target>
      </segment>
    </unit>
    <unit id="fztIGeJ" name="u2_core.show">
      <segment>
        <source>u2_core.show</source>
        <target>Show</target>
      </segment>
    </unit>
    <unit id="nxSVnql" name="u2_core.show_current_calendar_week">
      <segment>
        <source>u2_core.show_current_calendar_week</source>
        <target>Show current calendar week</target>
      </segment>
    </unit>
    <unit id="61jDLqF" name="u2_core.show_current_date">
      <segment>
        <source>u2_core.show_current_date</source>
        <target>Show current date</target>
      </segment>
    </unit>
    <unit id="eyN47Xs" name="u2_core.show_entities_linked_with_this_file">
      <segment>
        <source>u2_core.show_entities_linked_with_this_file</source>
        <target>Show entities linked with this file</target>
      </segment>
    </unit>
    <unit id="dWcT5Xt" name="u2_core.show_information_about_your_system">
      <segment>
        <source>u2_core.show_information_about_your_system</source>
        <target>Show information about your system</target>
      </segment>
    </unit>
    <unit id="qNFDdgY" name="u2_core.show_more">
      <segment>
        <source>u2_core.show_more</source>
        <target>Show more</target>
      </segment>
    </unit>
    <unit id="eNWimin" name="u2_core.show_the_filtered_table">
      <segment>
        <source>u2_core.show_the_filtered_table</source>
        <target>Show the filtered table</target>
      </segment>
    </unit>
    <unit id="b7kw43R" name="u2_core.showing_count_of_total_count_records">
      <segment>
        <source>u2_core.showing_count_of_total_count_records</source>
        <target>Showing %showing_count% of %count% record|Showing %showing_count% of %count% records</target>
      </segment>
    </unit>
    <unit id="h_VpskY" name="u2.simulate">
      <segment>
        <source>u2.simulate</source>
        <target>Simulate</target>
      </segment>
    </unit>
    <unit id="bLO5IeI" name="u2_core.start">
      <segment>
        <source>u2_core.start</source>
        <target>Start</target>
      </segment>
    </unit>
    <unit id="85k_eKp" name="u2_core.start_date">
      <segment>
        <source>u2_core.start_date</source>
        <target>Start Date</target>
      </segment>
    </unit>
    <unit id="gQ2BvhO" name="u2_core.started_at">
      <segment>
        <source>u2_core.started_at</source>
        <target>Started at</target>
      </segment>
    </unit>
    <unit id="lS6d.TH" name="u2_core.status">
      <segment>
        <source>u2_core.status</source>
        <target>Status</target>
      </segment>
    </unit>
    <unit id="z0DjOGB" name="u2_core.status_list">
      <segment>
        <source>u2_core.status_list</source>
        <target>Status List</target>
      </segment>
    </unit>
    <unit id="VXKU8ay" name="u2_core.status_type">
      <segment>
        <source>u2_core.status_type</source>
        <target>Status Type</target>
      </segment>
    </unit>
    <unit id="go70s3K" name="u2_core.statuses">
      <segment>
        <source>u2_core.statuses</source>
        <target>Statuses</target>
      </segment>
    </unit>
    <unit id="iceJ46x" name="u2_core.structure">
      <segment>
        <source>u2_core.structure</source>
        <target>Structure</target>
      </segment>
    </unit>
    <unit id="mSMFYFW" name="u2_core.styling_links">
      <segment>
        <source>u2_core.styling_links</source>
        <target>Styling Links</target>
      </segment>
    </unit>
    <unit id="EFNcQGh" name="u2_core.subscribed_groups">
      <segment>
        <source>u2_core.subscribed_groups</source>
        <target>Subscribed Groups</target>
      </segment>
    </unit>
    <unit id="NqUytJ3" name="u2_core.subscribed_users">
      <segment>
        <source>u2_core.subscribed_users</source>
        <target>Subscribed Users</target>
      </segment>
    </unit>
    <unit id=".CSRtIA" name="u2_core.subscriptions">
      <segment>
        <source>u2_core.subscriptions</source>
        <target>Subscriptions</target>
      </segment>
    </unit>
    <unit id="P_eo2va" name="u2_core.subscriptions_overview">
      <segment>
        <source>u2_core.subscriptions_overview</source>
        <target>Subscriptions Overview</target>
      </segment>
    </unit>
    <unit id="ckdIGkK" name="u2_core.success">
      <segment>
        <source>u2_core.success</source>
        <target>Success</target>
      </segment>
    </unit>
    <unit id="Hn9odVz" name="u2_core.success_saved">
      <segment>
        <source>u2_core.success_saved</source>
        <target>Saved.</target>
      </segment>
    </unit>
    <unit id="tm1MzAs" name="u2_core.support">
      <segment>
        <source>u2_core.support</source>
        <target>Support</target>
      </segment>
    </unit>
    <unit id="cAJPLrh" name="u2_core.system_message">
      <segment>
        <source>u2_core.system_message</source>
        <target>System Message</target>
      </segment>
    </unit>
    <unit id="hWHTSxb" name="u2_core.system_message.plural">
      <segment>
        <source>u2_core.system_message.plural</source>
        <target>System Messages</target>
      </segment>
    </unit>
    <unit id="rCTxOPw" name="u2_core.system_settings">
      <segment>
        <source>u2_core.system_settings</source>
        <target>System Settings</target>
      </segment>
    </unit>
    <unit id="QWnc5VH" name="u2_core.tags">
      <segment>
        <source>u2_core.tags</source>
        <target>Tags</target>
      </segment>
    </unit>
    <unit id="AAVdNiq" name="u2_core.tax_advisor">
      <segment>
        <source>u2_core.tax_advisor</source>
        <target>Tax Advisor</target>
      </segment>
    </unit>
    <unit id="P8kZcBr" name="u2_core.tax_assessment">
      <segment>
        <source>u2_core.tax_assessment</source>
        <target>Tax Assessment</target>
      </segment>
    </unit>
    <unit id="HnnijAz" name="u2_core.tax_number">
      <segment>
        <source>u2_core.tax_number</source>
        <target>Tax Number</target>
      </segment>
    </unit>
    <unit id="KN6D.u9" name="u2_core.telephone">
      <segment>
        <source>u2_core.telephone</source>
        <target>Telephone</target>
      </segment>
    </unit>
    <unit id="mmzTq69" name="u2_core.the_given_user_is_requesting_permissions_to_the_given_file">
      <segment>
        <source>u2_core.the_given_user_is_requesting_permissions_to_the_given_file</source>
        <target>The user %user_name% is requesting permissions to the file %file_name%.</target>
      </segment>
    </unit>
    <unit id="Zw57tN_" name="u2_core.the_name_of_this_file_is_restricted">
      <segment>
        <source>u2_core.the_name_of_this_file_is_restricted</source>
        <target>(The name of this file is restricted)</target>
      </segment>
    </unit>
    <unit id="FYWlPBI" name="u2.datasheets.datasheet_collection.name_restricted">
      <segment>
        <source>u2.datasheets.datasheet_collection.name_restricted</source>
        <target>+%count% restricted</target>
      </segment>
    </unit>
    <unit id="U7mTtXm" name="u2.datasheets.datasheet_collection.name_restricted.tooltip">
      <segment>
        <source>u2.datasheets.datasheet_collection.name_restricted.tooltip</source>
        <target>You do not have permission to view one or more datasheet collections</target>
      </segment>
    </unit>
    <unit id="LeFNlEZ" name="u2.no_attachments">
      <segment>
        <source>u2.no_attachments</source>
        <target>No attachments</target>
      </segment>
    </unit>
    <unit id="NnB_v.I" name="u2_core.there_are_no_elements_assigned">
      <segment>
        <source>u2_core.there_are_no_elements_assigned</source>
        <target>There are no elements assigned</target>
      </segment>
    </unit>
    <unit id="DlSle_I" name="u2_core.there_are_no_elements_to_be_assigned">
      <segment>
        <source>u2_core.there_are_no_elements_to_be_assigned</source>
        <target>There are no elements to be assigned</target>
      </segment>
    </unit>
    <unit id="ouNDfmy" name="u2_core.this_file_is_not_linked_to_anything_at_the_moment">
      <segment>
        <source>u2_core.this_file_is_not_linked_to_anything_at_the_moment</source>
        <target>This file is not linked to anything at the moment.</target>
      </segment>
    </unit>
    <unit id="JkGkN0w" name="u2.title">
      <segment>
        <source>u2.title</source>
        <target>Title</target>
      </segment>
    </unit>
    <unit id="q.iiZCU" name="u2_core.to_date">
      <segment>
        <source>u2_core.to_date</source>
        <target>To Date</target>
      </segment>
    </unit>
    <unit id=".lqLRyC" name="u2_core.to_grant_permissions_go_to_the_edit_page_forgiven_file">
      <segment>
        <source>u2_core.to_grant_permissions_go_to_the_edit_page_forgiven_file</source>
        <target>To grant permissions go to the edit page for %file_name%:</target>
      </segment>
    </unit>
    <unit id="GNHlCgC" name="u2_core.tools">
      <segment>
        <source>u2_core.tools</source>
        <target>Tools</target>
      </segment>
    </unit>
    <unit id="mY4Fn5V" name="u2_core.type">
      <segment>
        <source>u2_core.type</source>
        <target>Type</target>
      </segment>
    </unit>
    <unit id="Tkbsc9d" name="u2_core.types">
      <segment>
        <source>u2_core.types</source>
        <target>Types</target>
      </segment>
    </unit>
    <unit id="YumO3t." name="u2_core.u2_documentation">
      <segment>
        <source>u2_core.u2_documentation</source>
        <target>U² Documentation</target>
      </segment>
    </unit>
    <unit id="cB6nlAu" name="u2_core.unlink_file_with_given_name_from_entity.confirmation">
      <segment>
        <source>u2_core.unlink_file_with_given_name_from_entity.confirmation</source>
        <target>Are you sure you want to remove the file “%file_name%” from this entry?</target>
      </segment>
    </unit>
    <unit id="cNfYSio" name="u2_core.up_next">
      <segment>
        <source>u2_core.up_next</source>
        <target>Up Next</target>
      </segment>
    </unit>
    <unit id="2oc6Naj" name="u2_core.update_assignment.success">
      <segment>
        <source>u2_core.update_assignment.success</source>
        <target>Assignment saved.</target>
      </segment>
    </unit>
    <unit id="4tmw.ie" name="u2_core.updated">
      <segment>
        <source>u2_core.updated</source>
        <target>Updated</target>
      </segment>
    </unit>
    <unit id="9TovUqg" name="u2_core.updated_at">
      <segment>
        <source>u2_core.updated_at</source>
        <target>Updated at</target>
      </segment>
    </unit>
    <unit id="Jle2w8i" name="u2_core.updated_by">
      <segment>
        <source>u2_core.updated_by</source>
        <target>Updated by</target>
      </segment>
    </unit>
    <unit id="wmeYOAu" name="u2_core.upload">
      <segment>
        <source>u2_core.upload</source>
        <target>Upload</target>
      </segment>
    </unit>
    <unit id="in2uBtU" name="u2_core.upload_a_new_file">
      <segment>
        <source>u2_core.upload_a_new_file</source>
        <target>Upload a new file</target>
      </segment>
    </unit>
    <unit id="YfT._S4" name="u2_core.upload_file.drop_a_file_here_to_attach_it">
      <segment>
        <source>u2_core.upload_file.drop_a_file_here_to_attach_it</source>
        <target>Drop a file here to attach it</target>
      </segment>
    </unit>
    <unit id="4ror1LF" name="u2_core.upload_file.error">
      <segment>
        <source>u2_core.upload_file.error</source>
        <target>Upload failed.</target>
      </segment>
    </unit>
    <unit id="6TRTIRk" name="u2_core.uql">
      <segment>
        <source>u2_core.uql</source>
        <target>UQL</target>
      </segment>
    </unit>
    <unit id="TanDDn." name="u2_core.use_default">
      <segment>
        <source>u2_core.use_default</source>
        <target>Use Default</target>
      </segment>
    </unit>
    <unit id="jnd2mje" name="u2_core.user">
      <segment>
        <source>u2_core.user</source>
        <target>User</target>
      </segment>
    </unit>
    <unit id="Lekwf9j" name="u2_core.user_agent">
      <segment>
        <source>u2_core.user_agent</source>
        <target>User Agent</target>
      </segment>
    </unit>
    <unit id="IZrlrqu" name="u2_core.user_email_has_changed">
      <segment>
        <source>u2_core.user_email_has_changed</source>
        <target>The email address of your U² account “%user_name%” changed.</target>
      </segment>
    </unit>
    <unit id="CnfsZ7i" name="u2_core.user_email_listener.email_has_changed.mail_body.title">
      <segment>
        <source>u2_core.user_email_listener.email_has_changed.mail_body.title</source>
        <target>Your email address has changed</target>
      </segment>
    </unit>
    <unit id="gmCFPHi" name="u2_core.user_group_list">
      <segment>
        <source>u2_core.user_group_list</source>
        <target>User Group List</target>
      </segment>
    </unit>
    <unit id="KnDeoj8" name="u2_core.user_group_name">
      <segment>
        <source>u2_core.user_group_name</source>
        <target>User Group Name</target>
      </segment>
    </unit>
    <unit id="ijFXfGR" name="u2_core.user_group_with_given_group_name">
      <segment>
        <source>u2_core.user_group_with_given_group_name</source>
        <target>User Group “%group_name%”</target>
      </segment>
    </unit>
    <unit id="A2UqJ9Y" name="u2_core.user_groups">
      <segment>
        <source>u2_core.user_groups</source>
        <target>User Groups</target>
      </segment>
    </unit>
    <unit id="Dakw9Bk" name="u2_core.user_information">
      <segment>
        <source>u2_core.user_information</source>
        <target>User Information</target>
      </segment>
    </unit>
    <unit id="UOo0qKl" name="u2_core.user_interface">
      <segment>
        <source>u2_core.user_interface</source>
        <target>User Interface</target>
      </segment>
    </unit>
    <unit id="YNHyk_2" name="u2_core.user_list">
      <segment>
        <source>u2_core.user_list</source>
        <target>User List</target>
      </segment>
    </unit>
    <unit id="66GzCYU" name="u2_core.user_profile">
      <segment>
        <source>u2_core.user_profile</source>
        <target>User Profile</target>
      </segment>
    </unit>
    <unit id="Pm2BN.z" name="u2_core.user_property_has_changed.how_to_react">
      <segment>
        <source>u2_core.user_property_has_changed.how_to_react</source>
        <target>
          If you did not request this change or this notification is unexpected then there may be a security issue with your user account.
          In such an event, please update your credentials and notify your administrator.
          If, however, you requested this change then you may safely ignore this email.
        </target>
      </segment>
    </unit>
    <unit id="pKivktA" name="u2_core.user_settings">
      <segment>
        <source>u2_core.user_settings</source>
        <target>User Settings</target>
      </segment>
    </unit>
    <unit id="loQAaok" name="u2_core.username">
      <segment>
        <source>u2_core.username</source>
        <target>Username</target>
      </segment>
    </unit>
    <unit id="xcUJXf0" name="u2_core.users">
      <segment>
        <source>u2_core.users</source>
        <target>Users</target>
      </segment>
    </unit>
    <unit id="KSPEn9U" name="u2_core.valid_from">
      <segment>
        <source>u2_core.valid_from</source>
        <target>Valid from</target>
      </segment>
    </unit>
    <unit id="Nz_AEeA" name="u2_core.valid_to">
      <segment>
        <source>u2_core.valid_to</source>
        <target>Valid to</target>
      </segment>
    </unit>
    <unit id="wxEvn9T" name="u2_core.verified">
      <segment>
        <source>u2_core.verified</source>
        <target>Verified</target>
      </segment>
    </unit>
    <unit id="NqE4_KP" name="u2_core.view">
      <segment>
        <source>u2_core.view</source>
        <target>View</target>
      </segment>
    </unit>
    <unit id="XIY3aDm" name="u2_core.view_profile">
      <segment>
        <source>u2_core.view_profile</source>
        <target>View profile</target>
      </segment>
    </unit>
    <unit id="Do7sJ_T" name="u2_core.warning">
      <segment>
        <source>u2_core.warning</source>
        <target>Warning</target>
      </segment>
    </unit>
    <unit id="Use5UWf" name="u2_core.warning_authentication_success">
      <segment>
        <source>u2_core.warning_authentication_success</source>
        <target><![CDATA[
            There has been a failed login attempt.<br>
            The attempt was logged on:<br>
            %failed_login_times_message%<br>
            <strong>Please inform your administrator if this was not you.</strong>
            |
            There have been %count% failed login attempts.<br>
            The last recorded attempts were:<br>
            %failed_login_times_message%<br>
            <strong>Please inform your administrator if this was not you.</strong>
        ]]></target>
      </segment>
    </unit>
    <unit id="CqlWwcU" name="u2_core.watch.unwatch">
      <segment>
        <source>u2_core.watch.unwatch</source>
        <target>Unwatch</target>
      </segment>
    </unit>
    <unit id="50i_N_H" name="u2_core.watch.watch">
      <segment>
        <source>u2_core.watch.watch</source>
        <target>Watch</target>
      </segment>
    </unit>
    <unit id="3q0Pam9" name="u2_core.watch.watchers">
      <segment>
        <source>u2_core.watch.watchers</source>
        <target>Watchers</target>
      </segment>
    </unit>
    <unit id="51NLIDx" name="u2_core.welcome_to_u2">
      <segment>
        <source>u2_core.welcome_to_u2</source>
        <target>Welcome to U²</target>
      </segment>
    </unit>
    <unit id="qL0Znp7" name="u2_core.widget.does_not_exist">
      <segment>
        <source>u2_core.widget.does_not_exist</source>
        <target>The widget “%widget_name%” does not exist.</target>
      </segment>
    </unit>
    <unit id="P.9DgaS" name="u2_core.widget.insufficient_permissions">
      <segment>
        <source>u2_core.widget.insufficient_permissions</source>
        <target>Insufficient permissions to view widget “%widget_name%”.</target>
      </segment>
    </unit>
    <unit id="puTC5lf" name="u2_core.widget.invalid_argument_value">
      <segment>
        <source>u2_core.widget.invalid_argument_value</source>
        <target>Invalid value for property “%property%”. Unable to render widget “%widget_name%”.</target>
      </segment>
    </unit>
    <unit id="eM9chqL" name="u2_core.widget.malformed_configuration">
      <segment>
        <source>u2_core.widget.malformed_configuration</source>
        <target>Unable to parse widget configuration.</target>
      </segment>
    </unit>
    <unit id="xzSlBNF" name="u2_core.widget.missing_property">
      <segment>
        <source>u2_core.widget.missing_property</source>
        <target>Missing property “%property%”. Unable to render widget “%widget_name%”.</target>
      </segment>
    </unit>
    <unit id="t7AVuFQ" name="u2_core.widget.not_support">
      <segment>
        <source>u2_core.widget.not_support</source>
        <target>Widget “%widget_name%” is not supported.</target>
      </segment>
    </unit>
    <unit id="fA3Yqph" name="u2_core.widget.unknown_error_occurred_while_rendering_widget">
      <segment>
        <source>u2_core.widget.unknown_error_occurred_while_rendering_widget</source>
        <target>An unkown error occurred while rendering widget “%widget_name%”. View the logs for further information.</target>
      </segment>
    </unit>
    <unit id="VvZZVHc" name="u2_core.workflow">
      <segment>
        <source>u2_core.workflow</source>
        <target>Workflow</target>
      </segment>
    </unit>
    <unit id="y98DqRh" name="u2_core.workflow.actions">
      <segment>
        <source>u2_core.workflow.actions</source>
        <target>Actions</target>
      </segment>
    </unit>
    <unit id="jWQiheE" name="u2_core.workflow.add_action">
      <segment>
        <source>u2_core.workflow.add_action</source>
        <target>Add Action</target>
      </segment>
    </unit>
    <unit id="5r5F8Qi" name="u2_core.workflow.add_check">
      <segment>
        <source>u2_core.workflow.add_check</source>
        <target>Add Checklist Item</target>
      </segment>
    </unit>
    <unit id="Zsq.lrs" name="u2_core.workflow.add_condition">
      <segment>
        <source>u2_core.workflow.add_condition</source>
        <target>Add Condition</target>
      </segment>
    </unit>
    <unit id="sOoXnPT" name="u2_core.workflow.add_condition_to_transition.success">
      <segment>
        <source>u2_core.workflow.add_condition_to_transition.success</source>
        <target>Condition added.</target>
      </segment>
    </unit>
    <unit id="d78ZGEA" name="u2_core.workflow.add_new_transition">
      <segment>
        <source>u2_core.workflow.add_new_transition</source>
        <target>Add New Transition</target>
      </segment>
    </unit>
    <unit id="BbnRf1f" name="u2_core.workflow.add_new_workflow">
      <segment>
        <source>u2_core.workflow.add_new_workflow</source>
        <target>Add New Workflow</target>
      </segment>
    </unit>
    <unit id="7tHRBy7" name="u2_core.workflow.add_transition">
      <segment>
        <source>u2_core.workflow.add_transition</source>
        <target>Add Transition</target>
      </segment>
    </unit>
    <unit id="UJKDHcA" name="u2_core.workflow.adding_condition_to_transition">
      <segment>
        <source>u2_core.workflow.adding_condition_to_transition</source>
        <target>Adding condition to transition</target>
      </segment>
    </unit>
    <unit id="jWNziOt" name="u2_core.workflow.change_status_to_given_status">
      <segment>
        <source>u2_core.workflow.change_status_to_given_status</source>
        <target>Change status to %destinationStatus%</target>
      </segment>
    </unit>
    <unit id="JFrtNtH" name="u2_core.workflow.change_workflow.success">
      <segment>
        <source>u2_core.workflow.change_workflow.success</source>
        <target>Workflow changed successfully.</target>
      </segment>
    </unit>
    <unit id="llVtx7f" name="u2_core.workflow.check">
      <segment>
        <source>u2_core.workflow.check</source>
        <target>Checklist Item</target>
      </segment>
    </unit>
    <unit id="AB5sFLV" name="u2_core.workflow.checklist">
      <segment>
        <source>u2_core.workflow.checklist</source>
        <target>Checklist</target>
      </segment>
    </unit>
    <unit id="qb6f0C5" name="u2_core.workflow.conditions">
      <segment>
        <source>u2_core.workflow.conditions</source>
        <target>Conditions</target>
      </segment>
    </unit>
    <unit id="jZo1SSq" name="u2_core.workflow.confirm_status_transition">
      <segment>
        <source>u2_core.workflow.confirm_status_transition</source>
        <target>Confirm status transition</target>
      </segment>
    </unit>
    <unit id="IIitCXT" name="u2_core.workflow.current_status">
      <segment>
        <source>u2_core.workflow.current_status</source>
        <target>Current Status</target>
      </segment>
    </unit>
    <unit id="p93lb3b" name="u2_core.workflow.current_workflow">
      <segment>
        <source>u2_core.workflow.current_workflow</source>
        <target>Current Workflow</target>
      </segment>
    </unit>
    <unit id="2tSzh8A" name="u2_core.workflow.delete_check">
      <segment>
        <source>u2_core.workflow.delete_check</source>
        <target>Delete Checklist Item (%check%)</target>
      </segment>
    </unit>
    <unit id="jqeM2VL" name="u2_core.workflow.delete_check.confirmation">
      <segment>
        <source>u2_core.workflow.delete_check.confirmation</source>
        <target>This will permanently delete the checklist item and all history data associated with it. Alternatively, you can deactivate the item to no-longer show it in new checklists while keeping the existing history data.</target>
      </segment>
    </unit>
    <unit id="_voiKBy" name="u2_core.workflow.delete_transition.confirmation">
      <segment>
        <source>u2_core.workflow.delete_transition.confirmation</source>
        <target>Are you sure you want to remove this transition?</target>
      </segment>
    </unit>
    <unit id="mKxyFR0" name="u2_core.workflow.delete_transition_with_given_name">
      <segment>
        <source>u2_core.workflow.delete_transition_with_given_name</source>
        <target>Delete transition (%transition_name%)</target>
      </segment>
    </unit>
    <unit id="XF9PW7a" name="u2_core.workflow.delete_workflow">
      <segment>
        <source>u2_core.workflow.delete_workflow</source>
        <target>Delete Workflow</target>
      </segment>
    </unit>
    <unit id="_NJfN0S" name="u2_core.workflow.delete_workflow.confirmation">
      <segment>
        <source>u2_core.workflow.delete_workflow.confirmation</source>
        <target>Are you sure you want to delete this Workflow?</target>
      </segment>
    </unit>
    <unit id="00v5nUW" name="u2_core.workflow.destination_status">
      <segment>
        <source>u2_core.workflow.destination_status</source>
        <target>Destination Status</target>
      </segment>
    </unit>
    <unit id="YRWfZYZ" name="u2_core.workflow.destination_workflow">
      <segment>
        <source>u2_core.workflow.destination_workflow</source>
        <target>Destination Workflow</target>
      </segment>
    </unit>
    <unit id="mghvuWz" name="u2_core.workflow.edit_assignment">
      <segment>
        <source>u2_core.workflow.edit_assignment</source>
        <target>Edit Assignment</target>
      </segment>
    </unit>
    <unit id="xvDKtRR" name="u2_core.workflow.edit_check">
      <segment>
        <source>u2_core.workflow.edit_check</source>
        <target>Edit Checklist Item (%check%)</target>
      </segment>
    </unit>
    <unit id="ciiuNRO" name="u2_core.workflow.edit_transition_with_given_name">
      <segment>
        <source>u2_core.workflow.edit_transition_with_given_name</source>
        <target>Edit transition (%transition_name%)</target>
      </segment>
    </unit>
    <unit id="_YmeX0H" name="u2_core.workflow.edit_workflow">
      <segment>
        <source>u2_core.workflow.edit_workflow</source>
        <target>Edit Workflow</target>
      </segment>
    </unit>
    <unit id="rLPaJIL" name="u2_core.workflow.edit_workflow_assignment_with_given_name">
      <segment>
        <source>u2_core.workflow.edit_workflow_assignment_with_given_name</source>
        <target>Edit Workflow Assignment “%workflow_name%”</target>
      </segment>
    </unit>
    <unit id="HdGyuxo" name="u2_core.workflow.new_check">
      <segment>
        <source>u2_core.workflow.new_check</source>
        <target>New Checklist Item</target>
      </segment>
    </unit>
    <unit id="kDw7SKR" name="u2_core.workflow.new_transition">
      <segment>
        <source>u2_core.workflow.new_transition</source>
        <target>New Transition</target>
      </segment>
    </unit>
    <unit id="FlwXXWK" name="u2_core.workflow.new_workflow">
      <segment>
        <source>u2_core.workflow.new_workflow</source>
        <target>New Workflow</target>
      </segment>
    </unit>
    <unit id="WecZEI1" name="u2_core.workflow.no_actions_attached_to_transition">
      <segment>
        <source>u2_core.workflow.no_actions_attached_to_transition</source>
        <target>There are no actions attached to this transition.</target>
      </segment>
    </unit>
    <unit id="nRKuINC" name="u2_core.workflow.no_associated_statuses">
      <segment>
        <source>u2_core.workflow.no_associated_statuses</source>
        <target>No associated statuses</target>
      </segment>
    </unit>
    <unit id="K.oNb3T" name="u2_core.workflow.no_conditions_attached_to_transition">
      <segment>
        <source>u2_core.workflow.no_conditions_attached_to_transition</source>
        <target>There are no conditions attached to this transition.</target>
      </segment>
    </unit>
    <unit id="sfAoRcm" name="u2_core.workflow.no_destination_status_available">
      <segment>
        <source>u2_core.workflow.no_destination_status_available</source>
        <target>No destination status available</target>
      </segment>
    </unit>
    <unit id="YjzJB5t" name="u2_core.workflow.no_status_has_been_set">
      <segment>
        <source>u2_core.workflow.no_status_has_been_set</source>
        <target>No status has been set</target>
      </segment>
    </unit>
    <unit id="5YF0gHU" name="u2_core.workflow.no_transitions_available">
      <segment>
        <source>u2_core.workflow.no_transitions_available</source>
        <target>No transitions available</target>
      </segment>
    </unit>
    <unit id="bof4NF2" name="u2_core.workflow.origin_status">
      <segment>
        <source>u2_core.workflow.origin_status</source>
        <target>Origin Status</target>
      </segment>
    </unit>
    <unit id="GvhT_6U" name="u2_core.workflow.remap_missing_statuses">
      <segment>
        <source>u2_core.workflow.remap_missing_statuses</source>
        <target>Remap Missing Statuses</target>
      </segment>
    </unit>
    <unit id="RhatPrp" name="u2_core.workflow.remove_action.confirmation">
      <segment>
        <source>u2_core.workflow.remove_action.confirmation</source>
        <target>Are you sure you want to remove this action?</target>
      </segment>
    </unit>
    <unit id="QKybv6v" name="u2_core.workflow.remove_action.success">
      <segment>
        <source>u2_core.workflow.remove_action.success</source>
        <target>Action removed.</target>
      </segment>
    </unit>
    <unit id="FOFrkwI" name="u2_core.workflow.remove_action_with_given_name">
      <segment>
        <source>u2_core.workflow.remove_action_with_given_name</source>
        <target>Remove action (%action_readable_name%)</target>
      </segment>
    </unit>
    <unit id=".NEMRzc" name="u2_core.workflow.remove_condition.confirmation">
      <segment>
        <source>u2_core.workflow.remove_condition.confirmation</source>
        <target>Are you sure you want to remove this condition?</target>
      </segment>
    </unit>
    <unit id="wFiNndu" name="u2_core.workflow.remove_condition.success">
      <segment>
        <source>u2_core.workflow.remove_condition.success</source>
        <target>Condition removed.</target>
      </segment>
    </unit>
    <unit id="VD4LSgE" name="u2_core.workflow.remove_condition_with_given_name">
      <segment>
        <source>u2_core.workflow.remove_condition_with_given_name</source>
        <target>Remove condition (%condition_readable_name%)</target>
      </segment>
    </unit>
    <unit id="Ee2E3xb" name="u2_core.workflow.remove_transition.success">
      <segment>
        <source>u2_core.workflow.remove_transition.success</source>
        <target>Transition removed.</target>
      </segment>
    </unit>
    <unit id="ykMQfHl" name="u2_core.workflow.select_a_status">
      <segment>
        <source>u2_core.workflow.select_a_status</source>
        <target>Select a status</target>
      </segment>
    </unit>
    <unit id="v2clkNm" name="u2_core.workflow.select_destination_status_for_assigned_objects">
      <segment>
        <source>u2_core.workflow.select_destination_status_for_assigned_objects</source>
        <target>The following statuses have objects assigned that cannot be remapped automatically. Please select a destination status for these objects.</target>
      </segment>
    </unit>
    <unit id="Y1fE2Ny" name="u2_core.workflow.status_history_for_given_entity_name">
      <segment>
        <source>u2_core.workflow.status_history_for_given_entity_name</source>
        <target>Workflow Status History for “%entity_name%”</target>
      </segment>
    </unit>
    <unit id="MnY1h7U" name="u2_core.workflow.there_have_been_no_status_changes">
      <segment>
        <source>u2_core.workflow.there_have_been_no_status_changes</source>
        <target>There have been no status changes.</target>
      </segment>
    </unit>
    <unit id="6xIvauu" name="u2_core.workflow.transition_status.success_given_amount_of_records_transitioned">
      <segment>
        <source>u2_core.workflow.transition_status.success_given_amount_of_records_transitioned</source>
        <target>%count% record transitioned successfully.|%count% records transitioned successfully.</target>
      </segment>
    </unit>
    <unit id="TEq1Sso" name="u2_core.workflow.transitions">
      <segment>
        <source>u2_core.workflow.transitions</source>
        <target>Transitions</target>
      </segment>
    </unit>
    <unit id="tBS.3_R" name="u2_core.workflow.warning_no_destination_status_available">
      <segment>
        <source>u2_core.workflow.warning_no_destination_status_available</source>
        <target>Records with this status will not be transitioned.</target>
      </segment>
    </unit>
    <unit id="ZpPf2XI" name="u2_core.workflow_assignment">
      <segment>
        <source>u2_core.workflow_assignment</source>
        <target>Workflow Assignment</target>
      </segment>
    </unit>
    <unit id="tUcVDaQ" name="u2_core.workflow_assignments">
      <segment>
        <source>u2_core.workflow_assignments</source>
        <target>Workflow Assignments</target>
      </segment>
    </unit>
    <unit id="ZH0PWwS" name="u2_core.workflow_list">
      <segment>
        <source>u2_core.workflow_list</source>
        <target>Workflow List</target>
      </segment>
    </unit>
    <unit id="FMWKA0V" name="u2_core.workflows">
      <segment>
        <source>u2_core.workflows</source>
        <target>Workflows</target>
      </segment>
    </unit>
    <unit id="yEe7ig." name="u2_core.write">
      <segment>
        <source>u2_core.write</source>
        <target>Write</target>
      </segment>
    </unit>
    <unit id="wjzyu4g" name="u2_core.you_are_not_authorized_to_import_any_data">
      <segment>
        <source>u2_core.you_are_not_authorized_to_import_any_data</source>
        <target>You are not authorized to import any data.</target>
      </segment>
    </unit>
    <unit id="_VW2UOH" name="u2_core.your_title_text">
      <segment>
        <source>u2_core.your_title_text</source>
        <target>Your title text</target>
      </segment>
    </unit>
    <unit id="Gj_n74." name="u2.datasheets.cannot_initialize_with_missing_exchange_rate">
      <segment>
        <source>u2.datasheets.cannot_initialize_with_missing_exchange_rate</source>
        <target>Unable to restore defaults: Could not find exchange rates “%local_currency_iso%” to “%group_currency_iso%” for period “%period%”. Please add these and reload the page.</target>
      </segment>
    </unit>
    <unit id="Ysi0sWT" name="u2.datasheets.cannot_initialize_with_missing_previous_period">
      <segment>
        <source>u2.datasheets.cannot_initialize_with_missing_previous_period</source>
        <target>Unable to initialize defaults: Period “%period_name%” does not have a previous period. Add a previous period to the period and revisit the page.</target>
      </segment>
    </unit>
    <unit id="D6QGrwY" name="u2.datasheets.delete_given_entity_type_with_given_name">
      <segment>
        <source>u2.datasheets.delete_given_entity_type_with_given_name</source>
        <target>Delete %entity_type_name% (%entity_name%)</target>
      </segment>
    </unit>
    <unit id="XuSuQY_" name="u2.datasheets.delete_given_entity_type_with_given_name.confirmation">
      <segment>
        <source>u2.datasheets.delete_given_entity_type_with_given_name.confirmation</source>
        <target>Are you sure you want to delete this %entity_type_name% (%entity_name%)?</target>
      </segment>
    </unit>
    <unit id="5NfHPWc" name="u2.datasheets.edit_given_entity_type_with_given_name">
      <segment>
        <source>u2.datasheets.edit_given_entity_type_with_given_name</source>
        <target>Edit %entity_type_name% (%entity_name%)</target>
      </segment>
    </unit>
    <unit id="VMzEiUJ" name="u2.datasheets.exchange_method">
      <segment>
        <source>u2.datasheets.exchange_method</source>
        <target>Exchange Method</target>
      </segment>
    </unit>
    <unit id="Czq8sc5" name="u2.datasheets.exchange_method.average">
      <segment>
        <source>u2.datasheets.exchange_method.average</source>
        <target>Average</target>
      </segment>
    </unit>
    <unit id="eW_vPJA" name="u2.datasheets.exchange_method.current">
      <segment>
        <source>u2.datasheets.exchange_method.current</source>
        <target>Current</target>
      </segment>
    </unit>
    <unit id="63sXwxx" name="u2.datasheets.exchange_method.previous_average">
      <segment>
        <source>u2.datasheets.exchange_method.previous_average</source>
        <target>Average exchange rate of previous period</target>
      </segment>
    </unit>
    <unit id="mJfUQJ3" name="u2.datasheets.exchange_method.previous_current">
      <segment>
        <source>u2.datasheets.exchange_method.previous_current</source>
        <target>Latest exchange rate of previous period</target>
      </segment>
    </unit>
    <unit id="8GKsr5y" name="u2.datasheets.formula">
      <segment>
        <source>u2.datasheets.formula</source>
        <target>Formula</target>
      </segment>
    </unit>
    <unit id="GXi6PDH" name="u2.datasheets.formula.formula_string.help">
      <segment>
        <source>u2.datasheets.formula.formula_string.help</source>
        <target><![CDATA[
          Item Reference: {%item_refId%} <br> Reference to an item value from previous Period: {P%item_refId%} <br> All maths operators are allowed <br> Also if -> then is allowed: ItemXXX === 1 ? %true_statement% : %false_statement%]]></target>
      </segment>
    </unit>
    <unit id="QVrSWFP" name="u2.datasheets.group">
      <segment>
        <source>u2.datasheets.group</source>
        <target>Group</target>
      </segment>
    </unit>
    <unit id="vAXcic5" name="u2.datasheets.item.plural">
      <segment>
        <source>u2.datasheets.item.plural</source>
        <target>Items</target>
      </segment>
    </unit>
    <unit id="8CEP52y" name="u2.datasheets.datasheet.plural">
      <segment>
        <source>u2.datasheets.datasheet.plural</source>
        <target>Datasheets</target>
      </segment>
    </unit>
    <unit id="6Occz5S" name="u2.datasheets.module_name">
      <segment>
        <source>u2.datasheets.module_name</source>
        <target>Datasheets</target>
      </segment>
    </unit>
    <unit id="ydPHjya" name="u2.datasheets.name">
      <segment>
        <source>u2.datasheets.name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="uLwLepx" name="u2.datasheets.select_unit_hierarchy">
      <segment>
        <source>u2.datasheets.select_unit_hierarchy</source>
        <target>Select a unit hierarchy</target>
      </segment>
    </unit>
    <unit id="dTg1oET" name="u2.datasheets.status_monitor">
      <segment>
        <source>u2.datasheets.status_monitor</source>
        <target>Datasheet Monitor</target>
      </segment>
    </unit>
    <unit id="bBBmpn0" name="u2.datasheets.status_monitor.plural">
      <segment>
        <source>u2.datasheets.status_monitor.plural</source>
        <target>Datasheet Monitor</target>
      </segment>
    </unit>
    <unit id="Ck3ci4j" name="u2.datasheets.type">
      <segment>
        <source>u2.datasheets.type</source>
        <target>Type</target>
      </segment>
    </unit>
    <unit id="9gXbCXh" name="u2.datasheets.unit_view.form_is_disabled">
      <segment>
        <source>u2.datasheets.unit_view.form_is_disabled</source>
        <target>The form is disabled and cannot be saved.</target>
      </segment>
    </unit>
    <unit id="Tn5ARQJ" name="u2_financial.base_to_group_exchange_rate">
      <segment>
        <source>u2_financial.base_to_group_exchange_rate</source>
        <target>Value to Group Exchange Rate</target>
      </segment>
    </unit>
    <unit id="r2F52ec" name="u2_financial.billing_type">
      <segment>
        <source>u2_financial.billing_type</source>
        <target>Billing Type</target>
      </segment>
    </unit>
    <unit id="E6rat4X" name="u2_financial.billing_type.plural">
      <segment>
        <source>u2_financial.billing_type.plural</source>
        <target>Billing Types</target>
      </segment>
    </unit>
    <unit id="hoqypls" name="u2_financial.carry_forward">
      <segment>
        <source>u2_financial.carry_forward</source>
        <target>Carry Forward</target>
      </segment>
    </unit>
    <unit id="mO93s8K" name="u2_financial.carry_forward_financial_data.help">
      <segment>
        <source>u2_financial.carry_forward_financial_data.help</source>
        <target>Set this flag to mark the financial data to be carried forward during a data transfer to another period.</target>
      </segment>
    </unit>
    <unit id="WwBm8H_" name="u2_financial.collateralisation_type">
      <segment>
        <source>u2_financial.collateralisation_type</source>
        <target>Collateralisation Type</target>
      </segment>
    </unit>
    <unit id="kzQf188" name="u2_financial.collateralisation_type.plural">
      <segment>
        <source>u2_financial.collateralisation_type.plural</source>
        <target>Collateralisation Types</target>
      </segment>
    </unit>
    <unit id="0mNuaEn" name="u2_financial.exchange_rate_shortened">
      <segment>
        <source>u2_financial.exchange_rate_shortened</source>
        <target>E. Rate</target>
      </segment>
    </unit>
    <unit id="toLfTHw" name="u2_financial.group">
      <segment>
        <source>u2_financial.group</source>
        <target>Group</target>
      </segment>
    </unit>
    <unit id="CBn32c7" name="u2_financial.group_parent_income_tax">
      <segment>
        <source>u2_financial.group_parent_income_tax</source>
        <target>Group Parent - Income Tax</target>
      </segment>
    </unit>
    <unit id="4XoiYCX" name="u2_financial.group_parent_vat">
      <segment>
        <source>u2_financial.group_parent_vat</source>
        <target>Group Parent - VAT</target>
      </segment>
    </unit>
    <unit id="8.mbp1v" name="u2_financial.local">
      <segment>
        <source>u2_financial.local</source>
        <target>Local</target>
      </segment>
    </unit>
    <unit id="9Ck86t9" name="u2_core.api_docs">
      <segment>
        <source>u2_core.api_docs</source>
        <target>Api-Documentation</target>
      </segment>
    </unit>
    <unit id="2WZ1kew" name="u2.security.roles.api">
      <segment>
        <source>u2.security.roles.api</source>
        <target>Api</target>
      </segment>
    </unit>
    <unit id="O5Ncnbz" name="u2_financial.local_to_group_exchange_rate">
      <segment>
        <source>u2_financial.local_to_group_exchange_rate</source>
        <target>Local to Group Exchange Rate</target>
      </segment>
    </unit>
    <unit id="c3n3fnd" name="u2_financial.parent_legal_unit">
      <segment>
        <source>u2_financial.parent_legal_unit</source>
        <target>Parent Legal Unit</target>
      </segment>
    </unit>
    <unit id="rWrLd43" name="u2.partner_unit">
      <segment>
        <source>u2.partner_unit</source>
        <target>Partner Unit</target>
      </segment>
    </unit>
    <unit id="cCzUWLH" name="u2_financial.partner_unit_country">
      <segment>
        <source>u2_financial.partner_unit_country</source>
        <target>Partner Unit Country</target>
      </segment>
    </unit>
    <unit id="sk5uChc" name="u2_financial.partner_unit_name">
      <segment>
        <source>u2_financial.partner_unit_name</source>
        <target>Partner Unit Name</target>
      </segment>
    </unit>
    <unit id="bBtcKZc" name="u2_financial.pricing_method">
      <segment>
        <source>u2_financial.pricing_method</source>
        <target>Pricing Method</target>
      </segment>
    </unit>
    <unit id="ElIc7f1" name="u2_financial.pricing_method.plural">
      <segment>
        <source>u2_financial.pricing_method.plural</source>
        <target>Pricing Methods</target>
      </segment>
    </unit>
    <unit id=".HZRzcg" name="u2_financial.show_hide_value_and_exchange_rates_in_group_and_local_currencies">
      <segment>
        <source>u2_financial.show_hide_value_and_exchange_rates_in_group_and_local_currencies</source>
        <target>Show/hide value and exchange rates in the Group and Local currencies</target>
      </segment>
    </unit>
    <unit id="3dykVXT" name="u2_financial.tax_type">
      <segment>
        <source>u2_financial.tax_type</source>
        <target>Tax Type</target>
      </segment>
    </unit>
    <unit id="zpbob14" name="u2_financial.tax_type.plural">
      <segment>
        <source>u2_financial.tax_type.plural</source>
        <target>Tax Types</target>
      </segment>
    </unit>
    <unit id="49pchYU" name="u2_financial.transaction_type">
      <segment>
        <source>u2_financial.transaction_type</source>
        <target>Transaction Type</target>
      </segment>
    </unit>
    <unit id="k48JshM" name="u2_financial.transaction_type.plural">
      <segment>
        <source>u2_financial.transaction_type.plural</source>
        <target>Transaction Types</target>
      </segment>
    </unit>
    <unit id="qoAO_NT" name="u2_financial.type">
      <segment>
        <source>u2_financial.type</source>
        <target>Type</target>
      </segment>
    </unit>
    <unit id="xS7CqgW" name="u2_financial.unit_country">
      <segment>
        <source>u2_financial.unit_country</source>
        <target>Unit Country</target>
      </segment>
    </unit>
    <unit id="qiSAX_D" name="u2_financial.unit_id">
      <segment>
        <source>u2_financial.unit_id</source>
        <target>Unit ID</target>
      </segment>
    </unit>
    <unit id="Ywd2Qcn" name="u2_igt.module_name">
      <segment>
        <source>u2_igt.module_name</source>
        <target>Intra-Group Transactions</target>
      </segment>
    </unit>
    <unit id="ny0Kv8w" name="u2_structureddocument.add_attachment">
      <segment>
        <source>u2_structureddocument.add_attachment</source>
        <target>Add Attachment</target>
      </segment>
    </unit>
    <unit id="VjP19aG" name="u2_structureddocument.add_section_after">
      <segment>
        <source>u2_structureddocument.add_section_after</source>
        <target>Add Section After</target>
      </segment>
    </unit>
    <unit id="vpnqUXR" name="u2_structureddocument.add_section_before">
      <segment>
        <source>u2_structureddocument.add_section_before</source>
        <target>Add Section Before</target>
      </segment>
    </unit>
    <unit id="W7nluo8" name="u2_structureddocument.add_subsection">
      <segment>
        <source>u2_structureddocument.add_subsection</source>
        <target>Add Subsection</target>
      </segment>
    </unit>
    <unit id="PQ0X4QE" name="u2_structureddocument.after">
      <segment>
        <source>u2_structureddocument.after</source>
        <target>After</target>
      </segment>
    </unit>
    <unit id="_eWDavs" name="u2_structureddocument.allow_edits">
      <segment>
        <source>u2_structureddocument.allow_edits</source>
        <target>Allow Edits</target>
      </segment>
    </unit>
    <unit id="o3QPCsU" name="u2_structureddocument.also_delete_subsections">
      <segment>
        <source>u2_structureddocument.also_delete_subsections</source>
        <target>Also delete subsections.</target>
      </segment>
    </unit>
    <unit id="s0RSiAa" name="u2_structureddocument.attachments">
      <segment>
        <source>u2_structureddocument.attachments</source>
        <target>Attachments</target>
      </segment>
    </unit>
    <unit id="n8vFKGX" name="u2_structureddocument.attachments_archive_is_empty">
      <segment>
        <source>u2_structureddocument.attachments_archive_is_empty</source>
        <target>Attachments archive doesn’t contain any files</target>
      </segment>
    </unit>
    <unit id="wDJVjMT" name="u2_structureddocument.before">
      <segment>
        <source>u2_structureddocument.before</source>
        <target>Before</target>
      </segment>
    </unit>
    <unit id="GFP7Ei0" name="u2_structureddocument.caution">
      <segment>
        <source>u2_structureddocument.caution</source>
        <target>Caution</target>
      </segment>
    </unit>
    <unit id="CuRokRC" name="u2_structureddocument.configure_document_template">
      <segment>
        <source>u2_structureddocument.configure_document_template</source>
        <target>Configure Document Template</target>
      </segment>
    </unit>
    <unit id="T05ybV5" name="u2_structureddocument.configure_template_with_given_template_type">
      <segment>
        <source>u2_structureddocument.configure_template_with_given_template_type</source>
        <target>Configure %document_template_type% Template</target>
      </segment>
    </unit>
    <unit id="JkJlnkG" name="u2_structureddocument.confirm_deletion">
      <segment>
        <source>u2_structureddocument.confirm_deletion</source>
        <target>Confirm deletion</target>
      </segment>
    </unit>
    <unit id="aiClQ89" name="u2_structureddocument.content">
      <segment>
        <source>u2_structureddocument.content</source>
        <target>Content</target>
      </segment>
    </unit>
    <unit id="lVpGw4C" name="u2_structureddocument.created">
      <segment>
        <source>u2_structureddocument.created</source>
        <target>Created</target>
      </segment>
    </unit>
    <unit id="QCcz079" name="u2_structureddocument.delete">
      <segment>
        <source>u2_structureddocument.delete</source>
        <target>Delete</target>
      </segment>
    </unit>
    <unit id=".M4BFCH" name="u2_structureddocument.delete_section.error">
      <segment>
        <source>u2_structureddocument.delete_section.error</source>
        <target>Could not delete section. Please contact system administrator for further information.</target>
      </segment>
    </unit>
    <unit id="QoBQhuR" name="u2_structureddocument.delete_section.warning_attachments_will_also_be_removed">
      <segment>
        <source>u2_structureddocument.delete_section.warning_attachments_will_also_be_removed</source>
        <target>Attachments in deleted sections will also be removed.</target>
      </segment>
    </unit>
    <unit id="boWvKww" name="u2_structureddocument.do_not_require">
      <segment>
        <source>u2_structureddocument.do_not_require</source>
        <target>Do Not Require</target>
      </segment>
    </unit>
    <unit id="GugDnzg" name="u2_structureddocument.document_template">
      <segment>
        <source>u2_structureddocument.document_template</source>
        <target>Document Template</target>
      </segment>
    </unit>
    <unit id="e3SalM9" name="u2_structureddocument.document_template_list">
      <segment>
        <source>u2_structureddocument.document_template_list</source>
        <target>Document Template List</target>
      </segment>
    </unit>
    <unit id="fAa8qLB" name="u2.saving">
      <segment>
        <source>u2.saving</source>
        <target>We are currently saving your data. Please wait a moment.</target>
      </segment>
    </unit>
    <unit id="gJ7Wjvb" name="u2_structureddocument.document_templates">
      <segment>
        <source>u2_structureddocument.document_templates</source>
        <target>Document Templates</target>
      </segment>
    </unit>
    <unit id="1XKyl13" name="u2_structureddocument.documents">
      <segment>
        <source>u2_structureddocument.documents</source>
        <target>Documents</target>
      </segment>
    </unit>
    <unit id="VZf81YE" name="u2_structureddocument.download_all_accessible_attachments.help">
      <segment>
        <source>u2_structureddocument.download_all_accessible_attachments.help</source>
        <target>Download all attachments that you have permission to access</target>
      </segment>
    </unit>
    <unit id="ceo2jSr" name="u2.no_permission_to_perform_action">
      <segment>
        <source>u2.no_permission_to_perform_action</source>
        <target>You are missing permission "%permission%" to perform this action.</target>
      </segment>
    </unit>
    <unit id="O7z1aZp" name="u2_structureddocument.edit">
      <segment>
        <source>u2_structureddocument.edit</source>
        <target>Edit</target>
      </segment>
    </unit>
    <unit id="PM8zi96" name="u2_structureddocument.exclude">
      <segment>
        <source>u2_structureddocument.exclude</source>
        <target>Exclude</target>
      </segment>
    </unit>
    <unit id="1Fff7j5" name="u2_structureddocument.import_new_document_template">
      <segment>
        <source>u2_structureddocument.import_new_document_template</source>
        <target>Import New Document Template</target>
      </segment>
    </unit>
    <unit id="lM98swF" name="u2_structureddocument.include">
      <segment>
        <source>u2_structureddocument.include</source>
        <target>Include</target>
      </segment>
    </unit>
    <unit id="Xi1nel6" name="u2_structureddocument.included">
      <segment>
        <source>u2_structureddocument.included</source>
        <target>Included</target>
      </segment>
    </unit>
    <unit id="KnxUxfW" name="u2_structureddocument.information">
      <segment>
        <source>u2_structureddocument.information</source>
        <target>Information</target>
      </segment>
    </unit>
    <unit id="tuulW68" name="u2_structureddocument.move">
      <segment>
        <source>u2_structureddocument.move</source>
        <target>Move</target>
      </segment>
    </unit>
    <unit id="V8KyXe7" name="u2_structureddocument.move_given_section">
      <segment>
        <source>u2_structureddocument.move_given_section</source>
        <target>Move “%section_name%”</target>
      </segment>
    </unit>
    <unit id=".GfQCUZ" name="u2_structureddocument.move_section.error">
      <segment>
        <source>u2_structureddocument.move_section.error</source>
        <target>Could not move section. Please contact system administrator for further information.</target>
      </segment>
    </unit>
    <unit id="Pw4SFqs" name="u2_structureddocument.not_changed">
      <segment>
        <source>u2_structureddocument.not_changed</source>
        <target>Not changed.</target>
      </segment>
    </unit>
    <unit id="alQHY7a" name="u2_structureddocument.placement">
      <segment>
        <source>u2_structureddocument.placement</source>
        <target>Placement</target>
      </segment>
    </unit>
    <unit id="L46vaDy" name="u2_structureddocument.prevent_edits">
      <segment>
        <source>u2_structureddocument.prevent_edits</source>
        <target>Prevent Edits</target>
      </segment>
    </unit>
    <unit id="JdWuSLS" name="u2_structureddocument.preview">
      <segment>
        <source>u2_structureddocument.preview</source>
        <target>Preview</target>
      </segment>
    </unit>
    <unit id="3mr4M0O" name="u2_structureddocument.preview_given_document_template">
      <segment>
        <source>u2_structureddocument.preview_given_document_template</source>
        <target>Preview “%document_template_name%”</target>
      </segment>
    </unit>
    <unit id="zTNomSJ" name="u2_structureddocument.require">
      <segment>
        <source>u2_structureddocument.require</source>
        <target>Require</target>
      </segment>
    </unit>
    <unit id="SZPsm82" name="u2_structureddocument.required">
      <segment>
        <source>u2_structureddocument.required</source>
        <target>Required</target>
      </segment>
    </unit>
    <unit id="AsR8ifM" name="u2_structureddocument.section">
      <segment>
        <source>u2_structureddocument.section</source>
        <target>Section</target>
      </segment>
    </unit>
    <unit id="c0jDGzv" name="u2_structureddocument.select_a_section">
      <segment>
        <source>u2_structureddocument.select_a_section</source>
        <target>Select a section</target>
      </segment>
    </unit>
    <unit id="qvFbm2m" name="u2_structureddocument.subsection_of">
      <segment>
        <source>u2_structureddocument.subsection_of</source>
        <target>Subsection of</target>
      </segment>
    </unit>
    <unit id="jySRLCx" name="u2_structureddocument.templates">
      <segment>
        <source>u2_structureddocument.templates</source>
        <target>Templates</target>
      </segment>
    </unit>
    <unit id="psbQAN_" name="u2_structureddocument.title">
      <segment>
        <source>u2_structureddocument.title</source>
        <target>Title</target>
      </segment>
    </unit>
    <unit id="8a.IM5N" name="u2_structureddocument.type">
      <segment>
        <source>u2_structureddocument.type</source>
        <target>Type</target>
      </segment>
    </unit>
    <unit id="KSSPFwT" name="u2_structureddocument.updated">
      <segment>
        <source>u2_structureddocument.updated</source>
        <target>Updated</target>
      </segment>
    </unit>
    <unit id="SlMNelF" name="u2_table.advanced">
      <segment>
        <source>u2_table.advanced</source>
        <target>Advanced</target>
      </segment>
    </unit>
    <unit id="ae8iGfH" name="u2_table.all">
      <segment>
        <source>u2_table.all</source>
        <target>All</target>
      </segment>
    </unit>
    <unit id="rqV.XuC" name="u2_table.apply">
      <segment>
        <source>u2_table.apply</source>
        <target>Apply</target>
      </segment>
    </unit>
    <unit id="92oW_Y0" name="u2_table.apply_changes">
      <segment>
        <source>u2_table.apply_changes</source>
        <target>Apply Changes</target>
      </segment>
    </unit>
    <unit id="krIxKuG" name="u2_table.available_fields">
      <segment>
        <source>u2_table.available_fields</source>
        <target>Available fields</target>
      </segment>
    </unit>
    <unit id="GZW14P3" name="u2_table.available_functions">
      <segment>
        <source>u2_table.available_functions</source>
        <target>Available functions</target>
      </segment>
    </unit>
    <unit id="Jv6zQ8Q" name="u2_table.basic">
      <segment>
        <source>u2_table.basic</source>
        <target>Basic</target>
      </segment>
    </unit>
    <unit id="8ldai3l" name="u2_table.cancel">
      <segment>
        <source>u2_table.cancel</source>
        <target>Cancel</target>
      </segment>
    </unit>
    <unit id="3m5XUB5" name="u2_table.columns">
      <segment>
        <source>u2_table.columns</source>
        <target>Columns</target>
      </segment>
    </unit>
    <unit id="eT2XBMb" name="u2_table.default">
      <segment>
        <source>u2_table.default</source>
        <target>Default</target>
      </segment>
    </unit>
    <unit id="J7yfLku" name="u2_table.deselect_all">
      <segment>
        <source>u2_table.deselect_all</source>
        <target>Deselect All</target>
      </segment>
    </unit>
    <unit id="OTjZraJ" name="u2_table.deselect_all_visible">
      <segment>
        <source>u2_table.deselect_all_visible</source>
        <target>Deselect All Visible</target>
      </segment>
    </unit>
    <unit id="znwt3m4" name="u2_table.false">
      <segment>
        <source>u2_table.false</source>
        <target>False</target>
      </segment>
    </unit>
    <unit id="LutUySt" name="u2_table.filters">
      <segment>
        <source>u2_table.filters</source>
        <target>Filters</target>
      </segment>
    </unit>
    <unit id="uxi02BM" name="u2_table.next_page">
      <segment>
        <source>u2_table.next_page</source>
        <target>Next Page</target>
      </segment>
    </unit>
    <unit id="fN9vaOM" name="u2_table.no_results_that_match_search">
      <segment>
        <source>u2_table.no_results_that_match_search</source>
        <target>No results found</target>
      </segment>
    </unit>
    <unit id="PsveZ_g" name="u2_table.none">
      <segment>
        <source>u2_table.none</source>
        <target>None</target>
      </segment>
    </unit>
    <unit id="z5im_9B" name="u2_table.previous_page">
      <segment>
        <source>u2_table.previous_page</source>
        <target>Previous Page</target>
      </segment>
    </unit>
    <unit id="SeNebdf" name="u2_table.reset">
      <segment>
        <source>u2_table.reset</source>
        <target>Reset</target>
      </segment>
    </unit>
    <unit id="x1Yakm7" name="u2_table.search_results_empty.help">
      <segment>
        <source>u2_table.search_results_empty.help</source>
        <target>Try changing your query or create a new record to get going</target>
      </segment>
    </unit>
    <unit id="RnUF44n" name="u2_table.select">
      <segment>
        <source>u2_table.select</source>
        <target>Select:</target>
      </segment>
    </unit>
    <unit id="pKALFUc" name="u2_table.select_all_visible">
      <segment>
        <source>u2_table.select_all_visible</source>
        <target>Select All Visible</target>
      </segment>
    </unit>
    <unit id="oWSIFfP" name="u2_table.show">
      <segment>
        <source>u2_table.show</source>
        <target>Show:</target>
      </segment>
    </unit>
    <unit id="NpbnieZ" name="u2_table.switch_to_advanced_filtering">
      <segment>
        <source>u2_table.switch_to_advanced_filtering</source>
        <target>Switch to advanced filtering using UQL</target>
      </segment>
    </unit>
    <unit id="0JYogo_" name="u2_table.switch_to_basic_filtering">
      <segment>
        <source>u2_table.switch_to_basic_filtering</source>
        <target>Switch to basic filtering</target>
      </segment>
    </unit>
    <unit id="EG1wukT" name="u2_table.table_columns">
      <segment>
        <source>u2_table.table_columns</source>
        <target>Table Columns</target>
      </segment>
    </unit>
    <unit id="voamBJq" name="u2_table.true">
      <segment>
        <source>u2_table.true</source>
        <target>True</target>
      </segment>
    </unit>
    <unit id="3WqR_BI" name="u2_table.uql_filtered_table.filter_too_complex_to_display_in_basic_mode">
      <segment>
        <source>u2_table.uql_filtered_table.filter_too_complex_to_display_in_basic_mode</source>
        <target>The current filter is too complex to display in basic mode. Please reset or change your filter.</target>
      </segment>
    </unit>
    <unit id="OdG23Y0" name="u2_table.uql_help">
      <segment>
        <source>u2_table.uql_help</source>
        <target>UQL Help</target>
      </segment>
    </unit>
    <unit id="Bw27xDT" name="u2_tam.accrued">
      <segment>
        <source>u2_tam.accrued</source>
        <target>Accrued</target>
      </segment>
    </unit>
    <unit id="rAnrHCH" name="u2_tam.accrued_boy">
      <segment>
        <source>u2_tam.accrued_boy</source>
        <target>Accrued BOY</target>
      </segment>
    </unit>
    <unit id="rY9XaM." name="u2_tam.accrued_eoy">
      <segment>
        <source>u2_tam.accrued_eoy</source>
        <target>Accrued EOY</target>
      </segment>
    </unit>
    <unit id="qt5Uu3." name="u2_tam.addition">
      <segment>
        <source>u2_tam.addition</source>
        <target>Addition (+)</target>
      </segment>
    </unit>
    <unit id="gDb8mx9" name="u2_tam.additions">
      <segment>
        <source>u2_tam.additions</source>
        <target>Additions (+)</target>
      </segment>
    </unit>
    <unit id="Mhxxl7c" name="u2_tam.advice_type">
      <segment>
        <source>u2_tam.advice_type</source>
        <target>Advice Type</target>
      </segment>
    </unit>
    <unit id="NKqyYhP" name="u2_tam.advice_type.plural">
      <segment>
        <source>u2_tam.advice_type.plural</source>
        <target>Advice Types</target>
      </segment>
    </unit>
    <unit id="K91BoMl" name="u2_tam.amount">
      <segment>
        <source>u2_tam.amount</source>
        <target>Amount</target>
      </segment>
    </unit>
    <unit id="Ehl2wbG" name="u2_tam.amounts">
      <segment>
        <source>u2_tam.amounts</source>
        <target>Amounts</target>
      </segment>
    </unit>
    <unit id="oNASpSJ" name="u2_tam.base">
      <segment>
        <source>u2_tam.base</source>
        <target>Base</target>
      </segment>
    </unit>
    <unit id="2G4sMeC" name="u2_tam.beginning_of_year">
      <segment>
        <source>u2_tam.beginning_of_year</source>
        <target>Beginning of Year</target>
      </segment>
    </unit>
    <unit id="iuABAEt" name="u2_tam.beginning_of_year.short">
      <segment>
        <source>u2_tam.beginning_of_year.short</source>
        <target>Beg. of Year</target>
      </segment>
    </unit>
    <unit id="kXSCZRQ" name="u2_tam.cash_income_tax">
      <segment>
        <source>u2_tam.cash_income_tax</source>
        <target>Cash Income Tax</target>
      </segment>
    </unit>
    <unit id="uIwWiT7" name="u2_tam.compensation_of_tax_credits_accrued_as_dta">
      <segment>
        <source>u2_tam.compensation_of_tax_credits_accrued_as_dta</source>
        <target>Compensation of tax credits accrued as DTA</target>
      </segment>
    </unit>
    <unit id="qgomkeG" name="u2_tam.compensation_of_tax_credits_not_accrued_as_dta">
      <segment>
        <source>u2_tam.compensation_of_tax_credits_not_accrued_as_dta</source>
        <target>Compensation of tax credits not accrued as DTA</target>
      </segment>
    </unit>
    <unit id="0NhAYHN" name="u2_tam.compensation_of_tax_losses_accrued_as_dta">
      <segment>
        <source>u2_tam.compensation_of_tax_losses_accrued_as_dta</source>
        <target>Compensation of tax losses accrued as DTA</target>
      </segment>
    </unit>
    <unit id="qKYTQUv" name="u2_tam.compensation_of_tax_losses_not_accrued_as_dta">
      <segment>
        <source>u2_tam.compensation_of_tax_losses_not_accrued_as_dta</source>
        <target>Compensation of tax losses not accrued as DTA</target>
      </segment>
    </unit>
    <unit id="ICay4Ju" name="u2_tam.compliance_fee">
      <segment>
        <source>u2_tam.compliance_fee</source>
        <target>Compliance Fee</target>
      </segment>
    </unit>
    <unit id="3_SEt0K" name="u2_tam.compliance_fee.short">
      <segment>
        <source>u2_tam.compliance_fee.short</source>
        <target>Compl. Fee</target>
      </segment>
    </unit>
    <unit id="NHiSY1H" name="u2_tam.compliance_fees">
      <segment>
        <source>u2_tam.compliance_fees</source>
        <target>Compliance fees</target>
      </segment>
    </unit>
    <unit id="l3ZkPpK" name="u2_tam.compliance_tax_advisor">
      <segment>
        <source>u2_tam.compliance_tax_advisor</source>
        <target>Compliance Tax Advisor</target>
      </segment>
    </unit>
    <unit id="Uhk8SWs" name="u2_tam.compliance_tax_advisor.short">
      <segment>
        <source>u2_tam.compliance_tax_advisor.short</source>
        <target>Compl. Tax Advisor</target>
      </segment>
    </unit>
    <unit id="d__QtHz" name="u2_tam.consumption">
      <segment>
        <source>u2_tam.consumption</source>
        <target>Consumption (-)</target>
      </segment>
    </unit>
    <unit id="MJe92vs" name="u2_tam.correction">
      <segment>
        <source>u2_tam.correction</source>
        <target>Correction (+/-)</target>
      </segment>
    </unit>
    <unit id="Ka1DUe9" name="u2_tam.credit_type">
      <segment>
        <source>u2_tam.credit_type</source>
        <target>Credit Type</target>
      </segment>
    </unit>
    <unit id="zEKcPJ9" name="u2_tam.currency">
      <segment>
        <source>u2_tam.currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="VVd2bZX" name="u2_tam.details">
      <segment>
        <source>u2_tam.details</source>
        <target>Details</target>
      </segment>
    </unit>
    <unit id="6BOBM5u" name="u2_tam.details_of_perm_differences">
      <segment>
        <source>u2_tam.details_of_perm_differences</source>
        <target>Details of perm. differences</target>
      </segment>
    </unit>
    <unit id="tTkjpWO" name="u2_tam.details_of_temp_differences">
      <segment>
        <source>u2_tam.details_of_temp_differences</source>
        <target>Details of temp. differences</target>
      </segment>
    </unit>
    <unit id="HPXnRAV" name="u2_tam.documentation_available">
      <segment>
        <source>u2_tam.documentation_available</source>
        <target>Documentation Available</target>
      </segment>
    </unit>
    <unit id="_3Ct5Iz" name="u2_tam.documentation_available.short">
      <segment>
        <source>u2_tam.documentation_available.short</source>
        <target>Doc. Available</target>
      </segment>
    </unit>
    <unit id="15VHGBr" name="u2_tam.documentation_required">
      <segment>
        <source>u2_tam.documentation_required</source>
        <target>Documentation Required</target>
      </segment>
    </unit>
    <unit id="ACG7WUT" name="u2_tam.documentation_required.short">
      <segment>
        <source>u2_tam.documentation_required.short</source>
        <target>Doc. Required</target>
      </segment>
    </unit>
    <unit id="5V_6pr5" name="u2_tam.end_of_year">
      <segment>
        <source>u2_tam.end_of_year</source>
        <target>End of Year</target>
      </segment>
    </unit>
    <unit id="bZx0Q52" name="u2_tam.etr">
      <segment>
        <source>u2_tam.etr</source>
        <target>ETR</target>
      </segment>
    </unit>
    <unit id="qHxNnUm" name="u2_tam.etr_ifrs">
      <segment>
        <source>u2_tam.etr_ifrs</source>
        <target>ETR (IFRS)</target>
      </segment>
    </unit>
    <unit id="DAyDAFk" name="u2_tam.gross_risk">
      <segment>
        <source>u2_tam.gross_risk</source>
        <target>Gross Risk</target>
      </segment>
    </unit>
    <unit id="CM8z9h0" name="u2_tam.gross_risk_boy">
      <segment>
        <source>u2_tam.gross_risk_boy</source>
        <target>Gross Risk BOY</target>
      </segment>
    </unit>
    <unit id="6YuQPr7" name="u2_tam.gross_risk_eoy">
      <segment>
        <source>u2_tam.gross_risk_eoy</source>
        <target>Gross Risk EOY</target>
      </segment>
    </unit>
    <unit id="BETbB8B" name="u2_tam.identified_by_tax_admin">
      <segment>
        <source>u2_tam.identified_by_tax_admin</source>
        <target>Identified by Tax Admin (+/-)</target>
      </segment>
    </unit>
    <unit id="TTtWHyh" name="u2_tam.identified_by_tax_admin.short">
      <segment>
        <source>u2_tam.identified_by_tax_admin.short</source>
        <target>Ident. by Tax Adm.</target>
      </segment>
    </unit>
    <unit id="ovCyt0w" name="u2_tam.income_tax">
      <segment>
        <source>u2_tam.income_tax</source>
        <target>Income Tax</target>
      </segment>
    </unit>
    <unit id="E0v6d5u" name="u2_tam.income_tax_planning">
      <segment>
        <source>u2_tam.income_tax_planning</source>
        <target>Income Tax Planning</target>
      </segment>
    </unit>
    <unit id="JaVeTAc" name="u2_tam.income_tax_planning.plural">
      <segment>
        <source>u2_tam.income_tax_planning.plural</source>
        <target>Income Tax Planning</target>
      </segment>
    </unit>
    <unit id="vveIRlQ" name="u2_tam.less">
      <segment>
        <source>u2_tam.less</source>
        <target>Less (-)</target>
      </segment>
    </unit>
    <unit id="FVoeZb9" name="u2_tam.loss_carry_forward">
      <segment>
        <source>u2_tam.loss_carry_forward</source>
        <target>Loss Carry Forward</target>
      </segment>
    </unit>
    <unit id="yfXYndf" name="u2_tam.loss_carry_forward.plural">
      <segment>
        <source>u2_tam.loss_carry_forward.plural</source>
        <target>Loss Carry Forwards</target>
      </segment>
    </unit>
    <unit id="JWfWTlO" name="u2_tam.loss_restriction">
      <segment>
        <source>u2_tam.loss_restriction</source>
        <target>Loss Restriction</target>
      </segment>
    </unit>
    <unit id="PznsCWU" name="u2_tam.loss_restriction.plural">
      <segment>
        <source>u2_tam.loss_restriction.plural</source>
        <target>Loss Restrictions</target>
      </segment>
    </unit>
    <unit id="UmiGIFe" name="u2_tam.loss_type">
      <segment>
        <source>u2_tam.loss_type</source>
        <target>Loss Type</target>
      </segment>
    </unit>
    <unit id="zWlffOt" name="u2_tam.loss_type.plural">
      <segment>
        <source>u2_tam.loss_type.plural</source>
        <target>Loss Types</target>
      </segment>
    </unit>
    <unit id="37UaBSJ" name="u2_tam.module_name">
      <segment>
        <source>u2_tam.module_name</source>
        <target>Tax Assessment Management</target>
      </segment>
    </unit>
    <unit id="wCvcAal" name="u2_tam.module_name.short">
      <segment>
        <source>u2_tam.module_name.short</source>
        <target>TAM</target>
      </segment>
    </unit>
    <unit id="jW4maUI" name="u2_tam.p_l_effect_cy">
      <segment>
        <source>u2_tam.p_l_effect_cy</source>
        <target>P/L Effect CY</target>
      </segment>
    </unit>
    <unit id="9IPVWHs" name="u2_tam.parties">
      <segment>
        <source>u2_tam.parties</source>
        <target>Parties</target>
      </segment>
    </unit>
    <unit id="fyjc6rR" name="u2_tam.partner_unit_name">
      <segment>
        <source>u2_tam.partner_unit_name</source>
        <target>Partner Unit Name</target>
      </segment>
    </unit>
    <unit id="RtJuSR2" name="u2_tam.perm_differences">
      <segment>
        <source>u2_tam.perm_differences</source>
        <target>Perm. Differences</target>
      </segment>
    </unit>
    <unit id=".kUujI4" name="u2_tam.permanent">
      <segment>
        <source>u2_tam.permanent</source>
        <target>Permanent</target>
      </segment>
    </unit>
    <unit id="R3BqHra" name="u2_tam.planning_period">
      <segment>
        <source>u2_tam.planning_period</source>
        <target>Planning Period</target>
      </segment>
    </unit>
    <unit id="UXuMJ2J" name="u2_tam.potential_tax_liabilities">
      <segment>
        <source>u2_tam.potential_tax_liabilities</source>
        <target>Potential Tax Liabilities</target>
      </segment>
    </unit>
    <unit id="vzJNhYq" name="u2_tam.potential_tax_liabilities.short">
      <segment>
        <source>u2_tam.potential_tax_liabilities.short</source>
        <target>Pot. Tax Liab.</target>
      </segment>
    </unit>
    <unit id="w4eaUCM" name="u2_tam.pricing_method">
      <segment>
        <source>u2_tam.pricing_method</source>
        <target>Pricing Method</target>
      </segment>
    </unit>
    <unit id="RZS6ne5" name="u2_tam.profit_before_tax">
      <segment>
        <source>u2_tam.profit_before_tax</source>
        <target>Profit Before Tax</target>
      </segment>
    </unit>
    <unit id="S8rt4wF" name="u2_tam.profit_before_tax.short">
      <segment>
        <source>u2_tam.profit_before_tax.short</source>
        <target>Profit b. Tax</target>
      </segment>
    </unit>
    <unit id="hnEl0gF" name="u2_tam.profit_before_tax_calculated">
      <segment>
        <source>u2_tam.profit_before_tax_calculated</source>
        <target>Profit Before Tax Calculated</target>
      </segment>
    </unit>
    <unit id=".Tpn4CT" name="u2_tam.profit_before_tax_calculated.short">
      <segment>
        <source>u2_tam.profit_before_tax_calculated.short</source>
        <target>Profit b. Tax cal.</target>
      </segment>
    </unit>
    <unit id="WdYX0xh" name="u2_tam.reason">
      <segment>
        <source>u2_tam.reason</source>
        <target>Reason</target>
      </segment>
    </unit>
    <unit id="i5p_udT" name="u2_tam.restriction_reason">
      <segment>
        <source>u2_tam.restriction_reason</source>
        <target>Restriction Reason</target>
      </segment>
    </unit>
    <unit id="T7vCytk" name="u2_tam.restriction_reason.plural">
      <segment>
        <source>u2_tam.restriction_reason.plural</source>
        <target>Restriction Reasons</target>
      </segment>
    </unit>
    <unit id="8u0E6bp" name="u2_tam.restrictions">
      <segment>
        <source>u2_tam.restrictions</source>
        <target>Restrictions</target>
      </segment>
    </unit>
    <unit id="_15avuB" name="u2_tam.risk_probability">
      <segment>
        <source>u2_tam.risk_probability</source>
        <target>Risk Probability</target>
      </segment>
    </unit>
    <unit id="Ht9vt9f" name="u2_tam.risk_probability.short">
      <segment>
        <source>u2_tam.risk_probability.short</source>
        <target>Risk Prob.</target>
      </segment>
    </unit>
    <unit id="UGk4yjh" name="u2_tam.risk_type">
      <segment>
        <source>u2_tam.risk_type</source>
        <target>Risk Type</target>
      </segment>
    </unit>
    <unit id="RaYlOBb" name="u2_tam.risk_type.plural">
      <segment>
        <source>u2_tam.risk_type.plural</source>
        <target>Risk Types</target>
      </segment>
    </unit>
    <unit id="PnCIhe4" name="u2_tam.special_advisory_fees">
      <segment>
        <source>u2_tam.special_advisory_fees</source>
        <target>Special Advisory Fees</target>
      </segment>
    </unit>
    <unit id="d6rDqdB" name="u2_tam.special_advisory_fees.short">
      <segment>
        <source>u2_tam.special_advisory_fees.short</source>
        <target>Spec. Adv. Fees</target>
      </segment>
    </unit>
    <unit id="m80CjqR" name="u2_tam.specification">
      <segment>
        <source>u2_tam.specification</source>
        <target>Specification</target>
      </segment>
    </unit>
    <unit id="S6NoXOb" name="u2_tam.specification.plural">
      <segment>
        <source>u2_tam.specification.plural</source>
        <target>Specifications</target>
      </segment>
    </unit>
    <unit id="ZyYlqlA" name="u2_tam.tax_advisor">
      <segment>
        <source>u2_tam.tax_advisor</source>
        <target>Tax Advisor</target>
      </segment>
    </unit>
    <unit id="Bz.vEfK" name="u2_tam.tax_assessment_status">
      <segment>
        <source>u2_tam.tax_assessment_status</source>
        <target>Tax Assessment Status</target>
      </segment>
    </unit>
    <unit id="k7Agnaq" name="u2_tam.tax_assessment_status.plural">
      <segment>
        <source>u2_tam.tax_assessment_status.plural</source>
        <target>Tax Assessment Status</target>
      </segment>
    </unit>
    <unit id="3T0uOow" name="u2_tam.tax_audit_risk">
      <segment>
        <source>u2_tam.tax_audit_risk</source>
        <target>Tax Audit Risk</target>
      </segment>
    </unit>
    <unit id="gt7oekp" name="u2_tam.tax_audit_risk.plural">
      <segment>
        <source>u2_tam.tax_audit_risk.plural</source>
        <target>Tax Audit Risks</target>
      </segment>
    </unit>
    <unit id="32J5V.j" name="u2_tam.tax_base">
      <segment>
        <source>u2_tam.tax_base</source>
        <target>Tax Base</target>
      </segment>
    </unit>
    <unit id="3j4ifBt" name="u2_tam.tax_consulting_fee">
      <segment>
        <source>u2_tam.tax_consulting_fee</source>
        <target>Tax Consulting Fee</target>
      </segment>
    </unit>
    <unit id="ldEUsc5" name="u2_tam.tax_consulting_fee.plural">
      <segment>
        <source>u2_tam.tax_consulting_fee.plural</source>
        <target>Tax Consulting Fees</target>
      </segment>
    </unit>
    <unit id="YJm0RPv" name="u2_tam.tax_credit">
      <segment>
        <source>u2_tam.tax_credit</source>
        <target>Tax Credit</target>
      </segment>
    </unit>
    <unit id="pzctMT1" name="u2_tam.tax_credit.plural">
      <segment>
        <source>u2_tam.tax_credit.plural</source>
        <target>Tax Credits</target>
      </segment>
    </unit>
    <unit id="pVRTiZM" name="u2_tam.tax_credit_type">
      <segment>
        <source>u2_tam.tax_credit_type</source>
        <target>Tax Credit Type</target>
      </segment>
    </unit>
    <unit id="G5BrDZb" name="u2_tam.tax_credit_type.plural">
      <segment>
        <source>u2_tam.tax_credit_type.plural</source>
        <target>Tax Credit Types</target>
      </segment>
    </unit>
    <unit id="94H55CQ" name="u2_tam.tax_litigation">
      <segment>
        <source>u2_tam.tax_litigation</source>
        <target>Tax Litigation</target>
      </segment>
    </unit>
    <unit id="VFn3fhy" name="u2_tam.tax_litigation.plural">
      <segment>
        <source>u2_tam.tax_litigation.plural</source>
        <target>Tax Litigations</target>
      </segment>
    </unit>
    <unit id="GJttYDu" name="u2_tam.tax_month">
      <segment>
        <source>u2_tam.tax_month</source>
        <target>Tax. Month</target>
      </segment>
    </unit>
    <unit id="t05qT33" name="u2_tam.tax_rate">
      <segment>
        <source>u2_tam.tax_rate</source>
        <target>Tax Rate</target>
      </segment>
    </unit>
    <unit id="tma31py" name="u2_tam.tax_rate.plural">
      <segment>
        <source>u2_tam.tax_rate.plural</source>
        <target>Tax Rates</target>
      </segment>
    </unit>
    <unit id="lwIfXCo" name="u2_tam.tax_relevant_restriction">
      <segment>
        <source>u2_tam.tax_relevant_restriction</source>
        <target>Tax Relevant Restriction</target>
      </segment>
    </unit>
    <unit id="SidAvTi" name="u2_tam.tax_relevant_restriction.plural">
      <segment>
        <source>u2_tam.tax_relevant_restriction.plural</source>
        <target>Tax Relevant Restrictions</target>
      </segment>
    </unit>
    <unit id="g3kkt_w" name="u2_tam.tax_type">
      <segment>
        <source>u2_tam.tax_type</source>
        <target>Tax Type</target>
      </segment>
    </unit>
    <unit id="88AlxPw" name="u2_tam.taxable_income">
      <segment>
        <source>u2_tam.taxable_income</source>
        <target>Taxable Income</target>
      </segment>
    </unit>
    <unit id="DBHEapy" name="u2_tam.taxation_month">
      <segment>
        <source>u2_tam.taxation_month</source>
        <target>Taxation Month</target>
      </segment>
    </unit>
    <unit id="H72ZC5v" name="u2_tam.taxation_year">
      <segment>
        <source>u2_tam.taxation_year</source>
        <target>Taxation Year</target>
      </segment>
    </unit>
    <unit id="9H_0UN5" name="u2_tam.temp_differences">
      <segment>
        <source>u2_tam.temp_differences</source>
        <target>Temp. Differences</target>
      </segment>
    </unit>
    <unit id="3zE5emW" name="u2_tam.temporary">
      <segment>
        <source>u2_tam.temporary</source>
        <target>Temporary</target>
      </segment>
    </unit>
    <unit id="5yukGy1" name="u2_tam.temporary_permanent_risk">
      <segment>
        <source>u2_tam.temporary_permanent_risk</source>
        <target>Temporary/Permanent Risk</target>
      </segment>
    </unit>
    <unit id="jcIOoLh" name="u2_tam.transaction_type">
      <segment>
        <source>u2_tam.transaction_type</source>
        <target>Transaction Type</target>
      </segment>
    </unit>
    <unit id="7lDezRu" name="u2_tam.transfer_pricing">
      <segment>
        <source>u2_tam.transfer_pricing</source>
        <target>Transfer Pricing</target>
      </segment>
    </unit>
    <unit id="U5th30W" name="u2_tam.transfer_pricing.plural">
      <segment>
        <source>u2_tam.transfer_pricing.plural</source>
        <target>Transfer Pricing</target>
      </segment>
    </unit>
    <unit id="2RJ7UMZ" name="u2_tam.type_of_advice">
      <segment>
        <source>u2_tam.type_of_advice</source>
        <target>Type of Advice</target>
      </segment>
    </unit>
    <unit id="nFT2793" name="u2_tam.type_of_risk">
      <segment>
        <source>u2_tam.type_of_risk</source>
        <target>Type of Risk</target>
      </segment>
    </unit>
    <unit id="V8XJ70V" name="u2_tam.valid_from">
      <segment>
        <source>u2_tam.valid_from</source>
        <target>Valid From</target>
      </segment>
    </unit>
    <unit id="JF8M32f" name="u2_tam.valid_to">
      <segment>
        <source>u2_tam.valid_to</source>
        <target>Valid To</target>
      </segment>
    </unit>
    <unit id="mLs_Lrm" name="u2_tam.withholding_tax_payable">
      <segment>
        <source>u2_tam.withholding_tax_payable</source>
        <target>Withholding Tax Payable</target>
      </segment>
    </unit>
    <unit id="rXytOrX" name="u2_tam.withholding_tax_payable.short">
      <segment>
        <source>u2_tam.withholding_tax_payable.short</source>
        <target>With. Tax Pay.</target>
      </segment>
    </unit>
    <unit id="_j8rvfM" name="u2_tcm.assessment_type">
      <segment>
        <source>u2_tcm.assessment_type</source>
        <target>Assessment Type</target>
      </segment>
    </unit>
    <unit id="bPskQX9" name="u2_tcm.assessment_type.plural">
      <segment>
        <source>u2_tcm.assessment_type.plural</source>
        <target>Assessment Types</target>
      </segment>
    </unit>
    <unit id="GWtwkgB" name="u2_tcm.currency">
      <segment>
        <source>u2_tcm.currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="Zt1c9Fh" name="u2_tcm.date_received">
      <segment>
        <source>u2_tcm.date_received</source>
        <target>Date Received</target>
      </segment>
    </unit>
    <unit id="gcMivaP" name="u2_tcm.deadline_type">
      <segment>
        <source>u2_tcm.deadline_type</source>
        <target>Deadline Type</target>
      </segment>
    </unit>
    <unit id="aRx6hw3" name="u2_tcm.declaration_type">
      <segment>
        <source>u2_tcm.declaration_type</source>
        <target>Declaration Type</target>
      </segment>
    </unit>
    <unit id="unkYlvp" name="u2_tcm.declaration_type.plural">
      <segment>
        <source>u2_tcm.declaration_type.plural</source>
        <target>Declaration Types</target>
      </segment>
    </unit>
    <unit id="Q3tJWP4" name="u2_tcm.details">
      <segment>
        <source>u2_tcm.details</source>
        <target>Details</target>
      </segment>
    </unit>
    <unit id="_eRpZQv" name="u2_tcm.document_date">
      <segment>
        <source>u2_tcm.document_date</source>
        <target>Document Date</target>
      </segment>
    </unit>
    <unit id="qaxJ.I." name="u2.due_date">
      <segment>
        <source>u2.due_date</source>
        <target>Due Date</target>
      </segment>
    </unit>
    <unit id="0UU7_z9" name="u2_tcm.filing_date">
      <segment>
        <source>u2_tcm.filing_date</source>
        <target>Filing Date</target>
      </segment>
    </unit>
    <unit id="69UOu4f" name="u2_tcm.module_name">
      <segment>
        <source>u2_tcm.module_name</source>
        <target>Tax Compliance Monitor</target>
      </segment>
    </unit>
    <unit id="N2KPhPA" name="u2_tcm.module_name.short">
      <segment>
        <source>u2_tcm.module_name.short</source>
        <target>TCM</target>
      </segment>
    </unit>
    <unit id="pjasR8A" name="u2_tcm.other_deadline">
      <segment>
        <source>u2_tcm.other_deadline</source>
        <target>Other Deadline</target>
      </segment>
    </unit>
    <unit id="coICQuF" name="u2_tcm.other_deadline.plural">
      <segment>
        <source>u2_tcm.other_deadline.plural</source>
        <target>Other Deadlines</target>
      </segment>
    </unit>
    <unit id="F0IwUey" name="u2_tcm.penalty">
      <segment>
        <source>u2_tcm.penalty</source>
        <target>Penalty</target>
      </segment>
    </unit>
    <unit id="TsEXbwv" name="u2_tcm.penalty_for_late_filing">
      <segment>
        <source>u2_tcm.penalty_for_late_filing</source>
        <target>Penalty for Late Filing</target>
      </segment>
    </unit>
    <unit id="AiynA7q" name="u2_tcm.tax_assessment_monitor">
      <segment>
        <source>u2_tcm.tax_assessment_monitor</source>
        <target>Tax Assessment Monitor</target>
      </segment>
    </unit>
    <unit id="QvNuRh_" name="u2_tcm.tax_assessment_monitor.plural">
      <segment>
        <source>u2_tcm.tax_assessment_monitor.plural</source>
        <target>Tax Assessment Monitor</target>
      </segment>
    </unit>
    <unit id="V3ud6a6" name="u2_tcm.tax_authority_audit_objection">
      <segment>
        <source>u2_tcm.tax_authority_audit_objection</source>
        <target>Tax Authority / Audit Objection</target>
      </segment>
    </unit>
    <unit id="o2ci.KN" name="u2_tcm.tax_authority_audit_objection.plural">
      <segment>
        <source>u2_tcm.tax_authority_audit_objection.plural</source>
        <target>Tax Authority / Audit Objections</target>
      </segment>
    </unit>
    <unit id="2bwYQEf" name="u2_tcm.tax_filing_monitor">
      <segment>
        <source>u2_tcm.tax_filing_monitor</source>
        <target>Tax Filing Monitor</target>
      </segment>
    </unit>
    <unit id="0wVdfhI" name="u2_tcm.tax_filing_monitor.plural">
      <segment>
        <source>u2_tcm.tax_filing_monitor.plural</source>
        <target>Tax Filing Monitor</target>
      </segment>
    </unit>
    <unit id="UHSgUc4" name="u2_tcm.tax_month">
      <segment>
        <source>u2_tcm.tax_month</source>
        <target>Tax. Month</target>
      </segment>
    </unit>
    <unit id="xnkZYqb" name="u2_tcm.tax_type">
      <segment>
        <source>u2_tcm.tax_type</source>
        <target>Tax Type</target>
      </segment>
    </unit>
    <unit id="FekUFBq" name="u2_tcm.taxation_month">
      <segment>
        <source>u2_tcm.taxation_month</source>
        <target>Taxation Month</target>
      </segment>
    </unit>
    <unit id="x6Bk.Ge" name="u2_tcm.taxation_year">
      <segment>
        <source>u2_tcm.taxation_year</source>
        <target>Taxation Year</target>
      </segment>
    </unit>
    <unit id="7mOwno9" name="u2_tcm.type_of_assessment">
      <segment>
        <source>u2_tcm.type_of_assessment</source>
        <target>Type of Assessment</target>
      </segment>
    </unit>
    <unit id="Nikhu7c" name="u2_tcm.type_of_declaration">
      <segment>
        <source>u2_tcm.type_of_declaration</source>
        <target>Type of Declaration</target>
      </segment>
    </unit>
    <unit id="fVrkIe8" name="u2_tpm.accumulated_earnings_value">
      <segment>
        <source>u2_tpm.accumulated_earnings_value</source>
        <target>Accumulated Earnings</target>
      </segment>
    </unit>
    <unit id="p.xzCXG" name="u2_tpm.accumulated_earnings_value_base_value">
      <segment>
        <source>u2_tpm.accumulated_earnings_value_base_value</source>
        <target>Accumulated Earnings</target>
      </segment>
    </unit>
    <unit id="I5x4Pij" name="u2_tpm.accumulated_earnings_value_base_value.short">
      <segment>
        <source>u2_tpm.accumulated_earnings_value_base_value.short</source>
        <target>Acc. Earn.</target>
      </segment>
    </unit>
    <unit id="SWnALHK" name="u2_tpm.accumulated_earnings_value_group_value">
      <segment>
        <source>u2_tpm.accumulated_earnings_value_group_value</source>
        <target>Accumulated Earnings [Group Value]</target>
      </segment>
    </unit>
    <unit id="9C58G0b" name="u2_tpm.accumulated_earnings_value_group_value.short">
      <segment>
        <source>u2_tpm.accumulated_earnings_value_group_value.short</source>
        <target>Acc. Earn. [Group Value]</target>
      </segment>
    </unit>
    <unit id="2lzdozH" name="u2_tpm.accumulated_earnings_value_local_value">
      <segment>
        <source>u2_tpm.accumulated_earnings_value_local_value</source>
        <target>Accumulated Earnings [Local Value]</target>
      </segment>
    </unit>
    <unit id="KUfBZcW" name="u2_tpm.accumulated_earnings_value_local_value.short">
      <segment>
        <source>u2_tpm.accumulated_earnings_value_local_value.short</source>
        <target>Acc. Earn. [Local Value]</target>
      </segment>
    </unit>
    <unit id="uzawJVe" name="u2_tpm.add_first_section">
      <segment>
        <source>u2_tpm.add_first_section</source>
        <target>Add First Section</target>
      </segment>
    </unit>
    <unit id="0l8PHlR" name="u2_tpm.all_transaction_types">
      <segment>
        <source>u2_tpm.all_transaction_types</source>
        <target>All Transaction Types</target>
      </segment>
    </unit>
    <unit id="m93q94R" name="u2_tpm.arms_length">
      <segment>
        <source>u2_tpm.arms_length</source>
        <target>Arm's Length</target>
      </segment>
    </unit>
    <unit id="zsLDROU" name="u2_tpm.arms_length.help">
      <segment>
        <source>u2_tpm.arms_length.help</source>
        <target>Confirm whether the transaction is at arm's length</target>
      </segment>
    </unit>
    <unit id="oVoVK.R" name="u2_tpm.associated_units">
      <segment>
        <source>u2_tpm.associated_units</source>
        <target>Associated Units</target>
      </segment>
    </unit>
    <unit id="URJKlBD" name="u2_tpm.base">
      <segment>
        <source>u2_tpm.base</source>
        <target>Base</target>
      </segment>
    </unit>
    <unit id="BSwHZiH" name="u2_tpm.base_currency">
      <segment>
        <source>u2_tpm.base_currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="zxHhBHn" name="u2_tpm.base_currency.short">
      <segment>
        <source>u2_tpm.base_currency.short</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="TzYq2iT" name="u2_tpm.base_template">
      <segment>
        <source>u2_tpm.base_template</source>
        <target>Base Template</target>
      </segment>
    </unit>
    <unit id="31uc_9O" name="u2_tpm.billing_type">
      <segment>
        <source>u2_tpm.billing_type</source>
        <target>Billing Type</target>
      </segment>
    </unit>
    <unit id="PHUnj1C" name="u2_tpm.business_activity">
      <segment>
        <source>u2_tpm.business_activity</source>
        <target>Business Activity</target>
      </segment>
    </unit>
    <unit id="wAqGcRb" name="u2_tpm.business_activity.plural">
      <segment>
        <source>u2_tpm.business_activity.plural</source>
        <target>Business Activities</target>
      </segment>
    </unit>
    <unit id="CbFRneL" name="u2_tpm.carry_forward">
      <segment>
        <source>u2_tpm.carry_forward</source>
        <target>Carry Forward</target>
      </segment>
    </unit>
    <unit id="9Y3WJ5c" name="u2_tpm.carry_forward.help">
      <segment>
        <source>u2_tpm.carry_forward.help</source>
        <target>Set this flag to mark the transaction to be carried forward during a data transfer to another period.</target>
      </segment>
    </unit>
    <unit id="el3ToUs" name="u2_tpm.classification">
      <segment>
        <source>u2_tpm.classification</source>
        <target>Classification</target>
      </segment>
    </unit>
    <unit id="qd_CK9D" name="u2_tpm.contents">
      <segment>
        <source>u2_tpm.contents</source>
        <target>Contents</target>
      </segment>
    </unit>
    <unit id="GQj00wL" name="u2_tpm.contract_date">
      <segment>
        <source>u2_tpm.contract_date</source>
        <target>Date</target>
      </segment>
    </unit>
    <unit id="49g160g" name="u2_tpm.contract_name">
      <segment>
        <source>u2_tpm.contract_name</source>
        <target>Contract Name</target>
      </segment>
    </unit>
    <unit id="ugLjmfU" name="u2_tpm.contract_type">
      <segment>
        <source>u2_tpm.contract_type</source>
        <target>Contract Type</target>
      </segment>
    </unit>
    <unit id="F_ylgmn" name="u2_tpm.contracts.is_empty">
      <segment>
        <source>u2_tpm.contracts.is_empty</source>
        <target>No relevant contracts found.</target>
      </segment>
    </unit>
    <unit id="ZSUMbja" name="u2_tpm.country_by_country_report">
      <segment>
        <source>u2_tpm.country_by_country_report</source>
        <target>Country by Country Report</target>
      </segment>
    </unit>
    <unit id="WpenqN9" name="u2_tpm.country_by_country_report.plural">
      <segment>
        <source>u2_tpm.country_by_country_report.plural</source>
        <target>Country by Country Reports</target>
      </segment>
    </unit>
    <unit id="a8wmz_r" name="u2_tpm.country_founded">
      <segment>
        <source>u2_tpm.country_founded</source>
        <target>Country Founded</target>
      </segment>
    </unit>
    <unit id="qIhDKyp" name="u2_tpm.country_not_assigned">
      <segment>
        <source>u2_tpm.country_not_assigned</source>
        <target>No country is associated with the units of this report.</target>
      </segment>
    </unit>
    <unit id="kladVRq" name="u2_tpm.currency">
      <segment>
        <source>u2_tpm.currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="1rR8Ncl" name="u2_tpm.data_to_show">
      <segment>
        <source>u2_tpm.data_to_show</source>
        <target>Data to show</target>
      </segment>
    </unit>
    <unit id="k1tePr8" name="u2_tpm.details">
      <segment>
        <source>u2_tpm.details</source>
        <target>Details</target>
      </segment>
    </unit>
    <unit id="bAMUWXs" name="u2_tpm.document_widget">
      <segment>
        <source>u2_tpm.document_widget</source>
        <target>Document Widget</target>
      </segment>
    </unit>
    <unit id=".TTCFmv" name="u2_tpm.documentation">
      <segment>
        <source>u2_tpm.documentation</source>
        <target>Documentation</target>
      </segment>
    </unit>
    <unit id="9J7VSXr" name="u2_tpm.employees">
      <segment>
        <source>u2_tpm.employees</source>
        <target>Employees</target>
      </segment>
    </unit>
    <unit id="i5cixi5" name="u2_tpm.file">
      <segment>
        <source>u2_tpm.file</source>
        <target>File</target>
      </segment>
    </unit>
    <unit id="Tzt3hYv" name="u2_tpm.file.help">
      <segment>
        <source>u2_tpm.file.help</source>
        <target>To use the name of the file leave this field empty.</target>
      </segment>
    </unit>
    <unit id="QwVq9fP" name="u2_tpm.financial_data">
      <segment>
        <source>u2_tpm.financial_data</source>
        <target>Financial Data</target>
      </segment>
    </unit>
    <unit id="FlCsRZZ" name="u2_tpm.financial_data.plural">
      <segment>
        <source>u2_tpm.financial_data.plural</source>
        <target>Financial Data</target>
      </segment>
    </unit>
    <unit id="3mbDHe7" name="u2_tpm.financial_data_records.is_empty">
      <segment>
        <source>u2_tpm.financial_data_records.is_empty</source>
        <target>No relevant financial data found.</target>
      </segment>
    </unit>
    <unit id="9WQQR7A" name="u2_tpm.founded">
      <segment>
        <source>u2_tpm.founded</source>
        <target>Founded</target>
      </segment>
    </unit>
    <unit id="N7YaCJo" name="u2_tpm.group_currency">
      <segment>
        <source>u2_tpm.group_currency</source>
        <target>Group Currency</target>
      </segment>
    </unit>
    <unit id="aJ20n8Z" name="u2_tpm.group_currency.short">
      <segment>
        <source>u2_tpm.group_currency.short</source>
        <target>Group</target>
      </segment>
    </unit>
    <unit id="bF36Js3" name="u2_tpm.image">
      <segment>
        <source>u2_tpm.image</source>
        <target>Image</target>
      </segment>
    </unit>
    <unit id="GytM19G" name="u2_tpm.image_width">
      <segment>
        <source>u2_tpm.image_width</source>
        <target>Image width</target>
      </segment>
    </unit>
    <unit id="Yzzb0pp" name="u2_tpm.image_width.help">
      <segment>
        <source>u2_tpm.image_width.help</source>
        <target>A pixel value (e.g.: 200px) or “auto” (original size). The height will be calculated proportionally according to the width.</target>
      </segment>
    </unit>
    <unit id="OmcPQx3" name="u2_tpm.income_tax_accrued_value">
      <segment>
        <source>u2_tpm.income_tax_accrued_value</source>
        <target>Income Tax Accrued (current year)</target>
      </segment>
    </unit>
    <unit id="tXxpmAZ" name="u2_tpm.income_tax_accrued_value_base_value">
      <segment>
        <source>u2_tpm.income_tax_accrued_value_base_value</source>
        <target>Income Tax Accrued (current year)</target>
      </segment>
    </unit>
    <unit id="npI3Qrg" name="u2_tpm.income_tax_accrued_value_base_value.short">
      <segment>
        <source>u2_tpm.income_tax_accrued_value_base_value.short</source>
        <target>Inc. Tax Acc.</target>
      </segment>
    </unit>
    <unit id="4bKcMrW" name="u2_tpm.income_tax_accrued_value_group_value">
      <segment>
        <source>u2_tpm.income_tax_accrued_value_group_value</source>
        <target>Income Tax Accrued (current year) [Group Value]</target>
      </segment>
    </unit>
    <unit id="l6_a8Od" name="u2_tpm.income_tax_accrued_value_group_value.short">
      <segment>
        <source>u2_tpm.income_tax_accrued_value_group_value.short</source>
        <target>Inc. Tax Acc. [Group Value]</target>
      </segment>
    </unit>
    <unit id="_E9Osfx" name="u2_tpm.income_tax_accrued_value_local_value">
      <segment>
        <source>u2_tpm.income_tax_accrued_value_local_value</source>
        <target>Income Tax Accrued (current year) [Local Value]</target>
      </segment>
    </unit>
    <unit id="wau6uO8" name="u2_tpm.income_tax_accrued_value_local_value.short">
      <segment>
        <source>u2_tpm.income_tax_accrued_value_local_value.short</source>
        <target>Inc. Tax Acc. [Local Value]</target>
      </segment>
    </unit>
    <unit id="A3G7H5f" name="u2_tpm.income_tax_paid_value">
      <segment>
        <source>u2_tpm.income_tax_paid_value</source>
        <target>Income Tax Paid (on cash basis)</target>
      </segment>
    </unit>
    <unit id="OHrSI3q" name="u2_tpm.income_tax_paid_value_base_value">
      <segment>
        <source>u2_tpm.income_tax_paid_value_base_value</source>
        <target>Income Tax Paid (on cash basis)</target>
      </segment>
    </unit>
    <unit id="LDobK4r" name="u2_tpm.income_tax_paid_value_base_value.short">
      <segment>
        <source>u2_tpm.income_tax_paid_value_base_value.short</source>
        <target>Inc. Tax Paid</target>
      </segment>
    </unit>
    <unit id="Qjxn7zD" name="u2_tpm.income_tax_paid_value_group_value">
      <segment>
        <source>u2_tpm.income_tax_paid_value_group_value</source>
        <target>Income Tax Paid (on cash basis) [Group Value]</target>
      </segment>
    </unit>
    <unit id=".2LovDN" name="u2_tpm.income_tax_paid_value_group_value.short">
      <segment>
        <source>u2_tpm.income_tax_paid_value_group_value.short</source>
        <target>Inc. Tax Paid [Group Value]</target>
      </segment>
    </unit>
    <unit id="JXtNqGd" name="u2_tpm.income_tax_paid_value_local_value">
      <segment>
        <source>u2_tpm.income_tax_paid_value_local_value</source>
        <target>Income Tax Paid (on cash basis) [Local Value]</target>
      </segment>
    </unit>
    <unit id="au_QdYO" name="u2_tpm.income_tax_paid_value_local_value.short">
      <segment>
        <source>u2_tpm.income_tax_paid_value_local_value.short</source>
        <target>Inc. Tax Paid [Local Value]</target>
      </segment>
    </unit>
    <unit id="_nvdJT_" name="u2_tpm.key_figure">
      <segment>
        <source>u2_tpm.key_figure</source>
        <target>Key Figure</target>
      </segment>
    </unit>
    <unit id="HfHBE05" name="u2_tpm.key_figures">
      <segment>
        <source>u2_tpm.key_figures</source>
        <target>Key Figures</target>
      </segment>
    </unit>
    <unit id="CrJ2AZ0" name="u2_tpm.legal_form">
      <segment>
        <source>u2_tpm.legal_form</source>
        <target>Legal Form</target>
      </segment>
    </unit>
    <unit id="VEZiACX" name="u2_tpm.legal_name">
      <segment>
        <source>u2_tpm.legal_name</source>
        <target>Legal Name</target>
      </segment>
    </unit>
    <unit id="YgsiGb4" name="u2_tpm.local_currency">
      <segment>
        <source>u2_tpm.local_currency</source>
        <target>Local Currency</target>
      </segment>
    </unit>
    <unit id="T9dM5.h" name="u2_tpm.local_currency.short">
      <segment>
        <source>u2_tpm.local_currency.short</source>
        <target>Local</target>
      </segment>
    </unit>
    <unit id="7aTquKH" name="u2_tpm.local_file">
      <segment>
        <source>u2_tpm.local_file</source>
        <target>Local File</target>
      </segment>
    </unit>
    <unit id="3ZPo6uH" name="u2_tpm.local_file.plural">
      <segment>
        <source>u2_tpm.local_file.plural</source>
        <target>Local Files</target>
      </segment>
    </unit>
    <unit id="QAsqTg1" name="u2_tpm.main_business_activity">
      <segment>
        <source>u2_tpm.main_business_activity</source>
        <target>Main Business Activity</target>
      </segment>
    </unit>
    <unit id="3_f2QOM" name="u2_tpm.main_business_activity.is_empty">
      <segment>
        <source>u2_tpm.main_business_activity.is_empty</source>
        <target>No relevant business activities found.</target>
      </segment>
    </unit>
    <unit id="1pp_gzE" name="u2_tpm.main_business_activity.plural">
      <segment>
        <source>u2_tpm.main_business_activity.plural</source>
        <target>Main Business Activities</target>
      </segment>
    </unit>
    <unit id="VDzW4nr" name="u2_tpm.main_contracts">
      <segment>
        <source>u2_tpm.main_contracts</source>
        <target>Main Contracts</target>
      </segment>
    </unit>
    <unit id="NwKp9Ai" name="u2_tpm.master_file">
      <segment>
        <source>u2_tpm.master_file</source>
        <target>Master File</target>
      </segment>
    </unit>
    <unit id="AaOSmH4" name="u2_tpm.master_file.plural">
      <segment>
        <source>u2_tpm.master_file.plural</source>
        <target>Master Files</target>
      </segment>
    </unit>
    <unit id="ao1wP15" name="u2_tpm.module_name">
      <segment>
        <source>u2_tpm.module_name</source>
        <target>Transfer Pricing Monitor</target>
      </segment>
    </unit>
    <unit id="mQcXPLk" name="u2_tpm.module_name.short">
      <segment>
        <source>u2_tpm.module_name.short</source>
        <target>TPM</target>
      </segment>
    </unit>
    <unit id="ImaAIDa" name="u2_tpm.n_a">
      <segment>
        <source>u2_tpm.n_a</source>
        <target>n/a</target>
      </segment>
    </unit>
    <unit id="KPUNbRG" name="u2_tpm.name">
      <segment>
        <source>u2_tpm.name</source>
        <target>Name</target>
      </segment>
    </unit>
    <unit id="ZMhG9ET" name="u2_tpm.not_required">
      <segment>
        <source>u2_tpm.not_required</source>
        <target>Not Required</target>
      </segment>
    </unit>
    <unit id="UIiyGYI" name="u2_tpm.number_of_employees">
      <segment>
        <source>u2_tpm.number_of_employees</source>
        <target>Number of Employees</target>
      </segment>
    </unit>
    <unit id="G8NBFRj" name="u2_tpm.number_of_units">
      <segment>
        <source>u2_tpm.number_of_units</source>
        <target>Number of Units</target>
      </segment>
    </unit>
    <unit id="J_XhxuZ" name="u2_tpm.parties">
      <segment>
        <source>u2_tpm.parties</source>
        <target>Parties</target>
      </segment>
    </unit>
    <unit id="jpiCEil" name="u2_tpm.partner_doc">
      <segment>
        <source>u2_tpm.partner_doc</source>
        <target>Partner Doc.</target>
      </segment>
    </unit>
    <unit id="AO_2lfA" name="u2_tpm.partner_unit.help">
      <segment>
        <source>u2_tpm.partner_unit.help</source>
        <target>To select a partner unit, please uncheck the first checkbox and then choose from the list.</target>
      </segment>
    </unit>
    <unit id="pn69VXf" name="u2_tpm.partner_unit_country">
      <segment>
        <source>u2_tpm.partner_unit_country</source>
        <target>Partner Unit Country</target>
      </segment>
    </unit>
    <unit id="9.UMFK1" name="u2_tpm.partner_unit_is_to_be_documented">
      <segment>
        <source>u2_tpm.partner_unit_is_to_be_documented</source>
        <target>Partner Unit is to be Documented</target>
      </segment>
    </unit>
    <unit id="GKFk8bz" name="u2_tpm.partner_unit_name">
      <segment>
        <source>u2_tpm.partner_unit_name</source>
        <target>Partner Unit Name</target>
      </segment>
    </unit>
    <unit id="0HzD02c" name="u2.partner_unit_ref_id">
      <segment>
        <source>u2.partner_unit_ref_id</source>
        <target>Partner Unit Ref. ID</target>
      </segment>
    </unit>
    <unit id="MpA8w3O" name="u2_tpm.postal_address">
      <segment>
        <source>u2_tpm.postal_address</source>
        <target>Postal Address</target>
      </segment>
    </unit>
    <unit id="aUEBJ5N" name="u2_tpm.profit_loss_before_income_tax_value">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value</source>
        <target>Profit/Loss before Income Tax</target>
      </segment>
    </unit>
    <unit id="pXveByz" name="u2_tpm.profit_loss_before_income_tax_value_base_value">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value_base_value</source>
        <target>Profit/Loss before Income Tax</target>
      </segment>
    </unit>
    <unit id="RKiQxmz" name="u2_tpm.profit_loss_before_income_tax_value_base_value.short">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value_base_value.short</source>
        <target>P/L bef. Inc. Tax</target>
      </segment>
    </unit>
    <unit id="GArxwbx" name="u2_tpm.profit_loss_before_income_tax_value_group_value">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value_group_value</source>
        <target>Profit/Loss before Income Tax [Group Value]</target>
      </segment>
    </unit>
    <unit id="QMmTGyb" name="u2_tpm.profit_loss_before_income_tax_value_group_value.short">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value_group_value.short</source>
        <target>P/L bef. Inc. Tax [Group Value]</target>
      </segment>
    </unit>
    <unit id="65y_l6U" name="u2_tpm.profit_loss_before_income_tax_value_local_value">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value_local_value</source>
        <target>Profit/Loss before Income Tax [Local Value]</target>
      </segment>
    </unit>
    <unit id="0imv3.S" name="u2_tpm.profit_loss_before_income_tax_value_local_value.short">
      <segment>
        <source>u2_tpm.profit_loss_before_income_tax_value_local_value.short</source>
        <target>P/L bef. Inc. Tax [Local Value]</target>
      </segment>
    </unit>
    <unit id="YENrfYF" name="u2_tpm.providing_entity">
      <segment>
        <source>u2_tpm.providing_entity</source>
        <target>Providing Entity</target>
      </segment>
    </unit>
    <unit id="SFq1TI7" name="u2_tpm.providing_entity_country">
      <segment>
        <source>u2_tpm.providing_entity_country</source>
        <target>Providing Entity Country</target>
      </segment>
    </unit>
    <unit id="l_4sZzw" name="u2_tpm.providing_entity_name">
      <segment>
        <source>u2_tpm.providing_entity_name</source>
        <target>Providing Entity Name</target>
      </segment>
    </unit>
    <unit id="dTjvO9O" name="u2_tpm.receiving_entity">
      <segment>
        <source>u2_tpm.receiving_entity</source>
        <target>Receiving Entity</target>
      </segment>
    </unit>
    <unit id="y.FZZkr" name="u2_tpm.receiving_entity_country">
      <segment>
        <source>u2_tpm.receiving_entity_country</source>
        <target>Receiving Entity Country</target>
      </segment>
    </unit>
    <unit id="HZaxRSW" name="u2_tpm.receiving_entity_name">
      <segment>
        <source>u2_tpm.receiving_entity_name</source>
        <target>Receiving Entity Name</target>
      </segment>
    </unit>
    <unit id="cdph.4J" name="u2_tpm.ref_id">
      <segment>
        <source>u2_tpm.ref_id</source>
        <target>Ref Id</target>
      </segment>
    </unit>
    <unit id="oy4Bm7e" name="u2_tpm.register_number">
      <segment>
        <source>u2_tpm.register_number</source>
        <target>Register Number</target>
      </segment>
    </unit>
    <unit id="ypgpJPB" name="u2_tpm.registry_place">
      <segment>
        <source>u2_tpm.registry_place</source>
        <target>Registry Place</target>
      </segment>
    </unit>
    <unit id="O3wR4YR" name="u2_tpm.required">
      <segment>
        <source>u2_tpm.required</source>
        <target>Required</target>
      </segment>
    </unit>
    <unit id="ahYNXbr" name="u2_tpm.select_whether_the_partner_unit_needs_to_be_documented">
      <segment>
        <source>u2_tpm.select_whether_the_partner_unit_needs_to_be_documented</source>
        <target>Select whether the Partner Unit needs to be documented</target>
      </segment>
    </unit>
    <unit id="95uOyf2" name="u2_tpm.select_whether_the_unit_needs_to_be_documented">
      <segment>
        <source>u2_tpm.select_whether_the_unit_needs_to_be_documented</source>
        <target>Select whether the Unit needs to be documented</target>
      </segment>
    </unit>
    <unit id="_f7jceT" name="u2_tpm.show_template_list">
      <segment>
        <source>u2_tpm.show_template_list</source>
        <target>Show Template List</target>
      </segment>
    </unit>
    <unit id="4NoD04g" name="u2_tpm.snapshot">
      <segment>
        <source>u2_tpm.snapshot</source>
        <target>Units</target>
      </segment>
    </unit>
    <unit id="uo_C.Zs" name="u2_tpm.stated_capital_value">
      <segment>
        <source>u2_tpm.stated_capital_value</source>
        <target>Stated Capital</target>
      </segment>
    </unit>
    <unit id="ZbCuyK." name="u2_tpm.stated_capital_value_base_value">
      <segment>
        <source>u2_tpm.stated_capital_value_base_value</source>
        <target>Stated Capital</target>
      </segment>
    </unit>
    <unit id="uA18AMI" name="u2_tpm.stated_capital_value_base_value.short">
      <segment>
        <source>u2_tpm.stated_capital_value_base_value.short</source>
        <target>St. Capital</target>
      </segment>
    </unit>
    <unit id="g9ejELU" name="u2_tpm.stated_capital_value_group_value">
      <segment>
        <source>u2_tpm.stated_capital_value_group_value</source>
        <target>Stated Capital [Group Value]</target>
      </segment>
    </unit>
    <unit id="vTPjp5." name="u2_tpm.stated_capital_value_group_value.short">
      <segment>
        <source>u2_tpm.stated_capital_value_group_value.short</source>
        <target>St. Capital [Group Value]</target>
      </segment>
    </unit>
    <unit id="De8R2wI" name="u2_tpm.stated_capital_value_local_value">
      <segment>
        <source>u2_tpm.stated_capital_value_local_value</source>
        <target>Stated Capital [Local Value]</target>
      </segment>
    </unit>
    <unit id="RUcNyi_" name="u2_tpm.stated_capital_value_local_value.short">
      <segment>
        <source>u2_tpm.stated_capital_value_local_value.short</source>
        <target>St. Capital [Local Value]</target>
      </segment>
    </unit>
    <unit id="SXvrJYw" name="u2_tpm.sub_type">
      <segment>
        <source>u2_tpm.sub_type</source>
        <target>Sub-Type</target>
      </segment>
    </unit>
    <unit id="IAG4bq3" name="u2_tpm.tangible_assets_value">
      <segment>
        <source>u2_tpm.tangible_assets_value</source>
        <target>Tangible Assets other than Cash and Cash Equivalents</target>
      </segment>
    </unit>
    <unit id="QNWQEck" name="u2_tpm.tangible_assets_value_base_value">
      <segment>
        <source>u2_tpm.tangible_assets_value_base_value</source>
        <target>Tangible Assets other than Cash and Cash Equivalents</target>
      </segment>
    </unit>
    <unit id="Zm8Czsj" name="u2_tpm.tangible_assets_value_base_value.short">
      <segment>
        <source>u2_tpm.tangible_assets_value_base_value.short</source>
        <target>Tang. Assets other than Cash</target>
      </segment>
    </unit>
    <unit id="LrHOmmA" name="u2_tpm.tangible_assets_value_group_value">
      <segment>
        <source>u2_tpm.tangible_assets_value_group_value</source>
        <target>Tangible Assets other than Cash and Cash Equivalents [Group Value]</target>
      </segment>
    </unit>
    <unit id="EfShDnu" name="u2_tpm.tangible_assets_value_group_value.short">
      <segment>
        <source>u2_tpm.tangible_assets_value_group_value.short</source>
        <target>Tang. Assets other than Cash [Group Value]</target>
      </segment>
    </unit>
    <unit id="ZjYzzWG" name="u2_tpm.tangible_assets_value_local_value">
      <segment>
        <source>u2_tpm.tangible_assets_value_local_value</source>
        <target>Tangible Assets other than Cash and Cash Equivalents [Local Value]</target>
      </segment>
    </unit>
    <unit id="uuCuoAr" name="u2_tpm.tangible_assets_value_local_value.short">
      <segment>
        <source>u2_tpm.tangible_assets_value_local_value.short</source>
        <target>Tang. Assets other than Cash [Local Value]</target>
      </segment>
    </unit>
    <unit id="ShJPI8b" name="u2_tpm.tax_number">
      <segment>
        <source>u2_tpm.tax_number</source>
        <target>Tax Number</target>
      </segment>
    </unit>
    <unit id="vuVY6yA" name="u2_tpm.templates">
      <segment>
        <source>u2_tpm.templates</source>
        <target>Templates</target>
      </segment>
    </unit>
    <unit id="U8Vtj22" name="u2_tpm.text">
      <segment>
        <source>u2_tpm.text</source>
        <target>Text</target>
      </segment>
    </unit>
    <unit id="mqpX9X6" name="u2_tpm.the_name_of_this_file_is_restricted">
      <segment>
        <source>u2_tpm.the_name_of_this_file_is_restricted</source>
        <target>(The name of this file is restricted)</target>
      </segment>
    </unit>
    <unit id="aA9fBvY" name="u2_tpm.this_document_has_no_content">
      <segment>
        <source>u2_tpm.this_document_has_no_content</source>
        <target>This document has no content</target>
      </segment>
    </unit>
    <unit id="APviOkG" name="u2_tpm.total">
      <segment>
        <source>u2_tpm.total</source>
        <target>Total</target>
      </segment>
    </unit>
    <unit id="FrfpYrT" name="u2_tpm.totalRevenue_value_local_value">
      <segment>
        <source>u2_tpm.totalRevenue_value_local_value</source>
        <target>Total Revenue [Local Value]</target>
      </segment>
    </unit>
    <unit id="U3dN0lT" name="u2_tpm.totalRevenue_value_local_value.short">
      <segment>
        <source>u2_tpm.totalRevenue_value_local_value.short</source>
        <target>Total Rev. [Local Value]</target>
      </segment>
    </unit>
    <unit id="Vh_9Wa9" name="u2_tpm.total_for_given_document_country">
      <segment>
        <source>u2_tpm.total_for_given_document_country</source>
        <target>Total For %current_document_country%</target>
      </segment>
    </unit>
    <unit id="C0aoN4F" name="u2_tpm.total_revenue_related_value">
      <segment>
        <source>u2_tpm.total_revenue_related_value</source>
        <target>Total Revenue with Related Party</target>
      </segment>
    </unit>
    <unit id="pwkdGPg" name="u2_tpm.total_revenue_related_value_base_value">
      <segment>
        <source>u2_tpm.total_revenue_related_value_base_value</source>
        <target>Total Revenue with Related Party</target>
      </segment>
    </unit>
    <unit id="L6jTmeV" name="u2_tpm.total_revenue_related_value_base_value.short">
      <segment>
        <source>u2_tpm.total_revenue_related_value_base_value.short</source>
        <target>Rev. w. rel. party</target>
      </segment>
    </unit>
    <unit id="f5qnsKv" name="u2_tpm.total_revenue_related_value_group_value">
      <segment>
        <source>u2_tpm.total_revenue_related_value_group_value</source>
        <target>Total Revenue with Related Party [Group Value]</target>
      </segment>
    </unit>
    <unit id=".RxJh4y" name="u2_tpm.total_revenue_related_value_group_value.short">
      <segment>
        <source>u2_tpm.total_revenue_related_value_group_value.short</source>
        <target>Rev. w. rel. party [Group Value]</target>
      </segment>
    </unit>
    <unit id="IUeXydv" name="u2_tpm.total_revenue_related_value_local_value">
      <segment>
        <source>u2_tpm.total_revenue_related_value_local_value</source>
        <target>Total Revenue with Related Party [Local Value]</target>
      </segment>
    </unit>
    <unit id="F8UD.2." name="u2_tpm.total_revenue_related_value_local_value.short">
      <segment>
        <source>u2_tpm.total_revenue_related_value_local_value.short</source>
        <target>Rev. w. rel. party [Local Value]</target>
      </segment>
    </unit>
    <unit id="tnFa3Dp" name="u2_tpm.total_revenue_unrelated_value">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value</source>
        <target>Total Revenue with Unrelated Party</target>
      </segment>
    </unit>
    <unit id="9o1A6aj" name="u2_tpm.total_revenue_unrelated_value_base_value">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value_base_value</source>
        <target>Total Revenue with Unrelated Party</target>
      </segment>
    </unit>
    <unit id="Zujje7f" name="u2_tpm.total_revenue_unrelated_value_base_value.short">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value_base_value.short</source>
        <target>Rev. w. unrel. party</target>
      </segment>
    </unit>
    <unit id="IDsjhKN" name="u2_tpm.total_revenue_unrelated_value_group_value">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value_group_value</source>
        <target>Total Revenue with Unrelated Party [Group Value]</target>
      </segment>
    </unit>
    <unit id="HBKt7JJ" name="u2_tpm.total_revenue_unrelated_value_group_value.short">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value_group_value.short</source>
        <target>Rev. w. unrel. party [Group Value]</target>
      </segment>
    </unit>
    <unit id="zK0gsyB" name="u2_tpm.total_revenue_unrelated_value_local_value">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value_local_value</source>
        <target>Total Revenue with Unrelated Party [Local Value]</target>
      </segment>
    </unit>
    <unit id="hpjBH_n" name="u2_tpm.total_revenue_unrelated_value_local_value.short">
      <segment>
        <source>u2_tpm.total_revenue_unrelated_value_local_value.short</source>
        <target>Rev. w. unrel. party [Local Value]</target>
      </segment>
    </unit>
    <unit id="WLB_XJo" name="u2_tpm.total_revenue_value">
      <segment>
        <source>u2_tpm.total_revenue_value</source>
        <target>Total Revenue</target>
      </segment>
    </unit>
    <unit id="2dCHi4l" name="u2_tpm.total_revenue_value.help">
      <segment>
        <source>u2_tpm.total_revenue_value.help</source>
        <target>Sum of the total revenue with unrelated party value and the total revenue with related party value.</target>
      </segment>
    </unit>
    <unit id="qZMeaTf" name="u2_tpm.total_revenue_value_base_value">
      <segment>
        <source>u2_tpm.total_revenue_value_base_value</source>
        <target>Total Revenue</target>
      </segment>
    </unit>
    <unit id="U4CM2M." name="u2_tpm.total_revenue_value_base_value.short">
      <segment>
        <source>u2_tpm.total_revenue_value_base_value.short</source>
        <target>Total Rev.</target>
      </segment>
    </unit>
    <unit id="Nig_kRA" name="u2_tpm.total_revenue_value_group_value">
      <segment>
        <source>u2_tpm.total_revenue_value_group_value</source>
        <target>Total Revenue [Group Value]</target>
      </segment>
    </unit>
    <unit id="OIjGSpM" name="u2_tpm.total_revenue_value_group_value.short">
      <segment>
        <source>u2_tpm.total_revenue_value_group_value.short</source>
        <target>Total Rev. [Group Value]</target>
      </segment>
    </unit>
    <unit id="PgonO24" name="u2_tpm.total_volume">
      <segment>
        <source>u2_tpm.total_volume</source>
        <target>Total Volume</target>
      </segment>
    </unit>
    <unit id="xQN8crH" name="u2_tpm.transaction">
      <segment>
        <source>u2_tpm.transaction</source>
        <target>Transaction</target>
      </segment>
    </unit>
    <unit id="qt3NUQj" name="u2_tpm.transaction.plural">
      <segment>
        <source>u2_tpm.transaction.plural</source>
        <target>Transactions</target>
      </segment>
    </unit>
    <unit id="rCzLt55" name="u2_tpm.transaction_amount">
      <segment>
        <source>u2_tpm.transaction_amount</source>
        <target>Amount</target>
      </segment>
    </unit>
    <unit id="Wwjfp2R" name="u2_tpm.transaction_currency">
      <segment>
        <source>u2_tpm.transaction_currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="RuGp9Bq" name="u2_tpm.transaction_details">
      <segment>
        <source>u2_tpm.transaction_details</source>
        <target>Transaction details</target>
      </segment>
    </unit>
    <unit id="kYs6k.k" name="u2_tpm.transaction_table">
      <segment>
        <source>u2_tpm.transaction_table</source>
        <target>Transaction Table</target>
      </segment>
    </unit>
    <unit id="lZj36ng" name="u2_tpm.transaction_type">
      <segment>
        <source>u2_tpm.transaction_type</source>
        <target>Transaction Type</target>
      </segment>
    </unit>
    <unit id="KeDrN1I" name="u2_tpm.transaction_units">
      <segment>
        <source>u2_tpm.transaction_units</source>
        <target>Transaction Units</target>
      </segment>
    </unit>
    <unit id="ZbnmUbw" name="u2_tpm.transfer_pricing_method">
      <segment>
        <source>u2_tpm.transfer_pricing_method</source>
        <target>Transfer Pricing Method</target>
      </segment>
    </unit>
    <unit id="cyhHqG." name="u2_tpm.transfer_pricing_method.help">
      <segment>
        <source>u2_tpm.transfer_pricing_method.help</source>
        <target>Choose the appropriate transfer pricing method from the drop down list</target>
      </segment>
    </unit>
    <unit id="cec745Y" name="u2_tpm.type">
      <segment>
        <source>u2_tpm.type</source>
        <target>Type</target>
      </segment>
    </unit>
    <unit id="hZ_X_ZR" name="u2_tpm.unit_country">
      <segment>
        <source>u2_tpm.unit_country</source>
        <target>Unit Country</target>
      </segment>
    </unit>
    <unit id=".PK9ckS" name="u2_tpm.unit_doc">
      <segment>
        <source>u2_tpm.unit_doc</source>
        <target>Unit Doc.</target>
      </segment>
    </unit>
    <unit id="qFOpPiT" name="u2.unit_hierarchy_structure_at">
      <segment>
        <source>u2.unit_hierarchy_structure_at</source>
        <target>Structure at %date%</target>
      </segment>
    </unit>
    <unit id="zJrHvYZ" name="u2_tpm.unit_hierarchy">
      <segment>
        <source>u2_tpm.unit_hierarchy</source>
        <target>Hierarchy</target>
      </segment>
    </unit>
    <unit id="k3u7pP9" name="u2_tpm.unit_is_to_be_documented">
      <segment>
        <source>u2_tpm.unit_is_to_be_documented</source>
        <target>Unit is to be Documented</target>
      </segment>
    </unit>
    <unit id="Byjdbx0" name="u2.unit_name">
      <segment>
        <source>u2.unit_name</source>
        <target>Unit Name</target>
      </segment>
    </unit>
    <unit id="Y8XIqA1" name="u2.unit_ref_id">
      <segment>
        <source>u2.unit_ref_id</source>
        <target>Unit Ref. ID</target>
      </segment>
    </unit>
    <unit id="nSBhS.Y" name="u2_tpm.units">
      <segment>
        <source>u2_tpm.units</source>
        <target>Units</target>
      </segment>
    </unit>
    <unit id="9rAO5EH" name="u2_tpm.units.is_empty">
      <segment>
        <source>u2_tpm.units.is_empty</source>
        <target>There are no units associated with this document.</target>
      </segment>
    </unit>
    <unit id="dxvy87s" name="u2_tpm.units_widget.blocks">
      <segment>
        <source>u2_tpm.units_widget.blocks</source>
        <target>Blocks</target>
      </segment>
    </unit>
    <unit id="t2hbyHx" name="u2_tpm.units_widget.style">
      <segment>
        <source>u2_tpm.units_widget.style</source>
        <target>Style</target>
      </segment>
    </unit>
    <unit id="EnvC3Vg" name="u2_tpm.units_widget.table">
      <segment>
        <source>u2_tpm.units_widget.table</source>
        <target>Table</target>
      </segment>
    </unit>
    <unit id="tnbUUol" name="u2_tpm.widget.file.not_attached">
      <segment>
        <source>u2_tpm.widget.file.not_attached</source>
        <target>The selected file is no longer attached to this section.</target>
      </segment>
    </unit>
    <unit id="5xyyJkS" name="u2_tpm.widget.image.not_attached">
      <segment>
        <source>u2_tpm.widget.image.not_attached</source>
        <target>The selected image is no longer attached to this section.</target>
      </segment>
    </unit>
    <unit id="x_xlsqI" name="u2.open_menu">
      <segment>
        <source>u2.open_menu</source>
        <target>Open menu</target>
      </segment>
    </unit>
    <unit id="e7VOhtB" name="u2.dashboard.no_dashboard_exists">
      <segment>
        <source>u2.dashboard.no_dashboard_exists</source>
        <target>It seems no dashboards exist yet!</target>
      </segment>
    </unit>
    <unit id="TR13ciH" name="u2.dashboard.create_a_dashboard">
      <segment>
        <source>u2.dashboard.create_a_dashboard</source>
        <target>Create a dashboard</target>
      </segment>
    </unit>
    <unit id="z_5ACcc" name="u2.ask_an_admin_to_create_one">
      <segment>
        <source>u2.ask_an_admin_to_create_one</source>
        <target>Ask an administrator to create one for you.</target>
      </segment>
    </unit>
    <unit id=".0WLcD6" name="u2.view_associated_tasks">
      <segment>
        <source>u2.view_associated_tasks</source>
        <target>View associated tasks</target>
      </segment>
    </unit>
    <unit id="buNujJW" name="u2.ttl">
      <segment>
        <source>u2.ttl</source>
        <target>Time-To-Live</target>
      </segment>
    </unit>
    <unit id="jxSD4G8" name="u2.api_key.plural">
      <segment>
        <source>u2.api_key.plural</source>
        <target>Api-Keys</target>
      </segment>
    </unit>
    <unit id="3cgYicZ" name="u2_core.edit_api_key">
      <segment>
        <source>u2_core.edit_api_key</source>
        <target>Edit Api-Key</target>
      </segment>
    </unit>
    <unit id="PZqI8dU" name="u2_core.create_api_key">
      <segment>
        <source>u2_core.create_api_key</source>
        <target>Create Api-Key</target>
      </segment>
    </unit>
    <unit id="QhAhBpO" name="u2.infinite">
      <segment>
        <source>u2.infinite</source>
        <target>Infinite</target>
      </segment>
    </unit>
    <unit id="eCuBk_b" name="u2.7_days">
      <segment>
        <source>u2.7_days</source>
        <target>7 days</target>
      </segment>
    </unit>
    <unit id="HU22Y6V" name="u2.30_days">
      <segment>
        <source>u2.30_days</source>
        <target>30 days</target>
      </segment>
    </unit>
    <unit id="uTvBW8t" name="u2.60_days">
      <segment>
        <source>u2.60_days</source>
        <target>60 days</target>
      </segment>
    </unit>
    <unit id="QZhNePx" name="u2.90_days">
      <segment>
        <source>u2.90_days</source>
        <target>90 days</target>
      </segment>
    </unit>
    <unit id="wbO.dGd" name="u2.expand">
      <segment>
        <source>u2.expand</source>
        <target>Expand</target>
      </segment>
    </unit>
    <unit id="G25HpYE" name="u2.file_inherited_permissions_help">
      <segment>
        <source>u2.file_inherited_permissions_help</source>
        <target>Inherited via the security setting on the file</target>
      </segment>
    </unit>
    <unit id="4Lh.tm6" name="u2_core.download_file_name">
      <segment>
        <source>u2_core.download_file_name</source>
        <target>Download "%file_name%"</target>
      </segment>
    </unit>
    <unit id="kgWX_Ym" name="u2.number_more">
      <segment>
        <source>u2.number_more</source>
        <target>%number% more</target>
      </segment>
    </unit>
    <unit id="TqF2CZ5" name="u2.toggle_attachment_info">
      <segment>
        <source>u2.toggle_attachment_info</source>
        <target>Toggle attachment information</target>
      </segment>
    </unit>
    <unit id="7vuGtEa" name="u2.number_of_decimal_places">
      <segment>
        <source>u2.number_of_decimal_places</source>
        <target>Dacimal Places</target>
      </segment>
    </unit>
    <unit id="31t8VkJ" name="u2.number_of_decimal_places_help">
      <segment>
        <source>u2.number_of_decimal_places_help</source>
        <target>The number of decimal places to be used for values in this currency</target>
      </segment>
    </unit>
    <unit id="E7iUD7e" name="u2_core.edit_file_name">
      <segment>
        <source>u2_core.edit_file_name</source>
        <target>Edit "%file_name%"</target>
      </segment>
    </unit>
    <unit id="mo120Ih" name="u2.send_request">
      <segment>
        <source>u2.send_request</source>
        <target>Send request</target>
      </segment>
    </unit>
    <unit id="0UXClPF" name="u2.file_permissions_request_comment">
      <segment>
        <source>u2.file_permissions_request_comment</source>
        <target>Please add a comment to describe what permissions you are requesting and why you need them:</target>
      </segment>
    </unit>
    <unit id="5TPDyd2" name="u2_core.permissions_request_for_given_file">
      <segment>
        <source>u2_core.permissions_request_for_given_file</source>
        <target>You are requesting permissions for the file “%file_name%”.</target>
      </segment>
    </unit>
    <unit id="ycZSKvx" name="u2_core.current_permissions">
      <segment>
        <source>u2_core.current_permissions</source>
        <target>Current permissions</target>
      </segment>
    </unit>
    <unit id="ItSI0Ec" name="u2.dashboard_link_not_found">
      <segment>
        <source>u2.dashboard_link_not_found</source>
        <target>Dashboard not found</target>
      </segment>
    </unit>
    <unit id="EUS3PrV" name="u2.dashboard.choose_another_dashboard">
      <segment>
        <source>u2.dashboard.choose_another_dashboard</source>
        <target>The link for this dashboard may have changed or it may no longer exist. Please choose one of the following dashboards.</target>
      </segment>
    </unit>
    <unit id="nt5VApB" name="u2.datasheets.plural">
      <segment>
        <source>u2.datasheets.plural</source>
        <target>Datasheets</target>
      </segment>
    </unit>
    <unit id="RYs73kO" name="u2.role_is_inherited_from_role">
      <segment>
        <source>u2.role_is_inherited_from_role</source>
        <target>This role is inherited from another role.</target>
      </segment>
    </unit>
    <unit id="PDuNuTq" name="u2.user_permissions.record_is_public">
      <segment>
        <source>u2.user_permissions.record_is_public</source>
        <target>Assigned groups have no effect because "Visible to all users" is on</target>
      </segment>
    </unit>
    <unit id="nQtngFb" name="u2.group_permissions.record_is_public">
      <segment>
        <source>u2.group_permissions.record_is_public</source>
        <target>Assigned groups have no effect because "Visible to all users" is on</target>
      </segment>
    </unit>
    <unit id="wiv9iEs" name="u2.datasheets.select_a_datasheet">
      <segment>
        <source>u2.datasheets.select_a_datasheet</source>
        <target>Select a datasheet</target>
      </segment>
    </unit>
    <unit id="VnakL17" name="u2.unit_view_parameter">
      <segment>
        <source>u2.unit_view_parameter</source>
        <target>Unit View Parameters</target>
      </segment>
    </unit>
    <unit id="U.rqhak" name="u2.group_view_parameter">
      <segment>
        <source>u2.group_view_parameter</source>
        <target>Group View Parameters</target>
      </segment>
    </unit>
    <unit id="9mDxUTe" name="u2.datasheets.datasheet_collection.select_datasheets">
      <segment>
        <source>u2.datasheets.datasheet_collection.select_datasheets</source>
        <target>Select a datasheet</target>
      </segment>
    </unit>
    <unit id="jnwkeFf" name="u2.select">
      <segment>
        <source>u2.select</source>
        <target>Select</target>
      </segment>
    </unit>
    <unit id="8Vugng6" name="u2.breakdown.close_breakdown">
      <segment>
        <source>u2.breakdown.close_breakdown</source>
        <target>Close breakdown</target>
      </segment>
    </unit>
    <unit id="mi6lAbe" name="u2.datasheets.datasheet_collection.plural">
      <segment>
        <source>u2.datasheets.datasheet_collection.plural</source>
        <target>Datasheet Collections</target>
      </segment>
    </unit>
    <unit id="N92rldC" name="u2.number_of_datasheets">
      <segment>
        <source>u2.number_of_datasheets</source>
        <target>Number of datasheets</target>
      </segment>
    </unit>
    <unit id="PvOkExG" name="u2.datasheets.number_of_fields">
      <segment>
        <source>u2.datasheets.number_of_fields</source>
        <target>Number of fields</target>
      </segment>
    </unit>
    <unit id="noFKGuN" name="u2.datasheets.datasheet_collection.new">
      <segment>
        <source>u2.datasheets.datasheet_collection.new</source>
        <target>New Datasheet Collection</target>
      </segment>
    </unit>
    <unit id="GLNJLbW" name="Equity Type - Shares/Participation (real estate share deal)">
      <segment>
        <source>Equity Type - Shares/Participation (real estate share deal)</source>
        <target>Equity Type - Shares/Participation (real estate share deal)</target>
      </segment>
    </unit>
    <unit id="yejH0GX" name="Other Asset Transfer - Properties (real estate asset deal)">
      <segment>
        <source>Other Asset Transfer - Properties (real estate asset deal)</source>
        <target>Other Asset Transfer - Properties (real estate asset deal)</target>
      </segment>
    </unit>
    <unit id="RhTjjYE" name="Insurance">
      <segment>
        <source>Insurance</source>
        <target>Insurance</target>
      </segment>
    </unit>
    <unit id="KkaTfX5" name="Fees">
      <segment>
        <source>Fees</source>
        <target>Fees</target>
      </segment>
    </unit>
    <unit id="srXDYEQ" name="Commission">
      <segment>
        <source>Commission</source>
        <target>Commission</target>
      </segment>
    </unit>
    <unit id="Ipa3QpG" name="Dividends">
      <segment>
        <source>Dividends</source>
        <target>Dividends</target>
      </segment>
    </unit>
    <unit id="BRSLyxR" name="Cost or Revenue">
      <segment>
        <source>Cost or Revenue</source>
        <target>Cost or Revenue</target>
      </segment>
    </unit>
    <unit id="sW..OEw" name="Credit Default">
      <segment>
        <source>Credit Default</source>
        <target>Credit Default</target>
      </segment>
    </unit>
    <unit id="pKWQCUa" name="Interest Rate">
      <segment>
        <source>Interest Rate</source>
        <target>Interest Rate</target>
      </segment>
    </unit>
    <unit id="0WNp7ZI" name="Currency">
      <segment>
        <source>Currency</source>
        <target>Currency</target>
      </segment>
    </unit>
    <unit id="m974DdC" name="u2.choice.trace_id.plural">
      <segment>
        <source>u2.choice.trace_id.plural</source>
        <target>Trace IDs</target>
      </segment>
    </unit>
    <unit id="PRFACCN" name="u2.choice.trace_id.">
      <segment>
        <source>u2.choice.trace_id.</source>
        <target>Trace ID</target>
      </segment>
    </unit>
    <unit id="ANLO3kg" name="u2.email.api_key.subject.about_to_expire">
      <segment>
        <source>u2.email.api_key.subject.about_to_expire</source>
        <target>Your Api-Key "%apiKey%" is about to expire</target>
      </segment>
    </unit>
    <unit id="lUg2kLZ" name="u2.email.api_key.content.api_key_will_expire_at">
      <segment>
        <source>u2.email.api_key.content.api_key_will_expire_at</source>
        <target><![CDATA[API-Key <strong>„%apiKeyName%“</strong> will expire on <strong>%apiKeyExpiresDate%</strong>.]]></target>
      </segment>
    </unit>
    <unit id="iwRiK5T" name="u2.datasheets.choose_a_datasheet">
      <segment>
        <source>u2.datasheets.choose_a_datasheet</source>
        <target>Choose a datasheet</target>
      </segment>
    </unit>
    <unit id="P2rv2KB" name="u2.api_key.regenerate_title">
      <segment>
        <source>u2.api_key.regenerate_title</source>
        <target>Regenerate Api-Key</target>
      </segment>
    </unit>
    <unit id="WSA86wN" name="u2.email.api_key.content.if_needed_regenerate">
      <segment>
        <source>u2.email.api_key.content.if_needed_regenerate</source>
        <target><![CDATA[If this token is still needed, visit <a href="%userSettingUrl%">User Settings</a> to generate an equivalent.]]></target>
      </segment>
    </unit>
    <unit id="xvEHfpg" name="u2.email.sincerely_universal_units">
      <segment>
        <source>u2.email.sincerely_universal_units</source>
        <target><![CDATA[Thanks, <br> The Universal Units Team]]></target>
      </segment>
    </unit>
    <unit id="MgCEvRf" name="u2.feature_toggles">
      <segment>
        <source>u2.feature_toggles</source>
        <target>Experimental Features</target>
      </segment>
    </unit>
    <unit id="JqlFN8r" name="u2.password_min_length">
      <segment>
        <source>u2.password_min_length</source>
        <target>Password minimum length</target>
      </segment>
    </unit>
    <unit id="6.Jmhnk" name="u2.datasheets.previous">
      <segment>
        <source>u2.datasheets.previous</source>
        <target>Previous Datasheet</target>
      </segment>
    </unit>
    <unit id="lqqZjQG" name="u2.datasheets.next">
      <segment>
        <source>u2.datasheets.next</source>
        <target>Next Datasheet</target>
      </segment>
    </unit>
    <unit id="Ap4AeFb" name="u2.switch_to_group_view">
      <segment>
        <source>u2.switch_to_group_view</source>
        <target>Switch to group</target>
      </segment>
    </unit>
    <unit id="v6tHPJ_" name="u2.switch_to_unit_view">
      <segment>
        <source>u2.switch_to_unit_view</source>
        <target>Switch to unit</target>
      </segment>
    </unit>
    <unit id="pBqM3fG" name="u2.dashboard.widget.filter_results.error_while_fetching_data">
      <segment>
        <source>u2.dashboard.widget.filter_results.error_while_fetching_data</source>
        <target>An error occurred while fetching the saved filter results</target>
      </segment>
    </unit>
    <unit id="ibwXFF9" name="u2.preview">
      <segment>
        <source>u2.preview</source>
        <target>Preview</target>
      </segment>
    </unit>
    <unit id="1xtstM8" name="u2.size">
      <segment>
        <source>u2.size</source>
        <target>Size</target>
      </segment>
    </unit>
    <unit id="jPu3Ic0" name="u2.dashboard.widget.plural">
      <segment>
        <source>u2.dashboard.widget.plural</source>
        <target>Dashboard Widgets</target>
      </segment>
    </unit>
    <unit id="M6MrGt2" name="u2.dashboard.widget.not_configure_yet">
      <segment>
        <source>u2.dashboard.widget.not_configure_yet</source>
        <target>This widget has not yet been configured and therefore cannot be loaded.</target>
      </segment>
    </unit>
    <unit id="l8UndZW" name="u2.columns">
      <segment>
        <source>u2.columns</source>
        <target>Columns</target>
      </segment>
    </unit>
    <unit id="_X_X_Y8" name="u2.sort">
      <segment>
        <source>u2.sort</source>
        <target>Sorting</target>
      </segment>
    </unit>
    <unit id="kItEznX" name="u2.saved_filter.sort.column">
      <segment>
        <source>u2.saved_filter.sort.column</source>
        <target>Column to sort by</target>
      </segment>
    </unit>
    <unit id="UJpWJ2s" name="u2.saved_filter.sort.direction">
      <segment>
        <source>u2.saved_filter.sort.direction</source>
        <target>Sorting direction</target>
      </segment>
    </unit>
    <unit id="A8qtpbt" name="u2.dashboard.widget.new">
      <segment>
        <source>u2.dashboard.widget.new</source>
        <target>New Dashboard Widget</target>
      </segment>
    </unit>
    <unit id="OLdcOhj" name="u2.dashboard.widget.html">
      <segment>
        <source>u2.dashboard.widget.html</source>
        <target>Custom HTML</target>
      </segment>
    </unit>
    <unit id="dRcN.mJ" name="u2.dashboard.widget.filter_results">
      <segment>
        <source>u2.dashboard.widget.filter_results</source>
        <target>Results of a saved filter</target>
      </segment>
    </unit>
    <unit id="CcMizto" name="u2.dashboard.widget.up_next">
      <segment>
        <source>u2.dashboard.widget.up_next</source>
        <target>Today's Tasks</target>
      </segment>
    </unit>
    <unit id="OetqFm_" name="u2.dashboard.widget.current_week_overview">
      <segment>
        <source>u2.dashboard.widget.current_week_overview</source>
        <target>Current Week Overview</target>
      </segment>
    </unit>
    <unit id="Gu8KEl6" name="u2.widget.size.small">
      <segment>
        <source>u2.widget.size.small</source>
        <target>Small</target>
      </segment>
    </unit>
    <unit id="5OjNBPw" name="u2.widget.size.medium">
      <segment>
        <source>u2.widget.size.medium</source>
        <target>Medium</target>
      </segment>
    </unit>
    <unit id="D73rasp" name="u2.widget.size.large">
      <segment>
        <source>u2.widget.size.large</source>
        <target>Large</target>
      </segment>
    </unit>
    <unit id="f9hGhsu" name="u2.dashboard_widget.new">
      <segment>
        <source>u2.dashboard_widget.new</source>
        <target>Add Widget</target>
      </segment>
    </unit>
    <unit id="EUT37et" name="u2.dashboard.edit_widgets">
      <segment>
        <source>u2.dashboard.edit_widgets</source>
        <target>Edit Widgets</target>
      </segment>
    </unit>
    <unit id="3j126Qu" name="u2.showing_filtered_items_out_of_total">
      <segment>
        <source>u2.showing_filtered_items_out_of_total</source>
        <target>Showing %filtered% of %total% item(s)</target>
      </segment>
    </unit>
    <unit id="LUe1nMt" name="u2.showing_all_items">
      <segment>
        <source>u2.showing_all_items</source>
        <target>Showing all %total% item(s)</target>
      </segment>
    </unit>
    <unit id="GTLQfG7" name="u2.drop_item_here">
      <segment>
        <source>u2.drop_item_here</source>
        <target>Drop item here</target>
      </segment>
    </unit>
    <unit id="_rtvCRj" name="u2.undo">
      <segment>
        <source>u2.undo</source>
        <target>Undo</target>
      </segment>
    </unit>
    <unit id="GzGRBtq" name="u2.redo">
      <segment>
        <source>u2.redo</source>
        <target>Redo</target>
      </segment>
    </unit>
    <unit id="AcLyYzZ" name="u2.dashboard.widget.filter_statistics.error_while_fetching_data">
      <segment>
        <source>u2.dashboard.widget.filter_statistics.error_while_fetching_data</source>
        <target>An error occurred while fetching the saved filter statistics</target>
      </segment>
    </unit>
    <unit id="2.hPgtF" name="u2.dashboard.widget.filter_statistics">
      <segment>
        <source>u2.dashboard.widget.filter_statistics</source>
        <target>Filter Statistics</target>
      </segment>
    </unit>
    <unit id="gl2TOSA" name="u2.label">
      <segment>
        <source>u2.label</source>
        <target>Label</target>
      </segment>
    </unit>
    <unit id="vgyjPGh" name="u2.color">
      <segment>
        <source>u2.color</source>
        <target>Color</target>
      </segment>
    </unit>
    <unit id="SAIychc" name="u2.chart_size">
      <segment>
        <source>u2.chart_size</source>
        <target>Chart size</target>
      </segment>
    </unit>
    <unit id="tM9953Q" name="u2.widget.filter_statistic.new_entry_disabled_reason">
      <segment>
        <source>u2.widget.filter_statistic.new_entry_disabled_reason</source>
        <target>The filter statistics widget only supports a maximum of up to 10 saved filters.</target>
      </segment>
    </unit>
    <unit id="Z.nGp8v" name="u2.widget.filter_statistic.label_help_text">
      <segment>
        <source>u2.widget.filter_statistic.label_help_text</source>
        <target>The saved filter name will be used if no label has been defined.</target>
      </segment>
    </unit>
    <unit id="3MuhrHI" name="u2.chart_type">
      <segment>
        <source>u2.chart_type</source>
        <target>Chart Type</target>
      </segment>
    </unit>
    <unit id=".aNZfZH" name="u2.widget.chart_type.pie">
      <segment>
        <source>u2.widget.chart_type.pie</source>
        <target>Pie</target>
      </segment>
    </unit>
    <unit id="oHo8PRn" name="u2.widget.chart_type.doughnut">
      <segment>
        <source>u2.widget.chart_type.doughnut</source>
        <target>Doughnut</target>
      </segment>
    </unit>
    <unit id="Ru_jVSm" name="u2.show_percent">
      <segment>
        <source>u2.show_percent</source>
        <target>Show Percent</target>
      </segment>
    </unit>
    <unit id="uCFAM98" name="u2.advanced">
      <segment>
        <source>u2.advanced</source>
        <target>Advanced</target>
      </segment>
    </unit>
    <unit id="HHgIrSu" name="u2.show_schema">
      <segment>
        <source>u2.show_schema</source>
        <target>Show schema</target>
      </segment>
    </unit>
    <unit id="nuovq0Q" name="u2.rules">
      <segment>
        <source>u2.rules</source>
        <target>Rules</target>
      </segment>
    </unit>
    <unit id="phR.P6y" name="u2.has_rules">
      <segment>
        <source>u2.has_rules</source>
        <target>Has rules</target>
      </segment>
    </unit>
    <unit id="BwwIsn_" name="u2_core.import.invalid_headers">
      <segment>
        <source>u2_core.import.invalid_headers</source>
        <target>One or more headers in the imported file are not valid: %invalidHeaders%. Valid headers are: %validHeaders%</target>
      </segment>
    </unit>
    <unit id="y1uTljI" name="u2.choose_your_own">
      <segment>
        <source>u2.choose_your_own</source>
        <target>Choose your own</target>
      </segment>
    </unit>
    <unit id="JDTMmLe" name="u2_core.edit_system_images">
      <segment>
        <source>u2_core.edit_system_images</source>
        <target>Edit System Images</target>
      </segment>
    </unit>
    <unit id="LpTAUMF" name="u2_core.system_images">
      <segment>
        <source>u2_core.system_images</source>
        <target>System Images</target>
      </segment>
    </unit>
    <unit id="s2aiuTQ" name="u2_core.last_activity">
      <segment>
        <source>u2_core.last_activity</source>
        <target>Last Activity</target>
      </segment>
    </unit>
    <unit id="jL2DV6b" name="u2.help_text">
      <segment>
        <source>u2.help_text</source>
        <target>Help text</target>
      </segment>
    </unit>
    <unit id="mD7_.B1" name="u2.unit_or_partner_unit">
      <segment>
        <source>u2.unit_or_partner_unit</source>
        <target>Either Unit or Partner Unit</target>
      </segment>
    </unit>
    <unit id="ui54KLR" name="u2_core.entities_cannot_be_created_in_closed_period">
      <segment>
        <source>u2_core.entities_cannot_be_created_in_closed_period</source>
        <target>Period “%period_name%” is closed. Entities with this period cannot be created.</target>
      </segment>
    </unit>
    <unit id="QxfYW1l" name="u2_core.entities_cannot_be_deleted_in_closed_period">
      <segment>
        <source>u2_core.entities_cannot_be_deleted_in_closed_period</source>
        <target>Period “%period_name%” is closed. Entities with this period cannot be deleted.</target>
      </segment>
    </unit>
    <unit id="r0aVPFP" name="u2.unit_view">
      <segment>
        <source>u2.unit_view</source>
        <target>Unit View</target>
      </segment>
    </unit>
    <unit id="f4t2SRT" name="u2.user.no_units_assigned_admin">
      <segment>
        <source>u2.user.no_units_assigned_admin</source>
        <target>Assign some here for the user to work with.</target>
      </segment>
    </unit>
    <unit id="49HwpvI" name="u2.assign_units">
      <segment>
        <source>u2.assign_units</source>
        <target>Assign Units</target>
      </segment>
    </unit>
    <unit id="gB4a6vq" name="u2.user_group.no_units_assigned_admin">
      <segment>
        <source>u2.user_group.no_units_assigned_admin</source>
        <target>Assign some here for the users of this user group to work with.</target>
      </segment>
    </unit>
    <unit id="uD6tj61" name="u2.no_units">
      <segment>
        <source>u2.no_units</source>
        <target>No Units</target>
      </segment>
    </unit>
    <unit id="5aLm.Gi" name="u2.no_users">
      <segment>
        <source>u2.no_users</source>
        <target>No Users</target>
      </segment>
    </unit>
    <unit id="T7KEKt4" name="u2.unit.no_users_assigned_description">
      <segment>
        <source>u2.unit.no_users_assigned_description</source>
        <target>It looks like this unit does not have users yet.</target>
      </segment>
    </unit>
    <unit id="rNcD38O" name="u2.contact_admin">
      <segment>
        <source>u2.contact_admin</source>
        <target>Contact an admin to make some changes for you.</target>
      </segment>
    </unit>
    <unit id="D0FS8L0" name="u2.unit.no_users_assigned_admin">
      <segment>
        <source>u2.unit.no_users_assigned_admin</source>
        <target>Assign some here to allow them to use this unit.</target>
      </segment>
    </unit>
    <unit id="DzmTpdG" name="u2.assign_users">
      <segment>
        <source>u2.assign_users</source>
        <target>Assign Users</target>
      </segment>
    </unit>
    <unit id="nFxyUnv" name="u2.no_user_groups">
      <segment>
        <source>u2.no_user_groups</source>
        <target>No User Groups</target>
      </segment>
    </unit>
    <unit id="u53s9U2" name="u2.unit.no_user_groups_assigned_description">
      <segment>
        <source>u2.unit.no_user_groups_assigned_description</source>
        <target>It looks like this unit does not have user groups yet.</target>
      </segment>
    </unit>
    <unit id="Fie51Li" name="u2.unit.no_user_groups_assigned_admin">
      <segment>
        <source>u2.unit.no_user_groups_assigned_admin</source>
        <target>Assign some here to allow users of the groups to work with this unit.</target>
      </segment>
    </unit>
    <unit id="1M515rL" name="u2.assign_user_groups">
      <segment>
        <source>u2.assign_user_groups</source>
        <target>Assign User Groups</target>
      </segment>
    </unit>
    <unit id="kyPVXvH" name="u2.authorization.no_users_assigned_description">
      <segment>
        <source>u2.authorization.no_users_assigned_description</source>
        <target>No users are currently assigned. Add users here to grant them this authorisation.</target>
      </segment>
    </unit>
    <unit id="mAkFdOY" name="u2.authorization_profile.no_user_groups_assigned_description">
      <segment>
        <source>u2.authorization_profile.no_user_groups_assigned_description</source>
        <target>No user groups are currently assigned. Add user groups here to grant their members the authorisations connected with this profile.</target>
      </segment>
    </unit>
    <unit id="0KBTIYZ" name="u2.authorization.no_user_groups_assigned_description">
      <segment>
        <source>u2.authorization.no_user_groups_assigned_description</source>
        <target>No user groups are currently assigned. Add user groups here to grant their members this authorisation.</target>
      </segment>
    </unit>
    <unit id="KdkhO3g" name="u2.authorization_profile.no_users_assigned_description">
      <segment>
        <source>u2.authorization_profile.no_users_assigned_description</source>
        <target>No users are currently assigned. Add users here to grant them the authorisations connected with this profile.</target>
      </segment>
    </unit>
    <unit id="9kSA0pD" name="u2.no_roles">
      <segment>
        <source>u2.no_roles</source>
        <target>No Roles</target>
      </segment>
    </unit>
    <unit id="_szxAlA" name="u2.user.no_roles_assigned_description">
      <segment>
        <source>u2.user.no_roles_assigned_description</source>
        <target>It looks like this user does not have roles.</target>
      </segment>
    </unit>
    <unit id="UAgbd0V" name="u2.user.no_roles_assigned_admin">
      <segment>
        <source>u2.user.no_roles_assigned_admin</source>
        <target>Assign roles to this user here.</target>
      </segment>
    </unit>
    <unit id="Y5SpKe4" name="u2.assign_roles">
      <segment>
        <source>u2.assign_roles</source>
        <target>Assign Roles</target>
      </segment>
    </unit>
    <unit id="tgTV7.C" name="u2.user_group.no_roles_assigned_description">
      <segment>
        <source>u2.user_group.no_roles_assigned_description</source>
        <target>It looks like this user group does not have roles. </target>
      </segment>
    </unit>
    <unit id="vc_m9nH" name="u2.user_group.no_roles_assigned_admin">
      <segment>
        <source>u2.user_group.no_roles_assigned_admin</source>
        <target>Assign roles to this user group here.</target>
      </segment>
    </unit>
    <unit id="734e.Wv" name="u2.saved_filter_subscription.no_user_groups_assigned_description">
      <segment>
        <source>u2.saved_filter_subscription.no_user_groups_assigned_description</source>
        <target>It looks like this subscription does not have any user groups. Add some here to send their members email updates.</target>
      </segment>
    </unit>
    <unit id="W_b0WPX" name="u2.saved_filter.no_users_assigned_description">
      <segment>
        <source>u2.saved_filter.no_users_assigned_description</source>
        <target>This saved filter does not have any users yet. Add some here to allow them to discover this filter.</target>
      </segment>
    </unit>
    <unit id="Wp78d7M" name="u2.add_users">
      <segment>
        <source>u2.add_users</source>
        <target>Add Users</target>
      </segment>
    </unit>
    <unit id="O8Leo9X" name="u2.saved_filter_subscription.no_users_assigned_description">
      <segment>
        <source>u2.saved_filter_subscription.no_users_assigned_description</source>
        <target>It looks like this subscription does not have any users. Add some here to start sending them updates via email.</target>
      </segment>
    </unit>
    <unit id="W9YGlCs" name="u2.saved_filter.no_user_groups_assigned_description">
      <segment>
        <source>u2.saved_filter.no_user_groups_assigned_description</source>
        <target>This saved filter does not have any groups added yet. Add some here to allow their members to discover this filter.</target>
      </segment>
    </unit>
    <unit id="VvkNbCJ" name="u2.dashboard.no_users_assigned_description">
      <segment>
        <source>u2.dashboard.no_users_assigned_description</source>
        <target>This dashboard does not currently have any assigned users. Assign some here to grant them access.</target>
      </segment>
    </unit>
    <unit id="Y9iuv9Y" name="u2.dashboard.no_user_groups_assigned_description">
      <segment>
        <source>u2.dashboard.no_user_groups_assigned_description</source>
        <target>This dashboard does not currently have any assigned user groups. Assign some here to grant their members access.</target>
      </segment>
    </unit>
    <unit id="i7.r397" name="u2.review.no_reviews">
      <segment>
        <source>u2.review.no_reviews</source>
        <target>No Reviews</target>
      </segment>
    </unit>
    <unit id="ToSI8w0" name="u2.review.task_not_reviewed">
      <segment>
        <source>u2.review.task_not_reviewed</source>
        <target>This task has not yet been reviewed.</target>
      </segment>
    </unit>
    <unit id="4_KJmFo" name="u2.review.manual_review_disabled_info">
      <segment>
        <source>u2.review.manual_review_disabled_info</source>
        <target>Manual review is not enabled for this workflow. Reviews will only be added during transitions using workflow actions.</target>
      </segment>
    </unit>
    <unit id="X2Ubnjm" name="u2.no_user_group_permissions">
      <segment>
        <source>u2.no_user_group_permissions</source>
        <target>No Group Permissions</target>
      </segment>
    </unit>
    <unit id="BtabVkN" name="u2.assign_user_group_permissions_admin">
      <segment>
        <source>u2.assign_user_group_permissions_admin</source>
        <target>Assign permissions to some user groups here.</target>
      </segment>
    </unit>
    <unit id="oAuYk0P" name="u2.assign_user_group_permissions">
      <segment>
        <source>u2.assign_user_group_permissions</source>
        <target>Assign Group Permissions</target>
      </segment>
    </unit>
    <unit id="Xjcsw3J" name="u2.no_user_permissions">
      <segment>
        <source>u2.no_user_permissions</source>
        <target>No User Permissions</target>
      </segment>
    </unit>
    <unit id="1V6z6pC" name="u2.assign_user_permissions_admin">
      <segment>
        <source>u2.assign_user_permissions_admin</source>
        <target>Assign permissions to some users here.</target>
      </segment>
    </unit>
    <unit id="HVQ1yAu" name="u2.assign_user_permissions">
      <segment>
        <source>u2.assign_user_permissions</source>
        <target>Assign User Permissions</target>
      </segment>
    </unit>
    <unit id="CjwUiPU" name="u2.datasheets.no_user_group_permissions_description">
      <segment>
        <source>u2.datasheets.no_user_group_permissions_description</source>
        <target>There are no group permissions set for this datasheet.</target>
      </segment>
    </unit>
    <unit id="5NfZgYc" name="u2.datasheets.datasheet_collection.no_user_group_permissions_description">
      <segment>
        <source>u2.datasheets.datasheet_collection.no_user_group_permissions_description</source>
        <target>There are no group permissions set for this datasheet collection.</target>
      </segment>
    </unit>
    <unit id="wm1M5nL" name="u2.document_template.no_user_group_permissions_description">
      <segment>
        <source>u2.document_template.no_user_group_permissions_description</source>
        <target>There are no group permissions set for this template.</target>
      </segment>
    </unit>
    <unit id="7OSjrm0" name="u2.document.no_user_group_permissions_description">
      <segment>
        <source>u2.document.no_user_group_permissions_description</source>
        <target>There are no group permissions set for this document.</target>
      </segment>
    </unit>
    <unit id="k8wWRXq" name="u2.datasheets.no_user_permissions_description">
      <segment>
        <source>u2.datasheets.no_user_permissions_description</source>
        <target>There are no user permissions set for this datasheet.</target>
      </segment>
    </unit>
    <unit id="b2vFEzg" name="u2.datasheets.datasheet_collection.no_user_permissions_description">
      <segment>
        <source>u2.datasheets.datasheet_collection.no_user_permissions_description</source>
        <target>There are no user permissions set for this datasheet collection.</target>
      </segment>
    </unit>
    <unit id="4R0uuYA" name="u2.document_template.no_user_permissions_description">
      <segment>
        <source>u2.document_template.no_user_permissions_description</source>
        <target>There are no user permissions set for this template.</target>
      </segment>
    </unit>
    <unit id="7J5JfOD" name="u2.document.no_user_permissions_description">
      <segment>
        <source>u2.document.no_user_permissions_description</source>
        <target>There are no user permissions set for this document.</target>
      </segment>
    </unit>
    <unit id="aV_T7Km" name="u2.audit_log.no_changes_description">
      <segment>
        <source>u2.audit_log.no_changes_description</source>
        <target>The updates will show up here</target>
      </segment>
    </unit>
    <unit id="a0baDWj" name="u2.no_attachments_description">
      <segment>
        <source>u2.no_attachments_description</source>
        <target>Any added attachments will show up here.</target>
      </segment>
    </unit>
    <unit id="MAA39OP" name="u2.user.no_user_groups_assigned_admin">
      <segment>
        <source>u2.user.no_user_groups_assigned_admin</source>
        <target>Assign some groups here.</target>
      </segment>
    </unit>
    <unit id="e6ZdDGv" name="u2.add_user_groups">
      <segment>
        <source>u2.add_user_groups</source>
        <target>Add User Groups</target>
      </segment>
    </unit>
    <unit id="d8aU_31" name="u2.task_checklist.no_checks_title">
      <segment>
        <source>u2.task_checklist.no_checks_title</source>
        <target>No Checks</target>
      </segment>
    </unit>
    <unit id="eHlpPIh" name="u2.user_group.no_units_assigned_description">
      <segment>
        <source>u2.user_group.no_units_assigned_description</source>
        <target>It looks like this user group does not have any units.</target>
      </segment>
    </unit>
    <unit id="gBPxBc3" name="u2.user_group.no_users_assigned_description">
      <segment>
        <source>u2.user_group.no_users_assigned_description</source>
        <target>It looks like this user group does not have any users.</target>
      </segment>
    </unit>
    <unit id="CP5E8xq" name="u2.user_group.no_users_assigned_admin">
      <segment>
        <source>u2.user_group.no_users_assigned_admin</source>
        <target>Assign users to this user group here.</target>
      </segment>
    </unit>
    <unit id="myQBOKY" name="u2.user.no_user_groups_assigned_description">
      <segment>
        <source>u2.user.no_user_groups_assigned_description</source>
        <target>It looks like this user does not have any user groups.</target>
      </segment>
    </unit>
    <unit id=".cSl9Xw" name="u2.user.no_units_assigned_description">
      <segment>
        <source>u2.user.no_units_assigned_description</source>
        <target>It looks like this user does not have any units yet.</target>
      </segment>
    </unit>
    <unit id="tm1iuvM" name="u2.new_record.save_first">
      <segment>
        <source>u2.new_record.save_first</source>
        <target>This functionality is available after creation. Save the record to continue.</target>
      </segment>
    </unit>
    <unit id="sBjd5yO" name="u2_core.add_unit.help_select_a_hierarchy_and_save">
      <segment>
        <source>u2_core.add_unit.help_select_a_hierarchy_and_save</source>
        <target>To add units, please select a hierarchy and save.</target>
      </segment>
    </unit>
    <unit id="Qy8aEbe" name="u2.no_authorisations">
      <segment>
        <source>u2.no_authorisations</source>
        <target>No Authorisations</target>
      </segment>
    </unit>
    <unit id="ukWBn5C" name="u2.user_group.no_authorisations_assigned_description">
      <segment>
        <source>u2.user_group.no_authorisations_assigned_description</source>
        <target>This user group does not have any authorisations assigned. </target>
      </segment>
    </unit>
    <unit id="wAuBFni" name="u2.user.no_authorisations_assigned_description">
      <segment>
        <source>u2.user.no_authorisations_assigned_description</source>
        <target>This user does not have any authorisations assigned.</target>
      </segment>
    </unit>
    <unit id="j_QE92A" name="u2.assign_authorisations">
      <segment>
        <source>u2.assign_authorisations</source>
        <target>Assign Authorisations</target>
      </segment>
    </unit>
    <unit id="Fh4Zdcr" name="u2.no_authorisations_assigned_admin">
      <segment>
        <source>u2.no_authorisations_assigned_admin</source>
        <target>Assign authorisations to see them here.</target>
      </segment>
    </unit>
    <unit id="y4GfIla" name="u2.datasheets.item.types.number">
      <segment>
        <source>u2.datasheets.item.types.number</source>
        <target>Number</target>
      </segment>
    </unit>
    <unit id="jEY26Mh" name="u2.unit.help">
      <segment>
        <source>u2.unit.help</source>
        <target>To select a unit, please uncheck the first checkbox and then choose from the list.</target>
      </segment>
    </unit>
    <unit id="G0ykFhr" name="u2.previous_period_is">
      <segment>
        <source>u2.previous_period_is</source>
        <target>The previous period is</target>
      </segment>
    </unit>
    <unit id="bQ5O27." name="u2.period_has_no_previous_period">
      <segment>
        <source>u2.period_has_no_previous_period</source>
        <target>This period has no previous period.</target>
      </segment>
    </unit>
    <unit id="V85xgDy" name="u2.copy">
      <segment>
        <source>u2.copy</source>
        <target>Copy</target>
      </segment>
    </unit>
    <unit id="Ve5Pocy" name="u2.paste">
      <segment>
        <source>u2.paste</source>
        <target>Paste</target>
      </segment>
    </unit>
    <unit id="tGeC1LR" name="u2.task_checklist.show_checks_other_status">
      <segment>
        <source>u2.task_checklist.show_checks_other_status</source>
        <target>Show checks for other statuses</target>
      </segment>
    </unit>
    <unit id="DLpQRhn" name="u2.datasheets.item_has_no_formula">
      <segment>
        <source>u2.datasheets.item_has_no_formula</source>
        <target>This item has no formula.</target>
      </segment>
    </unit>
    <unit id="JJNqAzu" name="u2_core.show_calendar">
      <segment>
        <source>u2_core.show_calendar</source>
        <target>Show Calendar</target>
      </segment>
    </unit>
    <unit id="Lh.H1Fb" name="u2.target_route_is_current_route">
      <segment>
        <source>u2.target_route_is_current_route</source>
        <target>You are already on this page</target>
      </segment>
    </unit>
    <unit id="wcBA3nw" name="u2.inspect.already_inspecting">
      <segment>
        <source>u2.inspect.already_inspecting</source>
        <target>You are currently inspecting this field.</target>
      </segment>
    </unit>
    <unit id="rl.2iLn" name="u2.password_must_have_min_password_length">
      <segment>
        <source>u2.password_must_have_min_password_length</source>
        <target>Must have a minimum length of %min_password_length% characters.</target>
      </segment>
    </unit>
    <unit id="XIGZruJ" name="u2.collapse">
      <segment>
        <source>u2.collapse</source>
        <target>Collapse</target>
      </segment>
    </unit>
    <unit id="kxZWQQy" name="u2.datasheets.item_country_report">
      <segment>
        <source>u2.datasheets.item_country_report</source>
        <target>Item Report: Country</target>
      </segment>
    </unit>
    <unit id="lpCohtF" name="u2.datasheets.choose_an_item_country_report">
      <segment>
        <source>u2.datasheets.choose_an_item_country_report</source>
        <target>Choose an Item Report: Country</target>
      </segment>
    </unit>
    <unit id="6lvucBz" name="u2.document.collapse_with_subsections">
      <segment>
        <source>u2.document.collapse_with_subsections</source>
        <target>Collapse with subsections</target>
      </segment>
    </unit>
    <unit id="hM3maaP" name="u2.document.expand_with_subsections">
      <segment>
        <source>u2.document.expand_with_subsections</source>
        <target>Expand with subsections</target>
      </segment>
    </unit>
    <unit id="bE0kMLV" name="u2.document.expand_all_sections">
      <segment>
        <source>u2.document.expand_all_sections</source>
        <target>Expand all sections</target>
      </segment>
    </unit>
    <unit id="GMWKlMJ" name="u2.document.collapse_all_sections">
      <segment>
        <source>u2.document.collapse_all_sections</source>
        <target>Collapse all sections</target>
      </segment>
    </unit>
    <unit id="KUE9_5i" name="u2.not_editable">
      <segment>
        <source>u2.not_editable</source>
        <target>Not editable</target>
      </segment>
    </unit>
    <unit id="r0Oqozy" name="u2.editable">
      <segment>
        <source>u2.editable</source>
        <target>Editable</target>
      </segment>
    </unit>
    <unit id="v.Uip9G" name="u2.document.section_controls">
      <segment>
        <source>u2.document.section_controls</source>
        <target>Section Controls</target>
      </segment>
    </unit>
    <unit id="nPhMKf_" name="u2.field_inspector.show">
      <segment>
        <source>u2.field_inspector.show</source>
        <target>Show inspector</target>
      </segment>
    </unit>
    <unit id="CqwfEru" name="u2.field_inspector">
      <segment>
        <source>u2.field_inspector</source>
        <target>Inspector</target>
      </segment>
    </unit>
    <unit id="mRS5lAt" name="u2.select_a_field">
      <segment>
        <source>u2.select_a_field</source>
        <target>Select a field</target>
      </segment>
    </unit>
    <unit id="pfPNave" name="u2.field_inspector.select_a_field">
      <segment>
        <source>u2.field_inspector.select_a_field</source>
        <target>Select a field in this datasheet to see more information about that field.</target>
      </segment>
    </unit>
    <unit id="nqHLq4b" name="u2.field_inspector.field_not_on_datasheet">
      <segment>
        <source>u2.field_inspector.field_not_on_datasheet</source>
        <target>This field is not located on this datasheet.</target>
      </segment>
    </unit>
    <unit id="Gvzszwg" name="u2.inspect">
      <segment>
        <source>u2.inspect</source>
        <target>Inspect</target>
      </segment>
    </unit>
    <unit id="aaIaPqc" name="u2.inspect.tooltip">
      <segment>
        <source>u2.inspect.tooltip</source>
        <target>Click here to see the field in the datasheet.</target>
      </segment>
    </unit>
    <unit id="JVw6un1" name="u2.paste.field_is_disabled">
      <segment>
        <source>u2.paste.field_is_disabled</source>
        <target>Paste not possible. This field is disabled.</target>
      </segment>
    </unit>
    <unit id="loqIezu" name="u2.template">
      <segment>
        <source>u2.template</source>
        <target>Template</target>
      </segment>
    </unit>
    <unit id="mOt6Xzh" name="u2.group">
      <segment>
        <source>u2.group</source>
        <target>Group</target>
      </segment>
    </unit>
    <unit id="phXmvrH" name="u2.more_than_one_unit_period_found_matching_this_configuration">
      <segment>
        <source>u2.more_than_one_unit_period_found_matching_this_configuration</source>
        <target>More than one task is assigned to this datasheet collection. Please ensure that only one task is assigned in order to allow edits.</target>
      </segment>
    </unit>
    <unit id="pcMqn3A" name="u2.task.assign_datasheet_collection">
      <segment>
        <source>u2.task.assign_datasheet_collection</source>
        <target>Assign datasheet collection</target>
      </segment>
    </unit>
    <unit id="I7a8RyZ" name="u2.unrestricted">
      <segment>
        <source>u2.unrestricted</source>
        <target>Unrestricted</target>
      </segment>
    </unit>
    <unit id="enx6zpN" name="u2.unrestricted_to_any_datasheet_collection">
      <segment>
        <source>u2.unrestricted_to_any_datasheet_collection</source>
        <target>This task is unrestricted. It will be available to the following collections:</target>
      </segment>
    </unit>
    <unit id="BChTn.7" name="u2.task.restrict">
      <segment>
        <source>u2.task.restrict</source>
        <target>Restrict</target>
      </segment>
    </unit>
    <unit id="H8lkHzf" name="u2.update_tasks">
      <segment>
        <source>u2.update_tasks</source>
        <target>Task configuration conflict</target>
      </segment>
    </unit>
    <unit id="1N9fN82" name="u2.show_on_list">
      <segment>
        <source>u2.show_on_list</source>
        <target>Show on list</target>
      </segment>
    </unit>
    <unit id="tH3mkwZ" name="u2.dashboard.widget.filter_results.the_following_invalid_columns">
      <segment>
        <source>u2.dashboard.widget.filter_results.the_following_invalid_columns</source>
        <target>The configuration contains the following invalid columns:</target>
      </segment>
    </unit>
    <unit id="M7r7U6Q" name="u2.dashboard.widget.filter_results.remove_invalid_columns">
      <segment>
        <source>u2.dashboard.widget.filter_results.remove_invalid_columns</source>
        <target>Remove invalid columns</target>
      </segment>
    </unit>
    <unit id="nWiD5z9" name="u2.task.datasheet_collection.unable_to_add_collection">
      <segment>
        <source>u2.task.datasheet_collection.unable_to_add_collection</source>
        <target>Datasheet collection "%collectionName%" could not be added to task "%taskName%".</target>
      </segment>
    </unit>
    <unit id="7WCzqmt" name="u2.task.datasheet_collection.unable_to_remove_collection">
      <segment>
        <source>u2.task.datasheet_collection.unable_to_remove_collection</source>
        <target>Datasheet collection "%collectionName%" could not be removed from task "%taskName%".</target>
      </segment>
    </unit>
    <unit id="2FxQ8A2" name="u2.datasheets.datasheet_collection">
      <segment>
        <source>u2.datasheets.datasheet_collection</source>
        <target>Datasheet Collection</target>
      </segment>
    </unit>
    <unit id="LW3Dh6v" name="Undrawn credit facilities">
      <segment>
        <source>Undrawn credit facilities</source>
        <target>Undrawn credit facilities</target>
      </segment>
    </unit>
    <unit id="WdElRTq" name="u2.datasheets.collection.plural">
      <segment>
        <source>u2.datasheets.collection.plural</source>
        <target>Collections</target>
      </segment>
    </unit>
    <unit id="KDKVySD" name="u2.datasheets.sheet.plural">
      <segment>
        <source>u2.datasheets.sheet.plural</source>
        <target>Sheets</target>
      </segment>
    </unit>
    <unit id="Jqz5JUC" name="u2.document_template.field_disabled_choose_file_first">
      <segment>
        <source>u2.document_template.field_disabled_choose_file_first</source>
        <target>You need to select a file first before you can edit the name and description.</target>
      </segment>
    </unit>
    <unit id="wp4jo9T" name="u2_document.cannot_perform_action_while_editing">
      <segment>
        <source>u2_document.cannot_perform_action_while_editing</source>
        <target>This action cannot be performed while editing sections</target>
      </segment>
    </unit>
    <unit id="LLgcjVQ" name="u2.route.deprecated">
      <segment>
        <source>u2.route.deprecated</source>
        <target>You have been redirected because the given URL no longer exists. Please update any bookmarks to use the current URL instead.</target>
      </segment>
    </unit>
    <unit id="608oTt1" name="u2.field_inspector.configure_field_inspector">
      <segment>
        <source>u2.field_inspector.configure_field_inspector</source>
        <target>Configure field inspector</target>
      </segment>
    </unit>
    <unit id="8PnJYJc" name="u2.field_inspector.show_field_select">
      <segment>
        <source>u2.field_inspector.show_field_select</source>
        <target>Show field selector</target>
      </segment>
    </unit>
    <unit id="ioCSEGC" name="u2.field_inspector.show_colors">
      <segment>
        <source>u2.field_inspector.show_colors</source>
        <target>Show colours</target>
      </segment>
    </unit>
    <unit id="Kt3V0ZM" name="u2.watchers.view">
      <segment>
        <source>u2.watchers.view</source>
        <target>Show watchers</target>
      </segment>
    </unit>
    <unit id="LZSErHP" name="u2_structureddocument.new_section_content">
      <segment>
        <source>u2_structureddocument.new_section_content</source>
        <target>New section content</target>
      </segment>
    </unit>
    <unit id="W..8acd" name="u2_structureddocument.new_section">
      <segment>
        <source>u2_structureddocument.new_section</source>
        <target>New section</target>
      </segment>
    </unit>
    <unit id="6l9GNJF" name="u2.item.formula.element.previous_period_value">
      <segment>
        <source>u2.item.formula.element.previous_period_value</source>
        <target>The value of this item is taken from the previous period.</target>
      </segment>
    </unit>
    <unit id="cAykyB1" name="u2_structureddocument.add_section">
      <segment>
        <source>u2_structureddocument.add_section</source>
        <target>Add Section</target>
      </segment>
    </unit>
    <unit id="VpeJQL_" name="u2.inspect.insufficient_context">
      <segment>
        <source>u2.inspect.insufficient_context</source>
        <target>It is not possible to navigate in the current context.</target>
      </segment>
    </unit>
    <unit id="ATcgApV" name="u2.field_inspector.show_values">
      <segment>
        <source>u2.field_inspector.show_values</source>
        <target>Show values</target>
      </segment>
    </unit>
    <unit id="1MUSZ.p" name="u2.field.click_to_create">
      <segment>
        <source>u2.field.click_to_create</source>
        <target>Click to create this field </target>
      </segment>
    </unit>
    <unit id="x9Z2DLK" name="u2.datasheets.unassigned_fields">
      <segment>
        <source>u2.datasheets.unassigned_fields</source>
        <target>Unassigned fields</target>
      </segment>
    </unit>
    <unit id="jRO3zN2" name="u2.datasheets.missing_template">
      <segment>
        <source>u2.datasheets.missing_template</source>
        <target>This datasheet has no template. Therefore, all fields are marked as unassigned.</target>
      </segment>
    </unit>
    <unit id="SPUDZlK" name="u2.datasheets.all_fields_assigned">
      <segment>
        <source>u2.datasheets.all_fields_assigned</source>
        <target>All fields are used in the layout</target>
      </segment>
    </unit>
    <unit id="o172hw_" name="u2.datasheets.field_disabled">
      <segment>
        <source>u2.datasheets.field_disabled</source>
        <target>This field is disabled.</target>
      </segment>
    </unit>
    <unit id="tvjn8Ap" name="u2.field.name_placeholder">
      <segment>
        <source>u2.field.name_placeholder</source>
        <target>Search to view suggestions</target>
      </segment>
    </unit>
    <unit id="aFAYayt" name="u2.datasheets.missing_fields">
      <segment>
        <source>u2.datasheets.missing_fields</source>
        <target>Missing fields</target>
      </segment>
    </unit>
    <unit id="DkfIH_G" name="u2.no_address">
      <segment>
        <source>u2.no_address</source>
        <target>No address has been provided</target>
      </segment>
    </unit>
    <unit id="wVQ62ko" name="u2.enter_address">
      <segment>
        <source>u2.enter_address</source>
        <target>Enter an address</target>
      </segment>
    </unit>
    <unit id="f_40zWa" name="u2.remove_address">
      <segment>
        <source>u2.remove_address</source>
        <target>Remove address</target>
      </segment>
    </unit>
    <unit id="aNkMQi3" name="u2.datasheet.missing_field">
      <segment>
        <source>u2.datasheet.missing_field</source>
        <target>Missing field</target>
      </segment>
    </unit>
    <unit id="4HdLaN0" name="u2.off_canvas_menu">
      <segment>
        <source>u2.off_canvas_menu</source>
        <target>Off-Canvas Menu</target>
      </segment>
    </unit>
    <unit id="N6ftt_e" name="u2.warning_unsaved_changes_will_be_lost">
      <segment>
        <source>u2.warning_unsaved_changes_will_be_lost</source>
        <target>Warning: Unsaved changes will be lost.</target>
      </segment>
    </unit>
    <unit id="JJKyWpJ" name="u2.global_search">
      <segment>
        <source>u2.global_search</source>
        <target>Global Search</target>
      </segment>
    </unit>
    <unit id="TENMzpK" name="u2.global_search.description">
      <segment>
        <source>u2.global_search.description</source>
        <target>Search globally</target>
      </segment>
    </unit>
    <unit id="Qcrs1ZB" name="u2.transaction.transaction_volume">
      <segment>
        <source>u2.transaction.transaction_volume</source>
        <target>Quantity</target>
      </segment>
    </unit>
    <unit id="5FxDqF_" name="u2.transaction.underlying_contract">
      <segment>
        <source>u2.transaction.underlying_contract</source>
        <target>Underlying Contract</target>
      </segment>
    </unit>
    <unit id="TOTHQGY" name="u2.transaction.unit_standard_taxation_applicable">
      <segment>
        <source>u2.transaction.unit_standard_taxation_applicable</source>
        <target>Standard taxation applicable</target>
      </segment>
    </unit>
    <unit id="lIXR8.b" name="u2.transaction.partner_unit_standard_taxation_applicable">
      <segment>
        <source>u2.transaction.partner_unit_standard_taxation_applicable</source>
        <target>Standard taxation applicable</target>
      </segment>
    </unit>
    <unit id="hpM64x2" name="u2.transaction.unit_standard_taxation_applicable.help">
      <segment>
        <source>u2.transaction.unit_standard_taxation_applicable.help</source>
        <target>The business transaction is subject to standard taxation in the relevant tax jurisdiction.</target>
      </segment>
    </unit>
    <unit id="fUREMD1" name="u2.transaction.partner_unit_standard_taxation_applicable.help">
      <segment>
        <source>u2.transaction.partner_unit_standard_taxation_applicable.help</source>
        <target>The business transaction is subject to standard taxation in the relevant tax jurisdiction.</target>
      </segment>
    </unit>
    <unit id=".39ewS9" name="u2.transaction.transaction_volume.help">
      <segment>
        <source>u2.transaction.transaction_volume.help</source>
        <target>e.g. 1000 Kg, 1000 pieces, n/a</target>
      </segment>
    </unit>
    <unit id="nY.F7ND" name="u2.transaction.underlying_contract.help">
      <segment>
        <source>u2.transaction.underlying_contract.help</source>
        <target>Reference to underlying contract (e.g. contract number)</target>
      </segment>
    </unit>
    <unit id="NvA7c2B" name="u2.no_matching_option_found">
      <segment>
        <source>u2.no_matching_option_found</source>
        <target>Unknown selected option</target>
      </segment>
    </unit>
    <unit id="8mnx1Mw" name="u2.no_matching_option_found.help">
      <segment>
        <source>u2.no_matching_option_found.help</source>
        <target>Either the resource was not found or you do not have the required permissions to view it.</target>
      </segment>
    </unit>
    <unit id="misc2vm" name="u2.no_description">
      <segment>
        <source>u2.no_description</source>
        <target>This record has no description.</target>
      </segment>
    </unit>
    <unit id="LT9XMRN" name="u2.datasheet.missing_configuration">
      <segment>
        <source>u2.datasheet.missing_configuration</source>
        <target>Unable to display the template due to missing parameters.</target>
      </segment>
    </unit>
    <unit id="G5OP5sr" name="u2.security.roles.period_manager">
      <segment>
        <source>u2.security.roles.period_manager</source>
        <target>Period Manager</target>
      </segment>
    </unit>
    <unit id="TksCzfv" name="u2.security.roles.unit_manager">
      <segment>
        <source>u2.security.roles.unit_manager</source>
        <target>Unit Manager</target>
      </segment>
    </unit>
  </file>
</xliff>
