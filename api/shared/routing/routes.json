{"base_url": "", "routes": {"u2_attachment_view": {"tokens": [["text", "/view"], ["variable", "/", "\\d+", "id", true], ["text", "/file"], ["variable", "/", "\\d+", "entityId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy"]], "defaults": [], "requirements": {"entityId": "\\d+", "id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_attachmentsappendix_downloadarchive": {"tokens": [["text", "/download-archive"], ["variable", "/", "\\d+", "documentId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/appendix/attachments"]], "defaults": [], "requirements": {"documentId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_bulktransition_prepare": {"tokens": [["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/bulk-transition"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_bulktransition_transition": {"tokens": [["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/bulk-transition"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_calendar_weekjson": {"tokens": [["text", "/legacy/calendar/week.json"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_calendar_newentrytypedialogjson": {"tokens": [["text", "/legacy/calendar/new-entry-type-dialog.json"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_calendar_upnext": {"tokens": [["text", "/legacy/calendar/up-next"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_calendar_currentweek": {"tokens": [["text", "/legacy/calendar/current-week"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_configurationdata_sidebar": {"tokens": [["text", "/legacy/menu/configuration-data"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": [], "schemes": []}, "u2_layouttemplate_deletetemplate": {"tokens": [["text", "/delete-template"], ["variable", "/", "\\d+", "id", true], ["text", "/legacy/tax-accounting/layout"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["DELETE"], "schemes": []}, "u2_layouttemplate_downloadtemplate": {"tokens": [["text", "/download-template"], ["variable", "/", "\\d+", "id", true], ["text", "/legacy/tax-accounting/layout"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_datasheets_unithierarchyview_export": {"tokens": [["text", "/legacy/tax-accounting/group-view/export"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_layout_unitviewdata": {"tokens": [["text", "/legacy/tax-accounting/unit-view/data"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_layout_unitviewsubmit": {"tokens": [["text", "/legacy/tax-accounting/unit-view/submit"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_datasheets_unitview_export": {"tokens": [["text", "/legacy/tax-accounting/unit-view/export"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_update": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_documentsection_include": {"tokens": [["text", "/include"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_exclude": {"tokens": [["text", "/exclude"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_allowedit": {"tokens": [["text", "/allow-edit"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_preventedit": {"tokens": [["text", "/prevent-edit"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_require": {"tokens": [["text", "/require"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_unrequire": {"tokens": [["text", "/un-require"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_informationjson": {"tokens": [["text", "/information.json"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_documentsection_move": {"tokens": [["text", "/move"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_documentsection_create": {"tokens": [["variable", "/", "\\d+", "referenceSectionId", true], ["variable", "/", "(?:after|before|subsection-of)", "placement", true], ["text", "/new"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"placement": "(after|before|subsection-of)", "referenceSectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_documentsection_newinitialsection": {"tokens": [["variable", "/", "\\d+", "documentId", true], ["text", "/create-initial-section-for-document"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"documentId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_documentsection_remove": {"tokens": [["text", "/remove"], ["variable", "/", "\\d+", "sectionId", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/structured-document/section"]], "defaults": [], "requirements": {"sectionId": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_documentwidget_editorconfiguration": {"tokens": [["text", "/legacy/document-widget/configuration"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_dynamicassets_corplogo": {"tokens": [["text", "/legacy/dynamic-assets/corp-logo"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_entityinformation_listinfo": {"tokens": [["text", "/list-info"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_entityinformation_entityinformation": {"tokens": [["text", "/entity-information"], ["variable", "/", "[^/]++", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_entityinformation_entitymetadata": {"tokens": [["text", "/entity-metatdata"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_file_download": {"tokens": [["text", "/download"], ["variable", "/", "\\d+", "id", true], ["text", "/legacy/file"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_insecuredata__invoke": {"tokens": [["text", "/legacy/insecure/app-data"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": [], "schemes": []}, "u2_itemunitvalue_export": {"tokens": [["variable", "/", "[^/]++", "id", true], ["text", "/legacy/tax-accounting/item-unit-value/export/period"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_itemunitvalue_exportforunit": {"tokens": [["variable", "/", "[^/]++", "unitId", true], ["text", "/unit"], ["variable", "/", "[^/]++", "periodId", true], ["text", "/legacy/tax-accounting/item-unit-value/export/period"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_menu_mainjson": {"tokens": [["text", "/legacy/menu/main.json"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_menu_userjson": {"tokens": [["text", "/legacy/menu/user.json"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_refreshjwt__invoke": {"tokens": [["text", "/legacy/refresh-jwt"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": [], "schemes": []}, "u2_tasktype_renderdocumentsections": {"tokens": [["text", "/content"], ["variable", "/", "[^/]++", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_savedfiltersubscriptions_forcerun": {"tokens": [["text", "/force-run"], ["variable", "/", "[^/]++", "id", true], ["text", "/legacy/subscriptions/saved-filter"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_systemmessagedisplay_getmessages": {"tokens": [["text", "/legacy/system-message-display/get-messages"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_systemmessagedisplay_hidemessage": {"tokens": [["variable", "/", "[^/]++", "id", true], ["text", "/legacy/system-message-display/hide"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_tasktype_duplicate": {"tokens": [["text", "/duplicate"], ["variable", "/", "[^/]++", "id", true], ["text", "/legacy/task"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_tasktype_editdocument_data": {"tokens": [["text", "/edit-document/data"], ["variable", "/", "[^/]++", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_newform": {"tokens": [["text", "/new/form"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_submitnewform": {"tokens": [["text", "/new/form"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_tasktype_editform": {"tokens": [["text", "/edit/form"], ["variable", "/", "\\d+", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_submiteditform": {"tokens": [["text", "/edit/form"], ["variable", "/", "\\d+", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_tasktype_listcsv": {"tokens": [["text", "/list.csv"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_listjson": {"tokens": [["text", "/list.json"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_listxlsx": {"tokens": [["text", "/list.xlsx"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_pdfdownload": {"tokens": [["text", "/pdf-download"], ["variable", "/", "\\d+", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_xmldownload": {"tokens": [["text", "/export/xml"], ["variable", "/", "\\d+", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy/tasktype"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "table_bundle_uql_generate_from_filter": {"tokens": [["text", "/legacy/uql/generateFromFilter"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_user_unlock": {"tokens": [["text", "/unlock"], ["variable", "/", "[^/]++", "id", true], ["text", "/legacy/user"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_user_userrightsjson": {"tokens": [["text", "/rights-list.json"], ["variable", "/", "[^/]++", "id", true], ["text", "/legacy/user"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_usergroup_usergrouprightsjson": {"tokens": [["text", "/rights-list.json"], ["variable", "/", "[^/]++", "id", true], ["text", "/legacy/user-group"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_watch_watch": {"tokens": [["text", "/watch"], ["variable", "/", "[^/]++", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/legacy"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_xbrl_bmf_downloadlistjson": {"tokens": [["text", "/bmf/country-list.json"], ["variable", "/", "\\d+", "id", true], ["text", "/legacy/tpm/country-by-country-report"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_about": {"tokens": [["text", "/api/about"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": [], "schemes": []}, "u2_bulk_delete": {"tokens": [["text", "/delete"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/api/bulk"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["DELETE"], "schemes": []}, "u2_bulk_editform": {"tokens": [["text", "/edit/form"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/api/bulk"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_bulk_submiteditform": {"tokens": [["text", "/edit"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/api/bulk"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_dashboardwidget_filterresults": {"tokens": [["text", "/api/dashboard/widget/filter-results"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_dashboardwidget_filterstatistics": {"tokens": [["text", "/api/dashboards/widgets/filter-statistics"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_task_fields": {"tokens": [["text", "/api/tasks-fields"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_tasktype_delete": {"tokens": [["text", "/delete"], ["variable", "/", "\\d+", "id", true], ["variable", "/", "[^/]++", "shortName", true], ["text", "/api/tasktype"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["DELETE"], "schemes": []}, "u2_tasktype_parseUql": {"tokens": [["text", "/table"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/api/tasktype"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_tasktype_table": {"tokens": [["text", "/table"], ["variable", "/", "[^/]++", "shortName", true], ["text", "/api/tasktype"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_unitperiod_recalculate": {"tokens": [["text", "/recalculate"], ["variable", "/", "[^/]++", "period", true], ["text", "/period"], ["variable", "/", "[^/]++", "unit", true], ["text", "/api/tax-accounting/unit"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "u2_apm_apmtransaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/apm/transaction"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_contract_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/contract-management/contract"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt1transaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/igt/transaction/igt1"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt2transaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/igt/transaction/igt2"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt3transaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/igt/transaction/igt3"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt4transaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/igt/transaction/igt4"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt5transaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/igt/transaction/igt5"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_incometaxplanning_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/income-tax-planning"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_losscarryforward_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/loss-carry-forward"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentstatus_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-assessment-status"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauditrisk_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-audit-risk"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxconsultingfee_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-consulting-fee"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxcredit_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-credit"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxlitigation_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-litigation"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrate_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-rate"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrelevantrestriction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/tax-relevant-restriction"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transferpricing_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tam/transfer-pricing"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_otherdeadline_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tcm/other-deadline"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentmonitor_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tcm/tax-assessment-monitor"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauthorityauditobjection_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tcm/tax-authority-audit-objection"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxfilingmonitor_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tcm/tax-filing-monitor"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_countrybycountryreport_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tpm/country-by-country-report"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_financialdata_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tpm/financial-data"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_localfile_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tpm/local-file"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_mainbusinessactivity_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tpm/main-business-activity"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_masterfile_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tpm/master-file"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transaction_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tpm/transaction"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_unitperiod_list": {"tokens": [["variable", ".", "html", "format", true], ["text", "/tax-accounting/unit-period"]], "defaults": {"format": "html"}, "requirements": {"format": "html", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_apm_apmtransaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/apm/transaction"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_contract_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/contract-management/contract"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt1transaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/igt/transaction/igt1"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt2transaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/igt/transaction/igt2"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt3transaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/igt/transaction/igt3"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt4transaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/igt/transaction/igt4"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt5transaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/igt/transaction/igt5"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_incometaxplanning_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/income-tax-planning"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_losscarryforward_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/loss-carry-forward"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentstatus_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-assessment-status"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauditrisk_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-audit-risk"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxconsultingfee_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-consulting-fee"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxcredit_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-credit"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxlitigation_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-litigation"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrate_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-rate"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrelevantrestriction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/tax-relevant-restriction"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transferpricing_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tam/transfer-pricing"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_otherdeadline_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tcm/other-deadline"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentmonitor_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tcm/tax-assessment-monitor"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauthorityauditobjection_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tcm/tax-authority-audit-objection"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxfilingmonitor_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tcm/tax-filing-monitor"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_countrybycountryreport_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tpm/country-by-country-report"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_financialdata_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tpm/financial-data"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_localfile_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tpm/local-file"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_mainbusinessactivity_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tpm/main-business-activity"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_masterfile_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tpm/master-file"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transaction_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tpm/transaction"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_unitperiod_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "id", true], ["text", "/tax-accounting/unit-period"]], "defaults": [], "requirements": {"id": "\\d+", "_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_apm_apmtransaction_new": {"tokens": [["text", "/apm/transaction/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_contract_new": {"tokens": [["text", "/contract-management/contract/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt1transaction_new": {"tokens": [["text", "/igt/transaction/igt1/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt2transaction_new": {"tokens": [["text", "/igt/transaction/igt2/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt3transaction_new": {"tokens": [["text", "/igt/transaction/igt3/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt4transaction_new": {"tokens": [["text", "/igt/transaction/igt4/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt5transaction_new": {"tokens": [["text", "/igt/transaction/igt5/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_incometaxplanning_new": {"tokens": [["text", "/tam/income-tax-planning/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_losscarryforward_new": {"tokens": [["text", "/tam/loss-carry-forward/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentstatus_new": {"tokens": [["text", "/tam/tax-assessment-status/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauditrisk_new": {"tokens": [["text", "/tam/tax-audit-risk/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxconsultingfee_new": {"tokens": [["text", "/tam/tax-consulting-fee/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxcredit_new": {"tokens": [["text", "/tam/tax-credit/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxlitigation_new": {"tokens": [["text", "/tam/tax-litigation/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrate_new": {"tokens": [["text", "/tam/tax-rate/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrelevantrestriction_new": {"tokens": [["text", "/tam/tax-relevant-restriction/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transferpricing_new": {"tokens": [["text", "/tam/transfer-pricing/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_otherdeadline_new": {"tokens": [["text", "/tcm/other-deadline/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentmonitor_new": {"tokens": [["text", "/tcm/tax-assessment-monitor/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauthorityauditobjection_new": {"tokens": [["text", "/tcm/tax-authority-audit-objection/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxfilingmonitor_new": {"tokens": [["text", "/tcm/tax-filing-monitor/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_countrybycountryreport_new": {"tokens": [["text", "/tpm/country-by-country-report/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_financialdata_new": {"tokens": [["text", "/tpm/financial-data/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_localfile_new": {"tokens": [["text", "/tpm/local-file/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_mainbusinessactivity_new": {"tokens": [["text", "/tpm/main-business-activity/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_masterfile_new": {"tokens": [["text", "/tpm/master-file/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transaction_new": {"tokens": [["text", "/tpm/transaction/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_unitperiod_new": {"tokens": [["text", "/tax-accounting/unit-period/new"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_apm_apmtransaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/apm/transaction"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_contract_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/contract-management/contract"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt1transaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/igt/transaction/igt1"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt2transaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/igt/transaction/igt2"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt3transaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/igt/transaction/igt3"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt4transaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/igt/transaction/igt4"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_igt_igt5transaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/igt/transaction/igt5"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_incometaxplanning_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/income-tax-planning"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_losscarryforward_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/loss-carry-forward"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentstatus_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-assessment-status"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauditrisk_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-audit-risk"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxconsultingfee_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-consulting-fee"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxcredit_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-credit"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxlitigation_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-litigation"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrate_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-rate"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxrelevantrestriction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/tax-relevant-restriction"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transferpricing_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tam/transfer-pricing"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_otherdeadline_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tcm/other-deadline"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxassessmentmonitor_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tcm/tax-assessment-monitor"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxauthorityauditobjection_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tcm/tax-authority-audit-objection"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_taxfilingmonitor_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tcm/tax-filing-monitor"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_countrybycountryreport_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tpm/country-by-country-report"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_financialdata_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tpm/financial-data"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_localfile_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tpm/local-file"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_mainbusinessactivity_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tpm/main-business-activity"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_masterfile_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tpm/master-file"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_transaction_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tpm/transaction"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "u2_unitperiod_editdocument": {"tokens": [["text", "/edit-document"], ["variable", "/", "[^/]++", "id", true], ["text", "/tax-accounting/unit-period"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "two_factor_authentication_login_check": {"tokens": [["text", "/legacy/security/two-factor/check"]], "defaults": [], "requirements": {"_locale": "de|en"}, "hosttokens": [], "methods": [], "schemes": []}}, "prefix": "", "host": "u2.localhost", "port": "", "scheme": "https", "locale": ""}