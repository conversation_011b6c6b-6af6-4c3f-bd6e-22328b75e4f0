{"translations": {"en": {"validators": {"This value should be false.": "This value should be false.", "This value should be true.": "This value should be true.", "This value should be of type {{ type }}.": "This value should be of type {{ type }}.", "This value should be blank.": "This value should be blank.", "The value you selected is not a valid choice.": "The value you selected is not a valid choice.", "You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.": "You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.", "You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices.": "You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices.", "One or more of the given values is invalid.": "One or more of the given values is invalid.", "This field was not expected.": "This field was not expected.", "This field is missing.": "This field is missing.", "This value is not a valid date.": "This value is not a valid date.", "This value is not a valid datetime.": "This value is not a valid datetime.", "This value is not a valid email address.": "This value is not a valid email address.", "The file could not be found.": "The file could not be found.", "The file is not readable.": "The file is not readable.", "The file is too large ({{ size }} {{ suffix }}). Allowed maximum size is {{ limit }} {{ suffix }}.": "The file is too large ({{ size }} {{ suffix }}). Allowed maximum size is {{ limit }} {{ suffix }}.", "The mime type of the file is invalid ({{ type }}). Allowed mime types are {{ types }}.": "The mime type of the file is invalid ({{ type }}). Allowed mime types are {{ types }}.", "This value should be {{ limit }} or less.": "This value should be {{ limit }} or less.", "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.": "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.", "This value should be {{ limit }} or more.": "This value should be {{ limit }} or more.", "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.": "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.", "This value should not be blank.": "This value should not be blank.", "This value should not be null.": "This value should not be null.", "This value should be null.": "This value should be null.", "This value is not valid.": "This value is not valid.", "This value is not a valid time.": "This value is not a valid time.", "This value is not a valid URL.": "This value is not a valid URL.", "The two values should be equal.": "The two values should be equal.", "The file is too large. Allowed maximum size is {{ limit }} {{ suffix }}.": "The file is too large. Allowed maximum size is {{ limit }} {{ suffix }}.", "The file is too large.": "The file is too large.", "The file could not be uploaded.": "The file could not be uploaded.", "This value should be a valid number.": "This value should be a valid number.", "This file is not a valid image.": "This file is not a valid image.", "This is not a valid IP address.": "This value is not a valid IP address.", "This value is not a valid language.": "This value is not a valid language.", "This value is not a valid locale.": "This value is not a valid locale.", "This value is not a valid country.": "This value is not a valid country.", "This value is already used.": "This value is already used.", "The size of the image could not be detected.": "The size of the image could not be detected.", "The image width is too big ({{ width }}px). Allowed maximum width is {{ max_width }}px.": "The image width is too big ({{ width }}px). Allowed maximum width is {{ max_width }}px.", "The image width is too small ({{ width }}px). Minimum width expected is {{ min_width }}px.": "The image width is too small ({{ width }}px). Minimum width expected is {{ min_width }}px.", "The image height is too big ({{ height }}px). Allowed maximum height is {{ max_height }}px.": "The image height is too big ({{ height }}px). Allowed maximum height is {{ max_height }}px.", "The image height is too small ({{ height }}px). Minimum height expected is {{ min_height }}px.": "The image height is too small ({{ height }}px). Minimum height expected is {{ min_height }}px.", "This value should be the user's current password.": "This value should be the user's current password.", "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.": "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.", "The file was only partially uploaded.": "The file was only partially uploaded.", "No file was uploaded.": "No file was uploaded.", "No temporary folder was configured in php.ini.": "No temporary folder was configured in php.ini, or the configured folder does not exist.", "Cannot write temporary file to disk.": "Cannot write temporary file to disk.", "A PHP extension caused the upload to fail.": "A PHP extension caused the upload to fail.", "This collection should contain {{ limit }} element or more.|This collection should contain {{ limit }} elements or more.": "This collection should contain {{ limit }} element or more.|This collection should contain {{ limit }} elements or more.", "This collection should contain {{ limit }} element or less.|This collection should contain {{ limit }} elements or less.": "This collection should contain {{ limit }} element or less.|This collection should contain {{ limit }} elements or less.", "This collection should contain exactly {{ limit }} element.|This collection should contain exactly {{ limit }} elements.": "This collection should contain exactly {{ limit }} element.|This collection should contain exactly {{ limit }} elements.", "Invalid card number.": "Invalid card number.", "Unsupported card type or invalid card number.": "Unsupported card type or invalid card number.", "This is not a valid International Bank Account Number (IBAN).": "This value is not a valid International Bank Account Number (IBAN).", "This value is not a valid ISBN-10.": "This value is not a valid ISBN-10.", "This value is not a valid ISBN-13.": "This value is not a valid ISBN-13.", "This value is neither a valid ISBN-10 nor a valid ISBN-13.": "This value is neither a valid ISBN-10 nor a valid ISBN-13.", "This value is not a valid ISSN.": "This value is not a valid ISSN.", "This value is not a valid currency.": "This value is not a valid currency.", "This value should be equal to {{ compared_value }}.": "This value should be equal to {{ compared_value }}.", "This value should be greater than {{ compared_value }}.": "This value should be greater than {{ compared_value }}.", "This value should be greater than or equal to {{ compared_value }}.": "This value should be greater than or equal to {{ compared_value }}.", "This value should be identical to {{ compared_value_type }} {{ compared_value }}.": "This value should be identical to {{ compared_value_type }} {{ compared_value }}.", "This value should be less than {{ compared_value }}.": "This value should be less than {{ compared_value }}.", "This value should be less than or equal to {{ compared_value }}.": "This value should be less than or equal to {{ compared_value }}.", "This value should not be equal to {{ compared_value }}.": "This value should not be equal to {{ compared_value }}.", "This value should not be identical to {{ compared_value_type }} {{ compared_value }}.": "This value should not be identical to {{ compared_value_type }} {{ compared_value }}.", "The image ratio is too big ({{ ratio }}). Allowed maximum ratio is {{ max_ratio }}.": "The image ratio is too big ({{ ratio }}). Allowed maximum ratio is {{ max_ratio }}.", "The image ratio is too small ({{ ratio }}). Minimum ratio expected is {{ min_ratio }}.": "The image ratio is too small ({{ ratio }}). Minimum ratio expected is {{ min_ratio }}.", "The image is square ({{ width }}x{{ height }}px). Square images are not allowed.": "The image is square ({{ width }}x{{ height }}px). Square images are not allowed.", "The image is landscape oriented ({{ width }}x{{ height }}px). Landscape oriented images are not allowed.": "The image is landscape oriented ({{ width }}x{{ height }}px). Landscape oriented images are not allowed.", "The image is portrait oriented ({{ width }}x{{ height }}px). Portrait oriented images are not allowed.": "The image is portrait oriented ({{ width }}x{{ height }}px). Portrait oriented images are not allowed.", "An empty file is not allowed.": "An empty file is not allowed.", "The host could not be resolved.": "The host could not be resolved.", "This value does not match the expected {{ charset }} charset.": "This value does not match the expected {{ charset }} charset.", "This is not a valid Business Identifier Code (BIC).": "This value is not a valid Business Identifier Code (BIC).", "Error": "Error", "This is not a valid UUID.": "This value is not a valid UUID.", "This value should be a multiple of {{ compared_value }}.": "This value should be a multiple of {{ compared_value }}.", "This Business Identifier Code (BIC) is not associated with IBAN {{ iban }}.": "This Business Identifier Code (BIC) is not associated with IBAN {{ iban }}.", "This value should be valid JSON.": "This value should be valid JSON.", "This collection should contain only unique elements.": "This collection should contain only unique elements.", "This value should be positive.": "This value should be positive.", "This value should be either positive or zero.": "This value should be either positive or zero.", "This value should be negative.": "This value should be negative.", "This value should be either negative or zero.": "This value should be either negative or zero.", "This value is not a valid timezone.": "This value is not a valid timezone.", "This password has been leaked in a data breach, it must not be used. Please use another password.": "This password has been leaked in a data breach, it must not be used. Please use another password.", "This value should be between {{ min }} and {{ max }}.": "This value should be between {{ min }} and {{ max }}.", "This value is not a valid hostname.": "This value is not a valid hostname.", "The number of elements in this collection should be a multiple of {{ compared_value }}.": "The number of elements in this collection should be a multiple of {{ compared_value }}.", "This value should satisfy at least one of the following constraints:": "This value should satisfy at least one of the following constraints:", "Each element of this collection should satisfy its own set of constraints.": "Each element of this collection should satisfy its own set of constraints.", "This value is not a valid International Securities Identification Number (ISIN).": "This value is not a valid International Securities Identification Number (ISIN).", "This value should be a valid expression.": "This value should be a valid expression.", "This value is not a valid CSS color.": "This value is not a valid CSS color.", "This value is not a valid CIDR notation.": "This value is not a valid CIDR notation.", "The value of the netmask should be between {{ min }} and {{ max }}.": "The value of the netmask should be between {{ min }} and {{ max }}.", "The filename is too long. It should have {{ filename_max_length }} character or less.|The filename is too long. It should have {{ filename_max_length }} characters or less.": "The filename is too long. It should have {{ filename_max_length }} character or less.|The filename is too long. It should have {{ filename_max_length }} characters or less.", "The password strength is too low. Please use a stronger password.": "The password strength is too low. Please use a stronger password.", "This value contains characters that are not allowed by the current restriction-level.": "This value contains characters that are not allowed by the current restriction-level.", "Using invisible characters is not allowed.": "Using invisible characters is not allowed.", "Mixing numbers from different scripts is not allowed.": "Mixing numbers from different scripts is not allowed.", "Using hidden overlay characters is not allowed.": "Using hidden overlay characters is not allowed.", "The extension of the file is invalid ({{ extension }}). Allowed extensions are {{ extensions }}.": "The extension of the file is invalid ({{ extension }}). Allowed extensions are {{ extensions }}.", "The detected character encoding is invalid ({{ detected }}). Allowed encodings are {{ encodings }}.": "The detected character encoding is invalid ({{ detected }}). Allowed encodings are {{ encodings }}.", "This value is not a valid MAC address.": "This value is not a valid MAC address.", "This URL is missing a top-level domain.": "This URL is missing a top-level domain.", "This value is too short. It should contain at least one word.|This value is too short. It should contain at least {{ min }} words.": "This value is too short. It should contain at least one word.|This value is too short. It should contain at least {{ min }} words.", "This value is too long. It should contain one word.|This value is too long. It should contain {{ max }} words or less.": "This value is too long. It should contain one word.|This value is too long. It should contain {{ max }} words or less.", "This value does not represent a valid week in the ISO 8601 format.": "This value does not represent a valid week in the ISO 8601 format.", "This value is not a valid week.": "This value is not a valid week.", "This value should not be before week \"{{ min }}\".": "This value should not be before week \"{{ min }}\".", "This value should not be after week \"{{ max }}\".": "This value should not be after week \"{{ max }}\".", "This value is not a valid Twig template.": "This value is not a valid Twig template.", "This form should not contain extra fields.": "This form should not contain extra fields.", "The uploaded file was too large. Please try to upload a smaller file.": "The uploaded file was too large. Please try to upload a smaller file.", "The CSRF token is invalid. Please try to resubmit the form.": "The CSRF token is invalid. Please try to resubmit the form.", "This value is not a valid HTML5 color.": "This value is not a valid HTML5 color.", "Please enter a valid birthdate.": "Please enter a valid birthdate.", "The selected choice is invalid.": "The selected choice is invalid.", "The collection is invalid.": "The collection is invalid.", "Please select a valid color.": "Please select a valid color.", "Please select a valid country.": "Please select a valid country.", "Please select a valid currency.": "Please select a valid currency.", "Please choose a valid date interval.": "Please choose a valid date interval.", "Please enter a valid date and time.": "Please enter a valid date and time.", "Please enter a valid date.": "Please enter a valid date.", "Please select a valid file.": "Please select a valid file.", "The hidden field is invalid.": "The hidden field is invalid.", "Please enter an integer.": "Please enter an integer.", "Please select a valid language.": "Please select a valid language.", "Please select a valid locale.": "Please select a valid locale.", "Please enter a valid money amount.": "Please enter a valid money amount.", "Please enter a number.": "Please enter a number.", "The password is invalid.": "The password is invalid.", "Please enter a percentage value.": "Please enter a percentage value.", "The values do not match.": "The values do not match.", "Please enter a valid time.": "Please enter a valid time.", "Please select a valid timezone.": "Please select a valid timezone.", "Please enter a valid URL.": "Please enter a valid URL.", "Please enter a valid search term.": "Please enter a valid search term.", "Please provide a valid phone number.": "Please provide a valid phone number.", "The checkbox has an invalid value.": "The checkbox has an invalid value.", "Please enter a valid email address.": "Please enter a valid email address.", "Please select a valid option.": "Please select a valid option.", "Please select a valid range.": "Please select a valid range.", "Please enter a valid week.": "Please enter a valid week.", "unit_hierarchy.structure.remove_unit_exception": "Unit \"%unitRefId%\" cannot be removed because it has children assigned at date %futureChangeDate%.", "u2.frequency.invalid_cron_expression": "%cron_expression% is not a valid CRON expression", "The contract expiry date must be on or after the contract date.": "The contract expiry date must be on or after the contract date.", "The maturity date must be on or after the transaction date.": "The maturity date must be on or after the transaction date.", "This value should be between 0% and 100%": "This value should be between 0% and 100%", "u2.alpha": "This value should contain only letters.", "u2.authorisation.right_not_on_authorization_item": "Right “%right_name%” is not available on authorization item “%item_name%”", "u2.base_amount.not_in_range": "This amount should be between {{ min }} and {{ max }}.", "u2.base_to_group_exchange_rate.not_in_range": "This value to group exchange rate should be between {{ min }} and {{ max }}.", "u2.destination_status.origin_and_destination_statuses_should_not_be_the_same": "Origin and Destination statuses should not be the same.", "u2.email": "This value is not a valid email address.", "u2.exactLength": "This value should have exactly %limit% characters.", "u2.exchange_rate.value_has_to_be_one_if_output_and_input_currency_are_equal": "The exchange rate value has to be “1” if the output and input currency are equal.", "u2.exchange_rate_type.given_exchange_rate_type_not_supported": "The exchange rate type “%exchange_rate_type%” is not supported. Possible values are: %exchange_rate_types%", "u2.field.invalid_name": "Name can only contain alphanumeric characters, hyphens, and underscores.", "u2.field_configuration_statuses.field_configuration.invalid": "This field configuration has already been added to this workflow.", "u2.field_configuration_statuses.status_in_use": "The following statuses have already been assigned to other configurations: \"%already_assigned_statuses%\"", "u2.field_state.field.invalid": "The selected field is invalid.", "u2.german_date_format": "Date must be in format \"dd.mm.yyyy\"", "u2.group_amount.not_in_range": "This group amount should be between {{ min }} and {{ max }}.", "u2.group_permissions.no_group_selected": "A group must be selected.", "u2.datasheets.item.formula_is_required": "A formula is required.", "u2.datasheets.item.this_type_cannot_be_edited": "Items of type “%item_type%” cannot be edited.", "u2.level_of_first_section_must_be_one": "The first section must be level 1.", "u2.local_amount.not_in_range": "This local amount should be between {{ min }} and {{ max }}.", "u2.local_to_group_exchange_rate.not_in_range": "This local to group exchange rate should be between {{ min }} and {{ max }}.", "u2.maxLength": "This value is too long. It should have %limit% characters or less.", "u2.minLength": "This value is too short. It should have %limit% characters or more.", "u2.no_file_selected": "No file selected.", "u2.numeric": "This value should contain only numbers.", "u2.period.period_cannot_be_its_previous_period": "A period cannot use itself as a previous period", "u2.rate.not_in_range": "This value should be between 0% and 100%.", "u2.required": "Required.", "u2.section_level_must_be_1_higher_or_less_than_previous_section": "The level of section “%section_name%” must equal the level of the preceding section or differ by a maximum of 1.", "u2.structured_document.reference_section_must_not_be_in_move_sections": "Reference section must not belong to list of sections to be moved", "u2.structured_section.section_does_not_belong_to_the_same_document": "Section to manipulate does not belong to the same document as reference section", "u2.task.review.user_has_already_reviewed": "You have already reviewed this task", "u2.task.review.user_has_not_reviewed": "You have not yet reviewed this task", "u2.task_checklist.inactive_check_update_error": "You cannot update an inactive Check", "u2.tax_number.unit_owns_tax_number_for_given_country": "This unit already owns a tax number for the chosen country.", "u2.template_name_cannot_be_blank": "The name of the template cannot be empty.", "u2.template_with_given_name_and_type_already_exists": "A template already exists with the given name and type.", "u2.the_user_does_not_exist": "The user does not exist", "u2.this_value_must_not_be_null": "This value must not be null.", "u2.total_revenue_value_sum_incorrect": "“Total Revenue Value” must be the same as the sum of “Total Revenue with Unrelated Value” and “Total Revenue with Related Value”.", "u2.user_permissions.no_user_selected": "A user must be selected.", "u2.value_is_not_currently_in_its_valid_date_range": "Value is not currently in it's valid date range.", "u2.value_is_not_valid_for_the_end_date_of_the_selected_period": "Value is not valid for the end date of the selected period (%date%).", "u2.workflow.status_not_available_for_entity_possible_values": "Status “%status_name%” is not available for %entity_name%. Possible values are: %possible_values%", "u2.xml_cannot_be_loaded": "The XML cannot be loaded.", "u2.xml_schema_validation_failed_with_message": "XML validation failed with the message: %message%", "u2_contractmanagement.expiry_date.must_be_on_or_after_the_date": "The expiry date must be on or after the date.", "u2_contractmanagement.for_not_third_party.partner_unit_must_be_set": "A partner unit must be set if the contract is not to a third party", "u2_contractmanagement.for_not_third_party.third_party_country_cannot_be_set": "The third party Country cannot be set if the contract partner unit is not a third party", "u2_contractmanagement.for_not_third_party.third_party_name_cannot_be_set": "The third party Name cannot be set if the contract partner unit is not a third party", "u2_contractmanagement.for_third_party.partner_unit_cannot_be_selected": "A partner unit cannot be selected if the contract is to a third party", "u2_contractmanagement.for_third_party.third_party_country_must_be_set": "The third party Country must be set if the contract partner unit is a third party", "u2_contractmanagement.for_third_party.third_party_name_must_be_set": "The third party Name must be set if the contract partner unit is a third party", "u2_contractmanagement.reminder_date.must_be_on_or_after_the_date": "The reminder date must be on or after the date.", "u2_contractmanagement.reminder_date.must_be_on_or_after_the_expiry_date": "The reminder date must be on or before the expiry date.", "u2_core.assign_to_user.user_has_insufficient_permissions_to_view_record": "The user has insufficient permissions to view this record.", "u2_core.authorisation.invalid_authorization_item": "Invalid authorization item", "u2_core.authorizations.min_message": "You must specify at least one authorisation.", "u2_core.bulk_transition_mapping.invalid_transition_selected": "Invalid transition selected", "u2_core.configuration_key_does_not_exist": "The configuration key “%configuration_key%” is not valid", "u2_core.date_not_last_day_of_month_with_given_date": "Date must be last day of month, e.g.: %last_day_of_month%", "u2_core.entities_cannot_be_created_in_closed_period": "Period “%period_name%” is closed. Entities with this period cannot be created.", "u2_core.entities_cannot_be_updated_in_closed_period": "Period is closed. Entities with this period cannot be updated.", "u2_core.group_permissions.duplicate_entry_for_group_permissions": "Duplicate entry for group permissions. Only one access level can be assigned to each group.", "u2_core.not_enabled": "This value is currently disabled", "u2_core.number_of_attachments.greater_than_zero_message": "You can only require the record to have one or more attachments.", "u2_core.number_of_reviews.greater_than_zero_message": "You can only require the record to have one or more reviews.", "u2.password_is_not_correct": "The password is not correct", "u2.password_was_already_in_use_recently": "This password was already in use recently.", "u2_core.period.name_not_unique": "The period name is not unique", "u2_core.ref_id_already_in_use": "Ref. ID {{ value }} is already in use.", "u2_core.start_date_needs_to_be_before_end_date": "The start date needs to be before the end date.", "u2_core.status_transition.invalid_entity_status": "Could not transition from “%transition_origin_status%” to “%transition_destination_status%” because the status was already changed to “%workflowable_entity_status%”.", "u2_core.the_period_does_not_exist": "The period does not exist", "u2_core.the_unit_does_not_exist": "The unit does not exist", "u2_core.user_permissions.duplicate_entry_for_user_permissions": "Duplicate entry for user permissions. Only one access level can be assigned to each user.", "u2_core.user_permissions.one_user_should_have_permission_to_manage": "At least one user should have permissions to Manage.", "u2_core.valid_to_date_must_be_after_valid_from_date": "The valid to date must be after the valid from date", "u2.datasheets.item_formula.circular_reference": "Circular reference detected. Item “%item_ref_id%” seems to have itself as a reverse dependency.", "u2.datasheets.item_formula.item_without_formula": "Item “%item_ref_id%” does not have a formula.", "u2.datasheets.datasheet.field": "Field already exists.", "u2_financial.base_currency.not_blank": "The currency should not be blank.", "u2_financial.base_local_group_money.property_has_to_be_the_same_on_each": "The %property_readable% has to be the same on each money field.", "u2_financial.base_to_group_exchange_rate.not_blank": "Value to group exchange rate should not be blank.", "u2_financial.base_value.not_blank": "The amount should not be blank.", "u2_financial.could_not_find_base_to_group_exchange_rate_for_period": "Could not find base to group exchange rate %source_currency% to %destination_currency% (%exchange_rate_type%) for period '%period%'", "u2_financial.could_not_find_local_to_group_exchange_rate_for_period": "Could not find local to group exchange rate %source_currency% to %destination_currency% (%exchange_rate_type%) for period '%period%'", "u2_financial.currency_conversion.invalid": "The currency conversion is invalid.", "u2_financial.group_value.not_blank": "Group amount should not be blank.", "u2_financial.local_currency.not_blank": "The local currency should not be blank.", "u2_financial.local_to_group_exchange_rate.not_blank": "Local to group exchange rate should not be blank.", "u2_financial.local_value.not_blank": "Local amount should not be blank.", "u2_financial.partner.partner_unit_is_required": "The partner unit is required.", "u2_financial.partner.unit_and_partner_unit_cannot_be_the_same": "Unit and Partner Unit cannot be the same", "u2_financial.type.invalid_transaction_type": "Invalid transaction type", "u2_structureddocument.create_initial_section.document_already_has_sections": "You cannot create initial section if the document already has sections.", "u2_structureddocument.document_template.required_sections_can_not_be_excluded": "Required sections can not be excluded.", "u2_tam.planning_period.max_message": "Planning year must not be more than 10 years after the start year of the current period", "u2_tam.planning_period.planning_year_after_start_year": "Planning year must be after the start year of the current period", "u2_tpm.financial_data.base_local_group_money.currency_not_blank": "The currency should not be blank.", "u2_tpm.financial_data.unit.currency_not_blank": "This unit has no assigned currency.", "u2_tpm.given_units_country_do_not_match_document_country": "You can not add the following units as their country does not match the country assigned to this document (%document_country%): %units%", "u2.role.role_not_found": "Role \"%role%\" does not exists", "The transaction date must be on or after the contract date.": "The transaction date must be on or after the contract date.", "The validity period expiry date must be after the validity period start date.": "The validity period expiry date must be after the validity period start date.", "u2.password.requires_at_least_one_uppercase_letter": "At least one upper case letter is required", "u2.password.requires_at_least_one_lowercase_letter": "At least one lower case letter is required", "u2.password.requires_at_least_one_number": "At least one number is required", "u2.password.requires_at_least_one_non_alphanumeric_character": "At least one non-alphanumeric character is required", "u2.password.too_short": "The password is too short. At least \"%minPasswordLength%\" characters are required", "u2.array.maxLength": "This collection contains too many elements. It should have at most %limit% elements.", "u2.array.minLength": "This collection contains too few elements. It should have at least %limit% elements.", "u2.import.configuration_key_slug_not_found": "Configuration for slug \"%configurationKeySlug%\" not found", "u2.import.data_row_empty": " Row should not be empty", "u2.import.data_row_field_name_not_found": "Header \"%fieldName%\" is not supported", "u2.choice_field_does_not_meet_rule": "The rules of this choice are not met", "u2_core.import.no_data_found": "No entries could be found in the file.", "u2.upload_file_too_big": "The selected file \"%name%\" is too large (%fileSize%).", "u2.invalid_bop_account_number": "A BOP account number must be 10 digits long, e.g. **********.", "u2.invalid_bzst_number": "A valid BZSt number must start with 2 capital letters followed by 9 digits, e.g. BZ123456789.", "u2.invalid_field_name": "The field name can only contain letters, numbers, underscores (_), and hyphens (-). Please ensure it doesn't include spaces or special characters.", "u2.password.do_not_match": "Passwords do not match.", "u2.workflow.binding.destination_status_not_in_target_workflow": "Destination status \"%destinationStatus%\" does not exists in the target workflow \"%targetWorkflow%\"", "u2.workflow.binding.missing_mapping": "Missing mapping for status \"%status%\""}, "messages": {"u2.results_per_page": "Records per page", "u2.reference": "Reference", "u2.clear_all": "Clear all", "u2.breakdown": "Breakdown", "u2.search.refine_search": "More entries were found than can be displayed. Try refining your search if you can't find what you're looking for.", "u2.http_401": "You need to login in order to view this page or perform this action.", "u2.session_expired": "Session expired.", "APM Transaction": "APM Transaction", "u2.max_system_upload_size_exceeded": "The defined \"Max upload Size\" for the application exceeds the maximum upload size defined by the system (%systemMaxUploadSize%B).", "u2.regenerate": "Regenerate", "Aggregate Excess of Loss": "Aggregate Excess of Loss", "u2_core.api_key.copy_warning": "Make sure to copy your personal access token now as you will not be able to see this again.", "Assumption of Liabilities": "Assumption of Liabilities", "Bond": "Bond", "Bond - Collateralized": "Bond - Collateralized", "Bond - Uncollateralized": "Bond - Uncollateralized", "Borrower": "<PERSON><PERSON><PERSON>", "Contingent Liabilities": "Contingent Liabilities", "Creditor": "Creditor", "Derivatives - Forwards": "Derivatives - Forwards", "Derivatives - Futures": "Derivatives - Futures", "Derivatives - Options": "Derivatives - Options", "Derivatives - Others": "Derivatives - Others", "Efficient Portfolio Management": "Efficient Portfolio Management", "Equity Type - Dividends": "Equity Type - Dividends", "Equity Type - Others": "Equity Type - Others", "Equity Type - Shares/Participation": "Equity Type - Shares/Participation", "Excess of Loss (per Back-Up)": "Excess of Loss (per Back-Up)", "Excess of Loss (per Event and per Risk)": "Excess of Loss (per Event and per Risk)", "Excess of Loss (per Event)": "Excess of Loss (per Event)", "Excess of Loss (per Risk)": "Excess of Loss (per Risk)", "Excess of Loss with Basis Risk": "Excess of Loss with Basis Risk", "FX": "FX", "Facultative Non-Proportional": "Facultative Non-Proportional", "Facultative Proportional": "Facultative Proportional", "Financial Reinsurance": "Financial Reinsurance", "Fixed": "Fixed", "Guarantees - Credit Protection": "Guarantees - Credit Protection", "Guarantees - Others": "Guarantees - Others", "Interest": "Interest", "Loan": "Loan", "Loan - Collateralized": "Loan - Collateralized", "Loan - Uncollateralized": "Loan - Uncollateralized", "Macro Hedge": "<PERSON><PERSON>", "Matching Assets and Liabilities Cash Flows": "Matching Assets and Liabilities Cash Flows", "Micro Hedge": "Micro Hedge", "Mixed": "Mixed", "Other Asset Transfer - Others": "Other Asset Transfer - Others", "Other Asset Transfer - Properties": "Other Asset Transfer - Properties", "Other Non-Proportional Treaties": "Other Non-Proportional Treaties", "Other Proportional Treaties": "Other Proportional Treaties", "Others": "Others", "Promissory Note": "Promissory Note", "Promissory Note - Collateralized": "Promissory Note - Collateralized", "Promissory Note - Uncollateralized": "Promissory Note - Uncollateralized", "Quota Share": "Quota Share", "Reinstatement Cover": "Reinstatement Cover", "Senior": "Senior", "Stop Loss": "Stop Loss", "Subordinate": "Subordinate", "Surplus": "Surplus", "Swaps - Credit Default": "Swaps - Credit Default", "Swaps - Currency": "Swaps - <PERSON><PERSON><PERSON>cy", "Swaps - Interest Rate": "Swaps - Interest Rate", "Swaps - Others": "Swaps - Others", "Unlimited Excess of Loss": "Unlimited Excess of Loss", "Variable": "Variable", "Variable Quota Share": "Variable Quota Share", "u2.access_denied": "Access Denied", "u2.access_type.public": "Public", "u2.not_public": "Not public", "u2.access_type.public.help": "All users will be able to view this file", "u2.add": "Add", "u2.add_comment_successful": "Comment added.", "u2.address.city": "City", "u2.address.line_1": "Line 1", "u2.address.line_2": "Line 2", "u2.address.line_3": "Line 3", "u2.address.post_code": "Postcode", "u2.address.state": "State", "u2.all": "All", "u2.application_currency": "Application currency", "u2.application_currency.warning": "Changing this value will cause major changes to the functionality and data of the application. Do not change unless you are sure about the consequences.", "u2.are_you_sure": "Are you sure?", "u2.assign": "Assign", "u2.authentication.error.account_expired": "Account has expired.", "u2.authentication.error.account_locked": "Your account is locked. Please reset your password.", "u2.authentication.error.bad_credentials": "Invalid credentials.", "u2.authentication.error.password_expired": "Your password expired. Please click the “Password forgotten” link (below) to request a password reset.", "u2.authentication.password_reset.enter_username_or_email": "Please enter your username or email address:", "u2.authentication.password_reset.error.email_sending_failed": "An error occurred while sending the email. Please try again.", "u2.authentication.password_reset.error.password_reset_link_is_no_longer_valid": "The password reset link you clicked is no longer valid. Please try again.", "u2.authentication.password_reset.error.user_could_not_be_found": "This user could not be found.", "u2.authentication.password_reset.notice.click_on_reset_password_to_send_an_email": "Click on “Reset Password” to send the user an email to set his password.", "u2.authentication.password_reset.reset_password_for_given_username": "Reset Password: “%username%”", "u2.authentication.password_reset.success.email_has_been_sent": "The password reset request was successful. An email will be sent to the account if it exists.", "u2.authentication.password_reset.success.reset_password_email_sent": "An email with a reset password link was sent to %email_address%.", "u2.authentication.two_factor.invalid_code": "Invalid code", "u2.authorization.rights.select_an_item_first": "Select an authorization item to show the associated rights.", "u2.authorization.select_authorizations": "Select authorizations", "u2.authorization.select_rights": "Select rights", "u2.bulk_transition": "Bulk Transition", "u2.calendar.empty": "Your calendar is clear for the next month.", "u2.cancel": "Cancel", "u2.change_parameters": "Change Parameters", "u2.checked": "Checked", "u2.clear": "Clear", "u2.close": "Close", "u2.closed": "Closed", "u2.comment.add_comment_field_placeholder": "Add a comment here...", "u2.comment.add_quote": "Add this text as a quote in a new comment.", "u2.configuration": "Configuration", "u2.confirm": "Confirm", "u2.confirm_deletion": "Confirm deletion", "u2.confirm_reset": "Confirm reset", "u2.confirm_user_unlock": "Confirm user unlocking", "u2.confirmation": "Confirmation", "u2.content": "Content", "u2.contract_date": "Contract Date", "u2.contract_details": "Contract Details", "u2.contract_expiry_date": "Contract Expiry Date", "u2.country": "Country", "u2.country.plural": "Countries", "u2.country_by_country_report.reporting_role": "Reporting Role", "u2.country_founded": "Country Founded", "u2.create_new_record": "Create a new record to get going", "u2.data_transfer.status_types.completed": "Completed", "u2.data_transfer.status_types.in_progress": "In progress", "u2.data_transfer.status_types.queued": "Queued", "u2.date.last_day": "Last %day%", "u2.date.today": "Today", "u2.date.tomorrow": "Tomorrow", "u2.date.yesterday": "Yesterday", "u2.datetime.last_day": "Last %day% at %time%", "u2.datetime.next_week": "%day% at %time%", "u2.datetime.now": "Today at %time%", "u2.datetime.tomorrow": "Tomorrow at %time%", "u2.datetime.yesterday": "Yesterday at %time%", "u2.deactivate": "Deactivate", "u2.default_group_permissions": "Default Group Permissions", "u2.default_user_permissions": "Default User Permissions", "u2.delete": "Delete", "u2.delete_currency_with_given_name": "Delete Currency (%currency_name%)", "u2.delete_currency_with_given_name.confirmation": "Are you sure you want to delete this Currency (%currency_name%)?", "u2.delete_given_selected_records.confirmation": "Are you sure you want to delete the selected %entity_type% (%list%)?", "u2.delete_section_from_document.confirm": "Are you sure you want to delete the section “%section_name%”?", "u2.delete_selected_records": "Delete Selected Record(s)", "u2.delete_successful": "Successfully deleted", "u2.delete_unsuccessful": "The delete was unsuccessful", "u2.description": "Description", "u2.deselect_all": "Deselect all", "u2.deselect_filtered": "Deselect filtered", "u2.disabled": "Disabled", "u2.document.widget.transaction_table.help": "Grouping results will ignore the selected columns.", "u2.document_widget.transaction_table.group_results": "Group results", "u2.document_widget.transaction_table.group_results.help": "Groups the results by Unit, Partner Unit and Type.", "u2.duplicate": "Duplicate", "u2.duplicate.dialog.text": "Are you sure you want to duplicate \"%document_name%\"?", "u2.duplicate.dialog.title": "Confirm duplication", "u2.edit": "Edit", "u2.edit_comment_successful": "Comment changes saved.", "u2.edit_configuration": "Edit Configuration", "u2.edit_content": "Edit Content", "u2.edit_currency_with_given_name": "Edit Currency (%currency_name%)", "u2.edit_document.warning_unit_hierarchy_changed": "The unit hierarchy used in this document has been changed. Please open the configuration page to update.", "u2.edit_entity_type_name": "Edit %entity_type_name%", "u2.edit_group_permissions": "Edit Group Permissions", "u2.edit_selected_records": "Edit Selected Record(s)", "u2.edit_user_permissions": "Edit User Permissions", "u2.edited": "edited", "u2.email.password_reset": "Password Reset", "u2.email.password_reset.choose_account": "There are multiple accounts associated with your email. Select the account you wish to reset below.", "u2.email.greeting": "Hi <strong>%username_or_email%</strong>,", "u2.email.password_reset.help": "If you didn’t request this, you can ignore this email. Your password won’t change until you create a new one.", "u2.email.password_reset.request_received": "We received a request to change your password.", "u2.email.password_reset.username_button": "Reset Password for %username%", "u2.email.two_factor.finish_signin": "We noticed you recently tried to sign in to your Universal Units account from a new device. <br> You can finish signing in by clicking the button:", "u2.email.two_factor.greeting": "Hi <strong>%username_or_email%</strong>,", "u2.email.two_factor.help": "If you're having trouble signing in, please <a href=\"%contact_url%\">get in touch</a>.", "u2.email.two_factor.redirection": "Or copy and paste this code to the confirmation page you were redirected to after logging in:", "u2.email.two_factor.warning": "If you don’t recognize this sign-in, we recommend that you change your password immediately to secure your account.", "u2.empty": "Empty", "u2.enabled": "Enabled", "u2.error": "Error", "u2.error.locale_not_synced": "Your language setting has changed since you last visited this tab. Please reload the page.", "u2.export.item_values.csv": "Item Values (CSV)", "u2.field": "Field", "u2.datasheets.field_count": "%count% field is assigned to this datasheet.|%count% fields are assigned to this datasheet.", "u2.datasheets.no_fields": "There are no fields assigned to this datasheet.", "u2.field_configuration": "Field Configuration Collection", "u2.field_configuration.delete.confirm": "Are you sure you want to delete field configuration \"%field_configuration_collection%\"?", "u2.field_configuration.edit": "Edit Field Configuration", "u2.field_configuration.new": "New Field Configuration", "u2.field_configuration.plural": "Field Configurations", "u2.field_configuration.tooltip.delete": "Delete field configuration \"%field_configuration_collection%\"", "u2.field_configuration.tooltip.edit": "Edit field configuration \"%field_configuration_collection%\"", "u2.field_configuration_statuses.plural": "Status Field Configurations", "u2.file": "File", "u2.file_already_linked": "This file has been already linked to this record.", "u2.file_required_link": "A file must be selected to link.", "u2.file_size": "File Size", "u2.file_type": "File Type", "u2.file_type.plural": "File Types", "u2.file_upload_whitelist": "File upload whitelist", "u2.file_upload_whitelist.help": "\n          This whitelist defines the mime types of files that may be uploaded to U². Files that do not have a mime type in this whitelist are not accepted as valid and cannot be uploaded.\n        ", "u2.file_upload_whitelist.warning": "Certain file types could be a security risk and this list should be edited with caution.", "u2.filter": "Filter", "u2.filter_the_results_to_save": "Filter the results to save", "u2.form.text_field_placeholder": "Enter text…", "u2.group_permissions": "Group Permissions", "u2.headquarters": "Headquarters", "u2.hidden": "Hidden", "u2.history": "History", "u2.http_400": "Bad request. The request could not be processed.", "u2.http_403": "You do not have permissions to view this page or perform this action. Please contact your permissions administrator to request access.", "u2.http_404": "The resource you are looking for cannot be found or does not exist. It might have been removed, had its name changed, or is temporarily unavailable.", "u2.http_405": "The request method used is not supported by this resource.", "u2.http_408": "The server timed out waiting for the request. Please try again.", "u2.http_409": "The request could not be completed because the current state of the resource conflicts with the request.", "u2.http_422": "The resource could not be processed.", "u2.http_500": "Application Error.", "u2.http_503": "The system is currently under maintenance.", "u2.import.import": "Import", "u2.import.import_units": "Import Units", "u2.import.imported": "Imported", "u2.import.importing_given_item": "Importing %item%", "u2.import.imports": "Imports", "u2.import.record_was_imported": "This record was imported", "u2.import.record_was_imported_from_an_external_source": "This record was imported from an external source and not entered manually.", "u2.import.result_types.fail": "Failed", "u2.import.result_types.success": "Successful", "u2.import.status_types.completed": "Completed", "u2.import.status_types.in_progress": "In progress", "u2.import.status_types.queued": "Queued", "u2.information": "Information", "u2.insert_file_reference": "Insert File Reference", "u2.insert_image": "Insert Image", "u2.insert_widget": "Insert Widget", "u2.insufficient_permissions": "Insufficient permissions", "u2.insufficient_user_permissions_to_view_entity": "The user has insufficient permissions to view this %entity_name%.", "u2.datasheets.item.types.checkbox": "Checkbox", "u2.datasheets.item.types.diff": "Difference", "u2.datasheets.item.types.money": "Money", "u2.datasheets.item.types.percent": "Rate", "u2.datasheets.item.types.text": "Text", "u2.datasheets.item_value": "Value", "u2.datasheets.field.delete.confirm": "Are you sure you want to delete field \"%fieldId%\" of datasheet \"%layoutName%\"?", "u2.legal_unit": "Legal Unit", "u2.legal_unit.plural": "Legal Units", "u2.linked_entities.not_allowed": "You do not have permissions to view these entities.", "u2.list": "List", "u2.loading": "Loading…", "u2.locked": "Locked", "u2.login_color": "Login Colour", "u2.login_color.help": "Hexadecimal colour representation. Leave empty for the default colour. Example: #d22630 for U² Red.", "u2.maturity_date": "Maturity Date", "u2.max_upload_size": "Max upload size in KB", "u2.money.exchange_rate.types.average": "Average", "u2.money.exchange_rate.types.current": "Current", "u2.n_a": "n/a", "u2.name": "Name", "u2.new": "New", "u2.new_dashboard": "New Dashboard", "u2.new_entity_type_name": "New %entity_type_name%", "u2.new_exchange_rate": "New Exchange Rate", "u2.new_file": "New File", "u2.new_given_unit_type": "New %unit_type%", "u2.new_item_with_given_name": "New %item_name%", "u2.new_password": "New password", "u2.new_period": "New Period", "u2.new_saved_filter": "New Saved Filter", "u2.new_saved_filter_subscription": "New Saved Filter Subscription", "u2.new_status": "New Status", "u2.new_system_message": "New System Message", "u2.new_unit_hierarchy": "New Unit Hierarchy", "u2.new_user": "New User", "u2.new_user_group": "New User Group", "u2.no": "No", "u2.no_files_in_section": "There are no files available. Please attach a file to this section and try again.", "u2.no_images_in_section": "There are no images available. Please attach an image to this section and try again.", "u2.no_records_selected_delete": "No records selected to delete.", "u2.no_records_selected_edit": "No records selected to edit.", "u2.no_records_selected_transition": "No records selected to transition.", "u2.no_results": "No results", "u2.open": "Open", "u2.organisational_group": "Organisational Group", "u2.organisational_group.plural": "Organisational Groups", "u2.page_break": "Page Break", "u2.page_has_unsaved_changes": "This page has unsaved changes.", "u2.page_not_found": "Page not found", "u2.password_good": "Good", "u2.password_moderate": "Moderate", "u2.password_strong": "Strong", "u2.password_very_weak": "Very weak", "u2.password_weak": "Weak", "u2.periods": "Periods", "u2.permanent_establishment": "Permanent Establishment", "u2.permanent_establishment.plural": "Permanent Establishments", "u2.post_comment": "Post comment", "u2.recalculate": "Recalculate", "u2.recalculate_unit_period.confirmation.text": "This will recalculate all values for the unit \"%unit%\" in the period \"%period%\"", "u2.recalculate_unit_period.success": "Successfully recalculated the values for the unit \"%unit%\" in the period \"%period%\".", "u2.record_was_transferred": "This record was transferred and not entered manually.", "u2.records": "Records", "u2.remember_me": "Keep me logged in", "u2.removing_attachment_unsuccessful": "Removing the attachment failed.", "u2.reporting_company": "Reporting Company", "u2.reporting_company.country": "Parent Company Country", "u2.reporting_company.name": "Parent Company Name", "u2.reporting_company.ref_id": "Parent Company Ref. ID", "u2.reporting_roles.localfiling": "Local", "u2.reporting_roles.surrogateparententity": "Surrogate", "u2.reporting_roles.ultimateparententity": "Headquarters", "u2.request": "Request", "u2.request_permissions": "Request Permissions", "u2.reset_trusted_devices": "Reset trusted devices", "u2.return_to_the_login_page": "Return to the login page.", "u2.role_is_inherited": "This role is inherited from another role or from a group.", "u2.runtime": "Runtime", "u2.save": "Save", "u2.save_as": "Save As", "u2.save_the_current_filter": "Save the current filter", "u2.save_the_current_filter_as_new": "Save the current filter as a new filter", "u2.save_unit_hierarchy_confirmation": "Are you sure you want to save this unit hierarchy configuration for the date %date%?", "u2.saved_filter.visibility_user_group_permissions_warning": "Assigned groups have no effect because \"Share with all users\" is on", "u2.saved_filter.visibility_user_permissions_warning": "Assigned users have no effect because \"Share with all users\" is on", "u2.saved_filter_subscription.why_receiving": "You are receiving this information because you are subscribed to this filter.", "u2.saved_filter_subscriptions.confirm_send_text": "This action will run the subscription now and send emails to all recipients.", "u2.saved_filters": "Saved Filters", "u2.saved_filters.no_subscriptions_to_this_filter": "There are no subscriptions to this filter.", "u2.saved_filters_list": "Saved Filters List", "u2.search": "Search", "u2.search.recent_searches": "Recent", "u2.security.confirm_login": "Confirm login", "u2.security.finish_login": "Finish login", "u2.security.roles.admin": "Admin", "u2.security.roles.user": "User", "u2.security.roles.user_group_admin": "User Group Admin", "u2.security.two_factor": "Enforce two factor authentication", "u2.security.two_factor.code.label": "Please enter the authentication code from the message that was sent to your email:", "u2.security.two_factor.code.placeholder": "Code", "u2.security.two_factor.confirm_reset_of_trusted_devices": "Are you sure you want to reset your trusted devices?", "u2.security.two_factor.email": "Two Factor Email Authentication", "u2.security.two_factor.reset_trusted_devices.success": "Trusted devices have been successfully reset", "u2.security.two_factor.trust_device.label": "I trust this device", "u2.security.verify_login": "Verify login", "u2.select_a_task_type": "Select a Task Type", "u2.select_all": "Select all", "u2.select_country": "Select country", "u2.select_currency": "Select a currency", "u2.select_date": "Select a date", "u2.select_filtered": "Select filtered", "u2.select_option": "Select an option", "u2.service_provider": "Service Provider", "u2.share": "Share", "u2.share_filter": "Share Filter", "u2.share_filter.help": "You can copy and paste this link and it will point to the current view of the table", "u2.share_filter_link": "Share Filter Link", "u2.share_link_to_the_currently_filtered_table": "Share link to the currently filtered table", "u2.shared_with_all_users": "Share with all users", "u2.simulated": "Simulated", "u2.simulated.help": "This is a simulation and no changes have been made.", "u2.status.types.type_complete": "Complete", "u2.status.types.type_in_progress": "In Progress", "u2.status.types.type_open": "Open", "u2.statuses_field_configuration.edit": "Edit Statuses Field Configuration", "u2.statuses_field_configuration.new": "New Statuses Field Configuration", "u2.success_removed": "Removed.", "u2.system_message.types.type_info": "Info", "u2.system_message.types.type_warning": "Warning", "u2.table.count_records_found": "%count% record found|%count% records found", "u2.table.no_records": "No Records", "u2.table.selected_count": "%count% selected", "u2.task.choice_fields": "Choice Fields", "u2.task.duplicate.success": "Task type successfully duplicated.", "u2.task.task_id": "Task Id", "u2.task_checklist.check_history_title": "Checklist history for \"%title%\"", "u2.task_checklist.hide_checks": "Hide the checks that are not assigned to the current workflow status", "u2.task_checklist.no_checks": "There are no checks for this task.", "u2.task_checklist.no_checks_in_current_status": "There are no checks for the current status.", "u2.task_checklist.no_history": "This checklist item has not yet been checked", "u2.task_checklist.show_checks": "Show the checks that are not assigned to the current workflow status", "u2.task_field.coupon_interest_rate_type": "Coupon/Interest Rate Type", "u2.task_field.coupon_interest_rate_type.help": "Agreed type of interest rate e.g. fix, variable, mixed", "u2.task_field.current_period_book_value": "Book Value Current Period", "u2.task_field.current_period_book_value.help": "Amount booked as of the end of current period. Enter 0 if transaction ends in current period.", "u2.task_field.current_period_book_value_currency": "Book Value Current Period Currency", "u2.task_field.current_period_interest_expenses": "Interest Payments Current Period", "u2.task_field.current_period_interest_expenses.help": "Booked cumulative interest income (+) / expenses (-) as of 1.1. of current year", "u2.task_field.current_period_interest_expenses_currency": "Interest Payments Current Period Currency", "u2.task_field.id_of_asset_liability_underlying_the_derivative": "ID Code of Asset/Liability Underlying the Derivative", "u2.task_field.period": "Period", "u2.task_field.previous_period_book_value": "Book Value Previous Period", "u2.task_field.previous_period_book_value.help": "Booked amount as of 31.12. of previous year. Enter 0 if transaction starts in current year.", "u2.task_field.previous_period_book_value_currency": "Book Value Previous Period Currency", "u2.task_field.source_id": "Source Id", "u2.tax_accounting.formula_compile_error": "Could not compile the formula \"%formula%\" or one of it's dependencies", "u2.tax_accounting.item_breakdown": "Item Breakdown:", "u2.tax_number.add": "Add tax number", "u2.tax_numbers": "Tax numbers", "u2.transaction.asset_liability_id": "Asset/Liability ID", "u2.transaction.coupon_interest_rate": "Coupon/Interest Rate", "u2.transaction.forward_rate": "Forward Rate", "u2.transaction.guarantee_fee_amount": "Guarantee Fee Amount", "u2.transaction.guarantee_fee_currency": "Guarantee Fee Currency", "u2.transition": "Transition", "u2.transition_required": "You must select a transition.", "u2.transition_selected_records": "Transition Selected Record(s)", "u2.try_later": "Please try again later.", "u2.two_factor.email.subject": "Authentication Code: %authentication_code%", "u2.unassigned": "Unassigned", "u2.unauthorized": "Unauthorized", "u2.unchecked": "Unchecked", "u2.under_maintenance": "Under maintenance", "u2.unit": "Unit", "u2.unit.legal_name.inherit_from_name": "Inherit legal name from name", "u2.unit.plural": "Units", "u2.unit_edit_field_white_list": "Unit edit field white list", "u2.unit_hierarchies": "Unit Hierarchies", "u2.unit_hierarchy": "Unit Hierarchy", "u2.unit_hierarchy.structure.latest_previous_change": "Previous Change (%lastChangeDate%)", "u2.unit_hierarchy.structure.next_change": "Next Change (%nextChangeDate%)", "u2.unit_hierarchy_list": "Unit Hierarchy List", "u2.unit_is_inherited": "This unit is inherited from a group.", "u2.unit_list": "Unit List", "u2.unit_pool": "Unit Pool", "u2.unknown": "Unknown", "u2.unknown_error_occurred": "An unknown error occurred. Please contact an admin for further information.", "u2.unlock": "Unlock", "u2.unlock_user": "Unlock user", "u2.unlock_user_confirmation": "Are you sure you want to unlock this user?", "u2.unlock_user_successful": "User unlocked successfully.", "u2.unlock_user_unsuccessful": "The user could not be unlocked.", "u2.update_filter_with_given_saved_filter": "Update the filter \"%saved_filter_name%\"", "u2.update_saved_filter": "Update Saved Filter", "u2.user_already_unlocked": "The user is already unlocked.", "u2.user_is_inherited": "This user is inherited from a group.", "u2.user_permissions": "User Permissions", "u2.user_requesting_permissions": "The user %user_name% is requesting permissions to the file %file_name% with the following message:", "u2.user_settings.two_factor.enforced_by_admin": "The two-factor authentication has been activated by the system administrator and therefore cannot be deactivated", "u2.vat_number": "VAT Number", "u2.visibility_group_permissions_warning": "Assigned groups have no effect because \"Visible to all users\" is on", "u2.visibility_user_permissions_warning": "Assigned users have no effect because \"Visible to all users\" is on", "u2.visible_to_all_users": "Visible to all users", "u2.watch.start_watching": "Start watching", "u2.watch.stop_watching": "Stop watching", "u2.widget.incompatible_arguments": "The values of the following parameters are incompatible: %properties%. Unable to render widget “%widget_name%”.", "u2.widget.transaction_table.sub_filter": "Sub Filter", "u2.widget.transaction_table.sub_filter.help": "The “Sub Filter” is incompatible with “Group results”. Use either “Group results” or “Sub Filter”", "u2.workflow.configuration_error": "Please ensure the workflow is correctly configured", "u2.workflow.could_not_transition_status_changed": "Could not execute transition of %count% record because the record status has been already changed or the transition has been deleted.|Could not execute transition of %count% records because their status has been already\n          changed or the transition has been deleted.\n        ", "u2.workflow.transition_confirm": "You are about to run transition \"%transition_name%\"", "u2.workflow_check.disabled": "This check has been disabled by your administrator.", "u2.xbrl_generation_fail": "The XBRL file could not be generated.", "u2.xml_file": "XML file", "u2.yes": "Yes", "u2_apm.module_name": "Asset & Participation Management", "u2_comment.comment": "Comment", "u2_comment.delete_comment": "Delete Comment", "u2_comment.delete_comment.confirmation": "Are you sure you want to delete this comment?", "u2_comment.deleted_comment": "This comment has been deleted.", "u2_comment.edit_comment": "Edit Comment", "u2_comment.new_comment": "New Comment", "u2_comment.unrestricted": "Unrestricted", "u2_comment.quote": "Quote", "u2_comment.restricted_to_given_group": "This comment is restricted to group: %group_name%", "u2_contractmanagement.contract": "Contract", "u2_contractmanagement.contract.plural": "Contracts", "u2_contractmanagement.contract_type": "Contract Type", "u2_contractmanagement.contract_type.plural": "Contract Types", "u2_contractmanagement.date": "Date", "u2_contractmanagement.days_until_automatic_renewal": "Days Until Automatic Renewal", "u2_contractmanagement.details": "Details", "u2_contractmanagement.expiry_date": "Expiry Date", "u2_contractmanagement.identification_code": "Identification Code", "u2_contractmanagement.name": "Name", "u2_contractmanagement.notice_period_in_days": "Notice Period in Days", "u2_contractmanagement.parties": "Parties", "u2_contractmanagement.partner_is_third_party": "Partner is Third Party", "u2_contractmanagement.partner_unit": "Partner Unit", "u2_contractmanagement.partner_unit_country": "Partner Unit Country", "u2_contractmanagement.partner_unit_name": "Partner Unit Name", "u2_contractmanagement.reminder_date": "Reminder Date", "u2_contractmanagement.third_party_country": "Third Party Country", "u2_contractmanagement.third_party_name": "Third Party Name", "u2_contractmanagement.unit_country": "Unit Country", "u2_core.about": "About", "u2_core.access_type": "Access Type", "u2_core.account": "Account", "u2_core.account_expires": "Account Expires", "u2.expires": "Expires at", "u2.last_used_at": "Last used at", "u2_core.account_number": "Account number", "u2_core.account_number_help": "The BZSt Online Portal (BOP) account number of the account that will be used to transmit the CbCR XML file to the Bundeszentralamt für Steuern. When logged in, you find the number under the menu item \"My BOP\".", "u2_core.active": "Active", "u2_core.add_a_review": "Add a Review", "u2.add_attachment": "Add Attachment", "u2_core.add_new_calendar_entry": "Add New Calendar Entry", "u2_core.add_new_entry": "Add New Entry", "u2_core.add_new_exchange_rate": "Add New Exchange Rate", "u2_core.add_new_given_entity_type": "Add New %entity_type_name%", "u2_core.add_new_hierarchy": "Add New Hierarchy", "u2_core.add_new_period": "Add New Period", "u2_core.add_new_status": "Add New Status", "u2_core.add_new_system_message": "Add New System Message", "u2_core.add_new_unit": "Add New Unit", "u2_core.add_new_user": "Add New User", "u2_core.add_new_user.success": "The user was successfully created.", "u2_core.add_new_user_group": "Add New User Group", "u2_core.add_subscription": "Add subscription", "u2_core.add_unit.warning_selected_hierarchy_has_no_units_for_this_date": "The selected hierarchy does not have any units for this date.", "u2_core.added_on_given_date": "Added on %date%", "u2_core.address": "Address", "u2_core.administration": "Administration", "u2_core.application": "Application", "u2_core.application_language": "Application Language", "u2_core.assign_extra_permissions": "Assign Extra Permissions", "u2_core.assign_to_me": "Assign to me", "u2_core.assign_user_to_given_entity_name": "Assign User to “%entity_name%”", "u2_core.assigned": "Assigned", "u2_core.assigned_components": "Assigned Components", "u2.assigned_user_groups": "Assigned Groups", "u2_core.assigned_users": "Assigned Users", "u2_core.assignee": "Assignee", "u2_core.associated_statuses": "Associated statuses", "u2_core.attachments": "Attachments", "u2_core.attributes": "Attributes", "u2_core.audit": "Audit", "u2_core.auditor": "Auditor", "u2_core.auditor.plural": "Auditors", "u2_core.authorisation.add_authorisation": "Add Authorisation", "u2_core.authorisation.add_authorisation_profile": "Add Authorisation Profile", "u2_core.authorisation.add_new_authorisation": "Add New Authorisation", "u2_core.authorisation.add_new_profile": "Add New Profile", "u2_core.authorisation.authorisation": "Authorisation", "u2_core.authorisation.authorisation_list": "Authorisation List", "u2_core.authorisation.authorisation_overview": "Authorisation Overview", "u2_core.authorisation.authorisation_profile": "Authorisation Profile", "u2_core.authorisation.authorisations": "Authorisations", "u2_core.authorisation.authorised_groups": "Authorised Groups", "u2_core.authorisation.authorised_users": "Authorised Users", "u2_core.authorisation.create_authorisation.success": "Authorization created successfully.", "u2_core.authorisation.create_authorisation_profile.success": "Authorization profile created successfully.", "u2_core.authorisation.delete_authorisation_with_given_name": "Delete authorisation (%authorization_name%)", "u2_core.authorisation.delete_profile_with_given_name": "Delete profile (%profile_name%)", "u2_core.authorisation.edit_authorisation": "Edit Authorisation", "u2_core.authorisation.edit_profile": "Edit Profile", "u2_core.authorisation.item": "<PERSON><PERSON>", "u2_core.authorisation.profiles": "Profiles", "u2_core.authorisation.requires_authorisation_to": "Requires authorisation to", "u2_core.authorisation.rights": "Rights", "u2_core.authorisation.update_authorisation.success": "Authorization updated successfully.", "u2_core.authorisation.update_authorisation_profile.success": "Authorization profile updated successfully.", "u2_core.available_options": "Available options", "u2_core.back_to_the_support_information_page": "Back to the support information page", "u2_core.back_to_transition": "Back to Transition", "u2_core.back_to_workflow": "Back to Workflow", "u2_core.base": "Base", "u2_core.billing_address": "Billing Address", "u2_core.branch": "Branch", "u2_core.branch.plural": "Branches", "u2_core.bulk_delete.error_no_permission": "You are not allowed to delete one or more of the selected entries.", "u2_core.bulk_delete.success_for_given_amount_of_entries": "Entry removed successfully.|%count% entries removed successfully.", "u2_core.bulk_edit.invalid_record": "Record with ID %id% is invalid with the message: %violation_message%", "u2_core.bulk_edit_given_amount_of_selected_records.confirmation": "Are you sure that you want change the %count% selected records?", "u2_core.bulk_edit_given_item_with_given_count": "Bulk Edit %item_name% (%count%)", "u2_core.bulk_update.error_no_permission_to_edit_the_selected_records": "You do not have permission to edit the selected records.", "u2_core.bulk_update.success": "You successfully updated the selected records.", "u2_core.bzst_number": "BZSt number", "u2_core.bzst_number_help": "The BZSt Number that will be used to transmit the CbCR XML file on the BZSt online portal (BOP) to the Bundeszentralamt für Steuern.", "u2_core.calendar": "Calendar", "u2_core.calendar_week_with_given_week_number": "Calendar week %week_number%", "u2_core.change_password": "Change Password", "u2_core.change_password.success": "Password changed.", "u2_core.change_password.success_please_login_using_your_new_password": "Password was changed successfully. Please login using your new password.", "u2_core.change_password_for_given_user": "Change Password for “%username%”", "u2_core.changes": "Changes", "u2_core.click_to_remove_review": "Click to remove this review", "u2_core.click_to_review_this_given_entity_type": "Click to review", "u2_core.click_to_show_help": "Click to show help", "u2_core.client_information": "Client Information", "u2_core.client_system_information": "Client System Information", "u2.close_menu": "Close Menu", "u2_core.coloring_links_appropriately": "Coloring Links appropriately", "u2_core.comments": "Comments", "u2_core.company": "Company", "u2_core.completed_at": "Completed at", "u2_core.component": "Component", "u2_core.configuration_data": "Configuration Data", "u2_core.configuration_information": "Configuration Information", "u2_core.confirm_bulk_edit": "Confirm Bulk Edit", "u2_core.confirm_new_password": "Confirm new password", "u2_core.confirm_remove": "Confirm removal", "u2_core.contact_user": "Contact User", "u2_core.content": "Content", "u2_core.conversion": "Conversion", "u2_core.cookies_enabled": "Cookies Enabled", "u2_core.corp_logo": "Logo", "u2_core.corp_logo.help": "\n          • SVG\n        ", "u2_core.create_a_new_given_additional_name": "Create a new %additional_name%", "u2_core.create_file.success": "File saved.", "u2_core.created": "Created", "u2_core.created_at": "Created at", "u2_core.created_by": "Created by", "u2_core.creating_new_filters": "Creating new filters", "u2_core.creating_new_subscriptions": "Creating new subscriptions", "u2_core.cron_expression": "CRON Expression", "u2_core.currency": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.currency.missing": "The application currency has not been set. Please configure it in the system settings.", "u2_core.currency.plural": "Currencies", "u2_core.current_password": "Current password", "u2_core.current_week_overview": "Current week overview", "u2.dashboard": "Dashboard", "u2.dashboard.help": "This is your dashboard. Here you can find tools and shortcuts to help you with your work.", "u2.dashboard.plural": "Dashboards", "u2_core.deadline_type": "Deadline Type", "u2_core.deadline_type.plural": "Deadline Types", "u2_core.delete.cannot_delete_due_to_associated_data": "It is not possible to complete this deletion due to associated data.", "u2_core.delete_country_with_given_name": "Delete Country (%country_name%)", "u2_core.delete_country_with_given_name.confirmation": "Are you sure you want to delete this Country (%country_name%)?", "u2_core.delete_current_picture.hint_use_default": "Are you sure you want to delete the current picture and use the default one instead?", "u2_core.delete_entry.confirmation": "Are you sure you want to delete this entry?", "u2_core.delete_exchange_rate_with_given_name": "Delete Exchange Rate (%exchange_rate_name%)", "u2_core.delete_exchange_rate_with_given_name.confirmation": "Are you sure you want to delete this Exchange Rate (%exchange_rate_name%)?", "u2_core.delete_filter": "Delete Filter", "u2_core.delete_filter.confirmation": "Are you sure you want to delete this Filter?", "u2_core.delete_given_entity_type": "Delete %entity_type_name%", "u2_core.delete_given_entity_type.confirmation": "Are you sure you want to delete this %entity_type_name%?", "u2_core.delete_given_entity_type_with_given_name": "Delete %entity_type_name% (%entity_name%)", "u2_core.delete_given_entity_type_with_given_name.confirmation": "Are you sure you want to delete this %entity_type_name% (%entity_name%)?", "u2_core.delete_status": "Delete Status", "u2_core.delete_status.confirmation": "Are you sure you want to delete this Status?", "u2_core.delete_status_with_given_name": "Delete Status (%status_name%)", "u2_core.delete_status_with_given_name.confirmation": "Are you sure you want to delete this Status (%status_name%)?", "u2_core.delete_subscription.success": "Subscription deleted successfully.", "u2_core.delete_system_message": "Delete System Message", "u2_core.delete_system_message.confirmation": "Are you sure you want to delete this System Message?", "u2_core.delimiter": "Delimiter", "u2_core.description": "Description", "u2_core.details": "Details", "u2_core.direct_quotation": "Direct Quotation", "u2_core.direct_quotation_value": "Direct Quotation Value", "u2_core.display_from": "Display from", "u2_core.display_to": "Display to", "u2_core.documentation_corp_logo": "Documentation Corp. Logo", "u2_core.documentation_corp_logo.help": "\n            • The logo image must be squared<br>\n            • Required size: 10 x 10 millimeters at 120 dpi (47 x 47 pixels)\n        ", "u2_core.documentation_cover_picture": "Documentation Cover Picture", "u2_core.documentation_cover_picture.help": "\n            • Maximum size: 180 x 90 millimeters at 120 dpi (850 x 425 pixels)<br>\n            • Recommended size: 90 x 45 millimeters at 120 dpi (425 x 213 pixels)\n        ", "u2_core.download": "Download", "u2_core.download_file": "Download File", "u2_core.edit_attributes": "Edit Attributes", "u2_core.edit_attributes_of_given_unit_hierarchy": "Edit Unit Hierarchy Attributes for “%unit_hierarchy_name%”", "u2_core.edit_country_with_given_name": "Edit Country (%country_name%)", "u2_core.edit_exchange_rate_with_given_name": "Edit Exchange Rate (%exchange_rate_name%)", "u2_core.edit_file": "Edit File", "u2_core.edit_filter": "Edit Filter", "u2_core.edit_given_dashboard": "Edit “%dashboard_title%” Dashboard", "u2_core.edit_given_entity_type_with_given_name": "Edit %entity_type_name% (%entity_name%)", "u2_core.edit_given_period": "Edit Period (%period%)", "u2_core.edit_given_subscription": "Edit subscription (%subscription_name%)", "u2_core.edit_status_with_given_name": "Edit Status (%status_name%)", "u2_core.edit_structure": "Edit Structure", "u2_core.edit_support_information": "Edit Support Information", "u2_core.edit_system_message": "Edit System Message", "u2_core.edit_system_settings": "Edit System Settings", "u2_core.edit_the_support_information": "Edit the Support Information", "u2_core.edit_user_group": "Edit User Group", "u2_core.edit_user_settings": "Edit User Settings", "u2_core.elma5_help": "\n        <span>Here you can generate and download ELMA compatible CbCR XML files that can be transmitted via the <a href=\"https://www.elster.de/bportal/meinelster\">BZSt online portal</a> (BOP). Further information about ELMA and it's transmission can be found on the <a href=\"https://www.bzst.de/DE/Unternehmen/Intern_Informationsaustausch/CountryByCountryReporting/countrybycountryreporting_node.html\">BZSt website</a>.</span>\n        ", "u2_core.email": "Email", "u2_core.empty_widget": "Empty Widget.", "u2_core.empty_widget.help": "Please add some content.", "u2_core.enable_manual_review": "Enable manual review", "u2_core.end": "End", "u2_core.end_date": "End Date", "u2_core.english": "English", "u2_core.enter_filter_term": "Enter filter term", "u2_core.entities_linked_with_given_file": "Entities linked with “%file_name%”", "u2_core.error_could_not_save_check_the_highlighted_fields": "Could not save. Please check the highlighted fields below and try again.", "u2_core.exchange_rate": "Exchange Rate", "u2_core.exchange_rate_unavailable": "Exchange rate unavailable", "u2_core.exchange_rates": "Exchange Rates", "u2_core.expand_sidebar": "Expand Sidebar", "u2_core.experimental": "Experimental", "u2_core.fax": "Fax", "u2_core.file.access_type_protected": "Protected", "u2_core.file.access_type_protected.help": "Only explicitly assigned users and/or groups will be able to view this file. The file name will not be revealed to other users.", "u2_core.file.access_type_smart": "Smart", "u2_core.file.access_type_smart.help": "Users will be able to see this file if they are able to view the record to which it is attached. Additional users and groups may also be assigned explicitly to the file.", "u2_core.file_mime_content_type": "File MIME Content-Type", "u2_core.file_not_found": "File not found", "u2_core.files": "Files", "u2_core.filter": "Filter", "u2_core.filter_information": "Filter Information", "u2_core.filter_query": "Filter query", "u2_core.filter_table.error_occurred_filters_have_been_reset": "An error has occurred when filtering the current table. The filters have been reset.", "u2_core.filter_target": "Filter Target", "u2_core.filters": "Filters", "u2.frequency": "Frequency", "u2_core.from_date": "From Date", "u2_core.full_text_search": "Full text search", "u2_core.further_browser_information": "Further Browser Information", "u2_core.general": "General", "u2_core.german": "De<PERSON>ch", "u2_core.given_entity_type_list": "%entity_type_name% List", "u2_core.given_units_are_not_assigned_to_user": "You are not authorised to add the following units: %units%.", "u2_core.given_user_requests_permission_to_given_file": "%user_name% requests permission to %file_name%", "u2_core.given_version": "Version %version%", "u2_core.go_to_support_platform": "Get in touch with us over our support platform:", "u2_core.go_to_the_corresponding_list_page": "\n            <ol>\n              <li>Go to the corresponding list page.</li>\n              <li>Apply the filtering parameters as needed.</li>\n              <li>Click the <span class=\"icon-save-alt\"></span><strong>Save</strong> button.</li>\n              <li>Enter name and description, and click save.</li>\n            </ol>\n        ", "u2_core.go_to_the_universal_units_website": "Go to the Universal Units website", "u2_core.group": "Group", "u2_core.group_parent_income_tax": "Group Parent - Income Tax", "u2_core.group_parent_vat": "Group Parent - VAT", "u2_core.groups": "Groups", "u2_core.help": "Help", "u2_core.hide": "<PERSON>de", "u2_core.id": "ID", "u2_core.import.confirm": "Confirm Import", "u2_core.import.default_delimiter.help": "Default delimiter in CSV files used for imports.", "u2_core.import.email_content_fail": "The import failed. Click on the link below to see the result:", "u2_core.import.email_content_success": "The import was successfully completed. Click on the link below to see the result:", "u2.import.file.help": "The file containing the data to be imported.", "u2.import.simulate.help": "When this option is selected then the import will only be simulated and no values will be imported. This may be used to determine if the file is valid or has errors.", "u2_core.import.email_title_fail": "Failed Import", "u2_core.import.email_title_success": "Successful Import", "u2_core.import.help": "\n          <p>On this page you can import data via CSV or Excel. Select a CSV or Excel file containing the data you wish to import and press import.</p><p>The data in the CSV or Excel can be in any order. The CSV file must contain a header row that defines the format of the file and the order of the values you wish to import.</p>", "u2_core.import.help_values_contain_commas": "\n            <p>If your values contain commas you will need to enclose them in double quotation marks:</p>\n            <div class=\"data-group font-mono text-sm\">\n            Header One,Header Two,Header Three\n            <br>\n            value one,\"However, value two contains a comma\",value three\n            </div>\n        ", "u2_core.import.selection": "What would you like to import?", "u2_core.import.list": "Import List", "u2_core.import.new": "New Import", "u2_core.import.not_simulated_confirmation": "You are about to import the contents of the selected file into the application. Please cancel this operation and select the “Simulate” option if you would first like to test this file for errors and compatibility.", "u2_core.import.possible_headers_data_import": "The following are the possible headers for this data import:", "u2_core.import.result": "Import Result", "u2_core.import.select_a_file_to_import": "Select a file to import", "u2_core.import.start_import": "Start Import", "u2_core.import.uploaded_file": "Uploaded File", "u2_core.import_exchange_rates": "Import Exchange Rates", "u2_core.import_was_completed": "The Import was completed.", "u2_core.indirect_quotation": "Indirect Quotation", "u2_core.indirect_quotation_value": "Indirect Quotation Value", "u2_core.initial_status": "Initial Status", "u2_core.input_currency": "Input Currency", "u2_core.is_closed": "Is Closed", "u2_core.iso_code": "Iso Code", "u2.item": "<PERSON><PERSON>", "u2_core.last_login": "Last Login", "u2_core.last_run": "Last Run", "u2_core.last_sent": "Last Sent", "u2.datasheets.datasheet": "Datasheet", "u2_core.legal": "Legal", "u2_core.legal_form": "Legal Form", "u2_core.legal_form.plural": "Legal Forms", "u2_core.legal_name": "Legal Name", "u2_core.link": "Link", "u2_core.link_an_existing_file": "Link an existing file", "u2_core.link_file_to_entity.success": "The file was successfully linked.", "u2_core.linked_entities": "Linked Entities", "u2_core.login": "<PERSON><PERSON>", "u2_core.login_background": "Login <PERSON>", "u2_core.login_background.help": "\n            • Minimum size: 750 x 850 pixels<br>\n            • Recommended size: 1125 x 1275 pixels or more\n        ", "u2_core.login_logo": "<PERSON><PERSON>", "u2_core.login_logo.help": "\n            • SVG, or transparent PNG<br>\n            • Recommended size: not bigger than 400 pixels wide and 250 pixels tall\n        ", "u2_core.logout": "Logout", "u2_core.manage": "Manage", "u2_core.manually_run_subscription.success": "Subscription manually run successfully.", "u2_core.max_login_attempts": "Maximum amount of login attempts", "u2_core.maximum_file_size": "Maximum file size:", "u2_core.menu": "<PERSON><PERSON>", "u2_core.message": "Message", "u2_core.mobile": "Mobile", "u2_core.moved_on_given_date": "Moved on %date%", "u2_core.my_permissions": "My Permissions", "u2_core.n_a": "n/a", "u2_core.name": "Name", "u2_core.name_first": "First Name", "u2_core.name_last": "Last Name", "u2_core.name_long": "Name (long)", "u2_core.name_short": "Name (short)", "u2_core.nationality_long": "Nationality (long)", "u2_core.nationality_short": "Nationality (short)", "u2_core.never": "Never.", "u2_core.next_calendar_week": "Next Calendar Week", "u2_core.next_run": "Next Run", "u2_core.next_sent": "Next Sent", "u2_core.no_changes_on_given_date": "No changes on %date%", "u2.no_changes": "No Changes", "u2_core.no_period_selected": "No period selected", "u2_core.no_permissions_assigned_with_given_permission_type": "There are no %permission_type% permissions assigned.", "u2_core.no_subscriptions": "No Subscriptions", "u2_core.none": "None", "u2_core.none_selected": "None selected", "u2_core.number_of_attachments": "Number of Attachments", "u2_core.number_of_reviews": "Number of Reviews", "u2.number_of_widgets": "Number of Widgets", "u2_core.open_beginning_of_year": "Open Beginning of Year", "u2_core.open_beginning_of_year.help": "Allow manual entry for beginning of year values", "u2_core.operating_system": "Operating System", "u2_core.or": "or", "u2_core.output_currency": "Output Currency", "u2_core.owner": "Owner", "u2_core.parent_legal_unit": "Parent Legal Unit", "u2_core.parent_user": "Parent User", "u2.password": "Password", "u2.password_expires": "Password Expires", "u2.password_forgotten": "I forgot my password", "u2.password_history_length": "Password history length", "u2.password_max_age_in_days": "Password max age in days", "u2.password_must_have_at_least_one_lowercase_letter": "Must have at least one lowercase letter.", "u2.password_must_have_at_least_one_non_alphanumeric_character": "Must have at least one non alphanumeric character.", "u2.password_must_have_at_least_one_number": "Must have at least one number.", "u2.password_must_have_at_least_one_uppercase_letter": "Must have at least one uppercase letter.", "u2.password_must_meet_the_following_constraints": "Passwords must meet the following constraints:", "u2.password_requires_at_least_one_lowercase_letter": "Password requires at least one lowercase letter", "u2.password_requires_at_least_one_non_alphanumeric_character": "Password requires at least one non alphanumeric character", "u2.password_requires_at_least_one_number": "Password requires at least one number", "u2.password_requires_at_least_one_uppercase_letter": "Password requires at least one uppercase letter", "u2.password_reset": "Password Reset", "u2.password_reset_hours_valid": "Password reset hours valid", "u2_core.period": "Period", "u2_core.period_list": "Period List", "u2_core.period_management": "Period Management", "u2_core.permission_mask.1": "Read", "u2_core.permission_mask.1073741823": "God's Power", "u2_core.permission_mask.128": "Manage", "u2_core.permission_mask.16": "Undelete", "u2_core.permission_mask.2": "Create", "u2_core.permission_mask.32": "Operator", "u2_core.permission_mask.4": "Edit", "u2_core.permission_mask.64": "Master", "u2_core.permission_mask.8": "Delete", "u2_core.permissions": "Permissions", "u2_core.permissions_request_sent.success": "Permissions request has been successfully sent.", "u2_core.personal_information": "Personal Information", "u2_core.please_choose_a_new_password_for_given_user": "Please choose a new password for “%username%”.", "u2_core.postal_address": "Postal Address", "u2_core.previous_calendar_week": "Previous Calendar Week", "u2_core.previous_period": "Previous Period", "u2_core.profile": "Profile", "u2_core.progress": "Progress", "u2_core.read": "Read", "u2_core.ref_id": "Ref. ID", "u2_core.register_number": "Register Number", "u2_core.registry_place": "Registry Place", "u2_core.remove_file": "Remove File", "u2_core.remove_review": "Remove review", "u2_core.remove_review.success": "Successfully removed the review", "u2_core.removed_on_given_date": "Removed on %date%", "u2_core.reporter": "Reporter", "u2_core.request_a_password_reset": "Request a Password Reset", "u2_core.reset_password": "Reset Password", "u2_core.result": "Result", "u2_core.review.remove_review.confirm": "Are you sure you want to remove this review?", "u2_core.review.submit_review.confirm": "Are you sure you want to submit a review?", "u2_core.review.success": "Review successful.", "u2_core.reviews": "Reviews", "u2_core.roles": "Roles", "u2_core.run_now_this_given_subscription": "Run now this subscription (%subscription_name%)", "u2_core.save_groups": "Save groups", "u2_core.save_roles": "Save roles", "u2_core.save_units": "Save units", "u2_core.save_users": "Save users", "u2.saved_filter": "Saved Filter", "u2.saved_filter.frequency.help": "\n            <p>A string comprising five fields separated by spaces:</p>\n            <ul>\n                <li>Minutes (0 to 59)</li>\n                <li>Hours (0 to 23)</li>\n                <li>Day of Month (1 to 31)</li>\n                <li>Month (1 to 12 or JAN to DEC)</li>\n                <li>Day of Week (0 to 6 or SUN to SAT, 1 = Monday)</li>\n            </ul>\n            <p>An asterisk (<kbd>*</kbd>) means “all”.</p>\n            <p>Use commas (<kbd>,</kbd>) to separate items of a list.</p>\n            <p>Use a hyphen (<kbd>-</kbd>) to define ranges.</p>\n            <p>Use a slash (<kbd>/</kbd>) to specify increments. Only values that evenly divide their range are valid (Minutes and seconds: /2, /3, /4, /5, /6, /10, /12, /15, /20, /30. Hours: /2, /3, /4, /6, /8, /12). A number before the slash indicates where to start the increment (3/6 in hours means at 3, 9, 15, and 21), no value before the slash means 0.</p>\n            <p>For more information check: <a href=\"https://en.wikipedia.org/wiki/Cron#CRON_expression\" target=\"_blank\">en.wikipedia.org/wiki/Cron#CRON_expression</a>.</p>\n            <p>Examples:</p>\n            <ul>\n                <li><kbd>5 * * * *</kbd> Every day at each hour and 5 minutes (0:05, 1:05, […], 23:05)</li>\n                <li><kbd>0 7 * * MON</kbd> Mondays at 7:00</li>\n                <li><kbd>0 9 1,15 * *</kbd> The 1st and 15th day of the month at 9:00</li>\n                <li><kbd>*/15 9-18 * * MON-FRI</kbd> Every 15 minutes on weekdays between 9:00 and 18:00</li>\n            </ul>\n        ", "u2.saved_filter.subscription_did_not_run": "Subscription is overdue.", "u2.saved_filter.success_the_given_filter_has_been_saved": "The filter “%saved_filter_name%” has been saved successfully.", "u2.saved_filter_subscription": "Saved Filter Subscription", "u2.saved_filter_subscriptions": "Saved Filter Subscriptions", "u2.saved_filter_subscriptions.help": "To create a new subscription, go to the edit page for a saved filter.", "u2_core.security": "Security", "u2_core.see_all_saved_filters": "See all saved filters", "u2.select_a_file": "Select a file", "u2.select_a_period": "Select a period", "u2.select_a_unit": "Select a unit", "u2.select_a_user": "Select a user", "u2.select_an_item": "Select an item", "u2.select_entity_type_to_create": "Select entity type to create", "u2.select_file_types": "Select file types", "u2.select_unit_type": "Select unit type", "u2.select_unit_type_to_create": "Select unit type to create", "u2_core.send": "Send", "u2_core.send_email": "Send email", "u2_core.show": "Show", "u2_core.show_current_calendar_week": "Show current calendar week", "u2_core.show_current_date": "Show current date", "u2_core.show_entities_linked_with_this_file": "Show entities linked with this file", "u2_core.show_information_about_your_system": "Show information about your system", "u2_core.show_more": "Show more", "u2_core.show_the_filtered_table": "Show the filtered table", "u2_core.showing_count_of_total_count_records": "Showing %showing_count% of %count% record|Showing %showing_count% of %count% records", "u2.simulate": "Simulate", "u2_core.start": "Start", "u2_core.start_date": "Start Date", "u2_core.started_at": "Started at", "u2_core.status": "Status", "u2_core.status_list": "Status List", "u2_core.status_type": "Status Type", "u2_core.statuses": "Statuses", "u2_core.structure": "Structure", "u2_core.styling_links": "Styling Links", "u2_core.subscribed_groups": "Subscribed Groups", "u2_core.subscribed_users": "Subscribed Users", "u2_core.subscriptions": "Subscriptions", "u2_core.subscriptions_overview": "Subscriptions Overview", "u2_core.success": "Success", "u2_core.success_saved": "Saved.", "u2_core.support": "Support", "u2_core.system_message": "System Message", "u2_core.system_message.plural": "System Messages", "u2_core.system_settings": "System Settings", "u2_core.tags": "Tags", "u2_core.tax_advisor": "Tax Advisor", "u2_core.tax_assessment": "Tax Assessment", "u2_core.tax_number": "Tax Number", "u2_core.telephone": "Telephone", "u2_core.the_given_user_is_requesting_permissions_to_the_given_file": "The user %user_name% is requesting permissions to the file %file_name%.", "u2_core.the_name_of_this_file_is_restricted": "(The name of this file is restricted)", "u2.datasheets.datasheet_collection.name_restricted": "+%count% restricted", "u2.datasheets.datasheet_collection.name_restricted.tooltip": "You do not have permission to view one or more datasheet collections", "u2.no_attachments": "No attachments", "u2_core.there_are_no_elements_assigned": "There are no elements assigned", "u2_core.there_are_no_elements_to_be_assigned": "There are no elements to be assigned", "u2_core.this_file_is_not_linked_to_anything_at_the_moment": "This file is not linked to anything at the moment.", "u2.title": "Title", "u2_core.to_date": "To Date", "u2_core.to_grant_permissions_go_to_the_edit_page_forgiven_file": "To grant permissions go to the edit page for %file_name%:", "u2_core.tools": "Tools", "u2_core.type": "Type", "u2_core.types": "Types", "u2_core.u2_documentation": "U² Documentation", "u2_core.unlink_file_with_given_name_from_entity.confirmation": "Are you sure you want to remove the file “%file_name%” from this entry?", "u2_core.up_next": "Up Next", "u2_core.update_assignment.success": "Assignment saved.", "u2_core.updated": "Updated", "u2_core.updated_at": "Updated at", "u2_core.updated_by": "Updated by", "u2_core.upload": "Upload", "u2_core.upload_a_new_file": "Upload a new file", "u2_core.upload_file.drop_a_file_here_to_attach_it": "Drop a file here to attach it", "u2_core.upload_file.error": "Upload failed.", "u2_core.uql": "UQL", "u2_core.use_default": "Use Default", "u2_core.user": "User", "u2_core.user_agent": "User Agent", "u2_core.user_email_has_changed": "The email address of your U² account “%user_name%” changed.", "u2_core.user_email_listener.email_has_changed.mail_body.title": "Your email address has changed", "u2_core.user_group_list": "User Group List", "u2_core.user_group_name": "User Group Name", "u2_core.user_group_with_given_group_name": "User Group “%group_name%”", "u2_core.user_groups": "User Groups", "u2_core.user_information": "User Information", "u2_core.user_interface": "User Interface", "u2_core.user_list": "User List", "u2_core.user_profile": "User Profile", "u2_core.user_property_has_changed.how_to_react": "\n          If you did not request this change or this notification is unexpected then there may be a security issue with your user account.\n          In such an event, please update your credentials and notify your administrator.\n          If, however, you requested this change then you may safely ignore this email.\n        ", "u2_core.user_settings": "User Settings", "u2_core.username": "Username", "u2_core.users": "Users", "u2_core.valid_from": "<PERSON>id from", "u2_core.valid_to": "Valid to", "u2_core.verified": "Verified", "u2_core.view": "View", "u2_core.view_profile": "View profile", "u2_core.warning": "Warning", "u2_core.warning_authentication_success": "\n            There has been a failed login attempt.<br>\n            The attempt was logged on:<br>\n            %failed_login_times_message%<br>\n            <strong>Please inform your administrator if this was not you.</strong>\n            |\n            There have been %count% failed login attempts.<br>\n            The last recorded attempts were:<br>\n            %failed_login_times_message%<br>\n            <strong>Please inform your administrator if this was not you.</strong>\n        ", "u2_core.watch.unwatch": "Unwatch", "u2_core.watch.watch": "Watch", "u2_core.watch.watchers": "Watchers", "u2_core.welcome_to_u2": "Welcome to U²", "u2_core.widget.does_not_exist": "The widget “%widget_name%” does not exist.", "u2_core.widget.insufficient_permissions": "Insufficient permissions to view widget “%widget_name%”.", "u2_core.widget.invalid_argument_value": "Invalid value for property “%property%”. Unable to render widget “%widget_name%”.", "u2_core.widget.malformed_configuration": "Unable to parse widget configuration.", "u2_core.widget.missing_property": "Missing property “%property%”. Unable to render widget “%widget_name%”.", "u2_core.widget.not_support": "Widget “%widget_name%” is not supported.", "u2_core.widget.unknown_error_occurred_while_rendering_widget": "An unkown error occurred while rendering widget “%widget_name%”. View the logs for further information.", "u2_core.workflow": "Workflow", "u2_core.workflow.actions": "Actions", "u2_core.workflow.add_action": "Add Action", "u2_core.workflow.add_check": "Add Checklist Item", "u2_core.workflow.add_condition": "Add Condition", "u2_core.workflow.add_condition_to_transition.success": "Condition added.", "u2_core.workflow.add_new_transition": "Add New Transition", "u2_core.workflow.add_new_workflow": "Add New Workflow", "u2_core.workflow.add_transition": "Add Transition", "u2_core.workflow.adding_condition_to_transition": "Adding condition to transition", "u2_core.workflow.change_status_to_given_status": "Change status to %destinationStatus%", "u2_core.workflow.change_workflow.success": "Workflow changed successfully.", "u2_core.workflow.check": "Checklist Item", "u2_core.workflow.checklist": "Checklist", "u2_core.workflow.conditions": "Conditions", "u2_core.workflow.confirm_status_transition": "Confirm status transition", "u2_core.workflow.current_status": "Current Status", "u2_core.workflow.current_workflow": "Current Workflow", "u2_core.workflow.delete_check": "Delete Checklist Item (%check%)", "u2_core.workflow.delete_check.confirmation": "This will permanently delete the checklist item and all history data associated with it. Alternatively, you can deactivate the item to no-longer show it in new checklists while keeping the existing history data.", "u2_core.workflow.delete_transition.confirmation": "Are you sure you want to remove this transition?", "u2_core.workflow.delete_transition_with_given_name": "Delete transition (%transition_name%)", "u2_core.workflow.delete_workflow": "Delete Workflow", "u2_core.workflow.delete_workflow.confirmation": "Are you sure you want to delete this Workflow?", "u2_core.workflow.destination_status": "Destination Status", "u2_core.workflow.destination_workflow": "Destination Workflow", "u2_core.workflow.edit_assignment": "Edit Assignment", "u2_core.workflow.edit_check": "Edit Checklist Item (%check%)", "u2_core.workflow.edit_transition_with_given_name": "Edit transition (%transition_name%)", "u2_core.workflow.edit_workflow": "Edit Workflow", "u2_core.workflow.edit_workflow_assignment_with_given_name": "Edit Workflow Assignment “%workflow_name%”", "u2_core.workflow.new_check": "New Checklist Item", "u2_core.workflow.new_transition": "New Transition", "u2_core.workflow.new_workflow": "New Workflow", "u2_core.workflow.no_actions_attached_to_transition": "There are no actions attached to this transition.", "u2_core.workflow.no_associated_statuses": "No associated statuses", "u2_core.workflow.no_conditions_attached_to_transition": "There are no conditions attached to this transition.", "u2_core.workflow.no_destination_status_available": "No destination status available", "u2_core.workflow.no_status_has_been_set": "No status has been set", "u2_core.workflow.no_transitions_available": "No transitions available", "u2_core.workflow.origin_status": "Origin Status", "u2_core.workflow.remap_missing_statuses": "Remap Missing Statuses", "u2_core.workflow.remove_action.confirmation": "Are you sure you want to remove this action?", "u2_core.workflow.remove_action.success": "Action removed.", "u2_core.workflow.remove_action_with_given_name": "Remove action (%action_readable_name%)", "u2_core.workflow.remove_condition.confirmation": "Are you sure you want to remove this condition?", "u2_core.workflow.remove_condition.success": "Condition removed.", "u2_core.workflow.remove_condition_with_given_name": "Remove condition (%condition_readable_name%)", "u2_core.workflow.remove_transition.success": "Transition removed.", "u2_core.workflow.select_a_status": "Select a status", "u2_core.workflow.select_destination_status_for_assigned_objects": "The following statuses have objects assigned that cannot be remapped automatically. Please select a destination status for these objects.", "u2_core.workflow.status_history_for_given_entity_name": "Workflow Status History for “%entity_name%”", "u2_core.workflow.there_have_been_no_status_changes": "There have been no status changes.", "u2_core.workflow.transition_status.success_given_amount_of_records_transitioned": "%count% record transitioned successfully.|%count% records transitioned successfully.", "u2_core.workflow.transitions": "Transitions", "u2_core.workflow.warning_no_destination_status_available": "Records with this status will not be transitioned.", "u2_core.workflow_assignment": "Workflow Assignment", "u2_core.workflow_assignments": "Workflow Assignments", "u2_core.workflow_list": "Workflow List", "u2_core.workflows": "Workflows", "u2_core.write": "Write", "u2_core.you_are_not_authorized_to_import_any_data": "You are not authorized to import any data.", "u2_core.your_title_text": "Your title text", "u2.datasheets.cannot_initialize_with_missing_exchange_rate": "Unable to restore defaults: Could not find exchange rates “%local_currency_iso%” to “%group_currency_iso%” for period “%period%”. Please add these and reload the page.", "u2.datasheets.cannot_initialize_with_missing_previous_period": "Unable to initialize defaults: Period “%period_name%” does not have a previous period. Add a previous period to the period and revisit the page.", "u2.datasheets.delete_given_entity_type_with_given_name": "Delete %entity_type_name% (%entity_name%)", "u2.datasheets.delete_given_entity_type_with_given_name.confirmation": "Are you sure you want to delete this %entity_type_name% (%entity_name%)?", "u2.datasheets.edit_given_entity_type_with_given_name": "Edit %entity_type_name% (%entity_name%)", "u2.datasheets.exchange_method": "Exchange Method", "u2.datasheets.exchange_method.average": "Average", "u2.datasheets.exchange_method.current": "Current", "u2.datasheets.exchange_method.previous_average": "Average exchange rate of previous period", "u2.datasheets.exchange_method.previous_current": "Latest exchange rate of previous period", "u2.datasheets.formula": "Formula", "u2.datasheets.formula.formula_string.help": "\n          Item Reference: {%item_refId%} <br> Reference to an item value from previous Period: {P%item_refId%} <br> All maths operators are allowed <br> Also if -> then is allowed: ItemXXX === 1 ? %true_statement% : %false_statement%", "u2.datasheets.group": "Group", "u2.datasheets.item.plural": "Items", "u2.datasheets.datasheet.plural": "Datasheets", "u2.datasheets.module_name": "Datasheets", "u2.datasheets.name": "Name", "u2.datasheets.select_unit_hierarchy": "Select a unit hierarchy", "u2.datasheets.status_monitor": "Datasheet Monitor", "u2.datasheets.status_monitor.plural": "Datasheet Monitor", "u2.datasheets.type": "Type", "u2.datasheets.unit_view.form_is_disabled": "The form is disabled and cannot be saved.", "u2_financial.base_to_group_exchange_rate": "Value to Group Exchange Rate", "u2_financial.billing_type": "Billing Type", "u2_financial.billing_type.plural": "Billing Types", "u2_financial.carry_forward": "Carry Forward", "u2_financial.carry_forward_financial_data.help": "Set this flag to mark the financial data to be carried forward during a data transfer to another period.", "u2_financial.collateralisation_type": "Collateralisation Type", "u2_financial.collateralisation_type.plural": "Collateralisation Types", "u2_financial.exchange_rate_shortened": "E. Rate", "u2_financial.group": "Group", "u2_financial.group_parent_income_tax": "Group Parent - Income Tax", "u2_financial.group_parent_vat": "Group Parent - VAT", "u2_financial.local": "Local", "u2_core.api_docs": "Api-Documentation", "u2.security.roles.api": "Api", "u2_financial.local_to_group_exchange_rate": "Local to Group Exchange Rate", "u2_financial.parent_legal_unit": "Parent Legal Unit", "u2.partner_unit": "Partner Unit", "u2_financial.partner_unit_country": "Partner Unit Country", "u2_financial.partner_unit_name": "Partner Unit Name", "u2_financial.pricing_method": "Pricing Method", "u2_financial.pricing_method.plural": "Pricing Methods", "u2_financial.show_hide_value_and_exchange_rates_in_group_and_local_currencies": "Show/hide value and exchange rates in the Group and Local currencies", "u2_financial.tax_type": "Tax Type", "u2_financial.tax_type.plural": "Tax Types", "u2_financial.transaction_type": "Transaction Type", "u2_financial.transaction_type.plural": "Transaction Types", "u2_financial.type": "Type", "u2_financial.unit_country": "Unit Country", "u2_financial.unit_id": "Unit ID", "u2_igt.module_name": "Intra-Group Transactions", "u2_structureddocument.add_attachment": "Add Attachment", "u2_structureddocument.add_section_after": "Add Section After", "u2_structureddocument.add_section_before": "Add Section Before", "u2_structureddocument.add_subsection": "Add Subsection", "u2_structureddocument.after": "After", "u2_structureddocument.allow_edits": "Allow Edits", "u2_structureddocument.also_delete_subsections": "Also delete subsections.", "u2_structureddocument.attachments": "Attachments", "u2_structureddocument.attachments_archive_is_empty": "Attachments archive doesn’t contain any files", "u2_structureddocument.before": "Before", "u2_structureddocument.caution": "Caution", "u2_structureddocument.configure_document_template": "Configure Document Template", "u2_structureddocument.configure_template_with_given_template_type": "Configure %document_template_type% Template", "u2_structureddocument.confirm_deletion": "Confirm deletion", "u2_structureddocument.content": "Content", "u2_structureddocument.created": "Created", "u2_structureddocument.delete": "Delete", "u2_structureddocument.delete_section.error": "Could not delete section. Please contact system administrator for further information.", "u2_structureddocument.delete_section.warning_attachments_will_also_be_removed": "Attachments in deleted sections will also be removed.", "u2_structureddocument.do_not_require": "Do Not Require", "u2_structureddocument.document_template": "Document Template", "u2_structureddocument.document_template_list": "Document Template List", "u2.saving": "We are currently saving your data. Please wait a moment.", "u2_structureddocument.document_templates": "Document Templates", "u2_structureddocument.documents": "Documents", "u2_structureddocument.download_all_accessible_attachments.help": "Download all attachments that you have permission to access", "u2.no_permission_to_perform_action": "You are missing permission \"%permission%\" to perform this action.", "u2_structureddocument.edit": "Edit", "u2_structureddocument.exclude": "Exclude", "u2_structureddocument.import_new_document_template": "Import New Document Template", "u2_structureddocument.include": "Include", "u2_structureddocument.included": "Included", "u2_structureddocument.information": "Information", "u2_structureddocument.move": "Move", "u2_structureddocument.move_given_section": "Move “%section_name%”", "u2_structureddocument.move_section.error": "Could not move section. Please contact system administrator for further information.", "u2_structureddocument.not_changed": "Not changed.", "u2_structureddocument.placement": "Placement", "u2_structureddocument.prevent_edits": "Prevent Edits", "u2_structureddocument.preview": "Preview", "u2_structureddocument.preview_given_document_template": "Preview “%document_template_name%”", "u2_structureddocument.require": "Require", "u2_structureddocument.required": "Required", "u2_structureddocument.section": "Section", "u2_structureddocument.select_a_section": "Select a section", "u2_structureddocument.subsection_of": "Subsection of", "u2_structureddocument.templates": "Templates", "u2_structureddocument.title": "Title", "u2_structureddocument.type": "Type", "u2_structureddocument.updated": "Updated", "u2_table.advanced": "Advanced", "u2_table.all": "All", "u2_table.apply": "Apply", "u2_table.apply_changes": "Apply Changes", "u2_table.available_fields": "Available fields", "u2_table.available_functions": "Available functions", "u2_table.basic": "Basic", "u2_table.cancel": "Cancel", "u2_table.columns": "Columns", "u2_table.default": "<PERSON><PERSON><PERSON>", "u2_table.deselect_all": "Deselect All", "u2_table.deselect_all_visible": "Deselect All Visible", "u2_table.false": "False", "u2_table.filters": "Filters", "u2_table.next_page": "Next Page", "u2_table.no_results_that_match_search": "No results found", "u2_table.none": "None", "u2_table.previous_page": "Previous Page", "u2_table.reset": "Reset", "u2_table.search_results_empty.help": "Try changing your query or create a new record to get going", "u2_table.select": "Select:", "u2_table.select_all_visible": "Select All Visible", "u2_table.show": "Show:", "u2_table.switch_to_advanced_filtering": "Switch to advanced filtering using UQL", "u2_table.switch_to_basic_filtering": "Switch to basic filtering", "u2_table.table_columns": "Table Columns", "u2_table.true": "True", "u2_table.uql_filtered_table.filter_too_complex_to_display_in_basic_mode": "The current filter is too complex to display in basic mode. Please reset or change your filter.", "u2_table.uql_help": "UQL Help", "u2_tam.accrued": "Accrued", "u2_tam.accrued_boy": "Accrued BOY", "u2_tam.accrued_eoy": "Accrued EOY", "u2_tam.addition": "Addition (+)", "u2_tam.additions": "Additions (+)", "u2_tam.advice_type": "Advice Type", "u2_tam.advice_type.plural": "Advice Types", "u2_tam.amount": "Amount", "u2_tam.amounts": "Amounts", "u2_tam.base": "Base", "u2_tam.beginning_of_year": "Beginning of Year", "u2_tam.beginning_of_year.short": "Beg. of Year", "u2_tam.cash_income_tax": "Cash Income Tax", "u2_tam.compensation_of_tax_credits_accrued_as_dta": "Compensation of tax credits accrued as DTA", "u2_tam.compensation_of_tax_credits_not_accrued_as_dta": "Compensation of tax credits not accrued as DTA", "u2_tam.compensation_of_tax_losses_accrued_as_dta": "Compensation of tax losses accrued as DTA", "u2_tam.compensation_of_tax_losses_not_accrued_as_dta": "Compensation of tax losses not accrued as DTA", "u2_tam.compliance_fee": "Compliance Fee", "u2_tam.compliance_fee.short": "Compl. Fee", "u2_tam.compliance_fees": "Compliance fees", "u2_tam.compliance_tax_advisor": "Compliance Tax Advisor", "u2_tam.compliance_tax_advisor.short": "Compl. Tax Advisor", "u2_tam.consumption": "Consumption (-)", "u2_tam.correction": "Correction (+/-)", "u2_tam.credit_type": "Credit Type", "u2_tam.currency": "<PERSON><PERSON><PERSON><PERSON>", "u2_tam.details": "Details", "u2_tam.details_of_perm_differences": "Details of perm. differences", "u2_tam.details_of_temp_differences": "Details of temp. differences", "u2_tam.documentation_available": "Documentation Available", "u2_tam.documentation_available.short": "Doc. Available", "u2_tam.documentation_required": "Documentation Required", "u2_tam.documentation_required.short": "<PERSON>. Required", "u2_tam.end_of_year": "End of Year", "u2_tam.etr": "ETR", "u2_tam.etr_ifrs": "ETR (IFRS)", "u2_tam.gross_risk": "Gross Risk", "u2_tam.gross_risk_boy": "Gross Risk BOY", "u2_tam.gross_risk_eoy": "Gross Risk EOY", "u2_tam.identified_by_tax_admin": "Identified by Tax Admin (+/-)", "u2_tam.identified_by_tax_admin.short": "Ident. by Tax Adm.", "u2_tam.income_tax": "Income Tax", "u2_tam.income_tax_planning": "Income Tax Planning", "u2_tam.income_tax_planning.plural": "Income Tax Planning", "u2_tam.less": "Less (-)", "u2_tam.loss_carry_forward": "Loss Carry Forward", "u2_tam.loss_carry_forward.plural": "Loss Carry Forwards", "u2_tam.loss_restriction": "Loss Restriction", "u2_tam.loss_restriction.plural": "Loss Restrictions", "u2_tam.loss_type": "Loss Type", "u2_tam.loss_type.plural": "Loss Types", "u2_tam.module_name": "Tax Assessment Management", "u2_tam.module_name.short": "TAM", "u2_tam.p_l_effect_cy": "P/L Effect CY", "u2_tam.parties": "Parties", "u2_tam.partner_unit_name": "Partner Unit Name", "u2_tam.perm_differences": "Perm. Differences", "u2_tam.permanent": "Permanent", "u2_tam.planning_period": "Planning Period", "u2_tam.potential_tax_liabilities": "Potential Tax Liabilities", "u2_tam.potential_tax_liabilities.short": "Pot. Tax Liab.", "u2_tam.pricing_method": "Pricing Method", "u2_tam.profit_before_tax": "Profit Before Tax", "u2_tam.profit_before_tax.short": "Profit b. Tax", "u2_tam.profit_before_tax_calculated": "Profit Before Tax Calculated", "u2_tam.profit_before_tax_calculated.short": "Profit b. Tax cal.", "u2_tam.reason": "Reason", "u2_tam.restriction_reason": "Restriction Reason", "u2_tam.restriction_reason.plural": "Restriction Reasons", "u2_tam.restrictions": "Restrictions", "u2_tam.risk_probability": "Risk Probability", "u2_tam.risk_probability.short": "Risk Prob.", "u2_tam.risk_type": "Risk Type", "u2_tam.risk_type.plural": "Risk Types", "u2_tam.special_advisory_fees": "Special Advisory Fees", "u2_tam.special_advisory_fees.short": "Spec. Adv. Fees", "u2_tam.specification": "Specification", "u2_tam.specification.plural": "Specifications", "u2_tam.tax_advisor": "Tax Advisor", "u2_tam.tax_assessment_status": "Tax Assessment Status", "u2_tam.tax_assessment_status.plural": "Tax Assessment Status", "u2_tam.tax_audit_risk": "Tax Audit Risk", "u2_tam.tax_audit_risk.plural": "Tax Audit Risks", "u2_tam.tax_base": "Tax Base", "u2_tam.tax_consulting_fee": "Tax Consulting Fee", "u2_tam.tax_consulting_fee.plural": "Tax Consulting Fees", "u2_tam.tax_credit": "Tax Credit", "u2_tam.tax_credit.plural": "Tax Credits", "u2_tam.tax_credit_type": "Tax Credit Type", "u2_tam.tax_credit_type.plural": "Tax Credit Types", "u2_tam.tax_litigation": "Tax Litigation", "u2_tam.tax_litigation.plural": "Tax Litigations", "u2_tam.tax_month": "Tax. Month", "u2_tam.tax_rate": "Tax Rate", "u2_tam.tax_rate.plural": "Tax Rates", "u2_tam.tax_relevant_restriction": "Tax Relevant Restriction", "u2_tam.tax_relevant_restriction.plural": "Tax Relevant Restrictions", "u2_tam.tax_type": "Tax Type", "u2_tam.taxable_income": "Taxable Income", "u2_tam.taxation_month": "Taxation Month", "u2_tam.taxation_year": "Taxation Year", "u2_tam.temp_differences": "Temp. Differences", "u2_tam.temporary": "Temporary", "u2_tam.temporary_permanent_risk": "Temporary/Permanent Risk", "u2_tam.transaction_type": "Transaction Type", "u2_tam.transfer_pricing": "Transfer Pricing", "u2_tam.transfer_pricing.plural": "Transfer Pricing", "u2_tam.type_of_advice": "Type of Advice", "u2_tam.type_of_risk": "Type of Risk", "u2_tam.valid_from": "<PERSON><PERSON>", "u2_tam.valid_to": "<PERSON><PERSON>", "u2_tam.withholding_tax_payable": "Withholding Tax Payable", "u2_tam.withholding_tax_payable.short": "With. Tax Pay.", "u2_tcm.assessment_type": "Assessment Type", "u2_tcm.assessment_type.plural": "Assessment Types", "u2_tcm.currency": "<PERSON><PERSON><PERSON><PERSON>", "u2_tcm.date_received": "Date Received", "u2_tcm.deadline_type": "Deadline Type", "u2_tcm.declaration_type": "Declaration Type", "u2_tcm.declaration_type.plural": "Declaration Types", "u2_tcm.details": "Details", "u2_tcm.document_date": "Document Date", "u2.due_date": "Due Date", "u2_tcm.filing_date": "Filing Date", "u2_tcm.module_name": "Tax Compliance Monitor", "u2_tcm.module_name.short": "TCM", "u2_tcm.other_deadline": "Other Deadline", "u2_tcm.other_deadline.plural": "Other Deadlines", "u2_tcm.penalty": "Penalty", "u2_tcm.penalty_for_late_filing": "Penalty for Late Filing", "u2_tcm.tax_assessment_monitor": "Tax Assessment Monitor", "u2_tcm.tax_assessment_monitor.plural": "Tax Assessment Monitor", "u2_tcm.tax_authority_audit_objection": "Tax Authority / Audit Objection", "u2_tcm.tax_authority_audit_objection.plural": "Tax Authority / Audit Objections", "u2_tcm.tax_filing_monitor": "Tax Filing Monitor", "u2_tcm.tax_filing_monitor.plural": "Tax Filing Monitor", "u2_tcm.tax_month": "Tax. Month", "u2_tcm.tax_type": "Tax Type", "u2_tcm.taxation_month": "Taxation Month", "u2_tcm.taxation_year": "Taxation Year", "u2_tcm.type_of_assessment": "Type of Assessment", "u2_tcm.type_of_declaration": "Type of Declaration", "u2_tpm.accumulated_earnings_value": "Accumulated Earnings", "u2_tpm.accumulated_earnings_value_base_value": "Accumulated Earnings", "u2_tpm.accumulated_earnings_value_base_value.short": "Acc. Earn.", "u2_tpm.accumulated_earnings_value_group_value": "Accumulated Earnings [Group Value]", "u2_tpm.accumulated_earnings_value_group_value.short": "Acc. Earn. [Group Value]", "u2_tpm.accumulated_earnings_value_local_value": "Accumulated Earnings [Local Value]", "u2_tpm.accumulated_earnings_value_local_value.short": "Acc. Earn. [Local Value]", "u2_tpm.add_first_section": "Add First Section", "u2_tpm.all_transaction_types": "All Transaction Types", "u2_tpm.arms_length": "Arm's Length", "u2_tpm.arms_length.help": "Confirm whether the transaction is at arm's length", "u2_tpm.associated_units": "Associated Units", "u2_tpm.base": "Base", "u2_tpm.base_currency": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.base_currency.short": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.base_template": "Base Template", "u2_tpm.billing_type": "Billing Type", "u2_tpm.business_activity": "Business Activity", "u2_tpm.business_activity.plural": "Business Activities", "u2_tpm.carry_forward": "Carry Forward", "u2_tpm.carry_forward.help": "Set this flag to mark the transaction to be carried forward during a data transfer to another period.", "u2_tpm.classification": "Classification", "u2_tpm.contents": "Contents", "u2_tpm.contract_date": "Date", "u2_tpm.contract_name": "Contract Name", "u2_tpm.contract_type": "Contract Type", "u2_tpm.contracts.is_empty": "No relevant contracts found.", "u2_tpm.country_by_country_report": "Country by Country Report", "u2_tpm.country_by_country_report.plural": "Country by Country Reports", "u2_tpm.country_founded": "Country Founded", "u2_tpm.country_not_assigned": "No country is associated with the units of this report.", "u2_tpm.currency": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.data_to_show": "Data to show", "u2_tpm.details": "Details", "u2_tpm.document_widget": "Document Widget", "u2_tpm.documentation": "Documentation", "u2_tpm.employees": "Employees", "u2_tpm.file": "File", "u2_tpm.file.help": "To use the name of the file leave this field empty.", "u2_tpm.financial_data": "Financial Data", "u2_tpm.financial_data.plural": "Financial Data", "u2_tpm.financial_data_records.is_empty": "No relevant financial data found.", "u2_tpm.founded": "Founded", "u2_tpm.group_currency": "Group Currency", "u2_tpm.group_currency.short": "Group", "u2_tpm.image": "Image", "u2_tpm.image_width": "Image width", "u2_tpm.image_width.help": "A pixel value (e.g.: 200px) or “auto” (original size). The height will be calculated proportionally according to the width.", "u2_tpm.income_tax_accrued_value": "Income Tax Accrued (current year)", "u2_tpm.income_tax_accrued_value_base_value": "Income Tax Accrued (current year)", "u2_tpm.income_tax_accrued_value_base_value.short": "Inc. Tax Acc.", "u2_tpm.income_tax_accrued_value_group_value": "Income Tax Accrued (current year) [Group Value]", "u2_tpm.income_tax_accrued_value_group_value.short": "Inc. Tax Acc. [Group Value]", "u2_tpm.income_tax_accrued_value_local_value": "Income Tax Accrued (current year) [Local Value]", "u2_tpm.income_tax_accrued_value_local_value.short": "Inc. Tax Acc. [Local Value]", "u2_tpm.income_tax_paid_value": "Income Tax Paid (on cash basis)", "u2_tpm.income_tax_paid_value_base_value": "Income Tax Paid (on cash basis)", "u2_tpm.income_tax_paid_value_base_value.short": "Inc. Tax Paid", "u2_tpm.income_tax_paid_value_group_value": "Income Tax Paid (on cash basis) [Group Value]", "u2_tpm.income_tax_paid_value_group_value.short": "Inc. Tax Paid [Group Value]", "u2_tpm.income_tax_paid_value_local_value": "Income Tax Paid (on cash basis) [Local Value]", "u2_tpm.income_tax_paid_value_local_value.short": "Inc. Tax Paid [Local Value]", "u2_tpm.key_figure": "Key Figure", "u2_tpm.key_figures": "Key Figures", "u2_tpm.legal_form": "Legal Form", "u2_tpm.legal_name": "Legal Name", "u2_tpm.local_currency": "Local Currency", "u2_tpm.local_currency.short": "Local", "u2_tpm.local_file": "Local File", "u2_tpm.local_file.plural": "Local Files", "u2_tpm.main_business_activity": "Main Business Activity", "u2_tpm.main_business_activity.is_empty": "No relevant business activities found.", "u2_tpm.main_business_activity.plural": "Main Business Activities", "u2_tpm.main_contracts": "Main Contracts", "u2_tpm.master_file": "Master File", "u2_tpm.master_file.plural": "Master Files", "u2_tpm.module_name": "Transfer Pricing Monitor", "u2_tpm.module_name.short": "TPM", "u2_tpm.n_a": "n/a", "u2_tpm.name": "Name", "u2_tpm.not_required": "Not Required", "u2_tpm.number_of_employees": "Number of Employees", "u2_tpm.number_of_units": "Number of Units", "u2_tpm.parties": "Parties", "u2_tpm.partner_doc": "Partner Doc.", "u2_tpm.partner_unit.help": "To select a partner unit, please uncheck the first checkbox and then choose from the list.", "u2_tpm.partner_unit_country": "Partner Unit Country", "u2_tpm.partner_unit_is_to_be_documented": "Partner Unit is to be Documented", "u2_tpm.partner_unit_name": "Partner Unit Name", "u2.partner_unit_ref_id": "Partner Unit Ref. ID", "u2_tpm.postal_address": "Postal Address", "u2_tpm.profit_loss_before_income_tax_value": "Profit/Loss before Income Tax", "u2_tpm.profit_loss_before_income_tax_value_base_value": "Profit/Loss before Income Tax", "u2_tpm.profit_loss_before_income_tax_value_base_value.short": "P/L bef. Inc. Tax", "u2_tpm.profit_loss_before_income_tax_value_group_value": "Profit/Loss before Income Tax [Group Value]", "u2_tpm.profit_loss_before_income_tax_value_group_value.short": "P/L bef. Inc. Tax [Group Value]", "u2_tpm.profit_loss_before_income_tax_value_local_value": "Profit/Loss before Income Tax [Local Value]", "u2_tpm.profit_loss_before_income_tax_value_local_value.short": "P/L bef. Inc. Tax [Local Value]", "u2_tpm.providing_entity": "Providing Entity", "u2_tpm.providing_entity_country": "Providing Entity Country", "u2_tpm.providing_entity_name": "Providing Entity Name", "u2_tpm.receiving_entity": "Receiving Entity", "u2_tpm.receiving_entity_country": "Receiving Entity Country", "u2_tpm.receiving_entity_name": "Receiving Entity Name", "u2_tpm.ref_id": "Ref Id", "u2_tpm.register_number": "Register Number", "u2_tpm.registry_place": "Registry Place", "u2_tpm.required": "Required", "u2_tpm.select_whether_the_partner_unit_needs_to_be_documented": "Select whether the Partner Unit needs to be documented", "u2_tpm.select_whether_the_unit_needs_to_be_documented": "Select whether the Unit needs to be documented", "u2_tpm.show_template_list": "Show Template List", "u2_tpm.snapshot": "Units", "u2_tpm.stated_capital_value": "Stated Capital", "u2_tpm.stated_capital_value_base_value": "Stated Capital", "u2_tpm.stated_capital_value_base_value.short": "St. Capital", "u2_tpm.stated_capital_value_group_value": "Stated Capital [Group Value]", "u2_tpm.stated_capital_value_group_value.short": "St. Capital [Group Value]", "u2_tpm.stated_capital_value_local_value": "Stated Capital [Local Value]", "u2_tpm.stated_capital_value_local_value.short": "St. Capital [Local Value]", "u2_tpm.sub_type": "Sub-Type", "u2_tpm.tangible_assets_value": "Tangible Assets other than Cash and Cash Equivalents", "u2_tpm.tangible_assets_value_base_value": "Tangible Assets other than Cash and Cash Equivalents", "u2_tpm.tangible_assets_value_base_value.short": "Tang. Assets other than Cash", "u2_tpm.tangible_assets_value_group_value": "Tangible Assets other than Cash and Cash Equivalents [Group Value]", "u2_tpm.tangible_assets_value_group_value.short": "Tang. Assets other than Cash [Group Value]", "u2_tpm.tangible_assets_value_local_value": "Tangible Assets other than Cash and Cash Equivalents [Local Value]", "u2_tpm.tangible_assets_value_local_value.short": "Tang. Assets other than Cash [Local Value]", "u2_tpm.tax_number": "Tax Number", "u2_tpm.templates": "Templates", "u2_tpm.text": "Text", "u2_tpm.the_name_of_this_file_is_restricted": "(The name of this file is restricted)", "u2_tpm.this_document_has_no_content": "This document has no content", "u2_tpm.total": "Total", "u2_tpm.totalRevenue_value_local_value": "Total Revenue [Local Value]", "u2_tpm.totalRevenue_value_local_value.short": "Total Rev. [Local Value]", "u2_tpm.total_for_given_document_country": "Total For %current_document_country%", "u2_tpm.total_revenue_related_value": "Total Revenue with Related Party", "u2_tpm.total_revenue_related_value_base_value": "Total Revenue with Related Party", "u2_tpm.total_revenue_related_value_base_value.short": "Rev. w. rel. party", "u2_tpm.total_revenue_related_value_group_value": "Total Revenue with Related Party [Group Value]", "u2_tpm.total_revenue_related_value_group_value.short": "Rev. w. rel. party [Group Value]", "u2_tpm.total_revenue_related_value_local_value": "Total Revenue with Related Party [Local Value]", "u2_tpm.total_revenue_related_value_local_value.short": "Rev. w. rel. party [Local Value]", "u2_tpm.total_revenue_unrelated_value": "Total Revenue with Unrelated Party", "u2_tpm.total_revenue_unrelated_value_base_value": "Total Revenue with Unrelated Party", "u2_tpm.total_revenue_unrelated_value_base_value.short": "Rev. w. unrel. party", "u2_tpm.total_revenue_unrelated_value_group_value": "Total Revenue with Unrelated Party [Group Value]", "u2_tpm.total_revenue_unrelated_value_group_value.short": "Rev. w. unrel. party [Group Value]", "u2_tpm.total_revenue_unrelated_value_local_value": "Total Revenue with Unrelated Party [Local Value]", "u2_tpm.total_revenue_unrelated_value_local_value.short": "Rev. w. unrel. party [Local Value]", "u2_tpm.total_revenue_value": "Total Revenue", "u2_tpm.total_revenue_value.help": "Sum of the total revenue with unrelated party value and the total revenue with related party value.", "u2_tpm.total_revenue_value_base_value": "Total Revenue", "u2_tpm.total_revenue_value_base_value.short": "Total Rev.", "u2_tpm.total_revenue_value_group_value": "Total Revenue [Group Value]", "u2_tpm.total_revenue_value_group_value.short": "Total Rev. [Group Value]", "u2_tpm.total_volume": "Total Volume", "u2_tpm.transaction": "Transaction", "u2_tpm.transaction.plural": "Transactions", "u2_tpm.transaction_amount": "Amount", "u2_tpm.transaction_currency": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.transaction_details": "Transaction details", "u2_tpm.transaction_table": "Transaction Table", "u2_tpm.transaction_type": "Transaction Type", "u2_tpm.transaction_units": "Transaction Units", "u2_tpm.transfer_pricing_method": "Transfer Pricing Method", "u2_tpm.transfer_pricing_method.help": "Choose the appropriate transfer pricing method from the drop down list", "u2_tpm.type": "Type", "u2_tpm.unit_country": "Unit Country", "u2_tpm.unit_doc": "Unit Doc.", "u2.unit_hierarchy_structure_at": "Structure at %date%", "u2_tpm.unit_hierarchy": "Hierarchy", "u2_tpm.unit_is_to_be_documented": "Unit is to be Documented", "u2.unit_name": "Unit Name", "u2.unit_ref_id": "Unit Ref. ID", "u2_tpm.units": "Units", "u2_tpm.units.is_empty": "There are no units associated with this document.", "u2_tpm.units_widget.blocks": "Blocks", "u2_tpm.units_widget.style": "Style", "u2_tpm.units_widget.table": "Table", "u2_tpm.widget.file.not_attached": "The selected file is no longer attached to this section.", "u2_tpm.widget.image.not_attached": "The selected image is no longer attached to this section.", "u2.open_menu": "Open menu", "u2.dashboard.no_dashboard_exists": "It seems no dashboards exist yet!", "u2.dashboard.create_a_dashboard": "Create a dashboard", "u2.ask_an_admin_to_create_one": "Ask an administrator to create one for you.", "u2.view_associated_tasks": "View associated tasks", "u2.ttl": "Time-To-Live", "u2.api_key.plural": "Api-Keys", "u2_core.edit_api_key": "Edit Api-Key", "u2_core.create_api_key": "Create Api-Key", "u2.infinite": "Infinite", "u2.7_days": "7 days", "u2.30_days": "30 days", "u2.60_days": "60 days", "u2.90_days": "90 days", "u2.expand": "Expand", "u2.file_inherited_permissions_help": "Inherited via the security setting on the file", "u2_core.download_file_name": "Download \"%file_name%\"", "u2.number_more": "%number% more", "u2.toggle_attachment_info": "Toggle attachment information", "u2.number_of_decimal_places": "Dacimal Places", "u2.number_of_decimal_places_help": "The number of decimal places to be used for values in this currency", "u2_core.edit_file_name": "Edit \"%file_name%\"", "u2.send_request": "Send request", "u2.file_permissions_request_comment": "Please add a comment to describe what permissions you are requesting and why you need them:", "u2_core.permissions_request_for_given_file": "You are requesting permissions for the file “%file_name%”.", "u2_core.current_permissions": "Current permissions", "u2.dashboard_link_not_found": "Dashboard not found", "u2.dashboard.choose_another_dashboard": "The link for this dashboard may have changed or it may no longer exist. Please choose one of the following dashboards.", "u2.datasheets.plural": "Datasheets", "u2.role_is_inherited_from_role": "This role is inherited from another role.", "u2.user_permissions.record_is_public": "Assigned groups have no effect because \"Visible to all users\" is on", "u2.group_permissions.record_is_public": "Assigned groups have no effect because \"Visible to all users\" is on", "u2.datasheets.select_a_datasheet": "Select a datasheet", "u2.unit_view_parameter": "Unit View Parameters", "u2.group_view_parameter": "Group View Parameters", "u2.datasheets.datasheet_collection.select_datasheets": "Select a datasheet", "u2.select": "Select", "u2.breakdown.close_breakdown": "Close breakdown", "u2.datasheets.datasheet_collection.plural": "Datasheet Collections", "u2.number_of_datasheets": "Number of datasheets", "u2.datasheets.number_of_fields": "Number of fields", "u2.datasheets.datasheet_collection.new": "New Datasheet Collection", "Equity Type - Shares/Participation (real estate share deal)": "Equity Type - Shares/Participation (real estate share deal)", "Other Asset Transfer - Properties (real estate asset deal)": "Other Asset Transfer - Properties (real estate asset deal)", "Insurance": "Insurance", "Fees": "Fees", "Commission": "Commission", "Dividends": "Dividends", "Cost or Revenue": "Cost or Revenue", "Credit Default": "Credit Default", "Interest Rate": "Interest Rate", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "u2.choice.trace_id.plural": "Trace IDs", "u2.choice.trace_id.": "Trace ID", "u2.email.api_key.subject.about_to_expire": "Your Api-Key \"%apiKey%\" is about to expire", "u2.email.api_key.content.api_key_will_expire_at": "API-Key <strong>„%apiKeyName%“</strong> will expire on <strong>%apiKeyExpiresDate%</strong>.", "u2.datasheets.choose_a_datasheet": "Choose a datasheet", "u2.api_key.regenerate_title": "Regenerate Api-Key", "u2.email.api_key.content.if_needed_regenerate": "If this token is still needed, visit <a href=\"%userSettingUrl%\">User Settings</a> to generate an equivalent.", "u2.email.sincerely_universal_units": "Thanks, <br> The Universal Units Team", "u2.feature_toggles": "Experimental Features", "u2.password_min_length": "Password minimum length", "u2.datasheets.previous": "Previous Datasheet", "u2.datasheets.next": "Next Datasheet", "u2.switch_to_group_view": "Switch to group", "u2.switch_to_unit_view": "Switch to unit", "u2.dashboard.widget.filter_results.error_while_fetching_data": "An error occurred while fetching the saved filter results", "u2.preview": "Preview", "u2.size": "Size", "u2.dashboard.widget.plural": "Dashboard Widgets", "u2.dashboard.widget.not_configure_yet": "This widget has not yet been configured and therefore cannot be loaded.", "u2.columns": "Columns", "u2.sort": "Sorting", "u2.saved_filter.sort.column": "Column to sort by", "u2.saved_filter.sort.direction": "Sorting direction", "u2.dashboard.widget.new": "New Dashboard Widget", "u2.dashboard.widget.html": "Custom HTML", "u2.dashboard.widget.filter_results": "Results of a saved filter", "u2.dashboard.widget.up_next": "Today's Tasks", "u2.dashboard.widget.current_week_overview": "Current Week Overview", "u2.widget.size.small": "Small", "u2.widget.size.medium": "Medium", "u2.widget.size.large": "Large", "u2.dashboard_widget.new": "Add Widget", "u2.dashboard.edit_widgets": "Edit Widgets", "u2.showing_filtered_items_out_of_total": "Showing %filtered% of %total% item(s)", "u2.showing_all_items": "Showing all %total% item(s)", "u2.drop_item_here": "Drop item here", "u2.undo": "Undo", "u2.redo": "Redo", "u2.dashboard.widget.filter_statistics.error_while_fetching_data": "An error occurred while fetching the saved filter statistics", "u2.dashboard.widget.filter_statistics": "Filter Statistics", "u2.label": "Label", "u2.color": "Color", "u2.chart_size": "Chart size", "u2.widget.filter_statistic.new_entry_disabled_reason": "The filter statistics widget only supports a maximum of up to 10 saved filters.", "u2.widget.filter_statistic.label_help_text": "The saved filter name will be used if no label has been defined.", "u2.chart_type": "Chart Type", "u2.widget.chart_type.pie": "Pie", "u2.widget.chart_type.doughnut": "Doughnut", "u2.show_percent": "Show Percent", "u2.advanced": "Advanced", "u2.show_schema": "Show schema", "u2.rules": "Rules", "u2.has_rules": "Has rules", "u2_core.import.invalid_headers": "One or more headers in the imported file are not valid: %invalidHeaders%. Valid headers are: %validHeaders%", "u2.choose_your_own": "Choose your own", "u2_core.edit_system_images": "Edit System Images", "u2_core.system_images": "System Images", "u2_core.last_activity": "Last Activity", "u2.help_text": "Help text", "u2.unit_or_partner_unit": "Either Unit or Partner Unit", "u2_core.entities_cannot_be_created_in_closed_period": "Period “%period_name%” is closed. Entities with this period cannot be created.", "u2_core.entities_cannot_be_deleted_in_closed_period": "Period “%period_name%” is closed. Entities with this period cannot be deleted.", "u2.unit_view": "Unit View", "u2.user.no_units_assigned_admin": "Assign some here for the user to work with.", "u2.assign_units": "Assign Units", "u2.user_group.no_units_assigned_admin": "Assign some here for the users of this user group to work with.", "u2.no_units": "No Units", "u2.no_users": "No Users", "u2.unit.no_users_assigned_description": "It looks like this unit does not have users yet.", "u2.contact_admin": "Contact an admin to make some changes for you.", "u2.unit.no_users_assigned_admin": "Assign some here to allow them to use this unit.", "u2.assign_users": "Assign Users", "u2.no_user_groups": "No User Groups", "u2.unit.no_user_groups_assigned_description": "It looks like this unit does not have user groups yet.", "u2.unit.no_user_groups_assigned_admin": "Assign some here to allow users of the groups to work with this unit.", "u2.assign_user_groups": "Assign User Groups", "u2.authorization.no_users_assigned_description": "No users are currently assigned. Add users here to grant them this authorisation.", "u2.authorization_profile.no_user_groups_assigned_description": "No user groups are currently assigned. Add user groups here to grant their members the authorisations connected with this profile.", "u2.authorization.no_user_groups_assigned_description": "No user groups are currently assigned. Add user groups here to grant their members this authorisation.", "u2.authorization_profile.no_users_assigned_description": "No users are currently assigned. Add users here to grant them the authorisations connected with this profile.", "u2.no_roles": "No Roles", "u2.user.no_roles_assigned_description": "It looks like this user does not have roles.", "u2.user.no_roles_assigned_admin": "Assign roles to this user here.", "u2.assign_roles": "Assign Roles", "u2.user_group.no_roles_assigned_description": "It looks like this user group does not have roles. ", "u2.user_group.no_roles_assigned_admin": "Assign roles to this user group here.", "u2.saved_filter_subscription.no_user_groups_assigned_description": "It looks like this subscription does not have any user groups. Add some here to send their members email updates.", "u2.saved_filter.no_users_assigned_description": "This saved filter does not have any users yet. Add some here to allow them to discover this filter.", "u2.add_users": "Add Users", "u2.saved_filter_subscription.no_users_assigned_description": "It looks like this subscription does not have any users. Add some here to start sending them updates via email.", "u2.saved_filter.no_user_groups_assigned_description": "This saved filter does not have any groups added yet. Add some here to allow their members to discover this filter.", "u2.dashboard.no_users_assigned_description": "This dashboard does not currently have any assigned users. Assign some here to grant them access.", "u2.dashboard.no_user_groups_assigned_description": "This dashboard does not currently have any assigned user groups. Assign some here to grant their members access.", "u2.review.no_reviews": "No Reviews", "u2.review.task_not_reviewed": "This task has not yet been reviewed.", "u2.review.manual_review_disabled_info": "Manual review is not enabled for this workflow. Reviews will only be added during transitions using workflow actions.", "u2.no_user_group_permissions": "No Group Permissions", "u2.assign_user_group_permissions_admin": "Assign permissions to some user groups here.", "u2.assign_user_group_permissions": "Assign Group Permissions", "u2.no_user_permissions": "No User Permissions", "u2.assign_user_permissions_admin": "Assign permissions to some users here.", "u2.assign_user_permissions": "Assign User Permissions", "u2.datasheets.no_user_group_permissions_description": "There are no group permissions set for this datasheet.", "u2.datasheets.datasheet_collection.no_user_group_permissions_description": "There are no group permissions set for this datasheet collection.", "u2.document_template.no_user_group_permissions_description": "There are no group permissions set for this template.", "u2.document.no_user_group_permissions_description": "There are no group permissions set for this document.", "u2.datasheets.no_user_permissions_description": "There are no user permissions set for this datasheet.", "u2.datasheets.datasheet_collection.no_user_permissions_description": "There are no user permissions set for this datasheet collection.", "u2.document_template.no_user_permissions_description": "There are no user permissions set for this template.", "u2.document.no_user_permissions_description": "There are no user permissions set for this document.", "u2.audit_log.no_changes_description": "The updates will show up here", "u2.no_attachments_description": "Any added attachments will show up here.", "u2.user.no_user_groups_assigned_admin": "Assign some groups here.", "u2.add_user_groups": "Add User Groups", "u2.task_checklist.no_checks_title": "No Checks", "u2.user_group.no_units_assigned_description": "It looks like this user group does not have any units.", "u2.user_group.no_users_assigned_description": "It looks like this user group does not have any users.", "u2.user_group.no_users_assigned_admin": "Assign users to this user group here.", "u2.user.no_user_groups_assigned_description": "It looks like this user does not have any user groups.", "u2.user.no_units_assigned_description": "It looks like this user does not have any units yet.", "u2.new_record.save_first": "This functionality is available after creation. Save the record to continue.", "u2_core.add_unit.help_select_a_hierarchy_and_save": "To add units, please select a hierarchy and save.", "u2.no_authorisations": "No Authorisations", "u2.user_group.no_authorisations_assigned_description": "This user group does not have any authorisations assigned. ", "u2.user.no_authorisations_assigned_description": "This user does not have any authorisations assigned.", "u2.assign_authorisations": "Assign Authorisations", "u2.no_authorisations_assigned_admin": "Assign authorisations to see them here.", "u2.datasheets.item.types.number": "Number", "u2.unit.help": "To select a unit, please uncheck the first checkbox and then choose from the list.", "u2.previous_period_is": "The previous period is", "u2.period_has_no_previous_period": "This period has no previous period.", "u2.copy": "Copy", "u2.paste": "Paste", "u2.task_checklist.show_checks_other_status": "Show checks for other statuses", "u2.datasheets.item_has_no_formula": "This item has no formula.", "u2_core.show_calendar": "Show Calendar", "u2.target_route_is_current_route": "You are already on this page", "u2.inspect.already_inspecting": "You are currently inspecting this field.", "u2.password_must_have_min_password_length": "Must have a minimum length of %min_password_length% characters.", "u2.collapse": "Collapse", "u2.datasheets.item_country_report": "Item Report: Country", "u2.datasheets.choose_an_item_country_report": "Choose an Item Report: Country", "u2.document.collapse_with_subsections": "Collapse with subsections", "u2.document.expand_with_subsections": "Expand with subsections", "u2.document.expand_all_sections": "Expand all sections", "u2.document.collapse_all_sections": "Collapse all sections", "u2.not_editable": "Not editable", "u2.editable": "Editable", "u2.document.section_controls": "Section Controls", "u2.field_inspector.show": "Show inspector", "u2.field_inspector": "Inspector", "u2.select_a_field": "Select a field", "u2.field_inspector.select_a_field": "Select a field in this datasheet to see more information about that field.", "u2.field_inspector.field_not_on_datasheet": "This field is not located on this datasheet.", "u2.inspect": "Inspect", "u2.inspect.tooltip": "Click here to see the field in the datasheet.", "u2.paste.field_is_disabled": "Paste not possible. This field is disabled.", "u2.template": "Template", "u2.group": "Group", "u2.more_than_one_unit_period_found_matching_this_configuration": "More than one task is assigned to this datasheet collection. Please ensure that only one task is assigned in order to allow edits.", "u2.task.assign_datasheet_collection": "Assign datasheet collection", "u2.unrestricted": "Unrestricted", "u2.unrestricted_to_any_datasheet_collection": "This task is unrestricted. It will be available to the following collections:", "u2.task.restrict": "Restrict", "u2.update_tasks": "Task configuration conflict", "u2.show_on_list": "Show on list", "u2.dashboard.widget.filter_results.the_following_invalid_columns": "The configuration contains the following invalid columns:", "u2.dashboard.widget.filter_results.remove_invalid_columns": "Remove invalid columns", "u2.task.datasheet_collection.unable_to_add_collection": "Datasheet collection \"%collectionName%\" could not be added to task \"%taskName%\".", "u2.task.datasheet_collection.unable_to_remove_collection": "Datasheet collection \"%collectionName%\" could not be removed from task \"%taskName%\".", "u2.datasheets.datasheet_collection": "Datasheet Collection", "Undrawn credit facilities": "Undrawn credit facilities", "u2.datasheets.collection.plural": "Collections", "u2.datasheets.sheet.plural": "Sheets", "u2.document_template.field_disabled_choose_file_first": "You need to select a file first before you can edit the name and description.", "u2_document.cannot_perform_action_while_editing": "This action cannot be performed while editing sections", "u2.route.deprecated": "You have been redirected because the given URL no longer exists. Please update any bookmarks to use the current URL instead.", "u2.field_inspector.configure_field_inspector": "Configure field inspector", "u2.field_inspector.show_field_select": "Show field selector", "u2.field_inspector.show_colors": "Show colours", "u2.watchers.view": "Show watchers", "u2_structureddocument.new_section_content": "New section content", "u2_structureddocument.new_section": "New section", "u2.item.formula.element.previous_period_value": "The value of this item is taken from the previous period.", "u2_structureddocument.add_section": "Add Section", "u2.inspect.insufficient_context": "It is not possible to navigate in the current context.", "u2.field_inspector.show_values": "Show values", "u2.field.click_to_create": "Click to create this field ", "u2.datasheets.unassigned_fields": "Unassigned fields", "u2.datasheets.missing_template": "This datasheet has no template. Therefore, all fields are marked as unassigned.", "u2.datasheets.all_fields_assigned": "All fields are used in the layout", "u2.datasheets.field_disabled": "This field is disabled.", "u2.field.name_placeholder": "Search to view suggestions", "u2.datasheets.missing_fields": "Missing fields", "u2.no_address": "No address has been provided", "u2.enter_address": "Enter an address", "u2.remove_address": "Remove address", "u2.datasheet.missing_field": "Missing field", "u2.off_canvas_menu": "Off-Canvas Menu", "u2.warning_unsaved_changes_will_be_lost": "Warning: Unsaved changes will be lost.", "u2.global_search": "Global Search", "u2.global_search.description": "Search globally", "u2.transaction.transaction_volume": "Quantity", "u2.transaction.underlying_contract": "Underlying Contract", "u2.transaction.unit_standard_taxation_applicable": "Standard taxation applicable", "u2.transaction.partner_unit_standard_taxation_applicable": "Standard taxation applicable", "u2.transaction.unit_standard_taxation_applicable.help": "The business transaction is subject to standard taxation in the relevant tax jurisdiction.", "u2.transaction.partner_unit_standard_taxation_applicable.help": "The business transaction is subject to standard taxation in the relevant tax jurisdiction.", "u2.transaction.transaction_volume.help": "e.g. 1000 Kg, 1000 pieces, n/a", "u2.transaction.underlying_contract.help": "Reference to underlying contract (e.g. contract number)", "u2.no_matching_option_found": "Unknown selected option", "u2.no_matching_option_found.help": "Either the resource was not found or you do not have the required permissions to view it.", "u2.no_description": "This record has no description.", "u2.datasheet.missing_configuration": "Unable to display the template due to missing parameters.", "u2.security.roles.period_manager": "Period Manager", "u2.security.roles.unit_manager": "Unit Manager"}}}}