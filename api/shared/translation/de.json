{"translations": {"de": {"validators": {"This value should be false.": "Dieser Wert sollte false sein.", "This value should be true.": "Dieser Wert sollte true sein.", "This value should be of type {{ type }}.": "Dieser Wert sollte vom Typ {{ type }} sein.", "This value should be blank.": "Dieser Wert sollte leer sein.", "The value you selected is not a valid choice.": "Sie haben einen ungültigen Wert ausgewählt.", "You must select at least {{ limit }} choice.|You must select at least {{ limit }} choices.": "Sie müssen mindestens {{ limit }} Möglichkeit wählen.|Sie müssen mindestens {{ limit }} Möglichkeiten wählen.", "You must select at most {{ limit }} choice.|You must select at most {{ limit }} choices.": "Sie dürfen höchstens {{ limit }} Möglichkeit wählen.|Sie dürfen höchstens {{ limit }} Möglichkeiten wählen.", "One or more of the given values is invalid.": "Einer oder mehrere der angegebenen Werte sind ungültig.", "This field was not expected.": "<PERSON><PERSON> wurde nicht erwartet.", "This field is missing.": "<PERSON><PERSON> fehlt.", "This value is not a valid date.": "Dieser Wert entspricht keiner gültigen Datumsangabe.", "This value is not a valid datetime.": "Dieser Wert entspricht keiner gültigen Datums- und Zeitangabe.", "This value is not a valid email address.": "Dieser Wert ist keine gültige E-Mail-Adresse.", "The file could not be found.": "Die Datei wurde nicht gefunden.", "The file is not readable.": "Die Datei ist nicht lesbar.", "The file is too large ({{ size }} {{ suffix }}). Allowed maximum size is {{ limit }} {{ suffix }}.": "Die Datei ist zu groß ({{ size }} {{ suffix }}). Die maximal zulässige Größe beträgt {{ limit }} {{ suffix }}.", "The mime type of the file is invalid ({{ type }}). Allowed mime types are {{ types }}.": "Der Dateityp ist ungültig ({{ type }}). Erlaubte Dateitypen sind {{ types }}.", "This value should be {{ limit }} or less.": "Dieser Wert sollte kleiner oder gleich {{ limit }} sein.", "This value is too long. It should have {{ limit }} character or less.|This value is too long. It should have {{ limit }} characters or less.": "Diese Zeichenkette ist zu lang. Sie sollte höchstens {{ limit }} Zeichen haben.|Diese Zeichenkette ist zu lang. Sie sollte höchstens {{ limit }} Zeichen haben.", "This value should be {{ limit }} or more.": "Dieser Wert sollte größer oder gleich {{ limit }} sein.", "This value is too short. It should have {{ limit }} character or more.|This value is too short. It should have {{ limit }} characters or more.": "Diese Zeichenkette ist zu kurz. Sie sollte mindestens {{ limit }} Zeichen haben.|Diese Zeichenkette ist zu kurz. Sie sollte mindestens {{ limit }} Zeichen haben.", "This value should not be blank.": "Dieser Wert sollte nicht leer sein.", "This value should not be null.": "Dieser Wert sollte nicht null sein.", "This value should be null.": "Dieser Wert sollte null sein.", "This value is not valid.": "Dieser Wert ist nicht gültig.", "This value is not a valid time.": "Dieser Wert entspricht keiner gültigen Zeitangabe.", "This value is not a valid URL.": "Dieser Wert ist keine gültige URL.", "The two values should be equal.": "Die beiden Werte sollten identisch sein.", "The file is too large. Allowed maximum size is {{ limit }} {{ suffix }}.": "Die Datei ist zu groß. Die maximal zulässige Größe beträgt {{ limit }} {{ suffix }}.", "The file is too large.": "Die Datei ist zu groß.", "The file could not be uploaded.": "Die Datei konnte nicht hochgeladen werden.", "This value should be a valid number.": "Dieser Wert sollte eine gültige Zahl sein.", "This file is not a valid image.": "Diese Date<PERSON> ist kein gültiges Bild.", "This is not a valid IP address.": "Dieser Wert ist keine gültige IP-Adresse.", "This value is not a valid language.": "Dieser Wert entspricht keiner gültigen Sprache.", "This value is not a valid locale.": "Dieser Wert entspricht keinem gültigen Gebietsschema.", "This value is not a valid country.": "Dieser Wert entspricht keinem gültigen Land.", "This value is already used.": "Dieser Wert wird bereits verwendet.", "The size of the image could not be detected.": "Die Größe des Bildes konnte nicht ermittelt werden.", "The image width is too big ({{ width }}px). Allowed maximum width is {{ max_width }}px.": "Die Bildbreite ist zu groß ({{ width }}px). Die maximal zulässige Breite beträgt {{ max_width }}px.", "The image width is too small ({{ width }}px). Minimum width expected is {{ min_width }}px.": "Die Bildbreite ist zu gering ({{ width }}px). Die erwartete Mindestbreite beträgt {{ min_width }}px.", "The image height is too big ({{ height }}px). Allowed maximum height is {{ max_height }}px.": "Die Bildhöhe ist zu groß ({{ height }}px). Die maximal zulässige Höhe beträgt {{ max_height }}px.", "The image height is too small ({{ height }}px). Minimum height expected is {{ min_height }}px.": "Die Bildhöhe ist zu gering ({{ height }}px). Die erwartete Mindesthöhe beträgt {{ min_height }}px.", "This value should be the user's current password.": "Dieser Wert sollte dem aktuellen Benutzerpasswort entsprechen.", "This value should have exactly {{ limit }} character.|This value should have exactly {{ limit }} characters.": "Dieser Wert sollte genau {{ limit }} Zeichen lang sein.|Dieser Wert sollte genau {{ limit }} Zeichen lang sein.", "The file was only partially uploaded.": "Die Datei wurde nur teilweise hochgeladen.", "No file was uploaded.": "<PERSON>s wurde keine Date<PERSON> ho<PERSON>.", "No temporary folder was configured in php.ini.": "Es wurde kein temporärer Ordner in der php.ini konfiguriert oder der temporäre Ordner existiert nicht.", "Cannot write temporary file to disk.": "Kann die temporäre Datei nicht speichern.", "A PHP extension caused the upload to fail.": "Eine PHP-Erweiterung verhinderte den Upload.", "This collection should contain {{ limit }} element or more.|This collection should contain {{ limit }} elements or more.": "Diese Sammlung sollte {{ limit }} oder mehr Elemente beinhalten.|Diese Sammlung sollte {{ limit }} oder mehr Elemente beinhalten.", "This collection should contain {{ limit }} element or less.|This collection should contain {{ limit }} elements or less.": "Diese Sammlung sollte {{ limit }} oder weniger Elemente beinhalten.|Diese Sammlung sollte {{ limit }} oder weniger Elemente beinhalten.", "This collection should contain exactly {{ limit }} element.|This collection should contain exactly {{ limit }} elements.": "Diese Sammlung sollte genau {{ limit }} Element beinhalten.|Diese Sammlung sollte genau {{ limit }} Elemente beinhalten.", "Invalid card number.": "Ungültige Kartennummer.", "Unsupported card type or invalid card number.": "Nicht unterstützter Kartentyp oder ungültige Kartennummer.", "This is not a valid International Bank Account Number (IBAN).": "Dieser Wert ist keine gültige Internationale Bankkontonummer (IBAN).", "This value is not a valid ISBN-10.": "Dieser Wert entspricht keiner gültigen ISBN-10.", "This value is not a valid ISBN-13.": "Dieser Wert entspricht keiner gültigen ISBN-13.", "This value is neither a valid ISBN-10 nor a valid ISBN-13.": "<PERSON>ser Wert ist weder eine gültige ISBN-10 noch eine gültige ISBN-13.", "This value is not a valid ISSN.": "Dieser Wert ist keine gültige ISSN.", "This value is not a valid currency.": "Dieser Wert ist keine gültige Währung.", "This value should be equal to {{ compared_value }}.": "Dieser Wert sollte gleich {{ compared_value }} sein.", "This value should be greater than {{ compared_value }}.": "Dieser Wert sollte größer als {{ compared_value }} sein.", "This value should be greater than or equal to {{ compared_value }}.": "Dieser Wert sollte größer oder gleich {{ compared_value }} sein.", "This value should be identical to {{ compared_value_type }} {{ compared_value }}.": "Dieser Wert sollte identisch sein mit {{ compared_value_type }} {{ compared_value }}.", "This value should be less than {{ compared_value }}.": "Dieser Wert sollte kleiner als {{ compared_value }} sein.", "This value should be less than or equal to {{ compared_value }}.": "Dieser Wert sollte kleiner oder gleich {{ compared_value }} sein.", "This value should not be equal to {{ compared_value }}.": "Dieser Wert sollte nicht {{ compared_value }} sein.", "This value should not be identical to {{ compared_value_type }} {{ compared_value }}.": "Dieser Wert sollte nicht identisch sein mit {{ compared_value_type }} {{ compared_value }}.", "The image ratio is too big ({{ ratio }}). Allowed maximum ratio is {{ max_ratio }}.": "Das Seitenverhältnis des Bildes ist zu groß ({{ ratio }}). Der erlaubte Maximalwert ist {{ max_ratio }}.", "The image ratio is too small ({{ ratio }}). Minimum ratio expected is {{ min_ratio }}.": "Das Seitenverhältnis des Bildes ist zu klein ({{ ratio }}). Der erwartete Minimalwert ist {{ min_ratio }}.", "The image is square ({{ width }}x{{ height }}px). Square images are not allowed.": "Das Bild ist quadratisch ({{ width }}x{{ height }}px). Quadratische Bilder sind nicht erlaubt.", "The image is landscape oriented ({{ width }}x{{ height }}px). Landscape oriented images are not allowed.": "Das Bild ist im Querformat ({{ width }}x{{ height }}px). Bilder im Querformat sind nicht erlaubt.", "The image is portrait oriented ({{ width }}x{{ height }}px). Portrait oriented images are not allowed.": "Das Bild ist im Hochformat ({{ width }}x{{ height }}px). Bilder im Hochformat sind nicht erlaubt.", "An empty file is not allowed.": "Eine leere Datei ist nicht erlaubt.", "The host could not be resolved.": "Der Hostname konnte nicht aufgelöst werden.", "This value does not match the expected {{ charset }} charset.": "Dieser Wert entspricht nicht dem erwarteten Zeichensatz {{ charset }}.", "This is not a valid Business Identifier Code (BIC).": "Dieser Wert ist keine gültige internationale Bankleitzahl (BIC).", "Error": "<PERSON><PERSON>", "This is not a valid UUID.": "Dieser Wert ist keine gültige UUID.", "This value should be a multiple of {{ compared_value }}.": "Dieser Wert sollte ein Vielfaches von {{ compared_value }} sein.", "This Business Identifier Code (BIC) is not associated with IBAN {{ iban }}.": "Diese internationale Bankleitzahl (BIC) ist nicht mit der IBAN {{ iban }} assoziiert.", "This value should be valid JSON.": "Dieser Wert sollte gültiges JSON sein.", "This collection should contain only unique elements.": "<PERSON>se Sammlung darf keine doppelten Elemente enthalten.", "This value should be positive.": "<PERSON><PERSON> sollte positiv sein.", "This value should be either positive or zero.": "Diese Zahl sollte entweder positiv oder 0 sein.", "This value should be negative.": "<PERSON><PERSON> sollte negativ sein.", "This value should be either negative or zero.": "Diese Zahl sollte entweder negativ oder 0 sein.", "This value is not a valid timezone.": "Dieser Wert ist keine gültige Zeitzone.", "This password has been leaked in a data breach, it must not be used. Please use another password.": "Dieses Passwort ist Teil eines Datenlecks, es darf nicht verwendet werden.", "This value should be between {{ min }} and {{ max }}.": "Dieser Wert sollte zwischen {{ min }} und {{ max }} sein.", "This value is not a valid hostname.": "Dieser Wert ist kein gültiger Hostname.", "The number of elements in this collection should be a multiple of {{ compared_value }}.": "Die Anzahl an Elementen in dieser Sammlung sollte ein Vielfaches von {{ compared_value }} sein.", "This value should satisfy at least one of the following constraints:": "Dieser Wert sollte eine der folgenden Bedingungen erfüllen:", "Each element of this collection should satisfy its own set of constraints.": "<PERSON><PERSON> dieser Sammlung sollte seine eigene Menge an Bedingungen erfüllen.", "This value is not a valid International Securities Identification Number (ISIN).": "Dieser Wert ist keine gültige Internationale Wertpapierkennnummer (ISIN).", "This value should be a valid expression.": "Dieser Wert sollte eine gültige Expression sein.", "This value is not a valid CSS color.": "Dieser Wert ist keine gültige CSS-Farbe.", "This value is not a valid CIDR notation.": "Dieser Wert entspricht nicht der CIDR-Notation.", "The value of the netmask should be between {{ min }} and {{ max }}.": "Der Wert der Subnetzmaske sollte zwischen {{ min }} und {{ max }} liegen.", "The filename is too long. It should have {{ filename_max_length }} character or less.|The filename is too long. It should have {{ filename_max_length }} characters or less.": "Der Dateiname ist zu lang. Er sollte nicht länger als {{ filename_max_length }} Zeichen sein.|Der Dateiname ist zu lang. Er sollte nicht länger als {{ filename_max_length }} Zeichen sein.", "The password strength is too low. Please use a stronger password.": "Das Passwort ist zu schwach.", "This value contains characters that are not allowed by the current restriction-level.": "Der Wert enthält Zeichen, die auf der aktuellen Einschränkungsstufe nicht erlaubt sind.", "Using invisible characters is not allowed.": "Unsichtbare Zeichen sind nicht erlaubt.", "Mixing numbers from different scripts is not allowed.": "Das Mischen von Zahlen aus verschiedenen Skripten ist nicht erlaubt.", "Using hidden overlay characters is not allowed.": "Verstecke Overlay-Zeichen sind nicht erlaubt.", "The extension of the file is invalid ({{ extension }}). Allowed extensions are {{ extensions }}.": "Die Dateiendung ist ungültig ({{ extension }}). Gültige Dateiendungen sind {{ extensions }}.", "The detected character encoding is invalid ({{ detected }}). Allowed encodings are {{ encodings }}.": "Der erkannte Zeichensatz ist nicht gültig ({{ detected }}). Gültige Zeichensätze sind {{ encodings }}.", "This value is not a valid MAC address.": "Dieser Wert ist keine gültige MAC-Adresse.", "This URL is missing a top-level domain.": "Dieser URL fehlt eine Top-Level-Domain.", "This value is too short. It should contain at least one word.|This value is too short. It should contain at least {{ min }} words.": "Dieser Wert ist zu kurz. Er muss aus mindestens einem Wort bestehen.|Dieser Wert ist zu kurz. Er muss mindestens {{ min }} Wörter enthalten.", "This value is too long. It should contain one word.|This value is too long. It should contain {{ max }} words or less.": "Dieser Wert ist zu lang. Er darf maximal aus einem Wort bestehen.|Dieser Wert ist zu lang. Er darf maximal {{ max }} Wörter enthalten.", "This value does not represent a valid week in the ISO 8601 format.": "Dieser Wert ist keine Wochenangabe im ISO 8601-Format.", "This value is not a valid week.": "Dieser Wert ist keine gültige Woche.", "This value should not be before week \"{{ min }}\".": "<PERSON>ser Wert darf nicht vor der Woche \"{{ min }}\" sein.", "This value should not be after week \"{{ max }}\".": "Dieser Wert darf nicht nach der Woche \"{{ max }}\" sein.", "This value is not a valid Twig template.": "Dieser Wert ist kein valides Twig-Template.", "This form should not contain extra fields.": "Dieses Formular sollte keine zusätzlichen Felder enthalten.", "The uploaded file was too large. Please try to upload a smaller file.": "Die hochgeladene Datei ist zu groß. Versuchen Si<PERSON> bitte eine kleinere Datei hochzuladen.", "The CSRF token is invalid. Please try to resubmit the form.": "Der CSRF-Token ist ungültig. Versuchen Sie bitte, das Formular erneut zu senden.", "This value is not a valid HTML5 color.": "Dieser Wert ist keine gültige HTML5 Farbe.", "Please enter a valid birthdate.": "Bitte geben Si<PERSON> ein gültiges Geburtsdatum ein.", "The selected choice is invalid.": "Die Auswahl ist ungültig.", "The collection is invalid.": "Diese Gruppe von <PERSON>n ist ungültig.", "Please select a valid color.": "<PERSON>te geben Si<PERSON> eine gültige Farbe ein.", "Please select a valid country.": "Bitte wählen Sie ein gültiges Land aus.", "Please select a valid currency.": "Bitte wählen Sie eine gültige Währung aus.", "Please choose a valid date interval.": "Bitte wählen Sie ein gültiges Datumsintervall.", "Please enter a valid date and time.": "Bitte geben Si<PERSON> ein gültiges Datum samt Uhrzeit ein.", "Please enter a valid date.": "<PERSON>te geben Si<PERSON> ein gültiges Datum ein.", "Please select a valid file.": "Bitte wählen Si<PERSON> eine gültige Datei.", "The hidden field is invalid.": "Das versteckte Feld ist ungültig.", "Please enter an integer.": "<PERSON>te geben <PERSON> eine ganze <PERSON> ein.", "Please select a valid language.": "Bitte wählen Sie eine gültige Sprache.", "Please select a valid locale.": "Bitte wählen Sie eine gültige Locale-Einstellung aus.", "Please enter a valid money amount.": "Bitte geben Si<PERSON> einen gültigen Geldbetrag ein.", "Please enter a number.": "<PERSON>te geben Si<PERSON> eine gültige Zahl ein.", "The password is invalid.": "Das Kennwort ist ungültig.", "Please enter a percentage value.": "Bitte geben Si<PERSON> einen gültigen Prozentwert ein.", "The values do not match.": "Die Werte stimmen nicht überein.", "Please enter a valid time.": "Bitte geben Si<PERSON> eine gültige Uhrzeit ein.", "Please select a valid timezone.": "Bitte wählen Sie eine gültige Zeitzone.", "Please enter a valid URL.": "Bitte geben Si<PERSON> eine gültige URL ein.", "Please enter a valid search term.": "Bitte geben Si<PERSON> einen gültigen Suchbegriff ein.", "Please provide a valid phone number.": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein.", "The checkbox has an invalid value.": "Das Kontrollkästchen hat einen ungültigen Wert.", "Please enter a valid email address.": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein.", "Please select a valid option.": "Bitte wählen Sie eine gültige Option.", "Please select a valid range.": "Bitte wählen Sie einen gültigen Bereich.", "Please enter a valid week.": "<PERSON>te geben Si<PERSON> eine gültige W<PERSON>e ein.", "unit_hierarchy.structure.remove_unit_exception": "Die Einheit „%unitRefId%“ kann nicht entfernt werden, da ihr am Datum %futureChangeDate% untergeordnete Elemente zugewiesen sind.", "The contract expiry date must be on or after the contract date.": "The contract expiry date must be on or after the contract date.", "The maturity date must be on or after the transaction date.": "The maturity date must be on or after the transaction date.", "This value should be between 0% and 100%": "This value should be between 0% and 100%", "u2.alpha": "Dieser Wert sollte nur Buchstaben enthalten.", "u2.authorisation.right_not_on_authorization_item": "Das Recht „%right_name%“ ist für die folgende Berechtigung nicht verfügbar „%item_name%“", "u2.base_amount.not_in_range": "Dieser Betrag sollte zwischen {{ min }} und {{ max }} liegen.", "u2.base_to_group_exchange_rate.not_in_range": "Dieser Umrechnungskurs in Gruppenwährung sollte zwischen {{ min }} und {{ max }} liegen.", "u2.destination_status.origin_and_destination_statuses_should_not_be_the_same": "Quell- und Zielstatus dürfen nicht identisch sein.", "u2.email": "Dieser Wert ist keine gültige E-Mail-Adresse.", "u2.exactLength": "Dieser Wert sollte genau %limit% Zeichen lang sein.", "u2.exchange_rate.value_has_to_be_one_if_output_and_input_currency_are_equal": "Der Umrechnungskurs muss 1 sein, wenn die „von Währung“ und „nach Währung“ identisch sind.", "u2.exchange_rate_type.given_exchange_rate_type_not_supported": "Die Umrechnungsart „%exchange_rate_type%“ wird nicht unterstützt. Mögliche Werte sind : %exchange_rate_types%", "u2.field.invalid_name": "Der Name darf nur alphanumerische Zeichen, Bindestriche und Unterstriche enthalten.", "u2.field_configuration_statuses.field_configuration.invalid": "Diese Feldkonfiguration wurde diesem Workflow bereits hinzugefügt.", "u2.field_configuration_statuses.status_in_use": "Die folgenden Zustände wurden bereits anderen Konfigurationen zugewiesen: \"%already_assigned_statuses%\"", "u2.field_state.field.invalid": "Das ausgewählte Feld ist ungültig.", "u2.german_date_format": "Das Datum sollte das Format dd.mm.yyyy haben", "u2.group_amount.not_in_range": "Dieser Betrag in Kreiswährung sollte zwischen {{ min }} und {{ max }} liegen.", "u2.group_permissions.no_group_selected": "Eine Gruppe muss ausgewählt sein.", "u2.datasheets.item.formula_is_required": "Eine Formel muss angegeben werden.", "u2.datasheets.item.this_type_cannot_be_edited": "Items des Typs „%item_type%“ dürfen nicht bearbeitet werden.", "u2.level_of_first_section_must_be_one": "Die Ebene des ersten Abschnittes muss 1 sein.", "u2.local_amount.not_in_range": "Dieser Betrag in Hauswährung sollte zwischen {{ min }} und {{ max }} liegen.", "u2.local_to_group_exchange_rate.not_in_range": "Dieser Umrechnungskurs von der Lokal- in die Gruppenwährung sollte zwischen {{ min }} und {{ max }} liegen.", "u2.maxLength": "Diese Zeichenkette ist zu lang. Sie sollte höchstens %limit% Zeichen haben.", "u2.minLength": "Diese Zeichenkette ist zu kurz. Sie sollte mindestens  %limit% Zeichen haben.", "u2.no_file_selected": "<PERSON><PERSON> ausgewählt.", "u2.numeric": "Dieser Wert sollte nur Zahlen enthalten.", "u2.period.period_cannot_be_its_previous_period": "Eine Periode kann sich nicht als vorherige Periode verwenden", "u2.rate.not_in_range": "Dieser Betrag sollte zwischen {{ min }} und {{ max }} liegen.", "u2.required": "<PERSON>rf<PERSON>erlich.", "u2.section_level_must_be_1_higher_or_less_than_previous_section": "Die Ebene des Abschnitts „%section_name%“ muss gleich der Ebene des vorhergehenden Abschnitts sein oder sich maximal um 1 unterscheiden.", "u2.structured_document.reference_section_must_not_be_in_move_sections": "Referenz des Abschnittes muss nicht in der Liste der zu verschiebenden Abschnitte enthalten.", "u2.structured_section.section_does_not_belong_to_the_same_document": "Die Referenzen dieses Abschnittes gehört nicht zum gleichen Dokument.", "u2.task.review.user_has_already_reviewed": "Sie haben diese Aufgabe bereits überprüft", "u2.task.review.user_has_not_reviewed": "Sie haben diese Aufgabe noch nicht überprüft", "u2.task_checklist.inactive_check_update_error": "Inaktive Checklistenelement können nicht bearbeitet werden", "u2.tax_number.unit_owns_tax_number_for_given_country": "Diese Unit besitzt bereits eine Steuernummer für das ausgewählte Land.", "u2.template_name_cannot_be_blank": "Der Name der Vorlage darf nicht leer sein.", "u2.template_with_given_name_and_type_already_exists": "Eine Vorlage existiert bereits mit dem angegebenen Namen und Typ.", "u2.the_user_does_not_exist": "Der Benutzer existiert nicht.", "u2.this_value_must_not_be_null": "<PERSON>ser Wert darf nicht leer sein.", "u2.total_revenue_value_sum_incorrect": "„Wert der Gesamtumsätze“ muss aus der Summe von „Gesamtumsätze mit unverbundenen Unternehmen“ und „Gesamtumsätze mit verbundenen Unternehmen“ sich ergeben.", "u2.user_permissions.no_user_selected": "Ein Benutzer muss ausgewählt sein.", "u2.value_is_not_currently_in_its_valid_date_range": "Dieser Wert ist aktuell nicht in seinem Gültigkeitszeitraum.", "u2.value_is_not_valid_for_the_end_date_of_the_selected_period": "Dieser Wert ist für den Endzeitpunkt der ausgewählten Periode ungültig (%date%).", "u2.workflow.status_not_available_for_entity_possible_values": "Der Status „%status_name%“ ist für %entity_name% nicht verfügbar. Gültige Werte sind: %possible_values%", "u2.xml_cannot_be_loaded": "XML-Datei kann nicht zur Validierung hochgeladen werden.", "u2.xml_schema_validation_failed_with_message": "Die XML-Validierung ist mit der folgenden Meldung fehlgeschlagen: %message%", "u2_contractmanagement.expiry_date.must_be_on_or_after_the_date": "Das Vertragsenddatum muss größer oder gleich dem Vertragsdatum sein", "u2_contractmanagement.for_not_third_party.partner_unit_must_be_set": "Wählen Sie eine Partner-Unit aus, wenn der Vertrag nicht gegen „Dritte“ ausgestellt ist.", "u2_contractmanagement.for_not_third_party.third_party_country_cannot_be_set": "Es darf kein Land eingegeben werden, wenn der Partner keine „Dritter“ ist", "u2_contractmanagement.for_not_third_party.third_party_name_cannot_be_set": "<PERSON>s darf kein Name eingegeben werden, wenn der Partner keine „Dritter“ ist", "u2_contractmanagement.for_third_party.partner_unit_cannot_be_selected": "Es darf keine Partner-Unit selektiert werden, wenn der Partner ein Dritter ist", "u2_contractmanagement.for_third_party.third_party_country_must_be_set": "Bitte wählen Sie ein Land für den Partner „Dritte“ aus.", "u2_contractmanagement.for_third_party.third_party_name_must_be_set": "<PERSON>n der Partner ein „Dritter“ ist, muss ein Name eingegeben werden", "u2_contractmanagement.reminder_date.must_be_on_or_after_the_date": "Das Erinnerungsdatum muss größer oder gleich dem Vertragsenddatum sein", "u2_contractmanagement.reminder_date.must_be_on_or_after_the_expiry_date": "Das Erinnerungsdatum muss vor dem Vertragsenddatum liegen", "u2_core.assign_to_user.user_has_insufficient_permissions_to_view_record": "Der User hat unzureichende Berechtigungen um diesen Datensatz anzuzeigen.", "u2_core.authorisation.invalid_authorization_item": "Ungültige Berechtigung", "u2_core.authorizations.min_message": "Sie müssen mindestens eine Berechtigung spezifizieren.", "u2_core.bulk_transition_mapping.invalid_transition_selected": "Ausgewählte „Transition“ ist ungültig.", "u2_core.configuration_key_does_not_exist": "Der Konfigurationsschlüssel „%configuration_key%“ ist nicht gültig", "u2_core.date_not_last_day_of_month_with_given_date": "Datum muss jeweils der letzte Tag eines Monats sein, z.B.: %last_day_of_month%", "u2_core.entities_cannot_be_created_in_closed_period": "Periode „%period_name%“ ist geschlossen. In einer geschlossenen Periode können keine neuen Datensätze angelegt werden.", "u2_core.entities_cannot_be_updated_in_closed_period": "Periode ist geschlossen. Datensätze innerhalb einer geschlossenen Periode können nicht aktualisiert werden.", "u2_core.group_permissions.duplicate_entry_for_group_permissions": "Doppelter Eintrag für Gruppenberechtigung. Es darf nur eine Zugriffsebene zu jeder Gruppe zugeordnet werden.", "u2_core.not_enabled": "Dieser Wert ist nicht freigegeben", "u2.frequency.invalid_cron_expression": "%cron_expression% ist kein gültiger CRON-Ausdruck", "u2_core.number_of_attachments.greater_than_zero_message": "<PERSON>e können den Status ändern, wenn der Eintrag einen oder mehrere Anhänge hat.", "u2_core.number_of_reviews.greater_than_zero_message": "<PERSON>e können den Status ändern, wenn der Eintrag einen oder mehrere Reviews hat.", "u2.password_is_not_correct": "Das Passwort ist inkorrekt", "u2.password_was_already_in_use_recently": "Das Passwort wurde bereits verwendet.", "u2_core.period.name_not_unique": "Der Name der Periode ist nicht eindeutig", "u2_core.ref_id_already_in_use": "Die Ref.ID {{ value }} wird bereits verwendet", "u2_core.start_date_needs_to_be_before_end_date": "Startdatum muss vor dem Ende-Datum liegen.", "u2_core.status_transition.invalid_entity_status": "Konnte nicht von „%transition_origin_status%“ auf „%transition_destination_status%“ wechseln, da der Status bereits auf „%workflowable_entity_status%“ geändert wurde.", "u2_core.the_period_does_not_exist": "Die Periode existiert nicht.", "u2_core.the_unit_does_not_exist": "Die Unit existiert nicht.", "u2_core.user_permissions.duplicate_entry_for_user_permissions": "Doppelter Eintrag für User-Berechtigung. Es darf nur eine Zugriffsebene zu jedem User zugeordnet werden.", "u2_core.user_permissions.one_user_should_have_permission_to_manage": "Mindestens ein Benutzer sollte die Berechtigung zum Verwalten haben", "u2_core.valid_to_date_must_be_after_valid_from_date": "Das „gültig bis“ Datum muss nach dem „gültig ab“ Datum liegen", "u2.datasheets.item_formula.circular_reference": "Zirkelbezug erkannt. Item „%item_ref_id%“ scheint sich selbst als umgekehrte Abhängigkeit zu haben.", "u2.datasheets.item_formula.item_without_formula": "Item „%item_ref_id%“ hat keine Formel.", "u2.datasheets.datasheet.field": "Das Feld existsiert bereits.", "u2_financial.base_currency.not_blank": "Es muss eine Währung angebgen werden.", "u2_financial.base_local_group_money.property_has_to_be_the_same_on_each": "%property_readable% muss für jeden der Werte gleich sein.", "u2_financial.base_to_group_exchange_rate.not_blank": "Es konnte kein durchschnittlicher Umrechnungskurs für die Gruppenwährung der gewählten Periode ermittelt werden.", "u2_financial.base_value.not_blank": "Der Betrag muss angegeben werden.", "u2_financial.could_not_find_base_to_group_exchange_rate_for_period": "Umrechnungskurs für die Gruppenwährung von %source_currency% zu %destination_currency% (%exchange_rate_type%) für Period '%period%' existiert nicht.", "u2_financial.could_not_find_local_to_group_exchange_rate_for_period": "Umrechnungskurs von der Lokal- in die Gruppenwährung von %source_currency% zu %destination_currency% (%exchange_rate_type%) für Period '%period%' existiert nicht.", "u2_financial.currency_conversion.invalid": "Die Währungsumrechnung ist ungültig.", "u2_financial.group_value.not_blank": "Der Betrag in Kreiswährung muss angegeben werden.", "u2_financial.local_currency.not_blank": "Die lokale Währung muss angegeben werden.", "u2_financial.local_to_group_exchange_rate.not_blank": "Für die gewählte Periode konnte kein durschnittlicher Umrechnungskurs von der Lokal- in die Gruppenwährung ermittelt werden.", "u2_financial.local_value.not_blank": "Der lokale Wert muss angegeben werden.", "u2_financial.partner.partner_unit_is_required": "Die Partner Unit muss eingegeben werden.", "u2_financial.partner.unit_and_partner_unit_cannot_be_the_same": "Gesellschaft und Partner Unit dürfen nicht identisch sein.", "u2_financial.type.invalid_transaction_type": "Transaktionstyp ist ungültig.", "u2_structureddocument.create_initial_section.document_already_has_sections": "Ein Initialabschnitt kann nicht erstellt werden, wenn das Dokument bereits Abschnitte beeinhaltet.", "u2_structureddocument.document_template.required_sections_can_not_be_excluded": "Erforderliche Abschnitte können nicht ausgeschlossen werden.", "u2_tam.planning_period.max_message": "Das Planungsjahr muss nicht mehr als 10 Jahre nach Jahresbeginn der aktuellen Periode.", "u2_tam.planning_period.planning_year_after_start_year": "Das Planungsjahr muss nach Jahresanfang der aktuellen Periode beginnen", "u2_tpm.financial_data.base_local_group_money.currency_not_blank": "Die Währung muss angegeben werden.", "u2_tpm.financial_data.unit.currency_not_blank": "Dieser Unit ist keine Währung zugewiesen.", "u2_tpm.given_units_country_do_not_match_document_country": "Sie können die folgenden Einheiten nicht hinzufügen, da ihr Land nicht mit dem in diesem Dokument zugewiesenen Land übereinstimmt (%document_country%): %units%", "u2.role.role_not_found": "Die Rolle \"%role%\" existiert nicht", "The transaction date must be on or after the contract date.": "The transaction date must be on or after the contract date.", "The validity period expiry date must be after the validity period start date.": "The validity period expiry date must be after the validity period start date.", "u2.password.requires_at_least_one_uppercase_letter": "Es ist mindestens ein Großbuchstabe erforderlich", "u2.password.requires_at_least_one_lowercase_letter": "Es ist mindestens ein Kleinbuchstabe erforderlich", "u2.password.requires_at_least_one_number": "<PERSON>s ist mindestens eine Zahl erforderlich", "u2.password.requires_at_least_one_non_alphanumeric_character": "Es ist mindestens ein Sonderzeichen erforderlich", "u2.password.too_short": "Das Passwort ist zu kurz. Es sind mindestens „%minPasswordLength%“ Zeichen erforderlich", "u2.array.maxLength": "Diese Kollektion beinhaltet zu viele Elemente. Sie sollte höchstens %limit% Elemente haben.", "u2.array.minLength": "Diese Kollektion beinhaltet zu wenig Elemente. Sie sollte mindestens %limit% Elemente haben.", "u2.import.configuration_key_slug_not_found": "Konfigurationsschlüssel-Slug \"%configurationKeySlug%\" nicht gefunden", "u2.import.data_row_empty": "Die Zeile darf nicht leer sein.", "u2.import.data_row_field_name_not_found": "Das Kopfzeile „%fieldName%“ wird nicht unterstützt", "u2.choice_field_does_not_meet_rule": "Die Regeln für diese Auswahl sind nicht erfüllt", "u2_core.import.no_data_found": "Es konnten keine Einträge in der Datei gefunden werden.", "u2.upload_file_too_big": "Die ausgewählte Datei \"%nam%\" ist zu groß (%fileSize%).", "u2.invalid_bop_account_number": "Ein BOP-Accountnummer muss 10 <PERSON><PERSON><PERSON> lang sein, e.g. **********.", "u2.invalid_bzst_number": "Ein BZSt-Nummer muss mit 2 Großbuchstaben beginnen, gef<PERSON><PERSON> von 9 Ziffern. Z.B. BZ123456789", "u2.invalid_field_name": "Der Feldname darf nur Buchstaben, <PERSON><PERSON><PERSON>, Unterstriche (_) und Bindestriche (-) enthalten. <PERSON>te stellen <PERSON> sicher, dass es keine Leer- oder Sonderzeichen enthält.", "u2.password.do_not_match": "Die Passwörter stimmen nicht überein.", "u2.workflow.binding.destination_status_not_in_target_workflow": "Der Zielstatus \"%destinationStatus%\" ist nicht im Workflow \"%targetWorkflow%\" enthalten.", "u2.workflow.binding.missing_mapping": "Es fehlt eine Zuordnung für den Status „%status%“."}, "messages": {"APM Transaction": "APM Transaction", "u2.reference": "<PERSON><PERSON><PERSON><PERSON>", "u2.max_system_upload_size_exceeded": "Die definierte „Maximale Upload-Größe“ für die Anwendung überschreitet die vom System definierte maximale Upload-Größe (%systemMaxUploadSize%B).", "u2.datasheets.field_count": "Diesem Datasheet ist %count% Feld zugewiesen.|Diesem Datasheet sind %count% Felder zugewiesen.", "u2.datasheets.no_fields": "Diesem Datasheet sind keine Felder zugewiesen.", "Aggregate Excess of Loss": "Aggregate Excess of Loss", "u2.not_public": "<PERSON><PERSON>", "u2.regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Assumption of Liabilities": "Assumption of Liabilities", "u2.expires": "Ungültig ab", "u2.last_used_at": "Zuletzt verwendet am", "Bond": "Bond", "Bond - Collateralized": "Bond - Collateralized", "Bond - Uncollateralized": "Bond - Uncollateralized", "Borrower": "<PERSON><PERSON><PERSON>", "Contingent Liabilities": "Contingent Liabilities", "Creditor": "Creditor", "Derivatives - Forwards": "Derivatives - Forwards", "Derivatives - Futures": "Derivatives - Futures", "Derivatives - Options": "Derivatives - Options", "Derivatives - Others": "Derivatives - Others", "Efficient Portfolio Management": "Efficient Portfolio Management", "Equity Type - Dividends": "Equity Type - Dividends", "Equity Type - Others": "Equity Type - Others", "Equity Type - Shares/Participation": "Equity Type - Shares/Participation", "Excess of Loss (per Back-Up)": "Excess of Loss (per Back-Up)", "Excess of Loss (per Event and per Risk)": "Excess of Loss (per Event and per Risk)", "Excess of Loss (per Event)": "Excess of Loss (per Event)", "Excess of Loss (per Risk)": "Excess of Loss (per Risk)", "Excess of Loss with Basis Risk": "Excess of Loss with Basis Risk", "FX": "FX", "Facultative Non-Proportional": "Facultative Non-Proportional", "Facultative Proportional": "Facultative Proportional", "Financial Reinsurance": "Financial Reinsurance", "Fixed": "Fixed", "Guarantees - Credit Protection": "Guarantees - Credit Protection", "Guarantees - Others": "Guarantees - Others", "Interest": "Interest", "Loan": "Loan", "Loan - Collateralized": "Loan - Collateralized", "Loan - Uncollateralized": "Loan - Uncollateralized", "Macro Hedge": "<PERSON><PERSON>", "Matching Assets and Liabilities Cash Flows": "Matching Assets and Liabilities Cash Flows", "Micro Hedge": "Micro Hedge", "Mixed": "Mixed", "Other Asset Transfer - Others": "Other Asset Transfer - Others", "Other Asset Transfer - Properties": "Other Asset Transfer - Properties", "Other Non-Proportional Treaties": "Other Non-Proportional Treaties", "Other Proportional Treaties": "Other Proportional Treaties", "Others": "Others", "Promissory Note": "Promissory Note", "Promissory Note - Collateralized": "Promissory Note - Collateralized", "Promissory Note - Uncollateralized": "Promissory Note - Uncollateralized", "Quota Share": "Quota Share", "Reinstatement Cover": "Reinstatement Cover", "Senior": "Senior", "Stop Loss": "Stop Loss", "Subordinate": "Subordinate", "Surplus": "Surplus", "Swaps - Credit Default": "Swaps - Credit Default", "Swaps - Currency": "Swaps - <PERSON><PERSON><PERSON>cy", "Swaps - Interest Rate": "Swaps - Interest Rate", "Swaps - Others": "Swaps - Others", "Unlimited Excess of Loss": "Unlimited Excess of Loss", "Variable": "Variable", "Variable Quota Share": "Variable Quota Share", "u2.access_denied": "<PERSON><PERSON><PERSON> verweigert", "u2.access_type.public": "<PERSON><PERSON><PERSON><PERSON>", "u2.access_type.public.help": "Alle Benutzer können diese Datei sehen", "u2.add": "Hinzufügen", "u2.add_comment_successful": "<PERSON>mme<PERSON><PERSON> hinzugefügt.", "u2.address.city": "Stadt", "u2.address.line_1": "Zeile 1", "u2.address.line_2": "Zeile 2", "u2.address.line_3": "Zeile 3", "u2.address.post_code": "<PERSON><PERSON><PERSON><PERSON>", "u2.address.state": "Bundesland", "u2.all": "Alle", "u2.application_currency": "Applikationswährung", "u2.application_currency.warning": "Änderungen dieser Werte werden erhebliche Funktionsänderungen der Applikation verursachen. Bitte nicht ändern außer wenn Sie sich sicher sind über die Konsequenzen.", "u2.are_you_sure": "Sind Sie sicher?", "u2.assign": "<PERSON><PERSON><PERSON>", "u2.authentication.error.account_expired": "Account ist abgelaufen.", "u2.authentication.error.account_locked": "<PERSON><PERSON> ist gesperrt. Bitte setze dein Passwort zurück.", "u2.authentication.error.bad_credentials": "Ungültige Zugangsdaten.", "u2.authentication.error.password_expired": "Ihr Passwort ist abgelaufen. Klicken Sie bitte auf den Link „Passwort vergessen“ (unter), um ein Passwort anzufordern.", "u2.authentication.password_reset.enter_username_or_email": "Bitte geben Sie Ihren Benutzernamen oder Ihre E-Mail-Adresse ein:", "u2.authentication.password_reset.error.email_sending_failed": "Ein Fehler ist beim Versand der E-Mail aufgetreten.", "u2.authentication.password_reset.error.password_reset_link_is_no_longer_valid": "Der von Ihnen angeklickten Link zur Passwort-Zurücksetzung ist nicht mehr gültig. Bitte versuchen Sie es erneut.", "u2.authentication.password_reset.error.user_could_not_be_found": "Benutzer konnte nicht gefunden werden.", "u2.authentication.password_reset.notice.click_on_reset_password_to_send_an_email": "Nach dem Klick auf „Passwort zurücksetzen“ bekommt der Benutzer eine E-Mail, um sein Passwort zurückzusetzen.", "u2.authentication.password_reset.reset_password_for_given_username": "Passwort: %username% zurücksetzen", "u2.authentication.password_reset.success.email_has_been_sent": "Die Anfrage zum zurücksetzen des Passworts war erfolgreich. Eine E-Mail wird an das Benutzerkonto gesendet, falls dieses vorhanden ist.", "u2.authentication.password_reset.success.reset_password_email_sent": "Eine E-Mail mit dem zurückgesetzten Passwort wurde an %email_address% geschickt.", "u2.authentication.two_factor.invalid_code": "Ungültiger Code", "u2.authorization.rights.select_an_item_first": "Wählen Sie ein Berechtigungselement aus, um die zugehörigen Rechte anzuzeigen.", "u2.authorization.select_authorizations": "Berechtigungsprofile auswählen", "u2.authorization.select_rights": "Rechte auswählen", "u2.bulk_transition": "Bulk Transition", "u2.calendar.empty": "<PERSON><PERSON> ist leer für die nächsten 4 Wochen.", "u2.cancel": "Abbrechen", "u2.change_parameters": "Parameter ändern", "u2.checked": "Überprüft", "u2.clear": "Löschen", "u2.clear_all": "Alle löschen", "u2.close": "Schließen", "u2.closed": "Abgeschlossen", "u2.comment.add_comment_field_placeholder": "Kommentar einfügen...", "u2.comment.add_quote": "<PERSON>sen Text als Zitat in einen neuen Kommentar einfügen.", "u2.configuration": "Konfiguration", "u2.confirm": "Bestätigen", "u2.confirm_deletion": "Löschen bestätigen", "u2.confirm_reset": "Zurücksetzen bestätigen", "u2.confirm_user_unlock": "Benutzer entsperren bestätigen", "u2.confirmation": "Bestätigung", "u2.content": "Inhalt", "u2.contract_date": "Contract Date", "u2.contract_details": "Vertragsdetails", "u2.contract_expiry_date": "Contract Expiry Date", "u2.country": "Land", "u2.country.plural": "<PERSON><PERSON><PERSON>", "u2.country_by_country_report.reporting_role": "Reporting Rolle", "u2.country_founded": "Gründungsland", "u2.create_new_record": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> einen neuen Datensatz, um loszulegen", "u2.data_transfer.status_types.completed": "Abgeschlossen", "u2.data_transfer.status_types.in_progress": "In Bearbeitung", "u2.data_transfer.status_types.queued": "In Warteschlange", "u2.date.last_day": "Letzten %day%", "u2.date.today": "<PERSON><PERSON>", "u2.date.tomorrow": "<PERSON><PERSON>", "u2.date.yesterday": "Gestern", "u2.datetime.last_day": "Letzten %day% um %time%", "u2.datetime.next_week": "%day% um %time%", "u2.datetime.now": "Heute um %time%", "u2.datetime.tomorrow": "<PERSON>rgen um %time%", "u2.datetime.yesterday": "Gestern um %time%", "u2.deactivate": "Deaktivieren", "u2.default_group_permissions": "Standard-Gruppenberechtigungen", "u2.default_user_permissions": "Standard-Benutzerberechtigungen", "u2.delete": "Löschen", "u2.delete_currency_with_given_name": "Währung (%currency_name%) löschen", "u2.delete_currency_with_given_name.confirmation": "<PERSON>d <PERSON> sic<PERSON>, dass die Währung (%currency_name%) gelö<PERSON>t werden soll?", "u2.delete_given_selected_records.confirmation": "Sind <PERSON> sicher, dass die selektierten %entity_type% (%list%) gelöscht werden sollen?", "u2.delete_section_from_document.confirm": "Sind <PERSON> sicher, dass der Abschnitt „%section_name%“ gelöscht werden soll?", "u2.delete_selected_records": "Ausgewählte Datensätze gelöscht.", "u2.delete_successful": "Erfolg<PERSON><PERSON>", "u2.delete_unsuccessful": "Die Löschung ist fehlgeschlagen", "u2.description": "Beschreibung", "u2.deselect_all": "Alle abwählen", "u2.deselect_filtered": "Gefilterten abwählen", "u2.disabled": "Deaktiviert", "u2.document.widget.transaction_table.help": "Beim Gruppieren der Ergebnisse werden die ausgewählten Spalten ignoriert.", "u2.document_widget.transaction_table.group_results": "Ergebnisse gruppieren", "u2.document_widget.transaction_table.group_results.help": "Gruppiert die Ergebnisse nach Unit, Partner Unit und Typ.", "u2.duplicate": "Duplizieren", "u2.duplicate.dialog.text": "Sind <PERSON> sicher, dass Sie \"%document_name%\" duplizieren möchten?", "u2.duplicate.dialog.title": "Duplizierung bestätigen", "u2.edit": "Editieren", "u2.edit_comment_successful": "Kommentaränderungen gespeichert.", "u2.edit_configuration": "Konfiguration bearbeiten", "u2.edit_content": "Inhalt bearbeiten", "u2.edit_currency_with_given_name": "Währung (%currency_name%) bearbeiten", "u2.edit_document.warning_unit_hierarchy_changed": "Die in dieser Dokumentation verwendete Unit-Hierarchie wurde geändert. Um sie zu aktualisieren öffnen Sie bitte die Konfigurationsseite.", "u2.edit_entity_type_name": "%entity_type_name% bearbeiten", "u2.edit_group_permissions": "Gruppenberechtigung bearbeiten", "u2.edit_selected_records": "Ausgewählte Datensätze bearbeiten", "u2.edit_user_permissions": "Benutzerberechtigung bearbeiten", "u2.edited": "In Bearbeitung", "u2.email.password_reset": "Passwort zurücksetzen", "u2.email.password_reset.choose_account": "<PERSON>s gibt mehrere Konten, die mit Ihrer E-Mail verknüpft sind. Wählen Si<PERSON> unten das Konto aus, welches Sie zurücksetzen möchten.", "u2.email.greeting": "Hi <strong>%username_or_email%</strong>,", "u2.email.password_reset.help": "<PERSON><PERSON> <PERSON> dies nicht ange<PERSON>ert haben, dann können Sie diese E-Mail ignorieren. Ihr Passwort wird erst geändert, wenn Sie ein neues erstellen.", "u2.email.password_reset.request_received": "Wir haben eine Anfrage zum ändern Ihres Passworts erhalten.", "u2.email.password_reset.username_button": "Passwort für %username% zurücksetzen", "u2.email.two_factor.finish_signin": "Wir haben festgestellt, dass Sie kürzlich versucht haben, sich von einem neuen Gerät aus bei Ihrem Universal Units-Konto anzumelden. Sie können die Anmeldung abschließen, indem Sie auf die Schaltfläche klicken:", "u2.email.two_factor.greeting": "<PERSON>o <strong>%username_or_email%</strong>,", "u2.email.two_factor.help": "<PERSON>n Sie Probleme beim <PERSON> haben, <a href=\"%contact_url%\">setzen Sie sich bitte mit uns in Verbindung</a>.", "u2.email.two_factor.redirection": "Oder kopieren Sie diesen Code und fügen Sie ihn in die Bestätigungsseite ein, auf die Si<PERSON> nach dem Anmelden weitergeleitet wurden:", "u2.email.two_factor.warning": "<PERSON> das nicht Si<PERSON> waren, empf<PERSON><PERSON> wir Ihnen, <PERSON>hr Passwort sofort zu ändern, um Ihr Konto zu sichern.", "u2.empty": "<PERSON><PERSON>", "u2.enabled": "Aktiviert", "u2.error": "<PERSON><PERSON>", "u2.error.locale_not_synced": "Ihre Spracheinstellung hat sich seit dem letzten Besuch dieses Tabs geändert. Bitte laden Sie die Seite neu.", "u2.export.item_values.csv": "<PERSON><PERSON> (CSV)", "u2.field": "<PERSON><PERSON>", "u2.field_configuration": "Field Configuration", "u2.field_configuration.delete.confirm": "Sind Sie sicher das Sie die Feldkonfiguration \"%field_configuration_collection%\" löschen möchten?", "u2.field_configuration.edit": "Feldkonfiguration bearbeiten", "u2.field_configuration.new": "Neue Feldkonfiguration anlegen", "u2.field_configuration.plural": "Feldkonfigurationen", "u2.field_configuration.tooltip.delete": "Feldkonfiguration \"%field_configuration_collection%\" löschen", "u2.field_configuration.tooltip.edit": "Feldkonfiguration \"%field_configuration_collection%\" bearbeiten", "u2.field_configuration_statuses.plural": "Statusfeldkonfigurationen", "u2.file": "<PERSON><PERSON>", "u2.file_already_linked": "Diese Datei wurde bereits verlinkt.", "u2.file_required_link": "Eine Datei muss zum Verknüpfen ausgewählt werden.", "u2.file_size": "Dateigröße", "u2.file_type": "Dateiformat", "u2.file_type.plural": "Dateiformate", "u2.file_upload_whitelist": "Datei-Upload-Whitelist", "u2.file_upload_whitelist.help": "Diese Whitelist definiert die MIME-Typen von <PERSON>, welche auf U² hochgeladen werden können. <PERSON><PERSON>, die in dieser Whitelist keinen Mime-<PERSON><PERSON> haben, werden nicht als gültig akzeptiert und können nicht hochgeladen werden.\n        ", "u2.file_upload_whitelist.warning": "Bestimmte Dateitypen können ein Sicherheitsrisiko darstellen und diese Liste sollte mit Vorsicht bearbeitet werden.", "u2.filter": "Filter", "u2.filter_the_results_to_save": "Suchergebnissen filtern", "u2.form.text_field_placeholder": "Text erfassen…", "u2.group_permissions": "Gruppenberechtigungen", "u2.headquarters": "Headquarters", "u2.hidden": "Hidden", "u2.history": "<PERSON><PERSON><PERSON><PERSON>", "u2.http_400": "Ungültige Anfrage. Die Anfrage konnte nicht verarbeitet werden.", "u2.http_401": "<PERSON>e müssen sich zu<PERSON>t anmelden, um diese Seite anzuzeigen oder diese Aktion auszuführen.", "u2.session_expired": "Sitzung abgelaufen.", "u2.http_403": "Sie haben keine Berechtigung, diese Seite anzuzeigen oder diese Aktion auszuführen. Bitte wenden Sie sich an Ihren Berechtigungsadministrator, um den Zugriff anzufordern.", "u2.http_404": "Die angeforderte Ressource kann nicht gefunden werden oder existiert nicht. Möglicherweise wurde sie entfernt, umbenannt oder ist vorübergehend nicht verfügbar.", "u2.http_405": "Die verwendete Anfragemethode wird von dieser Ressource nicht gestützt.", "u2.http_408": "Der Server hat die Wartezeit für die Anfrage überschritten. Bitte versuchen Si<PERSON> es erneut.", "u2.http_409": "Die Anfrage konnte nicht abgeschlossen werden, da der aktuelle Zustand der Resource im Konflikt mit der Anfrage steht.", "u2.http_422": "Die Ressource konnte nicht verarbeitet werden.", "u2.http_500": "Anwendungsfehler.", "u2.http_503": "Das System wird derzeit gewartet.", "u2.import.import": "Import", "u2.import.import_units": "Import Units", "u2.import.imported": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2.import.importing_given_item": "Import %item%", "u2.import.imports": "Importe", "u2.import.record_was_imported": "Dieser Eintrag wurde importiert", "u2.import.record_was_imported_from_an_external_source": "Der Eintrag wurde von einer externen Datenquelle importiert und nicht manuell erfasst.", "u2.import.result_types.fail": "Fehlgeschlagen", "u2.import.result_types.success": "Erfolgreich", "u2.import.status_types.completed": "Abgeschlossen", "u2.import.status_types.in_progress": "In Bearbeitung", "u2.import.status_types.queued": "In Warteschlange", "u2.information": "Information", "u2.insert_file_reference": "Dateireferenz einfügen", "u2.insert_image": "Bild einfügen", "u2.insert_widget": "Widget einfügen", "u2.insufficient_permissions": "Nicht ausreichende Berechtigungen", "u2.insufficient_user_permissions_to_view_entity": "Der Benutzer hat unzureichende Berechtigungen um %entity_name% zu sehen.", "u2.datasheets.item.types.checkbox": "Checkbox", "u2.datasheets.item.types.diff": "<PERSON><PERSON><PERSON><PERSON>", "u2.datasheets.item.types.money": "Wert", "u2.datasheets.item.types.percent": "Steuersatz", "u2.datasheets.item.types.text": "Text", "u2.datasheets.item_value": "Wert", "u2.datasheets.field.delete.confirm": "Sind Sie sicher das Sie das Feld \"%fieldId%\" des Datasheets \"%layoutName%\" löschen möchten?\n        ", "u2.legal_unit": "Legale Einheit", "u2.legal_unit.plural": "Legale Units", "u2.linked_entities.not_allowed": "Sie sind nicht berechtigt, diese Entitäten anzuzeigen.", "u2.list": "Übersicht", "u2.loading": "<PERSON>de…", "u2.locked": "<PERSON><PERSON><PERSON><PERSON>", "u2.login_color": "Anmeldungsfarbe", "u2.login_color.help": "Hexadezimalfarbdarstellung. <PERSON><PERSON> für die Standardfarbe. Beispiel: #d22630 für U²-Rot.", "u2.maturity_date": "Maturity Date", "u2.max_upload_size": "Maximale Upload Dateigröße in KB", "u2.money.exchange_rate.types.average": "Durchschnitt", "u2.money.exchange_rate.types.current": "Stichtag", "u2.n_a": "n/a", "u2.name": "Name", "u2.new": "<PERSON>eu", "u2.new_dashboard": "Neues Dashboard", "u2.new_entity_type_name": "Neu %entity_type_name%", "u2.new_exchange_rate": "Neuer Wechselkurs", "u2.new_file": "Neue Datei", "u2.new_given_unit_type": "Neuer %unit_type%", "u2.new_item_with_given_name": "Neu %item_name%", "u2.new_password": "Neues Passwort", "u2.new_period": "Neue Periode", "u2.new_saved_filter": "<PERSON>euer Auswertungsfilter", "u2.new_saved_filter_subscription": "Neue Subskription", "u2.new_status": "Neuer Status", "u2.new_system_message": "Neue Systemmeldung", "u2.new_unit_hierarchy": "Neue Unit Hierarchie", "u2.new_user": "<PERSON><PERSON><PERSON>", "u2.new_user_group": "Neue Benutzergruppe", "u2.no": "<PERSON><PERSON>", "u2.no_files_in_section": "Es sind keine Dateien verfügbar. Bitte fügen Sie eine Datei zu diesem Abschnitt hinzu und versuchen Si<PERSON> es erneut.", "u2.no_images_in_section": "Es sind keine Bilder verfügbar. Bitte fügen Sie ein Bild zu diesem Abschnitt hinzu und versuchen Si<PERSON> es erneut.", "u2.no_records_selected_delete": "<PERSON>s wurden keine Datensätze zum Löschen ausgewählt.", "u2.no_records_selected_edit": "<PERSON>s wurden keine Datensätze zum Bearbeiten ausgewählt.", "u2.no_records_selected_transition": "<PERSON>s wurden keine Datensätze für die Statusänderung ausgewählt.", "u2.no_results": "<PERSON><PERSON>", "u2.open": "<PERSON>en", "u2.organisational_group": "Organisatorische Einheit", "u2.organisational_group.plural": "Organisatorische Einheiten", "u2.page_break": "Seitenumbruch", "u2.page_has_unsaved_changes": "Diese Seite hat nicht gespeicherte Änderungen.", "u2.page_not_found": "Seite nicht gefunden", "u2.password_good": "Gut", "u2.password_moderate": "<PERSON><PERSON><PERSON><PERSON>", "u2.password_strong": "<PERSON>", "u2.password_very_weak": "<PERSON><PERSON> schwach", "u2.password_weak": "<PERSON><PERSON><PERSON>", "u2.periods": "<PERSON>en", "u2.permanent_establishment": "Betriebsstätte", "u2.permanent_establishment.plural": "Betriebsstätten", "u2.post_comment": "Kommentar schreiben", "u2.recalculate": "<PERSON><PERSON>", "u2.recalculate_unit_period.confirmation.text": "Alle Werte für die Unit „%unit%“ im Zeitraum „%period%“ werden neu berechnet.", "u2.recalculate_unit_period.success": "Die Werte für die Unit „%unit%“ im Zeitraum „%period%“ wurden erfolgreich neu berechnet.", "u2.record_was_transferred": "Dieser Eintrag wurde übertragen und nicht manuell angelegt.", "u2.records": "Datensät<PERSON>", "u2.remember_me": "Ang<PERSON><PERSON><PERSON> bleiben", "u2.removing_attachment_unsuccessful": "Das Entfernen des Anhangs ist fehlgeschlagen.", "u2.reporting_company": "<PERSON><PERSON><PERSON><PERSON> Unternehmen", "u2.reporting_company.country": "Obergesellschaft Land", "u2.reporting_company.name": "Obergesellschaftsname", "u2.reporting_company.ref_id": "Obergesellschaft Ref. ID", "u2.reporting_roles.localfiling": "Lokale Meldung", "u2.reporting_roles.surrogateparententity": "Beauftragt durch Konzernmutter", "u2.reporting_roles.ultimateparententity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2.request": "An<PERSON>ern", "u2.request_permissions": "Berechtigungen anfordern", "u2.reset_trusted_devices": "Vertrauenswürdige Geräte zurücksetzen", "u2.return_to_the_login_page": "Zurück zur Login-Seite", "u2.role_is_inherited": "Diese Rolle wurde von einer anderen Rolle oder von einer Gruppe übernommen.", "u2.runtime": "Laufzeit", "u2.save": "Speichern", "u2.save_as": "<PERSON><PERSON><PERSON> unter", "u2.save_the_current_filter": "Aktuellen Auswertungsfilter speichern", "u2.save_the_current_filter_as_new": "Aktuellen Auswertungsfilter als neu speichern", "u2.save_unit_hierarchy_confirmation": "<PERSON>d <PERSON>, dass Sie dieses Unit Hierarchie Konfiguration für das Datum %date% speichern?", "u2.saved_filter.visibility_user_group_permissions_warning": "Zugewiesene Gruppen haben keine Auswirkung, da \"Mit allen Benutzern teilen\" aktiviert ist", "u2.saved_filter.visibility_user_permissions_warning": "Zugewiesene Benutzer haben keine Auswirkung, da \"Mit allen Benutzern teilen\" aktiviert ist", "u2.saved_filter_subscription.why_receiving": "Sie haben diese Information erhalten, weil Sie für diesen Filter registriert sind.", "u2.saved_filter_subscriptions.confirm_send_text": "Diese Aktion führt die Subskription jetzt aus und sendet E-Mails an alle Empfänger", "u2.saved_filters": "Gespeicherte Filter", "u2.saved_filters.no_subscriptions_to_this_filter": "<PERSON><PERSON><PERSON> diesen Filter liegen keine Subskriptions", "u2.saved_filters_list": "Übersicht gespeicherte Filter", "u2.search": "<PERSON><PERSON>", "u2.search.recent_searches": "Zuletzt ausgewählt", "u2.security.confirm_login": "<PERSON><PERSON> bestätigen", "u2.security.finish_login": "Login abschließen", "u2.security.roles.admin": "Administrator", "u2_core.api_docs": "Api-Dokumentation", "u2.security.roles.api": "Api", "u2.security.roles.user": "<PERSON><PERSON><PERSON>", "u2.security.roles.user_group_admin": "Benutzergruppen-Administrator", "u2.security.two_factor": "Zwei-Faktor-Authentifizierung erzwingen", "u2.security.two_factor.code.label": "Bitte geben Sie den Authentifizierungscode ein, den Sie per E-Mail erhalten haben:", "u2.security.two_factor.code.placeholder": "Code", "u2.security.two_factor.confirm_reset_of_trusted_devices": "Möchten Si<PERSON> wirklich Ihre vertrauenswürdigen Geräte zurücksetzen?", "u2.security.two_factor.email": "Zwei-Faktor-E-Mail-Authentifizierung", "u2.security.two_factor.reset_trusted_devices.success": "Vertrauenswürdige Geräte wurden erfolgreich zurückgesetzt", "u2.security.two_factor.trust_device.label": "Dies ist ein vertrauenswürdiger Computer", "u2.security.verify_login": "Login überprüfen", "u2.select_a_task_type": "Wählen Sie einen Task Type", "u2.select_all": "Alle auswählen", "u2.select_country": "Land auswählen", "u2.select_currency": "Währung wählen", "u2.select_date": "Da<PERSON> ausw<PERSON>en", "u2.select_filtered": "Gefilterten auswählen", "u2.select_option": "Option auswählen", "u2.service_provider": "Service Provider", "u2.share": "Teilen", "u2.share_filter": "Filter teilen", "u2.share_filter.help": "<PERSON><PERSON> können diesen Link kopieren und einfügen. Der Link wird auf die aktuelle Tabellenübersicht zeigen.", "u2.share_filter_link": "Filterlink teilen", "u2.share_link_to_the_currently_filtered_table": "Link zur aktuellen Filterauswertungstabelle hinzufügen", "u2.shared_with_all_users": "<PERSON>t allen Benutzern teilen", "u2.simulated": "<PERSON><PERSON><PERSON><PERSON>", "u2.simulated.help": "Dies ist eine Simulation und es wurden keine Änderungen vorgenommen.", "u2.status.types.type_complete": "<PERSON><PERSON><PERSON><PERSON>", "u2.status.types.type_in_progress": "In Bearbeitung", "u2.status.types.type_open": "<PERSON>en", "u2.statuses_field_configuration.edit": "Statusfeldkonfiguration bearbeiten", "u2.statuses_field_configuration.new": "Neue Statusfeldkonfigurationen anlegen", "u2.success_removed": "Gelöscht.", "u2.system_message.types.type_info": "Info", "u2.system_message.types.type_warning": "<PERSON><PERSON><PERSON>", "u2.table.count_records_found": "%count% Eintrag gefunden|%count% Einträge gefunden", "u2.table.no_records": "<PERSON><PERSON>", "u2.table.selected_count": "%count% ausgewählt", "u2.task.choice_fields": "Auswahlfelder", "u2.task.duplicate.success": "Aufgabe wurde erfolgreich dupliziert.", "u2.task.task_id": "Aufgaben-ID", "u2.task_checklist.check_history_title": "Checklistenverlauf für \"%title%\"", "u2.task_checklist.hide_checks": "Checklistenelemente, die nicht dem aktuellen Workflow Status zugeordnet sind, ausblenden", "u2.task_checklist.no_checks": "Aktuell gibt es keine Checks.", "u2.task_checklist.no_checks_in_current_status": "<PERSON><PERSON> gibt keine Checks für diesen Status", "u2.task_checklist.no_history": "Dieses Checklistenelement wurde noch nicht überprüft", "u2.task_checklist.show_checks": "Checklistenelemente, die nicht dem aktuellen Workflow Status zugeordnet sind, einblenden", "u2.task_field.coupon_interest_rate_type": "Coupon/Interest Rate Typ", "u2.task_field.coupon_interest_rate_type.help": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> fix, variable, mixed", "u2.task_field.current_period_book_value": "Buchwert der aktuellen Periode", "u2.task_field.current_period_book_value.help": "Gebuchter Betrag am Ende der Periode. Bitte 0 eingeben, wenn die Transaktion in der aktuellen Periode endet.", "u2.task_field.current_period_book_value_currency": "Buchwert der aktuellen Periode (Währung)", "u2.task_field.current_period_interest_expenses": "Zinszahlung der aktuellen Periode", "u2.task_field.current_period_interest_expenses.help": "Gebuchter kumulativer Zinsertrag/-aufwand zum 01.01. des aktuellen Jahres", "u2.task_field.current_period_interest_expenses_currency": "Zinszahlung der aktuellen Periode (Währung)", "u2.task_field.id_of_asset_liability_underlying_the_derivative": "ID-Code des dem Derivat zugrunde liegenden Vermögenswerts/Verbindlichkeit", "u2.task_field.period": "Periode", "u2.task_field.previous_period_book_value": "Buchwert der Vorperiode", "u2.task_field.previous_period_book_value.help": "Gebuchter Betrag zum 31.12. der Vorjahres. Bitte 0 eingeben, wenn die Transaktion im aktuellen Jahr beginnt.", "u2.task_field.previous_period_book_value_currency": "Buchwert der Vorperiode (Währung)", "u2.task_field.source_id": "<PERSON><PERSON> Id", "u2.tax_accounting.formula_compile_error": "Es war nicht möglich die Formel \"%formula%\" oder einer Ihrer Abhängigkeiten zu berechnen.", "u2.tax_accounting.item_breakdown": "Item Breakdown:", "u2.tax_number.add": "Steuernummer hinzufügen", "u2.tax_numbers": "Steuernummern", "u2.transaction.asset_liability_id": "Asset/Liability ID", "u2.transaction.coupon_interest_rate": "Coupon/Interest Rate", "u2.transaction.forward_rate": "Forward Rate", "u2.transaction.guarantee_fee_amount": "Guarantee Fee Amount", "u2.transaction.guarantee_fee_currency": "Guarantee Fee Currency", "u2.transition": "Transition", "u2.transition_required": "Sie müssen eine Transition auswählen.", "u2.transition_selected_records": "Statuswechsel der ausgewählten Datensätze", "u2.try_later": "Bitte versuchen Sie es später erneut.", "u2.two_factor.email.subject": "Bestätigungs-Code: %authentication_code%", "u2.unassigned": "<PERSON>cht zugewiesen", "u2.unauthorized": "Nicht Autorisiert", "u2.unchecked": "Nicht überprüft", "u2.under_maintenance": "Wartungsmodus aktiv", "u2.unit": "Unit", "u2.unit.legal_name.inherit_from_name": "Name als Juristische Bezeichnung übernehmen", "u2.unit.plural": "Units", "u2.unit_edit_field_white_list": "Unit White-Liste bearbeiten", "u2.unit_hierarchies": "Unit Hierarchien", "u2.unit_hierarchy": "Unit Hierarchie", "u2.unit_hierarchy.structure.latest_previous_change": "Vorherige Änderung (%lastChangeDate%)", "u2.unit_hierarchy.structure.next_change": "Nächste Änderung (%nextChangeDate%)", "u2.unit_hierarchy_list": "Übersicht Unit Hierarchie", "u2.unit_is_inherited": "Diese Unit wurde von einer Gruppe übernommen.", "u2.unit_list": "Übersicht Unit", "u2.unit_pool": "Unit Pool", "u2.unknown": "Unbekannt", "u2.unknown_error_occurred": "Ein unbekannter Fehler ist aufgetreten. Bitte kontaktieren Sie Ihren Administrator für zusätzliche Informationen.", "u2.unlock": "Entsperren", "u2.unlock_user": "Benutzer entsperren", "u2.unlock_user_confirmation": "Sind Si<PERSON> sicher, dass der Benutzer entsperrt werden soll?", "u2.unlock_user_successful": "Benutzer erfolgreich entsperrt.", "u2.unlock_user_unsuccessful": "Der Benutzer konnte nicht entsperrt werden.", "u2.update_filter_with_given_saved_filter": "Auswertungsfilter aktualisieren (%saved_filter_name%)", "u2.update_saved_filter": "Auswertungsfilter ändern", "u2.user_already_unlocked": "Der Benutzer ist bereits entsperrt.", "u2.user_is_inherited": "Dieser Benutzer wurde von einer Gruppe übernommen.", "u2.user_permissions": "Benutzerberechtigungen", "u2.user_requesting_permissions": "Benutzer %user_name% fordert Berechtigungen für die Datei %file_name% mit der Nachricht:", "u2.user_settings.two_factor.enforced_by_admin": "Die Zwei-Faktor-Authentifizierung wurde vom System-Administrator aktivier<PERSON> und kann daher nicht deaktiviert werden", "u2.vat_number": "USt-IdNr.", "u2.visibility_group_permissions_warning": "Zugewiesene Gruppen haben keine Auswirkung, da „<PERSON><PERSON>r alle Benutzer sichtbar“ aktiviert ist", "u2.visibility_user_permissions_warning": "Zugewiesene Benutzer haben keine Auswirkung, da „<PERSON>ür alle Benutzer sichtbar” aktiviert ist", "u2.visible_to_all_users": "<PERSON><PERSON><PERSON> alle Benutzer sichtbar", "u2.watch.start_watching": "Beobachtung starten", "u2.watch.stop_watching": "Beobachtung stoppen", "u2.widget.incompatible_arguments": "Die Werte der folgenden Parameter sind nicht kompatibel: %properties%. Das Widget „%widget_name%“ kann nicht dargestellt werden.", "u2.widget.transaction_table.sub_filter": "Unterfilter", "u2.widget.transaction_table.sub_filter.help": "Der „Unterfilter“ ist nicht mit kompatibel „Ergebnisse gruppieren“. Verwenden Sie entweder „Ergebnisse gruppieren“ oder „Unterfilter“.", "u2.workflow.configuration_error": "Bitte stellen Si<PERSON> sicher, dass der Workflow richtig konfiguriert ist", "u2.workflow.could_not_transition_status_changed": "Transition des Status von %count% Datensatz konnte nicht ausgeführt werden, da dieser bereits geändert wurde oder die Transition nicht mehr existiert.|Transition des Status von %count% Datensätzen konnte nicht ausgeführt\n          werden, da dieser bereits geändert wurde oder die Transition nicht mehr existiert.\n        ", "u2.workflow.transition_confirm": "Sie sind dabei, die Transition „%transition_name%“ auszuführen", "u2.workflow_check.disabled": "<PERSON><PERSON>list<PERSON>lement wurde von Ihrem Administrator <PERSON>akti<PERSON>.", "u2.xbrl_generation_fail": "Die XBRL-<PERSON><PERSON> konnte nicht generiert werden.", "u2.xml_file": "XML Datei", "u2.yes": "<PERSON>a", "u2_apm.module_name": "Asset & Participation Management", "u2_comment.comment": "Kommentar", "u2_comment.delete_comment": "Kommentar löschen", "u2_comment.delete_comment.confirmation": "Sind Si<PERSON> sicher, dass der Kommentar gelöscht werden soll?", "u2_comment.deleted_comment": "Kommentar wurde gelöscht", "u2_comment.edit_comment": "<PERSON><PERSON><PERSON><PERSON> bear<PERSON>", "u2_comment.new_comment": "<PERSON><PERSON>er Kommentar", "u2_comment.unrestricted": "<PERSON>cht e<PERSON>schrä<PERSON>", "u2_comment.quote": "Zitieren", "u2_comment.restricted_to_given_group": "Eingeschränkt auf: %group_name%", "u2_contractmanagement.contract": "Vertrag", "u2_contractmanagement.contract.plural": "Verträge", "u2_contractmanagement.contract_type": "Vertragsar<PERSON>", "u2_contractmanagement.contract_type.plural": "Vertragsarten", "u2_contractmanagement.date": "Datum", "u2_contractmanagement.days_until_automatic_renewal": "Vertragsverlängerung in Tagen", "u2_contractmanagement.details": "Details", "u2_contractmanagement.expiry_date": "Vertragsende", "u2_contractmanagement.identification_code": "Vertragsnummer", "u2_contractmanagement.name": "Name", "u2_contractmanagement.notice_period_in_days": "Mitteilungsfrist in Tagen", "u2_contractmanagement.parties": "Beteiligte", "u2_contractmanagement.partner_is_third_party": "Partner ist „Fremder Dritter“", "u2_contractmanagement.partner_unit": "Partner Unit", "u2_contractmanagement.partner_unit_country": "Partner Unit Land", "u2_contractmanagement.partner_unit_name": "Name Partner Unit", "u2_contractmanagement.reminder_date": "Erinnerungsdatum", "u2_contractmanagement.third_party_country": "Land „Dritter“", "u2_contractmanagement.third_party_name": "Name „Dritter“", "u2_contractmanagement.unit_country": "Unit Land", "u2_core.about": "Info", "u2_core.access_type": "Zugriffstyp", "u2_core.account": "Benutzerkonto", "u2_core.account_expires": "Benutzerkonto läuft ab am", "u2_core.account_number": "Accountnummer", "u2_core.account_number_help": "BZSt Online Portal (BOP) Account-Nummer mit der die CbCR XML Datei im BOP an das Bundeszentralamt für Steuern übermittelt wird. Nach Anmeldung im BOP findet man die Nummer im Bereich \"Mein BOP\".", "u2_core.active": "Aktiv", "u2_core.add_a_review": "Review hinzufügen", "u2.add_attachment": "<PERSON><PERSON>", "u2_core.add_new_calendar_entry": "Kalendereintrag hinzufügen", "u2_core.add_new_entry": "Neuen Eintrag hinzufügen", "u2_core.add_new_exchange_rate": "Neuen Wechselkurs hinzufügen", "u2_core.add_new_given_entity_type": "Neu %entity_type_name% hinzufügen", "u2.unit_hierarchy_structure_at": "Struktur am %date%", "u2.no_permission_to_perform_action": "Ihnen fehlt die Berechtigung „%permission%“, um diese Aktion auszuführen.", "u2_core.add_new_hierarchy": "Neue Hierarchie", "u2.saving": "Wir speichern derzeit Ihre Daten. Bitte warte einen Moment.", "u2_core.add_new_period": "Neue Periode hinzufügen", "u2_core.add_new_status": "Neuen Status hinzufügen", "u2_core.add_new_system_message": "Neue Systemmeldung hinzufügen", "u2_core.add_new_unit": "Neue Unit hinzufügen", "u2_core.add_new_user": "Neuen Benutzer hinzufügen", "u2_core.add_new_user.success": "Der Benutzer wurde erfolgreich hinzugefügt.", "u2_core.add_new_user_group": "Neue Benutzergruppe hinzufügen", "u2_core.add_subscription": "Subskription hinzufügen", "u2_core.add_unit.warning_selected_hierarchy_has_no_units_for_this_date": "Der ausgewählten Hierarchie sind keine Units zu diesem Datum zugeordnet.", "u2_core.added_on_given_date": "Erstellt am %date%", "u2_core.address": "<PERSON><PERSON><PERSON>", "u2_core.administration": "Administration", "u2_core.application": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.application_language": "Anwendungssprache", "u2_core.assign_extra_permissions": "Zusätzliche Berechtigungen zuweisen", "u2_core.assign_to_me": "<PERSON>", "u2_core.assign_user_to_given_entity_name": "Dem Benutzer „%entity_name%“ zuweisen", "u2_core.assigned": "Zugeordnet", "u2_core.assigned_components": "Zuordnete Komponenten", "u2.assigned_user_groups": "Zugewiesene Gruppen", "u2_core.assigned_users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_core.assignee": "<PERSON><PERSON><PERSON>", "u2_core.associated_statuses": "Zugehörige Status", "u2_core.attachments": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.attributes": "Eigenschaften", "u2_core.audit": "Prüfung", "u2_core.auditor": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.auditor.plural": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.authorisation.add_authorisation": "Berechtigung hinzufügen", "u2_core.authorisation.add_authorisation_profile": "Berechtigungsprofil hinzufügen", "u2_core.authorisation.add_new_authorisation": "Neue Berechtigung hinzufügen", "u2_core.authorisation.add_new_profile": "Neues Profil hinzufügen", "u2_core.authorisation.authorisation": "Berechtigungen", "u2_core.authorisation.authorisation_list": "Berechtigungsliste", "u2_core.authorisation.authorisation_overview": "Berechtigungsübersicht", "u2_core.authorisation.authorisation_profile": "Berechtigungsprofil", "u2_core.authorisation.authorisations": "Berechtigungen", "u2_core.authorisation.authorised_groups": "Berechtigte Gruppe", "u2_core.authorisation.authorised_users": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.authorisation.create_authorisation.success": "Berechtigung erfolgreich erstellt.", "u2_core.authorisation.create_authorisation_profile.success": "Berechtigungsprofil erfolgreich erstellt.", "u2_core.authorisation.delete_authorisation_with_given_name": "Berechtigung löschen (%authorization_name%)", "u2_core.authorisation.delete_profile_with_given_name": "Profil <PERSON> (%profile_name%)", "u2_core.authorisation.edit_authorisation": "Berechtigung bearbeiten", "u2_core.authorisation.edit_profile": "<PERSON><PERSON>", "u2_core.authorisation.item": "<PERSON><PERSON>", "u2_core.authorisation.profiles": "Profile", "u2_core.authorisation.requires_authorisation_to": "Erfordert Berechtigung zum", "u2_core.authorisation.rights": "<PERSON><PERSON><PERSON>", "u2_core.authorisation.update_authorisation.success": "Berechtigung erfolgreich aktualisiert.", "u2_core.authorisation.update_authorisation_profile.success": "Berechtigungsprofil erfolgreich aktualisiert.", "u2_core.available_options": "Verfügbare Optionen", "u2_core.back_to_the_support_information_page": "Zurück zur Support-Informationsseite", "u2_core.back_to_transition": "Zurück zu Transition", "u2_core.back_to_workflow": "Z<PERSON><PERSON> zu Workflow", "u2_core.base": "<PERSON><PERSON>", "u2_core.billing_address": "Rechnungsaddresse", "u2_core.branch": "Branche", "u2_core.branch.plural": "Branche", "u2_core.bulk_delete.error_no_permission": "Sie haben nicht genügend Berechtigungen, um eine oder mehrere der ausgewählten Einträge zu löschen.", "u2_core.bulk_delete.success_for_given_amount_of_entries": "Eintrag wurde erfolgreich gelöscht.|%count% Einträge wurden erfolgreich gelöscht.", "u2_core.bulk_edit.invalid_record": "Datensatz mit ID %id% is ungültig: %violation_message%", "u2_core.bulk_edit_given_amount_of_selected_records.confirmation": "Sind <PERSON> sic<PERSON>, dass der ausgewählte Datensatz %count% geändert werden soll?", "u2_core.bulk_edit_given_item_with_given_count": "Einträge %item_name% (%count%) bearbeiten", "u2_core.bulk_update.error_no_permission_to_edit_the_selected_records": "Sie haben keine Berechtigung, die ausgewählten Datensätze zu bearbeiten.", "u2_core.bulk_update.success": "Sie haben die ausgewählten Datensätze aktualisiert.", "u2_core.bzst_number": "BZSt-Nummer", "u2_core.bzst_number_help": "BZSt Nummer, mit der die CbCR XML Datei im BZSt Online Portal (BOP) an das Bundeszentralamt für Steuern übermittelt wird (z.B. BZ123456789).", "u2_core.calendar": "<PERSON><PERSON><PERSON>", "u2_core.calendar_week_with_given_week_number": "Kalenderwoche %week_number%", "u2_core.change_password": "Passwort ändern", "u2_core.change_password.success": "Passwort geändert.", "u2_core.change_password.success_please_login_using_your_new_password": "Passwort ist erfolgreich geändert. Melden Sie sich bitte mit Ihrem neuen Passwort an.", "u2_core.change_password_for_given_user": "Passwort für „%username%“ ändern", "u2_core.changes": "Änderungen", "u2_core.click_to_remove_review": "Klicken Sie um den Review zu entfernen", "u2_core.click_to_review_this_given_entity_type": "Für Review anklicken", "u2_core.click_to_show_help": "Hier Klicken um Hilfe zu zeigen", "u2_core.client_information": "Client-Information", "u2_core.client_system_information": "Computer System Information", "u2.close_menu": "<PERSON><PERSON> sch<PERSON>ßen", "u2_core.coloring_links_appropriately": "Farblinks geeignet", "u2_core.comments": "Kommentare", "u2_core.company": "Unternehmen", "u2_core.completed_at": "Abgeschlossen um", "u2_core.component": "Komponente", "u2_core.configuration_data": "Konfigurationsdaten", "u2_core.configuration_information": "Konfigurationsinformationen", "u2_core.confirm_bulk_edit": "Bearbeitung Mehreinträge bestätigen", "u2_core.confirm_new_password": "Neues Passwort bestätigen", "u2_core.confirm_remove": "Entfernen bestätigen", "u2_core.contact_user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_core.content": "Inhalt", "u2_core.conversion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_core.cookies_enabled": "<PERSON><PERSON> zu<PERSON>", "u2_core.corp_logo": "Logo", "u2_core.corp_logo.help": "\n          • SVG\n        ", "u2_core.create_a_new_given_additional_name": "Neu %additional_name% anlegen", "u2_core.create_file.success": "<PERSON>i g<PERSON>.", "u2_core.created": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.created_at": "Erstellt am", "u2_core.created_by": "<PERSON><PERSON><PERSON><PERSON> von", "u2_core.creating_new_filters": "Neue Filter anlegen", "u2_core.creating_new_subscriptions": "Neue Subskription anlegen", "u2_core.cron_expression": "CRON Ausdruck", "u2_core.currency": "Währung", "u2_core.currency.missing": "Die Applikationswährung wurde nicht festgelegt. Bitte konfigurieren Sie diese in den Systemeinstellungen.", "u2_core.currency.plural": "Währungen", "u2_core.current_password": "Aktuelles Passwort", "u2_core.current_week_overview": "Übersicht der aktuellen Woche", "u2.dashboard": "Dashboard", "u2.dashboard.help": "Das ist Ihr Dashboard. Hier finden Sie Tools und Schnellzugriffe zur Unterstützung Ihrer Arbeit.", "u2.dashboard.plural": "Dashboards", "u2_core.deadline_type": "Art der Frist", "u2_core.deadline_type.plural": "Arten der Frist", "u2_core.delete.cannot_delete_due_to_associated_data": "Der Löschvorgang konnte aufgrund bestehender Datenverbindungen nicht abgeschlossen werden.", "u2_core.delete_country_with_given_name": "Land (%country_name%) löschen", "u2_core.delete_country_with_given_name.confirmation": "Sind <PERSON> sic<PERSON>, dass dieses Land (%country_name%) gelöscht werden soll?", "u2_core.delete_current_picture.hint_use_default": "Sind Si<PERSON> sicher, dass das aktuelle Bild gelöscht und mit dem Standardbild ersetzt werden soll?", "u2_core.delete_entry.confirmation": "Sind <PERSON> sicher, dass Si<PERSON> den Eintrag löschen wollen?", "u2_core.delete_exchange_rate_with_given_name": "Wechselkurs (%exchange_rate_name%) löschen", "u2_core.delete_exchange_rate_with_given_name.confirmation": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass dieser Wechselkurs (%exchange_rate_name%) gel<PERSON>scht werden soll?", "u2_core.delete_filter": "<PERSON><PERSON>", "u2_core.delete_filter.confirmation": "Sind <PERSON> sicher, dass dieser Filter gelö<PERSON>t werden soll?", "u2_core.delete_given_entity_type": "Löschen %entity_type_name%", "u2_core.delete_given_entity_type.confirmation": "Sind Si<PERSON> sicher, dass %entity_type_name% gelöscht werden soll?", "u2_core.delete_given_entity_type_with_given_name": "Löschen %entity_type_name% (%entity_name%)", "u2_core.delete_given_entity_type_with_given_name.confirmation": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass \"%entity_type_name% (%entity_name%)\" gelöscht werden soll?", "u2_core.delete_status": "Status löschen", "u2_core.delete_status.confirmation": "Sind Si<PERSON> sicher, dass dieser Status gelöscht werden soll?", "u2_core.delete_status_with_given_name": "Status (%status_name%) löschen", "u2_core.delete_status_with_given_name.confirmation": "Sind <PERSON> sic<PERSON>, dass der Status (%status_name%) gel<PERSON>scht werden soll?", "u2_core.delete_subscription.success": "Löschen der Subskription erfolgreich durchgeführt.", "u2_core.delete_system_message": "Systemmeldung löschen", "u2_core.delete_system_message.confirmation": "Sind Si<PERSON> sicher, dass diese Systemmeldung gelöscht werden soll?", "u2_core.delimiter": "Trennzeichen", "u2_core.description": "Beschreibung", "u2_core.details": "Details", "u2_core.direct_quotation": "Preisnotierung", "u2_core.direct_quotation_value": "Preisnotierte Ku<PERSON>", "u2_core.display_from": "<PERSON><PERSON><PERSON> von", "u2_core.display_to": "Anze<PERSON> zu", "u2_core.documentation_corp_logo": "Dokumentationslogo", "u2_core.documentation_corp_logo.help": "• Das Logo-Bild muss viereckig sein <br>\n          • Erforderliche Größe: 10 x 10 Millimeter mit 120 dpi (47 x 47 Pixel)", "u2_core.documentation_cover_picture": "Bild auf Deckblatt der Dokumentation", "u2_core.documentation_cover_picture.help": "• Maximale Größe: 180 x 90 Millimeter mit 120 dpi (850 x 425 Pixel)<br>\n          • Empfohlene Größe: 90 x 45 Millimeter mit 120 dpi (425 x 213 Pixel)", "u2_core.download": "Download", "u2_core.download_file": "<PERSON><PERSON> run<PERSON>", "u2_core.edit_attributes": "Eigenschaften bearbeiten", "u2_core.edit_attributes_of_given_unit_hierarchy": "Eigenschaft der Unit Hierarchie „%unit_hierarchy_name%“ bearbeiten.", "u2_core.edit_country_with_given_name": "Land (%country_name%) bearbeiten", "u2_core.edit_exchange_rate_with_given_name": "Wechselkurs (%exchange_rate_name%) bearbeiten", "u2_core.edit_file": "<PERSON><PERSON> bearbeiten", "u2_core.edit_filter": "Filter bearbeiten", "u2_core.edit_given_dashboard": "„%dashboard_title%“ Dashboard bearbeiten", "u2_core.edit_given_entity_type_with_given_name": "Bearbeiten %entity_type_name% (%entity_name%)", "u2_core.edit_given_period": "Periode (%period%) bearbeiten", "u2_core.edit_given_subscription": "Registrierung (%subscription_name%) bearbeiten", "u2_core.edit_status_with_given_name": "Status (%status_name%) bearbeiten", "u2_core.edit_structure": "Struktur bearbeiten", "u2_core.edit_support_information": "Support-Information bearbeiten", "u2_core.edit_system_message": "Systemmeldung bearbeiten", "u2_core.edit_system_settings": "Systemeinstellungen bearbeiten", "u2_core.edit_the_support_information": "Support-Information bearbeiten", "u2_core.edit_user_group": "Benutzergruppe bearbeiten", "u2_core.edit_user_settings": "Benutzereinstellungen bearbeiten", "u2_core.elma5_help": "\n        <span>Hier können  CbCR XML Datein im ELMA-Format generiert, heruntergeladen und über das <a href=\"https://www.elster.de/bportal/meinelster\">BZSt online portal</a> (BOP) übertragen werden. Weitere Informationen zur Übermittlung finden Sie auf der Webseite des <a href=\"https://www.bzst.de/DE/Unternehmen/Intern_Informationsaustausch/CountryByCountryReporting/countrybycountryreporting_node.html\">BZSt</a>.</span>\n        ", "u2_core.email": "E-Mail", "u2_core.empty_widget": "<PERSON><PERSON> Widget.", "u2_core.empty_widget.help": "<PERSON>te fügen Si<PERSON> einen Inhalt ein.", "u2_core.enable_manual_review": "Manuelle Review aktivieren", "u2_core.end": "<PERSON><PERSON>", "u2_core.end_date": "Enddatum", "u2_core.english": "English", "u2_core.enter_filter_term": "Filterbegriffe eingeben", "u2_core.entities_linked_with_given_file": "Entities verknüpft mit „%file_name%“", "u2_core.error_could_not_save_check_the_highlighted_fields": "<PERSON>s kann nicht gespeichert werden. Überprüfen Sie bitte die unten markierten Felder und versuchen Si<PERSON> es erneut.", "u2_core.exchange_rate": "Wechselkurs", "u2_core.exchange_rate_unavailable": "Umrechnungskurse nicht verfügbar", "u2_core.exchange_rates": "Wechselkurse", "u2_core.expand_sidebar": "Seitenleiste erweitern", "u2_core.experimental": "Experimentell", "u2_core.fax": "Fax", "u2_core.file.access_type_protected": "Geschützt", "u2_core.file.access_type_protected.help": "Nur ausdrücklich zugewiesene Benutzer und / oder Gruppen können diese Datei anzeigen. Der Dateiname ist für andere Benutzer nicht sichtbar.", "u2_core.file.access_type_smart": "Smart", "u2_core.file.access_type_smart.help": "Benutzer können diese Date<PERSON> sehen, wenn sie den Date<PERSON>z ansehen können, an den sie angehängt ist. Zusätzliche Benutzer und Gruppen können auch explizit der Datei zugeordnet werden.", "u2_core.file_mime_content_type": "Datei MIME Content-Typ", "u2_core.file_not_found": "Datei nicht gefunden", "u2_core.files": "<PERSON><PERSON>", "u2_core.filter": "Filter", "u2_core.filter_information": "<PERSON><PERSON> filtern", "u2_core.filter_query": "Filterabfrage", "u2_core.filter_table.error_occurred_filters_have_been_reset": "Einen Fehler ist während der Auswertung der aktuellen Tabelle aufgetreten. Der Filter wurde zurückgesetzt.", "u2_core.filter_target": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.filters": "Filter", "u2.frequency": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.from_date": "<PERSON><PERSON>", "u2_core.full_text_search": "Volltextsuche", "u2_core.further_browser_information": "Zusätzliche Browser-Information", "u2_core.general": "Allgemein", "u2_core.german": "De<PERSON>ch", "u2_core.given_entity_type_list": "Liste %entity_type_name%", "u2_core.given_units_are_not_assigned_to_user": "Sie haben nicht die Befugnis, die folgenden Units hinzuzufügen: %units%", "u2_core.given_user_requests_permission_to_given_file": "%user_name% fordert die Berechtigung für %file_name%", "u2_core.given_version": "Version %version%", "u2_core.go_to_support_platform": "Sie können mit uns über unsere Support-Platform in Kontakt treten:", "u2_core.go_to_the_corresponding_list_page": "\n            <ol>\n              <li>Gehen Sie auf die entsprechende Übersichtsseite.</li>\n              <li>Verwenden Sie die Filterparameter nach Bedarf.</li>\n              <li>Klicken Sie auf <span class=\"icon-save-alt\"></span><strong>Speichern</strong>.</li>\n              <li>Geben Sie den Namen und die Beschreibung ein und klicken Sie auf Speichern.</li>\n            </ol>\n        ", "u2_core.go_to_the_universal_units_website": "Gehe zur Universal Units websseite", "u2_core.group": "Gruppe", "u2_core.group_parent_income_tax": "Organträger - Ertragssteuern", "u2_core.group_parent_vat": "Organträger - VAT", "u2_core.groups": "Gruppen", "u2_core.help": "<PERSON><PERSON><PERSON>", "u2_core.hide": "Verstecken", "u2_core.id": "ID", "u2_core.import.confirm": "Import bestätigen", "u2_core.import.default_delimiter.help": "Das Standard-Trennzeichen von Einträgen in CSV-Dateien, welches für Imports genutzt wird.", "u2_core.import.email_content_fail": "Der Import ist fehlgeschlagen. Um das Ergebnis zu sehen, klicken Si<PERSON> auf den Link:l", "u2_core.import.email_content_success": "Der Import wurde erfolgreich abgeschlossen. Um das Ergebnis zu sehen, klicken Si<PERSON> auf den Link.", "u2_core.import.email_title_fail": "Import fehlgeschlagen", "u2_core.import.email_title_success": "Erfolgreicher Import", "u2.import.file.help": "Die Datei welche die zu importierenden Daten enthält.", "u2.import.simulate.help": "<PERSON>t dieser Option wird der Import lediglich simuliert, keine Werte werden importiert. Diese Funktion kann genutzt werden um festzustellen ob die Datei gültig ist oder ob sie Fehler enthält.", "u2_core.import.help": "<p><PERSON><PERSON> können auf dieser Seite Daten in CSV- oder Excel-Format importieren. Wählen Sie einfach eine CSV- oder Excel-Datei aus, welche die zu importierende Daten beinhaltet und drücken Sie auf die „Import“ Button.</p>\n          <p>Die CSV- oder Excel-Datei kann Werte in beliebiger Reihenfolge enthalten. Die CSV- oder Excel-Datei muss eine Kopfzeile, die das Dateiformat und die Reihenfolge der zu importierende Werte definiert, enthalten.</p>", "u2_core.import.help_values_contain_commas": "\n            <p><PERSON>n die Werte durch Kommas getrennt sind, müssen sie doppelte Anführungszeichen enthalten:</p>\n            <div class=\"data-group font-mono text-sm\">\n            <PERSON><PERSON><PERSON>pfzeile,zweite Kopfzeile,dritte Kopfzeile\n            <br>\n            <PERSON><PERSON><PERSON>,\"hinge<PERSON>, zweiter Wert eine Komma beinhaltet\",dritter Wert\n            </div>\n        ", "u2_core.import.selection": "Was möchten Sie importieren?", "u2_core.import.list": "Import-Liste", "u2_core.import.new": "Neuer Import", "u2_core.import.not_simulated_confirmation": "<PERSON>e sind da<PERSON>, den Inhalt der ausgewählten Datei in die Anwendung zu importieren. Bitte brechen Sie diesen Vorgang ab und wählen Sie die Option „Simulieren“, wenn Sie diese Datei zunächst auf Fehler und Kompatibilität\n          testen möchten.\n        ", "u2_core.import.possible_headers_data_import": "Die folgende Liste zeigt die möglichen Spaltenüberschriften für diesen Datenimport:", "u2_core.import.result": "Ergebnis des Imports", "u2_core.import.select_a_file_to_import": "Datei auswählen zum Ho<PERSON>laden", "u2_core.import.start_import": "Import starten", "u2_core.import.uploaded_file": "Hochgeladene Datei", "u2_core.import_exchange_rates": "Import Wechselkurse", "u2_core.import_was_completed": "Der Import wurde abgeschlossen.", "u2_core.indirect_quotation": "Mengennotierung", "u2_core.indirect_quotation_value": "Mengennotierungswert", "u2_core.initial_status": "Initial Status", "u2_core.input_currency": "Von-Wä<PERSON>ung", "u2_core.is_closed": "Ist geschlossen", "u2_core.iso_code": "ISO Code", "u2.item": "Menüpunkt", "u2_core.last_login": "Letzte Anmeldung", "u2_core.last_run": "<PERSON><PERSON><PERSON>", "u2_core.last_sent": "Zuletzt geschickt", "u2.datasheets.datasheet": "Datasheet", "u2_core.legal": "Legal", "u2_core.legal_form": "Rechtsform", "u2_core.legal_form.plural": "Rechtsformen", "u2_core.legal_name": "Juristischer Name", "u2_core.link": "Verknüpfen", "u2_core.link_an_existing_file": "Verknüpfen einer vorhandenen Datei", "u2_core.link_file_to_entity.success": "Die Datei wurde erfolgreich verknüpft.", "u2_core.linked_entities": "Verknüpfte Entities", "u2_core.login": "<PERSON><PERSON>", "u2_core.login_background": "Login-<PERSON><PERSON>grun<PERSON>", "u2_core.login_background.help": "\n          • Minimale Größe: 750 x 850 Pixel<br>\n          • Empfohlene Größe: 1125 x 1275 Pixel oder mehr\n      ", "u2_core.login_logo": "<PERSON><PERSON> auf Login-Screen", "u2_core.login_logo.help": "\n            • SVG oder transparentes PNG<br>\n            • Empfohlene Größe: nicht größer als 400 Pixel breit und 250 Pixel hoch\n        ", "u2_core.logout": "Abmelden", "u2_core.manage": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.manually_run_subscription.success": "Manuelle Durchführung der Subskription erfolgreich ausgeführt.", "u2_core.max_login_attempts": "Maximale Anzahl der Anmeldeversuche", "u2_core.maximum_file_size": "Maximale Dateigröße:", "u2_core.menu": "<PERSON><PERSON>", "u2_core.message": "<PERSON><PERSON><PERSON>", "u2_core.mobile": "Mobiltelefon", "u2_core.moved_on_given_date": "Geändert am %date%", "u2_core.my_permissions": "<PERSON><PERSON>", "u2_core.n_a": "n.a", "u2_core.name": "Name", "u2_core.name_first": "<PERSON><PERSON><PERSON>", "u2_core.name_last": "Nachname", "u2_core.name_long": "Name (lang)", "u2_core.name_short": "Name (kurz)", "u2_core.nationality_long": "Nationalität (lang)", "u2_core.nationality_short": "Nationalität (kurz)", "u2_core.never": "<PERSON><PERSON><PERSON>", "u2_core.next_calendar_week": "Nächste Kalenderwoche", "u2_core.next_run": "<PERSON>ä<PERSON><PERSON>", "u2_core.next_sent": "Nächste Benachrichtigung", "u2_core.no_changes_on_given_date": "Keine Änderung am %date%", "u2.no_changes": "<PERSON><PERSON>", "u2_core.no_period_selected": "Keine Periode ausgewählt", "u2_core.no_permissions_assigned_with_given_permission_type": "Keine Berechtigung %permission_type% wurde zugeordnet", "u2_core.no_subscriptions": "<PERSON>ine <PERSON>skription", "u2_core.none": "<PERSON><PERSON>", "u2_core.none_selected": "<PERSON><PERSON> ausgewähl<PERSON>", "u2_core.number_of_attachments": "Anza<PERSON> der Anhänge", "u2_core.number_of_reviews": "<PERSON><PERSON><PERSON>", "u2.number_of_widgets": "<PERSON><PERSON><PERSON> der Widgets", "u2_core.open_beginning_of_year": "Jahresbeginn öffnen", "u2_core.open_beginning_of_year.help": "Manuelle Einträge zum Jahresbeginn sind erlaubt", "u2_core.operating_system": "Betriebsystem", "u2_core.or": "oder", "u2_core.output_currency": "Nach-Währung", "u2_core.owner": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.parent_legal_unit": "Übergeordnete legale Unit", "u2_core.parent_user": "Übergeordneter Benutzer", "u2.password": "Passwort", "u2.password_expires": "Passwort abgelaufen", "u2.password_forgotten": "Passwort vergessen?", "u2.password_history_length": "Länge des Kennwortverlaufs", "u2.password_max_age_in_days": "Passwort Höchstalter in Tagen", "u2.password_must_have_at_least_one_lowercase_letter": "Muss mindestens einen Kleinbuchstaben beinhalten.", "u2.password_must_have_at_least_one_non_alphanumeric_character": "Muss mindestens ein Symbol beinhalten.", "u2.password_must_have_at_least_one_number": "Muss mindestens eine Zahl beinhalten.", "u2.password_must_have_at_least_one_uppercase_letter": "Muss mindestens einen Großbuchstaben beinhalten.", "u2.password_must_meet_the_following_constraints": "Das Passwort muss den folgenden Anforderungen entsprechen:", "u2.password_requires_at_least_one_lowercase_letter": "Das Password benötigt mindestens einen Kleinbuchstaben", "u2.password_requires_at_least_one_non_alphanumeric_character": "Das Password benötigt mindestens ein Symbol", "u2.password_requires_at_least_one_number": "Das Password benötigt mindestens eine Zahl", "u2.password_requires_at_least_one_uppercase_letter": "Das Password benötigt mindestens einen Großbuchstaben", "u2.password_reset": "Passwort zurücksetzen", "u2.password_reset_hours_valid": "Gültigkeit des Passwort-Reset-Links in Stunden", "u2_core.period": "Periode", "u2_core.period_list": "Periodenübersicht", "u2_core.period_management": "Periodenmanagement", "u2_core.permission_mask.1": "<PERSON><PERSON>", "u2_core.permission_mask.1073741823": "<PERSON><PERSON>", "u2_core.permission_mask.128": "Manage", "u2_core.permission_mask.16": "Wiederherstellen", "u2_core.permission_mask.2": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.permission_mask.32": "<PERSON><PERSON><PERSON>", "u2_core.permission_mask.4": "<PERSON><PERSON><PERSON>", "u2_core.permission_mask.64": "<PERSON><PERSON>", "u2_core.permission_mask.8": "Löschen", "u2_core.permissions": "Berechtigung", "u2_core.permissions_request_sent.success": "Ihr Antrag auf Berechtigungen wurde erfolgreich gesendet.", "u2_core.personal_information": "Persönliche <PERSON>", "u2_core.please_choose_a_new_password_for_given_user": "Wählen Sie bitte ein neues Passwort für „%username%“.", "u2_core.postal_address": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.previous_calendar_week": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.previous_period": "Vorherige Periode", "u2_core.profile": "Profil", "u2_core.progress": "Fort<PERSON><PERSON>t", "u2_core.read": "<PERSON><PERSON>", "u2_core.ref_id": "Ref. ID", "u2_core.register_number": "Registernummer", "u2_core.registry_place": "Registergericht", "u2_core.remove_file": "<PERSON><PERSON> ent<PERSON>nen", "u2_core.remove_review": "Review entfernen", "u2_core.remove_review.success": "Review erfolgreich entfernt", "u2_core.removed_on_given_date": "Gelöscht am %date%", "u2_core.reporter": "<PERSON><PERSON>", "u2_core.request_a_password_reset": "Passwortzurücksetzung abfragen", "u2_core.reset_password": "Passwort zurücksetzen", "u2_core.result": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.review.remove_review.confirm": "Sind <PERSON> sicher, dass Sie diesen Review entfernen möchten?", "u2_core.review.submit_review.confirm": "Sind Sie sicher den Review durchzuführen?", "u2_core.review.success": "Review erfolgreich.", "u2_core.reviews": "Reviews", "u2_core.roles": "<PERSON><PERSON>", "u2_core.run_now_this_given_subscription": "Subskription (%subscription_name%) jetzt starten", "u2_core.save_groups": "Gruppe speichern", "u2_core.save_roles": "<PERSON>en speichern", "u2_core.save_units": "Units speichern", "u2_core.save_users": "Benutzer speichern", "u2.saved_filter": "Gespeicherte Filter", "u2.saved_filter.frequency.help": "\n          <p>Eine Frequenz beinhaltet fün<PERSON>, die durch Leerzeichen getrennt sind:</p>\n          <ul>\n            <li><PERSON><PERSON><PERSON> (0 to 59)</li>\n            <li>St<PERSON><PERSON> (0 to 23)</li>\n            <li><PERSON> eines Monats (1 to 31)</li>\n            <li><PERSON><PERSON> (1 to 12 or JAN to DEC)</li>\n            <li>Wochentag (0 to 6 oder Sonntag zu Samstag, 1 = Montag)</li>\n          </ul>\n          <p><PERSON> (<kbd>*</kbd>) bedeutet „alle“.</p>\n          <p>Verwenden Sie Kommata (<kbd>,</kbd>) zur Abtrennung der items in der Liste.</p>\n          <p>Verwenden Sie Bindestriche (<kbd>-</kbd>) um einen Bereich zu definieren.</p>\n          <p>Verwenden Si<PERSON>rich (<kbd>/</kbd>) um das Inkremet zu spezifizieren. N<PERSON> Werte, die gleichmäßig in ihrem Bereich aufgeteilt sind, sind gültig (Minuten und sekunden: /2, /3, /4, /5, /6, /10, /12, /15, /20, /30. Stunden: /2, /3, /4, /6, /8, /12). Die Zahl vor dem Querstrich gibt an, wo das Inkrement startet (3/6 in Stunden bedeutet am 3, 9, 15, und 21), keine Werte vor dem Querstrich bedeutet 0.</p>\n          <p>Für zusätzliche Informationen gehen Sie auf: <a href=\"https://de.wikipedia.org/wiki/Cron\" target=\"_blank\">https://de.wikipedia.org/wiki/Cron#Beispiele</a>.</p>\n          <p>Beispiele:</p>\n          <ul>\n            <li><kbd>5 * * * *</kbd> Jeden Tag um jede Stunde und 5 Minuten (0:05, 1:05, […], 23:05)</li>\n            <li><kbd>0 7 * * MON</kbd> Montags um 7:00</li>\n            <li><kbd>0 9 1,15 * *</kbd> Der erste und fünfzehnte Tag eines Monats um 9:00</li>\n            <li><kbd>*/15 9-18 * * MON-FRI</kbd> Alle fünfzehn Minuten in Wochentage zwischen 9:00 und 18:00</li>\n          </ul>\n        ", "u2.saved_filter.subscription_did_not_run": "Subskription ist überfällig.", "u2.saved_filter.success_the_given_filter_has_been_saved": "Speichervorgang des Filters „%saved_filter_name%“ wurde erfolgreich durchgeführt.", "u2.saved_filter_subscription": "Gespeicherte Subskription", "u2.saved_filter_subscriptions": "Gespeicherte Subskriptionen", "u2.saved_filter_subscriptions.help": "Um eine neue Subskription anzulegen, gehen Sie zur Bearbeitungsseite der gespeicherten Filter", "u2_core.security": "Sicherheit", "u2_core.see_all_saved_filters": "Zeige alle gespeicherten Filter", "u2.select_a_file": "<PERSON>i ausw<PERSON>hlen", "u2.select_a_period": "Periode auswählen", "u2.select_a_unit": "Unit auswählen", "u2.select_a_user": "Benutzer auswählen", "u2.select_an_item": "Auswahl treffen", "u2.select_entity_type_to_create": "Zum Anlegen wähle den Typ aus", "u2.select_file_types": "Dateityp auswählen", "u2.select_unit_type": "Auswahl Unit-Typ", "u2.select_unit_type_to_create": "Zum Anlegen wählen Sie den Unit-Typ", "u2_core.send": "Senden", "u2_core.send_email": "E-Mail senden", "u2_core.show": "Anzeigen", "u2_core.show_current_calendar_week": "Zeige aktuelle Kalenderwoche", "u2_core.show_current_date": "Zeige aktuelles Datum", "u2_core.show_entities_linked_with_this_file": "Zeige die Vorgänge, die mit dieser Datei verknüpft sind.", "u2_core.show_information_about_your_system": "Information zu Ihrem System zeigen", "u2_core.show_more": "<PERSON><PERSON><PERSON> mehr", "u2_core.show_the_filtered_table": "Filterergebnisse anzeigen", "u2_core.showing_count_of_total_count_records": "Es werden %showing_count% von %count% Datensatz angezeigt|Es werden %showing_count% von %count% Datensätzen angezeigt", "u2.simulate": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.start": "Start", "u2_core.start_date": "Anfangsdatum", "u2_core.started_at": "Angefangen am", "u2_core.status": "Status", "u2_core.status_list": "Statusliste", "u2_core.status_type": "Statustyp", "u2_core.statuses": "Status", "u2_core.structure": "Struktur", "u2_core.styling_links": "Gestaltunslinks", "u2_core.subscribed_groups": "Abonnierte Gruppen", "u2_core.subscribed_users": "Abonnierte Benutzer", "u2_core.subscriptions": "Subskriptions", "u2_core.subscriptions_overview": "Subskriptions Übersicht", "u2_core.success": "Erfolgreich", "u2_core.success_saved": "Gespeichert.", "u2_core.support": "Support", "u2_core.system_message": "Systemmeldung", "u2_core.system_message.plural": "Systemmeldungen", "u2_core.system_settings": "System Einstellungen", "u2_core.tags": "Tags", "u2_core.tax_advisor": "Steuerberater", "u2_core.tax_assessment": "Steuerbescheid", "u2_core.tax_number": "Steuern<PERSON>mer", "u2_core.telephone": "Telefon", "u2_core.the_given_user_is_requesting_permissions_to_the_given_file": "Benutzer %user_name% fordert Berechtigungen für die Datei %file_name%.", "u2_core.the_name_of_this_file_is_restricted": "(Der Dateiname ist beschränkt)", "u2.no_attachments": "<PERSON><PERSON>", "u2_core.there_are_no_elements_assigned": "Es existieren keine Zuordnugen", "u2_core.there_are_no_elements_to_be_assigned": "Es existiert kein Element zum Zuordnen", "u2_core.this_file_is_not_linked_to_anything_at_the_moment": "Diese Datei ist derzeit nicht verknüpft.", "u2.title": "Titel", "u2_core.to_date": "Zum Datum", "u2_core.to_grant_permissions_go_to_the_edit_page_forgiven_file": "Um Berechtigungen zu erteilen, gehen Sie zur Bearbeitungsseite von %file_name%:", "u2_core.tools": "Tools", "u2_core.type": "<PERSON><PERSON>", "u2_core.types": "Typen", "u2_core.u2_documentation": "U² Dokumentation", "u2_core.unlink_file_with_given_name_from_entity.confirmation": "Sind Si<PERSON> sicher, dass sie die „%file_name%“ <PERSON><PERSON> von diesem Eintrag entfernen möchten?", "u2_core.up_next": "Nächste Aufgaben", "u2_core.update_assignment.success": "Zuordnung gespeichert.", "u2_core.updated": "<PERSON>ktual<PERSON><PERSON>", "u2_core.updated_at": "Aktualisiert am", "u2_core.updated_by": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "u2_core.upload": "Hochladen", "u2_core.upload_a_new_file": "<PERSON><PERSON>laden einer neuen Datei", "u2_core.upload_file.drop_a_file_here_to_attach_it": "<PERSON>i hier ablegen um sie anzuhängen", "u2_core.upload_file.error": "Upload fehlgeschlagen.", "u2_core.uql": "UQL", "u2_core.use_default": "Standard verwenden", "u2_core.user": "<PERSON><PERSON><PERSON>", "u2_core.user_agent": "User Agent", "u2_core.user_email_has_changed": "Die E-Mail-Addresse Ihres U²-Benutzerkontos „%user_name%“ hat sich geändert.", "u2_core.user_email_listener.email_has_changed.mail_body.title": "Ihre E-Mail-Addresse hat sich geändert", "u2_core.user_group_list": "Benutzergruppen Übersicht", "u2_core.user_group_name": "Name der Benutzergruppe", "u2_core.user_group_with_given_group_name": "Benutzergruppe „%group_name%“", "u2_core.user_groups": "Benutzergruppen", "u2_core.user_information": "Benutzerinformation", "u2_core.user_interface": "Benutzeroberfläche", "u2_core.user_list": "Benutzerübersicht", "u2_core.user_profile": "Benutzerprofil", "u2_core.user_property_has_changed.how_to_react": "\n          Falls Sie diese Änderung nicht selbst vorgenommen haben, besteht möglicherweise ein Sicherheitsproblem mit Ihrem Benutzerkonto.\n          Bitte aktualisieren Sie in diesem Fall ihre Benutzerkontodaten und kontaktieren Sie Ihren Administrator für weitere Informationen.\n          Falls Sie selbst diese Änderung vorgenommen haben, können Sie diese Benachrichtigung ignorieren.\n        ", "u2_core.user_settings": "Benutzereinstellungen", "u2_core.username": "<PERSON><PERSON><PERSON><PERSON>", "u2_core.users": "<PERSON><PERSON><PERSON>", "u2_core.valid_from": "<PERSON><PERSON><PERSON><PERSON> ab", "u2_core.valid_to": "Gültig bis", "u2_core.verified": "Überprüft", "u2_core.view": "<PERSON><PERSON><PERSON>", "u2_core.view_profile": "<PERSON><PERSON> anzeigen", "u2_core.warning": "<PERSON><PERSON><PERSON>", "u2_core.warning_authentication_success": "\n          Es gab eine fehlgeschlagenen Anmeldeversuch.<br>\n          Der Versuch wurde angemeldet:<br>\n          %failed_login_times_message%<br>\n          <strong>Bitte informieren Sie Ihren Administrator, wenn Sie nicht der betroffene Benutzer sind.</strong>\n          |\n          Es wurden %count% fehlgeschlagene Anmeldeversuche.<br>\n          Die zuletzt aufgezeichnete Anmeldeversuche waren:<br>\n          %failed_login_times_message%<br>\n          <strong>Bitte informieren Sie Ihren Administrator, wenn Sie nicht der betroffene Benutzer sind.</strong>\n        ", "u2_core.watch.unwatch": "beobachtung entfernen", "u2_core.watch.watch": "beobachten", "u2_core.watch.watchers": "Beobachter", "u2_core.welcome_to_u2": "Willkommen in U²", "u2_core.widget.does_not_exist": "Das Widget „%widget_name%“ existiert nicht.", "u2_core.widget.insufficient_permissions": "Unzureichende Berechtigungen zum Anzeigen von Widget „%widget_name%“.", "u2_core.widget.invalid_argument_value": "Ungültiger Wert für Eigentschaft „%property%“. Das Widget „%widget_name%“ kann nicht dargestellt werden.", "u2_core.widget.malformed_configuration": "Widget-Konfiguration konnte nicht analysiert werden.", "u2_core.widget.missing_property": "Fehlender Eigentschaft „%property%“. Das Widget „%widget_name%“ kann nicht dargestellt werden.", "u2_core.widget.not_support": "Widget „%widget_name%“ wird nicht unterstützt.", "u2_core.widget.unknown_error_occurred_while_rendering_widget": "Während des Renderns von Widget „%widget_name%“ ist ein unbekannter Fehler ist aufgetreten. Für weitere Informationen sehen Sie sich die Log-Datei an.", "u2_core.workflow": "Workflow", "u2_core.workflow.actions": "Aktionen", "u2_core.workflow.add_action": "Aktion hinzufügen", "u2_core.workflow.add_check": "Checklistenelement hinzufügen", "u2_core.workflow.add_condition": "Bedingung hinzufügen", "u2_core.workflow.add_condition_to_transition.success": "Bedingung hinzugefügt.", "u2_core.workflow.add_new_transition": "Neue Transition hinzufügen", "u2_core.workflow.add_new_workflow": "Neuer Workflow hinzufügen", "u2_core.workflow.add_transition": "Transition hinzufügen", "u2_core.workflow.adding_condition_to_transition": "Aktion zu Transition hinzufügen", "u2_core.workflow.change_status_to_given_status": "Status ändern zu %destinationStatus%", "u2_core.workflow.change_workflow.success": "Workflow erfolgreich geändert.", "u2_core.workflow.check": "Checklistenelement", "u2_core.workflow.checklist": "Checkliste", "u2_core.workflow.conditions": "Workflow Konditionen", "u2_core.workflow.confirm_status_transition": "Status der Transition bestätigen", "u2_core.workflow.current_status": "Aktueller Status", "u2_core.workflow.current_workflow": "Aktueller Workflow", "u2_core.workflow.delete_check": "Checklistenelement löschen (%check%)", "u2_core.workflow.delete_check.confirmation": "Es wird das Checklistenelement sowie alle damit verbundenen Verläufe dauerhaft gelöscht. Alternativ können Sie das Element deaktivieren, um es nicht mehr in neuen Checklisten anzuzeigen, während die vorhandenen Verläufe\n          beibehalten werden.\n        ", "u2_core.workflow.delete_transition.confirmation": "Sind Si<PERSON> sicher, dass diese Transition gelöscht werden soll?", "u2_core.workflow.delete_transition_with_given_name": "Transition (%transition_name%) löschen", "u2_core.workflow.delete_workflow": "Workflow löschen", "u2_core.workflow.delete_workflow.confirmation": "Sind Si<PERSON> sicher, dass dieser Workflow gelöscht werden soll?", "u2_core.workflow.destination_status": "Ziel Status", "u2_core.workflow.destination_workflow": "Ausrichtung des Workflows", "u2_core.workflow.edit_assignment": "<PERSON><PERSON>rd<PERSON>ng bearbeiten", "u2_core.workflow.edit_check": "Checklistenelement bearbeiten (%check%)", "u2_core.workflow.edit_transition_with_given_name": "Transition (%transition_name%) bearbeiten", "u2_core.workflow.edit_workflow": "Workflow bearbeiten", "u2_core.workflow.edit_workflow_assignment_with_given_name": "Workflow Zuordnung „%workflow_name%“ bearbeiten", "u2_core.workflow.new_check": "Neues Checklistenelement", "u2_core.workflow.new_transition": "Neue Transition", "u2_core.workflow.new_workflow": "Neuer Workflow", "u2_core.workflow.no_actions_attached_to_transition": "<PERSON>s gibt keine zugeordnete Aktion zu dieser Transition.", "u2_core.workflow.no_associated_statuses": "<PERSON><PERSON> zu<PERSON>örig<PERSON> Status", "u2_core.workflow.no_conditions_attached_to_transition": "Es gibt keine zugeordnete Bedingung zu dieser Transition", "u2_core.workflow.no_destination_status_available": "<PERSON><PERSON>", "u2_core.workflow.no_status_has_been_set": "<PERSON>in <PERSON> wurde gesetzt", "u2_core.workflow.no_transitions_available": "Keine Transitionen verfügbar", "u2_core.workflow.origin_status": "Ursprung Status", "u2_core.workflow.remap_missing_statuses": "Status fehlen für neue Zuordnung", "u2_core.workflow.remove_action.confirmation": "Sind Si<PERSON> sicher, dass diese Aktion gelöscht werden soll?", "u2_core.workflow.remove_action.success": "Aktion gelöscht.", "u2_core.workflow.remove_action_with_given_name": "Aktion (%action_readable_name%) löschen", "u2_core.workflow.remove_condition.confirmation": "Sind Si<PERSON> sicher, dass diese Bedingung gelöscht werden soll?", "u2_core.workflow.remove_condition.success": "Bedingung gelöscht.", "u2_core.workflow.remove_condition_with_given_name": "Bedingung löschen (%condition_readable_name%)", "u2_core.workflow.remove_transition.success": "Transition gelöscht.", "u2_core.workflow.select_a_status": "Status auswählen", "u2_core.workflow.select_destination_status_for_assigned_objects": "Die folgenden Status haben zugeordnete Elemente, deshal<PERSON> können sie nicht automatisch neu zugeordnet werden. Wählen Sie bitte ein Ziel für Status dieser Elemente.", "u2_core.workflow.status_history_for_given_entity_name": "Statusverlauf für „%entity_name%“", "u2_core.workflow.there_have_been_no_status_changes": "<PERSON>s wurde kein Status geändert.", "u2_core.workflow.transition_status.success_given_amount_of_records_transitioned": "%count% Eintrag wurde erfolgreich übergeleitet. |%count% wurden erfolgreich übergeleitet.", "u2_core.workflow.transitions": "Transitionen", "u2_core.workflow.warning_no_destination_status_available": "Einträge mit diesen Status können nicht übergeleitet werden.", "u2_core.workflow_assignment": "Workflow Zuordnung", "u2_core.workflow_assignments": "Workflow Zuordnung", "u2_core.workflow_list": "Workflow Liste", "u2_core.workflows": "Workflows", "u2_core.write": "Schreiben", "u2_core.you_are_not_authorized_to_import_any_data": "Sie sind nicht berechtigt Daten zu importieren.", "u2_core.your_title_text": "Textfeld für Ihren Titel", "u2.datasheets.cannot_initialize_with_missing_exchange_rate": "Die Standardwerte konnten nicht wiederhergestellt werden: Es konnten kein Wechselkurse „%local_currency_iso%“ zu „%group_currency_iso%“ für den Zeitraum „%period%“ gefunden werden. Bitte fügen Sie diese hinzu und laden Sie\n          die Seite neu.\n        ", "u2.datasheets.cannot_initialize_with_missing_previous_period": "Standardeinstellungen können nicht initialisiert werden: Periode „%period_name%“ hat keine vorherige Period. Fügen Sie dem Zeitraum eine vorherige Periode hinzu und besuchen Sie die Seite erneut.", "u2.datasheets.delete_given_entity_type_with_given_name": "Löschen %entity_type_name% (%entity_name%)", "u2.datasheets.delete_given_entity_type_with_given_name.confirmation": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass dieser %entity_type_name% (%entity_name%) gel<PERSON>scht werden soll?", "u2.datasheets.edit_given_entity_type_with_given_name": "Bearbeiten %entity_type_name% (%entity_name%)", "u2.datasheets.exchange_method": "Wechselkursmethod", "u2.datasheets.exchange_method.average": "Durchschnitt", "u2.datasheets.exchange_method.current": "Stichtag", "u2.datasheets.exchange_method.previous_average": "Durchschnittlicher Wechselkurs der Vorperiode", "u2.datasheets.exchange_method.previous_current": "Aktueller Wechselkurs der Vorperiode", "u2.datasheets.formula": "Formel", "u2.datasheets.formula.formula_string.help": "\n          Item Reference: {%item_refId%} <br> Reference to an item value from previous Period: {P%item_refId%} <br> All maths operators are allowed <br> Also if -> then is allowed: ItemXXX === 1 ? %true_statement% : %false_statement%", "u2.datasheets.group": "Gruppe", "u2.datasheets.item.plural": "Items", "u2.datasheets.datasheet.plural": "Datasheets", "u2.datasheets.module_name": "Datasheets", "u2.datasheets.name": "Name", "u2.datasheets.select_unit_hierarchy": "Unit-Hierarchie auswählen", "u2.datasheets.status_monitor": "Datasheet Monitor", "u2.datasheets.status_monitor.plural": "Datasheet Monitor", "u2.datasheets.type": "<PERSON><PERSON>", "u2.datasheets.unit_view.form_is_disabled": "Das Formular ist deaktiviert und kann nicht gespeichert werden.", "u2_financial.base_to_group_exchange_rate": "Umrechnungskurs für Gruppenwert", "u2_financial.billing_type": "Abrechnungsart", "u2_financial.billing_type.plural": "Abrechnungsarten", "u2_financial.carry_forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_financial.carry_forward_financial_data.help": "<PERSON>zen Si<PERSON> diesen Merker, wenn die Financial Data im Rahmen der Datenübernahme in die Folgeperiode vorgetragen werden soll.", "u2_financial.collateralisation_type": "Sicherheitstyp", "u2_financial.collateralisation_type.plural": "Sicherheitstypen", "u2_financial.exchange_rate_shortened": "Umrechnungskurs", "u2_financial.group": "Gruppe", "u2_financial.group_parent_income_tax": "<PERSON><PERSON><PERSON><PERSON> (ESt.)", "u2_financial.group_parent_vat": "<PERSON><PERSON><PERSON><PERSON> (USt.)", "u2_financial.local": "<PERSON><PERSON>", "u2_financial.local_to_group_exchange_rate": "Lokaler- zu Gruppenwert Umrechnungsrate", "u2_financial.parent_legal_unit": "Muttergesellschaft", "u2.partner_unit": "Partner Unit", "u2_financial.partner_unit_country": "Partner Unit Land", "u2_financial.partner_unit_name": "Name Partner Unit", "u2_financial.pricing_method": "Verrechnungsmethode", "u2_financial.pricing_method.plural": "Verrechnungsmethoden", "u2_financial.show_hide_value_and_exchange_rates_in_group_and_local_currencies": "Wert und Umrechnungskurse für Gruppenwährung und lokaler Währung anzeigen/ausblenden", "u2_financial.tax_type": "Steuerart", "u2_financial.tax_type.plural": "Steuerarten", "u2_financial.transaction_type": "Transaktionstyp", "u2_financial.transaction_type.plural": "Transaktionstypen", "u2_financial.type": "<PERSON><PERSON>", "u2_financial.unit_country": "Unit Land", "u2_financial.unit_id": "Unit ID", "u2_igt.module_name": "Intra-Group Transactions", "u2_structureddocument.add_attachment": "<PERSON><PERSON>", "u2_structureddocument.add_section_after": "Abschnitt nach hinzufügen", "u2_structureddocument.add_section_before": "Abschnitt vor hinzufügen", "u2_structureddocument.add_subsection": "Unterabschnitt hinzufügen", "u2_structureddocument.after": "Nach", "u2_structureddocument.allow_edits": "Bearbeitung zulassen", "u2_structureddocument.also_delete_subsections": "Unterabschnitt auch löschen", "u2_structureddocument.attachments": "<PERSON><PERSON><PERSON><PERSON>", "u2_structureddocument.attachments_archive_is_empty": "Das Archiv enthält keine Anhänge.", "u2_structureddocument.before": "Vor", "u2_structureddocument.caution": "<PERSON><PERSON><PERSON>", "u2_structureddocument.configure_document_template": "Dokumentvorlage konfigurieren", "u2_structureddocument.configure_template_with_given_template_type": "Vorlage %document_template_type% konfigurieren", "u2_structureddocument.confirm_deletion": "Löschen bestätigen", "u2_structureddocument.content": "Inhalt", "u2_structureddocument.created": "<PERSON><PERSON><PERSON><PERSON>", "u2_structureddocument.delete": "Löschen", "u2_structureddocument.delete_section.error": "A<PERSON>ch<PERSON>tt kann nicht gelö<PERSON>t werden. <PERSON>te setzen <PERSON> sich mit dem Administrator in Verbindung.", "u2_structureddocument.delete_section.warning_attachments_will_also_be_removed": "Anhänge des gelöschten Abschnittes werden mitgelöscht.", "u2_structureddocument.do_not_require": "<PERSON><PERSON>", "u2_structureddocument.document_template": "Dokumentvorlage", "u2_structureddocument.document_template_list": "Dokumentvorlage Übersicht", "u2_structureddocument.document_templates": "Dokumentvorlagen", "u2_structureddocument.documents": "Dokumente", "u2_structureddocument.download_all_accessible_attachments.help": "Laden sie alle An<PERSON>änge herunter, auf welche Si<PERSON> Zugriff haben.", "u2_structureddocument.edit": "<PERSON><PERSON><PERSON>", "u2_structureddocument.exclude": "Ausschließen", "u2_structureddocument.import_new_document_template": "Eine neue Dokumentvorlage importieren", "u2_structureddocument.include": "Einschließen", "u2_structureddocument.included": "Enthalten", "u2_structureddocument.information": "Information", "u2_structureddocument.move": "Bewegen", "u2_structureddocument.move_given_section": "Schieben „%section_name%“", "u2_structureddocument.move_section.error": "A<PERSON>ch<PERSON>tt kann nicht verschoben werden. <PERSON>te setzen <PERSON> sich mit dem Administrator in Verbindung.", "u2_structureddocument.not_changed": "<PERSON><PERSON>", "u2_structureddocument.placement": "<PERSON><PERSON><PERSON><PERSON>", "u2_structureddocument.prevent_edits": "Bearbeitung verhindern", "u2_structureddocument.preview": "Vorschau", "u2_structureddocument.preview_given_document_template": "Vorschau „%document_template_name%“", "u2_structureddocument.require": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_structureddocument.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_structureddocument.section": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_structureddocument.select_a_section": "<PERSON><PERSON><PERSON><PERSON><PERSON> markieren", "u2_structureddocument.subsection_of": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "u2_structureddocument.templates": "Vorlagen", "u2_structureddocument.title": "Überschrift", "u2_structureddocument.type": "<PERSON><PERSON>", "u2_structureddocument.updated": "<PERSON>ktual<PERSON><PERSON>", "u2_table.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_table.all": "Alle", "u2_table.apply": "Übernehmen", "u2_table.apply_changes": "Änderungen übernehmen", "u2_table.available_fields": "<PERSON><PERSON><PERSON><PERSON>", "u2_table.available_functions": "Vorhandene Funktionen", "u2_table.basic": "<PERSON><PERSON><PERSON>", "u2_table.cancel": "Abbrechen", "u2_table.columns": "Spalten", "u2_table.default": "Voreinstellung", "u2_table.deselect_all": "Entmarkiere alle", "u2_table.deselect_all_visible": "Entmarkiere alle sichtbaren Einträge", "u2_table.false": "<PERSON><PERSON><PERSON>", "u2_table.filters": "Filter", "u2_table.next_page": "Nächste Seite", "u2_table.no_results_that_match_search": "<PERSON><PERSON>äge gefunden", "u2_table.none": "<PERSON><PERSON>", "u2_table.previous_page": "Vorherige Seite", "u2_table.reset": "Z<PERSON>ücksetzen", "u2_table.search_results_empty.help": "Versuchen Sie Ihre Suchanfrage zu ändern oder erstellen Sie einen neuen Datensatz, um loszulegen", "u2.search.refine_search": "Es wurden mehr Einträge gefunden als angezeigt werden können. Versuchen Sie Ihre Suchanfrage zu verfeinern falls das gewünschte nicht dabei ist.", "u2_table.select": "<PERSON><PERSON><PERSON>", "u2_table.select_all_visible": "Markiere alle sichtbaren Einträge", "u2_table.show": "Zeige:", "u2_table.switch_to_advanced_filtering": "Umschalten auf die erweiterte UQL-Filterung", "u2_core.api_key.copy_warning": "<PERSON><PERSON><PERSON>, dass Si<PERSON> Ihren persönlichen Zugangs-Token jetzt kopieren, da <PERSON><PERSON> diesen in Zukunft nicht mehr sehen können.", "u2_table.switch_to_basic_filtering": "Umschalten auf die einfache Text-Filterung", "u2_table.table_columns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_table.true": "<PERSON><PERSON><PERSON>", "u2_table.uql_filtered_table.filter_too_complex_to_display_in_basic_mode": "Der aktuelle Filter ist zu komplex für die Anzeige im einfachen Modus. Bitte den Filter zurücksetzen oder ändern.", "u2_table.uql_help": "UQL Hilfe", "u2_tam.accrued": "Entstanden", "u2_tam.accrued_boy": "Entstanden Jahresbeginn", "u2_tam.accrued_eoy": "Entstanden Jahresende", "u2_tam.addition": "Zuführung (+)", "u2_tam.additions": "Zuführungen (+)", "u2_tam.advice_type": "Art der Beratung", "u2_tam.advice_type.plural": "Typ der Beratung", "u2_tam.amount": "Betrag", "u2_tam.amounts": "Beträge", "u2_tam.base": "<PERSON><PERSON>", "u2_tam.beginning_of_year": "Jahresbeginn", "u2_tam.beginning_of_year.short": "Jahresbeginn", "u2_tam.cash_income_tax": "Zahlungswirksame Einkommensteuer", "u2_tam.compensation_of_tax_credits_accrued_as_dta": "Ausgleich von steuerlichen Gutschriften entstanden als DTA", "u2_tam.compensation_of_tax_credits_not_accrued_as_dta": "Ausgleich von steuerlichen Gutschriften nicht entstanden als DTA", "u2_tam.compensation_of_tax_losses_accrued_as_dta": "Ausgleich von steuerlichen Verluste entstanden als DTA", "u2_tam.compensation_of_tax_losses_not_accrued_as_dta": "Ausgleich von steuerlichen Verluste nicht entstanden als DTA", "u2_tam.compliance_fee": "Compliance Gebühren", "u2_tam.compliance_fee.short": "Compl. Geb.", "u2_tam.compliance_fees": "Compliance Gebühr", "u2_tam.compliance_tax_advisor": "Compliance Steuerberater", "u2_tam.compliance_tax_advisor.short": "Compl. Steuerberater", "u2_tam.consumption": "<PERSON><PERSON><PERSON><PERSON> (-)", "u2_tam.correction": "Korre<PERSON>ur (+/-)", "u2_tam.credit_type": "Typ der Gutschrift", "u2_tam.currency": "Währung", "u2_tam.details": "Details", "u2_tam.details_of_perm_differences": "Details zu perm. Unterschiede", "u2_tam.details_of_temp_differences": "Details zu temp. Unterschiede", "u2_tam.documentation_available": "Dokumentation vorhanden", "u2_tam.documentation_available.short": "Doku. vorh.", "u2_tam.documentation_required": "Do<PERSON><PERSON> er<PERSON>", "u2_tam.documentation_required.short": "Doku. erfdl.", "u2_tam.end_of_year": "J<PERSON><PERSON>ende", "u2_tam.etr": "ETR", "u2_tam.etr_ifrs": "ETR (IFRS)", "u2_tam.gross_risk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_tam.gross_risk_boy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_tam.gross_risk_eoy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_tam.identified_by_tax_admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von Steuerverwaltung (+/-)", "u2_tam.identified_by_tax_admin.short": "Identifiz<PERSON><PERSON> von St.-Verw.", "u2_tam.income_tax": "Einkommensteuer", "u2_tam.income_tax_planning": "Steuerplanung", "u2_tam.income_tax_planning.plural": "Steuerplanungen", "u2_tam.less": "Ab<PERSON>g (-)", "u2_tam.loss_carry_forward": "Verlustvortrag / Zinsschranke", "u2_tam.loss_carry_forward.plural": "Verlustvorträge / Zinsschranken", "u2_tam.loss_restriction": "Restriktion des steuerlichen Verlustes", "u2_tam.loss_restriction.plural": "Restriktionen der steuerlichen Verluste", "u2_tam.loss_type": "Typ des steuerlichen Verlustes", "u2_tam.loss_type.plural": "Typen der steuerlichen Verluste", "u2_tam.module_name": "Steuerbescheid Monitor", "u2_tam.module_name.short": "TAM", "u2_tam.p_l_effect_cy": "GuV Effekt laufendes Jahr", "u2_tam.parties": "Beteiligte", "u2_tam.partner_unit_name": "Partner Unit Name", "u2_tam.perm_differences": "Perm. Unterschiede", "u2_tam.permanent": "Permanent", "u2_tam.planning_period": "Planungsperiode", "u2_tam.potential_tax_liabilities": "Potentielle Steuerschuld", "u2_tam.potential_tax_liabilities.short": "Pot. Steu<PERSON>chuld", "u2_tam.pricing_method": "Verrechnungspreismethode", "u2_tam.profit_before_tax": "Gewinn vor Steuer", "u2_tam.profit_before_tax.short": "Gw. vor St.", "u2_tam.profit_before_tax_calculated": "Errechneter Gewinn vor Steuern", "u2_tam.profit_before_tax_calculated.short": "Errechneter Gw. vor St.", "u2_tam.reason": "<PERSON><PERSON><PERSON>", "u2_tam.restriction_reason": "Grund der Restriktion", "u2_tam.restriction_reason.plural": "Gründe der Restriktion", "u2_tam.restrictions": "Restriktionen", "u2_tam.risk_probability": "<PERSON><PERSON><PERSON>", "u2_tam.risk_probability.short": "Risiko Wkt.", "u2_tam.risk_type": "Risikotyp", "u2_tam.risk_type.plural": "Risikotypen", "u2_tam.special_advisory_fees": "Sonderberatungsgebühren", "u2_tam.special_advisory_fees.short": "Sonderberatungsgebühren", "u2_tam.specification": "Spezifizierung", "u2_tam.specification.plural": "Spezifizierungen", "u2_tam.tax_advisor": "Steuerberater", "u2_tam.tax_assessment_status": "Steuerfestsetzungsstatus", "u2_tam.tax_assessment_status.plural": "Steuerfestsetzungsstatus", "u2_tam.tax_audit_risk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_tam.tax_audit_risk.plural": "Steuerrisiken", "u2_tam.tax_base": "Steuerbemessungsgrundlage", "u2_tam.tax_consulting_fee": "Beratungsgebühr", "u2_tam.tax_consulting_fee.plural": "Beratungsgebühren", "u2_tam.tax_credit": "Vortragsfähige Steuer", "u2_tam.tax_credit.plural": "Vortragsfähige Steuern", "u2_tam.tax_credit_type": "Typ der Steuergutschrift", "u2_tam.tax_credit_type.plural": "Typen der Steuergutschriften", "u2_tam.tax_litigation": "Rechtsbehelf / Finanzgericht", "u2_tam.tax_litigation.plural": "Rechtsbehelfe / Finanzgerichte", "u2_tam.tax_month": "Steuer Monat", "u2_tam.tax_rate": "Steuersatz", "u2_tam.tax_rate.plural": "Steuersätze", "u2_tam.tax_relevant_restriction": "Steuerliche Restriktion", "u2_tam.tax_relevant_restriction.plural": "Steuerliche Restriktionen", "u2_tam.tax_type": "Steuerart", "u2_tam.taxable_income": "Steuerpflichtiges Einkommen", "u2_tam.taxation_month": "Steuer Monat", "u2_tam.taxation_year": "Steuerjahr", "u2_tam.temp_differences": "Te<PERSON><PERSON>", "u2_tam.temporary": "Temporär", "u2_tam.temporary_permanent_risk": "Temporäres/Permanentes Risiko", "u2_tam.transaction_type": "Transaktionstype", "u2_tam.transfer_pricing": "Konzerninterne Verrechnung", "u2_tam.transfer_pricing.plural": "Konzerninterne Verrechnungen", "u2_tam.type_of_advice": "Typ der Beratung", "u2_tam.type_of_risk": "Risikotyp", "u2_tam.valid_from": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "u2_tam.valid_to": "Gültig bis", "u2_tam.withholding_tax_payable": "Entrichtende Abzugssteuer", "u2_tam.withholding_tax_payable.short": "Entrichtende Abzugssteuer", "u2_tcm.assessment_type": "Steuerbescheid Typ", "u2_tcm.assessment_type.plural": "Steuerbescheid Typen", "u2_tcm.currency": "Währung", "u2_tcm.date_received": "Empfangsdatum", "u2_tcm.deadline_type": "<PERSON><PERSON>", "u2_tcm.declaration_type": "Steuererklärungstyp", "u2_tcm.declaration_type.plural": "Steuererklärungstypen", "u2_tcm.details": "Details", "u2_tcm.document_date": "Bescheiddatum", "u2.due_date": "Fälligkeit", "u2_tcm.filing_date": "Einreichungsdatum", "u2_tcm.module_name": "Tax Compliance Monitor", "u2_tcm.module_name.short": "TCM", "u2_tcm.other_deadline": "<PERSON><PERSON><PERSON>", "u2_tcm.other_deadline.plural": "Sonstige Fristsachen", "u2_tcm.penalty": "Sanktion", "u2_tcm.penalty_for_late_filing": "Sanktion verspäteter Einreichung", "u2_tcm.tax_assessment_monitor": "Bescheidprüfung", "u2_tcm.tax_assessment_monitor.plural": "Bescheidprüfungen", "u2_tcm.tax_authority_audit_objection": "Anfrage Finanzverwaltung", "u2_tcm.tax_authority_audit_objection.plural": "Anfragen Finanzverwaltung", "u2_tcm.tax_filing_monitor": "Erklärung / Anmeldung", "u2_tcm.tax_filing_monitor.plural": "Erklärungen / Anmeldungen", "u2_tcm.tax_month": "Steuer Monat", "u2_tcm.tax_type": "Steuerart", "u2_tcm.taxation_month": "Steuer Monat", "u2_tcm.taxation_year": "Steuerjahr", "u2_tcm.type_of_assessment": "Steuerbescheidtyp", "u2_tcm.type_of_declaration": "Steuererklärungstyp", "u2_tpm.accumulated_earnings_value": "Einbehaltener Gewinn", "u2_tpm.accumulated_earnings_value_base_value": "Einbehaltener Gewinn", "u2_tpm.accumulated_earnings_value_base_value.short": "Einbehaltener Gewinn", "u2_tpm.accumulated_earnings_value_group_value": "Einbehaltener Gewinn [Gruppenwert]", "u2_tpm.accumulated_earnings_value_group_value.short": "Einbehaltener Gewinn [Gruppenwert]", "u2_tpm.accumulated_earnings_value_local_value": "Einbehaltener Gewinn [lokaler Wert]", "u2_tpm.accumulated_earnings_value_local_value.short": "Einbehaltener Gewinn [lokaler Wert]", "u2_tpm.add_first_section": "<PERSON><PERSON><PERSON> Abschnitt hinzufügen", "u2_tpm.all_transaction_types": "Alle Transaktionstypen", "u2_tpm.arms_length": "Fremdvergleichsgrundsatz", "u2_tpm.arms_length.help": "Bestätigung, dass der Fremdvergleichsgrundsatz eingehalten wird", "u2_tpm.associated_units": "Assoziierte Units", "u2_tpm.base": "<PERSON><PERSON>", "u2_tpm.base_currency": "Währung", "u2_tpm.base_currency.short": "Währung", "u2_tpm.base_template": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.billing_type": "Abrechnungsart", "u2_tpm.business_activity": "Geschäftstätigkeit", "u2_tpm.business_activity.plural": "Geschäftstätigkeiten", "u2_tpm.carry_forward": "Vortrag", "u2_tpm.carry_forward.help": "<PERSON>äkchen setzen um die Transaktion zu markieren, die beim Datentransfer zur nächsten Periode übertragen wird.", "u2_tpm.classification": "Klassifikationen", "u2_tpm.contents": "Inhalte", "u2_tpm.contract_date": "Vertragsdatum", "u2_tpm.contract_name": "Vertragsname", "u2_tpm.contract_type": "Vertragsar<PERSON>", "u2_tpm.contracts.is_empty": "Keine relevanten Verträge gefunden.", "u2_tpm.country_by_country_report": "Country by Country Report", "u2_tpm.country_by_country_report.plural": "Country by Country Reports", "u2_tpm.country_founded": "Gründungsland", "u2_tpm.country_not_assigned": "Kein Land ist mit den Units dieses Berichts verknüpft.", "u2_tpm.currency": "Währung", "u2_tpm.data_to_show": "<PERSON><PERSON> zeigende Date<PERSON>", "u2_tpm.details": "Details", "u2_tpm.document_widget": "<PERSON><PERSON><PERSON> Widget", "u2_tpm.documentation": "Dokumentation", "u2_tpm.employees": "Beschäftigte", "u2_tpm.file": "<PERSON><PERSON>", "u2_tpm.file.help": "Um den Dateinamen zu verwenden, lassen <PERSON> dieses Fe<PERSON> leer.", "u2_tpm.financial_data": "Finan<PERSON>ten", "u2_tpm.financial_data.plural": "Finan<PERSON>ten", "u2_tpm.financial_data_records.is_empty": "<PERSON>s liegen keine Finanzdaten in diesem Dokument.", "u2_tpm.founded": "Gegründet", "u2_tpm.group_currency": "Gruppen Währung", "u2_tpm.group_currency.short": "Gruppenwährung", "u2_tpm.image": "Bild", "u2_tpm.image_width": "Bildbreite", "u2_tpm.image_width.help": "Ein Pixelwert (z.B.: 200px) oder „auto“ (Originalgröße). Die Höhe wird entsprechend der Breite proportional berechnet.", "u2_tpm.income_tax_accrued_value": "Die im Wirtschaftsjahr für dieses Wirtschaftsjahr gezahlten und zurückgestellten Ertragsteuern", "u2_tpm.income_tax_accrued_value_base_value": "Die im Wirtschaftsjahr für dieses Wirtschaftsjahr gezahlten und zurückgestellten Ertragsteuern", "u2_tpm.income_tax_accrued_value_base_value.short": "Im WJ für WJ gez./zurückg. Ertr.St.", "u2_tpm.income_tax_accrued_value_group_value": "<PERSON>ch zu zahlende Ertragsteuer (laufendes Jahr) [Gruppenwert]", "u2_tpm.income_tax_accrued_value_group_value.short": "<PERSON>ch zu zahlende Ertragsteuer (lfd. Jahr) [Gruppenwert]", "u2_tpm.income_tax_accrued_value_local_value": "<PERSON>ch zu zahlende Ertragsteuer (laufendes Jahr) [lokaler Wert]", "u2_tpm.income_tax_accrued_value_local_value.short": "<PERSON>ch zu zahlende Ertragsteuer (lfd. Jahr) [lokaler Wert]", "u2_tpm.income_tax_paid_value": "Im Wirtschaftsjahr gezahlte Ertragsteuern", "u2_tpm.income_tax_paid_value_base_value": "Im Wirtschaftsjahr gezahlte Ertragsteuern", "u2_tpm.income_tax_paid_value_base_value.short": "Im WJ gez. Ertr.St", "u2_tpm.income_tax_paid_value_group_value": "Gezahlte Ertrag<PERSON>uer (auf Kassenbasis) [Gruppenwert]", "u2_tpm.income_tax_paid_value_group_value.short": "Gezahlte Ertrag<PERSON>uer (auf Kassenbasis) [Gruppenwert]", "u2_tpm.income_tax_paid_value_local_value": "Gezahlte Ertrag<PERSON>uer (auf Kassenbasis) [lokaler Wert]", "u2_tpm.income_tax_paid_value_local_value.short": "Gezahlte Ertrag<PERSON>uer (auf Kassenbasis) [lokaler Wert]", "u2_tpm.key_figure": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.key_figures": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.legal_form": "Rechtsform", "u2_tpm.legal_name": "Juristische Bezeichnung", "u2_tpm.local_currency": "Lokale Währung", "u2_tpm.local_currency.short": "Lokale Währung", "u2_tpm.local_file": "Local File", "u2_tpm.local_file.plural": "Local Files", "u2_tpm.main_business_activity": "Hauptgeschäftstätigkeit", "u2_tpm.main_business_activity.is_empty": "Derzeit liegen keine Einträge zur Hauptgeschäftstätigkeit(en) vor.", "u2_tpm.main_business_activity.plural": "Hauptgeschäftstätigkeiten", "u2_tpm.main_contracts": "Hauptverträge", "u2_tpm.master_file": "Master File", "u2_tpm.master_file.plural": "Master Files", "u2_tpm.module_name": "Verrechnungspreis Monitor", "u2_tpm.module_name.short": "TPM", "u2_tpm.n_a": "n.a", "u2_tpm.name": "Name", "u2_tpm.not_required": "<PERSON><PERSON>", "u2_tpm.number_of_employees": "Zahl der Beschäftigten", "u2_tpm.number_of_units": "<PERSON><PERSON><PERSON> der Units", "u2_tpm.parties": "Beteiligte", "u2_tpm.partner_doc": "Partner Dok.", "u2_tpm.partner_unit.help": "Um eine Partner Unit auszuwählen, heben Sie die Markierung im Ankreuzfeld zu<PERSON>t auf und setzen Sie die Suche aus der Liste neu auf.", "u2_tpm.partner_unit_country": "Partner Unit Land", "u2_tpm.partner_unit_is_to_be_documented": "Partner Unit muss dokumentieren", "u2_tpm.partner_unit_name": "Partner Unit Name", "u2.partner_unit_ref_id": "Partner Unit Ref. ID", "u2_tpm.postal_address": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.profit_loss_before_income_tax_value": "Jahresergebnis vor Ertragsteuern", "u2_tpm.profit_loss_before_income_tax_value_base_value": "Jahresergebnis vor Ertragsteuern", "u2_tpm.profit_loss_before_income_tax_value_base_value.short": "Jahreserg. vor Ertr.St.", "u2_tpm.profit_loss_before_income_tax_value_group_value": "Vorsteuergewinn (-verlust) [Gruppenwert]", "u2_tpm.profit_loss_before_income_tax_value_group_value.short": "Vorsteuergewinn (-verlust) [Gruppenwert]", "u2_tpm.profit_loss_before_income_tax_value_local_value": "Vorsteuergewinn (-verlust) [lokaler Wert]", "u2_tpm.profit_loss_before_income_tax_value_local_value.short": "Vorsteuergewinn (-verlust) [lokaler Wert]", "u2_tpm.providing_entity": "Leistende Einheit", "u2_tpm.providing_entity_country": "Land der erbringenden Einheit", "u2_tpm.providing_entity_name": "Name der leistenden Einheit", "u2_tpm.receiving_entity": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.receiving_entity_country": "Land der Empfängereinheit", "u2_tpm.receiving_entity_name": "Name der Empfängereinheit", "u2_tpm.ref_id": "Ref Id", "u2_tpm.register_number": "Registernummer", "u2_tpm.registry_place": "Registergericht", "u2_tpm.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2_tpm.select_whether_the_partner_unit_needs_to_be_documented": "<PERSON><PERSON><PERSON><PERSON>, ob die Partner Unit dokumentiert werden muss", "u2_tpm.select_whether_the_unit_needs_to_be_documented": "<PERSON><PERSON><PERSON><PERSON>, ob die Unit dokumentiert werden muss", "u2_tpm.show_template_list": "Vorlagenliste zeigen", "u2_tpm.snapshot": "Units", "u2_tpm.stated_capital_value": "Eigenkapital", "u2_tpm.stated_capital_value_base_value": "Eigenkapital", "u2_tpm.stated_capital_value_base_value.short": "Eigenkapital", "u2_tpm.stated_capital_value_group_value": "Ausgewiesenes Kapital [Gruppenwert]", "u2_tpm.stated_capital_value_group_value.short": "Ausgewiesenes Kapital [Gruppenwert]", "u2_tpm.stated_capital_value_local_value": "Ausgewiesenes Kapital [lokaler Wert]", "u2_tpm.stated_capital_value_local_value.short": "Ausgewiesenes Kapital [lokaler Wert]", "u2_tpm.sub_type": "Subtyp", "u2_tpm.tangible_assets_value": "Materielle Vermögenswerte", "u2_tpm.tangible_assets_value_base_value": "Materielle Vermögenswerte", "u2_tpm.tangible_assets_value_base_value.short": "<PERSON><PERSON> Verm.-werte", "u2_tpm.tangible_assets_value_group_value": "Materielle Vermögenswerte (ohne flüssige Mittel) [Gruppenwert]", "u2_tpm.tangible_assets_value_group_value.short": "Materielle Vermögenswerte (ohne flüssige Mittel) [Gruppenwert]", "u2_tpm.tangible_assets_value_local_value": "Materielle Vermögenswerte (ohne flüssige Mittel) [lokaler Wert]", "u2_tpm.tangible_assets_value_local_value.short": "Materielle Vermögenswerte (ohne flüssige Mittel) [lokaler Wert]", "u2_tpm.tax_number": "Steuern<PERSON>mer", "u2_tpm.templates": "Vorlagen", "u2_tpm.text": "Text", "u2_tpm.the_name_of_this_file_is_restricted": "(Der Dateiname ist beschränkt)", "u2_tpm.this_document_has_no_content": "<PERSON><PERSON> Inhalte liegen in diesem Dokument vor.", "u2_tpm.total": "Gesamt", "u2_tpm.totalRevenue_value_local_value": "Summe Einkünfte [lokaler Wert]", "u2_tpm.totalRevenue_value_local_value.short": "Summe Einkünfte [lokaler Wert]", "u2_tpm.total_for_given_document_country": "Summe für %current_document_country%", "u2_tpm.total_revenue_related_value": "Umsatzerlöse und sonstige Erträge - Nahestehende Unternehmen", "u2_tpm.total_revenue_related_value_base_value": "Umsatzerlöse und sonstige Erträge - Nahestehende Unternehmen", "u2_tpm.total_revenue_related_value_base_value.short": "Umsatzerl./sonst Ertr. - Nahest.", "u2_tpm.total_revenue_related_value_group_value": "Summe Einkünfte mit nahstehenden Unternehmen [Gruppenwert]", "u2_tpm.total_revenue_related_value_group_value.short": "Summe Einkünfte mit nahstehenden U. [Gruppenwert]", "u2_tpm.total_revenue_related_value_local_value": "Summe Einkünfte mit nahstehenden Unternehmen [lokaler Wert]", "u2_tpm.total_revenue_related_value_local_value.short": "Summe Einkünfte mit nahstehenden U. [lokaler Wert]", "u2_tpm.total_revenue_unrelated_value": "Umsatzerlöse und sonstige Erträge - Fremde Unternehmen", "u2_tpm.total_revenue_unrelated_value_base_value": "Umsatzerlöse und sonstige Erträge - Fremde Unternehmen", "u2_tpm.total_revenue_unrelated_value_base_value.short": "Umsatzerl./sonst Ertr. - Fremde", "u2_tpm.total_revenue_unrelated_value_group_value": "Summe Einkünfte mit fremden Unternehmen [Gruppenwert]", "u2_tpm.total_revenue_unrelated_value_group_value.short": "Summe Einkünfte mit fremden U. [Gruppenwert]", "u2_tpm.total_revenue_unrelated_value_local_value": "Summe Einkünfte mit fremden Unternehmen [lokaler Wert]", "u2_tpm.total_revenue_unrelated_value_local_value.short": "Summe Einkünfte mit fremden U. [lokaler Wert]", "u2_tpm.total_revenue_value": "Umsatzerlöse und sonstige Erträge - Summe", "u2_tpm.total_revenue_value.help": "Summe der Gesamteinnahmen mit abhängigen und unabhängigen Parteien.", "u2_tpm.total_revenue_value_base_value": "Umsatzerlöse und sonstige Erträge - Summe", "u2_tpm.total_revenue_value_base_value.short": "Umsatzerl./sonst Ertr. - Summe", "u2_tpm.total_revenue_value_group_value": "Summe Einkünfte [Gruppenwert]", "u2_tpm.total_revenue_value_group_value.short": "Summe Einkünfte [Gruppenwert]", "u2_tpm.total_volume": "Summe", "u2_tpm.transaction": "Transaktion", "u2_tpm.transaction.plural": "Transaktionen", "u2_tpm.transaction_amount": "Betrag", "u2_tpm.transaction_currency": "Währung", "u2_tpm.transaction_details": "Transaktionsdetails", "u2_tpm.transaction_table": "Transaktionstabelle", "u2_tpm.transaction_type": "Transaktionstyp", "u2_tpm.transaction_units": "Transaktion-Units", "u2_tpm.transfer_pricing_method": "Verrechnungspreis-Methode", "u2_tpm.transfer_pricing_method.help": "Wählen Sie die entsprechende Verrechnungspreis-Methode aus der Liste.", "u2_tpm.type": "<PERSON><PERSON>", "u2_tpm.unit_country": "Unit Land", "u2_tpm.unit_doc": "Unit Dok.", "u2_tpm.unit_hierarchy": "Hierarchie", "u2_tpm.unit_is_to_be_documented": "Unit muss dokumentieren", "u2.unit_name": "Unit Name", "u2.unit_ref_id": "Unit Ref. ID", "u2_tpm.units": "Units", "u2_tpm.units.is_empty": "Es existieren keine Units, die zu diesem Dokument zugeordnet wurden.", "u2_tpm.units_widget.blocks": "<PERSON><PERSON><PERSON><PERSON>", "u2_tpm.units_widget.style": "Art", "u2_tpm.units_widget.table": "<PERSON><PERSON><PERSON>", "u2_tpm.widget.file.not_attached": "Die ausgewählte Datei ist diesem Abschnitt nicht mehr beigefügt.", "u2_tpm.widget.image.not_attached": "Das ausgewählte Bild ist diesem Abschnitt nicht mehr beigefügt.", "u2.open_menu": "<PERSON><PERSON>", "u2.dashboard.no_dashboard_exists": "<PERSON><PERSON> scheint, dass noch keine Dashboards existieren!", "u2.dashboard.create_a_dashboard": "Dashboard erstellen", "u2.ask_an_admin_to_create_one": "<PERSON>ten Sie einen Administrator, eines für Sie zu erstellen.", "u2.view_associated_tasks": "Zugeordnete Tasks anzeigen", "u2.ttl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "u2.api_key.plural": "Api-Keys", "u2_core.edit_api_key": "Api-Key bearbeiten", "u2_core.create_api_key": "Api-Key erstellen", "u2.infinite": "<PERSON><PERSON>", "u2.7_days": "7 Tage", "u2.30_days": "30 Tage", "u2.60_days": "60 Tage", "u2.90_days": "90 Tage", "u2.expand": "Ausklappen", "u2.file_inherited_permissions_help": "Über die Sicherheitseinstellung der Datei geerbt", "u2_core.download_file_name": "\"%file_name%\" herunterladen", "u2.number_more": "%number% mehr", "u2.toggle_attachment_info": "Anhangsinformationen umschalten", "u2.number_of_decimal_places": "Dezimalstellen", "u2.number_of_decimal_places_help": "Die Anzahl der Dezimalstellen, die für Beträge dieser Währung verwendet werden sollen", "u2_core.edit_file_name": "\"%file_name%\" bearbeiten", "u2.send_request": "<PERSON><PERSON><PERSON> senden", "u2.file_permissions_request_comment": "Bitte fügen Sie einen Kommentar hinzu, um zu beschreiben, welche Berechtigungen Sie anfordern und warum Sie diese benötigen:", "u2_core.permissions_request_for_given_file": "Sie fordern Berechtigungen für die Datei „%file_name%“. ", "u2.dashboard_link_not_found": "Dashboard nicht gefunden", "u2.dashboard.choose_another_dashboard": "Der Link für dieses Dashboard hat sich möglicherweise geändert oder ist nicht mehr vorhanden. Bitte wählen Sie eines der folgenden Dashboards aus.", "u2_core.current_permissions": "Aktuelle Berechtigungen", "u2.datasheets.plural": "Datasheets", "u2.role_is_inherited_from_role": "Diese Rolle wurde von einer anderen Rolle übernommen.", "u2.user_permissions.record_is_public": "Zugewiesene Benutzer haben keine Auswirkung, da „<PERSON>ür alle Benutzer sichtbar” aktiviert ist", "u2.group_permissions.record_is_public": "Zugewiesene Gruppen haben keine Auswirkung, da „<PERSON><PERSON>r alle Benutzer sichtbar“ aktiviert ist", "u2.datasheets.select_a_datasheet": "Datasheet auswählen", "u2.unit_view_parameter": "Unit View Parameter", "u2.group_view_parameter": "Group View Parameter", "u2.datasheets.datasheet_collection.select_datasheets": "Datasheets auswählen", "u2.select": "Auswählen", "u2.breakdown.close_breakdown": "Breakdown schließen", "u2.breakdown": "Breakdown", "u2.datasheets.datasheet_collection.plural": "Datasheet-Sammlungen", "u2.number_of_datasheets": "Anzahl der zugewiesenen Datasheets", "u2.datasheets.number_of_fields": "Anzahl der zugewiesenen Felder", "u2.datasheets.datasheet_collection.new": "Neue Datasheet-Sammlung", "Equity Type - Shares/Participation (real estate share deal)": "Equity Type - Shares/Participation (real estate share deal)", "Other Asset Transfer - Properties (real estate asset deal)": "Other Asset Transfer - Properties (real estate asset deal)", "Insurance": "Insurance", "Fees": "Fees", "Commission": "Commission", "Dividends": "Dividends", "Cost or Revenue": "Cost or Revenue", "Credit Default": "Credit Default", "Interest Rate": "Interest Rate", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "u2.choice.trace_id.plural": "Trace IDs", "u2.choice.trace_id.": "Trace ID", "u2.email.api_key.subject.about_to_expire": "Ihr API-Schlüssel „%apiKey%“ läuft bald ab", "u2.email.api_key.content.api_key_will_expire_at": "Der API-Schlüssel <strong>„%apiKeyName%“</strong> läuft am <strong>%apiKeyExpiresDate%</strong> ab.", "u2.datasheets.choose_a_datasheet": "Datasheet auswählen", "u2.api_key.regenerate_title": "API-Schlüssel neu generieren", "u2.email.api_key.content.if_needed_regenerate": "<PERSON><PERSON> dieses Token weiterhin benötigt wird, besuchen Sie Ihre<a href=\"%userSettingUrl%\">Benutzereinstellungen</a>, um ein Äquivalent zu generieren.", "u2.email.sincerely_universal_units": "<PERSON><PERSON>, <br> Ihr Universal Units Team", "u2.feature_toggles": "Experimentelle Features", "u2.password_min_length": "Mindestlänge des Passworts", "u2.datasheets.previous": "Vorheriges Datasheet", "u2.datasheets.next": "Nächstes Datasheet", "u2.switch_to_group_view": "Zur \"group\"", "u2.switch_to_unit_view": "Zur \"unit\"", "u2.dashboard.widget.filter_results.error_while_fetching_data": "Beim Abrufen der gespeicherten Filterergebnisse ist ein Fehler aufgetreten", "u2.preview": "Vorschau", "u2.size": "Größe", "u2.dashboard.widget.plural": "Dashboard-Widgets", "u2.dashboard.widget.not_configure_yet": "<PERSON>se Widget wurde noch nicht konfiguriert und kann daher nicht geladen werden.", "u2.columns": "Spalten", "u2.sort": "Sortierung", "u2.saved_filter.sort.column": "Spalte zum Sortieren", "u2.saved_filter.sort.direction": "Sortierrichtung", "u2.dashboard.widget.new": "Neues Dashboard-Widget", "u2.dashboard.widget.html": "HTML", "u2.dashboard.widget.filter_results": "Ergebnisse eines gespeicherten Filters", "u2.dashboard.widget.up_next": "<PERSON><PERSON><PERSON> Aufgaben", "u2.dashboard.widget.current_week_overview": "Aktuelle Wochenübersicht", "u2.widget.size.small": "<PERSON>", "u2.widget.size.medium": "<PERSON><PERSON><PERSON>", "u2.widget.size.large": "<PERSON><PERSON><PERSON>", "u2.dashboard_widget.new": "Widget hinzufügen", "u2.dashboard.edit_widgets": "Widgets bearbeiten", "u2.showing_filtered_items_out_of_total": "%filtered% von %total% Einträgen", "u2.showing_all_items": "Alle %total% Einträge werden angezeigt", "u2.drop_item_here": "<PERSON><PERSON><PERSON> hier <PERSON>gen", "u2.dashboard.widget.filter_statistics.error_while_fetching_data": "Beim Abrufen der Filtererstatistiken ist ein Fehler aufgetreten", "u2.dashboard.widget.filter_statistics": "Filterstatistike<PERSON>", "u2.label": "Label", "u2.color": "Farbe", "u2.chart_size": "Diagrammgröße", "u2.widget.filter_statistic.new_entry_disabled_reason": "Das Filterstatistik-Widget unterstützt maximal bis zu 10 gespeicherte Filter.", "u2.widget.filter_statistic.label_help_text": "Der gespeicherte Filtername wird verwendet, wenn keine Bezeichnung definiert wurde.", "u2.chart_type": "Diagramm Typ", "u2.widget.chart_type.pie": "<PERSON><PERSON>", "u2.widget.chart_type.doughnut": "Doughnut", "u2.show_percent": "Prozent anzeigen", "u2.undo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "u2.redo": "Wiederherstellen", "u2.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "u2.show_schema": "<PERSON><PERSON><PERSON> zeigen", "u2.rules": "Regeln", "u2.has_rules": "Hat <PERSON>n", "u2_core.import.invalid_headers": "Ein oder mehrere Header in der importierten Datei sind ungültig: %invalidHeaders%. Gültige Kopfzeilen sind: %validHeaders%", "u2.choose_your_own": "Wähle dein eigenes", "u2_core.edit_system_images": "Systembilder bearbeiten", "u2_core.system_images": "Systembilder", "u2_core.last_activity": "Letzte Aktivität", "u2.help_text": "Hilfstext", "u2.unit_or_partner_unit": "Entweder Unit oder Partner Unit", "u2_core.entities_cannot_be_created_in_closed_period": "Periode „%period_name%“ ist geschlossen. In einer geschlossenen Periode können keine neuen Datensätze angelegt werden.", "u2_core.entities_cannot_be_deleted_in_closed_period": "Periode „%period_name%“ ist geschlossen. Datensätze innerhalb einer geschlossenen Periode können nicht gelöscht werden.", "u2.unit_view": "Unit View", "u2.user.no_units_assigned_admin": "<PERSON>sen Sie hier einige zu, mit denen gearbeitet werden soll.", "u2.assign_units": "Units zuweisen", "u2.user_group.no_units_assigned_admin": "Weisen Sie der Benutzergruppe hier einige zu, mit der der Benutzer dieser Gruppe arbeiten sollen.", "u2.no_units": "Keine Units", "u2.no_users": "<PERSON><PERSON>", "u2.unit.no_users_assigned_description": "<PERSON><PERSON> sche<PERSON>, als wäre dieser Unit noch keine Benutzer zugeordnet worden.", "u2.contact_admin": "Kontaktieren Sie einen Administrator, um Änderungen für Sie vorzunehmen.", "u2.unit.no_users_assigned_admin": "<PERSON>sen Sie hier einige zu, um ihnen die Nutzung dieser Unit zu ermöglichen.", "u2.assign_users": "<PERSON><PERSON><PERSON>", "u2.no_user_groups": "<PERSON><PERSON>ergruppen", "u2.unit.no_user_groups_assigned_description": "<PERSON><PERSON> sche<PERSON>, als hätte diese Unit noch keine Benutzergruppen.", "u2.unit.no_user_groups_assigned_admin": "<PERSON>sen Si<PERSON> hier einige zu, um Benutzern der Gruppen die Arbeit mit dieser Unit zu ermöglichen.", "u2.assign_user_groups": "Benutzergruppen zuweisen", "u2.authorization.no_users_assigned_description": "Derzeit sind keine Benutzer zugewiesen. Fügen Sie hier Benutzer hinzu, um ihnen diese Berechtigung zu erteilen.", "u2.authorization_profile.no_user_groups_assigned_description": "Derzeit sind keine Benutzergruppen zugewiesen. Fügen Sie hier Benutzergruppen hinzu, um deren Mitgliedern die mit diesem Profil verbundenen Berechtigungen zu erteilen.", "u2.authorization.no_user_groups_assigned_description": "Derzeit sind keine Benutzergruppen zugewiesen. Fügen Sie hier Benutzergruppen hinzu, um deren Mitgliedern diese Berechtigung zu erteilen.", "u2.authorization_profile.no_users_assigned_description": "Derzeit sind keine Benutzer zugewiesen. Fügen Sie hier Benutzer hinzu, um ihnen die mit diesem Profil verbundenen Berechtigungen zu erteilen.", "u2.no_roles": "<PERSON><PERSON>", "u2.user.no_roles_assigned_description": "<PERSON><PERSON> sche<PERSON>, als hätte dieser Benutzer keine Rollen.", "u2.user.no_roles_assigned_admin": "<PERSON>sen Sie diesem Benutzer hier Rollen zu.", "u2.assign_roles": "<PERSON><PERSON>", "u2.user_group.no_roles_assigned_description": "<PERSON><PERSON> sche<PERSON>, als hätte diese Benutzergruppe keine Rollen.", "u2.user_group.no_roles_assigned_admin": "<PERSON>sen Sie dieser Benutzergruppe hier Rollen zu.", "u2.saved_filter_subscription.no_user_groups_assigned_description": "<PERSON><PERSON>, als hätte dieses Subskription noch keine Benutzergruppen. Fügen Sie hier einige hinzu, um deren Mitgliedern E-Mail-Updates zu senden.", "u2.saved_filter.no_users_assigned_description": "Dieser gespeicherte Filter hat noch keine Benutzer. Fügen Sie hier einige hinzu, um ihnen die Entdeckung dieses Filters zu ermöglichen.", "u2.add_users": "Benutzer hinzufügen", "u2.saved_filter_subscription.no_users_assigned_description": "<PERSON><PERSON> sche<PERSON>, als hätte dieses Subskription noch keine Benutzer. Fügen Sie hier einige hinzu, um ihnen Updates per E-Mail zu senden.", "u2.saved_filter.no_user_groups_assigned_description": "Dieser gespeicherte Filter hat noch keine Gruppen hinzugefügt. Fügen Sie hier einige hinzu, um deren Mitgliedern die Entdeckung dieses Filters zu ermöglichen.", "u2.dashboard.no_users_assigned_description": "Dieses Dashboard hat derzeit keine zugewiesenen Benutzer. <PERSON>sen Si<PERSON> hier einige zu, um ihnen Zugang zu gewähren.", "u2.dashboard.no_user_groups_assigned_description": "Dieses Dashboard hat derzeit keine zugewiesenen Benutzergruppen. <PERSON>sen <PERSON> hier einige zu, um deren Mitgliedern Zugang zu gewähren.", "u2.review.no_reviews": "Keine Reviews", "u2.review.task_not_reviewed": "Diese Aufgabe wurde noch nicht überprüft.", "u2.review.manual_review_disabled_info": "Manuelle Review ist für diesen Workflow nicht aktiviert. Reviews werden nur während Transitions mittels Workflow-Aktionen hinzugefügt.", "u2.no_user_group_permissions": "<PERSON>ine Gruppenberechtigungen", "u2.assign_user_group_permissions_admin": "<PERSON>sen Sie hier einigen Benutzergruppen Berechtigungen zu.", "u2.assign_user_group_permissions": "Gruppenberechtigungen zuweisen", "u2.no_user_permissions": "<PERSON><PERSON>echtigun<PERSON>", "u2.assign_user_permissions_admin": "<PERSON>sen Sie hier einigen Benutzern Berechtigungen zu.", "u2.assign_user_permissions": "Benutzerberechtigungen zuweisen", "u2.user.no_units_assigned_description": "<PERSON>s sieht so aus, als hätte dieser Benutzer noch keine Units.", "u2.no_attachments_description": "Hinzugefügte Anhänge werden hier angezeigt.", "u2.datasheets.no_user_group_permissions_description": "<PERSON><PERSON><PERSON> dieses Datasheet sind keine Gruppenberechtigungen festgelegt.", "u2.datasheets.datasheet_collection.no_user_group_permissions_description": "<PERSON><PERSON><PERSON> diese Datasheet-Sammlung sind keine Gruppenberechtigungen festgelegt.", "u2.document_template.no_user_group_permissions_description": "<PERSON><PERSON><PERSON> diese Vorlage sind keine Gruppenberechtigungen festgelegt.", "u2.document.no_user_group_permissions_description": "<PERSON><PERSON><PERSON> dieses Dokument sind keine Gruppenberechtigungen festgelegt.", "u2.datasheets.no_user_permissions_description": "<PERSON><PERSON><PERSON> dieses Datasheet sind keine Benutzerberechtigungen festgelegt.", "u2.datasheets.datasheet_collection.no_user_permissions_description": "<PERSON><PERSON><PERSON> diese Datasheet-Sammlungen sind keine Benutzerberechtigungen festgelegt.", "u2.document_template.no_user_permissions_description": "<PERSON><PERSON><PERSON> diese Vorlage sind keine Benutzerberechtigungen festgelegt.", "u2.document.no_user_permissions_description": "<PERSON><PERSON><PERSON> dieses Dokument sind keine Benutzerberechtigungen festgelegt.", "u2.user.no_user_groups_assigned_description": "<PERSON><PERSON> sche<PERSON>, als hätte dieser Benutzer keine Benutzergruppen.", "u2.user.no_user_groups_assigned_admin": "<PERSON>sen Si<PERSON> hier einige Gruppen zu.", "u2.user_group.no_units_assigned_description": "Es sieht so aus, als hätte diese Benutzergruppe noch keine Units.", "u2.user_group.no_users_assigned_description": "<PERSON><PERSON> sche<PERSON>, als hätte diese Benutzergruppe keine Benutzer.", "u2.user_group.no_users_assigned_admin": "<PERSON>sen Sie dieser Benutzergruppe hier Benutzer zu.", "u2.add_user_groups": "Benutzergruppen hinzufügen", "u2.task_checklist.no_checks_title": "<PERSON><PERSON>s", "u2.audit_log.no_changes_description": "Die Aktualisierungen werden hier angezeigt", "u2.new_record.save_first": "Diese Funktion ist nach der Erstellung verfügbar. Speichern Sie den Datensatz, um fortzufahren.", "u2_core.add_unit.help_select_a_hierarchy_and_save": "Um eine Unit hinzuzufügen, wählen Sie bitte eine Hierarchie und drücken Sie auf den Button Speichern.", "u2.no_authorisations": "<PERSON><PERSON>", "u2.user_group.no_authorisations_assigned_description": "Dieser Benutzergruppe sind keine Berechtigungen zugewiesen.", "u2.user.no_authorisations_assigned_description": "Diesem Benutzer sind keine Berechtigungen zugewiesen.", "u2.assign_authorisations": "Berechtigungen zuweisen", "u2.no_authorisations_assigned_admin": "<PERSON>sen Sie Berechtigungen zu, um sie hier zu sehen.", "u2.datasheets.item.types.number": "<PERSON><PERSON><PERSON>", "u2.results_per_page": "Ergebnisse pro Seite", "u2.unit.help": "Um eine Unit auszuwählen, heben Sie die Markierung im Ankreuzfeld zu<PERSON>t auf und setzen Sie die Suche aus der Liste neu auf.", "u2.previous_period_is": "Die vorherige Periode ist", "u2.period_has_no_previous_period": "Diese Periode hat keinen vorherige Periode.", "u2.task_checklist.show_checks_other_status": "Checks für andere Status anzeigen", "u2.datasheets.item_has_no_formula": "<PERSON><PERSON>em hat keine Formel.", "u2_core.show_calendar": "<PERSON><PERSON><PERSON> zeigen", "u2.target_route_is_current_route": "Sie befinden sich bereits auf dieser Seite", "u2.password_must_have_min_password_length": "Muss mindestens %min_password_length% Zeichen beinhalten.", "u2.collapse": "Einklappen", "u2.document.collapse_with_subsections": "Einklappen mit Unterabschnitten", "u2.document.expand_with_subsections": "Ausklappen mit Unterabschnitten", "u2.copy": "<PERSON><PERSON><PERSON>", "u2.paste": "Einfügen", "u2.document.expand_all_sections": "Alle Abschnitte ausklappen", "u2.document.collapse_all_sections": "Alle Abschnitte einklappen", "u2.datasheets.item_country_report": "Item-Bericht: Land", "u2.datasheets.choose_an_item_country_report": "Item-Bericht: Land auswählen", "u2.not_editable": "<PERSON>cht bearbeitbar", "u2.editable": "Bearbeitbar", "u2.document.section_controls": "Abschnittsaktionen", "u2.inspect.already_inspecting": "Sie schauen sich diese Feld bereits an.", "u2.select_a_field": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Feld", "u2.field_inspector": "Inspektor", "u2.field_inspector.show": "Inspektor öffnen", "u2.field_inspector.select_a_field": "<PERSON><PERSON>hlen Si<PERSON> ein Feld im Datasheet aus, um weitere Informationen zu diesem Feld anzuzeigen.", "u2.field_inspector.field_not_on_datasheet": "<PERSON><PERSON> Feld befindet sich nicht in diesem Datasheet.", "u2.inspect": "Anschauen", "u2.inspect.tooltip": "<PERSON><PERSON><PERSON> hier, um das Feld im Datasheet anzuzeigen.", "u2.paste.field_is_disabled": "Einfügen nicht möglich. Dieses Feld ist deaktiviert.", "u2.template": "Template", "u2.group": "Gruppe", "u2.more_than_one_unit_period_found_matching_this_configuration": "Dieser Datasheet-sammlung ist mehr als eine Aufgabe zugewiesen. <PERSON>te stellen <PERSON> sicher, dass nur eine Aufgabe zugewiesen ist, um Änderungen zu ermöglichen.", "u2.datasheets.datasheet_collection.name_restricted": "+%count% eingeschränkt", "u2.datasheets.datasheet_collection.name_restricted.tooltip": "Sie haben keine Berechtigung, diese Datasheet-sammlung anzuzeigen", "u2.task.assign_datasheet_collection": "Datasheet-samm<PERSON> zu<PERSON>", "u2.unrestricted": "<PERSON><PERSON>", "u2.unrestricted_to_any_datasheet_collection": "Diese Aufgabe unterliegt keinen Einschränkungen. Sie ist für die folgenden Sammlungen verfügbar:", "u2.task.restrict": "Beschränk<PERSON>", "u2.update_tasks": "Aufgabenkonfigurationskonflikt", "u2.show_on_list": "In List anzeigen", "u2.dashboard.widget.filter_results.the_following_invalid_columns": "Die Konfiguration enthält die folgenden ungültigen Spalten:", "u2.dashboard.widget.filter_results.remove_invalid_columns": "Ungültige Spalten entfernen", "u2.task.datasheet_collection.unable_to_add_collection": "Datasheet-sammlung \"%collectionName%\" konnte der Aufgabe \"%taskName%\" nicht hinzugefügt werden.", "u2.task.datasheet_collection.unable_to_remove_collection": "Datasheet-sammlung \"%collectionName%\" konnte nicht von der Aufgabe \"%taskName%\" entfernt werden.", "u2.datasheets.datasheet_collection": "Datasheet-Sammlung", "Undrawn credit facilities": "Undrawn credit facilities", "u2.datasheets.collection.plural": "Sammlungen", "u2.datasheets.sheet.plural": "Sheets", "u2.document_template.field_disabled_choose_file_first": "<PERSON><PERSON> müssen zu<PERSON>t eine Datei auswählen, bevor <PERSON> Name<PERSON> und die Beschreibung bearbeiten können.", "u2_document.cannot_perform_action_while_editing": "Diese Aktion kann nicht ausgeführt werden, während Abschnitte bearbeitet werden.", "u2.route.deprecated": "<PERSON>e wurden umgeleitet, da die angegebene URL nicht mehr existiert. Bitte aktualisieren Sie alle Lesezeichen, um stattdessen die aktuelle URL zu verwenden.", "u2.field_inspector.configure_field_inspector": "Inspektor konfigurieren", "u2.field_inspector.show_field_select": "Feldauswahl zeigen", "u2.field_inspector.show_colors": "<PERSON><PERSON> zeigen", "u2.watchers.view": "Beobachter zeigen", "u2_structureddocument.new_section_content": "Neuer Abschnittsinhalt", "u2_structureddocument.new_section": "<PERSON><PERSON><PERSON>", "u2.item.formula.element.previous_period_value": "Der Wert dieses Postens wird aus der Vorperiode übernommen.", "u2_structureddocument.add_section": "Abschnitt hinzufügen", "u2.inspect.insufficient_context": "Eine Navigation im aktuellen Kontext ist nicht möglich.", "u2.field_inspector.show_values": "Werte anzeigen", "u2.field.click_to_create": "<PERSON><PERSON><PERSON> Si<PERSON> hier, um dieses Feld zu erstellen", "u2.datasheets.unassigned_fields": "<PERSON>cht zugewiesene Felder", "u2.datasheets.missing_template": "<PERSON><PERSON><PERSON> dieses Datenblatt gibt es keine Vorlage. Da<PERSON> sind alle Felder als nicht zugeordnet gekennzeichnet.", "u2.datasheets.all_fields_assigned": "Alle Felder werden im Layout verwendet", "u2.datasheets.field_disabled": "Dieses Feld ist deaktiviert.", "u2.field.name_placeholder": "Search to view suggestions", "u2.datasheets.missing_fields": "<PERSON><PERSON><PERSON><PERSON>", "u2.no_address": "<PERSON>s wurde keine Adresse angegeben.", "u2.enter_address": "<PERSON><PERSON><PERSON>", "u2.remove_address": "<PERSON><PERSON><PERSON> en<PERSON>", "u2.datasheet.missing_field": "<PERSON><PERSON><PERSON><PERSON>", "u2.off_canvas_menu": "Off-Canvas-Menü", "u2.warning_unsaved_changes_will_be_lost": "Warnung: Nicht gespeicherte Änderungen gehen verloren.", "u2.global_search": "Globale Suche", "u2.global_search.description": "Globale Suche", "u2.transaction.transaction_volume": "<PERSON><PERSON>", "u2.transaction.underlying_contract": "Vertragliche Grundlage", "u2.transaction.unit_standard_taxation_applicable": "Unterliegt der Regelbesteuerung", "u2.transaction.partner_unit_standard_taxation_applicable": "Unterliegt der Regelbesteuerung", "u2.transaction.unit_standard_taxation_applicable.help": "Geschäftsvorfall unterliegt der Regelbesteuerung im betreffenden Steuerhoheitsgebiet", "u2.transaction.partner_unit_standard_taxation_applicable.help": "Geschäftsvorfall unterliegt der Regelbesteuerung im betreffenden Steuerhoheitsgebiet", "u2.transaction.transaction_volume.help": "z.B. 1000 Kg, 1000 Stück, nicht relevant", "u2.transaction.underlying_contract.help": "Hinweis zur vertraglichen Grundlage (z.B. Vertragsnummer)", "u2.no_matching_option_found": "Unbekannte ausgewählte Option", "u2.no_matching_option_found.help": "Entweder wurde die Resource nicht gefunden oder Sie haben nicht die erforderlichen Berechtigungen um diese zu sehen.", "u2.no_description": "Dieser Eintrag hat keine Beschreibung.", "u2.datasheet.missing_configuration": "Aufgrund fehlender Parameter kann die Vorlage nicht angezeigt werden.", "u2.security.roles.period_manager": "Periodenmanager", "u2.security.roles.unit_manager": "Unitmanager"}}}}