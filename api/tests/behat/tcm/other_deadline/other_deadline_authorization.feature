@clear-database
Feature: Other Deadlines Authorization
  As a user without the required authorisation
  I should have no access to any feature of Other Deadlines

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Other Deadline Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tcm_other_deadline | Other Deadline Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Deadline Type:
      | Name            |
      | Deadline Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TCM Other Deadline:
      | Unit   | Description      | Tax Year | Deadline Type   |
      | RefId1 | Other Deadline 1 | 2014     | Deadline Type 1 |
      | RefId2 | Other Deadline 2 | 2015     | Deadline Type 1 |
    And I am logged in

  Scenario: A User without rights to Other Deadlines tries to edit an Other Deadline
    When I go to "/tcm/other-deadline/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Other Deadlines tries to list Other Deadlines
    When I go to "/tcm/other-deadline"
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Other Deadlines tries to create a new Other Deadline record
    When I go to "/tcm/other-deadline/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Other Deadline new page
    Given the following Authorization:
      | Name                            | Item               | Rights |
      | TCM Other Deadlines READ Access | TCM_OTHER_DEADLINE | READ   |
    And I have the authorization "TCM Other Deadlines READ Access"
    When I go to "/tcm/other-deadline/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Other Deadlines
    Given the following Authorization:
      | Name                            | Item               | Rights |
      | TCM Other Deadlines READ Access | TCM_OTHER_DEADLINE | READ   |
    And I have the authorization "TCM Other Deadlines READ Access"
    When I go to "/tcm/other-deadline"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
