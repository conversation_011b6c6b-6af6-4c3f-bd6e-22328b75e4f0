@clear-database
Feature: Other Deadline - Create, Read, Update and Delete
  In order to manage Other Deadlines
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Other Deadlines

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Other Deadline Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tcm_other_deadline | Other Deadline Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Deadline Type:
      | Name            |
      | Deadline Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TCM Other Deadline:
      | Unit   | Description      | Tax Year | Deadline Type   |
      | RefId1 | Other Deadline 1 | 2014     | Deadline Type 1 |
      | RefId2 | Other Deadline 2 | 2015     | Deadline Type 1 |
    And the following Authorization:
      | Name                                | Item               | Rights                       |
      | TCM Other Deadlines COMPLETE Access | TCM_OTHER_DEADLINE | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "TCM Other Deadlines COMPLETE Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on "/tcm/other-deadline?q="
    Then I should see "No results found"

  Scenario: A user with the required authorisation visits the list page
    Given I am assigned to unit "RefId1"
    When I am on "/tcm/other-deadline?q="
    Then I should see the following table:
      | Unit Name    | Deadline Type   | Taxation Year |
      | Legal Unit 1 | Deadline Type 1 | 2014          |

  Scenario: A user with the required authorisation creates a new Other Deadline
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/other-deadline/new"
    When I fill in the "Other Deadline" form with:
      | Unit                | Legal Unit 1     |
      | Tax Year            | 2018             |
      | Description         | Other Deadline 3 |
      | Deadline Type       | Deadline Type 1  |
      | Late Filing Penalty | 1337             |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tcm/other-deadline/3/edit"

  Scenario: A user with the required authorisation updating an Other Deadline
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/other-deadline/1/edit"
    When I fill in the "Other Deadline" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should see a success message
    And I should be on "/tcm/other-deadline/1/edit"

  Scenario: A user with the required authorisation deleting an Other Deadline
    Given I am assigned to unit "RefId2"
    And I am on "/tcm/other-deadline/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tcm/other-deadline"
    And I should not see "Legal Unit 2"
