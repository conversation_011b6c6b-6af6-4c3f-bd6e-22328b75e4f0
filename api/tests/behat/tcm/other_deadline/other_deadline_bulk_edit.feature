@clear-database
Feature: Other Deadlines Bulk Edit
  In order to bulk edit Other Deadlines
  As a user with the required authorisation
  I should be able to perform bulk edit on Other Deadline records that are not completed

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Other Deadline Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tcm_other_deadline | Other Deadline Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Deadline Type:
      | Name            |
      | Deadline Type 1 |
      | Deadline Type 2 |
      | Deadline Type 3 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 2 | EUR      | Zambia  |
    And the following TCM Other Deadline:
      | Status | Unit   | Description      | Tax Year | Deadline Type   |
      | open   | RefId1 | Other Deadline 1 | 2014     | Deadline Type 1 |
      | open   | RefId2 | Other Deadline 2 | 2015     | Deadline Type 2 |
      | done   | RefId3 | Other Deadline 3 | 2015     | Deadline Type 3 |
    And the following Authorization:
      | Name                                | Item               | Rights               |
      | TCM Other Deadlines READ Access     | TCM_OTHER_DEADLINE | READ                 |
      | TCM Other Deadlines COMPLETE Access | TCM_OTHER_DEADLINE | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "TCM Other Deadlines READ Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/other-deadline?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Other Deadline is selected
    Given I have the authorization "TCM Other Deadlines COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am on "/tcm/other-deadline?q="
    When I check the checkbox on "Deadline Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tcm/other-deadline/1/edit"

  Scenario: A user can bulk edit Other Deadlines
    Given I have the authorization "TCM Other Deadlines COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/other-deadline?q="
    When I check the checkbox on "Deadline Type 1" table row for bulk action
    And I check the checkbox on "Deadline Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-other-deadline/edit?selection=1%2C2"
    When I enable the "Name" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Name | Test Name |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tcm/other-deadline"
    And I should see a success message

  Scenario: A user cannot bulk edit Other Deadlines because the entered value is invalid
    Given I have the authorization "TCM Other Deadlines COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/other-deadline?q="
    When I check the checkbox on "Deadline Type 1" table row for bulk action
    And I check the checkbox on "Deadline Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-other-deadline/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 1000 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "TCM Other Deadlines COMPLETE Access"
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                |
      | Description disabled | done     | Other Deadline Workflow |
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tcm/other-deadline?q="
    When I check the checkbox on "Deadline Type 1" table row for bulk action
    And I check the checkbox on "Deadline Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-other-deadline/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
