@clear-database
Feature: Tax Assessment Monitor
  In order to manage Tax Assessment Monitor records
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Tax Assessment Monitor records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                            | Initial Status | Transitions             |
      | Tax Assessment Monitor Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                 | Workflow                        |
      | tcm_tax_assessment_monitor | Tax Assessment Monitor Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Assessment Type:
      | Name              |
      | Assessment Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TCM Tax Assessment Monitor:
      | Unit   | Description              | Tax Year | Tax Type   | Assessment Type   |
      | RefId1 | Tax Assessment Monitor 1 | 2014     | Tax Type 1 | Assessment Type 1 |
      | RefId2 | Tax Assessment Monitor 2 | 2015     | Tax Type 1 | Assessment Type 1 |
    And the following Authorization:
      | Name                               | Item                       | Rights                       |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on "/tcm/tax-assessment-monitor"
    Then I should see "No results found"

  Scenario: A user with the required authorisation lists the Tax Assessment Monitor records
    Given I am assigned to unit "RefId1"
    When I am on "/tcm/tax-assessment-monitor"
    Then I should see the following table:
      | Unit Name    | Type of Assessment | Taxation Year |
      | Legal Unit 1 | Assessment Type 1  | 2014          |

  Scenario: A user with the required authorisation creates a new Tax Assessment Monitor record
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/tax-assessment-monitor/new"
    When I fill in the "Tax Assessment Monitor" form with:
      | Unit            | Legal Unit 1             |
      | Tax Year        | 2018                     |
      | Description     | Tax Assessment Monitor 3 |
      | Tax Type        | Tax Type 1               |
      | Assessment Type | Assessment Type 1        |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tcm/tax-assessment-monitor/3/edit"

  Scenario: A user with the required authorisation updating a Tax Assessment Monitor record
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/tax-assessment-monitor/1/edit"
    When I fill in the "Tax Assessment Monitor" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should see a success message
    And I should be on "/tcm/tax-assessment-monitor/1/edit"

  Scenario: A user with the required authorisation deleting a Tax Assessment Monitor record
    Given I am assigned to unit "RefId2"
    And I am on "/tcm/tax-assessment-monitor/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tcm/tax-assessment-monitor"
    And I should not see "Legal Unit 2"
