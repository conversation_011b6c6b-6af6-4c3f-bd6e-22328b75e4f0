@clear-database
Feature: Tax Assessment Monitor Navigation - Menu
  In order to manage Tax Assessment Monitor
  As a user allowed to the TCM Tax Assessment Monitor
  I should be able to navigate through the Tax Assessment Monitor pages

  Background:
    Given the following Authorization:
      | Name                               | Item                       | Rights               |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Assessment Monitor" in the menu under "TCM"
    Then I should be on "/tcm/tax-assessment-monitor"
