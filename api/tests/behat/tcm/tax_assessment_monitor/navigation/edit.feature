@clear-database
Feature: Tax Assessment Monitor Navigation - Edit
  In order to manage Tax Assessment Monitor
  As a user allowed to the TCM Tax Assessment Monitor
  I should be able to navigate through the Tax Assessment Monitor pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                            | Initial Status | Transitions |
      | Tax Assessment Monitor Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                 | Workflow                        |
      | tcm_tax_assessment_monitor | Tax Assessment Monitor Workflow |
    And the following Assessment Type:
      | Name                 |
      | Test Assessment Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TCM Tax Assessment Monitor:
      | Unit   | Description              | Tax Type   | Assessment Type      |
      | RefId1 | Tax Assessment Monitor 1 | Tax Type 1 | Test Assessment Type |
    And the following Authorization:
      | Name                               | Item                       | Rights               |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tcm/tax-assessment-monitor"
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tcm/tax-assessment-monitor/1/edit"
