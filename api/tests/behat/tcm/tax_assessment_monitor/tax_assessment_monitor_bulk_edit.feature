@clear-database
Feature: Tax Assessment Monitor
  In order to bulk edit Tax Assessment Monitor records
  As a User with the required authorisation
  I should be able to perform bulk edit on Tax Assessment Monitor records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                            | Initial Status | Transitions             |
      | Tax Assessment Monitor Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                 | Workflow                        |
      | tcm_tax_assessment_monitor | Tax Assessment Monitor Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Assessment Type:
      | Name                 |
      | Test Assessment Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following TCM Tax Assessment Monitor:
      | Status | Unit   | Description              | Tax Year | Tax Type   | Assessment Type      |
      | open   | RefId1 | Tax Assessment Monitor 1 | 2014     | Tax Type 1 | Test Assessment Type |
      | open   | RefId2 | Tax Assessment Monitor 2 | 2015     | Tax Type 2 | Test Assessment Type |
      | done   | RefId3 | Tax Assessment Monitor 3 | 2015     | Tax Type 3 | Test Assessment Type |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given the following Authorization:
      | Name                               | Item                       | Rights |
      | Tax Assessment Monitor Read Access | TCM_TAX_ASSESSMENT_MONITOR | READ   |
    And I have the authorization "Tax Assessment Monitor Read Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/tax-assessment-monitor"
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one tax assessment monitor is selected
    Given the following Authorization:
      | Name                               | Item                       | Rights               |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tcm/tax-assessment-monitor"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tcm/tax-assessment-monitor/1/edit"

  Scenario: A user can bulk edit tax assessment monitor
    Given the following Authorization:
      | Name                               | Item                       | Rights               |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/tax-assessment-monitor"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-tax-assessment-monitor/edit?selection=1%2C2"
    When I enable the "Name" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Name | Test Name |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tcm/tax-assessment-monitor"
    And I should see a success message

  Scenario: A user can not bulk edit tax assessment monitor because the entered values are invalid
    Given the following Authorization:
      | Name                               | Item                       | Rights               |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/tax-assessment-monitor"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-tax-assessment-monitor/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 1000 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given the following Authorization:
      | Name                               | Item                       | Rights               |
      | Tax Assessment Monitor Full Access | TCM_TAX_ASSESSMENT_MONITOR | READ, UPDATE, DELETE |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                        |
      | Description disabled | done     | Tax Assessment Monitor Workflow |
    And I have the authorization "Tax Assessment Monitor Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tcm/tax-assessment-monitor"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-tax-assessment-monitor/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
