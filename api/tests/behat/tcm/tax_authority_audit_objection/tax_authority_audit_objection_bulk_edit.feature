@clear-database
Feature: Tax Authority / Audit Objections Bulk Edit
  In order to bulk edit Tax Authority / Audit Objections
  As a User with the required authorisation
  I should be able to perform bulk edit on Tax Authority / Audit Objections records

  Background:
    Given the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | done               |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | Tax Authority Audit Objection Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                        | Workflow                               |
      | tcm_tax_authority_audit_objection | Tax Authority Audit Objection Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
      | RefId3 | Legal Unit 3 | EUR      | Germany |
    And the following TCM Tax Authority Audit Objection:
      | Status | Unit   | Description                     | Tax Year | Tax Type   |
      | open   | RefId1 | Tax Authority Audit Objection 1 | 2014     | Tax Type 1 |
      | open   | RefId2 | Tax Authority Audit Objection 2 | 2015     | Tax Type 2 |
      | done   | RefId3 | Tax Authority Audit Objection 3 | 2015     | Tax Type 3 |
    And the following Authorization:
      | Name                                              | Item                              | Rights               |
      | TCM Tax Authority Audit Objection READ Access     | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | READ                 |
      | TCM Tax Authority Audit Objection COMPLETE Access | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "TCM Tax Authority Audit Objection READ Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/tax-authority-audit-objection"
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Tax Authority Audit Objection is selected
    Given I have the authorization "TCM Tax Authority Audit Objection COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am on "/tcm/tax-authority-audit-objection"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tcm/tax-authority-audit-objection/1/edit"

  Scenario: A user can bulk edit Tax Authority Audit Objections
    Given I have the authorization "TCM Tax Authority Audit Objection COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/tax-authority-audit-objection"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-tax-authority-audit-objection/edit?selection=1%2C2"
    When I enable the "Name" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Name | Test Name |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tcm/tax-authority-audit-objection"
    And I should see a success message

  Scenario: A user can not bulk edit Tax Authority Audit Objections because the entered values are invalid
    Given I have the authorization "TCM Tax Authority Audit Objection COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tcm/tax-authority-audit-objection"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-tax-authority-audit-objection/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 1000 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "TCM Tax Authority Audit Objection COMPLETE Access"
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                               |
      | Description disabled | done     | Tax Authority Audit Objection Workflow |
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tcm/tax-authority-audit-objection"
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tcm-tax-authority-audit-objection/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
