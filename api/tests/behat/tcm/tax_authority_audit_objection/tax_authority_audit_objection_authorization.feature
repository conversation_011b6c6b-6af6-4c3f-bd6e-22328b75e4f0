@clear-database
Feature: Tax Authority / Audit Objections Authorization
  As a user with no authorization to the Tax Authority / Audit Objections
  I should have no access to any feature of Tax Authority / Audit Objections

  Background:
    Given the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | done               |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | Tax Authority Audit Objection Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                        | Workflow                               |
      | tcm_tax_authority_audit_objection | Tax Authority Audit Objection Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
    And the following TCM Tax Authority Audit Objection:
      | Unit   | Description                     | Tax Year | Tax Type   |
      | RefId1 | Tax Authority Audit Objection 1 | 2014     | Tax Type 1 |
      | RefId2 | Tax Authority Audit Objection 2 | 2015     | Tax Type 1 |
    And I am logged in

  Scenario: A user without the required authorisation tries to edit a Tax Authority Audit Objection record
    When I go to "/tcm/tax-authority-audit-objection/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A user without the required authorisation tries to list the Tax Authority Audit Objection records
    When I go to "/tcm/tax-authority-audit-objection"
    Then I should see "403 Access Denied"

  Scenario: A user without the required authorisation tries to create a Tax Authority Audit Objection record
    When I go to "/tcm/tax-authority-audit-objection/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation accessing the Tax Authority Audit Objection creation form
    Given the following Authorization:
      | Name                                          | Item                              | Rights |
      | TCM Tax Authority Audit Objection READ Access | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | READ   |
    And I have the authorization "TCM Tax Authority Audit Objection READ Access"
    When I go to "/tcm/tax-authority-audit-objection/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Authority Audit Objection records
    Given the following Authorization:
      | Name                                          | Item                              | Rights |
      | TCM Tax Authority Audit Objection READ Access | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | READ   |
    And I have the authorization "TCM Tax Authority Audit Objection READ Access"
    When I go to "/tcm/tax-authority-audit-objection"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
