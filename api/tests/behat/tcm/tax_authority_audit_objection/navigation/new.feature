@clear-database
Feature: Tax Authority / Audit Objection - New
  In order to create a new Tax Authority / Audit Objection
  As a user with the required authorisation
  I should be able to browse the Tax Authority / Audit Objection new page

  Background:
    Given the following Authorization:
      | Name                                            | Item                              | Rights       |
      | TCM Tax Authority Audit Objection UPDATE Access | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | Tax Authority Audit Objection Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                        | Workflow                               |
      | tcm_tax_authority_audit_objection | Tax Authority Audit Objection Workflow |
    And I have the authorization "TCM Tax Authority Audit Objection UPDATE Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tcm/tax-authority-audit-objection"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tcm/tax-authority-audit-objection/new"
