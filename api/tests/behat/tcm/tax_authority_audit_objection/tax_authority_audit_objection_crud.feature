@clear-database
Feature: Tax Authority / Audit Objection - Create, Read, Update and Delete
  In order to manage Tax Authority / Audit Objections
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Tax Authority / Audit Objections

  Background:
    Given the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | done               |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | Tax Authority Audit Objection Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                        | Workflow                               |
      | tcm_tax_authority_audit_objection | Tax Authority Audit Objection Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
    And the following TCM Tax Authority Audit Objection:
      | Unit   | Description                     | Tax Year | Tax Type   |
      | RefId1 | Tax Authority Audit Objection 1 | 2014     | Tax Type 1 |
      | RefId2 | Tax Authority Audit Objection 2 | 2015     | Tax Type 1 |
    And the following Authorization:
      | Name                                              | Item                              | Rights                       |
      | TCM Tax Authority Audit Objection COMPLETE Access | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "TCM Tax Authority Audit Objection COMPLETE Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on "/tcm/tax-authority-audit-objection"
    Then I should see "No results found"

  Scenario: A user with the required authorisation lists the Tax Authority Audit Objections records
    Given I am assigned to unit "RefId1"
    When I am on "/tcm/tax-authority-audit-objection"
    Then I should see the following table:
      | Unit Name    | Tax Type   | Taxation Year |
      | Legal Unit 1 | Tax Type 1 | 2014          |

  Scenario: A user with the required authorisation creates a new Tax Authority Audit Objection record
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/tax-authority-audit-objection/new"
    When I fill in the "Tax Authority Audit Objection" form with:
      | Unit        | Legal Unit 1                    |
      | Tax Year    | 2018                            |
      | Description | Tax Authority Audit Objection 3 |
      | Tax Type    | Tax Type 1                      |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tcm/tax-authority-audit-objection/3/edit"

  Scenario: A user with the required authorisation updating a Tax Authority Audit Objection record
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/tax-authority-audit-objection/1/edit"
    When I fill in the "Tax Authority Audit Objection" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should see a success message
    And I should be on "/tcm/tax-authority-audit-objection/1/edit"

  Scenario: A user with the required authorisation deleting a Tax Authority Audit Objection record
    Given I am assigned to unit "RefId2"
    And I am on "/tcm/tax-authority-audit-objection/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tcm/tax-authority-audit-objection"
    And I should not see "Legal Unit 2"
