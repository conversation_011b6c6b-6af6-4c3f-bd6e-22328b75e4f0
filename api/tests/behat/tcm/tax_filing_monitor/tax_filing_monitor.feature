@clear-database
Feature: Tax Filing Monitor
  In order to manage Tax Filing Monitor records
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Tax Filing Monitor records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                        | Initial Status | Transitions             |
      | Tax Filing Monitor Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tcm_tax_filing_monitor | Tax Filing Monitor Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Declaration Type:
      | Name                  |
      | Test Declaration Type |
    And the following Tax Type:
      | Name          |
      | Test Tax Type |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TCM Tax Filing Monitor:
      | Unit   | Description          | Tax Year | Tax Type      | Declaration Type      | Late Filing Penalty |
      | RefId1 | Tax Filing Monitor 1 | 2014     | Test Tax Type | Test Declaration Type | 20                  |
      | RefId2 | Tax Filing Monitor 2 | 2015     | Test Tax Type | Test Declaration Type | 20                  |
    And the following Authorization:
      | Name                           | Item                   | Rights                       |
      | Tax Filing Monitor Full Access | TCM_TAX_FILING_MONITOR | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Tax Filing Monitor Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I go to "/tcm/tax-filing-monitor"
    Then I should see "No results found"

  Scenario: A user with the required authorisation lists the Tax Filing Monitor records
    Given I am assigned to unit "RefId1"
    When I go to "/tcm/tax-filing-monitor"
    Then I should see the following table:
      | Unit Name    | Tax Type      | Type of Declaration   | Taxation Year |
      | Legal Unit 1 | Test Tax Type | Test Declaration Type | 2014          |

  Scenario: A user with the required authorisation creates a new Tax Filing Monitor record
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/tax-filing-monitor/new"
    When I fill in the "Tax Filing Monitor" form with:
      | Unit                | Legal Unit 1          |
      | Tax Year            | 2018                  |
      | Description         | Tax Filing Monitor 3  |
      | Tax Type            | Test Tax Type         |
      | Declaration Type    | Test Declaration Type |
      | Late Filing Penalty | 1337                  |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tcm/tax-filing-monitor/3/edit"

  Scenario: A user with the required authorisation updating a Tax Filing Monitor record
    Given I am assigned to unit "RefId1"
    And I am on "/tcm/tax-filing-monitor/1/edit"
    When I fill in the "Tax Filing Monitor" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should see a success message
    And I should be on "/tcm/tax-filing-monitor/1/edit"

  Scenario: A user with the required authorisation deleting a Tax Filing Monitor record
    Given I am assigned to unit "RefId2"
    And I am on "/tcm/tax-filing-monitor/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tcm/tax-filing-monitor"
    And I should not see "Legal Unit 2"
