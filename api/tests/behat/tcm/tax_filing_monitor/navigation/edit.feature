@clear-database
Feature: Tax Filing Monitor Navigation - Edit
  In order to manage Tax Filing Monitor
  As a user allowed to the TCM Tax Filing Monitor
  I should be able to navigate through the Tax Filing Monitor pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Tax Filing Monitor Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tcm_tax_filing_monitor | Tax Filing Monitor Workflow |
    And the following Declaration Type:
      | Name             |
      | Declaration Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TCM Tax Filing Monitor:
      | Unit   | Description          | Tax Type   | Declaration Type |
      | RefId1 | Tax Filing Monitor 1 | Tax Type 1 | Declaration Type |
    And the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Filing Monitor Full Access | TCM_TAX_FILING_MONITOR | READ, UPDATE, DELETE |
    And I have the authorization "Tax Filing Monitor Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tcm/tax-filing-monitor"
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tcm/tax-filing-monitor/1/edit"
