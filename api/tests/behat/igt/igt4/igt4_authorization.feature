@clear-database
Feature: IGT4 Authorisation
  As a user with no authorization to the Igt4 Module
  I should have no access to any feature of Igt4 Transactions

  Background:
    Given the following Status:
      | Type | Name |
      | OPEN | open |
    And the following Workflow:
      | Name                      | Initial Status | Transitions |
      | IGT4 Transaction Workflow | open           |             |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | igt_igt4_transaction | IGT4 Transaction Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following Country:
      | Iso3166Code | NameShort |
      | DE          | Germany   |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
    And the following IGT IGT4 Transaction:
      | Type      | Period      | Unit         | Partner Unit | Description               |
      | insurance | Period 2012 | Legal Unit 1 | Legal Unit 2 | Transaction 1 Description |
    And the following Authorization:
      | Name                    | Item                 | Rights |
      | Transaction Read Access | IGT_IGT4_TRANSACTION | READ   |

  Scenario: A user without any authorisation tries to access pages
    Given I am logged in
    When I go to "/igt/transaction/igt4?q="
    Then I should see "403 Access Denied"

    When I go to "/igt/transaction/igt4/1/edit"
    Then I should see "403 Access Denied"

    When I go to "/igt/transaction/igt4/new?type=insurance"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation can only read
    Given I am assigned to unit "RefId1"
    And I have the authorization "Transaction Read Access"
    And I am logged in

    When I go to "/igt/transaction/igt4/1/edit"
    Then I should not see "403 Access Denied"
    And I should see "Insurance"

    When I go to "/igt/transaction/igt4/new"
    Then I should see "403 Access Denied"

    When I go to "/igt/transaction/igt4/new?type=insurance"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation cannot delete transaction records
    Given I am assigned to unit "RefId1"
    And I have the authorization "Transaction Read Access"
    And I am logged in
    When I go to "/igt/transaction/igt4?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
