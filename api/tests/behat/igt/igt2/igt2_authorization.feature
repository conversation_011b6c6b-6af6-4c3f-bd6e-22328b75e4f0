@clear-database
Feature: IGT2 Authorisation
  As a user with no authorization to the Igt2 Module
  I should have no access to any feature of Igt2 Transactions

  Background:
    Given the following Status:
      | Type | Name |
      | OPEN | open |
    And the following Workflow:
      | Name                      | Initial Status | Transitions |
      | IGT2 Transaction Workflow | open           |             |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | igt_igt2_transaction | IGT2 Transaction Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following Country:
      | Iso3166Code | NameShort |
      | DE          | Germany   |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
    And the following IGT IGT2 Transaction:
      | Type                | Period      | Unit         | Partner Unit | Description               |
      | derivatives-futures | Period 2012 | Legal Unit 1 | Legal Unit 2 | Transaction 1 Description |
    And the following Authorization:
      | Name                    | Item                 | Rights |
      | Transaction Read Access | IGT_IGT2_TRANSACTION | READ   |

  Scenario: A user without any authorisation tries to access pages
    Given I am logged in
    When I go to "/igt/transaction/igt2?q="
    Then I should see "403 Access Denied"

    When I go to "/igt/transaction/igt2/1/edit"
    Then I should see "403 Access Denied"

    When I go to "/igt/transaction/igt2/new?type=derivatives-futures"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation can only read
    Given I am assigned to unit "RefId1"
    And I have the authorization "Transaction Read Access"
    And I am logged in

    When I go to "/igt/transaction/igt2/1/edit"
    Then I should not see "403 Access Denied"
    And I should see "Derivatives - Futures"

    When I go to "/igt/transaction/igt2/new"
    Then I should see "403 Access Denied"

    When I go to "/igt/transaction/igt2/new?type=derivatives-futures"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation cannot delete transaction records
    Given I am assigned to unit "RefId1"
    And I have the authorization "Transaction Read Access"
    And I am logged in
    When I go to "/igt/transaction/igt2?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
