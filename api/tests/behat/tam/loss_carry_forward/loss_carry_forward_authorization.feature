@clear-database
Feature: Loss Carry Forward Authorization
  As a user with no authorization to Loss Carry Forwards
  I should have no access to any feature of Loss Carry Forwards

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Loss Restriction:
      | Name               |
      | Loss Restriction 1 |
    And the following Loss Type:
      | Name        |
      | Loss Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                        | Initial Status | Transitions             |
      | Loss Carry Forward Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_loss_carry_forward | Loss Carry Forward Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Loss Carry Forward:
      | Unit   | Period      | Tax Type   | Description | Loss Type   | Restrictions       |
      | RefId1 | Period 2012 | Tax Type 1 | LCF 1       | Loss Type 1 | Loss Restriction 1 |
    And I am logged in

  Scenario: A User without rights to Loss Carry Forward tries to list the Loss Carry Forward records
    When I go to "/tam/loss-carry-forward?q="
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Loss Carry Forward tries to edit an Loss Carry Forward record
    When I go to "/tam/loss-carry-forward/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Loss Carry Forward tries to create a new Loss Carry Forward record
    When I go to "/tam/loss-carry-forward/new"
    Then I should see "403 Access Denied"

  Scenario: A User without read rights accessing the TAM Loss Carry Forward creation form
    Given the following Authorization:
      | Name                                | Item                   | Rights |
      | TAM Loss Carry Forwards READ Access | TAM_LOSS_CARRY_FORWARD | READ   |
    And I have the authorization "TAM Loss Carry Forwards READ Access"
    When I go to "/tam/loss-carry-forward/new"
    Then I should see "403 Access Denied"

  Scenario: A User without read rights tries to bulk delete Loss Carry Forward records
    Given the following Authorization:
      | Name                                | Item                   | Rights |
      | TAM Loss Carry Forwards READ Access | TAM_LOSS_CARRY_FORWARD | READ   |
    And I have the authorization "TAM Loss Carry Forwards READ Access"
    When I go to "/tam/loss-carry-forward?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
