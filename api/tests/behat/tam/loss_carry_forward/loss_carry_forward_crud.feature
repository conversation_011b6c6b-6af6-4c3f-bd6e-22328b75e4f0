@clear-database
Feature: Loss Carry Forward - Create, Read, Update and Delete
  In order to manage Loss Carry Forwards
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Loss Carry Forwards

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Loss Restriction:
      | Name               |
      | Loss Restriction 1 |
    And the following Loss Type:
      | Name        |
      | Loss Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Loss Carry Forward Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_loss_carry_forward | Loss Carry Forward Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
    And the following TAM Loss Carry Forward:
      | Unit   | Period      | Tax Type   | Description | Loss Type   | Restrictions       |
      | RefId1 | Period 2014 | Tax Type 1 | LCF 1       | Loss Type 1 | Loss Restriction 1 |
    And the following Authorization:
      | Name                                    | Item                   | Rights                       |
      | TAM Loss Carry Forwards COMPLETE Access | TAM_LOSS_CARRY_FORWARD | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "TAM Loss Carry Forwards COMPLETE Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I am on "/tam/loss-carry-forward?q="
    Then I should see "No results found"

  Scenario: A user list the records
    Given I am assigned to unit "RefId1"
    When I am on "/tam/loss-carry-forward?q="
    Then I should see the following table portion:
      | Unit Name    | Tax Type   |
      | Legal Unit 1 | Tax Type 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tam/loss-carry-forward?q=Period = 'Period 2014'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/loss-carry-forward/new"
    And the "Loss Carry Forward" form field "Period" should be "Period 2014"

  Scenario: Creating a new record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/loss-carry-forward/new"
    When I fill in the "Loss Carry Forward" form with:
      | Unit         | Legal Unit 1       |
      | Restrictions | Loss Restriction 1 |
      | Period       | Period 2014        |
      | Tax Type     | Tax Type 1         |
      | Loss Type    | Loss Type 1        |
      | Description  | LCF 2              |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tam/loss-carry-forward/2/edit"
    And I should see a success message

  Scenario: Updating a record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/loss-carry-forward/1/edit"
    When I fill in the "Loss Carry Forward" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should be on "/tam/loss-carry-forward/1/edit"
    And I should see a success message

  Scenario: Deleting a record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/loss-carry-forward/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tam/loss-carry-forward"
    And I should not see "Legal Unit 1"

  Scenario: Delete a record where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    When I go to "/tam/loss-carry-forward/1/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: Creating a record where the period is closed fails
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    And I am on "/tam/loss-carry-forward/new"
    When I fill in the "Loss Carry Forward" form with:
      | Unit         | Legal Unit 1       |
      | Restrictions | Loss Restriction 1 |
      | Period       | Period 2014        |
      | Tax Type     | Tax Type 1         |
      | Loss Type    | Loss Type 1        |
      | Description  | LCF 2              |
    And I press "Save"
    Then I should see an error message

  Scenario: Updating a record where the period is closed fails
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    When I go to "/tam/loss-carry-forward/1/edit"
    Then the "Save" button in "Page Controls" should be disabled
