@clear-database
Feature: Loss Carry Forwards Bulk Edit
  In order to bulk edit Loss Carry Forwards
  As a User with the required authorisation
  I should be able to perform bulk edit on Loss Carry Forward records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Loss Restriction:
      | Name               |
      | Loss Restriction 1 |
      | Loss Restriction 2 |
    And the following Loss Type:
      | Name        |
      | Loss Type 1 |
      | Loss Type 2 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                        | Initial Status | Transitions             |
      | Loss Carry Forward Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_loss_carry_forward | Loss Carry Forward Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following TAM Loss Carry Forward:
      | Status | Unit   | Period      | Tax Type   | Description | Loss Type   | Restrictions       |
      | open   | RefId1 | Period 2012 | Tax Type 1 | LCF 1       | Loss Type 1 | Loss Restriction 1 |
      | open   | RefId2 | Period 2013 | Tax Type 2 | LCF 2       | Loss Type 2 | Loss Restriction 2 |
      | done   | RefId3 | Period 2013 | Tax Type 3 | LCF 3       | Loss Type 2 | Loss Restriction 2 |
    And the following Authorization:
      | Name                                | Item                   | Rights               |
      | TAM Loss Carry Forwards READ Access | TAM_LOSS_CARRY_FORWARD | READ                 |
      | TAM Loss Carry Forwards FULL Access | TAM_LOSS_CARRY_FORWARD | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "TAM Loss Carry Forwards READ Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/loss-carry-forward?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Loss Carry Forward is selected
    Given I have the authorization "TAM Loss Carry Forwards FULL Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/loss-carry-forward?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/loss-carry-forward/1/edit"

  Scenario: A user can bulk edit Loss Carry Forward
    Given I have the authorization "TAM Loss Carry Forwards FULL Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/loss-carry-forward?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-loss-carry-forward/edit?selection=1%2C2"
    When I enable the "Addition" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Addition | 5 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/loss-carry-forward"
    And I should see a success message

  Scenario: A user can not bulk edit Loss Carry Forwards because the entered values are invalid
    Given I have the authorization "TAM Loss Carry Forwards FULL Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/loss-carry-forward?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-loss-carry-forward/edit?selection=1%2C2"
    When I enable the "Addition" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Addition | -5 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "TAM Loss Carry Forwards FULL Access"
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                    |
      | Description disabled | done     | Loss Carry Forward Workflow |
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tam/loss-carry-forward?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-loss-carry-forward/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
