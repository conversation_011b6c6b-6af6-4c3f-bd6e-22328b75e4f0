@clear-database
Feature: Loss Carry Forward - Menu
  In order to to browse the Loss Carry Forwards list page
  As a user with the required authorisation
  I should be able to navigate through the Loss Carry Forward menu entries

  Background:
    Given the following Authorization:
      | Name                                | Item                   | Rights |
      | TAM Loss Carry Forwards READ Access | TAM_LOSS_CARRY_FORWARD | READ   |
    And I have the authorization "TAM Loss Carry Forwards READ Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Loss Carry Forward" in the menu under "TAM"
    Then I should be on "/tam/loss-carry-forward"
