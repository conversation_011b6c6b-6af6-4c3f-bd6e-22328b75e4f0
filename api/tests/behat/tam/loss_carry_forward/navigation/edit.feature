@clear-database
Feature: Loss Carry Forward - Edit
  In order to edit a Loss Carry Forward
  As a user with the required authorisation
  I should be able to browse the Loss Carry Forward edit pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Loss Carry Forward Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_loss_carry_forward | Loss Carry Forward Workflow |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following Loss Restriction:
      | Name               |
      | Loss Restriction 1 |
    And the following Loss Type:
      | Name        |
      | Loss Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Period:
      | Name        |
      | Period 2015 |
    And the following TAM Loss Carry Forward:
      | Unit   | Period      | Tax Type   | Description | Loss Type   | Restrictions       |
      | RefId1 | Period 2015 | Tax Type 1 | LCF 1       | Loss Type 1 | Loss Restriction 1 |
    And the following Authorization:
      | Name                                  | Item                   | Rights       |
      | TAM Loss Carry Forwards UPDATE Access | TAM_LOSS_CARRY_FORWARD | READ, UPDATE |
    And I have the authorization "TAM Loss Carry Forwards UPDATE Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/loss-carry-forward?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/loss-carry-forward/1/edit"
