@clear-database
Feature: Loss Carry Forward - New
  In order to create a new Loss Carry Forward
  As a user with the required authorisation
  I should be able to navigate to the Loss Carry Forward new page

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Loss Carry Forward Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_loss_carry_forward | Loss Carry Forward Workflow |
    And the following Authorization:
      | Name                                  | Item                   | Rights       |
      | TAM Loss Carry Forwards UPDATE Access | TAM_LOSS_CARRY_FORWARD | CREATE, READ |
    And I have the authorization "TAM Loss Carry Forwards UPDATE Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/loss-carry-forward?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/loss-carry-forward/new"
