@clear-database
Feature: Tax Rates Authorization
  As a user with no authorization to the Tax Rates
  I should have no access to any feature of Tax Rates

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name              | Initial Status | Transitions             |
      | Tax Rate Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id   | Workflow          |
      | tam_tax_rate | Tax Rate Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Tax Rate:
      | Unit   | Period      | Description | Tax Type   | Tax Rate |
      | RefId1 | Period 2012 | Tax Rate 1  | Tax Type 1 | 0.20     |
    And I am logged in

  Scenario: A User without rights to Tax Rates tries to list Tax Rates
    When I go to "/tam/tax-rate?q="
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Tax Rates tries to edit a Tax Rate record
    When I go to "/tam/tax-rate/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Tax Rates tries to create a new Tax Rate record
    When I go to "/tam/tax-rate/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Tax Rate new page
    Given the following Authorization:
      | Name                      | Item         | Rights |
      | TAM Tax Rates READ Access | TAM_TAX_RATE | READ   |
    And I have the authorization "TAM Tax Rates READ Access"
    When I go to "/tam/tax-rate/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Rates
    Given the following Authorization:
      | Name                      | Item         | Rights |
      | TAM Tax Rates READ Access | TAM_TAX_RATE | READ   |
    And I have the authorization "TAM Tax Rates READ Access"
    When I go to "/tam/tax-rate?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
