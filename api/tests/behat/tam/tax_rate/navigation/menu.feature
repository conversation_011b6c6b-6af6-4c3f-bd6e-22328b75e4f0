@clear-database
Feature: Tax Rate Navigation - Menu
  In order to browse the Tax Rates list page
  As a user with the required authorisation
  I should be able to navigate through the Tax Rate menu entries

  Background:
    Given the following Authorization:
      | Name                      | Item         | Rights |
      | TAM Tax Rates READ Access | TAM_TAX_RATE | READ   |
    And I have the authorization "TAM Tax Rates READ Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Rate" in the menu under "TAM"
    Then I should be on "/tam/tax-rate"
