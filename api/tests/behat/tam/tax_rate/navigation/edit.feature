@clear-database
Feature: Tax Rate Navigation - Edit
  In order to edit a Tax Rate
  As a user with the required authorisation
  I should be able to browse the Tax Rate edit pages

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name              | Initial Status | Transitions |
      | Tax Rate Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id   | Workflow          |
      | tam_tax_rate | Tax Rate Workflow |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TAM Tax Rate:
      | Unit   | Period      | Description | Tax Type   |
      | RefId1 | Period 2015 | Tax Rate 1  | Tax Type 1 |
    And the following Authorization:
      | Name                        | Item         | Rights       |
      | TAM Tax Rates UPDATE Access | TAM_TAX_RATE | READ, UPDATE |
    And I have the authorization "TAM Tax Rates UPDATE Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/tax-rate?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/tax-rate/1/edit"
