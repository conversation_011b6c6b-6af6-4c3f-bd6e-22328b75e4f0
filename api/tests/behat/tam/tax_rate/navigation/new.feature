@clear-database
Feature: Tax Rates Navigation - New
  In order to create a new Tax Rate
  As a user with the required authorisation
  I should be able to navigate to the Tax Credit new page

  Background:
    Given the following Authorization:
      | Name                        | Item         | Rights       |
      | TAM TAx Rates UPDATE Access | TAM_TAX_RATE | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name              | Initial Status | Transitions |
      | Tax Rate Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id   | Workflow          |
      | tam_tax_rate | Tax Rate Workflow |
    And I have the authorization "TAM Tax Rates UPDATE Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-rate?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-rate/new"
