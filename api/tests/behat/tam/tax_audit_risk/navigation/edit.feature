@clear-database
Feature: Tax Audit Risk Navigation - Edit
  In order to manage Tax Audit Risk
  As a user allowed to the TAM Tax Audit Risk
  I should be able to navigate through the Tax Audit Risk pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Tax Audit Risk Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_audit_risk | Tax Audit Risk Workflow |
    And the following Period:
      | Name        |
      | Period 2015 |
    And the following Risk Type:
      | Name        |
      | Risk Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TAM Tax Audit Risk:
      | Unit   | Period      | Description      | Tax Type   | Risk Type   |
      | RefId1 | Period 2015 | Tax Audit Risk 1 | Tax Type 1 | Risk Type 1 |
    And the following Authorization:
      | Name                       | Item               | Rights               |
      | Tax Audit Risk Full Access | TAM_TAX_AUDIT_RISK | READ, UPDATE, DELETE |
    And I have the authorization "Tax Audit Risk Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/tax-audit-risk?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/tax-audit-risk/1/edit"
