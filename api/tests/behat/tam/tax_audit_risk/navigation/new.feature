@clear-database
Feature: Tax Audit Risk Navigation - New
  In order to manage Tax Audit Risk
  As a user allowed to the TAM Tax Audit Risk
  I should be able to navigate through the Tax Audit Risk pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Tax Audit Risk Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_audit_risk | Tax Audit Risk Workflow |
    And the following Authorization:
      | Name                       | Item               | Rights       |
      | Tax Audit Risk Full Access | TAM_TAX_AUDIT_RISK | CREATE, READ |
    And I have the authorization "Tax Audit Risk Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-audit-risk?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-audit-risk/new"
