@clear-database
Feature: Tax Audit Risk Authorization
  As a user with no authorization to the TAM Module
  I should have no access to any feature of Tax Audit Risk Module

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Tax Audit Risk Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_audit_risk | Tax Audit Risk Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Risk Type:
      | Name        |
      | Risk Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Tax Audit Risk:
      | Unit   | Period      | Description      | Tax Year | Tax Type   | Risk Type   |
      | RefId1 | Period 2012 | Tax Audit Risk 1 | 2014     | Tax Type 1 | Risk Type 1 |
    And I am logged in

  Scenario: A User without TAM Tax Audit Risk rights tries to list the Tax Audit Risk records
    When I go to "/tam/tax-audit-risk?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Audit Risk rights tries to edit an Tax Audit Risk record
    When I go to "/tam/tax-audit-risk/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Audit Risk rights tries to create a new Tax Audit Risk record
    When I go to "/tam/tax-audit-risk/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the TAM Tax Audit Risk creation form
    Given the following Authorization:
      | Name                       | Item               | Rights |
      | Tax Audit Risk Read Access | TAM_TAX_AUDIT_RISK | READ   |
    And I have the authorization "Tax Audit Risk Read Access"
    When I go to "/tam/tax-audit-risk/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Audit Risk records
    Given the following Authorization:
      | Name                       | Item               | Rights |
      | Tax Audit Risk Read Access | TAM_TAX_AUDIT_RISK | READ   |
    And I have the authorization "Tax Audit Risk Read Access"
    When I go to "/tam/tax-audit-risk?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
