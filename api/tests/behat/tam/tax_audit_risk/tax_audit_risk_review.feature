@clear-database
Feature: Tax Audit Risk Review
  In order to review Tax Audit Risk records
  As a user
  I should be able to add and remove my approval

  Background:
    Given the following User:
      | username |
      | testuser |
    And the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
    And the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | done               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Tax Audit Risk Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_audit_risk | Tax Audit Risk Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Risk Type:
      | Name       |
      | Incentives |
    And the following Tax Type:
      | Name          |
      | Capital gains |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
    And the following Authorization:
      | Name                       | Item               | Rights               |
      | Tax Audit Risk Full Access | TAM_TAX_AUDIT_RISK | READ, UPDATE, DELETE |
    And I have the authorization "Tax Audit Risk Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user approves a Tax Audit Risk record
    Given the following TAM Tax Audit Risk:
      | Status | Unit   | Period      | Description      | Tax Year | Tax Type      | Risk Type  |
      | open   | RefId1 | Period 2012 | Tax Audit Risk 1 | 2014     | Capital gains | Incentives |
    When I go to "/tam/tax-audit-risk/1/edit"
    Then I should see "This task has not yet been reviewed."
    And the "yes-ok" button in "Entity Reviews" should be enabled
    When I click the "yes-ok" button in "Entity Reviews"
    Then I should see "Add a Review"
    And I click the "Review" button in the dialog
    Then I should be on "/tam/tax-audit-risk/1/edit"
    And I should see a success message

  Scenario: A user cannot approve a Tax Audit Risk record in a closed period
    Given the following TAM Tax Audit Risk:
      | Status | Unit   | Period      | Description      | Tax Year | Tax Type      | Risk Type  |
      | open   | RefId1 | Period 2012 | Tax Audit Risk 1 | 2014     | Capital gains | Incentives |
    And The period "Period 2012" is closed
    When I go to "/tam/tax-audit-risk/1/edit"
    Then the "yes-ok" button in "Entity Reviews" should be disabled

  Scenario: A user removes his approval from a Tax Audit Risk record
    Given the following TAM Tax Audit Risk:
      | Id | Status | Unit   | Period      | Tax Year | Tax Type      | Risk Type  |
      | 1  | open   | RefId1 | Period 2012 | 2014     | Capital gains | Incentives |
    And I have performed a review on "TAM Tax Audit Risk" where "id" is "1"
    When I go to "/tam/tax-audit-risk/1/edit"
    Then the "no" button in "Entity Reviews" should be enabled
    When I click the "no" button in "Entity Reviews"
    Then I should see "Remove Review"
    When I click the "Remove review" button in the dialog
    Then I should be on "/tam/tax-audit-risk/1/edit"
    And I should see a success message

  Scenario: A user cannot remove his approval from a Tax Audit Risk record in a closed period
    Given the following TAM Tax Audit Risk:
      | Id | Status | Unit   | Period      | Tax Year | Tax Type      | Risk Type  |
      | 1  | open   | RefId1 | Period 2012 | 2014     | Capital gains | Incentives |
    And The period "Period 2012" is closed
    And I have performed a review on "TAM Tax Audit Risk" where "id" is "1"
    When I go to "/tam/tax-audit-risk/1/edit"
    Then the "no" button in "Entity Reviews" should be disabled

  Scenario: A user cannot remove his approval from a Tax Audit Risk in the status type "complete"
    Given the following TAM Tax Audit Risk:
      | Id | Status | Unit   | Period      | Tax Year | Tax Type      | Risk Type  |
      | 1  | done   | RefId1 | Period 2012 | 2014     | Capital gains | Incentives |
    And I have performed a review on "TAM Tax Audit Risk" where "id" is "1"
    When I go to "/tam/tax-audit-risk/1/edit"
    Then the "no" button in "Entity Reviews" should be disabled
