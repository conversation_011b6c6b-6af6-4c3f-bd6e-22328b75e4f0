@clear-database
Feature: Tax Audit Risk transition log
  In order to track changes in tax audit risk
  As a user with the required authorisation
  I should be able to view the transition log of a tax audit risk

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
    And the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | done               |
      | Complete | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions     |
      | Tax Audit Risk Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_audit_risk | Tax Audit Risk Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Risk Type:
      | Name        |
      | Risk Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
    And the following TAM Tax Audit Risk:
      | Unit   | Period      | Description      | Tax Year | Tax Type   | Risk Type   | Status |
      | RefId1 | Period 2012 | Tax Audit Risk 1 | 2015     | Tax Type 1 | Risk Type 1 | open   |
    And the following Authorization:
      | Name                       | Item               | Rights               |
      | Tax Audit Risk Full Access | TAM_TAX_AUDIT_RISK | READ, UPDATE, DELETE |
    And I have the authorization "Tax Audit Risk Full Access"
    And I am logged in

  Scenario: A user views an empty transition log
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-audit-risk/1/edit"
    When I click the "open" button in "Page Controls"
    And I click the "History" button
    Then I should see "Status History"
    And I should see "There have been no status changes."

  Scenario: A user views a transition log
    Given the following Task:
      | Type               | Status |
      | tam_tax_audit_risk | open   |
    And the following Status Transition Log:
      | id | Username | Origin Status Name | Origin Status Type | Destination Status Name | Destination Status Type | Task               |
      | 1  | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 2  | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 3  | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 4  | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 5  | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 6  | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 7  | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 8  | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 9  | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 10 | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 11 | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 12 | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 13 | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
      | 14 | testUser | done               | Complete           | open                    | Open                    | tam_tax_audit_risk |
      | 15 | testUser | open               | Open               | done                    | Complete                | tam_tax_audit_risk |
    And the following TAM Tax Audit Risk:
      | Id | Unit   | Period      | Description      | Tax Year | Tax Type   | Risk Type   | Task               |
      | 1  | RefId1 | Period 2012 | Tax Audit Risk 2 | 2015     | Tax Type 1 | Risk Type 1 | tam_tax_audit_risk |
    And I am assigned to unit "RefId1"
    And I am on "/tam/tax-audit-risk/2/edit"
    When I click the "open" button in "Page Controls"
    And I click the "History" button
    Then I should see "Status History"
    And I should see 15 "#workflow-log-entries li" elements
