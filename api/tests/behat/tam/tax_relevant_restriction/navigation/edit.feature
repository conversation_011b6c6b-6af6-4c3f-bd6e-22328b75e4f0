@clear-database
Feature: Tax Relevant Restriction - Edit
  In order to edit a Tax Relevant Restriction
  As a user with the required authorisation
  I should be able to navigate to the Tax Relevant Restriction edit page

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                               | Initial Status | Transitions |
      | Tax Relevant Restrictions Workflow | open           | Start       |
    And the following Binding:
      | Binding Id                   | Workflow                           |
      | tam_tax_relevant_restriction | Tax Relevant Restrictions Workflow |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Restriction Reason:
      | Name                 |
      | Restriction Reason 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TAM Tax Relevant Restriction:
      | Unit   | Period      | Description                 | Tax Type   | Reason               |
      | RefId1 | Period 2015 | Tax Relevant Restrictions 1 | Tax Type 1 | Restriction Reason 1 |
    And the following Authorization:
      | Name                                        | Item                         | Rights       |
      | TAM Tax Relevant Restrictions UPDATE Access | TAM_TAX_RELEVANT_RESTRICTION | READ, UPDATE |
    And I have the authorization "TAM Tax Relevant Restrictions UPDATE Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/tax-relevant-restriction?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/tax-relevant-restriction/1/edit"
