@clear-database
Feature: Tax Relevant Restriction - New
  In order to create a new Tax Relevant Restriction
  As a user with the required authorisation
  I should be able to navigate to the Tax Relevant Restriction new page

  Background:
    Given the following Authorization:
      | Name                                        | Item                         | Rights       |
      | TAM Tax Relevant Restrictions UPDATE Access | TAM_TAX_RELEVANT_RESTRICTION | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                               | Initial Status | Transitions |
      | Tax Relevant Restrictions Workflow | open           | Start       |
    And the following Binding:
      | Binding Id                   | Workflow                           |
      | tam_tax_relevant_restriction | Tax Relevant Restrictions Workflow |
    And I have the authorization "TAM Tax Relevant Restrictions UPDATE Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-relevant-restriction?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-relevant-restriction/new"
