@clear-database
Feature: Tax Relevant Restriction - Menu
  In order to browse the Tax Relevant Restrictions list page
  As a user with the required authorisation
  I should be able to navigate to the Tax Relevant Restriction list page form the menu

  Background:
    Given the following Authorization:
      | Name                                      | Item                         | Rights |
      | TAM Tax Relevant Restrictions READ Access | TAM_TAX_RELEVANT_RESTRICTION | READ   |
    And I have the authorization "TAM Tax Relevant Restrictions READ Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Relevant Restrictions" in the menu under "TAM"
    Then I should be on "/tam/tax-relevant-restriction"
