@clear-database
Feature: Tax Relevant Restriction Bulk Edit
  In order to bulk edit Tax Relevant Restrictions
  As a User with the required authorisation
  I should be able to perform bulk edit on Tax Relevant Restrictions

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
    And the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | done               |
      | Complete | done          | open               |
    And the following Workflow:
      | Name                               | Initial Status | Transitions     |
      | Tax Relevant Restrictions Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id                   | Workflow                           |
      | tam_tax_relevant_restriction | Tax Relevant Restrictions Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Restriction Reason:
      | Name                 |
      | Restriction Reason 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
      | RefId3 | Legal Unit 3 | EUR      | Germany |
    And the following TAM Tax Relevant Restriction:
      | Status | Unit   | Period      | Description                 | Tax Type   | Reason               | Tax Base | Potential Tax Liabilities | Valid From | Valid To   |
      | open   | RefId1 | Period 2012 | Tax Relevant Restrictions 1 | Tax Type 1 | Restriction Reason 1 | 20       | 20                        | 01.01.2016 | 01.01.2017 |
      | open   | RefId2 | Period 2012 | Tax Relevant Restrictions 2 | Tax Type 2 | Restriction Reason 1 | 20       | 20                        | 01.01.2016 | 01.01.2017 |
      | done   | RefId3 | Period 2012 | Tax Relevant Restrictions 3 | Tax Type 3 | Restriction Reason 1 | 20       | 20                        | 01.01.2016 | 01.01.2017 |
    Given the following Authorization:
      | Name                                      | Item                         | Rights               |
      | TAM Tax Relevant Restrictions READ Access | TAM_TAX_RELEVANT_RESTRICTION | READ                 |
      | TAM Tax Relevant Restrictions FULL Access | TAM_TAX_RELEVANT_RESTRICTION | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "TAM Tax Relevant Restrictions READ Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-relevant-restriction?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Tax Relevant Restriction is selected
    Given I have the authorization "TAM Tax Relevant Restrictions FULL Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/tax-relevant-restriction?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/tax-relevant-restriction/1/edit"

  Scenario: A user can bulk edit Tax Relevant Restrictions
    Given I have the authorization "TAM Tax Relevant Restrictions FULL Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-relevant-restriction?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-relevant-restriction/edit?selection=1%2C2"
    When I enable the "Tax Type" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Type | Tax Type 1 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/tax-relevant-restriction"
    And I should see a success message

  Scenario: A user cannot bulk edit Tax Relevant Restrictions because the entered values is invalid
    Given I have the authorization "TAM Tax Relevant Restrictions FULL Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-relevant-restriction?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-relevant-restriction/edit?selection=1%2C2"
    When I enable the "Valid From" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Valid From | Not a date |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "TAM Tax Relevant Restrictions FULL Access"
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                           |
      | Description disabled | done     | Tax Relevant Restrictions Workflow |
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tam/tax-relevant-restriction?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-relevant-restriction/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled

