@clear-database
Feature: Tax Relevant Restrictions Authorization
  As a user with no authorization to the Tax Relevant Restrictions
  I should have no access to any feature of Tax Relevant Restrictions

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                               | Initial Status | Transitions             |
      | Tax Relevant Restrictions Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                   | Workflow                           |
      | tam_tax_relevant_restriction | Tax Relevant Restrictions Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Restriction Reason:
      | Name                 |
      | Restriction Reason 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Tax Relevant Restriction:
      | Unit   | Period      | Description                 | Tax Type   | Reason               |
      | RefId1 | Period 2012 | Tax Relevant Restrictions 1 | Tax Type 1 | Restriction Reason 1 |
    And I am logged in

  Scenario: A User without rights to Tax Relevant Restrictions tries to list the Tax Relevant Restrictions
    When I go to "/tam/tax-relevant-restriction?q="
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Tax Relevant Restrictions tries to edit a Tax Relevant Restriction
    When I go to "/tam/tax-relevant-restriction/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Tax Relevant Restrictions tries to create a new Tax Relevant Restriction
    When I go to "/tam/tax-relevant-restriction/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read rights to Tax Relevant Restrictions tries ot edita Tax Relevant Restriction
    Given the following Authorization:
      | Name                                      | Item                         | Rights |
      | TAM Tax Relevant Restrictions READ Access | TAM_TAX_RELEVANT_RESTRICTION | READ   |
    And I have the authorization "TAM Tax Relevant Restrictions READ Access"
    When I go to "/tam/tax-relevant-restriction/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read rights to Tax Relevant Restrictions tries to bulk delete Tax Relevant Restrictions
    Given the following Authorization:
      | Name                                      | Item                         | Rights |
      | TAM Tax Relevant Restrictions READ Access | TAM_TAX_RELEVANT_RESTRICTION | READ   |
    And I have the authorization "TAM Tax Relevant Restrictions READ Access"
    When I go to "/tam/tax-relevant-restriction?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
