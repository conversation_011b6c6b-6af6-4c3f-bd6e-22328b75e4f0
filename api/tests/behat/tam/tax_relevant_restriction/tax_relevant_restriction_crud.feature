@clear-database
Feature: Tax Relevant Restrictions - Create, Read, Update and Delete
  In order to manage Tax Relevant Restrictions
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Tax Relevant Restrictions

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
    And the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | done               |
      | Complete | done          | open               |
    And the following Workflow:
      | Name                               | Initial Status | Transitions     |
      | Tax Relevant Restrictions Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id                   | Workflow                           |
      | tam_tax_relevant_restriction | Tax Relevant Restrictions Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Restriction Reason:
      | Name                 |
      | Restriction Reason 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
    And the following TAM Tax Relevant Restriction:
      | Unit   | Period      | Description                 | Tax Type   | Reason               | Tax Base | Potential tax liabilities | Valid From | Valid To   |
      | RefId1 | Period 2012 | Tax Relevant Restrictions 1 | Tax Type 1 | Restriction Reason 1 | 20       | 20                        | 01.01.2016 | 01.01.2017 |
    And the following Authorization:
      | Name                                      | Item                         | Rights                       |
      | TAM Tax Relevant Restrictions FULL Access | TAM_TAX_RELEVANT_RESTRICTION | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "TAM Tax Relevant Restrictions FULL Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I am on "/tam/tax-relevant-restriction?q="
    Then I should see "No results found"

  Scenario: A user with the required authorisation visits the list page
    Given I am assigned to unit "RefId1"
    When I am on "/tam/tax-relevant-restriction?q="
    Then I should see the following table portion:
      | Unit Name    | Tax Type   | Reason               |
      | Legal Unit 1 | Tax Type 1 | Restriction Reason 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tam/tax-relevant-restriction?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-relevant-restriction/new"
    And the "Tax Relevant Restriction" form field "Period" should be "Period 2012"

  Scenario: A user with the required authorisation creates a new Tax Relevant Restriction
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-relevant-restriction/new"
    When I fill in the "Tax Relevant Restriction" form with:
      | Unit                      | Legal Unit 1                |
      | Period                    | Period 2012                 |
      | Description               | Tax Relevant Restrictions 2 |
      | Reason                    | Restriction Reason 1        |
      | Tax Type                  | Tax Type 1                  |
      | Valid From                | 01.01.2016                  |
      | Valid To                  | 01.01.2017                  |
      | Tax Base                  | 20                          |
      | Potential Tax Liabilities | 20                          |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tam/tax-relevant-restriction/2/edit"
    And I should see a success message

  Scenario: A user with the required authorisation updating a Tax Relevant Restriction
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-relevant-restriction/1/edit"
    When I fill in the "Tax Relevant Restriction" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should be on "/tam/tax-relevant-restriction/1/edit"
    And I should see a success message

  Scenario: A user with the required authorisation deleting a Tax Relevant Restriction
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-relevant-restriction/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/tam/tax-relevant-restriction"
    And I should see a success message
    And I should not see "Legal Unit 1"

  Scenario: A user with the required authorisation tries to delete a Tax Relevant Restriction where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2012" is closed
    When I go to "/tam/tax-relevant-restriction/1/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: A user with the required authorisation tries to create a Tax Relevant Restriction where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2012" is closed
    And I am on "/tam/tax-relevant-restriction/new"
    When I fill in the "Tax Relevant Restriction" form with:
      | Unit                      | Legal Unit 1                |
      | Period                    | Period 2012                 |
      | Description               | Tax Relevant Restrictions 2 |
      | Reason                    | Restriction Reason 1        |
      | Tax Type                  | Tax Type 1                  |
      | Valid From                | 01.01.2016                  |
      | Valid To                  | 01.01.2017                  |
      | Tax Base                  | 20                          |
      | Potential Tax Liabilities | 20                          |
    And I press "Save"
    Then I should see an error message

  Scenario: A user with the required authorisation tries to edit a Tax Relevant Restriction where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2012" is closed
    When I go to "/tam/tax-relevant-restriction/1/edit"
    Then the "Save" button in "Page Controls" should be disabled
