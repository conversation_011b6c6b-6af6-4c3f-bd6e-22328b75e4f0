@clear-database
Feature: Tax Litigation Authorization
  As a user with no authorization to the TAM Module
  I should have no access to any feature of Tax Litigation Module

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Specification:
      | Name               |
      | Test Specification |
    And the following Risk Type:
      | Name           |
      | Test Risk Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Tax Litigation Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_litigation | Tax Litigation Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Tax Litigation:
      | Unit   | Period      | Tax Type   | Description      | Tax Year | Risk Type      | Specification      |
      | RefId1 | Period 2012 | Tax Type 1 | Tax Litigation 1 | 2014     | Test Risk Type | Test Specification |
    And I am logged in

  Scenario: A User without TAM Tax Litigation rights tries to list the Tax Litigation records
    When I go to "/tam/tax-litigation?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Litigation rights tries to edit an Tax Litigation record
    When I go to "/tam/tax-litigation/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Litigation rights tries to create a new Tax Litigation record
    When I go to "/tam/tax-litigation/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the TAM Tax Litigation creation form
    Given the following Authorization:
      | Name                       | Item               | Rights |
      | Tax Litigation Read Access | TAM_TAX_LITIGATION | READ   |
    And I have the authorization "Tax Litigation Read Access"
    When I go to "/tam/tax-litigation/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Litigation records
    Given the following Authorization:
      | Name                       | Item               | Rights |
      | Tax Litigation Read Access | TAM_TAX_LITIGATION | READ   |
    And I have the authorization "Tax Litigation Read Access"
    When I go to "/tam/tax-litigation?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
