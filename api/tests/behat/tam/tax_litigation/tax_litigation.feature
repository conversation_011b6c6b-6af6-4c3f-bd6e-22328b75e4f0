@clear-database
Feature: Tax Litigation
  In order to manage Tax Litigation records
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Tax Litigation records

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Specification:
      | Name               |
      | Test Specification |
    And the following Risk Type:
      | Name           |
      | Test Risk Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Tax Litigation Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_litigation | Tax Litigation Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TAM Tax Litigation:
      | Unit   | Period      | Tax Type   | Description      | Tax Year | Risk Type      | Specification      | Permanent |
      | RefId1 | Period 2012 | Tax Type 1 | Tax Litigation 1 | 2014     | Test Risk Type | Test Specification | No        |
      | RefId2 | Period 2013 | Tax Type 2 | Tax Litigation 2 | 2015     | Test Risk Type | Test Specification | No        |
    And the following Authorization:
      | Name                       | Item               | Rights                       |
      | Tax Litigation Full Access | TAM_TAX_LITIGATION | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Tax Litigation Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I am on "/tam/tax-litigation?q="
    Then I should see "No results found"

  Scenario: A user with the required authorisation visits the list page
    Given I am assigned to unit "RefId1"
    When I am on "/tam/tax-litigation?q="
    Then I should see the following table portion:
      | Unit Name    | Tax Type   |
      | Legal Unit 1 | Tax Type 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tam/tax-litigation?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-litigation/new"
    And the "Tax Litigation" form field "Period" should be "Period 2012"

  Scenario: A user with the required authorisation creates a new Tax Litigation record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-litigation/new"
    When I fill in the "Tax Litigation" form with:
      | Unit          | Legal Unit 1       |
      | Period        | Period 2012        |
      | Tax Type      | Tax Type 1         |
      | Risk Type     | Test Risk Type     |
      | Specification | Test Specification |
      | Tax Year      | 2016               |
      | Description   | Tax Litigation 3   |
      | Permanent     | Permanent          |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tam/tax-litigation/3/edit"
    And I should see a success message

  Scenario: A user with the required authorisation updating a Tax Litigation record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-litigation/1/edit"
    When I fill in the "Tax Litigation" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should be on "/tam/tax-litigation/1/edit"
    And I should see a success message

  Scenario: A user with the required authorisation deleting a Tax Litigation record
    Given I am assigned to unit "RefId2"
    And I am on "/tam/tax-litigation/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    When I click the "Delete" button in the dialog
    Then I should be on "/tam/tax-litigation"
    And I should see a success message
    And I should not see "Legal Unit 2"

  Scenario: A user with the required authorisation tries to delete a tax litigation where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/tax-litigation/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: A user with the required authorisation tries to create a tax litigation where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    And I am on "/tam/tax-litigation/new"
    When I fill in the "Tax Litigation" form with:
      | Unit          | Legal Unit 1       |
      | Period        | Period 2014        |
      | Tax Type      | Tax Type 1         |
      | Risk Type     | Test Risk Type     |
      | Specification | Test Specification |
      | Tax Year      | 2016               |
      | Description   | Tax Litigation 3   |
      | Permanent     | Permanent          |
    And I press "Save"
    Then I should see an error message

  Scenario: A user with the required authorisation tries to edit a tax litigation where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/tax-litigation/2/edit"
    Then the "Save" button in "Page Controls" should be disabled
