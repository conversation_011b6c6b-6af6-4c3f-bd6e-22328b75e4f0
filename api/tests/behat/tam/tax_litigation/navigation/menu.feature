@clear-database
Feature: Tax Litigation Navigation - Menu
  In order to manage Tax Litigation
  As a user allowed to the TAM Tax Litigation
  I should be able to navigate through the Tax Litigation pages

  Background:
    Given the following Authorization:
      | Name                       | Item               | Rights               |
      | Tax Litigation Full Access | TAM_TAX_LITIGATION | READ, UPDATE, DELETE |
    And I have the authorization "Tax Litigation Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Litigation" in the menu under "TAM"
    Then I should be on "/tam/tax-litigation"
