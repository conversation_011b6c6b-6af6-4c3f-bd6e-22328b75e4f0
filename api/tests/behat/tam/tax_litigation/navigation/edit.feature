@clear-database
Feature: Tax Litigation Navigation - Edit
  In order to manage Tax Litigation
  As a user allowed to the TAM Tax Litigation
  I should be able to navigate through the Tax Litigation pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Tax Litigation Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_litigation | Tax Litigation Workflow |
    And the following Period:
      | Name        |
      | Period 2015 |
    And the following Specification:
      | Name               |
      | Test Specification |
    And the following Risk Type:
      | Name           |
      | Test Risk Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TAM Tax Litigation:
      | Unit   | Period      | Tax Type   | Description      | Risk Type      | Specification      |
      | RefId1 | Period 2015 | Tax Type 1 | Tax Litigation 1 | Test Risk Type | Test Specification |
    And the following Authorization:
      | Name                       | Item               | Rights               |
      | Tax Litigation Full Access | TAM_TAX_LITIGATION | READ, UPDATE, DELETE |
    And I have the authorization "Tax Litigation Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/tax-litigation?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/tax-litigation/1/edit"
