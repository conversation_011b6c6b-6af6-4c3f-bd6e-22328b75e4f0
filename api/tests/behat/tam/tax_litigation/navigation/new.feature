@clear-database
Feature: Tax Litigation Navigation - New
  In order to manage Tax Litigation
  As a user allowed to the TAM Tax Litigation
  I should be able to navigate through the Tax Litigation pages

  Background:
    Given the following Authorization:
      | Name                       | Item               | Rights       |
      | Tax Litigation Full Access | TAM_TAX_LITIGATION | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Tax Litigation Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tam_tax_litigation | Tax Litigation Workflow |
    And I have the authorization "Tax Litigation Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-litigation?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-litigation/new"
