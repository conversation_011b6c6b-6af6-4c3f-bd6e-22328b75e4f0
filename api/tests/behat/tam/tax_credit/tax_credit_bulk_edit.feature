@clear-database
Feature: Tax Credits Bulk Edit
  In order to bulk edit Tax Credits
  As a User with the required authorisation
  I should be able to perform bulk edit on Tax Credit records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                | Initial Status | Transitions             |
      | Tax Credit Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id     | Workflow            |
      | tam_tax_credit | Tax Credit Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Tax Credit Type:
      | Name              |
      | Credit Tax Type 1 |
      | Credit Tax Type 2 |
      | Credit Tax Type 3 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following TAM Tax Credit:
      | Status | Unit   | Period      | Description  | Tax Type   | Credit Type       |
      | open   | RefId1 | Period 2012 | Tax Credit 1 | Tax Type 1 | Credit Tax Type 1 |
      | open   | RefId2 | Period 2013 | Tax Credit 2 | Tax Type 2 | Credit Tax Type 2 |
      | done   | RefId3 | Period 2013 | Tax Credit 3 | Tax Type 3 | Credit Tax Type 2 |
    And the following Authorization:
      | Name                            | Item           | Rights               |
      | TAM Tax Credits READ Access     | TAM_TAX_CREDIT | READ                 |
      | TAM Tax Credits COMPLETE Access | TAM_TAX_CREDIT | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "TAM Tax Credits READ Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-credit?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Tax Credit is selected
    Given I have the authorization "TAM Tax Credits COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/tax-credit?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/tax-credit/1/edit"

  Scenario: A user can bulk edit Tax Credits
    Given I have the authorization "TAM Tax Credits COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-credit?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-credit/edit?selection=1%2C2"
    When I enable the "Addition" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Addition | 5 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/tax-credit"
    And I should see a success message

  Scenario: A user cannot bulk edit Tax Credits because the entered value is invalid
    Given I have the authorization "TAM Tax Credits COMPLETE Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-credit?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-credit/edit?selection=1%2C2"
    When I enable the "Addition" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Addition | -5 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "TAM Tax Credits COMPLETE Access"
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow            |
      | Description disabled | done     | Tax Credit Workflow |
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tam/tax-credit?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-credit/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled

