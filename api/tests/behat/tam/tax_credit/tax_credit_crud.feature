@clear-database
Feature: Tax Credit - Create, Read, Update and Delete
  In order to manage Tax Credits
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Tax Credits

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                | Initial Status | Transitions             |
      | Tax Credit Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id     | Workflow            |
      | tam_tax_credit | Tax Credit Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Tax Credit Type:
      | Name              |
      | Credit Tax Type 1 |
      | Credit Tax Type 2 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
    And the following Loss Restriction:
      | Name          |
      | Restriction 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TAM Tax Credit:
      | Unit   | Period      | Description  | Tax Type   | Credit Type       |
      | RefId1 | Period 2012 | Tax Credit 1 | Tax Type 1 | Credit Tax Type 1 |
      | RefId2 | Period 2013 | Tax Credit 2 | Tax Type 1 | Credit Tax Type 2 |
    And the following Authorization:
      | Name                            | Item           | Rights                       |
      | TAM Tax Credits COMPLETE Access | TAM_TAX_CREDIT | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "TAM Tax Credits COMPLETE Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I am on "/tam/tax-credit?q="
    Then I should see "No results found"

  Scenario: A user with the required authorisation visits the list page
    Given I am assigned to unit "RefId1"
    When I am on "/tam/tax-credit?q="
    Then I should see the following table portion:
      | Unit Name    | Tax Type   |
      | Legal Unit 1 | Tax Type 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tam/tax-credit?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-credit/new"
    And the "Tax Credit" form field "Period" should be "Period 2012"

  Scenario: A user with the required authorisation creates a new Tax Credit
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-credit/new"
    When I fill in the "Tax Credit" form with:
      | Unit         | Legal Unit 1      |
      | Period       | Period 2012       |
      | Description  | Tax Credit 3      |
      | Tax Type     | Tax Type 1        |
      | Credit Type  | Credit Tax Type 1 |
      | Restrictions | Restriction 1     |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tam/tax-credit/3/edit"
    And I should see a success message

  Scenario: A user with the required authorisation updating a Tax Credit
    Given I am assigned to unit "RefId1"
    And I am on "/tam/tax-credit/1/edit"
    When I fill in the "Tax Credit" form with:
      | Description  | This is a description |
      | Restrictions | Restriction 1         |
    And I press "Save"
    Then I should be on "/tam/tax-credit/1/edit"
    And I should see a success message

  Scenario: A user with the required authorisation deleting a Tax Credit
    Given I am assigned to unit "RefId2"
    And I am on "/tam/tax-credit/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/tam/tax-credit"
    And I should see a success message
    And I should not see "Legal Unit 2"

  Scenario: A user with the required authorisation tries to delete a Tax Credit where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/tax-credit/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: A user with the required authorisation tries to create a Tax Credit where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    And I am on "/tam/tax-credit/new"
    When I fill in the "Tax Credit" form with:
      | Unit         | Legal Unit 1      |
      | Period       | Period 2014       |
      | Description  | Tax Credit 3      |
      | Tax Type     | Tax Type 1        |
      | Credit Type  | Credit Tax Type 1 |
      | Restrictions | Restriction 1     |
    And I press "Save"
    Then I should see an error message

  Scenario: A user with the required authorisation tries to edit a Tax Credit where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/tax-credit/2/edit"
    Then the "Save" button in "Page Controls" should be disabled
