@clear-database
Feature: Tax Credits Authorization
  As a user with no authorization to Tax Credits
  I should have no access to any feature of Tax Credits

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                | Initial Status | Transitions             |
      | Tax Credit Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id     | Workflow            |
      | tam_tax_credit | Tax Credit Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Tax Credit Type:
      | Name              |
      | Credit Tax Type 1 |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Tax Credit:
      | Unit   | Period      | Description  | Tax Type   | Credit Type       |
      | RefId1 | Period 2012 | Tax Credit 1 | Tax Type 1 | Credit Tax Type 1 |
    And the following Authorization:
      | Name                        | Item           | Rights |
      | TAM Tax Credits READ Access | TAM_TAX_CREDIT | READ   |
    And I am logged in

  Scenario: A User without rights to Tax Credits tries to list Tax Credits
    When I go to "/tam/tax-credit?q="
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Tax Credits tries to edit an Tax Credit record
    When I go to "/tam/tax-credit/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without rights to Tax Credits tries to create a new Tax Credit record
    When I go to "/tam/tax-credit/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Tax Credit new page
    Given I have the authorization "TAM Tax Credits READ Access"
    When I go to "/tam/tax-credit/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Credits
    Given I have the authorization "TAM Tax Credits READ Access"
    When I go to "/tam/tax-credit?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
