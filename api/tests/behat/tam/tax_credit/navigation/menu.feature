@clear-database
Feature: Tax Credit Navigation - Menu
  In order to to browse the Tax Credits list page
  As a user with the required authorisation
  I should be able to navigate through the Tax Credit menu entries

  Background:
    Given the following Authorization:
      | Name                        | Item           | Rights |
      | TAM Tax Credits READ Access | TAM_TAX_CREDIT | READ   |
    And I have the authorization "TAM Tax Credits READ Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Credit" in the menu under "TAM"
    Then I should be on "/tam/tax-credit"
