@clear-database
Feature: Tax Credit Navigation - New
  In order to create a new Tax Credit
  As a user with the required authorisation
  I should be able to navigate to the Tax Credit new page

  Background:
    Given the following Authorization:
      | Name                          | Item           | Rights       |
      | TAM Tax Credits UPDATE Access | TAM_TAX_CREDIT | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                | Initial Status | Transitions |
      | Tax Credit Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow            |
      | tam_tax_credit | Tax Credit Workflow |
    And I have the authorization "TAM Tax Credits UPDATE Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-credit?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-credit/new"
