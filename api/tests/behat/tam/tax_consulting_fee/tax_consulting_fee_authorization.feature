@clear-database
Feature: Tax Consulting Fee Authorization
  As a user with no authorization to the TAM Module
  I should have no access to any feature of Tax Consulting Fee Module

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                        | Initial Status | Transitions             |
      | Tax Consulting Fee Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_tax_consulting_fee | Tax Consulting Fee Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following Advice Type:
      | Name     |
      | Payroll  |
      | Training |
    And the following Auditor:
      | Name |
      | EY   |
      | KPMG |
    And the following TAM Tax Consulting Fee:
      | Unit   | Period      | Description          | Tax Advisor | Advice Type | Tax Year | Compliance Tax Advisor |
      | RefId1 | Period 2012 | Tax Consulting Fee 1 | EY          | Payroll     | 2015     | KPMG                   |
    And I am logged in

  Scenario: A User without TAM Tax Consulting Fee rights tries to list the Tax Consulting Fee records
    When I go to "/tam/tax-consulting-fee?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Consulting Fee rights tries to edit an Tax Consulting Fee record
    When I go to "/tam/tax-consulting-fee/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Consulting Fee rights tries to create a new Tax Consulting Fee record
    When I go to "/tam/tax-consulting-fee/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the TAM Tax Consulting Fee creation form
    Given the following Authorization:
      | Name                           | Item                   | Rights |
      | Tax Consulting Fee Read Access | TAM_TAX_CONSULTING_FEE | READ   |
    And I have the authorization "Tax Consulting Fee Read Access"
    When I go to "/tam/tax-consulting-fee/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Consulting Fee records
    Given the following Authorization:
      | Name                           | Item                   | Rights |
      | Tax Consulting Fee Read Access | TAM_TAX_CONSULTING_FEE | READ   |
    And I have the authorization "Tax Consulting Fee Read Access"
    When I go to "/tam/tax-consulting-fee?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
