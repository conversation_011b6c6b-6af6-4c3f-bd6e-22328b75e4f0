@clear-database
Feature: Tax Consulting Fee Navigation - New
  In order to manage Tax Consulting Fee
  As a user allowed to the TAM Tax Consulting Fee
  I should be able to navigate through the Tax Consulting Fee pages

  Background:
    Given the following Authorization:
      | Name                           | Item                   | Rights       |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Tax Consulting Fee Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_tax_consulting_fee | Tax Consulting Fee Workflow |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-consulting-fee?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-consulting-fee/new"
