@clear-database
Feature: Tax Consulting Fee Navigation - Menu
  In order to manage Tax Consulting Fee
  As a user allowed to the TAM Tax Consulting Fee
  I should be able to navigate through the Tax Consulting Fee pages

  Background:
    Given the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | READ, UPDATE, DELETE |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Consulting Fee" in the menu under "TAM"
    Then I should be on "/tam/tax-consulting-fee"
