@clear-database
Feature: Tax Consulting Fee Navigation - Edit
  In order to manage Tax Consulting Fee
  As a user allowed to the TAM Tax Consulting Fee
  I should be able to navigate through the Tax Consulting Fee pages

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                        | Initial Status | Transitions |
      | Tax Consulting Fee Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_tax_consulting_fee | Tax Consulting Fee Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following Advice Type:
      | Name    |
      | Payroll |
    And the following Auditor:
      | Name |
      | EY   |
    And the following TAM Tax Consulting Fee:
      | Unit   | Period      | Description          | Tax Advisor | Advice Type | Compliance Tax Advisor |
      | RefId1 | Period 2015 | Tax Consulting Fee 1 | EY          | Payroll     | EY                     |
    And the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | READ, UPDATE, DELETE |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/tax-consulting-fee?q="
    When I click "Edit" on the table row for "EY"
    Then I should be on "/tam/tax-consulting-fee/1/edit"
