@clear-database
Feature: Tax Consulting Fee Bulk Edit
  In order to bulk edit Tax Consulting Fee records
  As a User with the required authorisation
  I should be able to perform bulk edit actions on Tax Consulting Fee records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                        | Initial Status | Transitions             |
      | Tax Consulting Fee Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tam_tax_consulting_fee | Tax Consulting Fee Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following Advice Type:
      | Name     |
      | Payroll  |
      | Training |
    And the following Auditor:
      | Name |
      | EY   |
      | KPMG |
    And the following TAM Tax Consulting Fee:
      | Status | Unit   | Period      | Description          | Tax Advisor | Advice Type | Tax Year | Compliance Tax Advisor |
      | open   | RefId1 | Period 2012 | Tax Consulting Fee 1 | EY          | Payroll     | 2015     | KPMG                   |
      | open   | RefId2 | Period 2013 | Tax Consulting Fee 2 | KPMG        | Training    | 2016     | EY                     |
      | done   | RefId3 | Period 2014 | Tax Consulting Fee 3 | KPMG        | Payroll     | 2016     | EY                     |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given the following Authorization:
      | Name                           | Item                   | Rights |
      | Tax Consulting Fee Read Access | TAM_TAX_CONSULTING_FEE | READ   |
    And I have the authorization "Tax Consulting Fee Read Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-consulting-fee?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one tax consulting fee is selected
    Given the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | READ, UPDATE, DELETE |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/tax-consulting-fee?q="
    When I check the checkbox on "Legal Unit 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/tax-consulting-fee/1/edit"

  Scenario: A user can bulk edit tax consulting fees
    Given the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | READ, UPDATE, DELETE |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-consulting-fee?q="
    When I check the checkbox on "Legal Unit 1" table row for bulk action
    And I check the checkbox on "Legal Unit 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-consulting-fee/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 2025 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/tax-consulting-fee"
    And I should see a success message

  Scenario: A user can not bulk edit tax consulting fees because the entered values are invalid
    Given the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | READ, UPDATE, DELETE |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-consulting-fee?q="
    When I check the checkbox on "Legal Unit 1" table row for bulk action
    And I check the checkbox on "Legal Unit 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-consulting-fee/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 9999 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given the following Authorization:
      | Name                           | Item                   | Rights               |
      | Tax Consulting Fee Full Access | TAM_TAX_CONSULTING_FEE | READ, UPDATE, DELETE |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                    |
      | Description disabled | done     | Tax Consulting Fee Workflow |
    And I have the authorization "Tax Consulting Fee Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tam/tax-consulting-fee?q="
    When I check the checkbox on "Legal Unit 1" table row for bulk action
    And I check the checkbox on "Legal Unit 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-consulting-fee/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
