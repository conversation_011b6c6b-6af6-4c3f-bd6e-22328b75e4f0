@clear-database
Feature: Income Tax Planning Bulk Edit
  In order to bulk edit Income Tax Planning
  As a User with the required authorisation
  I should be able to perform bulk edit on Income Tax Planning records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                         | Initial Status | Transitions             |
      | Income Tax Planning Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id              | Workflow                     |
      | tam_income_tax_planning | Income Tax Planning Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country  |
      | RefId1 | Legal Unit 1 | TEU      | Yemen    |
      | RefId2 | Legal Unit 2 | EUR      | Zambia   |
      | RefId3 | Legal Unit 3 | EUR      | Zimbabwe |
    And the following TAM Income Tax Planning:
      | Status | Unit   | Period      | Tax Rate | Tax Type   | Planning Period | Profit before tax | Description |
      | open   | RefId1 | Period 2012 | 0.05     | Tax Type 1 | 2012            | 2                 | ITP 1       |
      | open   | RefId2 | Period 2013 | 0.05     | Tax Type 2 | 2013            | 3                 | ITP 2       |
      | done   | RefId3 | Period 2013 | 0.05     | Tax Type 3 | 2013            | 3                 | ITP 3       |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given the following Authorization:
      | Name                           | Item                    | Rights |
      | Income Tax Planing Read Access | TAM_INCOME_TAX_PLANNING | READ   |
    And I have the authorization "Income Tax Planing Read Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tam/income-tax-planning?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one income tax planning is selected
    Given the following Authorization:
      | Name                           | Item                    | Rights               |
      | Income Tax Planing Full Access | TAM_INCOME_TAX_PLANNING | READ, UPDATE, DELETE |
    And I have the authorization "Income Tax Planing Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/income-tax-planning?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/income-tax-planning/1/edit"

  Scenario: A user can bulk edit income tax planning
    Given the following Authorization:
      | Name                           | Item                    | Rights               |
      | Income Tax Planing Full Access | TAM_INCOME_TAX_PLANNING | READ, UPDATE, DELETE |
    And I have the authorization "Income Tax Planing Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tam/income-tax-planning?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-income-tax-planning/edit?selection=1%2C2"
    When I enable the "Planning Period" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Planning Period | 2020 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/income-tax-planning"
    And I should see a success message

  Scenario: A user can not bulk edit income tax planning because the entered values are invalid
    Given the following Authorization:
      | Name                           | Item                    | Rights               |
      | Income Tax Planing Full Access | TAM_INCOME_TAX_PLANNING | READ, UPDATE, DELETE |
    And I have the authorization "Income Tax Planing Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tam/income-tax-planning?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-income-tax-planning/edit?selection=1%2C2"
    When I enable the "Planning Period" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Planning Period | 2000 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given the following Authorization:
      | Name                           | Item                    | Rights               |
      | Income Tax Planing Full Access | TAM_INCOME_TAX_PLANNING | READ, UPDATE, DELETE |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                     |
      | Description disabled | done     | Income Tax Planning Workflow |
    And I have the authorization "Income Tax Planing Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And I am on "/tam/income-tax-planning?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-income-tax-planning/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
