@clear-database
Feature: Income Tax Planning
  In order to manage Income Tax Planning
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Income Tax Planning records

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                         | Initial Status | Transitions             |
      | Income Tax Planning Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id              | Workflow                     |
      | tam_income_tax_planning | Income Tax Planning Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TAM Income Tax Planning:
      | Unit   | Period      | Tax Rate | Tax Type   | Planning Period | Profit Before Tax | Description |
      | RefId1 | Period 2012 | 0.05     | Tax Type 1 | 2012            | 2                 | ITP 1       |
      | RefId2 | Period 2013 | 0.05     | Tax Type 2 | 2013            | 3                 | ITP 2       |
    And the following Authorization:
      | Name                           | Item                    | Rights                       |
      | Income Tax Planing Full Access | TAM_INCOME_TAX_PLANNING | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Income Tax Planing Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I am on "/tam/income-tax-planning?q="
    Then I should see "No results found"

  Scenario: A user list the records available to them
    Given I am assigned to unit "RefId1"
    When I am on "/tam/income-tax-planning?q="
    Then I should see the following table portion:
      | Unit Name    | Tax Type   |
      | Legal Unit 1 | Tax Type 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tam/income-tax-planning?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/income-tax-planning/new"
    And the "Income Tax Planning" form field "Period" should be "Period 2012"

  Scenario: A user with the required authorisation creates a new Income Tax Planning record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/income-tax-planning/new"
    When I fill in the "Income Tax Planning" form with:
      | Unit              | Legal Unit 1 |
      | Tax Rate          | 5            |
      | Period            | Period 2012  |
      | Planning Period   | 2016         |
      | Tax Type          | Tax Type 1   |
      | Profit Before Tax | 1            |
      | Temp Diff         | 0            |
      | Perm Diff         | 1            |
      | Description       | ITP 3        |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tam/income-tax-planning/3/edit"
    And I should see a success message
    And the "Income Tax Planning" form has the values:
      | Unit              | RefId1 - Legal Unit 1 |
      | Tax Rate          | 5.0000                |
      | Period            | Period 2012           |
      | Planning Period   | 2016                  |
      | Tax Type          | Tax Type 1            |
      | Profit Before Tax | 1                     |
      | Temp Diff         | 0                     |
      | Perm Diff         | 1                     |
      | Description       | ITP 3                 |

  Scenario: A user with the required authorisation updating a Income Tax Planning record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/income-tax-planning/1/edit"
    When I fill in the "Income Tax Planning" form with:
      | Planning Period | 2018 |
      | Temp Diff       | 1    |
      | Perm Diff       | 1    |
    And I press "Save"
    Then I should be on "/tam/income-tax-planning/1/edit"
    And I should see a success message

  Scenario: A user with the required authorisation deleting a Income Tax Planning record
    Given I am assigned to unit "RefId2"
    And I am on "/tam/income-tax-planning/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/tam/income-tax-planning"
    And I should see a success message
    And I should not see "Legal Unit 2"

  Scenario: A user with the required authorisation tries to delete a income tax planning where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/income-tax-planning/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: A user with the required authorisation tries to create a income tax planning where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    And I am on "/tam/income-tax-planning/new"
    When I fill in the "Income Tax Planning" form with:
      | Unit              | Legal Unit 1 |
      | Tax Rate          | 5            |
      | Period            | Period 2014  |
      | Planning Period   | 2016         |
      | Tax Type          | Tax Type 1   |
      | Profit Before Tax | 1            |
      | Description       | ITP 3        |
      | Temp Diff         | 5            |
      | Perm Diff         | 10           |
    And I press "Save"
    Then I should see an error message

  Scenario: A user with the required authorisation tries to edit a income tax planning where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/income-tax-planning/2/edit"
    Then the "Save" button in "Page Controls" should be disabled
