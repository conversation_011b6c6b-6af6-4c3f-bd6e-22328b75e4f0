@clear-database
Feature: Income Tax Planning Authorization
  As a user with no authorization to the TAM Module
  I should have no access to any feature of Income Tax Planning Module

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                         | Initial Status | Transitions             |
      | Income Tax Planning Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id              | Workflow                     |
      | tam_income_tax_planning | Income Tax Planning Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Income Tax Planning:
      | Unit   | Period      | Tax Rate | Tax Type   | Planning Period | Profit before tax | Description |
      | RefId1 | Period 2012 | 0.05     | Tax Type 1 | 2012            | 2                 | ITP 1       |
    And I am logged in

  Scenario: A User without TAM Income Tax Planning rights tries to list the Income Tax Planning records
    When I go to "/tam/income-tax-planning?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Income Tax Planning rights tries to edit an Income Tax Planning record
    When I go to "/tam/income-tax-planning/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Income Tax Planning rights tries to create a new Income Tax Planning record
    When I go to "/tam/income-tax-planning/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the TAM Income Tax Planning creation form
    Given the following Authorization:
      | Name                           | Item                    | Rights |
      | Income Tax Planing Read Access | TAM_INCOME_TAX_PLANNING | READ   |
    And I have the authorization "Income Tax Planing Read Access"
    When I go to "/tam/income-tax-planning/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Income Tax Planning records
    Given the following Authorization:
      | Name                           | Item                    | Rights |
      | Income Tax Planing Read Access | TAM_INCOME_TAX_PLANNING | READ   |
    And I have the authorization "Income Tax Planing Read Access"
    When I go to "/tam/income-tax-planning?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
