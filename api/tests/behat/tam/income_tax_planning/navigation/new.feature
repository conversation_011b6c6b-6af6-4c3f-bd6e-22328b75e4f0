@clear-database
Feature: Income Tax Planning Navigation - New
  In order to manage Income Tax Planning
  As a user allowed to the TAM Income Tax Planning
  I should be able to navigate through the Income Tax Planning pages

  Background:
    Given the following Authorization:
      | Name                            | Item                    | Rights       |
      | Income Tax Planning Full Access | TAM_INCOME_TAX_PLANNING | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                         | Initial Status | Transitions |
      | Income Tax Planning Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id              | Workflow                     |
      | tam_income_tax_planning | Income Tax Planning Workflow |
    And I have the authorization "Income Tax Planning Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/income-tax-planning?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/income-tax-planning/new"
