@clear-database
Feature: Transfer Pricing Bulk Edit
  In order to bulk edit Transfer Pricing records
  As a User with the required authorisation
  I should be able to perform bulk edit on Transfer Pricing records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                      | Initial Status | Transitions             |
      | Transfer Pricing Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | tam_transfer_pricing | Transfer Pricing Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Pricing Method:
      | Name             |
      | Pricing Method 1 |
    And the following Transaction Type:
      | Name               |
      | Transaction Type 1 |
      | Transaction Type 2 |
      | Transaction Type 3 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following TAM Transfer Pricing:
      | Status | Unit   | Partner Unit | Period      | Description        | Tax Year | Transaction Type   | Withholding Tax Payable | Pricing Method   | Amount |
      | open   | RefId1 | RefId2       | Period 2012 | Transfer Pricing 1 | 2014     | Transaction Type 1 | 20                      | Pricing Method 1 | 20     |
      | open   | RefId2 | RefId1       | Period 2013 | Transfer Pricing 2 | 2015     | Transaction Type 2 | 20                      | Pricing Method 1 | 20     |
      | done   | RefId3 | RefId3       | Period 2013 | Transfer Pricing 3 | 2015     | Transaction Type 3 | 20                      | Pricing Method 1 | 20     |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given the following Authorization:
      | Name                      | Item                 | Rights |
      | Tam Transfer Pricing Read | TAM_TRANSFER_PRICING | READ   |
    And I have the authorization "Tam Transfer Pricing Read"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tam/transfer-pricing?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one transfer pricing is selected
    Given the following Authorization:
      | Name                             | Item                 | Rights               |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | READ, UPDATE, DELETE |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/transfer-pricing?q="
    When I check the checkbox on "Transaction Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/transfer-pricing/1/edit"

  Scenario: A user can bulk edit transfer pricing
    Given the following Authorization:
      | Name                             | Item                 | Rights               |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | READ, UPDATE, DELETE |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tam/transfer-pricing?q="
    When I check the checkbox on "Transaction Type 1" table row for bulk action
    And I check the checkbox on "Transaction Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-transfer-pricing/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 2000 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/transfer-pricing"
    And I should see a success message

  Scenario: A user can not bulk edit transfer pricing because the entered values are invalid
    Given the following Authorization:
      | Name                             | Item                 | Rights               |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | READ, UPDATE, DELETE |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tam/transfer-pricing?q="
    When I check the checkbox on "Transaction Type 1" table row for bulk action
    And I check the checkbox on "Transaction Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-transfer-pricing/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 9999 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given the following Authorization:
      | Name                             | Item                 | Rights               |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | READ, UPDATE, DELETE |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                  |
      | Description disabled | done     | Transfer Pricing Workflow |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And I am on "/tam/transfer-pricing?q="
    When I check the checkbox on "Transaction Type 1" table row for bulk action
    And I check the checkbox on "Transaction Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-transfer-pricing/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
