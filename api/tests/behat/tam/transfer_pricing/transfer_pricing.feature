@clear-database
Feature: Transfer Pricing
  In order to manage Transfer Pricing records
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Transfer Pricing records

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                      | Initial Status | Transitions             |
      | Transfer Pricing Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | tam_transfer_pricing | Transfer Pricing Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Pricing Method:
      | Name             |
      | Pricing Method 1 |
    And the following Transaction Type:
      | Name               |
      | Transaction Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TAM Transfer Pricing:
      | Unit   | Partner Unit | Period      | Description        | Tax Year | Transaction Type   | Withholding Tax Payable | Pricing Method   | Amount |
      | RefId1 | RefId2       | Period 2012 | Transfer Pricing 1 | 2014     | Transaction Type 1 | 20                      | Pricing Method 1 | 20     |
      | RefId2 | RefId1       | Period 2013 | Transfer Pricing 2 | 2015     | Transaction Type 1 | 20                      | Pricing Method 1 | 20     |
    And the following Authorization:
      | Name                             | Item                 | Rights                       |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on "/tam/transfer-pricing?q="
    Then I should see "No results found"

  Scenario: A user with the required authorisation visits list page
    Given I am assigned to unit "RefId1"
    When I am on "/tam/transfer-pricing?q="
    Then I should see the following table portion:
      | Unit Name    | Transaction Type   |
      | Legal Unit 1 | Transaction Type 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tam/transfer-pricing?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/transfer-pricing/new"
    And the "Transfer Pricing" form field "Period" should be "Period 2012"

  Scenario: A user with the required authorisation creates a new Transfer Pricing record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/transfer-pricing/new"
    When I fill in the "Transfer Pricing" form with:
      | Period                  | Period 2012        |
      | Unit                    | Legal Unit 1       |
      | Partner Unit            | Legal Unit 2       |
      | Documentation Required  | Yes                |
      | Documentation Available | No                 |
      | Tax Year                | 2016               |
      | Transaction Type        | Transaction Type 1 |
      | Withholding Tax Payable | 20                 |
      | Amount                  | 50                 |
      | Pricing Method          | Pricing Method 1   |
      | Description             | Transfer Pricing 3 |

    And I click the "Save" button in "Page Controls"
    Then I should be on "/tam/transfer-pricing/3/edit"
    And I should see a success message

  Scenario: A user with the required authorisation updating a Transfer Pricing record
    Given I am assigned to unit "RefId1"
    And I am on "/tam/transfer-pricing/1/edit"
    When I fill in the "Transfer Pricing" form with:
      | Description | This is a description |
    And I press "Save"
    Then I should be on "/tam/transfer-pricing/1/edit"
    And I should see a success message

  Scenario: A user with the required authorisation deleting a Transfer Pricing record
    Given I am assigned to unit "RefId2"
    And I am on "/tam/transfer-pricing/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/tam/transfer-pricing"
    And I should see a success message
    And I should not see "Legal Unit 2"

  Scenario: A user with the required authorisation tries to delete a transfer pricing where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/transfer-pricing/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: A user with the required authorisation tries to create a transfer pricing where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    And I am on "/tam/transfer-pricing/new"
    When I fill in the "Transfer Pricing" form with:
      | Unit                    | Legal Unit 1       |
      | Partner Unit            | Legal Unit 2       |
      | Period                  | Period 2014        |
      | Documentation Required  | Yes                |
      | Documentation Available | No                 |
      | Tax Year                | 2016               |
      | Transaction Type        | Transaction Type 1 |
      | Withholding Tax Payable | 20                 |
      | Amount                  | 50                 |
      | Pricing Method          | Pricing Method 1   |
      | Description             | Transfer Pricing 3 |
    And I press "Save"
    Then I should see an error message

  Scenario: A user with the required authorisation tries to edit a transfer pricing where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tam/transfer-pricing/2/edit"
    Then the "Save" button in "Page Controls" should be disabled
