@clear-database
Feature: Transfer Pricing Authorization
  As a user with no authorization to the TAM Module
  I should have no access to any feature of Transfer Pricing Module

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                      | Initial Status | Transitions             |
      | Transfer Pricing Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | tam_transfer_pricing | Transfer Pricing Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And the following Pricing Method:
      | Name             |
      | Pricing Method 1 |
    And the following Transaction Type:
      | Name               |
      | Transaction Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TAM Transfer Pricing:
      | Unit   | Partner Unit | Period      | Description        | Tax Year | Transaction Type   | Withholding Tax Payable | Pricing Method   |
      | RefId1 | RefId2       | Period 2012 | Transfer Pricing 1 | 2014     | Transaction Type 1 | 20                      | Pricing Method 1 |
      | RefId2 | RefId1       | Period 2013 | Transfer Pricing 2 | 2015     | Transaction Type 1 | 20                      | Pricing Method 1 |
    And I am logged in

  Scenario: A User without TAM Transfer Pricing rights tries to list the Transfer Pricing records
    When I go to "/tam/transfer-pricing?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Transfer Pricing rights tries to edit an Transfer Pricing record
    When I go to "/tam/transfer-pricing/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Transfer Pricing rights tries to create a new Transfer Pricing record
    When I go to "/tam/transfer-pricing/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the TAM Transfer Pricing creation form
    Given the following Authorization:
      | Name                      | Item                 | Rights |
      | Tam Transfer Pricing Read | TAM_TRANSFER_PRICING | READ   |
    And I have the authorization "Tam Transfer Pricing Read"
    When I go to "/tam/transfer-pricing/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Transfer Pricing records
    Given the following Authorization:
      | Name                      | Item                 | Rights |
      | Tam Transfer Pricing Read | TAM_TRANSFER_PRICING | READ   |
    And I have the authorization "Tam Transfer Pricing Read"
    When I go to "/tam/transfer-pricing?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
