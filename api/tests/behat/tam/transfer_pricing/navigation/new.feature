@clear-database
Feature: Transfer Pricing Navigation - New
  In order to manage Transfer Pricing
  As a user allowed to the TAM Transfer Pricing
  I should be able to navigate through the Transfer Pricing pages

  Background:
    Given the following Authorization:
      | Name                             | Item                 | Rights       |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                      | Initial Status | Transitions |
      | Transfer Pricing Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | tam_transfer_pricing | Transfer Pricing Workflow |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/transfer-pricing?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/transfer-pricing/new"
