@clear-database
Feature: Transfer Pricing Navigation - Edit
  In order to manage Transfer Pricing
  As a user allowed to the TAM Transfer Pricing
  I should be able to navigate through the Transfer Pricing pages

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                      | Initial Status | Transitions |
      | Transfer Pricing Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id           | Workflow                  |
      | tam_transfer_pricing | Transfer Pricing Workflow |
    And the following Pricing Method:
      | Name             |
      | Pricing Method 1 |
    And the following Transaction Type:
      | Name               |
      | Transaction Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
      | RefId2 | Legal Unit 2 |
    And the following TAM Transfer Pricing:
      | Unit   | Partner Unit | Period      | Description        | Transaction Type   | Pricing Method   |
      | RefId1 | RefId2       | Period 2015 | Transfer Pricing 1 | Transaction Type 1 | Pricing Method 1 |
    And the following Authorization:
      | Name                             | Item                 | Rights               |
      | Tam Transfer Pricing Full Access | TAM_TRANSFER_PRICING | READ, UPDATE, DELETE |
    And I have the authorization "Tam Transfer Pricing Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/transfer-pricing?q="
    When I click "Edit" on the table row for "Transaction Type 1"
    Then I should be on "/tam/transfer-pricing/1/edit"
