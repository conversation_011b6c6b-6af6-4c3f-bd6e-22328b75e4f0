@clear-database
Feature: Tax Assessment Status Bulk Edit
  In order to manage Tax Assessment Status records
  As a User with the required authorisation
  I should be able to perform bulk edit on Tax Assessment Status records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
      | Tax Type 2 |
      | Tax Type 3 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                           | Initial Status | Transitions             |
      | Tax Assessment Status Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                | Workflow                       |
      | tam_tax_assessment_status | Tax Assessment Status Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country  |
      | RefId1 | Legal Unit 1 | TEU      | Yemen    |
      | RefId2 | Legal Unit 2 | EUR      | Zambia   |
      | RefId3 | Legal Unit 3 | EUR      | Zimbabwe |
    And the following TAM Tax Assessment Status:
      | Status | Unit   | Period      | Tax Type   | Description | Tax Year |
      | open   | RefId1 | Period 2012 | Tax Type 1 | TAS 1       | 2014     |
      | open   | RefId2 | Period 2013 | Tax Type 2 | TAS 2       | 2015     |
      | done   | RefId3 | Period 2013 | Tax Type 3 | TAS 3       | 2015     |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given the following Authorization:
      | Name                              | Item                      | Rights |
      | Tax Assessment Status Read Access | TAM_TAX_ASSESSMENT_STATUS | READ   |
    And I have the authorization "Tax Assessment Status Read Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-assessment-status?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one tax assessment status is selected
    Given the following Authorization:
      | Name                              | Item                      | Rights               |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tam/tax-assessment-status?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tam/tax-assessment-status/1/edit"

  Scenario: A user can bulk edit tax assessment status
    Given the following Authorization:
      | Name                              | Item                      | Rights               |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-assessment-status?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-assessment-status/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 2000 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tam/tax-assessment-status"
    And I should see a success message

  Scenario: A user can not bulk edit tax assessment status because the entered values are invalid
    Given the following Authorization:
      | Name                              | Item                      | Rights               |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am on "/tam/tax-assessment-status?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-assessment-status/edit?selection=1%2C2"
    When I enable the "Tax Year" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Tax Year | 9999 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given the following Authorization:
      | Name                              | Item                      | Rights               |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | READ, UPDATE, DELETE |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                       |
      | Description disabled | done     | Tax Assessment Status Workflow |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId3"
    And I am on "/tam/tax-assessment-status?q="
    When I check the checkbox on "Tax Type 1" table row for bulk action
    And I check the checkbox on "Tax Type 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tam-tax-assessment-status/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
