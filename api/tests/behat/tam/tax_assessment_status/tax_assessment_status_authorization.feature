@clear-database
Feature: Tax Assessment Status Authorization
  As a user with no authorization to the TAM Module
  I should have no access to any feature of Tax Assessment Status Module

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                           | Initial Status | Transitions             |
      | Tax Assessment Status Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                | Workflow                       |
      | tam_tax_assessment_status | Tax Assessment Status Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following TAM Tax Assessment Status:
      | Unit   | Period      | Tax Type   | Description | Tax Year |
      | RefId1 | Period 2012 | Tax Type 1 | TAS 1       | 2014     |
    And I am logged in

  Scenario: A User without TAM Tax Assessment Status rights tries to list the Tax Assessment Status records
    When I go to "/tam/tax-assessment-status?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Assessment Status rights tries to edit an Tax Assessment Status record
    When I go to "/tam/tax-assessment-status/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TAM Tax Assessment Status rights tries to create a new Tax Assessment Status record
    When I go to "/tam/tax-assessment-status/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the TAM Tax Assessment Status creation form
    Given the following Authorization:
      | Name                              | Item                      | Rights |
      | Tax Assessment Status Read Access | TAM_TAX_ASSESSMENT_STATUS | READ   |
    And I have the authorization "Tax Assessment Status Read Access"
    When I go to "/tam/tax-assessment-status/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Assessment Status records
    Given the following Authorization:
      | Name                              | Item                      | Rights |
      | Tax Assessment Status Read Access | TAM_TAX_ASSESSMENT_STATUS | READ   |
    And I have the authorization "Tax Assessment Status Read Access"
    When I go to "/tam/tax-assessment-status?q="
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
