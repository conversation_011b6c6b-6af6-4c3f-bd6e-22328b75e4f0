@clear-database
Feature: Tax Assessment Status Navigation - Menu
  In order to manage Tax Assessment Status
  As a user allowed to the TAM Tax Assessment Status
  I should be able to navigate through the Tax Assessment Status pages

  Background:
    Given the following Authorization:
      | Name                              | Item                      | Rights               |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Assessment Status" in the menu under "TAM"
    Then I should be on "/tam/tax-assessment-status"
