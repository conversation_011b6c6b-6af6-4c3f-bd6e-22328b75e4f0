@clear-database
Feature: Tax Assessment Status Navigation - Edit
  In order to manage Tax Assessment Status
  As a user allowed to the TAM Tax Assessment Status
  I should be able to navigate through the Tax Assessment Status pages

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                           | Initial Status | Transitions |
      | Tax Assessment Status Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                | Workflow                       |
      | tam_tax_assessment_status | Tax Assessment Status Workflow |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TAM Tax Assessment Status:
      | Unit   | Period      | Tax Type   | Description |
      | RefId1 | Period 2015 | Tax Type 1 | TAS 1       |
    And the following Authorization:
      | Name                              | Item                      | Rights               |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | READ, UPDATE, DELETE |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/tax-assessment-status?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/tax-assessment-status/1/edit"
