@clear-database
Feature: Tax Assessment Status Navigation - New
  In order to manage Tax Assessment Status
  As a user allowed to the TAM Tax Assessment Status
  I should be able to navigate through the Tax Assessment Status pages

  Background:
    Given the following Authorization:
      | Name                              | Item                      | Rights       |
      | Tax Assessment Status Full Access | TAM_TAX_ASSESSMENT_STATUS | CREATE, READ |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                           | Initial Status | Transitions |
      | Tax Assessment Status Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                | Workflow                       |
      | tam_tax_assessment_status | Tax Assessment Status Workflow |
    And I have the authorization "Tax Assessment Status Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tam/tax-assessment-status?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tam/tax-assessment-status/new"
