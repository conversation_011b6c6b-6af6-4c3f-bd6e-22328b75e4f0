@clear-database
Feature: Contract Authorization
  As a user with no authorization to the CM Module
  I should have no access to any feature of Contract Module

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name              | Initial Status | Transitions |
      | Contract Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And I am logged in

  Scenario: A User without CM Contract rights tries to list the Contract records
    When I go to "/contract-management/contract"
    Then I should see "403 Access Denied"

  Scenario: A User without CM Contract rights tries to create a new Contract record
    When I go to "/contract-management/contract/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the CM Contract creation form
    Given the following Authorization:
      | Name          | Item        | Rights |
      | Contract Read | CM_CONTRACT | READ   |
    And I have the authorization "Contract Read"
    When I go to "/contract-management/contract/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Contract records
    Given the following Authorization:
      | Name          | Item        | Rights |
      | Contract Read | CM_CONTRACT | READ   |
    And I have the authorization "Contract Read"
    When I go to "/contract-management/contract"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
