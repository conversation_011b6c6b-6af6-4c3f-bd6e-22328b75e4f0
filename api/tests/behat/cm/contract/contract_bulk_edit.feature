@clear-database
Feature: Contract Bulk Edit
  In order to manage Contract records
  As a User with the required authorisation
  I should be able to perform bulk edit on Contract records that are not completed

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name              | Initial Status | Transitions             |
      | Contract Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country  |
      | RefId1 | Legal Unit 1 | TEU      | Yemen    |
      | RefId2 | Legal Unit 2 | EUR      | Zambia   |
      | RefId3 | Legal Unit 3 | EUR      | Zimbabwe |
    And the following Contract Type:
      | Name            |
      | Contract Type 1 |
    And the following CM Contract:
      | Status | Unit   | Name       | Partner Is Third Party | Partner Unit | Expiry Date | Contract Type   |
      | open   | RefId1 | Contract 1 | No                     | RefId2       | 09.11.2015  | Contract Type 1 |
      | open   | RefId2 | Contract 2 | No                     | RefId3       | 09.11.2015  | Contract Type 1 |
      | done   | RefId3 | Contract 3 | No                     | RefId2       | 09.11.2015  | Contract Type 1 |
    And the following Authorization:
      | Name          | Item        | Rights               |
      | Contract Read | CM_CONTRACT | READ                 |
      | Contract Full | CM_CONTRACT | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "Contract Read"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/contract-management/contract"
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Contract is selected
    Given I have the authorization "Contract Full"
    And I am assigned to unit "RefId1"
    And I am on "/contract-management/contract"
    When I check the checkbox on "Contract 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/contract-management/contract/1/edit"

  Scenario: A user can bulk edit Contract
    Given I have the authorization "Contract Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/contract-management/contract"
    When I check the checkbox on "Contract 1" table row for bulk action
    And I check the checkbox on "Contract 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/cm-contract/edit?selection=1%2C2"
    When I enable the "Contract Type" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Contract Type | Contract Type 1 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/contract-management/contract"
    And I should see a success message

  Scenario: A user can not bulk edit Contract because the entered values are invalid
    Given I have the authorization "Contract Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/contract-management/contract"
    When I check the checkbox on "Contract 1" table row for bulk action
    And I check the checkbox on "Contract 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/cm-contract/edit?selection=1%2C2"
    When I enable the "Reminder Date" field in "Bulk Edit Form"
    When I enable the "Expiry Date" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Reminder Date | 11.11.2015 |
      | Expiry Date   | 09.11.2015 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "Contract Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow          |
      | Description disabled | done     | Contract Workflow |
    And I am on "/contract-management/contract"
    When I check the checkbox on "Contract 1" table row for bulk action
    And I check the checkbox on "Contract 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/cm-contract/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
