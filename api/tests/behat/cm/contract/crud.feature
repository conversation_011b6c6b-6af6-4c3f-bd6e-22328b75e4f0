@clear-database
Feature: Contract Crud
  In order to manage Contract records
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Contract records

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name              | Initial Status | Transitions             |
      | Contract Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following Contract Type:
      | Name            |
      | Contract Type 1 |
    And the following CM Contract:
      | Unit   | Name       | Status | Contract Type   |
      | RefId1 | Contract 1 | open   | Contract Type 1 |
      | RefId2 | Contract 2 | done   | Contract Type 1 |
    And the following Authorization:
      | Name                 | Item        | Rights                       |
      | Contract Full Access | CM_CONTRACT | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Contract Full Access"
    And I am logged in

  Scenario: A user visits the list page
    When I am on "/contract-management/contract"
    Then I should see "No results found"

  Scenario: A user visits the list page
    Given I am assigned to unit "RefId1"
    When I am on "/contract-management/contract"
    Then I should see the following table:
      | Unit Name    | Unit Country |
      | Legal Unit 1 | Yemen        |

  Scenario: A user creates a new Contract record
    Given I am assigned to unit "RefId1"
    And I am on "/contract-management/contract/new"
    When I fill in the "Contract" form with:
      | Unit                   | Legal Unit 1    |
      | Name                   | Contract 3      |
      | Contract Type          | Contract Type 1 |
      | Identification Code    | CM C3           |
      | Expiry Date            | 01.01.2014      |
      | Partner Unit           | Legal Unit 2    |
      | Partner is Third Party | No              |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/contract-management/contract/3/edit"
    And I should see a success message

  Scenario: A user updating a Contract record
    Given I am assigned to unit "RefId1"
    And I am on "/contract-management/contract/1/edit"
    When I fill in the "Contract" form with:
      | Unit                   | Legal Unit 1           |
      | Name                   | This is a updated name |
      | Contract Type          | Contract Type 1        |
      | Identification Code    | CM C3                  |
      | Expiry Date            | 01.01.2014             |
      | Partner Unit           | Legal Unit 2           |
      | Partner is Third Party | No                     |
    And I press "Save"
    Then I should be on "/contract-management/contract/1/edit"
    And I should see a success message

  Scenario: A user deleting a Contract record
    Given I am assigned to unit "RefId1"
    And I am on "/contract-management/contract/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/contract-management/contract"
    And I should see a success message
    And I should not see "Legal Unit 2"
