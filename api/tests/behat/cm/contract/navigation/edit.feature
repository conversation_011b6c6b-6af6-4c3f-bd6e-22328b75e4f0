@clear-database
Feature: Contract Navigation - Edit
  In order to manage Contract
  As a user allowed to the CM Contract
  I should be able to navigate to the Contract edit page

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name              | Initial Status | Transitions |
      | Contract Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following CM Contract:
      | Status | Unit   | Name       |
      | open   | RefId1 | Contract 1 |
    And the following Authorization:
      | Name                 | Item        | Rights               |
      | Contract Full Access | CM_CONTRACT | READ, UPDATE, DELETE |
    And I have the authorization "Contract Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/contract-management/contract"
    When I click "Edit" on the table row for "Contract 1"
    Then I should be on "/contract-management/contract/1/edit"
