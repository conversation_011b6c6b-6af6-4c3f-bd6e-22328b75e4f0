@clear-database
Feature: Contract Navigation - New
  In order to manage Contract
  As a user allowed to the CM Contract
  I should be able to navigate through the Contract pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name              | Initial Status | Transitions |
      | Contract Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And the following Authorization:
      | Name                 | Item        | Rights       |
      | Contract Full Access | CM_CONTRACT | CREATE, READ |
    And I have the authorization "Contract Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/contract-management/contract"
    When I click the "New" button in "Page Controls"
    Then I should be on "/contract-management/contract/new"
