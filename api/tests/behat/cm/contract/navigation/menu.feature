@clear-database
Feature: Contract Navigation - Menu
  In order to manage Contract
  As a user allowed to the CM Contract
  I should be able to navigate through the Contract menu

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name              | Initial Status | Transitions |
      | Contract Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And the following Authorization:
      | Name                 | Item        | Rights               |
      | Contract Full Access | CM_CONTRACT | READ, UPDATE, DELETE |
    And I have the authorization "Contract Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Contracts" in the menu under "Tools"
    Then I should be on "/contract-management/contract"
