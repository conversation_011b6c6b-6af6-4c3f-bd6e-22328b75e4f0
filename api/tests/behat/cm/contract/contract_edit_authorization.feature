@clear-database
Feature: Contract edit authorization
  As a user with no authorization to the CM Module
  I should have no access to any feature of Contract Module

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name              | Initial Status | Transitions             |
      | Contract Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id  | Workflow          |
      | cm_contract | Contract Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following CM Contract:
      | Status | Unit   | Name       |
      | open   | RefId1 | Contract 1 |

  Scenario: A User without CM Contract rights tries to edit an Contract record
    Given I am logged in
    When I go to "/contract-management/contract/1/edit"
    Then I should see "403 Access Denied"

  Scenario: An admin without CM Contract rights tries to edit an Contract record
    Given I am logged in as an administrator
    When I go to "/contract-management/contract/1/edit"
    Then I should see "403 Access Denied"
