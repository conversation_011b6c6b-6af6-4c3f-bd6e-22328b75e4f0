@clear-database
Feature: Main Business Activity Navigation - Edit
  In order to manage Main Business Activity
  As a user allowed to the TPM Main Business Activity
  I should be able to navigate to the Main Business Activity edit page

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Business Activity:
      | Name                                       |
      | Holding shares or other equity instruments |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name             | Initial Status | Transitions |
      | CBC MBA Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                 | Workflow         |
      | tpm_main_business_activity | CBC MBA Workflow |
    And the following Unit:
      | Ref Id     | Name |
      | RefId Unit | Unit |
    And the following TPM Main Business Activity:
      | Unit       | Period      | Description | Business Activity                          |
      | RefId Unit | Period 2015 | CBC MBA 1   | Holding shares or other equity instruments |
    And the following Authorization:
      | Name                               | Item                       | Rights               |
      | Main Business Activity Full Access | TPM_MAIN_BUSINESS_ACTIVITY | READ, UPDATE, DELETE |
    And I have the authorization "Main Business Activity Full Access"
    And I am assigned to unit "RefId Unit"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tpm/main-business-activity?q="
    When I click "Edit" on the table row for "RefId Unit"
    Then I should be on "/tpm/main-business-activity/1/edit"
