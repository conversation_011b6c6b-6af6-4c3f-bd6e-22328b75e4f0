@clear-database
Feature: Main Business Activity Navigation - Menu
  In order to manage Main Business Activity
  As a user allowed to the TPM Main Business Activity
  I should be able to navigate through the Main Business Activity menu

  Background:
    Given the following Authorization:
      | Name                               | Item                       | Rights               |
      | Main Business Activity Full Access | TPM_MAIN_BUSINESS_ACTIVITY | READ, UPDATE, DELETE |
    And I have the authorization "Main Business Activity Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Main Business Activities" in the menu under "TPM"
    Then I should be on "/tpm/main-business-activity"
