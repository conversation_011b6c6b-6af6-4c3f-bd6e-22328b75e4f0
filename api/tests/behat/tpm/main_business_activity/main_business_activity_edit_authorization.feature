@clear-database
Feature: Main Business Activity Authorization - Edit
  As a user with no authorization to the TPM Module
  I should have no access to any feature of Main Business Activity

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
    And the following Business Activity:
      | Name                                       |
      | Holding shares or other equity instruments |
      | Insurance                                  |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name             | Initial Status | Transitions     |
      | CBC MBA Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id                 | Workflow         |
      | tpm_main_business_activity | CBC MBA Workflow |
    And the following Unit:
      | Ref Id      | Name   |
      | RefId Unit  | Unit   |
      | RefId Unit2 | Unit 2 |
    And the following TPM Main Business Activity:
      | Unit       | Period      | Description | Business Activity                          |
      | RefId Unit | Period 2012 | CBC MBA 1   | Holding shares or other equity instruments |

  Scenario: An admin without TPM Main Business Activity rights tries to edit an Main Business Activity record
    Given I am logged in as an administrator
    When I go to "/tpm/main-business-activity/1/edit"
    Then I should see "403 Access Denied"
