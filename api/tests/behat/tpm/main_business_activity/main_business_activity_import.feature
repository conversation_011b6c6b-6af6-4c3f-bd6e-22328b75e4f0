@clear-database
Feature: Importing TPM Main Business Activity
  In order to import a Main Business Activity
  As a user allowed to the TPM Module
  I should be able to import records

  Background:
    Given the following Period:
      | Name | Start Date | End Date   |
      | 2024 | 01.01.2013 | 31.12.2013 |
    And the following System Setting:
      | Id                             | Value           |
      | security.file_upload_whitelist | ["text\/plain"] |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Business Activity:
      | Name  |
      | Other |
    And the following Status:
      | Type        | Name        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name             | Initial Status | Transitions |
      | CBC MBA Workflow | done           | Complete    |
    And the following Workflow Binding:
      | Binding Id                 | Workflow         |
      | tpm_main_business_activity | CBC MBA Workflow |
    And the following Unit:
      | Ref Id | Name |
      | CORP1  | Unit |
      | CORP2  | Unit |
    And the following Authorization:
      | Name                               | Item                       | Rights               |
      | Main Business Activity Full Access | TPM_MAIN_BUSINESS_ACTIVITY | READ, UPDATE, DELETE |
    And I have the authorization "Main Business Activity Full Access"
    And I am assigned to the following units:
      | CORP1 |
      | CORP2 |
    And I am logged in

  Scenario: A user imports a CSV file for Main Business Activity
    Given I am on "/imports/tpm-main-business-activity/start"
    Then I should see "Select a file to import"
    When I attach the file "mainBusinessActivity.csv" to "Select a file to import"
    And I click the "Import" button
    Then I should see "Confirm Import"
    When I click the "Import" button in the dialog
    Then the url should match "imports\/\d+"
    And I should see "Records 2"
    When I go to "/tpm/main-business-activity?q="
    Then I should see the following table:
      | ID | Status      | Period | Unit Ref. ID | Unit Name | Business Activity |
      | 2  | DONE        | 2024   | CORP2        | Unit      | Other             |
      | 1  | IN PROGRESS | 2024   | CORP1        | Unit      | Other             |
    When I click "Edit" on the table row for "CORP1"
    Then I should be on "/tpm/main-business-activity/1/edit"
    And I should see "Unwatch"

  Scenario: A user imports an XLS file for Main Business Activity
    Given I am on "/imports/tpm-main-business-activity/start"
    Then I should see "Select a file"
    When I attach the file "mainBusinessActivity.xls" to "Select a file to import"
    And I click the "Import" button
    Then I should see "Confirm Import"
    When I click the "Import" button in the dialog
    Then the url should match "imports\/\d+"
    And I should see "Records 1"
    When I go to "/tpm/main-business-activity?q="
    Then I should see the following table:
      | ID | Status      | Period | Unit Ref. ID | Unit Name | Business Activity |
      | 1  | IN PROGRESS | 2024   | CORP1        | Unit      | Other             |
    When I click "Edit" on the table row for "CORP1"
    Then I should be on "/tpm/main-business-activity/1/edit"
    And I should see "Unwatch"

  Scenario: A user imports an XLSX file with Main Business Activity
    Given I am on "/imports/tpm-main-business-activity/start"
    Then I should see "Select a file"
    When I attach the file "mainBusinessActivity.xlsx" to "Select a file to import"
    And I click the "Import" button
    Then I should see "Confirm Import"
    When I click the "Import" button in the dialog
    Then the url should match "imports\/\d+"
    And I should see "Records 1"
    When I go to "/tpm/main-business-activity?q="
    Then I should see the following table:
      | ID | Status      | Period | Unit Ref. ID | Unit Name | Business Activity |
      | 1  | IN PROGRESS | 2024   | CORP1        | Unit      | Other             |
    When I click "Edit" on the table row for "CORP1"
    Then I should be on "/tpm/main-business-activity/1/edit"
    And I should see "Unwatch"
