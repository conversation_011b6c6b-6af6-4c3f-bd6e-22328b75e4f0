@clear-database
Feature: Main Business Activity - Create, Read, Update, Delete and List
  In order to manage Main Business Activity records
  As a User with the required authorisation
  I should be able to perform create, read, update and delete actions on Main Business Activity records

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2013 | 31.12.2013 |
    And the following Business Activity:
      | Name                                       |
      | Holding shares or other equity instruments |
      | Insurance                                  |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name             | Initial Status | Transitions             |
      | CBC MBA Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                 | Workflow         |
      | tpm_main_business_activity | CBC MBA Workflow |
    And the following Unit:
      | Ref Id      | Name   |
      | RefId Unit  | Unit   |
      | RefId Unit2 | Unit 2 |
    And the following TPM Main Business Activity:
      | Unit        | Period      | Description | Business Activity                          |
      | RefId Unit  | Period 2012 | CBC MBA 1   | Holding shares or other equity instruments |
      | RefId Unit2 | Period 2013 | CBC MBA 2   | Insurance                                  |
    And the following Authorization:
      | Name                               | Item                       | Rights                       |
      | Main Business Activity Full Access | TPM_MAIN_BUSINESS_ACTIVITY | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Main Business Activity Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I go to "/tpm/main-business-activity?q="
    Then I should see "No results found"

  Scenario: A user list the records available to them
    Given I am assigned to unit "RefId Unit"
    When I go to "/tpm/main-business-activity?q="
    Then I should see the following table:
      | Unit Ref. ID | Unit Name |
      | RefId Unit   | Unit      |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tpm/main-business-activity?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/main-business-activity/new"
    And the "Main Business Activity" form field "Period" should be "Period 2012"

  Scenario: Creating a new record
    Given I am assigned to unit "RefId Unit"
    And I am on "/tpm/main-business-activity/new"
    When I fill in the "Main Business Activity" form with:
      | Unit              | Unit        |
      | Period            | Period 2012 |
      | Description       | CBC MBA 3   |
      | Business Activity | Insurance   |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tpm/main-business-activity/3/edit"
    And I should see a success message

  Scenario: Updating a record
    Given I am assigned to unit "RefId Unit"
    And I am on "/tpm/main-business-activity/1/edit"
    When I fill in the "Main Business Activity" form with:
      | Description | This is a description |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tpm/main-business-activity/1/edit"
    And I should see a success message

  Scenario: Deleting a record
    Given I am assigned to unit "RefId Unit2"
    And I am on "/tpm/main-business-activity/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/main-business-activity"
    And I should not see "Unit 2"

  Scenario: Delete a record where the period is closed
    Given I am assigned to unit "RefId Unit2"
    And The period "Period 2013" is closed
    When I go to "/tpm/main-business-activity/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: Creating a record where the period is closed fails
    Given I am assigned to unit "RefId Unit"
    And The period "Period 2014" is closed
    And I am on "/tpm/main-business-activity?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/main-business-activity/new"
    When I fill in the "Main Business Activity" form with:
      | Unit              | Unit                                       |
      | Period            | Period 2014                                |
      | Description       | CBC MBA 3                                  |
      | Business Activity | Holding shares or other equity instruments |
    And I click the "Save" button in "Page Controls"
    Then I should see an error message
