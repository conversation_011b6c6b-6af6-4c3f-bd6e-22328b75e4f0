@clear-database
Feature: Main Business Activity Bulk Edit
  In order to manage Main Business Activity records
  As a User with the required authorisation
  I should be able to perform bulk edit on Main Business Activity records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
    And the following Business Activity:
      | Name                                       |
      | Holding shares or other equity instruments |
      | Insurance                                  |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name             | Initial Status | Transitions             |
      | CBC MBA Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                 | Workflow         |
      | tpm_main_business_activity | CBC MBA Workflow |
    And the following Unit:
      | Ref Id | Name   |
      | RefId1 | Unit   |
      | RefId2 | Unit 2 |
      | RefId3 | Unit 3 |
    And the following TPM Main Business Activity:
      | Unit   | Period      | Description | Status | Business Activity                          |
      | RefId1 | Period 2012 | CBC MBA 1   | open   | Holding shares or other equity instruments |
      | RefId2 | Period 2013 | CBC MBA 2   | open   | Insurance                                  |
      | RefId3 | Period 2012 | CBC MBA 3   | done   | Insurance                                  |
    And the following Authorization:
      | Name                               | Item                       | Rights               |
      | Main Business Activity Read        | TPM_MAIN_BUSINESS_ACTIVITY | READ                 |
      | Main Business Activity Full Access | TPM_MAIN_BUSINESS_ACTIVITY | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "Main Business Activity Read"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    When I go to "/tpm/main-business-activity?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Main Business Activity is selected
    Given I have the authorization "Main Business Activity Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tpm/main-business-activity?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tpm/main-business-activity/1/edit"

  Scenario: A user can bulk edit Main Business Activity
    Given I have the authorization "Main Business Activity Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/main-business-activity?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-main-business-activity/edit?selection=1%2C2"
    When I enable the "Period" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Period | Period 2012 |
    And I click the "Save" button in "Page Controls"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tpm/main-business-activity"
    And I should see a success message

  Scenario: A user can not bulk edit Main Business Activity because the entered values are invalid
    Given I have the authorization "Main Business Activity Full Access"
    And The period "Period 2012" is closed
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/main-business-activity?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-main-business-activity/edit?selection=1%2C2"
    When I enable the "Period" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Period | Period 2012 |
    And I click the "Save" button in "Page Controls"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see an error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "Main Business Activity Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow         |
      | Description disabled | done     | CBC MBA Workflow |
    And I am on "/tpm/main-business-activity?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-main-business-activity/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
