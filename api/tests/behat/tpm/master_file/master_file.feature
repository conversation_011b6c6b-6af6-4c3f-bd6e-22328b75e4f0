@clear-database
Feature: Master Files
  In order to manage Master Files
  As a user allowed to the TPM Master Files
  I should be able to perform create, read, update and delete actions on Master File records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                     | Initial Status | Transitions             |
      | TPM Master File Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_master_file | TPM Master File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 1 | Period 2012 | Unit Hierarchy 1 |
      | Master File 2 | Period 2013 | Unit Hierarchy 1 |
      | Master File 3 | Period 2013 | Unit Hierarchy 1 |
    And the following Authorization:
      | Name                             | Item            | Rights                       |
      | Master File Access/Create Access | TPM_MASTER_FILE | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Master File Access/Create Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on the homepage
    When I click "Master File" in the menu under "TPM"
    Then I should be on "/tpm/master-file"
    And I should see "No results found"

  Scenario: A User lists Master File records where he has group based permission
    Given I am assigned to group "Group 1"
    And user group "Group 1" has view permission to TPM Master File "Master File 3"
    When I am on "/tpm/master-file?q="
    Then I should see the following table:
      | Name          | Hierarchy        |
      | Master File 3 | Unit Hierarchy 1 |

  Scenario: A user lists master file records where he has permission
    Given I have view permission to TPM Master File "Master File 1"
    And I have view permission to TPM Master File "Master File 2"
    When I am on "/tpm/master-file?q="
    Then I should see the following table:
      | Name          | Hierarchy        |
      | Master File 2 | Unit Hierarchy 1 |
      | Master File 1 | Unit Hierarchy 1 |

  Scenario: A user creates a new Master File
    Given I am on "/tpm/master-file"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/master-file/new"
    When I fill in the "Master File" form with:
      | Name           | Master File 4    |
      | Unit Hierarchy | Unit Hierarchy 1 |
      | Period         | Period 2012      |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/master-file/4/edit"

  Scenario: A user creates a new Master File from document template
    Given the following Document Template:
      | Name            | Type        |
      | A Base Template | master-file |
    And there is a user named creator_user
    And there is a user named reader_user
    And the following User Group:
      | Name              |
      | Group For Writing |
    And reader_user has view permission to Document Template "A Base Template"
    And user group "Group For Writing" has view and edit permission to Document Template "A Base Template"
    And user creator_user has the authorization "Master File Access/Create Access"
    And I am logged in as creator_user
    And I am on "/tpm/master-file/new"
    When I fill in the "Master File" form with:
      | Name           | Master File 4    |
      | Unit Hierarchy | Unit Hierarchy 1 |
      | Period         | Period 2012      |
      | Base Template  | A Base Template  |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/master-file/4/edit"
    And TPM Master File "Master File 4" should have following user permission table defined:
      | name         | permissions                  |
      | creator_user | view, edit, delete and owner |
      | reader_user  | view                         |
    And TPM Master File "Master File 4" should have following group permission table defined:
      | name              | permissions   |
      | Group For Writing | view and edit |

  Scenario: Updating a Master File
    Given I have view, edit, delete and owner permission to TPM Master File "Master File 2"
    And I am on "/tpm/master-file?q="
    When I click "Edit" on the table row for "Master File 2"
    And I am on "/tpm/master-file/2/edit"
    When I fill in the "Master File" form with:
      | Name | Updated Name |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/master-file/2/edit"

  Scenario: Deleting a Master File from the configuration page
    Given I have view, edit, delete and owner permission to TPM Master File "Master File 1"
    And I am on "/tpm/master-file/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/master-file"
    And I should not see "Master File 1"

  Scenario: Deleting a Master File from the edit page
    Given I have view, edit and delete permission to TPM Master File "Master File 1"
    And I am on "/tpm/master-file/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/master-file"
    And I should not see "Master File 1"

  Scenario: When creating a new master file, the last filtered period is used to pre-populate the period field
    Given I am on "/tpm/master-file?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/master-file/new"
    And the "Master File" form field "Period" should be "Period 2012"

  Scenario: Assigning unauthorised units to master file document fails
    Given the following Unit:
      | Ref Id     | Name |
      | RefId Unit | Unit |
    And the following Unit Hierarchy Definition:
      | Unit Hierarchy   | Unit       | Parent Unit |
      | Unit Hierarchy 1 | RefId Unit |             |
    And I have view, edit, delete and owner permission to TPM Master File "Master File 1"
    And I am on "/tpm/master-file/1/edit"
    Then I should see "RefId Unit - Unit"
    When I check "RefId Unit - Unit"
    And I press "Save"
    Then I should see an error message
