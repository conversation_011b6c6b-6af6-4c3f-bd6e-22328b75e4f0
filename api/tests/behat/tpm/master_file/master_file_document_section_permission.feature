@clear-database
Feature: TPM Master File Section Permission
  As a user
  I should be able to edit TPM Master File Sections if I have the correct permissions

  Background:
    Given the following Status:
      | Type     | Name   |
      | OPEN     | open   |
      | COMPLETE | closed |
    And the following Transition:
      | Name     | Origin status | Destination status |
      | Start    | open          | closed             |
      | Complete | closed        | open               |
    And the following Workflow:
      | Name                     | Initial status | Transitions |
      | TPM Master File Workflow | open           | Start       |
    And the following Binding:
      | Binding Id      | Workflow                 |
      | tpm_master_file | TPM Master File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 1 | Period 2012 | Unit Hierarchy 1 |
    And the following TPM Master File Section:
      | Document      | Name                           | Order Position |
      | Master File 1 | First Section of Master File 1 | 1              |
    And the following Authorization:
      | Name                    | Item            | Rights                  |
      | Master File Full Access | TPM_MASTER_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Master File Full Access"
    And I am logged in

  Scenario: A user without manage permissions can not change the editable status of a section
    Given the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 2 | Period 2012 | Unit Hierarchy 1 |
    And the following TPM Master File Section:
      | Document      | Name                | Editable | Order Position |
      | Master File 2 | First Section Title | 1        | 1              |
    And I have view and edit permission to TPM Master File "Master File 2"
    When I am on "/tpm/master-file/2/edit-document"
    Then I should see "1 First Section Title"
    When I click on the document section actions dropdown
    Then I should not see text matching "Prevent Edits"

  Scenario: A user without write permissions can not update section
    Given I have view permission to TPM Master File "Master File 1"
    When I go to "/tpm/master-file/1/edit-document"
    Then the "Edit" button in the section controls should be disabled

  Scenario: A user without manage permissions can not update section title
    Given I have view and edit permission to TPM Master File "Master File 1"
    When I go to "/tpm/master-file/1/edit-document"
    Then I should see "1 First Section of Master File 1"
    When I click on the document section "Edit" action
    And the section title should be disabled

  Scenario: A user without manage permissions can not move, add nor remove a section
    Given I have view and edit permission to TPM Master File "Master File 1"
    When I go to "/tpm/master-file/1/edit-document"
    Then the "Move" button in the section controls should be disabled
    And the "Add Section After" button in the section controls should be disabled
    And the "Add Section Before" button in the section controls should be disabled
    And the "Add Subsection" button in the section controls should be disabled
    And the "Delete" button in the section controls should be disabled

  Scenario: A user without manage permissions can not change the require status of a section
    Given the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 2 | Period 2012 | Unit Hierarchy 1 |
    And the following TPM Master File Section:
      | Document      | Name                | Order Position | Required | Order Position |
      | Master File 2 | First Section Title | 1              | 1        | 1              |
    And I have view and edit permission to TPM Master File "Master File 2"
    When I am on "/tpm/master-file/2/edit-document"
    Then I should see "First Section Title"
    When I click on the document section actions dropdown
    Then I should not see text matching "Do Not Require"
