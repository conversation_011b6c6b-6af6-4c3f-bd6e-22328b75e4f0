@clear-database
Feature: Permissions for master files can be controlled via groups
  As a user
  I should have access to TPM Master File configuration if i belong to a group with permissions

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                     | Initial Status | Transitions |
      | TPM Master File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_master_file | TPM Master File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 1 | Period 2012 | Unit Hierarchy 1 |
    And the following Authorization:
      | Name                    | Item            | Rights       |
      | Master File Full Access | TPM_MASTER_FILE | READ, CREATE |
    And I have the authorization "Master File Full Access"
    And I am logged in

  Scenario: A user cannot write master file configurations if none of his groups has manage permission
    Given the following User Group:
      | Name        |
      | EMPTY_GROUP |
    And I am assigned to group "EMPTY_GROUP"
    When I go to "/tpm/master-file/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A user can edit a master file configuration if one of his groups has manage permission
    Given the following User Group:
      | Name       |
      | VIEW_GROUP |
    And user group "VIEW_GROUP" has view permission to TPM Master File "Master File 1"
    And I am assigned to group "VIEW_GROUP"
    When I go to "/tpm/master-file/1/edit"
    Then I should be on "/tpm/master-file/1/edit"
