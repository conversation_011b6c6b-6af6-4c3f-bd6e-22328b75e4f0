@clear-database
Feature: Permissions for master files can be controlled via groups
  As a user
  I should have access to TPM Master File content if i belong to a group with permissions

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                     | Initial Status | Transitions |
      | TPM Master File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_master_file | TPM Master File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 1 | Period 2012 | Unit Hierarchy 1 |
    And the following User Group:
      | Name                |
      | MF_READ_WRITE_GROUP |
    And user group "MF_READ_WRITE_GROUP" has view and edit permission to TPM Master File "Master File 1"
    And the following User:
      | Username          |
      | MasterFileManager |
    And MasterFileManager has view permission to TPM Master File "Master File 1"
    And the following Authorization:
      | Name                    | Item            | Rights                  |
      | Master File Full Access | TPM_MASTER_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Master File Full Access"
    And I am logged in

  Scenario: A user can read a master file content if one of his groups has read permission
    Given I am assigned to group "MF_READ_WRITE_GROUP"
    When I am on "/tpm/master-file/1/edit-document"
    Then I should be on "/tpm/master-file/1/edit-document"
