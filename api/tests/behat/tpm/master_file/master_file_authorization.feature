@clear-database
Feature: TPM Master File Authorization
  As a user with no authorization to the TPM Module
  I should have no access to any feature of TPM Master File

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                     | Initial Status | Transitions             |
      | TPM Master File Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_master_file | TPM Master File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 1 | Period 2012 | Unit Hierarchy 1 |
      | Master File 2 | Period 2013 | Unit Hierarchy 1 |
      | Master File 3 | Period 2013 | Unit Hierarchy 1 |
    And I am logged in

  Scenario: A User without TPM master file rights tries to list the Master File records
    When I go to "/tpm/master-file?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TPM master file rights tries to edit an Master File record
    When I go to "/tpm/master-file/2/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TPM master file rights tries to create a new Master File record
    When I go to "/tpm/master-file/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Master File creation form
    Given the following Authorization:
      | Name                    | Item            | Rights |
      | Master File Read Access | TPM_MASTER_FILE | READ   |
    And I have the authorization "Master File Read Access"
    When I go to "/tpm/master-file/new"
    Then I should see "403 Access Denied"
