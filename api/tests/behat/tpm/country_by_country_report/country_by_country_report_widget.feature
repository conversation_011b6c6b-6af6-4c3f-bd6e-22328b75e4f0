@clear-database
Feature: Using widgets in TPM Country by Country Reports
  In order to present document's data in its content
  As a user allowed to access this document
  I should be able to use widget functionality

  # This feature also verifies that supported document widgets are correctly found and recognized

  Background:
    Given the following Status:
      | Type     | Name |
      | OPEN     | open |
      | COMPLETE | done |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | done               |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | TPM Country by Country Report Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name           |
      | Unit Hierarchy |
    And the following Period:
      | Name        |
      | Period 2016 |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | German  |
    And the following TPM Country by Country Report:
      | Name                      | Period      | Unit Hierarchy | Units        |
      | Country by Country Report | Period 2016 | Unit Hierarchy | Legal Unit 1 |
    And the following Authorization:
      | Name                             | Item                          | Rights |
      | Country by Country Report Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ   |
    And I have the authorization "Country by Country Report Access"
    And I am assigned to unit "RefId1"
    And I am logged in
    And I have view and edit permission to TPM Country by Country Report "Country by Country Report"

  Scenario: Visiting a document that uses a widget to present data
    Given the following TPM Country by Country Report Section:
      | Document                  | Name      | Content                                                                                               | Order Position |
      | Country by Country Report | A Section | <widget>eyJuYW1lIjoiZmluYW5jaWFsLWRhdGEtc3VtbWFyeS10YWJsZSIsImlzQ29uZmlndXJhYmxlIjpmYWxzZX0=</widget> | 1              |
    And I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see text matching "Total For Germany"
    And I should not see text matching "The widget “financial-data-summary-table” does not exist"
