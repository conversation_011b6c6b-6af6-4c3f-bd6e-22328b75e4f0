@clear-database
Feature: Country by Country Report section management

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name                                   | Initial status | Transitions     |
      | TPM Country by Country Report Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name                |
      | Test Unit Hierarchy |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following Authorization:
      | Name                                  | Item                          | Rights                  |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ, CREATE, SUPERVISE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in

  Scenario: Add a new section to a document
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    And I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see "This document has no content"
    When I click the "Add First Section" button
    Then I should see "*** New section content ***"

  Scenario: Opening section information dialog
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Document                       | Name          | Order Position |
      | Test Country by Country Report | First Section | 1              |
    And I have view permission to TPM Country by Country Report "Test Country by Country Report"
    When I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see "First Section"
    When I click on the document section "Information" action
    Then I should see "Editable Yes"
    And I should see "Required No"
    And I should see "Included Yes"

  Scenario: Updating a section
    Given the following User:
      | Username |
      | admin    |
    And the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section
      | Document                       | Name          | Order Position |
      | Test Country by Country Report | First Section | 1              |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    When I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see "1 First Section"
    But I should not see "1 Updated Title"
    And I should not see "Save"
    When I click on the element with text "First Section" in element "[id='section-1']"
    Then I should see "Save"
    And I fill in the "document_section_1" form with:
      | name | Updated Title |
    And I click the "Save" button
    Then I should see "1 Updated Title"
    And I should not see "1 First Section"

  Scenario: Delete a section
    Given the following User:
      | Username |
      | admin    |
    And the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section
      | Document                       | Name         | Order Position |
      | Test Country by Country Report | Some Section | 1              |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    And I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see "Some Section"
    When I click on the document section "Delete" action
    Then I should see "Confirm Deletion"
    When I click the "Delete" button in the dialog
    Then I should not see "Some Section"

  Scenario: Required sections cannot be excluded
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Document                       | Name          | Order Position | Required |
      | Test Country by Country Report | First Section | 1              | 1        |
    And I have view and edit permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Exclude" button in the section controls should be disabled

  Scenario: Including a section
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Id | Document                       | Name          | Order Position | Required | Include |
      | 1  | Test Country by Country Report | First Section | 1              | 0        | 0       |
    And I have view and edit permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then I should see text matching "First Section"
    And I should not see text matching "1 First Section"
    And "First Section" section should be excluded
    And "First Section" section TOC link should be excluded
    When I click on the document section "Include" action
    Then I should see text matching "1 First Section"
    And "First Section" section should be included
    And "First Section" section TOC link should be included

  Scenario: Excluding a section
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Id | Document                       | Name          | Order Position | Required | Include |
      | 1  | Test Country by Country Report | First Section | 1              | 0        | 1       |
    And I have view and edit permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then I should see text matching "1 First Section"
    And "First Section" section should be included
    And "First Section" section TOC link should be included
    When I click on the document section "Exclude" action
    Then I should see text matching "First Section"
    And I should not see text matching "1 First Section"
    And "First Section" section should be excluded
    And "First Section" section TOC link should be excluded

  Scenario: Preventing section content changes
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Id | Document                       | Name          | Order Position | Editable |
      | 1  | Test Country by Country Report | First Section | 1              | 1        |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Edit" button in the section controls should be enabled
    When I click on the document section "Prevent Edits" action
    Then the "Edit" button in the section controls should be disabled

  Scenario: Allowing section content changes
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Id | Document                       | Name          | Order Position | Editable |
      | 1  | Test Country by Country Report | First Section | 1              | 0        |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Edit" button in the section controls should be disabled
    When I click on the document section "Allow Edits" action
    Then the "Edit" button in the section controls should be enabled

  Scenario: Making a section required
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Id | Document                       | Name          | Order Position | Required | Include |
      | 1  | Test Country by Country Report | First Section | 1              | 0        | 1       |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Exclude" button in the section controls should be enabled
    When I click on the document section "Require" action
    Then the "Exclude" button in the section controls should be disabled

  Scenario: Making a section optional
    Given the following TPM Country by Country Report:
      | Id | Name                           | Period      | Unit Hierarchy      | Country                     |
      | 1  | Test Country by Country Report | Period 2012 | Test Unit Hierarchy | Federal Republic of Germany |
    And the following TPM Country by Country Report Section:
      | Id | Document                       | Name          | Order Position | Required | Include |
      | 1  | Test Country by Country Report | First Section | 1              | 1        | 1       |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Test Country by Country Report"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Exclude" button in the section controls should be disabled
    When I click on the document section "Do Not Require" action
    Then the "Exclude" button in the section controls should be enabled
