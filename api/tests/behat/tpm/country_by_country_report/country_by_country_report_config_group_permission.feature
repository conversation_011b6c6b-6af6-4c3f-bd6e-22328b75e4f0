@clear-database
Feature: Permissions for Country by Country Reports can be controlled via groups
  As a user
  I should have access to Country by Country Report configuration if i belong to a group with permissions

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | TPM Country by Country Report Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following UserGroup:
      | Name    |
      | Group 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
    And the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country            |
      | Country by Country Report 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following Authorization:
      | Name                                  | Item                          | Rights         |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ, CREATE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in

  Scenario: A user cannot write Country by Country Report configurations if none of his groups has manage permission
    Given the following User Group:
      | Name        |
      | EMPTY_GROUP |
    And I am assigned to group "EMPTY_GROUP"
    When I go to "/tpm/country-by-country-report/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A user can edit a Country by Country Report configuration if one of his groups has manage permission
    Given the following User Group:
      | Name       |
      | VIEW_GROUP |
    And user group "VIEW_GROUP" has view permission to TPM Country by Country Report "Country by Country Report 1"
    And I am assigned to group "VIEW_GROUP"
    When I go to "/tpm/country-by-country-report/1/edit"
    Then I should be on "/tpm/country-by-country-report/1/edit"
