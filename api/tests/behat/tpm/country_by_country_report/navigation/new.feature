@clear-database
Feature: Country by Country Reports Navigation - New
  In order to manage Country by Country Reports
  As a user allowed to the TPM Country by Country Reports
  I should be able to navigate through the Country by Country Report pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | TPM Country by Country Report Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Authorization:
      | Name                                  | Item                          | Rights       |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ, CREATE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tpm/country-by-country-report?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/country-by-country-report/new"
