@clear-database
Feature: Country by Country Reports Navigation - Menu
  In order to manage Country by Country Reports
  As a user allowed to the TPM Country by Country Reports
  I should be able to navigate through the Country by Country Report pages

  Background:
    Given the following Authorization:
      | Name                                  | Item                          | Rights                  |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ, CREATE, SUPERVISE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Country by Country Report" in the menu under "TPM"
    Then I should be on "/tpm/country-by-country-report"
