@clear-database
Feature: Country by Country Reports Navigation - Edit
  In order to manage Country by Country Reports
  As a user allowed to the TPM Country by Country Reports
  I should be able to navigate through the Country by Country Report pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | TPM Country by Country Report Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           |
      | ZM          | Zambia    | Republic of Zambia |
    And the following Period:
      | Name        |
      | Period 2015 |
    And the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country            |
      | Country by Country Report 1 | Period 2015 | Unit Hierarchy 1 | Republic of Zambia |
    And the following Authorization:
      | Name                                  | Item                          | Rights                  |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ, CREATE, SUPERVISE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I have view and edit permission to TPM Country by Country Report "Country by Country Report 1"
    And I am on "/tpm/country-by-country-report?q="
    When I click "Edit" on the table row for "Country by Country Report 1"
    Then I should be on "/tpm/country-by-country-report/1/edit"
