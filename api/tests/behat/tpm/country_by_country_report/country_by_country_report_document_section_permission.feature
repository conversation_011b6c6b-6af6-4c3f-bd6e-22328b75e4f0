@clear-database
Feature: TPM Country by Country Report Section Permission
  As a user
  I should be able to edit TPM Country by Country Sections if I have the correct permissions

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                                   | Initial status | Transitions |
      | TPM Country by Country Report Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country |
      | Country by Country Report 1 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following TPM Country by Country Report Section:
      | Document                    | Name                                         | Order Position |
      | Country by Country Report 1 | First Section of Country by Country Report 1 | 1              |
    And the following Authorization:
      | Name                                  | Item                          | Rights                  |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ, CREATE, SUPERVISE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in

  Scenario: A user without manage permissions can not change the editable status of a section
    Given the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country |
      | Country by Country Report 2 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following TPM Country by Country Report Section:
      | Document                    | Name                | Editable | Order Position |
      | Country by Country Report 2 | First Section Title | 1        | 1              |
    And I have view and edit permission to TPM Country by Country Report "Country by Country Report 2"
    When I am on "/tpm/country-by-country-report/2/edit-document"
    Then I should see "First Section Title"
    When I click on the document section actions dropdown
    Then I should not see text matching "Prevent Edits"

  Scenario: A user without write permissions can not update section
    Given I have view permission to TPM Country by Country Report "Country by Country Report 1"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Edit" button in the section controls should be disabled

  Scenario: A user without manage permissions can not update section title
    Given I have view and edit permission to TPM Country by Country Report "Country by Country Report 1"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then I should see "First Section of Country by Country Report 1"
    When I click on the document section "Edit" action
    And the section title should be disabled

  Scenario: A user without manage permissions can not move, add nor remove a section
    Given I have view and edit permission to TPM Country by Country Report "Country by Country Report 1"
    When I go to "/tpm/country-by-country-report/1/edit-document"
    Then the "Move" button in the section controls should be disabled
    And the "Add Section After" button in the section controls should be disabled
    And the "Add Section Before" button in the section controls should be disabled
    And the "Add Subsection" button in the section controls should be disabled
    And the "Delete" button in the section controls should be disabled

  Scenario: A user without manage permissions can not change the require status of a section
    Given the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country |
      | Country by Country Report 2 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following TPM Country by Country Report Section:
      | Document                    | Name                | Required | Order Position |
      | Country by Country Report 2 | First Section Title | 1        | 1              |
    And I have view and edit permission to TPM Country by Country Report "Country by Country Report 2"
    When I am on "/tpm/country-by-country-report/2/edit-document"
    Then I should see "First Section Title"
    When I click on the document section actions dropdown
    Then I should not see text matching "Do Not Require"
