@clear-database
Feature: TPM Country by Country Report Authorization
  As a user with no authorization to the TPM Module
  I should have no access to any feature of TPM Country by Country Report

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions             |
      | TPM Country by Country Report Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Period:
      | Name        |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country           |
      | Country by Country Report 2 | Period 2013 | Unit Hierarchy 1 | Republic of Yemen |
    And I am logged in

  Scenario: A User without TPM Country by Country Report rights tries to list the Country by Country Report records
    When I go to "/tpm/country-by-country-report?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TPM Country by Country Report rights tries to edit an Country by Country Report record
    When I go to "/tpm/country-by-country-report/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TPM Country by Country Report rights tries to create a new Country by Country Report record
    When I go to "/tpm/country-by-country-report/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Country by Country Report creation page
    Given the following Authorization:
      | Name                                  | Item                          | Rights |
      | Country by Country Report Read Access | TPM_COUNTRY_BY_COUNTRY_REPORT | READ   |
    And I have the authorization "Country by Country Report Read Access"
    When I go to "/tpm/country-by-country-report/new"
    Then I should see "403 Access Denied"
