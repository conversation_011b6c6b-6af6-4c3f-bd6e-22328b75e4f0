@clear-database
Feature: Country by Country Reports
  In order to manage Country by Country Reports
  As a user allowed to the TPM Country by Country Reports
  I should be able to perform create, read, update and delete actions on Country by Country Report records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions             |
      | TPM Country by Country Report Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country            |
      | Country by Country Report 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
      | Country by Country Report 2 | Period 2013 | Unit Hierarchy 1 | Republic of Yemen  |
    And the following Authorization:
      | Name                                           | Item                          | Rights                       |
      | Country by Country Report Access/Create Access | TPM_COUNTRY_BY_COUNTRY_REPORT | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Country by Country Report Access/Create Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on "/tpm/country-by-country-report?q="
    Then I should see "No results found"

  Scenario: A user lists Country by Country Report records where he has group based permission
    Given I am assigned to group "Group 1"
    And user group "Group 1" has view permission to TPM Country by Country Report "Country by Country Report 2"
    When I go to "/tpm/country-by-country-report?q="
    Then I should see the following table:
      | Name                        | Hierarchy        |
      | Country by Country Report 2 | Unit Hierarchy 1 |

  Scenario: A user lists Country by Country Report records where he has permission
    Given I have view permission to TPM Country by Country Report "Country by Country Report 1"
    And I have view permission to TPM Country by Country Report "Country by Country Report 2"
    When I go to "/tpm/country-by-country-report?q="
    Then I should see the following table:
      | Name                        | Hierarchy        |
      | Country by Country Report 2 | Unit Hierarchy 1 |
      | Country by Country Report 1 | Unit Hierarchy 1 |

  Scenario: A user creates a new Country by Country Report
    Given I am on "/tpm/country-by-country-report/new"
    When I fill in the "Country By Country Report" form with:
      | Name           | Country by Country Report 4 |
      | Period         | Period 2012                 |
      | Country        | Zambia                      |
      | Unit Hierarchy | Unit Hierarchy 1            |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/country-by-country-report/3/edit"

  Scenario: A user creates a new Country by Country Report from document template
    Given the following Document Template:
      | Name            | Type                      |
      | A Base Template | country-by-country-report |
    And there is a user named Creator
    And there is a user named UserForReading
    And the following User Group:
      | Name              |
      | Group For Writing |
    And UserForReading has view permission to Document Template "A Base Template"
    And user group "Group For Writing" has view and edit permission to Document Template "A Base Template"
    And user Creator has the authorization "Country by Country Report Access/Create Access"
    And I am logged in as Creator
    And I am on "/tpm/country-by-country-report/new"
    When I fill in the "Country By Country Report" form with:
      | Name           | Country by Country Report 4 |
      | Period         | Period 2012                 |
      | Country        | Zambia                      |
      | Base Template  | A Base Template             |
      | Unit Hierarchy | Unit Hierarchy 1            |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/country-by-country-report/3/edit"
    And TPM Country by Country Report "Country by Country Report 4" should have following user permission table defined:
      | name           | permissions                  |
      | Creator        | view, edit, delete and owner |
      | UserForReading | view                         |
    And TPM Country by Country Report "Country by Country Report 4" should have following group permission table defined:
      | name              | permissions   |
      | Group For Writing | view and edit |

  Scenario: Updating a Country by Country Report
    Given I have view, edit, delete and owner permission to TPM Country by Country Report "Country by Country Report 2"
    And I am on "/tpm/country-by-country-report/2/edit"
    When I fill in the "Country By Country Report" form with:
      | Name | Updated name |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/country-by-country-report/2/edit"

  Scenario: Deleting a Country by Country Report from the configuration page
    Given I have view, edit, delete and owner permission to TPM Country by Country Report "Country by Country Report 1"
    And I am on "/tpm/country-by-country-report/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/country-by-country-report"
    And I should not see "Country by Country Report 1"

  Scenario: Deleting a Country by Country Report from the edit page
    Given I have view, edit, delete and owner permission to TPM Country by Country Report "Country by Country Report 1"
    And I am on "/tpm/country-by-country-report/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/country-by-country-report"
    And I should not see "Country by Country Report 1"

  Scenario: When creating a new Country by Country Report, the last filtered period is used to pre-populate the period field
    Given I am on "/tpm/country-by-country-report?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/country-by-country-report/new"
    And the "Country by Country Report" form field "Period" should be "Period 2012"

  Scenario: Assigning unauthorised units to country by country document fails
    Given the following Unit:
      | Ref Id     | Name | Country            |
      | RefId Unit | Unit | Republic of Zambia |
    And the following Unit Hierarchy Definition:
      | Unit Hierarchy   | Unit       | Parent Unit |
      | Unit Hierarchy 1 | RefId Unit |             |
    And I have view, edit, delete and owner permission to TPM Country by Country Report "Country by Country Report 1"
    And I am on "/tpm/country-by-country-report/1/edit"
    Then I should see "RefId Unit - Unit"
    When I check "RefId Unit - Unit"
    And I press "Save"
    Then I should see an error message
