@clear-database
Feature: Country by Country Report attachments

  Background:
    Given the following System Setting:
      | Id                             | Value           |
      | security.file_upload_whitelist | ["text\/plain"] |
    And the following Status:
      | Type     | Name   |
      | OPEN     | open   |
      | COMPLETE | closed |
    And the following Transition:
      | Name  | Origin status | Destination status |
      | Close | open          | closed             |
    And the following Workflow:
      | Name                                   | Initial status | Transitions |
      | TPM Country by Country Report Workflow | open           | Close       |
    And the following Workflow Binding:
      | Binding Id                    | Workflow                               |
      | tpm_country_by_country_report | TPM Country by Country Report Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | Name Short | Name Long          | Nationality Short | Nationality Long |
      | ZM          | Zambia     | Republic of Zambia | Zambian           | Zambian          |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following TPM Country by Country Report:
      | Name                        | Period      | Unit Hierarchy   | Country            |
      | Country by Country Report 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Country by Country Report Section:
      | Document                    | Name                                         | Order Position |
      | Country by Country Report 1 | First Section of Country by Country Report 1 | 1              |
    And the following Authorization:
      | Name                                  | Item                          | Rights                                  |
      | Country by Country Report Full Access | TPM_COUNTRY_BY_COUNTRY_REPORT | CREATE, READ, UPDATE, DELETE, SUPERVISE |
    And I have the authorization "Country by Country Report Full Access"
    And I am logged in

  Scenario: Attaching a newly uploaded file to a section
    And I have view and edit permission to TPM Country by Country Report "Country by Country Report 1"
    When I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see "First Section of Country by Country Report 1"
    When I click on the document section "Add Attachment" action
    And I attach the file "test.txt" to "uploadedFile"
    And I click the "Upload" button in the dialog
    Then I should see a success message
    When I click on the document section "Information" action
    Then I should see "test.txt"

  Scenario: Attaching an existing file to a section
    Given the following User:
      | Username  |
      | Fileowner |
    And the following File:
      | Name         | Description              | Access Type | Path                | Created By | Updated By |
      | File to Link | File to Link description | public      | /file/path/download | Fileowner  | Fileowner  |
    And I have view and edit permission to TPM Country by Country Report "Country by Country Report 1"
    When I am on "/tpm/country-by-country-report/1/edit-document"
    Then I should see "First Section of Country by Country Report 1"
    When I click on the document section "Add Attachment" action
    And I click the "Link an existing file" button
    Then I should see a table with "File to Link" in the "Name" column
    When I check the radio button "File to Link"
    And I click the "Link" button in the dialog
    Then I should see a success message
    When I click on the document section "Information" action
    Then I should see "File to Link"
