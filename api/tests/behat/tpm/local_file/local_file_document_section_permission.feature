@clear-database
Feature: TPM Local File Section Permission
  As a user
  I should be able to edit TPM Local File Sections if I have the correct permissions

  Background:
    Given the following Status:
      | Type     | Name   |
      | OPEN     | open   |
      | COMPLETE | closed |
    And the following Transition:
      | Name  | Origin status | Destination status |
      | Start | open          | closed             |
      | Stop  | closed        | open               |
    And the following Workflow:
      | Name                    | Initial status | Transitions |
      | TPM Local File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following TPM Local File Section:
      | Document     | Name                          | Order Position |
      | Local File 1 | First Section of Local File 1 | 1              |
    And the following Authorization:
      | Name                   | Item           | Rights                  |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: A user without manage permissions can not change the editable status of a section
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following TPM Local File Section:
      | Document     | Name                | Editable | Order Position |
      | Local File 2 | First Section Title | 1        | 1              |
    And I have view and edit permission to TPM Local File "Local File 2"
    When I am on "/tpm/local-file/2/edit-document"
    Then I should see "First Section Title"
    When I click on the document section actions dropdown
    Then I should not see text matching "Prevent Edits"

  Scenario: A user without write permissions can not update section
    Given I have view permission to TPM Local File "Local File 1"
    When I go to "/tpm/local-file/1/edit-document"
    Then the "Edit" button in the section controls should be disabled

  Scenario: A user without manage permissions can not update section title
    Given I have view and edit permission to TPM Local File "Local File 1"
    When I go to "/tpm/local-file/1/edit-document"
    Then I should see "First Section of Local File 1"
    When I click on the document section "Edit" action
    And the section title should be disabled

  Scenario: A user without manage permissions can not move, add nor remove a section
    Given I have view and edit permission to TPM Local File "Local File 1"
    When I go to "/tpm/local-file/1/edit-document"
    Then the "Move" button in the section controls should be disabled
    And the "Add Section After" button in the section controls should be disabled
    And the "Add Section Before" button in the section controls should be disabled
    And the "Add Subsection" button in the section controls should be disabled
    And the "Delete" button in the section controls should be disabled

  Scenario: A user without manage permissions can not change the require status of a section
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following TPM Local File Section:
      | Document     | Name                | Required | Order Position |
      | Local File 2 | First Section Title | 1        | 1              |
    And I have view and edit permission to TPM Local File "Local File 2"
    When I am on "/tpm/local-file/2/edit-document"
    Then I should see "First Section Title"
    When I click on the document section actions dropdown
    Then I should not see text matching "Do Not Require"
