@clear-database
Feature: Local File section management
  In order to manage Local File sections
  As a logged in user
  I need to go to Local File edit page

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name                    | Initial status | Transitions     |
      | TPM Local File Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | Name Short | Name Long          | Nationality Short | Nationality Long |
      | ZM          | Zambia     | Republic of Zambia | Zambian           | Zambian          |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name                          | Order Position |
      | Local File 1 | First Section of Local File 1 | 1              |
    And the following Authorization:
      | Name                   | Item           | Rights                  |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: Opening the document section information dialog
    Given I have view permission to TPM Local File "Local File 1"
    When I am on "/tpm/local-file/1/edit-document"
    Then I should see "First Section of Local File 1"
    And I should see "First Section"
    When I click on the document section "Information" action
    Then I should see "Editable Yes"
    And I should see "Required No"
    And I should see "Included Yes"

  Scenario: Updating a section
    Given the following User:
      | Username |
      | admin    |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Id | Document     | Name                          | Content                      | Order Position |
      | 2  | Local File 2 | First Section of Local File 2 | <p>First Section Content</p> | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I am on "/tpm/local-file/2/edit-document"
    Then I should see "1 First Section of Local File 2"
    But I should not see "1 Updated Title"
    And I should not see "Save"
    When I click on the element with text "First Section of Local File 2" in element "[id='section-2']"
    Then I should see "Save"
    And I fill in the "document_section_2" form with:
      | name | Updated Title |
    And I click the "Save" button
    Then I should see "1 Updated Title"
    And I should not see "1 First Section of Local File 2"

  Scenario: Exclude button should be disabled on section which is required
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Required | Order Position |
      | Local File 2 | First Section | 1        | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then the "Exclude" button in the section controls should be disabled

  Scenario: Including a section on a Local File
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Required | Include | Order Position |
      | Local File 2 | First Section | 0        | 0       | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then I should see text matching "First Section"
    And I should not see text matching "1 First Section"
    And "First Section" section should be excluded
    And "First Section" section TOC link should be excluded
    When I click on the document section "Include" action
    Then I should see text matching "1 First Section"
    And "First Section" section should be included
    And "First Section" section TOC link should be included

  Scenario: Excluding a section from a Local File
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Required | Include | Order Position |
      | Local File 2 | First Section | 0        | 1       | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then I should see text matching "1 First Section"
    And "First Section" section should be included
    And "First Section" section TOC link should be included
    When I click on the document section "Exclude" action
    Then I should see text matching "First Section"
    And I should not see text matching "1 First Section"
    And "First Section" section should be excluded
    And "First Section" section TOC link should be excluded

  Scenario: A manager can prevent edits on a section
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Editable | Order Position |
      | Local File 2 | First Section | 1        | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then the "Edit" button in the section controls should be enabled
    When I click on the document section "Prevent Edits" action
    Then the "Edit" button in the section controls should be disabled

  Scenario: A manager can allow edits on a section
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Editable | Order Position |
      | Local File 2 | First Section | 0        | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then the "Edit" button in the section controls should be disabled
    When I click on the document section "Allow Edits" action
    Then the "Edit" button in the section controls should be enabled

  Scenario: Making an optional section required on a Local File
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Required | Include | Order Position |
      | Local File 2 | First Section | 0        | 1       | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then the "Exclude" button in the section controls should be enabled
    When I click on the document section "Require" action
    Then the "Exclude" button in the section controls should be disabled

  Scenario: Making a required section optional on a Local File
    Given the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 2 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following TPM Local File Section:
      | Document     | Name          | Required | Include | Order Position |
      | Local File 2 | First Section | 1        | 1       | 1              |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    When I go to "/tpm/local-file/2/edit-document"
    Then the "Exclude" button in the section controls should be disabled
    When I click on the document section "Do Not Require" action
    Then the "Exclude" button in the section controls should be enabled
