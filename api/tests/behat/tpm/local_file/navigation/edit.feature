@clear-database
Feature: Local Files Navigation - Edit
  In order to manage Local Files
  As a user allowed to the TPM Local Files
  I should be able to navigate through the Local File pages

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | TPM Local File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
    And the following Period:
      | Name        |
      | Period 2015 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 1 | Period 2015 | Unit Hierarchy 1 | Republic of Zambia |
    And the following Authorization:
      | Name                   | Item           | Rights                  |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I have view and edit permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file?q="
    When I click "Edit" on the table row for "Local File 1"
    Then I should be on "/tpm/local-file/1/edit"
