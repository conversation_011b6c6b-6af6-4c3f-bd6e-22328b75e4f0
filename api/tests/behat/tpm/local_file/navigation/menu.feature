@clear-database
Feature: Local Files Navigation - Menu
  In order to manage Local Files
  As a user allowed to the TPM Local Files
  I should be able to navigate through the Local File pages

  Background:
    Given the following Authorization:
      | Name                   | Item           | Rights                  |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Local File Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Local File" in the menu under "TPM"
    Then I should be on "/tpm/local-file"
