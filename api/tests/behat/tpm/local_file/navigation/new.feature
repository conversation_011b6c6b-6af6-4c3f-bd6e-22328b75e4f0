@clear-database
Feature: Local Files Navigation - New
  In order to manage Local Files
  As a user allowed to the TPM Local Files
  I should be able to navigate through the Local File pages

  Background:
    Given the following Authorization:
      | Name                   | Item           | Rights       |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | TPM Local File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tpm/local-file?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/local-file/new"
