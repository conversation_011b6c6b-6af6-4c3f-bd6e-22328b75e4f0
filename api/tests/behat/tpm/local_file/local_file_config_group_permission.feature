@clear-database
Feature: Permissions for local files can be controlled via groups
  As a user
  I should have access to Local File configuration if i belong to a group with permissions

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | TPM Local File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following Authorization:
      | Name                   | Item           | Rights       |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: A user cannot write local file configurations if none of his groups has manage permission
    Given the following User Group:
      | Name        |
      | EMPTY_GROUP |
    And I am assigned to group "EMPTY_GROUP"
    When I go to "/tpm/local-file/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A user can edit a local file configuration if one of his groups has manage permission
    Given the following User Group:
      | Name       |
      | VIEW_GROUP |
    And user group "VIEW_GROUP" has view permission to TPM Local File "Local File 1"
    And I am assigned to group "VIEW_GROUP"
    When I go to "/tpm/local-file/1/edit"
    Then I should be on "/tpm/local-file/1/edit"
