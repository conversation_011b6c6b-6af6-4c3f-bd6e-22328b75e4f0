@clear-database
Feature: TPM Local File Authorization
  As a user with no authorization to the TPM Module
  I should have no access to any feature of TPM Local File

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | TPM Local File Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
      | Local File 2 | Period 2013 | Unit Hierarchy 1 | Republic of Yemen  |
      | Local File 3 | Period 2013 | Unit Hierarchy 1 | Republic of Yemen  |
    And I am logged in

  Scenario: A User without TPM local file rights tries to list the Local File records
    When I go to "/tpm/local-file?q="
    Then I should see "403 Access Denied"

  Scenario: A User without TPM local file rights tries to edit an Local File record
    When I go to "/tpm/local-file/2/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without TPM local file rights tries to create a new Local File record
    When I go to "/tpm/local-file/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Local File creation page
    And the following Authorization:
      | Name                   | Item           | Rights |
      | Local File Read Access | TPM_LOCAL_FILE | READ   |
    And I have the authorization "Local File Read Access"
    When I go to "/tpm/local-file/new"
    Then I should see "403 Access Denied"
