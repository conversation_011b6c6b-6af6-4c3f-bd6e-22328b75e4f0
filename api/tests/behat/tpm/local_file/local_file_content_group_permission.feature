@clear-database
Feature: Permissions for local files can be controlled via groups
  As a user
  I should have access to Local File content if i belong to a group with permissions

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | TPM Local File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
    And the following User Group:
      | Name                |
      | LF_READ_WRITE_GROUP |
    And user group "LF_READ_WRITE_GROUP" has view permission to TPM Local File "Local File 1"
    And the following Authorization:
      | Name                   | Item           | Rights                  |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: A user can read a local file content if one of his groups has read permission
    Given I am assigned to group "LF_READ_WRITE_GROUP"
    When I am on "/tpm/local-file/1/edit-document"
    Then I should be on "/tpm/local-file/1/edit-document"
