@clear-database
Feature: Financial Data - Create, Read, Update, Delete and List
  In order to manage Financial Data
  As a User with the required authorisation
  I should be able to perform create, read, update and delete actions on Financial Data records

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
      | GBP         |
    And the following Exchange Rate:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period      |
      | GBP            | EUR             | 2           | Average            | Period 2012 |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Financial Data Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tpm_financial_data | Financial Data Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Yemen   |
      | RefId3 | Legal Unit 3 | EUR      | Yemen   |
    And the following TPM Financial Data:
      | Unit   | Period      | Status | Total Revenue Unrelated Value | Total Revenue Related Value | Total Revenue Value | Description | Profit Loss Before Income Tax Value | Income Tax Paid Value | Income Tax Accrued Value | Stated Capital Value | Accumulated Earnings Value | Number of Employees | Tangible Assets Value |
      | RefId1 | Period 2012 | open   | 101                           | 102                         | 203                 | CBC FD 1    | 104                                 | 105                   | 106                      | 107                  | 108                        | 5                   | 109                   |
      | RefId2 | Period 2013 | open   | 111                           | 112                         | 223                 | CBC FD 2    | 114                                 | 115                   | 116                      | 117                  | 118                        | 5                   | 119                   |
    And the following Authorization:
      | Name                | Item               | Rights                       |
      | Financial Data Full | TPM_FINANCIAL_DATA | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Financial Data Full"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    When I go to "/tpm/financial-data?q="
    Then I should see "No results found"

  Scenario: A user list the records available to them
    Given I am assigned to unit "RefId1"
    When I go to "/tpm/financial-data?q="
    Then I should see the following table:
      | Status | Unit Ref. ID | Unit Name    |
      | OPEN   | RefId1       | Legal Unit 1 |

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/tpm/financial-data?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/financial-data/new"
    And the "Financial Data" form field "Period" should be "Period 2012"

  Scenario: A user creates a new Financial Data record
    Given I am assigned to unit "RefId1"
    And I am on "/tpm/financial-data/new"
    When I fill in the "Financial Data" form with:
      | Unit                                             | Legal Unit 1 |
      | Period                                           | Period 2012  |
      | Description                                      | CBC FD 3     |
      | Carry Forward                                    | Yes          |
      | Transaction Currency                             | GBP          |
      | Total Revenue Unrelated Value > Base Value       | 111          |
      | Total Revenue Related Value > Base Value         | 112          |
      | Profit Loss Before Income Tax Value > Base Value | 114          |
      | Income Tax Paid Value > Base Value               | 115          |
      | Income Tax Accrued Value > Base Value            | 116          |
      | Stated Capital Value > Base Value                | 117          |
      | Accumulated Earnings Value > Base Value          | 118          |
      | Tangible Assets Value > Base Value               | 119          |
      | Number of Employees                              | 5            |
    Then the "Financial Data" form field "Total Revenue Value > Base Value" should be "223"
    When I click the "Save" button in "Page Controls"
    Then I should be on "/tpm/financial-data/3/edit"
    And I should see a success message
    And the "Financial Data" form has the values:
      | Unit                                             | RefId1 - Legal Unit 1 |
      | Period                                           | Period 2012           |
      | Description                                      | CBC FD 3              |
      | Carry Forward                                    | Yes                   |
      | Transaction Currency                             | GBP                   |
      | Total Revenue Unrelated Value > Base Value       | 111                   |
      | Total Revenue Related Value > Base Value         | 112                   |
      | Profit Loss Before Income Tax Value > Base Value | 114                   |
      | Income Tax Paid Value > Base Value               | 115                   |
      | Income Tax Accrued Value > Base Value            | 116                   |
      | Stated Capital Value > Base Value                | 117                   |
      | Accumulated Earnings Value > Base Value          | 118                   |
      | Tangible Assets Value > Base Value               | 119                   |
      | Number of Employees                              | 5                     |

  Scenario: A user with the required authorisation updating a Financial Data record
    Given I am assigned to unit "RefId1"
    And I am on "/tpm/financial-data/1/edit"
    And I fill in the "Financial Data" form with:
      | Description | Test |
    And I press "Save"
    Then I should be on "/tpm/financial-data/1/edit"
    And I should see a success message

  Scenario: A user with the required authorisation deleting a Financial Data record
    Given I am assigned to unit "RefId2"
    And I am on "/tpm/financial-data/2/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/tpm/financial-data"
    And I should see a success message
    And I should not see "Legal Unit 2"

  Scenario: A user with the required authorisation tries to create a Financial Data where the period is closed
    Given I am assigned to unit "RefId1"
    And The period "Period 2014" is closed
    And I am on "/tpm/financial-data?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/financial-data/new"
    When I fill in the "Financial Data" form with:
      | Unit                                             | Legal Unit 1 |
      | Period                                           | Period 2014  |
      | Description                                      | CBC FD 3     |
      | Carry Forward                                    | Yes          |
      | Transaction Currency                             | EUR          |
      | Total Revenue Unrelated Value > Base Value       | 111          |
      | Total Revenue Related Value > Base Value         | 112          |
      | Profit Loss before Income Tax Value > Base Value | 114          |
      | Income Tax Paid Value > Base Value               | 115          |
      | Income Tax Accrued Value > Base Value            | 116          |
      | Stated Capital Value > Base Value                | 117          |
      | Accumulated Earnings Value > Base Value          | 118          |
      | Tangible Assets Value > Base Value               | 119          |
      | Number of Employees                              | 5            |
    And I press "Save"
    Then I should see an error message

  Scenario: A user with the required authorisation tries to edit a Financial Data where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tpm/financial-data/2/edit"
    Then the "Save" button in "Page Controls" should be disabled

  Scenario: A user with the required authorisation tries to delete a Financial Data where the period is closed
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tpm/financial-data/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
