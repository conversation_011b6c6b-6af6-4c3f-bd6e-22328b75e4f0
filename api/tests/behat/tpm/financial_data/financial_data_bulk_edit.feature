@clear-database
Feature: Financial Data Bulk Edit
  In order to bulk edit Financial Data
  As a User with the required authorisation
  I should be able to perform bulk edit on Financial Data records that are not completed

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
      | Period 2013 | 01.01.2013 | 31.12.2013 |
      | Period 2014 | 01.01.2014 | 31.12.2014 |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Financial Data Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tpm_financial_data | Financial Data Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Yemen   |
      | RefId3 | Legal Unit 3 | EUR      | Yemen   |
    And the following TPM Financial Data:
      | Unit   | Period      | Status | Total Revenue Unrelated Value | Total Revenue Related Value | Total Revenue Value | Description | Profit Loss Before Income Tax Value | Income Tax Paid Value | Income Tax Accrued Value | Stated Capital Value | Accumulated Earnings Value | Number of Employees | Tangible Assets Value |
      | RefId1 | Period 2012 | open   | 101                           | 102                         | 203                 | CBC FD 1    | 104                                 | 105                   | 106                      | 107                  | 108                        | 5                   | 109                   |
      | RefId2 | Period 2013 | open   | 111                           | 112                         | 223                 | CBC FD 2    | 114                                 | 115                   | 116                      | 117                  | 118                        | 5                   | 119                   |
      | RefId3 | Period 2014 | done   | 121                           | 122                         | 243                 | CBC FD 3    | 124                                 | 125                   | 126                      | 127                  | 128                        | 5                   | 129                   |
    And the following Authorization:
      | Name                | Item               | Rights               |
      | Financial Data Read | TPM_FINANCIAL_DATA | READ                 |
      | Financial Data Full | TPM_FINANCIAL_DATA | READ, UPDATE, DELETE |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "Financial Data Read"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/financial-data?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Financial Data is selected
    Given I have the authorization "Financial Data Full"
    And I am assigned to unit "RefId1"
    And I am on "/tpm/financial-data?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tpm/financial-data/1/edit"

  Scenario: A user can bulk edit Financial Data
    Given I have the authorization "Financial Data Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/financial-data?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-financial-data/edit?selection=1%2C2"
    When I enable the "task-description" field in "Bulk Edit Form"
    When I fill in the "Bulk Edit" form field "task-description" with "This is a description"
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tpm/financial-data"
    And I should see a success message

  Scenario: A user can not bulk edit Financial Data because the entered values are invalid
    Given I have the authorization "Financial Data Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/financial-data?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-financial-data/edit?selection=1%2C2"
    When I enable the "Number Of Employees" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Number Of Employees | 9999999999 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should see "Record with ID 1 is invalid"

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "Financial Data Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                |
      | Description disabled | done     | Financial Data Workflow |
    And I am on "/tpm/financial-data?q="
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-financial-data/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
