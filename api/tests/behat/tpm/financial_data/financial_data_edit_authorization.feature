@clear-database
Feature: Financial Data Authorization - Edit
  As a user with no authorization to TPM Financial Data
  I should have no access to the edit feature of TPM Financial Data

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 31.12.2012 |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions     |
      | Financial Data Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tpm_financial_data | Financial Data Workflow |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Yemen   |
    And the following TPM Financial Data:
      | Unit   | Period      | Total Revenue Unrelated Value | Total Revenue Related Value | Total Revenue Value | Description | Profit Loss Before Income Tax Value | Income Tax Paid Value | Income Tax Accrued Value | Stated Capital Value | Accumulated Earnings Value | Number of Employees | Tangible Assets Value |
      | RefId1 | Period 2012 | 101                           | 102                         | 203                 | CBC FD      | 104                                 | 105                   | 106                      | 107                  | 108                        | 5                   | 109                   |

  Scenario: An admin without TPM Financial Data rights tries to edit an Financial Data record
    Given I am logged in as an administrator
    When I go to "/tpm/financial-data/1/edit"
    Then I should see "403 Access Denied"

