@clear-database
Feature: Financial Data Navigation - Menu
  In order to manage Financial Data
  As a user allowed to the TPM Financial Data
  I should be able to navigate through the Financial Data menu

  Background:
    Given the following Authorization:
      | Name                       | Item               | Rights               |
      | Financial Data Full Access | TPM_FINANCIAL_DATA | READ, UPDATE, DELETE |
    And I have the authorization "Financial Data Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Financial Data" in the menu under "TPM"
    Then I should be on "/tpm/financial-data"
