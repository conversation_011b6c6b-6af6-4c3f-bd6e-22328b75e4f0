@clear-database
Feature: TPM Transactions Bulk Edit
  In order to bulk edit TPM Transactions
  As a user allowed to the TPM transactions
  I should be able to bulk update the transactions that are not completed

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                     | Initial Status | Transitions             |
      | TPM Transaction Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_transaction | TPM Transaction Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
      | Period 2014 |
    And the following Exchange Rate:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period      |
      | TEU            | EUR             | 523         | Current            | Period 2012 |
      | EUR            | TEU             | 325         | Current            | Period 2012 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following Billing Type:
      | Name       |
      | Allocation |
    And the following Transaction Type:
      | Name      |
      | ARIS      |
      | Financing |
    And the following TPM Transaction:
      | Status | Name          | Unit         | Partner Unit | Period      | Transaction Currency | Type      | Partner Unit Requires Documentation | Unit Requires Documentation | Unit Standard Taxation Applicable | Partner Unit Standard Taxation Applicable | Billing Type |
      | open   | Transaction 1 | Legal Unit 1 | Legal Unit 3 | Period 2012 | TEU                  | ARIS      | Yes                                 | Yes                         | No                                | No                                        | Allocation   |
      | open   | Transaction 2 | Legal Unit 2 | Legal Unit 3 | Period 2013 | EUR                  | Financing | Yes                                 | Yes                         | No                                | No                                        | Allocation   |
      | done   | Transaction 3 | Legal Unit 3 | Legal Unit 1 | Period 2013 | EUR                  | Financing | Yes                                 | Yes                         | No                                | No                                        | Allocation   |
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given the following Authorization:
      | Name                    | Item            | Rights |
      | Transaction Read Access | TPM_TRANSACTION | READ   |
    And I have the authorization "Transaction Read Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/transaction?q="
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one transaction is selected
    Given the following Authorization:
      | Name                    | Item            | Rights               |
      | Transaction Full Access | TPM_TRANSACTION | READ, UPDATE, DELETE |
    And I have the authorization "Transaction Full Access"
    And I am assigned to unit "RefId1"
    And I am on "/tpm/transaction?q="
    When I check the checkbox on "Transaction 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tpm/transaction/1/edit"

  Scenario: A user can bulk edit transactions
    Given the following Authorization:
      | Name                    | Item            | Rights               |
      | Transaction Full Access | TPM_TRANSACTION | READ, UPDATE, DELETE |
    And I have the authorization "Transaction Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/transaction?q="
    When I check the checkbox on "Transaction 1" table row for bulk action
    And I check the checkbox on "Transaction 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-transaction/edit?selection=1%2C2"
    When I enable the "Sub Type" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Sub Type | Test |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tpm/transaction"
    And I should see a success message

  Scenario: A user can not bulk edit transactions because the entered values are invalid
    Given the following Authorization:
      | Name                    | Item            | Rights               |
      | Transaction Full Access | TPM_TRANSACTION | READ, UPDATE, DELETE |
    And I have the authorization "Transaction Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tpm/transaction?q="
    When I check the checkbox on "Transaction 1" table row for bulk action
    And I check the checkbox on "Transaction 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-transaction/edit?selection=1%2C2"
    When I enable the "Transaction Amount" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | Transaction Amount | this value is not valid |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    And I should see a error message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given the following Authorization:
      | Name                    | Item            | Rights               |
      | Transaction Full Access | TPM_TRANSACTION | READ, UPDATE, DELETE |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                 |
      | Description disabled | done     | TPM Transaction Workflow |
    And I have the authorization "Transaction Full Access"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And I am on "/tpm/transaction?q="
    When I check the checkbox on "Transaction 1" table row for bulk action
    And I check the checkbox on "Transaction 3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-transaction/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
    And the element "#enables-unit input[type='checkbox']" should be enabled
