@clear-database
Feature: TPM Transactions
  In order to manage Transactions
  As a user allowed to the TPM transactions
  I should be able to perform create, read, update and delete actions on Transaction records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                     | Initial Status | Transitions             |
      | TPM Transaction Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_transaction | TPM Transaction Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following Exchange Rates:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period      |
      | TEU            | EUR             | 523         | Current            | Period 2012 |
      | EUR            | TEU             | 325         | Current            | Period 2012 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
    And the following Billing Type:
      | Name       |
      | Allocation |
    And the following Transaction Type:
      | Name      |
      | ARIS      |
      | Financing |
    And the following TPM Transaction:
      | Name          | Unit         | Partner Unit | Period      | Transaction Currency | Type      | Billing Type | Unit Requires Documentation | Partner Unit Requires Documentation | Unit Standard Taxation Applicable | Partner Unit Standard Taxation Applicable |
      | Transaction 1 | Legal Unit 1 | Legal Unit 3 | Period 2012 | TEU                  | ARIS      | Allocation   | 1                           | 0                                   | 0                                 | 0                                         |
      | Transaction 2 | Legal Unit 2 | Legal Unit 1 | Period 2013 | EUR                  | Financing | Allocation   | 1                           | 0                                   | 0                                 | 0                                         |
      | Transaction 3 | Legal Unit 3 | Legal Unit 2 | Period 2013 | EUR                  | Financing | Allocation   | 1                           | 0                                   | 0                                 | 0                                         |
    And the following Authorization:
      | Name                        | Item            | Rights                       |
      | TPM Transaction Full Access | TPM_TRANSACTION | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "TPM Transaction Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the transaction list page
    Given I am on the homepage
    When I click "Transaction" in the menu under "TPM"
    Then I should be on "/tpm/transaction"
    And I should see "No results found"

  Scenario: When creating a new transaction, the last filtered period is used to pre-populate the period field
    Given I am on "/tpm/transaction?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/transaction/new"
    And the "Transaction" form field "Period" should be "Period 2012"

  Scenario: A user with the required authorisation list, create, update and deletes a transaction
    Given I am assigned to unit "RefId1"
    When I am on "/tpm/transaction?q="
    Then I should see the following table portion:
      | Name          | Type      | Unit Name    | Partner Unit Name |
      | Transaction 2 | Financing | Legal Unit 2 | Legal Unit 1      |
      | Transaction 1 | ARIS      | Legal Unit 1 | Legal Unit 3      |

    # Navigate to the record to edit
    When I click "Edit" on the table row for "Transaction 1"
    Then I should be on "/tpm/transaction/1/edit"

    # Update the record
    When I fill in the "Transaction" form with:
      | Name                                | Updated name |
      | Unit Requires Documentation         | Required     |
      | Partner Unit Requires Documentation | Not Required |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/transaction/1/edit"

    # Create a new record
    Given I am assigned to unit "RefId2"
    And I am on "/tpm/transaction/new"
    When I fill in the "Transaction" form with:
      | Name                                      | Transaction 4 |
      | Unit                                      | Legal Unit 1  |
      | Partner Unit                              | Legal Unit 2  |
      | Period                                    | Period 2012   |
      | Type                                      | Financing     |
      | Unit Requires Documentation               | Required      |
      | Partner Unit Requires Documentation       | Not Required  |
      | Unit Standard Taxation Applicable         | Yes           |
      | Partner Unit Standard Taxation Applicable | No            |
      | Billing Type                              | Allocation    |
      | Transaction Amount                        | 1234          |
      | Transaction Currency                      | TEU           |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/transaction/4/edit"

    # Delete the record
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/transaction"
    And I should not see "Transaction 4"

  Scenario: A user with the required authorisation tries to manage a transaction where the period is closed
    Given I am assigned to unit "RefId1"
    Given I am assigned to unit "RefId2"
    And The period "Period 2013" is closed
    When I go to "/tpm/transaction/2/edit"
    Then the "Save" button in "Page Controls" should be disabled
    When I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
    When I go to "/tpm/transaction/new"
    And I fill in the "Transaction" form with:
      | Name                                      | Transaction 3 |
      | Unit                                      | Legal Unit 1  |
      | Partner Unit                              | Legal Unit 2  |
      | Period                                    | Period 2013   |
      | Type                                      | Financing     |
      | Unit Requires Documentation               | Required      |
      | Partner Unit Requires Documentation       | Not Required  |
      | Unit Standard Taxation Applicable         | Yes           |
      | Partner Unit Standard Taxation Applicable | No            |
      | Billing Type                              | Allocation    |
      | Transaction Amount                        | 1234          |
      | Transaction Currency                      | TEU           |
    And I press "Save"
    Then I should see an error message
