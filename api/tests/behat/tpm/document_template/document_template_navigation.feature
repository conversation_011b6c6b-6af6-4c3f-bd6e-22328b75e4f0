@clear-database
Feature: Document Template Navigation
  In order to manage Document Templates
  As an Administrator
  I should be able to navigate through the Document Template pages

  Background:
    Given I am logged in as an administrator

  <PERSON><PERSON><PERSON>: Visit the document template import page over the list page
    Given I am on "/configuration/document/templates"
    When I click the "Import" button in "Page Controls"
    Then I should be on "/configuration/document/templates/import"

  Scenario: Visit the document template list page over the menu
    Given I am on the homepage
    When I click "Administration" in the menu under "Tools"
    And I click "Templates"
    Then I should be on "/configuration/document/templates"

  Scenario: Visit document template list page over the local file list page
    Given the following Authorization:
      | Name                   | Item           | Rights |
      | Local File Read Access | TPM_LOCAL_FILE | READ   |
    And I have the authorization "Local File Read Access"
    And I am on "/tpm/local-file?q="
    When I click the "dots" button in "Page Controls"
    And I click the "Templates" dropdown item
    Then I should be on "/configuration/document/templates"

  Scenario: Visit document template list page over the master file list page
    Given the following Authorization:
      | Name                    | Item            | Rights |
      | Master File Read Access | TPM_MASTER_FILE | READ   |
    And I have the authorization "Master File Read Access"
    And I am on "/tpm/master-file?q="
    When I click the "dots" button in "Page Controls"
    And I click the "Templates" dropdown item
    Then I should be on "/configuration/document/templates"

  Scenario: Accessing the document template configure form
    Given the following Document Template:
      | Id | Name                     | Type       |
      | 1  | Test Local File Template | local-file |
    And I am on "/configuration/document/templates"
    When I click "Configuration" on the table row for "Test Local File Template"
    Then I should be on "/configuration/document/templates/1/configure"

  Scenario: Accessing the document template preview page
    Given the following Document Template:
      | Id | Name                     | Type       |
      | 1  | Test Local File Template | local-file |
    And I am on "/configuration/document/templates"
    When I click "Preview" on the table row for "Test Local File Template"
    Then I should be on "/configuration/document/templates/1/preview"
