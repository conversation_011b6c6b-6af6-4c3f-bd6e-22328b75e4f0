@clear-database
Feature: Unit Period Bulk Edit
  In order to manage Unit Period records
  As a User with the required authorisation
  I should be able to perform bulk edit on Unit Period records that are not completed

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name                   | Origin status | Destination Status |
      | Transition with action | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions            |
      | Layout Monitor Workflow | in progress    | Transition with action |
    And the following SetDefaultItemValuesAction:
      | Transition             |
      | Transition with action |
    And the following Workflow Binding:
      | Binding Id  | Workflow                |
      | unit_period | Layout Monitor Workflow |
    And the following Currency:
      | Name      | Iso 4217 Code |
      | Euro      | EUR           |
      | US Dollar | USD           |
    And the following Unit:
      | Id | Ref Id | Name | Currency |
      | 1  | RefId1 | Unit | USD      |
      | 2  | RefId2 | Unit | USD      |
      | 3  | RefId3 | Unit | USD      |
    And the following System Setting:
      | Id                   | Value |
      | application_currency | EUR   |
    And the following Period:
      | Id | Name            | Previous Period |
      | 1  | Previous Period |                 |
      | 2  | Current Period  | Previous Period |
    And the following Authorization:
      | Name             | Item        | Rights               |
      | Unit Period Full | UNIT_PERIOD | READ, UPDATE, DELETE |
      | Unit Period Read | UNIT_PERIOD | READ                 |
    And the following Unit Period:
      | Unit   | Period         | Status |
      | RefId1 | Current Period | open   |
      | RefId2 | Current Period | open   |
      | RefId3 | Current Period | open   |
    And I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"
    And I am logged in

  Scenario: A user with read only permissions is not able to access the bulk edit page
    Given I have the authorization "Unit Period Read"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tax-accounting/unit-period"
    And I click the "dots" button in "Page Controls"
    Then the "Edit Selected Record(s)" button should be disabled

  Scenario: A user is redirected to the single edit page if only one Unit Period is selected
    Given I have the authorization "Unit Period Full"
    And I am assigned to unit "RefId1"
    And I am on "/tax-accounting/unit-period"
    When I check the checkbox on "RefId1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tax-accounting/unit-period/1/edit"

  Scenario: A user can bulk edit Unit Period
    Given I have the authorization "Unit Period Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId2 |
    And I am on "/tax-accounting/unit-period"
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/unit-period/edit?selection=1%2C2"
    When I enable the "task-description" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | task-description | Description 1 |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tax-accounting/unit-period"
    And I should see a success message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "Unit Period Full"
    And I am assigned to the following units:
      | RefId1 |
      | RefId3 |
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field       | Field Configuration  | State    |
      | description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                |
      | Description disabled | done     | Layout Monitor Workflow |
    And I am on "/tax-accounting/unit-period"
    When I check the checkbox on "RefId1" table row for bulk action
    And I check the checkbox on "RefId3" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/unit-period/edit?selection=1%2C3"
    And the element "#enables-description input[type='checkbox']" should be disabled
