@clear-database
Feature: Unit Period Crud
  In order to manage Unit Period records
  As a user with the required authorisation
  I should be able to perform create, read, update and delete actions on Unit Period records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name                   | Origin status | Destination Status |
      | Transition with action | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions            |
      | Layout Monitor Workflow | in progress    | Transition with action |
    And the following SetDefaultItemValuesAction:
      | Transition             |
      | Transition with action |
    And the following Workflow Binding:
      | Binding Id  | Workflow                |
      | unit_period | Layout Monitor Workflow |
    And the following Currency:
      | Name      | Iso 4217 Code |
      | Euro      | EUR           |
      | US Dollar | USD           |
    And the following Unit:
      | Id | Ref Id | Name | Currency |
      | 1  | RefId1 | Unit | USD      |
      | 2  | RefId2 | Unit | USD      |
      | 3  | RefId3 | Unit | USD      |
    And the following System Setting:
      | Id                   | Value |
      | application_currency | EUR   |
    And the following Period:
      | Id | Name            | Previous Period |
      | 1  | Previous Period |                 |
      | 2  | Current Period  | Previous Period |
    And the following Authorization:
      | Name             | Item        | Rights                       |
      | Unit Period Full | UNIT_PERIOD | CREATE, READ, UPDATE, DELETE |
    And the following Unit Period:
      | Unit   | Period         | Status |
      | RefId1 | Current Period | open   |
      | RefId2 | Current Period | open   |
      | RefId3 | Current Period | open   |
    And I have the authorization "Unit Period Full"
    And I am logged in

  Scenario: A user list, create, updates and deletes a unit period
    Given I am assigned to unit "RefId1"
    When I am on "/tax-accounting/unit-period"
    Then I should see the following table:
      | Unit Name |
      | Unit      |

    # Create a new one
    When I click the "New" button in "Page Controls"
    Then I should be on "/tax-accounting/unit-period/new"
    When I fill in the "Unit Period" form with:
      | Unit   | RefId1          |
      | Period | Previous Period |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tax-accounting/unit-period/4/edit"
    And I should see a success message

    # Update the record
    When I fill in the "Unit Period" form with:
      | Period | Current Period |
    And I press "Save"
    Then I should be on "/tax-accounting/unit-period/4/edit"
    And I should see a success message

    # Delete the record
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/tax-accounting/unit-period"
    And I should see a success message
    And I should not see "RefId2"
