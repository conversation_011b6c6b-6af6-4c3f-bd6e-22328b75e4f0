@clear-database
Feature: Files
  In order to manage files
  As a user
  I should be able to perform read and update actions on file records

  Background:
    Given the following System Setting:
      | Id                             | Value           |
      | security.file_upload_whitelist | ["text\/plain"] |
    And the following Currency:
      | Name      | Iso 4217 Code |
      | Euro      | EUR           |
    And the following System Setting:
      | Id                   | Value |
      | application_currency | EUR   |
    And I am logged in

  Scenario: Updating a File record
    Given the following File:
      | Name   | Description        | Access Type | Path                    |
      | A File | A File description | protected   | /path/to/file/protected |
    And I have view, edit, delete and owner permission to File "A File"
    And I am on "/files/1"
    When I fill in the "file" form with:
      | description | A File updated description |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/files/1"

  Scenario: Uploading a new file
    Given I am on "/files/new"
    Then I should see "Select a file"
    When I attach the file "test.txt" to "uploadedFile"
    And I fill in the "file" form with:
      | description | Uploaded without attaching |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/files/1"

  Scenario: Uploading and attaching a new file
    Given the following Unit:
      | Ref Id     | Name |
      | RefId Unit | Unit |
    And I am assigned to unit "RefId Unit"
    And I am on "/units/1"
    When I click on the new attachment button
    And I attach the file "test.txt" to "uploadedFile"
    And I click the "Upload" button in the dialog
    Then I should see a success message
    And I should see "test.txt"

  Scenario: Attaching an existing file
    Given the following User:
      | Username  |
      | Fileowner |
    And the following Unit:
      | Ref Id     | Name |
      | RefId Unit | Unit |
    And the following File:
      | Name         | Description              | Access Type | Path                | Created By | Updated By |
      | File to Link | File to Link description | smart       | /path/to/file/smart | Fileowner  | Fileowner  |
    And I have view permission to File "File to Link"
    And I am assigned to unit "RefId Unit"
    And I am on "/units/1"
    When I click on the new attachment button
    And I click the "Link an existing file" button
    Then I should see a table with "File to Link" in the "Name" column
    And I check the radio button "File to Link"
    And I click the "Link" button in the dialog
    Then I should see a success message
    And I should see "File to Link"

  Scenario: Names of uploaded files can be protected
    Given the following User:
      | Username  |
      | Fileowner |
    And the following File:
      | Name                       | Description                | Access Type | Path                    | Created By | Updated By |
      | File with public access    | File with public access    | public      | /path/to/file/public    | Fileowner  | Fileowner  |
      | File with smart access     | File with smart access     | smart       | /path/to/file/smart     | Fileowner  | Fileowner  |
      | File with protected access | File with protected access | protected   | /path/to/file/protected | Fileowner  | Fileowner  |
    And the following Unit:
      | Ref Id          | Name | Files                                                                       |
      | RefId Test Unit | Unit | File with public access, File with smart access, File with protected access |
    And I am assigned to unit "RefId Test Unit"
    When I go to "/units/1"
    Then I should see "File with public access"
    And I should see "File with smart access"
    And I should not see "File with protected access"
    And I should see "(The name of this file is restricted)"
