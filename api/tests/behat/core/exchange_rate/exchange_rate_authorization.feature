@clear-database
Feature: Exchange Rate Authorisation
  As a user without authorization to manage exchange rates
  I should have no access to any feature of Exchange Rates

  Background:
    Given the following Period:
      | Id | Name        |
      | 1  | Period 2012 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Exchange Rate:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period      |
      | TEU            | EUR             | 2           | Average            | Period 2012 |
    And I am logged in

  Scenario: A User without authorization tries to edit a Exchange Rate
    When I go to "/periods/1/exchange-rates/1"
    Then I should see "403 Access Denied"

  Scenario: A User without authorization tries to create a Exchange Rate
    Given I go to "/periods/0/exchange-rates/new"
    Then I should see "403 Access Denied"

  Scenario: A User with the required authorisation accessing the new page over the list
    Given I have the role ROLE_PERIOD_MANAGER
    And I am on "/periods/1"
    When I click the "Add New Exchange Rate" button
    Then I should be on "/periods/1/exchange-rates/new"

  Scenario: A User with the required authorisation accessing the edit page over the list
    Given I have the role ROLE_PERIOD_MANAGER
    And I am on "/periods/1"
    When I click "Edit" on the table row for "Average"
    Then I should be on "/periods/1/exchange-rates/1"
