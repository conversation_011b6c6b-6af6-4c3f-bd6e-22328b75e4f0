@clear-database
Feature: Exchange Rate
  In order to manage exchange rates
  As a user authorized to manage exchange rates
  I should be able to perform create, read, update and delete actions on Exchange Rate records

  Background:
    Given the following Period:
      | Id | Name     |
      | 1  | Period 1 |
      | 2  | Period 2 |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Exchange Rate:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period   |
      | TEU            | EUR             | 2           | Average            | Period 1 |
      | EUR            | TEU             | 4           | Average            | Period 2 |
    And I have the role ROLE_PERIOD_MANAGER
    And I am logged in

  Scenario: When creating a new issue, the last filtered period is used to pre-populate the period field
    Given I am on "/periods/1"
    And I click the "Add New Exchange Rate" button
    Then I should be on "/periods/1/exchange-rates/new"

  Scenario: An authorized user lists exchange rates for a specific period
    When I go to "/periods/1"
    Then I should see "Average"
    And I should see the following table:
      | Type    | Input Currency | Output Currency | Direct Quotation | Indirect Quotation |
      | Average | TEU            | EUR             | 2.0000000000     | 0.5000000000       |

  Scenario: An authorized user creates a new record using the direct quotation
    Given I am on "/periods/1/exchange-rates/new"
    When I fill in the "Exchange Rate" form with:
      | Output Currency    | TEU     |
      | Input Currency     | EUR     |
      | Exchange Rate Type | average |
      | Direct Rate        | 2       |
    And I press "Save"
    Then I should see a success message
    And I should be on "/periods/1/exchange-rates/3"
    And the "Exchange Rate" form field "Direct Rate" should be "2.0000000000"

  Scenario: An authorized user creates a new record using the indirect quotation
    Given I am on "/periods/2/exchange-rates/new"
    When I fill in the "Exchange Rate" form with:
      | Output Currency          | TEU     |
      | Input Currency           | EUR     |
      | Exchange Rate Type       | current |
      | Indirect Quotation Value | 0.5     |
    And I press "Save"
    Then I should see a success message
    And I should be on "/periods/2/exchange-rates/3"
    And the "Exchange Rate" form field "Direct Rate" should be "2.0000000000"

  Scenario: An authorized user updates a record
    Given I am on "/periods/1/exchange-rates/1"
    When I fill in form field "Direct Quotation Value" with "8"
    And I press "Save"
    Then I should see a success message
    And I should be on "/periods/1/exchange-rates/1"
    And the "Exchange Rate" form field "Direct Rate" should be "8.0000000000"

  Scenario: An authorized user deletes a record
    Given I am on "/periods/2/exchange-rates/2"
    When I click the "Delete" button in "Page Controls"
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/periods/2"
    And I should see a success message
    And I should not see "Exchange Rate 2"

  Scenario: An authorized user deletes a record from the list
    Given I am on "/periods/2"
    When I click "Delete" on the table row for "Average"
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    And I should see a success message
    And I should not see "Exchange Rate 2"

  Scenario: An authorized user tries to delete a record where the period is closed
    Given The period "Period 2" is closed
    When I go to "/periods/2/exchange-rates/2"
    Then the "Delete" button should be disabled

  Scenario: An authorized user tries to create a record where the period is closed
    Given The period "Period 1" is closed
    And I am on "/periods/1"
    Then I should see "Average"
    And the "Add New Exchange Rate" button should be disabled

  Scenario: An authorized user tries to edit a record where the period is closed
    Given The period "Period 1" is closed
    When I go to "/periods/1/exchange-rates/1"
    Then the "Save" button should be disabled
