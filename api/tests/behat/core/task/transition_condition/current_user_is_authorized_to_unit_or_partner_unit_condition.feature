@clear-database
Feature: Transition Condition
  As a task user
  I should only be able to transition when all conditions are met

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | closed      |
    And the following Transition:
      | Name                                    | Origin status | Destination Status |
      | Transition unit access required         | open          | in progress        |
      | Transition partner unit access required | open          | in progress        |
    And the following Workflow:
      | Name          | Initial Status | Transitions                                                              |
      | Task Workflow | open           | Transition unit access required, Transition partner unit access required |
    And the following CurrentUserIsAuthorisedToUnitAndOrPartnerUnitCondition:
      | Transition                              | Requires Authorization To |
      | Transition unit access required         | 1                         |
      | Transition partner unit access required | 2                         |
    And the following Workflow Binding:
      | Binding Id      | Workflow      |
      | tpm_transaction | Task Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | German  |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId2 | Legal Unit 2 | TEU      | German  |
    And the following Billing Type:
      | Name       |
      | Allocation |
    And the following Transaction Type:
      | Name |
      | ARIS |
    And the following Period:
      | Name |
      | 2018 |
    And the following TPM Transaction:
      | Name          | Unit         | Partner Unit | Period | Transaction Currency | Type | Billing Type | Unit Requires Documentation | Partner Unit Requires Documentation |
      | Transaction 1 | Legal Unit 1 | Legal Unit 2 | 2018   | TEU                  | ARIS | Allocation   | 1                           | 0                                   |
    And the following Authorization:
      | Name                         | Item            | Rights       |
      | TPM Transaction write access | TPM_TRANSACTION | READ, UPDATE |
    And I am logged in
    And I have the authorization "TPM Transaction write access"

  Scenario: Transition is not allowed when user has not access to partner unit
    Given I am assigned to unit "RefId1"
    And I am on "/tpm/transaction/1/edit"
    When I click the "open" button in "Page Controls"
    And I click the "Transition partner unit access required" button
    And I click the "Change status to IN PROGRESS" button
    Then I should see "The current user must be authorised to the partner unit"
    And 0 Status Transition Log should have been created

  Scenario: Transition is not allowed when user has not access to unit
    Given I am assigned to unit "RefId2"
    And I am on "/tpm/transaction/1/edit"
    When I click the "open" button in "Page Controls"
    And I click the "Transition unit access required" button
    And I click the "Change status to IN PROGRESS" button
    Then I should see "The current user must be authorised to the unit"
    And 0 Status Transition Log should have been created

  Scenario: Transition is allowed when user has access to partner unit
    Given I am assigned to unit "RefId1"
    And I am on "/tpm/transaction/1/edit"
    When I click the "open" button in "Page Controls"
    And I click the "Transition unit access required" button
    And I click the "Change status to IN PROGRESS" button
    Then 1 Status Transition Log should have been created

  Scenario: Transition is allowed when user has access to partner unit
    Given I am assigned to unit "RefId2"
    And I am on "/tpm/transaction/1/edit"
    When I click the "open" button in "Page Controls"
    And I click the "Transition partner unit access required" button
    And I click the "Change status to IN PROGRESS" button
    Then 1 Status Transition Log should have been created
