@clear-database
Feature: Transition Condition
  As a task user
  I should only be able to transition when all conditions are met

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | closed      |
    And the following Transition:
      | Name                      | Origin status | Destination Status |
      | Transition with condition | open          | in progress        |
    And the following Workflow:
      | Name          | Initial Status | Transitions               |
      | Task Workflow | open           | Transition with condition |
    And the following CurrentUserIsNotCreatorCondition:
      | Transition                |
      | Transition with condition |
    And the following Workflow Binding:
      | Binding Id         | Workflow      |
      | tcm_other_deadline | Task Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Deadline Type:
      | Name            |
      | Deadline Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | German  |
    And the following TCM Other Deadline:
      | Id | Unit   | Description      | Tax Year | Deadline Type   |
      | 1  | RefId1 | Other Deadline 1 | 2014     | Deadline Type 1 |
    And the following Authorization:
      | Name                              | Item               | Rights               |
      | TCM Other Deadlines UPDATE Access | TCM_OTHER_DEADLINE | CREATE, READ, UPDATE |
    And I have the authorization "TCM Other Deadlines UPDATE Access"
    And I am logged in
    And I am assigned to unit "RefId1"

  Scenario: Transition is allowed when user is not creator
    Given I am on "/tcm/other-deadline/1/edit"
    When I click the "open" button in "Page Controls"
    And I click the "Transition with condition" button
    And I click the "Change status to IN PROGRESS" button
    Then 1 Status Transition Log should have been created

  Scenario: Transition is not allowed when user is creator
    Given I am on "/tcm/other-deadline/new"
    When I fill in the "Other Deadline" form with:
      | Unit                | Legal Unit 1     |
      | Tax Year            | 2018             |
      | Description         | Other Deadline 3 |
      | Deadline Type       | Deadline Type 1  |
      | Late Filing Penalty | 1337             |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/tcm/other-deadline/2/edit"
    When I click the "open" button in "Page Controls"
    And I click the "Transition with condition" button
    And I click the "Change status to IN PROGRESS" button
    Then I should see "User must not be the creator"
    And 0 Status Transition Log should have been created

