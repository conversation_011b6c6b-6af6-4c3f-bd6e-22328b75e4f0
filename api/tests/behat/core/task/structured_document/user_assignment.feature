@clear-database
Feature: User Assignment
  As a user
  I should be able to assign users to tasks from the the edit content page

  Background:
    Given there is a user named testuser
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | TPM Local File Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Period:
      | Name        |
      | Period 2012 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Germany |
    And the following Authorization:
      | Name                   | Item           | Rights                          |
      | Local File Full Access | TPM_LOCAL_FILE | READ, ASSIGN, CREATE, SUPERVISE |
    And user testuser has the authorization "Local File Full Access"
    And testuser has view permission to TPM Local File "Local File 1"
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: Assigning a user to a Local File from the configuration page
    Given I have view, edit, delete and owner permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file/1/edit"
    When I click the button with locator "Assign" in "Entity Information Section"
    Then I should see "Assign User"
    When I fill in the "Assign to User" form with:
      | Assignee | testuser |
    And I click the "Assign" button in the dialog
    Then I should be on "/tpm/local-file/1/edit"
    And I should see a success message

  Scenario: Assigning a user to a Local File from the edit page
    Given I have view, edit, delete and owner permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file/1/edit"
    When I click the button with locator "Assign" in "Entity Information Section"
    Then I should see "Assign User"
    When I fill in the "Assign to User" form with:
      | Assignee | testuser |
    And I click the "Assign" button in the dialog
    Then I should be on "/tpm/local-file/1/edit"
    And I should see a success message
