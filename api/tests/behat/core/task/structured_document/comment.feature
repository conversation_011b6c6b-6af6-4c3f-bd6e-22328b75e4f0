@clear-database
Feature: Commenting on tasks
  As a structured document user
  I should be able to comments on tasks from the edit content page

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions     |
      | TPM Local File Workflow | open           | Start, Complete |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Period:
      | Name        |
      | Period 2015 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country |
      | Local File 1 | Period 2015 | Unit Hierarchy 1 | Germany |
    And the following Authorization:
      | Name                   | Item           | Rights                    |
      | Local File Full Access | TPM_LOCAL_FILE | READ, CREATE, SUPERVISE |
    And I have the authorization "Local File Full Access"
    And I am logged in

  Scenario: Adding a comment
    Given I have view, edit, delete and owner permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file/1/edit-document"
    When I click the "New" button in "Comments"
    And I fill in form field "comment" with "This is a Comment"
    And I click the "Post comment" button
    Then I should see a success message
    And I should see "This is a Comment"
