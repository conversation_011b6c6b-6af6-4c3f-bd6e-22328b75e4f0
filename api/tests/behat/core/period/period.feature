@clear-database
Feature: Period
  In order to manage periods
  As an Admin
  I should be able to perform create, read, update and delete actions on Period records

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 02.01.2012 |
      | Period 2013 | 01.01.2013 | 02.01.2013 |

  Scenario: An Admin lists the Periods
    Given I am logged in
    When I go to "/periods"
    Then I should see the following table:
      | Name        |
      | Period 2013 |
      | Period 2012 |

  Scenario: A user creates a new period
    Given I am logged in
    And I have the role ROLE_PERIOD_MANAGER
    And I am on "/periods/new"
    When I fill in the "period" form with:
      | name      | Period     |
      | startDate | 01.01.2012 |
      | endDate   | 02.01.2012 |
    And I press "Save"
    Then I should be on "/periods/3"
    And I should see a success message

  Sc<PERSON><PERSON>: Updating the period name
    Given I am logged in
    And I have the role ROLE_PERIOD_MANAGER
    And I am on "/periods/2"
    And the "Save" button in "Page Controls" should be enabled
    When I fill in the "period" form with:
      | name | Test |
    And I press "Save"
    Then I should be on "/periods/2"
    And I should see a success message

  Sc<PERSON><PERSON>: Deleting a period that is not used
    Given I am logged in
    And I have the role ROLE_PERIOD_MANAGER
    And I am on "/periods/2"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/periods"
#    Then I should see a success message
    And I should not see "Period 2013"

  Scenario: Deleting a period that is used
    Given I am logged in
    And I have the role ROLE_PERIOD_MANAGER
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following exchange rate:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period      |
      | TEU            | EUR             | 523         | Average            | Period 2013 |
    And I am on "/periods/2"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete Period" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see an error message
    And I should be on "/periods/2"
