@clear-database
Feature: Period Navigation
  In order to manage Period
  As an Admin
  I should be able to navigate through the Period pages

  Background:
    Given the following Period:
      | Name        | Start Date | End Date   |
      | Period 2012 | 01.01.2012 | 02.01.2012 |
    And I have the role ROLE_PERIOD_MANAGER
    And I am logged in

  Scenario: A User with the required authorisation navigates through the menu to the list page
    Given I am on the homepage
    When I click "Periods" in the menu under "Tools"
    Then I should be on "/periods"

  Scenario: A User with the required authorisation accessing the new page over the list page
    Given I am on "/periods"
    When I click the "New" button in "Page Controls"
    Then I should be on "/periods/new"

  Scenario: A User with the required authorisation accessing the edit form
    Given I am on "/periods"
    When I click "Edit" on the table row for "Period 2012"
    Then I should be on "/periods/1"
