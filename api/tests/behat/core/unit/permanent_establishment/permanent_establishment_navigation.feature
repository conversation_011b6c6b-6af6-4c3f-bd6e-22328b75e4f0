@clear-database
Feature: Permanent Establishment Navigation
  In order to manage Permanent Establishments
  As a user allowed to a Permanent Establishment
  I should be able to navigate through the Permanent Establishment pages

  Background:
    Given the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Legal Unit:
      | Ref Id       | Name       | Currency | Country |
      | Legal Unit 1 | Legal Unit | EUR      | Germany |
    And the following Permanent Establishment:
      | Ref Id   | Name                    | Currency | Country | Parent Legal Unit |
      | RefId PE | Permanent Establishment | EUR      | Germany | Legal Unit 1      |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: A User navigates through the menu to the list page
    Given I am on the homepage
    When I click "Unit" in the menu under "Tools"
    Then I should be on "/units"

  Scenario: A User accessing the new page over the list page
    Given I am on "/units"
    When I click the "New" button in "Page Controls"
    And I choose "Permanent Establishment" from the selection list
    Then I should be on "/units/new?type=permanent-establishment"

  Scenario: A User accessing the edit page
    Given I am assigned to unit "RefId PE"
    And I am on "/units"
    When I click "Edit" on the table row for "Permanent Establishment"
    Then I should be on "/units/2"
