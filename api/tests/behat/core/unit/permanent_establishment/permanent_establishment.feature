@clear-database
Feature: Permanent Establishment
  In order to manage units
  As an authorized user
  I should be able to perform with the right authorizations create, read, update and delete actions on Permanent Establishment records

  Background:
    Given the following Currency:
      | Iso4217Code | Name |
      | EUR         | Euro |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Legal Unit:
      | Ref Id       | Name       | Currency | Country |
      | Legal Unit 1 | Legal Unit | EUR      | Germany |
    And the following Permanent Establishment:
      | Ref Id    | Name                            | Currency | Country | Legal Name                  | Parent Legal Unit |
      | RefId PE  | Permanent Establishment         | EUR      | Germany | Permanent Establishment XYZ | Legal Unit 1      |
      | RefId APE | Another Permanent Establishment | EUR      | Germany | Permanent Establishment ZYX | Legal Unit 1      |
    And I am logged in as an administrator
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: Creating a new Permanent Establishment record
    Given I am on "/units/new?type=permanent-establishment"
    And the "Save" button in "Page Controls" should be enabled
    When I fill in the "unit" form with:
      | refId           | Test RefId                   |
      | name            | Test Permanent Establishment |
      | parentLegalUnit | Legal Unit 1                 |
      | currency        | EUR - Euro                   |
      | country         | Germany                      |
    And I click the "Save" button
    Then I should see a success message
    And I should be on "/units/4"

  Scenario: Updating the name of a Permanent Establishment record
    Given I am assigned to unit "RefId PE"
    And I am on "/units/2"
    When I fill in the "unit" form with:
      | name | Test Permanent Establishment |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/2"

  Scenario: Deleting a Permanent Establishment record
    Given I am on "/units/3"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    Then I should be on "/units"
    And I should not see "Another Permanent Establishment"
