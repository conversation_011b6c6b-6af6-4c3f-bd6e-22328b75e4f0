@clear-database
Feature: Permanent Establishment Authorization
  As a user with no authorization to Permanent Establishments
  I should have no access to any feature of Permanent Establishments

  Background:
    Given the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Legal Unit:
      | Ref Id       | Name       | Currency | Country |
      | Legal Unit 1 | Legal Unit | EUR      | Germany |
    And the following Permanent Establishment:
      | Ref Id   | Name                    | Currency | Country | Parent Legal Unit |
      | RefId PE | Permanent Establishment | EUR      | Germany | Legal Unit 1      |
    And I am logged in

  Scenario: A user with no authorization to edit a specific unit tries to edit
    When I go to "/units/2"
    Then I should see "403 Access Denied"

  Scenario: A user with no authorization to manage units tries to create a Permanent Establishment
    When I go to "/units/new?type=permanent-establishment"
    Then I should see "403 Access Denied"

  Scenario: A user with no authorization to manage units tries to delete a Permanent Establishment
    And I am assigned to unit "RefId PE"
    When I go to "/units/2"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled

  Scenario: A user with no authorization to manage units tries to import Permanent Establishments
    When I am on "/imports/permanent-establishment/start"
    Then I should see "403 Access Denied"
