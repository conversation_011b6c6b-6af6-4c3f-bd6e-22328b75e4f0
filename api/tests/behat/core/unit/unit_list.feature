@clear-database
Feature: Unit List
  As a user
  I should be able to list and filter units

  Background:
    Given the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Unit:
      | Ref Id      | Name         |
      | RefId Unit  | Unit         |
      | RefId AUnit | Another Unit |
    And the following Legal Unit:
      | Ref Id    | Name               | Currency | Country |
      | RefId LU  | Legal Unit         | EUR      | Germany |
      | RefId ALU | Another Legal Unit | EUR      | Germany |
    And the following Permanent Establishment:
      | Ref Id    | Name                            | Currency | Country | Parent Legal Unit |
      | RefId PE  | Permanent Establishment         | EUR      | Germany | Legal Unit        |
      | RefId APE | Another Permanent Establishment | EUR      | Germany | Legal Unit        |
    And the following Organisational Group:
      | Ref Id    | Name                         | Currency |
      | RefId OG  | Organisational Group         | EUR      |
      | RefId AOG | Another Organisational Group | EUR      |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in


  Scenario: A user filters the units by unit type
    When I am on "/units"
    Then I should see the following table:
      | Ref. ID     | Name                            |
      | RefId Unit  | Unit                            |
      | RefId AUnit | Another Unit                    |
      | RefId LU    | Legal Unit                      |
      | RefId ALU   | Another Legal Unit              |
      | RefId PE    | Permanent Establishment         |
      | RefId APE   | Another Permanent Establishment |
      | RefId OG    | Organisational Group            |
      | RefId AOG   | Another Organisational Group    |
    And I fill in form field "UnitType" with "Legal Unit"
    Then I should see the following table:
      | Ref. ID   | Name               |
      | RefId LU  | Legal Unit         |
      | RefId ALU | Another Legal Unit |
