@clear-database
Feature: Unit Hierarchy Navigation
  In order to manage Unit Hierarchies
  As a user allowed to a Unit Hierarchy
  I should be able to navigate through the Unit Hierarchy pages

  Background:
    Given the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Yemen   |
    And the following Unit Hierarchy:
      | Name             | Description                  |
      | Unit Hierarchy 1 | Unit Hierarchy description 1 |
    And the following Unit Hierarchy Definition:
      | Unit Hierarchy   | Unit         | Parent Unit |
      | Unit Hierarchy 1 | Legal Unit 1 |             |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: A User navigates through the menu to the list page
    Given I am on the homepage
    When I click "Unit Hierarchies" in the menu under "Tools"
    Then I should be on "/units/hierarchies"

  Scenario: Visiting the unit hierarchy structure edit page
    Given I am on "/units/hierarchies"
    When I click "Structure" on the table row for "Unit Hierarchy 1"
    Then I should be on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"

  Scenario: Visiting the unit hierarchy attribute edit page
    Given I am on "/units/hierarchies"
    When I click "Attributes" on the table row for "Unit Hierarchy 1"
    Then I should be on "/units/hierarchies/1"

  Scenario: Visiting the unit hierarchy creation page
    Given I am on "/units/hierarchies"
    When I click the "New" button in "Page Controls"
    Then I should be on "/units/hierarchies/new"
