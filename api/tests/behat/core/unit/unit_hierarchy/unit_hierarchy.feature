@clear-database
Feature: Unit Hierarchy
  In order to manage units
  As an authorized user
  I should be able to perform with the right authorizations create, read, update and delete actions on Unit Hierarchy records

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
      | RefId3 | Legal Unit 3 | EUR      | Zambia  |
      | RefId4 | Legal Unit 4 | EUR      | Zambia  |
      | RefId5 | Legal Unit 5 | EUR      | Zambia  |
      | RefId6 | Legal Unit 6 | EUR      | Zambia  |
    And the following Unit Hierarchy:
      | Name             | Description                  |
      | Unit Hierarchy 1 | Unit Hierarchy description 1 |
      | Unit Hierarchy 2 | Unit Hierarchy description 2 |
      | Unit Hierarchy 3 | Unit Hierarchy description 3 |
    And the following Unit Hierarchy Definition:
      | Unit Hierarchy   | Unit         | Parent Unit  |
      | Unit Hierarchy 1 | Legal Unit 4 |              |
      | Unit Hierarchy 1 | Legal Unit 5 | Legal Unit 4 |
      | Unit Hierarchy 1 | Legal Unit 6 |              |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: Listing unit hierarchies
    When I am on "/units/hierarchies"
    Then I should see the following table portion:
      | Name             |
      | Unit Hierarchy 1 |
      | Unit Hierarchy 2 |
      | Unit Hierarchy 3 |

  Scenario: Creating a new unit hierarchy
    Given I am on "/units/hierarchies/new"
    When I fill in the "Unit Hierarchy" form with:
      | Name        | Unit Hierarchy 4         |
      | Description | Amazing Unit Hierarchy 4 |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/hierarchies/4"

  Scenario: Updating the unit hierarchy name attribute
    Given I am on "/units/hierarchies/1"
    When I fill in the "Unit Hierarchy" form with:
      | Name | Unit Hierarchy X |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/hierarchies/1"

  Scenario: Deleting a unit hierarchy
    Given I am on "/units/hierarchies/1"
    When I click the "Delete" button in "Page Controls"
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/units/hierarchies"
    And I should not see "Unit Hierarchy 1"

# TODO Fix the drag and drop tests
# It appears that selenium does not work with the native drag api.
# Maybe it is this problem: https://github.com/w3c/webdriver/issues/1488.
# We should keep an eye on the drivers and try occasionally to see if there is a fix.
# Ideally, these drag and drop tests should be in the frontend test suite since
# backend is only an API. Eg playwright/cypress/etc

#  Scenario: Adding an element to the unit hierarchy
#    Given I am on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    Then I should see "Unit Hierarchy 1"
#    Then I should see "RefId1 - Legal Unit 1"
#    When I drag "RefId1 - Legal Unit 1" from the unit-pool and I drop it in the unit-structure
#    And I click the "Save" button in "Page Controls"
#    Then I should see "Confirm"
#    When I click the "Save" button in the dialog
#    Then I should see a success message
#    And I should be on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    And I should see "Unit Hierarchy 1"
#    And "RefId1 - Legal Unit 1" is in the unit-structure

#  Scenario: Adding child nodes to a node in a unit hierarchy
#    Given I am on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    Then I should see "Unit Hierarchy 1"
#    When I drag "RefId1 - Legal Unit 1" from the unit-pool and I drop it on "RefId4 - Legal Unit 4"
#    And I drag "RefId2 - Legal Unit 2" from the unit-pool and I drop it on "RefId4 - Legal Unit 4"
#    And I drag "RefId3 - Legal Unit 3" from the unit-pool and I drop it on "RefId4 - Legal Unit 4"
#    And I click the "Save" button in "Page Controls"
#    Then I should see "Confirm"
#    When I click the "Save" button in the dialog
#    Then I should see a success message
#    And I should be on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    And "RefId1 - Legal Unit 1" is on "RefId4 - Legal Unit 4"
#    And "RefId2 - Legal Unit 2" is on "RefId4 - Legal Unit 4"
#    And "RefId3 - Legal Unit 3" is on "RefId4 - Legal Unit 4"

#  Scenario: Removing a node with it child nodes from a unit hierarchy
#    Given I am on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    Then I should see "Unit Hierarchy 1"
#    Then I should see "RefId4 - Legal Unit 4"
#    When I drag "RefId4 - Legal Unit 4" from the unit-structure and I drop it in the unit-pool
#    Then "RefId4 - Legal Unit 4" is in the unit-pool
#    And "RefId5 - Legal Unit 5" is in the unit-pool
#    When I click the "Save" button in "Page Controls"
#    Then I should see "Confirm"
#    When I click the "Save" button in the dialog
#    Then I should see a success message
#
#  Scenario: Removing a node from a unit hierarchy
#    Given I am on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    Then I should see "Unit Hierarchy 1"
#    Then I should see "RefId6 - Legal Unit 6"
#    When I drag "RefId6 - Legal Unit 6" from the unit-structure and I drop it in the unit-pool
#    And I click the "Save" button in "Page Controls"
#    Then I should see "Confirm"
#    When I click the "Save" button in the dialog
#    Then I should see a success message
#    And I should be on the Unit Hierarchy "editStructure" page of "Unit Hierarchy 1"
#    And "RefId6 - Legal Unit 6" is in the unit-pool
