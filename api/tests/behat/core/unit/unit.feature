@clear-database
Feature: Unit
  In order to manage units
  As an authorized user
  I should be able to perform with the right authorizations create, read, update and delete actions on Unit records

  Background:
    Given the following Unit:
      | Ref Id     | Name |
      | RefId Unit | Unit |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: Creating a new Unit record
    Given I am on "/units/new?type=unit"
    When I fill in the "unit" form with:
      | refId | Test RefId |
      | name  | Test Unit  |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/2"

  Scenario: Updating the name of a Unit record
    Given I am assigned to unit "RefId Unit"
    And I am on "/units/1"
    When I fill in the "unit" form with:
      | name | Test Unit |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/1"

  Scenario: Deleting a Unit record
    Given the following Unit:
      | Ref Id               | Name           |
      | RefId Unit To Remove | Unit To Remove |
    And I am on "/units/2"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    When I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/units"
    And I should not see "Unit To Remove"
