@clear-database
Feature: Unit Audit log
  In order to track changes in units
  As a user with the required authorizations
  I should be able to view the audit log of a unit

  Background:
    Given the following Unit:
      | Ref Id     | Name |
      | RefId Unit | Unit |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And I am logged in

  Scenario: A user views an empty audit log
    Given I am assigned to unit "RefId Unit"
    When I am on "/units/1"
    Then I should see "Changes"
    When I click on the element with text "Changes"
    Then I should see "No Changes"

  Scenario: A unit manager views an empty audit log
    Given I have the role ROLE_UNIT_MANAGER
    When I am on "/units/1"
    Then I should see "Changes"
    When I click on the element with text "Changes"
    Then I should see "No Changes"

  Scenario: A unit manager views an audited log
    Given I have the role ROLE_UNIT_MANAGER
    And the Unit where "name" is "Unit" has the following audit log entries:
      | field               | oldValue                 | newValue                 | timestamp  |
      | dummy-field-01-name | dummy-field-01-old-value | dummy-field-01-new-value | 1.01.2020  |
      | dummy-field-02-name | dummy-field-02-old-value | dummy-field-02-new-value | 2.01.2020  |
      | dummy-field-03-name | dummy-field-03-old-value | dummy-field-03-new-value | 3.01.2020  |
      | dummy-field-04-name | dummy-field-04-old-value | dummy-field-04-new-value | 4.01.2020  |
      | dummy-field-05-name | dummy-field-05-old-value | dummy-field-05-new-value | 5.01.2020  |
      | dummy-field-06-name | dummy-field-06-old-value | dummy-field-06-new-value | 6.01.2020  |
      | dummy-field-07-name | dummy-field-07-old-value | dummy-field-07-new-value | 7.01.2020  |
      | dummy-field-08-name | dummy-field-08-old-value | dummy-field-08-new-value | 8.01.2020  |
      | dummy-field-09-name | dummy-field-09-old-value | dummy-field-09-new-value | 9.01.2020  |
      | dummy-field-10-name | dummy-field-10-old-value | dummy-field-10-new-value | 10.01.2020 |
      | dummy-field-11-name | dummy-field-11-old-value | dummy-field-11-new-value | 11.01.2020 |
      | dummy-field-12-name | dummy-field-12-old-value | dummy-field-12-new-value | 12.01.2020 |
    When I am on "/units/1"
    Then I should see "Changes"
    When I click on the element with text "Changes"
    Then I should see "dummy-field-12-name"
    But I should not see "dummy-field-01-name"
    When I scroll to the bottom of the element "[data-testid='scroll-container']"
    Then I should see "dummy-field-01-name"

  Scenario: A user views an audited log
    Given I am assigned to unit "RefId Unit"
    And the Unit where "name" is "Unit" has the following audit log entries:
      | field               | oldValue                 | newValue                 | timestamp  |
      | dummy-field-01-name | dummy-field-01-old-value | dummy-field-01-new-value | 1.01.2020  |
      | dummy-field-02-name | dummy-field-02-old-value | dummy-field-02-new-value | 2.01.2020  |
      | dummy-field-03-name | dummy-field-03-old-value | dummy-field-03-new-value | 3.01.2020  |
      | dummy-field-04-name | dummy-field-04-old-value | dummy-field-04-new-value | 4.01.2020  |
      | dummy-field-05-name | dummy-field-05-old-value | dummy-field-05-new-value | 5.01.2020  |
      | dummy-field-06-name | dummy-field-06-old-value | dummy-field-06-new-value | 6.01.2020  |
      | dummy-field-07-name | dummy-field-07-old-value | dummy-field-07-new-value | 7.01.2020  |
      | dummy-field-08-name | dummy-field-08-old-value | dummy-field-08-new-value | 8.01.2020  |
      | dummy-field-09-name | dummy-field-09-old-value | dummy-field-09-new-value | 9.01.2020  |
      | dummy-field-10-name | dummy-field-10-old-value | dummy-field-10-new-value | 10.01.2020 |
      | dummy-field-11-name | dummy-field-11-old-value | dummy-field-11-new-value | 11.01.2020 |
      | dummy-field-12-name | dummy-field-12-old-value | dummy-field-12-new-value | 12.01.2020 |
    When I am on "/units/1"
    Then I should see "Changes"
    When I click on the element with text "Changes"
    Then I should see "dummy-field-12-name"
    But I should not see "dummy-field-01-name"
    When I scroll to the bottom of the element "[data-testid='scroll-container']"
    Then I should see "dummy-field-01-name"

