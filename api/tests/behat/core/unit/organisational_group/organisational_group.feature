@clear-database
Feature: Organisational Group
  In order to manage units
  As an authorized user
  I should be able to perform with the right authorizations create, read, update and delete actions on Organisational Group records

  Background:
    Given the following Currency:
      | Iso4217Code | Name  |
      | TEU         | Teuro |
      | EUR         | Euro  |
    And the following Organisational Group:
      | Ref Id    | Name                         | Currency |
      | RefId OG  | Organisational Group         | TEU      |
      | RefId AOG | Another Organisational Group | EUR      |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: Creating a new Organisational Group record
    Given I am on "/units/new?type=organisational-group"
    And the "Save" button in "Page Controls" should be enabled
    When I fill in the "unit" form with:
      | refId    | Test RefId                |
      | name     | Test Organisational Group |
      | currency | EUR - Euro                |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/3"

  Scenario: Updating the name of a Organisational Group record
    Given I am assigned to unit "RefId OG"
    And I am on "/units/1"
    When I fill in the "unit" form with:
      | name | Test Organisational Group |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/1"

  Scenario: Deleting a Organisational Group record
    Given I am on "/units/2"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    Then I should be on "/units"
    And I should not see "Another Organisational Group"
