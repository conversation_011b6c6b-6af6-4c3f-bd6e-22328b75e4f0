@clear-database
Feature: Organisational Group Navigation
  In order to manage Organisational Groups
  As a user allowed to a Organisational Group
  I should be able to navigate through the Organisational Group pages

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Organisational Group:
      | Ref Id    | Name                         | Currency |
      | RefId OG  | Organisational Group         | TEU      |
      | RefId AOG | Another Organisational Group | EUR      |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: A User navigates through the menu to the list page
    Given I am on the homepage
    When I click "Unit" in the menu under "Tools"
    Then I should be on "/units"

  Scenario: A User accessing the new page over the list page
    Given I am on "/units"
    When I click the "New" button in "Page Controls"
    And I choose "Organisational Group" from the selection list
    Then I should be on "/units/new?type=organisational-group"

  Scenario: A User accessing the edit page
    Given I am assigned to unit "RefId OG"
    And I am on "/units"
    When I click "Edit" on the table row for "Organisational Group"
    Then I should be on "/units/1"
