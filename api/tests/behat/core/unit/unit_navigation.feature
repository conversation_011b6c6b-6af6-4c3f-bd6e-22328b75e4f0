@clear-database
Feature: Unit Navigation
  In order to manage Units
  As a user allowed to a Unit
  I should be able to navigate through the Unit pages

  Background:
    Given the following Unit:
      | Ref Id      | Name        |
      | RefId Unit  | Unit        |
      | RefId AUnit | AnotherUnit |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: A User navigates through the menu to the list page
    Given I am on the homepage
    When I click "Unit" in the menu under "Tools"
    Then I should be on "/units"

  Scenario: A User with the required authorisation accessing the new page over the list page
    Given I am on "/units"
    When I click the "New" button in "Page Controls"
    And I choose "Legal Unit" from the selection list
    Then I should be on "/units/new?type=legal-unit"

  Scenario: A User with the required authorisation accessing the edit page
    Given I am assigned to unit "RefId Unit"
    And I am on "/units"
    When I click "Edit" on the table row for "Unit"
    Then I should be on "/units/1"
