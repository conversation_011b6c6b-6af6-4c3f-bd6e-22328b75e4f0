@clear-database
Feature: Legal Unit Navigation
  In order to manage Legal Units
  As a user allowed to a Legal Unit
  I should be able to navigate through the Legal Unit pages

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id    | Name               | Currency | Country |
      | RefId LU  | Legal Unit         | TEU      | Yemen   |
      | RefId ALU | Another Legal Unit | EUR      | Zambia  |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: A User navigates through the menu to the list page
    Given I am on the homepage
    When I click "Unit" in the menu under "Tools"
    Then I should be on "/units"

  Scenario: A User accessing the new page over the list page
    Given I am on "/units"
    When I click the "New" button in "Page Controls"
    And I choose "Legal Unit" from the selection list
    Then I should be on "/units/new?type=legal-unit"

  Scenario: A User accessing the edit page
    Given I am assigned to unit "RefId LU"
    And I am on "/units"
    When I click "Edit" on the table row for "Legal Unit"
    Then I should be on "/units/1"
