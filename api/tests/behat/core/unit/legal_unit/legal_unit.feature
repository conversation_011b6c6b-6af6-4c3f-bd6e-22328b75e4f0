@clear-database
Feature: Legal Unit
  In order to manage units
  As an authorized user
  I should be able to perform with the right authorizations create, read, update and delete actions on Legal Unit records

  Background:
    Given the following Currency:
      | Iso4217Code | Name  |
      | TEU         | Teuro |
      | EUR         | Euro  |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id    | Name               | Currency | Country | Legal Name                    |
      | RefId LU  | Legal Unit         | TEU      | Yemen   | Legal Unit Legal Name         |
      | RefId ALU | Another Legal Unit | EUR      | Zambia  | Another Legal Unit Legal Name |
    And I have the role ROLE_UNIT_MANAGER
    And I am logged in

  Scenario: Creating a new Legal Unit record
    Given I am on "/units/new?type=legal-unit"
    And the "Save" button in "Page Controls" should be enabled
    When I fill in the "unit" form with:
      | refId    | Test RefId      |
      | name     | Test Legal Unit |
      | currency | TEU - Teuro     |
      | country  | Zimbabwe        |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/3"

  Scenario: Updating the name of a Legal Unit record
    Given I am assigned to unit "RefId LU"
    And I am on "/units/1"
    And the "Save" button in "Page Controls" should be enabled
    When I fill in the "unit" form with:
      | name | Test Legal Unit |
    And I press "Save"
    Then I should see a success message
    And I should be on "/units/1"

  Scenario: Deleting a Legal Unit record
    Given I am on "/units/2"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    Then I should be on "/units"
    And I should not see "Another Legal Unit"
