@clear-database
Feature: Currency Authorisation
  As a user without admin permissions
  I should have no access to any feature of Currencies

  Background:
    Given the following Currency:
      | Name         | Iso 4217 Code |
      | Polish Zloty | PLN           |
    And I am logged in

  Scenario: A User without admin rights tries to edit a Currency
    When I go to "/configuration/currencies/1"
    Then I should see "403 Access Denied"

  Scenario: A User without admin rights tries to list the Currencies
    When I go to "/configuration/currencies"
    Then I should see "403 Access Denied"

  Scenario: A User without admin rights tries to create a Currency
    When I go to "/configuration/currencies/new"
    Then I should see "403 Access Denied"
