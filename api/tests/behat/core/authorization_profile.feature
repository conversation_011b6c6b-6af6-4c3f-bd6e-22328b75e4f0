@clear-database
Feature: Authorizations
  In order to manage the authorization profile
  As a user with ROLE_USER_GROUP_ADMIN
  I should be able to perform create, read, update and delete actions on authorization profile records

  Background:
    Given the following Authorization:
      | Name                         | Item                    | Rights |
      | Module Something Access      | MODULE_TAX_AUDIT_RISK   | READ   |
      | Module Something Half Access | MODULE_TRANSFER_PRICING | READ   |
    And the following Authorization Profile:
      | Name        | Authorizations                                        |
      | Full Access | Module Something Access, Module Something Half Access |
    And the following User Group:
      | Name     |
      | Group 1  |
      | Group 12 |

  Scenario: A user group admin lists the authorizations
    Given I am logged in as a user group administrator
    And I am on the homepage
    When I click "Authorisation" in the menu under "Tools"
    Then I should be on "/authorisations"
    And I should see "Full Access"
    And the "first" row in the table "authorization profile table" should contain "Full Access" in the column "Name"
    And the "first" row in the table "authorization profile table" should contain "Module Something Access Module Something Half Access" in the column "Authorisations"

  Scenario: A user group admin creates a new authorization profile record
    Given I am logged in as a user group administrator
    And I am on "/authorisations"
    When I click the "New" button in "Profile List"
    Then I should be on "/authorisations/profiles/new"
    When I fill in the "authorization_profile" form with:
      | name           | Test Authorization Profile |
      | authorizations | Module Something Access    |
    And I click the "Save" button
    Then I should be on "/authorisations/profiles/2"

  Scenario: A user group admin updates a authorization profile record
    Given I am logged in as a user group administrator
    And I am on "/authorisations"
    When I press "Edit" on the table row for "Full Access"
    Then I should be on "/authorisations/profiles/1"
    When I fill in the "authorization_profile" form with:
      | name | Full Access Renamed |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/authorisations/profiles/1"
    And I should see a success message

  Scenario: A user group admin deletes a authorization profile record
    Given I am logged in as a user group administrator
    And I am on "/authorisations"
    When I press "Delete" on the table row for "Full Access"
    Then I should see "Confirm deletion"
    And I click the "Delete" button in the dialog
    Then I should be on "/authorisations"
    And I should not see "Full Access"
    And I should see a success message

  Scenario: A user group admin assigns a user to an authorization record
    Given there is a user named testUser
    And I am logged in as a user group administrator
    And I am on "/authorisations/profiles/1"
    Then I should not see "testUser" in the "#authorised-users" element
    When I click the "edit" button in "Authorised Users"
    And I check "testUser"
    And I click the "Save" button in the dialog
    Then I should see "testUser" in the "#authorised-users" element

  Scenario: A user group admin assigns a group to an authorization profile record
    Given I am logged in as a user group administrator
    And I am on "/authorisations/profiles/1"
    Then I should not see "Group 1" in the "#authorised-user-groups" element
    And I should not see "Group 12" in the "#authorised-user-groups" element
    When I click the "edit" button in "Authorised User Groups"
    And I check "Group 1"
    And I check "Group 12"
    And I click the "Save" button in the dialog
    Then I should be on "/authorisations/profiles/1"
    And I should see "Group 1" in the "#authorised-user-groups" element
    And I should see "Group 12" in the "#authorised-user-groups" element

  Scenario: A user without user group admin rights tries to edit a authorization profile record
    Given I am logged in
    When I go to "/authorisations/profiles/1"
    Then I should see "403 Access Denied"

  Scenario: A user without user group admin rights tries to create a authorization profile record
    Given I am logged in
    When I go to "/authorisations/profiles/new"
    Then I should see "403 Access Denied"
