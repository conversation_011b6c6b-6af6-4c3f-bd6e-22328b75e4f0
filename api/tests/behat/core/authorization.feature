@clear-database
Feature: Authorization
  In order to access the authorizations
  As a user with ROLE_USER_GROUP_ADMIN
  I should be able visit the authorization pages

  Background:
    Given the following Authorization:
      | Name                         | Item                         | Rights |
      | Module Authorization         | MODULE_AUTHORIZATION         | READ   |
      | Another Module Authorization | ANOTHER_MODULE_AUTHORIZATION | READ   |
      | Test Module Authorization    | TEST_MODULE_AUTHORIZATION    | READ   |

  Scenario: A user group admin lists the authorizations
    Given I am logged in as a user group administrator
    And I am on the homepage
    When I click "Authorisation" in the menu under "Tools"
    Then I should be on "/authorisations"
    And I should see the following table:
      | Name                         | Item                         | Rights |
      | Module Authorization         | MODULE_AUTHORIZATION         | READ   |
      | Another Module Authorization | ANOTHER_MODULE_AUTHORIZATION | READ   |
      | Test Module Authorization    | TEST_MODULE_AUTHORIZATION    | READ   |

  Scenario: A user without user group admin rights tries to edit a authorization record
    Given I am logged in
    When I go to "/authorisations/1"
    Then I should see "403 Access Denied"

  Scenario: A user without user group admin rights tries to create a authorization record
    Given I am logged in
    When I go to "/authorisations/new"
    Then I should see "403 Access Denied"

  Scenario: A user without user group admin rights tries to list the authorizations
    Given I am logged in
    And I am on the homepage
    When I go to "/authorisations"
    Then I should see "403 Access Denied"



