@clear-database
Feature: APM Transactions
  In order to manage Transactions
  As a user allowed to the IGT Module with the required authorisation
  I should be able to perform create, read, update and delete actions on Transaction records

  Background:
    Given the following Status:
      | Type        | Name        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name                     | Initial Status | Transitions |
      | APM Transaction Workflow | in progress    | Complete    |
    And the following Workflow Binding:
      | Binding Id       | Workflow                 |
      | apm_transactions | APM Transaction Workflow |
    And the following Currency:
      | Iso4217Code |
      | GBP         |
      | EUR         |
    And the following Collateralisation Type:
      | Name                     |
      | Collateralisation Type 1 |
    And the following Period:
      | Name          | Closed |
      | Closed Period | true   |
      | Open Period   | false  |
    And the following Exchange Rate:
      | Input Currency | Output Currency | Direct Rate | Exchange Rate Type | Period      |
      | GBP            | EUR             | 523         | Average            | Open Period |
    And the following Country:
      | Iso3166Code | NameShort |
      | DE          | Germany   |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
      | RefId2 | Legal Unit 2 | EUR      | Germany |
      | RefId3 | Legal Unit 3 | EUR      | Germany |
    And the following APM Transaction:
      | Type | Period        | Description   | Unit         | Partner Unit | Internal Id | Coupon Interest Rate | Contract Date | Contract Expiry Date |
      | bond | Closed Period | Transaction 1 | Legal Unit 1 | Legal Unit 3 | APM ID 1    | 0.1                  |               |                      |
      | bond | Closed Period | Transaction 2 | Legal Unit 2 | Legal Unit 1 | APM ID 2    | 0.1                  | 05.09.2016    | 06.09.2016           |
      | bond | Closed Period | Transaction 3 | Legal Unit 2 | Legal Unit 3 | APM ID 2    | 0.1                  | 05.09.2016    | 06.09.2016           |
    And the following Authorization:
      | Name                    | Item            | Rights                       |
      | Transaction Full Access | APM_TRANSACTION | CREATE, READ, UPDATE, DELETE |
    And I have the authorization "Transaction Full Access"
    And I am logged in

  Scenario: A user without a record permission assigned visits the list page
    Given I am on "/apm/transaction?q="
    Then I should see "No results found"

  Scenario: When creating a new transaction, the last filtered period is used to pre-populate the period field
    Given I am on "/apm/transaction?q=Period = 'Closed Period'"
    When I click the "New" button in "Page Controls"
    # Check we see all sub types
    Then I should see "Bond"
    And I should see "Loan"
    And I should see "Promissory Note"
    And I should see "Equity Type - Shares/Participation"
    And I should see "Equity Type - Shares/Participation (real estate share deal)"
    And I should see "Other Asset Transfer - Properties (real estate asset deal)"
    And I should see "Assumption of Liabilities"
    When I choose "Bond" from the selection list
    Then I should be on "/apm/transaction/new?type=bond"
    And the "APM IGT Transaction" form field "Period" should be "Closed Period"

  Scenario: A user with the required authorisation creates, reads, updates and deletes an apm record
    Given I am assigned to unit "RefId1"
    And I am on "/apm/transaction?q="
    Then I should see a table with 2 rows
    Then I should see the following table portion:
      | Type | Unit Name    | Partner Unit Name |
      | Bond | Legal Unit 2 | Legal Unit 1      |
      | Bond | Legal Unit 1 | Legal Unit 3      |
    # Create
    When I click the "New" button in "Page Controls"
    And I choose "Bond" from the selection list
    Then I am on "/apm/transaction/new?type=bond"
    When I fill in the "APM IGT Transaction" form with:
      | Period                         | Open Period              |
      | Unit                           | Legal Unit 1             |
      | Partner Unit                   | Legal Unit 2             |
      | Internal ID                    | 123456                   |
      | Reporting Date                 | 30.04.2016               |
      | Contract Date                  | 01.04.2016               |
      | Contract Expiry Date           | 03.05.2016               |
      | Transaction Value > Base Value | 100                      |
      | Contract Party                 | Borrower                 |
      | Coupon Interest Rate Type      | Fixed                    |
      | Coupon Interest Rate           | 20                       |
      | Financing Purpose              | A Purpose                |
      | Repayment Conditions           | Some Conditions          |
      | Seniority                      | Senior                   |
      | Collateralisation Type         | Collateralisation Type 1 |
      | Transaction Currency           | GBP                      |
      | Description                    | Transaction 4            |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    # Read
    When I click the "List" button in "Page Controls"
    Then I should be on "/apm/transaction"
    And I should see a table with 3 rows
    When I click "Edit" on the table row for "Open Period"
    Then I should be on "/apm/transaction/4/edit"
    And the "APM IGT Transaction" form has the values:
      | Period                         | Open Period              |
      | Unit                           | RefId1 - Legal Unit 1    |
      | Partner Unit                   | RefId2 - Legal Unit 2    |
      | Internal ID                    | 123456                   |
      | Reporting Date                 | 2016-04-30               |
      | Contract Date                  | 2016-04-01               |
      | Contract Expiry Date           | 2016-05-03               |
      | Transaction Value > Base Value | 100                      |
      | Contract Party                 | Borrower                 |
      | Coupon Interest Rate Type      | Fixed                    |
      | Coupon Interest Rate           | 20.0000                  |
      | Financing Purpose              | A Purpose                |
      | Repayment Conditions           | Some Conditions          |
      | Seniority                      | Senior                   |
      | Collateralisation Type         | Collateralisation Type 1 |
      | Transaction Currency           | GBP                      |
      | Description                    | Transaction 4            |
    # Update
    When I fill in the "APM IGT Transaction" form with:
      | Transaction Value > Base Value | 200                  |
      | Coupon Interest Rate           | 30                   |
      | Description                    | Edited Transaction 4 |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/apm/transaction/4/edit"
    And the "APM IGT Transaction" form has the values:
      | Transaction Value > Base Value | 200                  |
      | Coupon Interest Rate           | 30.0000              |
      | Description                    | Edited Transaction 4 |
    # Delete
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/apm/transaction"
    And I should see a table with 2 rows
    And I should not see "Edited Transaction 4"

  Scenario: A user with the required authorisation tries to change records where the period is closed
    Given I am assigned to unit "RefId1"
    And I am assigned to unit "RefId2"

    When I go to "/apm/transaction/2/edit"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
    And the "Save" button should be disabled

    When I am on "/apm/transaction/new?type=bond"
    And I fill in the "APM IGT Transaction" form with:
      | Period                         | Closed Period            |
      | Unit                           | RefId1 - Legal Unit 1    |
      | Partner Unit                   | RefId2 - Legal Unit 2    |
      | Internal ID                    | 123456                   |
      | Reporting Date                 | 2016-04-30               |
      | Contract Date                  | 2016-04-01               |
      | Contract Expiry Date           | 2016-05-03               |
      | Transaction Value > Base Value | 100                      |
      | Contract Party                 | Borrower                 |
      | Coupon Interest Rate Type      | Fixed                    |
      | Coupon Interest Rate           | 20.0000                  |
      | Financing Purpose              | A Purpose                |
      | Repayment Conditions           | Some Conditions          |
      | Seniority                      | Senior                   |
      | Collateralisation Type         | Collateralisation Type 1 |
      | Transaction Currency           | GBP                      |
      | Description                    | Transaction 4            |
    And I press "Save"
    Then I should see an error message
