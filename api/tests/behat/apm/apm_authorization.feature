@clear-database
Feature: Authorization management
  In order to manage the authorizations
  As a user with ROLE_USER_GROUP_ADMIN
  I should be able to perform create, read, update and delete actions on authorizations records

  Background:
    Given the following Authorization:
      | Name            | Item            | Rights |
      | APM Transaction | APM_TRANSACTION | READ   |
    And the following Authorization Profile:
      | Name            | Authorizations  |
      | APM Full Access | APM Transaction |
    And the following User Group:
      | Name    |
      | Group 1 |
      | Group 2 |
    And I am logged in as a user group administrator

  <PERSON><PERSON><PERSON>: A user & group admin creates a new authorization record
    Given I am on "/authorisations"
    When I click the "New" button in "Authorization list"
    Then I should be on "/authorisations/new"
    When I fill in the "authorization" form with:
      | name | Test Authorization |
      | item | APM_TRANSACTION    |
    Then I should see "Select rights" in a dropdown
    When I fill in the "authorization" form field "rights" with "READ"
    And I click the "Save" button
    Then I should be on "/authorisations/2"

  Scenario: A user & group admin updates a authorization record
    Given I am on "/authorisations"
    When I click the "Edit" button in "authorization table"
    Then I should be on "/authorisations/1"
    When I fill in the "authorization" form with:
      | name | APM Transaction Renamed |
    And I click the "Save" button in "Page Controls"
    Then I should be on "/authorisations/1"
    And I should see a success message

  Scenario: A user & group admin deletes a authorization record
    Given I am on "/authorisations"
    When I click "Delete" on the table row for "APM Transaction"
    Then I should see "Confirm deletion"
    When I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/authorisations"
    And I should not see "Test Module Authorization"

  Scenario: A user & group admin assigns a user to an authorization record
    Given there is a user named testUser
    And I am on "/authorisations/1"
    Then I should not see "testUser" in the "#authorised-users" element
    When I click the "edit" button in "Authorised Users"
    And I check "testUser"
    And I click the "Save" button in the dialog
    Then I should be on "/authorisations/1"
    And I should see "testUser" in the "#authorised-users" element

  Scenario: A user & group admin assigns a group to an authorization record
    Given I am on "/authorisations/1"
    Then I should not see "Group 1" in the "#authorised-user-groups" element
    And I should not see "Group 2" in the "#authorised-user-groups" element
    When I click the "edit" button in "Authorised User Groups"
    And I check "Group 1"
    And I check "Group 2"
    And I click the "Save" button in the dialog
    Then I should be on "/authorisations/1"
    And I should see "Group 1" in the "#authorised-user-groups" element
    And I should see "Group 2" in the "#authorised-user-groups" element
