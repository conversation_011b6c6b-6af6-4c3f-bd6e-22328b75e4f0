<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Datasheets\Item\Formula\Element;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\ItemFactory;
use U2\Datasheets\Item\Formula\Element\CurrentPeriodItemValue;
use U2\Datasheets\Item\Formula\Element\NumberElementEvaluator;
use U2\Datasheets\Item\Formula\Element\PreviousPeriodItemValue;
use U2\Datasheets\Item\UnitHierarchyValue\UnitHierarchyPeriodContext;
use U2\Datasheets\Item\UnitHierarchyValue\UnitHierarchyValueCalculator;
use U2\Datasheets\Item\UnitValue\UnitPeriodContext;
use U2\Entity\NumberItemUnitValue;
use U2\Entity\Period;
use U2\Entity\Unit;
use U2\Entity\UnitHierarchy;
use U2\Repository\CachedItemUnitValueRepository;

class NumberElementEvaluatorTest extends UnitTestCase
{
    public function test_substitutes_current_period_number_elements_with_their_local_currency_value(): void
    {
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $evaluator = new NumberElementEvaluator($itemUnitValueRepository, $groupValueCalculator);
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $element = new CurrentPeriodItemValue('element id', $itemOfElement);
        $numberItemUnitValue = $this->createMock(NumberItemUnitValue::class);
        $unit = $this->createMock(Unit::class);
        $period = $this->createMock(Period::class);
        $itemUnitValueRepository->expects($this->once())->method('getFromCacheOrAdd')->with(
            self::equalTo($itemOfElement),
            self::equalTo($unit),
            self::equalTo($period)
        )->willReturn($numberItemUnitValue);
        $numberItemUnitValue->expects($this->once())->method('getValue')->willReturn('2');
        self::assertSame(
            '2',
            $evaluator->getValue($itemOfFormula, $element, new UnitPeriodContext($period, $unit))
        );
    }

    public function test_substitutes_previous_period_diff_elements_with_their_local_currency_value(): void
    {
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $evaluator = new NumberElementEvaluator($itemUnitValueRepository, $groupValueCalculator);
        $element = $this->createMock(PreviousPeriodItemValue::class);
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $numberItemUnitValue = $this->createMock(NumberItemUnitValue::class);
        $unit = $this->createMock(Unit::class);
        $period = $this->createMock(Period::class);
        $previousPeriod = $this->createMock(Period::class);
        $element->expects($this->once())->method('getItem')->willReturn($itemOfElement);
        $period->expects($this->once())->method('getPreviousPeriod')->willReturn($previousPeriod);
        $itemUnitValueRepository->expects($this->once())->method('getFromCacheOrAdd')->with(
            self::equalTo($itemOfElement),
            self::equalTo($unit),
            self::equalTo($previousPeriod)
        )->willReturn($numberItemUnitValue);
        $numberItemUnitValue->expects($this->once())->method('getValue')->willReturn('2');
        self::assertSame(
            '2',
            $evaluator->getValue($itemOfFormula, $element, new UnitPeriodContext($period, $unit))
        );
    }

    public function test_substitutes_previous_period_number_elements_without_a_previous_period_with0(): void
    {
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $evaluator = new NumberElementEvaluator($itemUnitValueRepository, $groupValueCalculator);
        $element = $this->createMock(PreviousPeriodItemValue::class);
        $itemOfFormula = ItemFactory::new()->money()->create();
        $unit = $this->createMock(Unit::class);
        $period = $this->createMock(Period::class);
        $period->expects($this->once())->method('getPreviousPeriod')->willReturn(null);
        $element->expects($this->never())->method('getItem');
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        self::assertSame(
            '0',
            $evaluator->getValue($itemOfFormula, $element, new UnitPeriodContext($period, $unit))
        );
    }

    public function test_substitutes_current_period_number_elements_for_a_unit_hierarchy_period_context(): void
    {
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $evaluator = new NumberElementEvaluator($itemUnitValueRepository, $groupValueCalculator);
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $element = new CurrentPeriodItemValue('element id', $itemOfElement);
        $unitHierarchy = $this->createMock(UnitHierarchy::class);
        $period = $this->createMock(Period::class);
        $groupValueCalculator->expects($this->once())->method('calculate')->with($itemOfElement, $unitHierarchy, $period)->willReturn('calculated value');
        $unitHierarchyPeriodContext = new UnitHierarchyPeriodContext($period, $unitHierarchy);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd')->with(self::anything());
        self::assertSame(
            'calculated value',
            $evaluator->getValue($itemOfFormula, $element, $unitHierarchyPeriodContext)
        );
    }

    public function test_substitutes_previous_period_number_elements_for_a_unit_hierarchy_period_context(): void
    {
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $evaluator = new NumberElementEvaluator($itemUnitValueRepository, $groupValueCalculator);
        $element = $this->createMock(PreviousPeriodItemValue::class);
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $unitHierarchy = $this->createMock(UnitHierarchy::class);
        $period = $this->createMock(Period::class);
        $previousPeriod = $this->createMock(Period::class);
        $element->expects($this->once())->method('getItem')->willReturn($itemOfElement);
        $period->expects($this->once())->method('getPreviousPeriod')->willReturn($previousPeriod);
        $groupValueCalculator->expects($this->once())->method('calculate')->with(self::equalTo($itemOfElement), self::equalTo($unitHierarchy), self::equalTo($previousPeriod))->willReturn('calculated value');
        $unitHierarchyPeriodContext = new UnitHierarchyPeriodContext($period, $unitHierarchy);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        self::assertSame(
            'calculated value',
            $evaluator->getValue($itemOfFormula, $element, $unitHierarchyPeriodContext)
        );
    }
}
