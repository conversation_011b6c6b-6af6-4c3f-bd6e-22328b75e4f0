<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Datasheets\Item\Formula\Element;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\CountryFactory;
use U2\DataFixtures\Example\ItemFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\Datasheets\Item\Formula\Element\CurrentPeriodItemValue;
use U2\Datasheets\Item\Formula\Element\PreviousPeriodItemValue;
use U2\Datasheets\Item\Formula\Element\TextElementEvaluator;
use U2\Datasheets\Item\UnitHierarchyCountryValue\UnitHierarchyCountryContext;
use U2\Datasheets\Item\UnitHierarchyCountryValue\UnitHierarchyCountryValueCalculator;
use U2\Datasheets\Item\UnitHierarchyValue\UnitHierarchyPeriodContext;
use U2\Datasheets\Item\UnitHierarchyValue\UnitHierarchyValueCalculator;
use U2\Datasheets\Item\UnitValue\UnitPeriodContext;
use U2\Entity\TextItemUnitValue;
use U2\Entity\Unit;
use U2\Entity\UnitHierarchy;
use U2\Repository\CachedItemUnitValueRepository;

class TextElementEvaluatorTest extends UnitTestCase
{
    public function test_substitutes_current_period_elements_for_a_unit_period_context(): void
    {
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $unit = $this->createMock(Unit::class);
        $period = PeriodFactory::getObject();
        $element = $this->createMock(CurrentPeriodItemValue::class);
        $element->expects($this->atLeastOnce())->method('getItem')->willReturn($itemOfElement);
        $itemUnitValue = $this->createMock(TextItemUnitValue::class);
        $itemUnitValue->expects($this->atLeastOnce())->method('getComment')->willReturn('text');
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $groupValueCalculator->expects($this->never())->method('calculate');
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->atLeastOnce())->method('getFromCacheOrAdd')->with($itemOfElement, $unit, $period)->willReturn($itemUnitValue);

        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $groupValueCalculator, $this->createMock(UnitHierarchyCountryValueCalculator::class));

        self::assertSame(
            'text',
            $evaluator->getValue($itemOfFormula, $element, new UnitPeriodContext($period, $unit))
        );
    }

    public function test_substitutes_current_period_elements_for_a_unit_hierarchy_period_context(): void
    {
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $unitHierarchy = new UnitHierarchy();
        $period = PeriodFactory::getObject();
        $element = $this->createMock(CurrentPeriodItemValue::class);
        $element->expects($this->atLeastOnce())->method('getItem')->willReturn($itemOfElement);
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $groupValueCalculator->expects($this->atLeastOnce())->method('calculate')->with($itemOfElement, $unitHierarchy, $period)->willReturn('calculated value');
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $groupValueCalculator, $this->createMock(UnitHierarchyCountryValueCalculator::class));

        self::assertSame(
            'calculated value',
            $evaluator->getValue($itemOfFormula, $element, new UnitHierarchyPeriodContext($period, $unitHierarchy))
        );
    }

    public function test_substitutes_previous_period_elements_for_a_unit_period_context(): void
    {
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $element = new PreviousPeriodItemValue('element id', $itemOfElement);
        $unit = $this->createMock(Unit::class);
        $period = PeriodFactory::getObject(['previousPeriod' => PeriodFactory::getObject()]);
        $previousPeriod = $period->getPreviousPeriod();
        $itemUnitValue = $this->createMock(TextItemUnitValue::class);
        $itemUnitValue->expects($this->atLeastOnce())->method('getComment')->willReturn('text');
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->atLeastOnce())->method('getFromCacheOrAdd')->with($itemOfElement, $unit, $previousPeriod)->willReturn($itemUnitValue);
        $groupValueCalculator->expects($this->never())->method('calculate');
        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $groupValueCalculator, $this->createMock(UnitHierarchyCountryValueCalculator::class));

        self::assertSame(
            'text',
            $evaluator->getValue($itemOfFormula, $element, new UnitPeriodContext($period, $unit))
        );
    }

    public function test_substitutes_previous_period_elements_for_a_unit_hierarchy_period_context(): void
    {
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $element = new PreviousPeriodItemValue('element id', $itemOfElement);
        $unitHierarchy = new UnitHierarchy();
        $period = PeriodFactory::getObject(['previousPeriod' => PeriodFactory::getObject()]);
        $previousPeriod = $period->getPreviousPeriod();
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $groupValueCalculator->expects($this->atLeastOnce())->method('calculate')->with($itemOfElement, $unitHierarchy, $previousPeriod)->willReturn('calculated value');
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $groupValueCalculator, $this->createMock(UnitHierarchyCountryValueCalculator::class));

        self::assertSame(
            'calculated value',
            $evaluator->getValue($itemOfFormula, $element, new UnitHierarchyPeriodContext($period, $unitHierarchy))
        );
    }

    public function test_substitutes_previous_period_elements_without_a_previous_period_with_na(): void
    {
        $element = new PreviousPeriodItemValue('element id', ItemFactory::new()->money()->create());
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        $groupValueCalculator = $this->createMock(UnitHierarchyValueCalculator::class);
        $groupValueCalculator->expects($this->never())->method('calculate');
        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $groupValueCalculator, $this->createMock(UnitHierarchyCountryValueCalculator::class));

        self::assertSame(
            'n/a',
            $evaluator->getValue($itemOfFormula, $element, new UnitPeriodContext(PeriodFactory::getObject(), $this->createMock(Unit::class)))
        );
    }

    public function test_substitutes_current_period_elements_for_a_unit_hierarchy_country_context(): void
    {
        $itemOfFormula = ItemFactory::new()->money()->create();
        $itemOfElement = ItemFactory::new()->money()->create();
        $unitHierarchy = new UnitHierarchy();
        $period = PeriodFactory::getObject();
        $country = CountryFactory::getObject();
        $element = $this->createMock(CurrentPeriodItemValue::class);
        $element->expects($this->atLeastOnce())->method('getItem')->willReturn($itemOfElement);
        $countryValueCalculator = $this->createMock(UnitHierarchyCountryValueCalculator::class);
        $countryValueCalculator->expects($this->atLeastOnce())->method('calculate')->with($itemOfElement, $unitHierarchy, $period, $country)->willReturn('calculated value');
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $this->createMock(UnitHierarchyValueCalculator::class), $countryValueCalculator);

        self::assertSame(
            'calculated value',
            $evaluator->getValue($itemOfFormula, $element, new UnitHierarchyCountryContext($period, $unitHierarchy, $country))
        );
    }

    public function test_substitutes_previous_period_elements_for_a_unit_hierarchy_country_context_with_na(): void
    {
        $itemOfFormula = ItemFactory::new()->money()->create();
        $unitHierarchy = new UnitHierarchy();
        $element = new PreviousPeriodItemValue('element id', ItemFactory::new()->money()->create());
        $countryValueCalculator = $this->createMock(UnitHierarchyCountryValueCalculator::class);
        $itemUnitValueRepository = $this->createMock(CachedItemUnitValueRepository::class);
        $itemUnitValueRepository->expects($this->never())->method('getFromCacheOrAdd');
        $evaluator = new TextElementEvaluator($itemUnitValueRepository, $this->createMock(UnitHierarchyValueCalculator::class), $countryValueCalculator);

        self::assertSame(
            'n/a',
            $evaluator->getValue($itemOfFormula, $element, new UnitHierarchyCountryContext(PeriodFactory::getObject(), $unitHierarchy, CountryFactory::getObject()))
        );
    }
}
