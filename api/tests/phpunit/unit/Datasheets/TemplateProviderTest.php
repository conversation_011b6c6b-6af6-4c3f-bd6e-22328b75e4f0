<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Datasheets;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Datasheets\TemplateProvider;
use U2\Datasheets\TemplateStorage;
use U2\Entity\Datasheet;

class TemplateProviderTest extends UnitTestCase
{
    private TemplateProvider $templateProvider;

    /**
     * @var MockObject&TemplateStorage
     */
    private MockObject $templateStorage;

    public function test_provides_the_content_of_a_layout_template(): void
    {
        $layout = $this->createMock(Datasheet::class);
        $this->templateStorage->expects($this->atLeastOnce())->method('getContent')->with(self::equalTo($layout))->willReturn('content');
        self::assertSame(<<<template
            {% import 'datasheet/template_macro.html.twig' as layout %}

            content
            template, $this->templateProvider->getContent($layout));
    }

    public function test_returns_null_if_the_template_does_not_exist(): void
    {
        $layout = $this->createMock(Datasheet::class);
        self::assertNull($this->templateProvider->getContent($layout));
    }

    protected function setUp(): void
    {
        $this->templateStorage = $this->createMock(TemplateStorage::class);
        $this->templateProvider = new TemplateProvider($this->templateStorage);
    }
}
