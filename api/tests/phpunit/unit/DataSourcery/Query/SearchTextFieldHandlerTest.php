<?php

declare(strict_types=1);
namespace Tests\Unit\U2\DataSourcery\Query;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\DataSourcery\DataSource\Configuration\Field;
use U2\DataSourcery\DataType\SearchTextDataType;
use U2\DataSourcery\DataType\StringDataType;
use U2\DataSourcery\Query\Filter;
use U2\DataSourcery\Query\FilterCondition;
use U2\DataSourcery\Query\SearchTextFieldHandler;
use U2\DataSourcery\Query\SearchTextFilterConditionTransformer;

class SearchTextFieldHandlerTest extends UnitTestCase
{
    private SearchTextFieldHandler $searchTextFieldHandler;

    /**
     * @var MockObject&SearchTextFilterConditionTransformer
     */
    private MockObject $transformer;

    protected function setUp(): void
    {
        $this->transformer = $this->createMock(SearchTextFilterConditionTransformer::class);
        $this->searchTextFieldHandler = new SearchTextFieldHandler($this->transformer);
    }

    public function test_handles_search_text_fields(): void
    {
        $searchTextFilterCondition = new FilterCondition('searchText', FilterCondition::METHOD_STRING_EQ, 'searched text', '');
        $stringFilterCondition = new FilterCondition('stringValue', FilterCondition::METHOD_STRING_EQ, 'test', '');
        $stringField = new Field('stringValue', 'stringValue', 'a dataSourceField', new StringDataType());
        $searchTextField = new Field('searchText', 'searchText', 'a dataSourceField', new SearchTextDataType());
        $this->transformer->expects($this->atLeastOnce())
            ->method('transform')->with(self::equalTo($searchTextFilterCondition))->willReturn(
                new Filter(
                    [
                        new FilterCondition('searchText', FilterCondition::METHOD_STRING_EQ, 'searched text', 'searched text'),
                    ]
                )
            );
        $filter = new Filter([$searchTextFilterCondition, $stringFilterCondition], Filter::CONDITION_TYPE_OR);
        $handledFilter = $this->searchTextFieldHandler->handle($filter, [$searchTextField, $stringField]);
        self::assertInstanceOf(Filter::class, $handledFilter);
        self::assertSame('(searchText = "searched text") or stringValue = "test"', $handledFilter->getUql());
        self::assertCount(2, $handledFilter->getConditions());
        self::assertInstanceOf(Filter::class, $handledFilter->getConditions()[0]);
        self::assertSame('searchText = "searched text"', $handledFilter->getConditions()[0]->getUql());
        self::assertCount(1, $handledFilter->getConditions()[0]->getConditions());
        self::assertInstanceOf(FilterCondition::class, $handledFilter->getConditions()[1]);
        self::assertSame('stringValue = "test"', $handledFilter->getConditions()[1]->getUql());
        self::assertSame($filter->getConditionType(), $handledFilter->getConditionType());
    }

    public function test_handles_search_text_fields_in_nested_filter_conditions(): void
    {
        $searchTextFilterCondition = new FilterCondition('searchText', FilterCondition::METHOD_STRING_EQ, 'searched text', '');
        $stringFilterCondition = new FilterCondition('stringValue', FilterCondition::METHOD_STRING_EQ, 'test', '');
        $searchTextField = new Field('searchText', 'searchText', 'a dataSourceField', new SearchTextDataType());
        $stringField = new Field('stringValue', 'stringValue', 'a dataSourceField', new StringDataType());
        $this->transformer->expects($this->atLeastOnce())
            ->method('transform')->with(self::equalTo($searchTextFilterCondition))->willReturn(
                new Filter(
                    [
                        new FilterCondition('searchText', FilterCondition::METHOD_STRING_EQ, 'searched text', 'searched text'),
                    ]
                )
            );
        $filter = new Filter([new Filter([$searchTextFilterCondition, $stringFilterCondition])], Filter::CONDITION_TYPE_OR);
        $handledFilter = $this->searchTextFieldHandler->handle($filter, [$stringField, $searchTextField]);
        self::assertInstanceOf(Filter::class, $handledFilter);
        self::assertSame('((searchText = "searched text") and stringValue = "test")', $handledFilter->getUql());
        self::assertCount(1, $handledFilter->getConditions());
        self::assertInstanceOf(Filter::class, $handledFilter->getConditions()[0]);
        self::assertSame('(searchText = "searched text") and stringValue = "test"', $handledFilter->getConditions()[0]->getUql());
        self::assertCount(2, $handledFilter->getConditions()[0]->getConditions());
        self::assertInstanceOf(Filter::class, $handledFilter->getConditions()[0]->getConditions()[0]);
        self::assertSame('searchText = "searched text"', $handledFilter->getConditions()[0]->getConditions()[0]->getUql());
        self::assertCount(1, $handledFilter->getConditions()[0]->getConditions()[0]->getConditions());
        self::assertInstanceOf(FilterCondition::class, $handledFilter->getConditions()[0]->getConditions()[1]);
        self::assertSame('stringValue = "test"', $handledFilter->getConditions()[0]->getConditions()[1]->getUql());
        self::assertSame($filter->getConditionType(), $handledFilter->getConditionType());
    }

    public function test_ignores_non_search_text_fields(): void
    {
        $filterCondition = new FilterCondition('not-search-text', FilterCondition::METHOD_STRING_EQ, 'test', '');
        $filter = new Filter([$filterCondition], Filter::CONDITION_TYPE_OR);
        $field = new Field('not-search-text', 'not-search-text', 'a field', new StringDataType());
        $handledFilter = $this->searchTextFieldHandler->handle(
            $filter,
            [
                $field,
            ]
        );
        self::assertInstanceOf(Filter::class, $handledFilter);
        self::assertSame('not-search-text = "test"', $handledFilter->getUql());
        self::assertCount(1, $handledFilter->getConditions());
        self::assertSame('not-search-text = "test"', $handledFilter->getConditions()[0]->getUql());
        self::assertSame($filterCondition, $handledFilter->getConditions()[0]);
        self::assertSame($filter->getConditionType(), $handledFilter->getConditionType());
        $this->transformer->expects($this->never())->method('transform')->with(self::anything(), [$field]);
    }
}
