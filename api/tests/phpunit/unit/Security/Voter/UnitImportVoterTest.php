<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\Security\Voter\UnitImportVoter;
use U2\Security\Voter\VoterAttributes;

class UnitImportVoterTest extends UnitTestCase
{
    public function test_supports_attribute(): void
    {
        $voter = new UnitImportVoter($this->createMock(Security::class));

        self::assertTrue($voter->supportsAttribute(VoterAttributes::import));
        self::assertFalse($voter->supportsAttribute('wrong attribute'));
    }

    public function test_supports_type(): void
    {
        $voter = new UnitImportVoter($this->createMock(Security::class));

        self::assertTrue($voter->supportsType('string'));
        self::assertFalse($voter->supportsType(VoterAttributes::class));
    }

    /**
     * @return array<string,array<string,int|bool>>
     */
    public static function provideVoterTestData(): array
    {
        return [
            'voter grants access when user is authorized' => [
                'expectedVoterDecision' => VoterInterface::ACCESS_GRANTED,
                'authorizationManagerDecision' => true,
            ],
            'voter denies access when user is not authorized' => [
                'expectedVoterDecision' => VoterInterface::ACCESS_DENIED,
                'authorizationManagerDecision' => false,
            ],
        ];
    }

    #[DataProvider('provideVoterTestData')]
    public function test_vote(int $expectedVoterDecision, bool $authorizationManagerDecision): void
    {
        $user = new User();
        $token = new UsernamePasswordToken($user, 'TEST_PROVIDER');
        $authorizationManager = $this->createMock(Security::class);

        $authorizationManager
            ->method('isGranted')
            ->with(UserRoles::UnitManager->value)
            ->willReturn($authorizationManagerDecision);
        $unitImportVoter = new UnitImportVoter($authorizationManager);
        $voterDecision = $unitImportVoter->vote($token, Unit::class, [VoterAttributes::import]);

        self::assertSame($expectedVoterDecision, $voterDecision);
    }

    /**
     * @covers \U2\Security\Voter\UnitImportVoter::supports
     */
    public function test_abstains_when_subject_string_is_not_a_unit_class_string(): void
    {
        $user = new User();
        $token = new UsernamePasswordToken($user, 'TEST_PROVIDER');
        $voter = new UnitImportVoter($this->createMock(Security::class));

        self::assertSame(VoterInterface::ACCESS_ABSTAIN, $voter->vote($token, VoterAttributes::class, [VoterAttributes::import]));
        self::assertSame(VoterInterface::ACCESS_ABSTAIN, $voter->vote($token, 'any string', [VoterAttributes::import]));
    }
}
