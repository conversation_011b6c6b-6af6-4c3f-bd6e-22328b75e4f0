<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\Security\Voter\UnitVoter;
use U2\Security\Voter\VoterAttributes;
use U2\Unit\Assignment\UserUnitAssignmentChecker;

class UnitVoterTest extends UnitTestCase
{
    #[DataProvider('provideVoterAttributes')]
    public function test_voter_grants_access_when_user_is_unit_manager(string $attribute): void
    {
        $currentUser = $this->createMock(User::class);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(true);

        $unitVoter = new UnitVoter($authorizationManager, $this->createMock(UserUnitAssignmentChecker::class));

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $this->getUnit(),
            [$attribute]
        );

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision');
    }

    /**
     * @return array<int, array<int, string>>
     */
    public static function provideVoterAttributes(): array
    {
        return [
            [VoterAttributes::delete],
            [VoterAttributes::read],
            [VoterAttributes::write],
            [VoterAttributes::addAttachment],
            [VoterAttributes::removeAttachment],
        ];
    }

    public function test_voter_grants_read_access_when_user_is_not_unit_manager_and_unit_is_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(true);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit,
            [VoterAttributes::read]
        );

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_allows_to_add_an_attachment_when_user_is_not_unit_manager_and_unit_is_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(true);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit, [VoterAttributes::addAttachment]
        );

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_allows_to_remove_an_attachment_when_user_is_not_unit_manager_and_unit_is_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(true);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit, [VoterAttributes::removeAttachment]
        );

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_grants_write_access_when_user_is_not_unit_manager_and_unit_is_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(true);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit,
            [VoterAttributes::write]
        );

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_denies_read_access_when_user_is_not_unit_manager_and_unit_is_not_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(false);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit,
            [VoterAttributes::read]
        );

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_denies_write_access_when_user_is_not_unit_manager_and_unit_is_not_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(false);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit,
            [VoterAttributes::write]
        );

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_denies_write_access_when_user_is_not_unit_manager_and_unit_is_new(): void
    {
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);
        $token = new UsernamePasswordToken($currentUser, 'TEST_PROVIDER');

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            $token,
            new Unit(),
            [VoterAttributes::write]
        );
        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision');

        $decision = $unitVoter->vote(
            $token,
            Unit::class,
            [VoterAttributes::write]
        );
        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_denies_to_add_an_attachment_when_user_is_not_unit_manager_and_unit_is_not_assigned_to_user(): void
    {
        $unit = $this->getUnit();
        $currentUser = $this->createMock(User::class);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->with($currentUser, [$unit])->willReturn(false);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $userUnitAssignmentChecker);

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            $unit,
            [VoterAttributes::addAttachment]
        );

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision');
    }

    public function test_voter_denies_delete_access_when_user_is_not_unit_manager(): void
    {
        $currentUser = $this->createMock(User::class);
        $authorizationManager = $this->createMock(Security::class);
        $authorizationManager->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $unitVoter = new UnitVoter($authorizationManager, $this->createMock(UserUnitAssignmentChecker::class));

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($currentUser, 'TEST_PROVIDER'),
            Unit::class,
            [VoterAttributes::delete]
        );

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision');
    }

    #[DataProvider('provideVoterAttributes')]
    public function test_voter_abstains_when_class_is_not_supported(string $attribute): void
    {
        $unitVoter = new UnitVoter($this->createMock(Security::class), $this->createMock(UserUnitAssignmentChecker::class));

        $decision = $unitVoter->vote(
            new UsernamePasswordToken($this->createMock(User::class), 'TEST_PROVIDER'),
            $this->createMock(\stdClass::class),
            [$attribute]
        );

        self::assertSame(VoterInterface::ACCESS_ABSTAIN, $decision, 'The voter returned the wrong decision for a unsupported class');
    }

    public function test_supported_attributes(): void
    {
        $unitVoter = new UnitVoter(
            $this->createMock(Security::class),
            $this->createMock(UserUnitAssignmentChecker::class)
        );

        self::assertTrue($unitVoter->supportsAttribute(VoterAttributes::read));
        self::assertTrue($unitVoter->supportsAttribute(VoterAttributes::write));
        self::assertTrue($unitVoter->supportsAttribute(VoterAttributes::addAttachment));
        self::assertTrue($unitVoter->supportsAttribute(VoterAttributes::removeAttachment));
        self::assertTrue($unitVoter->supportsAttribute(VoterAttributes::delete));
        self::assertFalse($unitVoter->supportsAttribute('UNSUPPORTED_ATTRIBUTE'));
    }

    protected function getUnit(): Unit
    {
        $unit = new Unit();
        TestUtils::setId($unit, 1);

        return $unit;
    }
}
