<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\ExchangeRate;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\Security\Voter\ExchangeRateImportVoter;
use U2\Security\Voter\VoterAttributes;

/**
 * @coversDefaultClass \U2\Security\Voter\ExchangeRateImportVoter
 */
class ExchangeRateImportVoterTest extends UnitTestCase
{
    public function test_supports_attribute(): void
    {
        $voter = new ExchangeRateImportVoter($this->createMock(Security::class));

        self::assertTrue($voter->supportsAttribute(VoterAttributes::import));
        self::assertFalse($voter->supportsAttribute('wrong attribute'));
    }

    public function test_supports_type(): void
    {
        $voter = new ExchangeRateImportVoter($this->createMock(Security::class));

        self::assertTrue($voter->supportsType('string'));
        self::assertFalse($voter->supportsType(VoterAttributes::class));
    }

    /**
     * @return array<string,array<string,int|bool>>
     */
    public static function provideVoterTestData(): array
    {
        return [
            'voter grants access when user is authorized' => [
                'authorizationManagerDecision' => true,
                'expectedVoterDecision' => VoterInterface::ACCESS_GRANTED,
            ],
            'voter denies access when user is not authorized' => [
                'authorizationManagerDecision' => false,
                'expectedVoterDecision' => VoterInterface::ACCESS_DENIED,
            ],
        ];
    }

    #[DataProvider('provideVoterTestData')]
    public function test_vote(bool $authorizationManagerDecision, int $expectedVoterDecision): void
    {
        $user = new User();
        $security = $this->createMock(Security::class);
        $token = new UsernamePasswordToken($user, 'TEST_PROVIDER');

        $security
            ->method('isGranted')
            ->with(UserRoles::PeriodManager->value)
            ->willReturn($authorizationManagerDecision);
        $voter = new ExchangeRateImportVoter($security);
        $voterDecision = $voter->vote($token, ExchangeRate::class, [VoterAttributes::import]);

        self::assertSame($expectedVoterDecision, $voterDecision);
    }

    /**
     * @covers ::supports
     */
    public function test_abstains_when_subject_string_is_not_an_exchange_rate_class_string(): void
    {
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $voter = new ExchangeRateImportVoter($this->createMock(Security::class));

        self::assertSame(VoterInterface::ACCESS_ABSTAIN, $voter->vote($token, VoterAttributes::class, [VoterAttributes::import]));
        self::assertSame(VoterInterface::ACCESS_ABSTAIN, $voter->vote($token, 'any string', [VoterAttributes::import]));
    }
}
