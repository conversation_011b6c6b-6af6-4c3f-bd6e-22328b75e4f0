<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AccessDecisionManagerInterface;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\UnitTestCase;
use U2\DataSourcery\DataSource\DataSource;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\User;
use U2\Security\Voter\TableVoter;
use U2\Security\Voter\VoterAttributes;
use U2\Table\Table;

class TableVoterTest extends UnitTestCase
{
    /**
     * @var AccessDecisionManagerInterface&MockObject
     */
    private MockObject $accessDecisionManager;

    private TableVoter $tableVoter;

    public function test_grants_read_access_if_the_user_is_allowed_to_read(): void
    {
        $token = $this->createMock(TokenInterface::class);
        $table = $this->createMock(Table::class);
        $dataSourceConfiguration = $this->createMock(DataSource::class);
        $user = $this->createMock(User::class);
        $table->expects($this->atLeastOnce())->method('getDataSource')->willReturn($dataSourceConfiguration);
        $entityClass = Transaction::class;
        $dataSourceConfiguration->expects($this->atLeastOnce())->method('getEntityClass')->willReturn($entityClass);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn($user);
        $this->accessDecisionManager->expects($this->atLeastOnce())->method('decide')->with(self::equalTo($token))->willReturn(true);
        self::assertSame(VoterInterface::ACCESS_GRANTED, $this->tableVoter->vote($token, $table, [VoterAttributes::read]));
    }

    public function test_grants_read_access_if_the_user_is_allowed_to_access(): void
    {
        $token = $this->createMock(TokenInterface::class);
        $table = $this->createMock(Table::class);
        $dataSourceConfiguration = $this->createMock(DataSource::class);
        $user = $this->createMock(User::class);
        $table->expects($this->atLeastOnce())->method('getDataSource')->willReturn($dataSourceConfiguration);
        $entityClass = Transaction::class;
        $dataSourceConfiguration->expects($this->atLeastOnce())->method('getEntityClass')->willReturn($entityClass);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn($user);
        $this->accessDecisionManager
            ->expects($this->atLeastOnce())
            ->method('decide')
            ->with($token)
            ->willReturn(true);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $this->tableVoter->vote($token, $table, [VoterAttributes::read]));
    }

    public function test_denies_read_access_if_the_user_is_not_allowed(): void
    {
        $token = $this->createMock(TokenInterface::class);
        $table = $this->createMock(Table::class);
        $dataSourceConfiguration = $this->createMock(DataSource::class);
        $user = $this->createMock(User::class);
        $table->expects($this->atLeastOnce())->method('getDataSource')->willReturn($dataSourceConfiguration);
        $entityClass = Transaction::class;
        $dataSourceConfiguration->expects($this->atLeastOnce())->method('getEntityClass')->willReturn($entityClass);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn($user);
        $this->accessDecisionManager->expects($this->atLeastOnce())->method('decide')->with(self::equalTo($token))->willReturn(false);
        $this->accessDecisionManager->expects($this->atLeastOnce())->method('decide')->with(self::equalTo($token))->willReturn(false);
        self::assertSame(VoterInterface::ACCESS_DENIED, $this->tableVoter->vote($token, $table, [VoterAttributes::read]));
    }

    public function test_supported_attributes(): void
    {
        self::assertTrue($this->tableVoter->supportsAttribute(VoterAttributes::read));
        self::assertFalse($this->tableVoter->supportsAttribute(VoterAttributes::write));
    }

    public function test_supported_types(): void
    {
        self::assertTrue($this->tableVoter->supportsType(Table::class));
        self::assertFalse($this->tableVoter->supportsType(\stdClass::class));
    }

    protected function setUp(): void
    {
        $this->accessDecisionManager = $this->createMock(AccessDecisionManagerInterface::class);
        $this->tableVoter = new TableVoter($this->accessDecisionManager);
    }
}
