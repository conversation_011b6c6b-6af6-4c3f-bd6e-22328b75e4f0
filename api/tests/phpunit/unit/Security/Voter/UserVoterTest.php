<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\Security\Voter\UserVoter;
use U2\Security\Voter\VoterAttributes;
use U2\User\CurrentUserProvider;

class UserVoterTest extends UnitTestCase
{
    public function test_voter_grants_access_to_user_admins(): void
    {
        // Given
        $user = new User();
        TestUtils::setId($user, 1);

        $token = new UsernamePasswordToken($user, 'TEST_PROVIDER_KEY');

        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $currentUserProvider
            ->method('get')
            ->willReturn($user);

        $security = $this->createMock(Security::class);
        $security
            ->expects($this->once())
            ->method('isGranted')
            ->with(UserRoles::UserGroupAdmin->value)
            ->willReturn(true);

        $userVoter = new UserVoter(
            $currentUserProvider,
            $security
        );
        // When
        $actualVoterDecision = $userVoter->vote($token, new User(), [VoterAttributes::write]);

        // Then
        self::assertSame(
            VoterInterface::ACCESS_GRANTED,
            $actualVoterDecision,
            'The voter returned the wrong decision for a user admin'
        );
    }

    public function test_voter_grants_access_if_subject_is_current_user(): void
    {
        // Given
        $user = new User();
        TestUtils::setId($user, 1);

        $token = new UsernamePasswordToken($user, 'TEST_PROVIDER_KEY');

        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $currentUserProvider
            ->method('get')
            ->willReturn($user);

        $security = $this->createMock(Security::class);
        $security
            ->expects($this->once())
            ->method('isGranted')
            ->with(UserRoles::UserGroupAdmin->value)
        ->willReturn(false);

        $userVoter = new UserVoter(
            $currentUserProvider,
            $security
        );

        // When
        $actualVoterDecision = $userVoter->vote($token, $user, [VoterAttributes::write]);

        // Then
        self::assertSame(
            VoterInterface::ACCESS_GRANTED,
            $actualVoterDecision,
            'The voter returned the wrong decision for a subject that is the current user'
        );
    }

    public function test_voter_denies_access_if_subject_is_not_current_user(): void
    {
        // Given
        $user = new User();
        TestUtils::setId($user, 1);

        $token = new UsernamePasswordToken($user, 'TEST_PROVIDER_KEY');

        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $currentUserProvider
            ->method('get')
            ->willReturn(new User());

        $security = $this->createMock(Security::class);
        $security
            ->expects($this->once())
            ->method('isGranted')
            ->with(UserRoles::UserGroupAdmin->value)
        ->willReturn(false);

        $userVoter = new UserVoter(
            $currentUserProvider,
            $security
        );

        // When
        $actualVoterDecision = $userVoter->vote($token, $user, [VoterAttributes::write]);

        // Then
        self::assertSame(
            VoterInterface::ACCESS_DENIED,
            $actualVoterDecision,
            'The voter returned the wrong decision for a subject that is not the current user'
        );
    }

    public function test_voter_abstains_when_class_is_not_supported(): void
    {
        $userVoter = new UserVoter(
            $this->createMock(CurrentUserProvider::class),
            $this->createMock(Security::class)
        );

        self::assertFalse(
            $userVoter->supportsType(get_debug_type(new \stdClass())),
            'The voter returned the wrong decision for a unsupported class'
        );
    }

    public function test_voter_abstains_when_attribute_is_not_supported(): void
    {
        $userVoter = new UserVoter(
            $this->createMock(CurrentUserProvider::class),
            $this->createMock(Security::class)
        );

        self::assertFalse(
            $userVoter->supportsAttribute('UNSUPPORTED_ATTRIBUTE'),
            'The voter returned the wrong decision for a unsupported attribute'
        );
    }
}
