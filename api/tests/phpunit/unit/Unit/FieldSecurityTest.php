<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit;

use Symfony\Bundle\SecurityBundle\Security;
use Tests\U2\UnitTestCase;
use U2\Security\UserRoles;
use U2\SystemSettings\SystemSettings;
use U2\Unit\FieldSecurity;

class FieldSecurityTest extends UnitTestCase
{
    public function test_returns_true_if_the_property_is_inside_the_white_list(): void
    {
        $security = $this->createMock(Security::class);
        $security->expects($this->once())->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(false);

        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityUnitEditFieldWhitelist')->willReturn(['property']);

        $fieldSecurity = new FieldSecurity($security, $systemSettings);
        self::assertTrue($fieldSecurity->canEdit('property'));
    }

    public function test_returns_true_if_the_user_is_a_unit_manager(): void
    {
        $security = $this->createMock(Security::class);
        $security->expects($this->once())->method('isGranted')->with(UserRoles::UnitManager->value)->willReturn(true);

        $fieldSecurity = new FieldSecurity($security, $this->createMock(SystemSettings::class));
        self::assertTrue($fieldSecurity->canEdit('property'));
    }

    public function test_returns_false_if_the_property_is_not_inside_the_white_list(): void
    {
        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityUnitEditFieldWhitelist')->willReturn(['property']);

        $fieldSecurity = new FieldSecurity($this->createMock(Security::class), $systemSettings);
        self::assertFalse($fieldSecurity->canEdit('another property'));
    }

    public function test_returns_false_if_the_white_list_is_empty(): void
    {
        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityUnitEditFieldWhitelist')->willReturn([]);

        $fieldSecurity = new FieldSecurity($this->createMock(Security::class), $systemSettings);
        self::assertFalse($fieldSecurity->canEdit('property'));
    }
}
