<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Subscription\SavedFilter;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\DataSourcery\Query\Pagination;
use U2\DataSourcery\Query\Query;
use U2\Entity\SavedFilter;
use U2\Entity\SavedFilterSubscription;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\User;
use U2\EntityMetadata\MetadataProvider;
use U2\Exception\EmptySubscriptionException;
use U2\Exception\Exception;
use U2\Exception\SubscriptionAuthorizationException;
use U2\Repository\UnitRepository;
use U2\Security\CurrentUserManipulator;
use U2\Security\Voter\VoterAttributes;
use U2\Subscription\SavedFilter\SavedFilterSubscriptionRenderer;
use U2\Subscription\SubscriptionInterface;
use U2\Table\SavedFilter\RoutingHelper;
use U2\Table\SavedFilter\SavedFilterDefinitionsCollection;
use U2\Table\State;
use U2\Table\Table;
use U2\Table\TableFactory;
use U2\Table\View\TwigTableView;
use U2\TaxCompliance\OtherDeadline\OtherDeadlineDataSourceConfiguration;
use U2\TaxCompliance\OtherDeadline\OtherDeadlineTableType;
use U2\User\CurrentUserProvider;
use U2\Workflow\WorkflowManager;

class SavedFilterSubscriptionRendererTest extends UnitTestCase
{
    public function test_render(): void
    {
        // Given
        $table = $this->createMock(Table::class);
        $table->method('getData')->willReturn(['not-empty']);

        $twigTableView = $this->createMock(TwigTableView::class);
        $table->method('getTwigView')->willReturn($twigTableView);

        $query = $this->createMock(Query::class);
        $query->method('getPagination')->willReturn(new Pagination());
        $query->method('withPagination')->willReturn($newQuery = new Query());

        $tableState = new State();
        $tableState->setQuery($query);

        $table->method('getState')->willReturn($tableState);

        $savedFilter = new SavedFilter();
        $savedFilter->setTableViewConfigurationFullyQualifiedClass(OtherDeadlineTableType::class);

        $tableFactory = $this->createMock(TableFactory::class);
        $tableFactory->method('createFromSavedFilter')->with($savedFilter)->willReturn($table);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with(VoterAttributes::read, $table)->willReturn(true);

        $routingHelper = $this->createMock(RoutingHelper::class);
        $routingHelper->method('generateUrlToSavedFilterTable')->with($savedFilter, true)->willReturn('link');

        $subscription = new SavedFilterSubscription();
        $subscription->setSavedFilter($savedFilter);

        $dataSourceConfiguration = new OtherDeadlineDataSourceConfiguration(
            $this->createMock(CurrentUserProvider::class),
            $this->createMock(WorkflowManager::class),
            $this->createMock(UnitRepository::class),
            $this->createMock(Security::class),
        );

        $metadataProvider = $this->createMock(MetadataProvider::class);
        $metadataProvider->method('getShortName')->with(OtherDeadline::class)->willReturn('short-name');

        $savedFilterDefinitionsCollection = $this->createMock(SavedFilterDefinitionsCollection::class);
        $savedFilterDefinitionsCollection
            ->method('tableViewConfigurationFullyQualifiedClassToReadableName')
            ->with(OtherDeadlineTableType::class)
            ->willReturn('readable-name');
        $savedFilterDefinitionsCollection
            ->method('tableViewConfigurationFullyQualifiedClassToDataSourceConfiguration')
            ->with(OtherDeadlineTableType::class)
            ->willReturn($dataSourceConfiguration);

        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->method('render')->willReturn('rendered content');

        $currentUserManipulator = $this->createMock(CurrentUserManipulator::class);
        $renderer = new SavedFilterSubscriptionRenderer(
            $authorizationChecker,
            $templatingEngine,
            $savedFilterDefinitionsCollection,
            $tableFactory,
            $routingHelper,
            $currentUserManipulator,
            $this->createMock(CurrentUserProvider::class),
        );

        $recipient = new User();

        // Then
        $templatingEngine
            ->expects($this->atLeastOnce())
            ->method('render')
            ->with(
                'email/saved_filter_subscription.email.html.twig',
                [
                    'subscription' => $subscription,
                    'table' => $twigTableView,
                    'recipient' => $recipient,
                    'edit_route' => 'u2_otherdeadline_edit',
                    'saved_filter_layout_readable' => 'readable-name',
                    'link' => 'link',
                ]
            );

        $query
            ->expects($this->atLeastOnce())
            ->method('withPagination');

        $currentUserManipulator
            ->expects($this->atLeastOnce())
            ->method('change')
            ->with($recipient);

        // When
        $renderer->renderSubscriptionContent($subscription, $recipient);

        // Then
        self::assertEquals($newQuery, $tableState->getQuery());
    }

    public function test_user_token_is_restored_after_rendering(): void
    {
        // Given
        $query = $this->createMock(Query::class);
        $query->method('getPagination')->willReturn(new Pagination());
        $query->method('withPagination')->willReturn(new Query());

        $tableState = new State();
        $tableState->setQuery($query);

        $twigTableView = $this->createMock(TwigTableView::class);

        $table = $this->createMock(Table::class);
        $table->method('getData')->willReturn(['not-empty']);
        $table->method('getTwigView')->willReturn($twigTableView);
        $table->method('getState')->willReturn($tableState);

        $savedFilter = new SavedFilter();
        $savedFilter->setTableViewConfigurationFullyQualifiedClass(OtherDeadlineTableType::class);

        $tableFactory = $this->createMock(TableFactory::class);
        $tableFactory->method('createFromSavedFilter')->with($savedFilter)->willReturn($table);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with(VoterAttributes::read, $table)->willReturn(true);

        $routingHelper = $this->createMock(RoutingHelper::class);
        $routingHelper->method('generateUrlToSavedFilterTable')->with($savedFilter, true)->willReturn('link');

        $subscription = new SavedFilterSubscription();
        $subscription->setSavedFilter($savedFilter);

        $dataSourceConfiguration = new OtherDeadlineDataSourceConfiguration(
            $this->createMock(CurrentUserProvider::class),
            $this->createMock(WorkflowManager::class),
            $this->createMock(UnitRepository::class),
            $this->createMock(Security::class),
        );

        $metadataProvider = $this->createMock(MetadataProvider::class);
        $metadataProvider->method('getShortName')->with(OtherDeadline::class)->willReturn('short-name');

        $savedFilterDefinitionsCollection = $this->createMock(SavedFilterDefinitionsCollection::class);
        $savedFilterDefinitionsCollection
            ->method('tableViewConfigurationFullyQualifiedClassToReadableName')
            ->with(OtherDeadlineTableType::class)
            ->willReturn('readable-name');
        $savedFilterDefinitionsCollection
            ->method('tableViewConfigurationFullyQualifiedClassToDataSourceConfiguration')
            ->with(OtherDeadlineTableType::class)
            ->willReturn($dataSourceConfiguration);

        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->method('render')->willReturn('rendered content');

        $currentUserManipulator = $this->createMock(CurrentUserManipulator::class);
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $currentUserProvider->method('hasUser')->willReturn(true);
        $currentUserProvider->method('get')->willReturn($originalUser = new User());

        $renderer = new SavedFilterSubscriptionRenderer(
            $authorizationChecker,
            $templatingEngine,
            $savedFilterDefinitionsCollection,
            $tableFactory,
            $routingHelper,
            $currentUserManipulator,
            $currentUserProvider,
        );

        $recipient = new User();

        // Then
        $currentUserManipulator
            ->expects($this->exactly(2))
            ->method('change')
            ->with(self::callback(
                fn (User $user): bool => $user === $originalUser || $user === $recipient
            ));

        // When
        $renderer->renderSubscriptionContent($subscription, $recipient);
    }

    public function test_user_token_is_empty_after_rendering_if_it_was_empty_before(): void
    {
        // Given
        $table = $this->createMock(Table::class);
        $table->method('getData')->willReturn(['not-empty']);

        $twigTableView = $this->createMock(TwigTableView::class);
        $table->method('getTwigView')->willReturn($twigTableView);

        $query = $this->createMock(Query::class);
        $query->method('getPagination')->willReturn(new Pagination());
        $query->method('withPagination')->willReturn(new Query());

        $tableState = new State();
        $tableState->setQuery($query);

        $table->method('getState')->willReturn($tableState);

        $savedFilter = new SavedFilter();
        $savedFilter->setTableViewConfigurationFullyQualifiedClass(OtherDeadlineTableType::class);

        $tableFactory = $this->createMock(TableFactory::class);
        $tableFactory->method('createFromSavedFilter')->with($savedFilter)->willReturn($table);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with(VoterAttributes::read, $table)->willReturn(true);

        $routingHelper = $this->createMock(RoutingHelper::class);
        $routingHelper->method('generateUrlToSavedFilterTable')->with($savedFilter, true)->willReturn('link');

        $subscription = new SavedFilterSubscription();
        $subscription->setSavedFilter($savedFilter);

        $dataSourceConfiguration = new OtherDeadlineDataSourceConfiguration(
            $this->createMock(CurrentUserProvider::class),
            $this->createMock(WorkflowManager::class),
            $this->createMock(UnitRepository::class),
            $this->createMock(Security::class),
        );

        $metadataProvider = $this->createMock(MetadataProvider::class);
        $metadataProvider->method('getShortName')->with(OtherDeadline::class)->willReturn('short-name');

        $savedFilterDefinitionsCollection = $this->createMock(SavedFilterDefinitionsCollection::class);
        $savedFilterDefinitionsCollection
            ->method('tableViewConfigurationFullyQualifiedClassToReadableName')
            ->with(OtherDeadlineTableType::class)
            ->willReturn('readable-name');
        $savedFilterDefinitionsCollection
            ->method('tableViewConfigurationFullyQualifiedClassToDataSourceConfiguration')
            ->with(OtherDeadlineTableType::class)
            ->willReturn($dataSourceConfiguration);

        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->method('render')->willReturn('rendered content');

        $currentUserManipulator = $this->createMock(CurrentUserManipulator::class);
        $renderer = new SavedFilterSubscriptionRenderer(
            $authorizationChecker,
            $templatingEngine,
            $savedFilterDefinitionsCollection,
            $tableFactory,
            $routingHelper,
            $currentUserManipulator,
            $this->createMock(CurrentUserProvider::class),
        );

        $recipient = new User();

        // Then
        $currentUserManipulator
            ->expects($this->once())
            ->method('change')
            ->with($recipient);
        $currentUserManipulator
            ->expects($this->once())
            ->method('remove');

        // When
        $renderer->renderSubscriptionContent($subscription, $recipient);
    }

    public function test_render_fails_if_recipient_is_not_authorized_to_view_the_subscription_data(): void
    {
        // Given
        $table = $this->createMock(Table::class);

        $savedFilter = new SavedFilter();
        $savedFilter->setTableViewConfigurationFullyQualifiedClass(OtherDeadlineTableType::class);

        $tableFactory = $this->createMock(TableFactory::class);
        $tableFactory->method('createFromSavedFilter')->with($savedFilter)->willReturn($table);

        $subscription = new SavedFilterSubscription();
        $subscription->setSavedFilter($savedFilter);

        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->method('render')->willReturn('rendered content');

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with(VoterAttributes::read, $table)->willReturn(false);

        $renderer = new SavedFilterSubscriptionRenderer(
            $authorizationChecker,
            $templatingEngine,
            $this->createMock(SavedFilterDefinitionsCollection::class),
            $tableFactory,
            $this->createMock(RoutingHelper::class),
            $this->createMock(CurrentUserManipulator::class),
            $this->createMock(CurrentUserProvider::class),
        );

        // Then
        $this->expectException(SubscriptionAuthorizationException::class);

        // When
        $renderer->renderSubscriptionContent($subscription, new User());
    }

    public function test_render_fails_if_table_has_no_data(): void
    {
        // Given
        $table = $this->createMock(Table::class);

        $query = $this->createMock(Query::class);
        $query->method('getPagination')->willReturn(new Pagination());
        $query->method('withPagination')->willReturn(new Query());

        $tableState = new State();
        $tableState->setQuery($query);

        $table->method('getState')->willReturn($tableState);

        $savedFilter = new SavedFilter();
        $savedFilter->setTableViewConfigurationFullyQualifiedClass(OtherDeadlineTableType::class);

        $tableFactory = $this->createMock(TableFactory::class);
        $tableFactory->method('createFromSavedFilter')->with($savedFilter)->willReturn($table);

        $subscription = new SavedFilterSubscription();
        $subscription->setSavedFilter($savedFilter);

        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->method('render')->willReturn('rendered content');

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with(VoterAttributes::read, $table)->willReturn(true);

        $table->method('getData')->willReturn([]);

        $renderer = new SavedFilterSubscriptionRenderer(
            $authorizationChecker,
            $templatingEngine,
            $this->createMock(SavedFilterDefinitionsCollection::class),
            $tableFactory,
            $this->createMock(RoutingHelper::class),
            $this->createMock(CurrentUserManipulator::class),
            $this->createMock(CurrentUserProvider::class),
        );

        // Then
        $this->expectException(EmptySubscriptionException::class);

        // When
        $renderer->renderSubscriptionContent($subscription, new User());
    }

    public function test_render_fails_if_subscription_is_not_a_saved_filter_subscription(): void
    {
        // Given
        $renderer = new SavedFilterSubscriptionRenderer(
            $this->createMock(AuthorizationCheckerInterface::class),
            $this->createMock(Environment::class),
            $this->createMock(SavedFilterDefinitionsCollection::class),
            $this->createMock(TableFactory::class),
            $this->createMock(RoutingHelper::class),
            $this->createMock(CurrentUserManipulator::class),
            $this->createMock(CurrentUserProvider::class),
        );

        // Then
        $this->expectException(Exception::class);

        // When
        $renderer->renderSubscriptionContent($this->createMock(SubscriptionInterface::class), new User());
    }
}
