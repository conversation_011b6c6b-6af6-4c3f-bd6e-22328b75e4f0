<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener\Import;

use Tests\U2\UnitTestCase;
use U2\Entity\PercentItemUnitValue;
use U2\Event\Import\PreBindDataImportEvent;
use U2\EventListener\Import\PercentItemUnitValueUpdateEventSubscriber;
use U2\Exception\ImportInvalidValueException;

class PercentItemUnitValueUpdateEventSubscriberTest extends UnitTestCase
{
    public function test_return_early_for_non_percent(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $subscriber = new PercentItemUnitValueUpdateEventSubscriber();

        $subscriber->transformValue($preBindDataImportEvent);
    }

    public function test_sets_value_for_percent(): void
    {
        $entity = $this->createMock(PercentItemUnitValue::class);
        $preBindDataImportEvent = new PreBindDataImportEvent($entity, ['value' => '2']);
        $subscriber = new PercentItemUnitValueUpdateEventSubscriber();

        $subscriber->transformValue($preBindDataImportEvent);

        self::assertEquals(0.02, $preBindDataImportEvent->getInterpretedData()['value']);
    }

    public function test_throws_exception_for_invalid_value(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $entity = $this->createMock(PercentItemUnitValue::class);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData')->willReturn(['value' => 'invalid']);
        $subscriber = new PercentItemUnitValueUpdateEventSubscriber();

        $this->expectException(ImportInvalidValueException::class);

        $subscriber->transformValue($preBindDataImportEvent);
    }
}
