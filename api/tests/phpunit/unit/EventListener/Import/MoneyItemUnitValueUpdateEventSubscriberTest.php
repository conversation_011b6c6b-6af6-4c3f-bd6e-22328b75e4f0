<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener\Import;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\CurrencyFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\MoneyItemUnitValueFactory;
use U2\Event\Import\PreBindDataImportEvent;
use U2\EventListener\Import\MoneyItemUnitValueUpdateEventSubscriber;
use U2\Exception\ImportInvalidValueException;

class MoneyItemUnitValueUpdateEventSubscriberTest extends UnitTestCase
{
    public function test_return_early_for_non_money(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $subscriber = new MoneyItemUnitValueUpdateEventSubscriber();

        $subscriber->updateMoneyItemUnitValue($preBindDataImportEvent);
    }

    public function test_manipulates_the_value(): void
    {
        $unit = LegalUnitFactory::getObject(['currency' => CurrencyFactory::new(['scale' => 1])]);
        $entity = MoneyItemUnitValueFactory::getObject(['unit' => $unit]);
        $preBindDataImportEvent = new PreBindDataImportEvent($entity, ['value' => '5,670.45555555']);
        $subscriber = new MoneyItemUnitValueUpdateEventSubscriber();

        $subscriber->updateMoneyItemUnitValue($preBindDataImportEvent);

        self::assertEquals('5670.5', $preBindDataImportEvent->getInterpretedData()['localCurrencyValue']);
    }

    public function test_manipulates_the_value_of_null(): void
    {
        $unit = LegalUnitFactory::getObject(['currency' => CurrencyFactory::new(['scale' => 1])]);
        $entity = MoneyItemUnitValueFactory::getObject(['unit' => $unit]);
        $preBindDataImportEvent = new PreBindDataImportEvent($entity, ['value' => null]);
        $subscriber = new MoneyItemUnitValueUpdateEventSubscriber();

        $subscriber->updateMoneyItemUnitValue($preBindDataImportEvent);

        self::assertEquals('', $preBindDataImportEvent->getInterpretedData()['localCurrencyValue']);
    }

    public function test_throws_exception_for_invalid_value(): void
    {
        $entity = MoneyItemUnitValueFactory::getObject();
        $preBindDataImportEvent = new PreBindDataImportEvent($entity, ['value' => 'invalid']);
        $subscriber = new MoneyItemUnitValueUpdateEventSubscriber();

        $this->expectException(ImportInvalidValueException::class);

        $subscriber->updateMoneyItemUnitValue($preBindDataImportEvent);
    }
}
