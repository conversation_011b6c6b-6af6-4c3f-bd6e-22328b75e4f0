<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener\Import;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\CurrencyFactory;
use U2\DataFixtures\Example\ImportFactory;
use U2\DataFixtures\Example\MoneyItemUnitValueFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\Datasheets\Item\UnitValue\FormulaRecalculator;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Period;
use U2\Entity\Unit;
use U2\Event\Import\PostInterpretImportEvent;
use U2\EventListener\Import\ItemUnitValueEventSubscriber;
use U2\Exception\ExchangeRateNotFoundException;
use U2\Exception\MissingPreviousPeriodException;
use U2\Import\Configuration\ImportConfig;
use U2\Import\ImportLog;
use U2\Money\ExchangeRate\ExchangeRateTypes;

class ItemUnitValueEventSubscriberTest extends UnitTestCase
{
    public function test_reports_errors_for_cross_period_item_unit_value_imports(): void
    {
        $itemUnitValueEventSubscriber = new ItemUnitValueEventSubscriber(
            $this->createMock(FormulaRecalculator::class),
        );

        $itemUnitValue2015 = MoneyItemUnitValueFactory::getObject();
        $itemUnitValue2016 = MoneyItemUnitValueFactory::getObject();

        // When
        $importLog = new ImportLog(ImportFactory::createOne(), $this->createMock(ImportConfig::class));
        $importLog->getEntityLookupCache()->add($itemUnitValue2015);
        $importLog->getEntityLookupCache()->add($itemUnitValue2016);
        $postInterpretImportEvent = new PostInterpretImportEvent($importLog);
        $itemUnitValueEventSubscriber->calculateUnitPeriods($postInterpretImportEvent);

        // Then
        self::assertCount(1, $postInterpretImportEvent->getLog()->getGeneralErrors());
    }

    public function test_only_handles_item_unit_values(): void
    {
        $formulaRecalculator = $this->createMock(FormulaRecalculator::class);
        $itemUnitValueEventSubscriber = new ItemUnitValueEventSubscriber(
            $formulaRecalculator,
        );

        // Then
        $formulaRecalculator
            ->expects($this->never())
            ->method('recalculateUnitInPeriod');

        // It should not run for non-item unit values
        $notAnItemValue = $this->createMock(Entity::class);

        // When
        $importLog = new ImportLog(ImportFactory::createOne(), $this->createMock(ImportConfig::class));
        $importLog->getEntityLookupCache()->add($notAnItemValue);
        $postInterpretImportEvent = new PostInterpretImportEvent($importLog);
        $itemUnitValueEventSubscriber->calculateUnitPeriods($postInterpretImportEvent);

        // Then
        self::assertCount(0, $postInterpretImportEvent->getLog()->getDataErrors());
    }

    public function test_calculates_related_unit_periods(): void
    {
        $formulaRecalculator = $this->createMock(FormulaRecalculator::class);

        $itemUnitValueEventSubscriber = new ItemUnitValueEventSubscriber($formulaRecalculator);

        $period = PeriodFactory::getObject();

        $unit1 = UnitFactory::getObject();
        $itemUnitValue1 = MoneyItemUnitValueFactory::getObject(['period' => $period, 'unit' => $unit1]);

        $unit2 = UnitFactory::getObject();
        $itemUnitValue2 = MoneyItemUnitValueFactory::getObject(['period' => $period, 'unit' => $unit2]);

        $itemUnitValue3 = MoneyItemUnitValueFactory::getObject(['period' => $period, 'unit' => $unit2]);

        // Then
        $recalculatedUnitPeriods = [];
        $formulaRecalculator
            ->expects($this->exactly(2))
            ->method('recalculateUnitInPeriod')
            ->willReturnCallback(
                static function (Unit $unit, Period $period) use (&$recalculatedUnitPeriods): bool {
                    $recalculatedUnitPeriods[] = [$unit, $period];

                    return true;
                }
            );

        // When
        $importLog = new ImportLog(ImportFactory::createOne(), $this->createMock(ImportConfig::class));
        $importLog->getEntityLookupCache()->add($itemUnitValue1);
        $importLog->getEntityLookupCache()->add($itemUnitValue2);
        $importLog->getEntityLookupCache()->add($itemUnitValue3);
        $itemUnitValueEventSubscriber->calculateUnitPeriods(new PostInterpretImportEvent($importLog));

        // Then
        self::assertEquals([
            [$unit1, $period],
            [$unit2, $period],
        ], $recalculatedUnitPeriods);
    }

    public function test_reports_errors_for_exchange_rate_not_found_exception(): void
    {
        $formulaRecalculator = $this->createMock(FormulaRecalculator::class);
        $period = PeriodFactory::createOne();
        $inputCurrency = CurrencyFactory::createOne();
        $outputCurrency = CurrencyFactory::createOne();
        $formulaRecalculator->method('recalculateUnitInPeriod')->willThrowException(new ExchangeRateNotFoundException($period, $inputCurrency, $outputCurrency, ExchangeRateTypes::CURRENT));

        $itemUnitValue2016 = MoneyItemUnitValueFactory::getObject(['period' => $period]);

        // When
        $importLog = new ImportLog(ImportFactory::createOne(), $this->createMock(ImportConfig::class));
        $importLog->getEntityLookupCache()->add($itemUnitValue2016);
        $postInterpretImportEvent = new PostInterpretImportEvent($importLog);
        $itemUnitValueEventSubscriber = new ItemUnitValueEventSubscriber($formulaRecalculator);

        $itemUnitValueEventSubscriber->calculateUnitPeriods($postInterpretImportEvent);

        // Then
        self::assertCount(1, $postInterpretImportEvent->getLog()->getGeneralErrors());
        self::assertEquals(\sprintf(
            'Exchange rate from %s to %s (%d) in period "%s" not found.',
            $inputCurrency->getIso4217code(),
            $outputCurrency->getIso4217code(),
            ExchangeRateTypes::CURRENT,
            $period->getName()
        ), $postInterpretImportEvent->getLog()->getGeneralErrors()[0]);
    }

    public function test_reports_errors_for_missing_previous_period_exception(): void
    {
        $formulaRecalculator = $this->createMock(FormulaRecalculator::class);
        $period = PeriodFactory::createOne();
        $formulaRecalculator->method('recalculateUnitInPeriod')->willThrowException(new MissingPreviousPeriodException($period));

        $itemUnitValue2016 = MoneyItemUnitValueFactory::getObject(['period' => $period]);

        // When
        $importLog = new ImportLog(ImportFactory::createOne(), $this->createMock(ImportConfig::class));
        $importLog->getEntityLookupCache()->add($itemUnitValue2016);
        $postInterpretImportEvent = new PostInterpretImportEvent($importLog);
        $itemUnitValueEventSubscriber = new ItemUnitValueEventSubscriber($formulaRecalculator);

        $itemUnitValueEventSubscriber->calculateUnitPeriods($postInterpretImportEvent);

        // Then
        self::assertCount(1, $postInterpretImportEvent->getLog()->getGeneralErrors());
        self::assertEquals('Previous period for period "' . $period->getName() . '" not found.', $postInterpretImportEvent->getLog()->getGeneralErrors()[0]);
    }
}
