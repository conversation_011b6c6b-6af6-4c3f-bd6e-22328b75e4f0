<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\EventListener\CurrencySubscriber;
use U2\Exception\MissingCurrencyException;
use U2\Util\FlashMessageHandler;

class CurrencySubscriberTest extends UnitTestCase
{
    public function test_missing_currency_exception(): void
    {
        $event = new ExceptionEvent(
            $this->createMock(KernelInterface::class),
            $this->createMock(Request::class),
            KernelInterface::MAIN_REQUEST,
            new MissingCurrencyException()
        );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->once())->method('warning');
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->once())->method('addError')->with('Translation of: u2_core.currency.missing');

        $subscriber = new CurrencySubscriber(
            $logger,
            $flashMessageHandler,
            $this->getTranslator(),
        );

        $subscriber->currencyException($event);
    }

    public function test_previous_missing_currency_exception(): void
    {
        $event = new ExceptionEvent(
            $this->createMock(KernelInterface::class),
            $this->createMock(Request::class),
            KernelInterface::MAIN_REQUEST,
            new \Exception(
                'Error message',
                500,
                new MissingCurrencyException()
            )
        );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->once())->method('warning');
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->once())->method('addError')->with('Translation of: u2_core.currency.missing');

        $subscriber = new CurrencySubscriber(
            $logger,
            $flashMessageHandler,
            $this->getTranslator(),
        );

        $subscriber->currencyException($event);
    }

    public function test_unsupported_exception(): void
    {
        $event = new ExceptionEvent(
            $this->createMock(KernelInterface::class),
            $this->createMock(Request::class),
            KernelInterface::MAIN_REQUEST,
            $this->createMock(\Throwable::class)
        );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->never())->method('warning');
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->never())->method('addError');

        $subscriber = new CurrencySubscriber(
            $logger,
            $flashMessageHandler,
            $this->getTranslator(),
        );

        $subscriber->currencyException($event);
    }

    protected function getTranslator(): TranslatorInterface
    {
        $translator = $this->createMock(TranslatorInterface::class);
        $translator->method('trans')->willReturnCallback(fn ($transId): string => 'Translation of: ' . $transId);

        return $translator;
    }
}
