<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller\Task;

use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Apm\Transaction\ApmTransactionTypes;
use U2\Controller\Helper;
use U2\Controller\Task\TaskTypeForm;
use U2\Controller\VueHelper;
use U2\Entity\AuthorizationItem as AuthorizationItemEnum;
use U2\Entity\Interfaces\TaskTypeWithUnitHierarchy;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Entity\Task\TaskType\Contract;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Task\TaskType\Igt1Transaction;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use U2\Entity\Task\TaskType\TaxAssessmentStatus;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Entity\Task\TaskType\TaxAuthorityAuditObjection;
use U2\Entity\Task\TaskType\TaxConsultingFee;
use U2\Entity\Task\TaskType\TaxCredit;
use U2\Entity\Task\TaskType\TaxFilingMonitor;
use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Entity\Task\TaskType\TaxRate;
use U2\Entity\Task\TaskType\TaxRelevantRestriction;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Task\TaskType\TransferPricing;
use U2\Entity\Workflow\Status;
use U2\Exception\WorkflowException;
use U2\Form\NonAutoAffirmativeDisabledFormValidator;
use U2\Igt\Igt1\Igt1Types;
use U2\Igt\Igt2\Igt2Types;
use U2\Igt\Igt3\Igt3Types;
use U2\Igt\Igt4\Igt4Types;
use U2\Igt\Igt5\Igt5Types;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\DocumentVoterAttributes;
use U2\Security\Voter\VoterAttributes;
use U2\Task\TaskTypeFactory;
use U2\Task\TaskTypeKnowledge;
use U2\Util\FlashMessageHandler;

class TaskTypeFormTest extends UnitTestCase
{
    /**
     * @return array<string,array<string,string|class-string|null>>
     */
    public static function provideTaskTypes(): array
    {
        return [
            FinancialData::class => [
                'pathInfo' => '/tpm/financial-data',
                'entityClass' => FinancialData::class,
                'type' => null,
            ],
            Transaction::class => [
                'pathInfo' => '/tpm/transaction',
                'entityClass' => Transaction::class,
                'type' => null,
            ],
            MainBusinessActivity::class => [
                'pathInfo' => '/tpm/main-business-activity',
                'entityClass' => MainBusinessActivity::class,
                'type' => null,
            ],
            Igt1Transaction::class => [
                'pathInfo' => '/igt/transaction/igt1',
                'entityClass' => Igt1Transaction::class,
                'type' => Igt1Types::bondCollateralized,
            ],
            Igt2Transaction::class => [
                'pathInfo' => '/igt/transaction/igt2',
                'entityClass' => Igt2Transaction::class,
                'type' => Igt2Types::derivativesForwards,
            ],
            Igt3Transaction::class => [
                'pathInfo' => '/igt/transaction/igt3',
                'entityClass' => Igt3Transaction::class,
                'type' => Igt3Types::contingentLiabilities,
            ],
            Igt4Transaction::class => [
                'pathInfo' => '/igt/transaction/igt4',
                'entityClass' => Igt4Transaction::class,
                'type' => Igt4Types::excessOfLossBackUp,
            ],
            Igt5Transaction::class => [
                'pathInfo' => '/igt/transaction/igt4',
                'entityClass' => Igt5Transaction::class,
                'type' => Igt5Types::costOrRevenue,
            ],
            ApmTransaction::class => [
                'pathInfo' => '/apm/transaction',
                'entityClass' => ApmTransaction::class,
                'type' => ApmTransactionTypes::assumptionOfLiabilities,
            ],
            OtherDeadline::class => [
                'pathInfo' => '/tcm/other-deadline',
                'entityClass' => OtherDeadline::class,
                'type' => null,
            ],
            TaxAuthorityAuditObjection::class => [
                'pathInfo' => '/tcm/tax-authority-audit-objection',
                'entityClass' => TaxAuthorityAuditObjection::class,
                'type' => null,
            ],
            TaxFilingMonitor::class => [
                'pathInfo' => '/tcm/tax-filing-monitor',
                'entityClass' => TaxFilingMonitor::class,
                'type' => null,
            ],
            TaxAssessmentMonitor::class => [
                'pathInfo' => '/tcm/tax-assessment-monitor',
                'entityClass' => TaxAssessmentMonitor::class,
                'type' => null,
            ],
            TaxAssessmentStatus::class => [
                'pathInfo' => '/tam/tax-assessment-status',
                'entityClass' => TaxAssessmentStatus::class,
                'type' => null,
            ],
            TaxAuditRisk::class => [
                'pathInfo' => '/tam/tax-audit-risk',
                'entityClass' => TaxAuditRisk::class,
                'type' => null,
            ],
            TaxLitigation::class => [
                'pathInfo' => '/tam/tax-litigation',
                'entityClass' => TaxLitigation::class,
                'type' => null,
            ],
            TransferPricing::class => [
                'pathInfo' => '/tam/transfer-pricing',
                'entityClass' => TransferPricing::class,
                'type' => null,
            ],
            TaxRelevantRestriction::class => [
                'pathInfo' => '/tam/tax-relevant-restriction',
                'entityClass' => TaxRelevantRestriction::class,
                'type' => null,
            ],
            LossCarryForward::class => [
                'pathInfo' => '/tam/loss-carry-forward',
                'entityClass' => LossCarryForward::class,
                'type' => null,
            ],
            TaxCredit::class => [
                'pathInfo' => '/tam/tax-credit',
                'entityClass' => TaxCredit::class,
                'type' => null,
            ],
            TaxRate::class => [
                'pathInfo' => '/tam/tax-rate',
                'entityClass' => TaxRate::class,
                'type' => null,
            ],
            IncomeTaxPlanning::class => [
                'pathInfo' => '/tam/income-tax-planning',
                'entityClass' => IncomeTaxPlanning::class,
                'type' => null,
            ],
            TaxConsultingFee::class => [
                'pathInfo' => '/tam/tax-consulting-fee',
                'entityClass' => TaxConsultingFee::class,
                'type' => null,
            ],
            Contract::class => [
                'pathInfo' => '/contract-management/contract',
                'entityClass' => Contract::class,
                'type' => null,
            ],
        ];
    }

    /**
     * @return array<string,array<string,string|class-string>>
     */
    public static function provideTaskTypesWithDocument(): array
    {
        return [
            'Country by Country Report' => [
                'pathInfo' => '/tpm/country-by-country-report',
                'entityClass' => CountryByCountryReport::class,
                'authorizationItem' => AuthorizationItemEnum::CountryByCountryReport->value,
            ],
            'Master File' => [
                'pathInfo' => '/tpm/master-file',
                'entityClass' => MasterFile::class,
                'authorizationItem' => AuthorizationItemEnum::MasterFile->value,
            ],
            'Local File' => [
                'pathInfo' => '/tpm/local-file',
                'entityClass' => LocalFile::class,
                'authorizationItem' => AuthorizationItemEnum::LocalFile->value,
            ],
        ];
    }

    #[DataProvider('provideTaskTypes')]
    public function test_newform_action_throws_access_denied_for_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, ?string $type): void
    {
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $container = $this->createMock(ContainerInterface::class);
        $container->method('has')->with('form.factory')->willReturn(true);
        $container->method('get')->with('form.factory')->willReturn($formFactory);
        $request = $this->createMock(Request::class);
        $request->method('getPathInfo')->willReturn($pathInfo);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $taskTypeFactory = $this->createMock(TaskTypeFactory::class);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.')
            ->willThrowException(new AccessDeniedException('You do not have permission to perform this action.'));
        $templatingEngine = $this->createMock(Environment::class);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $requestStack,
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $this->createMock(EntityManagerInterface::class),
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $taskTypeFactory,
        );
        $controller->setContainer($container);

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have permission to perform this action.');
        $this->expectExceptionCode(403);

        $controller->newForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass]);
    }

    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_newform_action_throws_access_denied_for_document_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, string $authorizationItem): void
    {
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $container = $this->createMock(ContainerInterface::class);
        $container->method('has')->with('form.factory')->willReturn(true);
        $container->method('get')->with('form.factory')->willReturn($formFactory);
        $request = $this->createMock(Request::class);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $controllerHelper = $this->createMock(Helper::class);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $controllerHelper->method('denyAccessUnlessGranted')
            ->with($authorizationItem->value . ':CREATE', null, 'You do not have permission to perform this action.')
            ->willThrowException(new AccessDeniedException('You do not have permission to perform this action.'));
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $templatingEngine = $this->createMock(Environment::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $requestStack,
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $this->createMock(EntityManagerInterface::class),
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($container);

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have permission to perform this action.');
        $this->expectExceptionCode(403);

        $controller->newForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass]);
    }

    #[DataProvider('provideTaskTypes')]
    public function test_submitnewform_action_throws_access_denied_for_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, ?string $type): void
    {
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.')
            ->willThrowException(new AccessDeniedException('You do not have permission to perform this action.'));
        $templatingEngine = $this->createMock(Environment::class);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $this->createMock(RequestStack::class),
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $this->createMock(EntityManagerInterface::class),
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($this->createMock(ContainerInterface::class));

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have permission to perform this action.');
        $this->expectExceptionCode(403);

        $controller->submitNewForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], $this->createMock(Request::class));
    }

    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_submitnewform_action_throws_access_denied_for_document_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, string $authorizationItem): void
    {
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with($authorizationItem . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.')
            ->willThrowException(new AccessDeniedException('You do not have permission to perform this action.'));
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $this->createMock(RequestStack::class),
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $this->createMock(EntityManagerInterface::class),
            new VueHelper(
                $this->createMock(Environment::class),
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($this->createMock(ContainerInterface::class));

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have permission to perform this action.');
        $this->expectExceptionCode(403);

        $controller->submitNewForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], $this->createMock(Request::class));
    }

    #[DataProvider('provideTaskTypes')]
    public function test_submitnewform_action_handles_workflow_exceptions(string $pathInfo, string $entityClass, ?string $type = null): void
    {
        $request = new Request(query: ['type' => $type]);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $status = new Status();
        $status->setName('Test Status');
        $entity = new $entityClass($status);
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('handleRequest')->with(self::equalTo($request));
        $form->expects($this->atLeastOnce())->method('isDisabled')->willReturn(false);
        $form->method('getData')->willReturn($entity);
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $formFactory->expects($this->atLeastOnce())->method('create')->willReturn($form);
        $container = $this->createMock(ContainerInterface::class);
        $container->method('has')->with('form.factory')->willReturn(true);
        $container->method('get')->with('form.factory')->willReturn($formFactory);
        \assert(class_exists($entityClass));
        $nonAutoAffirmativeDisabledFormValidator = $this->createMock(NonAutoAffirmativeDisabledFormValidator::class);
        $nonAutoAffirmativeDisabledFormValidator
            ->expects($this->atLeastOnce())
            ->method('isValid')
            ->with($form)
            ->willReturn(true);
        $taskTypeFactory = $this->createMock(TaskTypeFactory::class);
        $taskTypeFactory->method('createWithDefaults')->with($entityClass)->willReturn($entity);
        $translator = $this->createMock(TranslatorInterface::class);
        $translator->method('trans')->with('u2.workflow.configuration_error')->willReturn('my error message');
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->method('persist')->willThrowException(new WorkflowException('Workflow not found'));
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->once())->method('addError')->with('my error message');
        $templatingEngine = $this->createMock(Environment::class);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, null, 'You do not have permission to perform this action.');
        $controller = new TaskTypeForm($flashMessageHandler,
            $requestStack,
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $nonAutoAffirmativeDisabledFormValidator,
            $entityManager,
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $translator,
            $taskTypeFactory,
        );
        $controller->setContainer($container);

        $controller->submitNewForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], $request);
    }

    #[DataProvider('provideTaskTypes')]
    public function test_editform_action_throws_access_denied_for_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, ?string $type = null): void
    {
        $task = new $entityClass(new Status());
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with(VoterAttributes::read, $task, 'You do not have permission to view this entry.')
            ->willThrowException(new AccessDeniedException('You do not have permission to view this entry.'));
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($task);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $this->createMock(RequestStack::class),
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $entityManager,
            new VueHelper(
                $this->createMock(Environment::class),
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($this->createMock(ContainerInterface::class));

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have permission to view this entry.');
        $this->expectExceptionCode(403);

        $controller->editForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], 1);
    }

    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_editform_action_throws_access_denied_for_document_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, ?string $authorizationItem = null): void
    {
        $task = new $entityClass(new Status());
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with(DocumentVoterAttributes::viewConfiguration, $task, 'You do not have rights to configure this entity')
            ->willThrowException(new AccessDeniedException('You do not have rights to configure this entity'));
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($task);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $this->createMock(RequestStack::class),
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $entityManager,
            new VueHelper(
                $this->createMock(Environment::class),
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($this->createMock(ContainerInterface::class));

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have rights to configure this entity');
        $this->expectExceptionCode(403);

        $controller->editForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], 1);
    }

    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_editform_action_renders_configuration_form(string $pathInfo, string $entityClass, string $authorizationItem): void
    {
        $formView = $this->createMock(FormView::class);
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('createView')->willReturn($formView);
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $formFactory->expects($this->atLeastOnce())->method('create')->willReturn($form);
        $container = $this->createMock(ContainerInterface::class);
        $container->method('has')->with('form.factory')->willReturn(true);
        $container->method('get')->with('form.factory')->willReturn($formFactory);
        $request = $this->createMock(Request::class);
        $request->method('getPathInfo')->willReturn($pathInfo);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $status = new Status();
        $status->setName('Test Status');
        $document = new $entityClass($status);
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($document);
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper->expects($this->atLeastOnce())->method('denyAccessUnlessGranted')->with(self::equalTo(DocumentVoterAttributes::viewConfiguration));
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->method('isGranted')
            ->willReturnCallback(
                static fn (string $route): true => match ($route) {
                    DocumentVoterAttributes::editConfiguration, DocumentVoterAttributes::delete, DocumentVoterAttributes::viewConfiguration, DocumentVoterAttributes::viewContent => true,
                    default => throw new \InvalidArgumentException(\sprintf('Unknown value "%s".', $route)),
                }
            );
        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->expects($this->atLeastOnce())->method('render')->with(self::equalTo('task/type/document_configure_form.html.twig'))->willReturn('renderedContent');
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $requestStack,
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $entityManager,
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($container);

        self::assertEquals(
            '{"html":"renderedContent","disabled":false,"messages":[]}',
            $controller->editForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], 1)->getContent(),
        );
    }

    #[DataProvider('provideTaskTypes')]
    public function test_submiteditform_action_throws_access_denied_for_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, ?string $type = null): void
    {
        $task = new $entityClass(new Status());
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with(VoterAttributes::write, $task, 'You do not have permission to view this entry.')
            ->willThrowException(new AccessDeniedException('You do not have permission to view this entry.'));
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($task);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $this->createMock(RequestStack::class),
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $entityManager,
            new VueHelper(
                $this->createMock(Environment::class),
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($this->createMock(ContainerInterface::class));

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have permission to view this entry.');
        $this->expectExceptionCode(403);

        $controller->submitEditForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], 1, $this->createMock(Request::class));
    }

    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_submiteditform_action_throws_access_denied_for_document_tasks_if_user_is_unauthorized(string $pathInfo, string $entityClass, string $authorizationItem): void
    {
        $task = new $entityClass(new Status());
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->expects($this->once())
            ->method('denyAccessUnlessGranted')
            ->with(DocumentVoterAttributes::editConfiguration, $task, 'You do not have rights to configure this entity')
            ->willThrowException(new AccessDeniedException('You do not have rights to configure this entity'));
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($task);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $this->createMock(RequestStack::class),
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(NonAutoAffirmativeDisabledFormValidator::class),
            $entityManager,
            new VueHelper(
                $this->createMock(Environment::class),
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($this->createMock(ContainerInterface::class));

        $this->expectException(AccessDeniedException::class);
        $this->expectExceptionMessage('You do not have rights to configure this entity');
        $this->expectExceptionCode(403);

        $controller->submitEditForm(TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass], 1, $this->createMock(Request::class));
    }

    /**
     * @throws \JsonException
     */
    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_submiteditform_action_updates_document_and_renders_it_again(string $pathInfo, string $entityClass, string $authorizationItem): void
    {
        // Given
        $request = $this->createMock(Request::class);
        $request->method('getPathInfo')->willReturn($pathInfo);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('handleRequest')->with(self::equalTo($request));
        $form->expects($this->atLeastOnce())->method('isDisabled')->willReturn(false);
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $formFactory->expects($this->atLeastOnce())->method('create')->willReturn($form);
        $container = $this->createMock(ContainerInterface::class);
        $container->method('has')->with('form.factory')->willReturn(true);
        $container->method('get')->with('form.factory')->willReturn($formFactory);
        $nonAutoAffirmativeDisabledFormValidator = $this->createMock(NonAutoAffirmativeDisabledFormValidator::class);
        $nonAutoAffirmativeDisabledFormValidator
            ->expects($this->atLeastOnce())
            ->method('isValid')
            ->with($form)
            ->willReturn(true);
        \assert(class_exists($entityClass));
        $status = new Status();
        $status->setName('Test Status');
        $document = new $entityClass($status);
        \assert($document instanceof TaskTypeWithUnitHierarchy);
        $document->setUnitHierarchyDefinitionsChanged(true);
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper->expects($this->atLeastOnce())->method('denyAccessUnlessGranted')->with(DocumentVoterAttributes::editConfiguration);
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($document);
        $entityManager->expects($this->atLeastOnce())->method('flush');
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->atLeastOnce())->method('addSavedMessage');
        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->expects($this->atLeastOnce())->method('render')->with(self::equalTo('task/type/document_configure_form.html.twig'))->willReturn('renderedContent');
        $controller = new TaskTypeForm($flashMessageHandler,
            $requestStack,
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $nonAutoAffirmativeDisabledFormValidator,
            $entityManager,
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($container);

        // Then
        self::assertEquals(
            json_encode([
                'html' => 'renderedContent',
                'disabled' => false,
                'messages' => [],
            ], \JSON_THROW_ON_ERROR),
            $controller
                ->submitEditForm(
                    TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass],
                    1,
                    $request,
                )
                ->getContent()
        );
        self::assertFalse($document->getUnitHierarchyDefinitionsChanged());
    }

    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_submiteditform_action_validates_configuration_form_and_renders_it_again_if_not_valid(string $pathInfo, string $entityClass, string $authorizationItem): void
    {
        $request = $this->createMock(Request::class);
        $request->method('getPathInfo')->willReturn($pathInfo);
        $requestStack = $this->createMock(RequestStack::class);
        $requestStack->method('getCurrentRequest')->willReturn($request);
        $status = new Status();
        $status->setName('Test Status');
        $document = new $entityClass($status);
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('find')->with($entityClass, 1)->willReturn($document);
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper->expects($this->atLeastOnce())->method('denyAccessUnlessGranted')->with(self::equalTo(DocumentVoterAttributes::editConfiguration));
        $formView = $this->createMock(FormView::class);
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('handleRequest')->with(self::equalTo($request));
        $form->expects($this->atLeastOnce())->method('createView')->willReturn($formView);
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $formFactory->expects($this->atLeastOnce())->method('create')->willReturn($form);
        $container = $this->createMock(ContainerInterface::class);
        $container->method('has')->with('form.factory')->willReturn(true);
        $container->method('get')->willReturn($formFactory);
        $nonAutoAffirmativeDisabledFormValidator = $this->createMock(NonAutoAffirmativeDisabledFormValidator::class);
        $nonAutoAffirmativeDisabledFormValidator
            ->expects($this->atLeastOnce())
            ->method('isValid')
            ->with($form)
            ->willReturn(false);
        $this->createMock(AuthorizationCheckerInterface::class)
            ->method('isGranted')
            ->willReturnCallback(
                static fn (string $route): true => match ($route) {
                    DocumentVoterAttributes::editConfiguration, DocumentVoterAttributes::delete, DocumentVoterAttributes::viewConfiguration, DocumentVoterAttributes::viewContent => true,
                    default => throw new \InvalidArgumentException(\sprintf('Unknown value "%s".', $route)),
                }
            );
        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->expects($this->atLeastOnce())->method('render')->with(self::equalTo('task/type/document_configure_form.html.twig'))->willReturn('renderedContent');
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $controller = new TaskTypeForm($flashMessageHandler,
            $requestStack,
            $controllerHelper,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
            $nonAutoAffirmativeDisabledFormValidator,
            $entityManager,
            new VueHelper(
                $templatingEngine,
                $flashMessageHandler,
                $this->createMock(SerializerInterface::class)
            ),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(TaskTypeFactory::class),
        );
        $controller->setContainer($container);

        self::assertEquals(
            '{"html":"renderedContent","disabled":false,"messages":[]}',
            $controller->submitEditForm(
                TaskTypeKnowledge::taskTypeClassToShortNameMap[$entityClass],
                1,
                $request,
            )->getContent()
        );
    }
}
