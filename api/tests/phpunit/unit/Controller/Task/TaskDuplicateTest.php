<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller\Task;

use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\Controller\Helper;
use U2\Controller\Task\TaskDuplicate;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Entity\Task\TaskType\Contract;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Task\TaskType\Igt1Transaction;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use U2\Entity\Task\TaskType\TaxAssessmentStatus;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Entity\Task\TaskType\TaxAuthorityAuditObjection;
use U2\Entity\Task\TaskType\TaxConsultingFee;
use U2\Entity\Task\TaskType\TaxCredit;
use U2\Entity\Task\TaskType\TaxFilingMonitor;
use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Entity\Task\TaskType\TaxRate;
use U2\Entity\Task\TaskType\TaxRelevantRestriction;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Task\TaskType\TransferPricing;
use U2\Entity\Workflow\Status;
use U2\Security\Authorization\AuthorizationRight;
use U2\Task\TaskTypeCopier;
use U2\Task\TaskTypeKnowledge;
use U2\Task\TaskTypeResolver;
use U2\Util\FlashMessageHandler;

class TaskDuplicateTest extends UnitTestCase
{
    /**
     * @return array<int, array<string, class-string<TaskType>>>
     */
    public static function provideTaskTypesWithDocument(): array
    {
        return [
            ['entityClass' => CountryByCountryReport::class],
            ['entityClass' => MasterFile::class],
            ['entityClass' => LocalFile::class],
        ];
    }

    /**
     * @param class-string<TaskType> $entityClass
     */
    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_duplicates_a_document_and_redirects_to_the_configuration_page(string $entityClass): void
    {
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->once())->method('addSuccess')->with('duplicate success message');

        $translator = $this->createMock(TranslatorInterface::class);
        $translator->expects($this->once())->method('trans')->with('u2.task.duplicate.success')->willReturn('duplicate success message');

        $router = $this->createMock(RouterInterface::class);
        $router->expects($this->once())->method('generate')->with(TaskTypeKnowledge::getAvailableRoutes($entityClass)['edit'])->willReturn('edit route');

        $status = new Status();
        $status->setName('Test Status');

        $document = new $entityClass($status);

        $taskTypeResolver = $this->createMock(TaskTypeResolver::class);
        $taskTypeResolver->expects($this->once())->method('resolve')->with($document->getTask())->willReturn($document);

        $copiedDocument = new $entityClass($status);

        $taskTypeCopier = $this->createMock(TaskTypeCopier::class);
        $taskTypeCopier->expects($this->once())->method('copy')->with($document)->willReturn($copiedDocument);

        $validator = $this->createMock(ValidatorInterface::class);
        $validator->method('validate')->willReturn(new ConstraintViolationList());

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('persist')->with($copiedDocument);
        $entityManager->expects($this->once())->method('flush');

        $controller = new TaskDuplicate(
            $flashMessageHandler,
            $this->createMock(Helper::class),
            $router,
            $entityManager,
            $taskTypeCopier,
            $translator,
            $taskTypeResolver,
            $validator,
        );
        $response = $controller->__invoke($document->getTask());

        self::assertEquals(json_encode(['redirect' => 'edit route', 'messages' => []]), $response->getContent());
    }

    /**
     * @param class-string $entityClass
     */
    #[DataProvider('provideTaskTypesWithDocument')]
    public function test_denies_user_with_insufficient_permission_to_a_record_to_duplicate_it(string $entityClass): void
    {
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->method('denyAccessUnlessGranted')
            ->with($authorizationItem->value . ':CREATE', null, self::anything())
            ->willThrowException(new AccessDeniedException('path'));
        $this->expectException(AccessDeniedException::class);

        $status = new Status();
        $status->setName('Test Status');

        /** @var TaskType $document */
        $document = new $entityClass($status);

        $taskTypeResolver = $this->createMock(TaskTypeResolver::class);
        $taskTypeResolver->expects($this->once())->method('resolve')->with($document->getTask())->willReturn($document);

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->never())->method('persist');
        $entityManager->expects($this->never())->method('flush');

        $controller = new TaskDuplicate(
            self::createStub(FlashMessageHandler::class),
            $controllerHelper,
            $this->createMock(RouterInterface::class),
            $entityManager,
            $this->createMock(TaskTypeCopier::class),
            $this->createMock(TranslatorInterface::class),
            $taskTypeResolver,
            $this->createMock(ValidatorInterface::class),
        );
        $controller->__invoke($document->getTask());
    }

    /**
     * @return array<int, array<string, class-string>>
     */
    public static function provideTaskTypes(): array
    {
        return [
            ['taskTypeClass' => FinancialData::class],
            ['taskTypeClass' => Transaction::class],
            ['taskTypeClass' => MainBusinessActivity::class],
            ['taskTypeClass' => Igt1Transaction::class],
            ['taskTypeClass' => Igt2Transaction::class],
            ['taskTypeClass' => Igt3Transaction::class],
            ['taskTypeClass' => Igt4Transaction::class],
            ['taskTypeClass' => Igt5Transaction::class],
            ['taskTypeClass' => ApmTransaction::class],
            ['taskTypeClass' => OtherDeadline::class],
            ['taskTypeClass' => TaxAuthorityAuditObjection::class],
            ['taskTypeClass' => TaxFilingMonitor::class],
            ['taskTypeClass' => TaxAssessmentMonitor::class],
            ['taskTypeClass' => TaxAssessmentStatus::class],
            ['taskTypeClass' => TaxAuditRisk::class],
            ['taskTypeClass' => TaxLitigation::class],
            ['taskTypeClass' => TransferPricing::class],
            ['taskTypeClass' => TaxRelevantRestriction::class],
            ['taskTypeClass' => LossCarryForward::class],
            ['taskTypeClass' => TaxCredit::class],
            ['taskTypeClass' => TaxRate::class],
            ['taskTypeClass' => IncomeTaxPlanning::class],
            ['taskTypeClass' => TaxConsultingFee::class],
            ['taskTypeClass' => Contract::class],
        ];
    }

    /**
     * @param class-string<TaskType> $taskTypeClass
     */
    #[DataProvider('provideTaskTypes')]
    public function test_duplicates_a_task_type_and_redirects_to_the_edit_page(string $taskTypeClass): void
    {
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->once())->method('addSuccess')->with('duplicate success message');

        $translator = $this->createMock(TranslatorInterface::class);
        $translator->expects($this->once())->method('trans')->with('u2.task.duplicate.success')->willReturn('duplicate success message');

        $router = $this->createMock(RouterInterface::class);
        $router->expects($this->once())->method('generate')->with(TaskTypeKnowledge::getAvailableRoutes($taskTypeClass)['edit'])->willReturn('edit route');

        $status = new Status();
        $status->setName('Test Status');

        $taskType = new $taskTypeClass($status);

        $taskTypeResolver = $this->createMock(TaskTypeResolver::class);
        $taskTypeResolver->expects($this->once())->method('resolve')->with($taskType->getTask())->willReturn($taskType);

        $status = new Status();
        $status->setName('Test Status');

        $copiedTaskType = new $taskTypeClass($status);

        $taskTypeCopier = $this->createMock(TaskTypeCopier::class);
        $taskTypeCopier->expects($this->once())->method('copy')->with($taskType)->willReturn($copiedTaskType);

        $validator = $this->createMock(ValidatorInterface::class);
        $validator->method('validate')->willReturn(new ConstraintViolationList());

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('persist')->with($copiedTaskType);
        $entityManager->expects($this->once())->method('flush');

        $controller = new TaskDuplicate(
            $flashMessageHandler,
            $this->createMock(Helper::class),
            $router,
            $entityManager,
            $taskTypeCopier,
            $translator,
            $taskTypeResolver,
            $validator,
        );
        $response = $controller->__invoke($taskType->getTask());

        self::assertEquals(json_encode(['redirect' => 'edit route', 'messages' => []]), $response->getContent());
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypes')]
    public function test_denies_user_with_insufficient_permission_to_a_task_type_to_duplicate_it(string $taskTypeClass): void
    {
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$taskTypeClass];

        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper
            ->method('denyAccessUnlessGranted')
            ->with($authorizationItem->value . ':' . AuthorizationRight::CREATE->value, self::anything(), self::anything())
            ->willThrowException(new AccessDeniedException('path'));

        $status = new Status();
        $status->setName('Test Status');

        $taskType = new $taskTypeClass($status);
        \assert($taskType instanceof TaskType);

        $taskTypeResolver = $this->createMock(TaskTypeResolver::class);
        $taskTypeResolver->expects($this->once())->method('resolve')->with($taskType->getTask())->willReturn($taskType);

        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $flashMessageHandler->expects($this->never())->method('addSuccess');

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->never())->method('persist');
        $entityManager->expects($this->never())->method('flush');

        $this->expectException(AccessDeniedException::class);

        $controller = new TaskDuplicate(
            $flashMessageHandler,
            $controllerHelper,
            $this->createMock(RouterInterface::class),
            $entityManager,
            $this->createMock(TaskTypeCopier::class),
            $this->createMock(TranslatorInterface::class),
            $taskTypeResolver,
            $this->createMock(ValidatorInterface::class),
        );
        $controller->__invoke($taskType->getTask());
    }

    protected function setUp(): void
    {
    }
}
