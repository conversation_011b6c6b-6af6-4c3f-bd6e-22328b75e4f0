<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller;

use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Uid\Ulid;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Controller\DatasheetUnitHierarchyViewXls;
use U2\Controller\Helper;
use U2\Datasheets\TemplateProvider;
use U2\Datasheets\View\UnitHierarchy\UnitHierarchyView;
use U2\Datasheets\View\UnitHierarchy\UnitHierarchyViewFactory;
use U2\Entity\Currency;
use U2\Entity\Datasheet;
use U2\Entity\DatasheetCollection;
use U2\Entity\Period;
use U2\Entity\UnitHierarchy;
use U2\Form\Type\UnitHierarchyViewFormType;
use U2\Repository\DatasheetCollectionRepository;
use U2\Repository\DatasheetRepository;
use U2\Repository\PeriodRepository;
use U2\Repository\UnitHierarchyRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Util\ApplicationCurrencyProvider;

class DatasheetUnitHierarchyViewXlsTest extends UnitTestCase
{
    public function test_does_not_export_the_current_view_for_invalid_parameters(): void
    {
        $layoutCollectionId = new Ulid();
        $layoutCollection = $this->createMock(DatasheetCollection::class);
        $layoutCollection->method('getId')->willReturn($layoutCollectionId);
        $datasheetCollectionRepository = $this->createMock(DatasheetCollectionRepository::class);
        $datasheetCollectionRepository->method('find')->with($layoutCollectionId->toBase32())->willReturn($layoutCollection);
        $layoutId = 1;
        $layout = $this->createMock(Datasheet::class);
        $layout->method('getId')->willReturn($layoutId);
        $datasheetRepository = $this->createMock(DatasheetRepository::class);
        $datasheetRepository->method('find')->with($layoutId)->willReturn($layout);
        $periodId = 2;
        $periodRepository = $this->createMock(PeriodRepository::class);
        $periodRepository->method('find')->with($periodId)->willReturn(null);
        $unitHierarchyId = 3;
        $unitHierarchy = $this->createMock(UnitHierarchy::class);
        $unitHierarchy->method('getId')->willReturn($unitHierarchyId);
        $unitHierarchyRepository = $this->createMock(UnitHierarchyRepository::class);
        $unitHierarchyRepository->method('find')->with($unitHierarchyId)->willReturn($unitHierarchy);

        $groupViewXlsAction = new DatasheetUnitHierarchyViewXls(
            $this->createMock(UnitHierarchyViewFactory::class),
            $this->createMock(FormFactoryInterface::class),
            $this->createMock(Environment::class),
            $this->createMock(TemplateProvider::class),
            $this->createMock(Helper::class),
            $datasheetCollectionRepository,
            $datasheetRepository,
            $periodRepository,
            $unitHierarchyRepository,
            $this->createMock(ApplicationCurrencyProvider::class),
        );

        $this->expectException(UnprocessableEntityHttpException::class);

        $groupViewXlsAction->__invoke($layoutCollectionId->toBase32(), $layoutId, $periodId, $unitHierarchyId);
    }

    public function test_denies_access_if_user_does_not_have_read_access_to_the_view(): void
    {
        $layoutCollectionId = new Ulid();
        $layoutCollection = $this->createMock(DatasheetCollection::class);
        $layoutCollection->method('getId')->willReturn($layoutCollectionId);
        $datasheetCollectionRepository = $this->createMock(DatasheetCollectionRepository::class);
        $datasheetCollectionRepository->method('find')->with($layoutCollectionId->toBase32())->willReturn($layoutCollection);
        $layoutId = 1;
        $layout = $this->createMock(Datasheet::class);
        $layout->method('getId')->willReturn($layoutId);
        $datasheetRepository = $this->createMock(DatasheetRepository::class);
        $datasheetRepository->method('find')->with($layoutId)->willReturn($layout);
        $periodId = 2;
        $period = $this->createMock(Period::class);
        $period->method('getId')->willReturn($periodId);
        $periodRepository = $this->createMock(PeriodRepository::class);
        $periodRepository->expects($this->once())->method('find')->with($periodId)->willReturn($period);
        $unitHierarchyId = 3;
        $unitHierarchy = $this->createMock(UnitHierarchy::class);
        $unitHierarchy->method('getId')->willReturn($unitHierarchyId);
        $unitHierarchyRepository = $this->createMock(UnitHierarchyRepository::class);
        $unitHierarchyRepository->method('find')->with($unitHierarchyId)->willReturn($unitHierarchy);
        $groupView = $this->createMock(UnitHierarchyView::class);
        $groupViewFactory = $this->createMock(UnitHierarchyViewFactory::class);
        $groupViewFactory->expects($this->atLeastOnce())->method('create')->with(self::equalTo($layoutCollection), self::equalTo($layout), self::equalTo($period), self::equalTo($unitHierarchy))->willReturn($groupView);
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper->method('denyAccessUnlessGranted')->with(VoterAttributes::read, $groupView)->willThrowException(new AccessDeniedException());

        $this->expectException(AccessDeniedException::class);

        $groupViewXlsAction = new DatasheetUnitHierarchyViewXls(
            $groupViewFactory,
            $this->createMock(FormFactoryInterface::class),
            $this->createMock(Environment::class),
            $this->createMock(TemplateProvider::class),
            $controllerHelper,
            $datasheetCollectionRepository,
            $datasheetRepository,
            $periodRepository,
            $unitHierarchyRepository,
            $this->createMock(ApplicationCurrencyProvider::class),
        );

        $groupViewXlsAction->__invoke($layoutCollectionId->toBase32(), $layoutId, $periodId, $unitHierarchyId);
    }

    public function test_exports_the_current_view(): void
    {
        $layoutCollectionId = new Ulid();
        $layoutCollection = $this->createMock(DatasheetCollection::class);
        $layoutCollection->method('getId')->willReturn($layoutCollectionId);
        $datasheetCollectionRepository = $this->createMock(DatasheetCollectionRepository::class);
        $datasheetCollectionRepository->method('find')->with($layoutCollectionId->toBase32())->willReturn($layoutCollection);
        $layoutId = 1;
        $layout = $this->createMock(Datasheet::class);
        $layout->method('getId')->willReturn($layoutId);
        $layout->method('getName')->willReturn('layout name');
        $datasheetRepository = $this->createMock(DatasheetRepository::class);
        $datasheetRepository->method('find')->with($layoutId)->willReturn($layout);
        $periodId = 2;
        $period = $this->createMock(Period::class);
        $period->method('getId')->willReturn($periodId);
        $periodRepository = $this->createMock(PeriodRepository::class);
        $periodRepository->expects($this->once())->method('find')->with($periodId)->willReturn($period);
        $unitHierarchyId = 3;
        $unitHierarchy = $this->createMock(UnitHierarchy::class);
        $unitHierarchy->method('getId')->willReturn($unitHierarchyId);
        $unitHierarchyRepository = $this->createMock(UnitHierarchyRepository::class);
        $unitHierarchyRepository->method('find')->with($unitHierarchyId)->willReturn($unitHierarchy);

        $groupView = $this->createMock(UnitHierarchyView::class);
        $groupViewFactory = $this->createMock(UnitHierarchyViewFactory::class);
        $groupViewFactory->expects($this->atLeastOnce())->method('create')->with(self::equalTo($layoutCollection), self::equalTo($layout), self::equalTo($period), self::equalTo($unitHierarchy))->willReturn($groupView);
        $formView = $this->createMock(FormView::class);
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('createView')->willReturn($formView);
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $formFactory->expects($this->atLeastOnce())->method('create')->with(self::equalTo(UnitHierarchyViewFormType::class), self::equalTo($groupView))->willReturn($form);
        $layoutTemplateProvider = $this->createMock(TemplateProvider::class);
        $layoutTemplateProvider->expects($this->atLeastOnce())
            ->method('getContent')
            ->with(self::equalTo($layout))
            ->willReturn('layout template content');
        $templatingEngine = $this->createMock(Environment::class);
        $templatingEngine->expects($this->atLeastOnce())
            ->method('render')
            ->with(
                self::equalTo('datasheet/unit_hierarchy_view_excel_export.html.twig'),
                self::isArray()
            )
            ->willReturn('content');
        $controllerHelper = $this->createMock(Helper::class);
        $controllerHelper->expects($this->atLeastOnce())->method('denyAccessUnlessGranted')->with(VoterAttributes::read, $groupView);
        $currency = $this->createMock(Currency::class);
        $currencyProvider = $this->createMock(ApplicationCurrencyProvider::class);
        $currencyProvider->method('get')->willReturn($currency);

        $groupViewXlsAction = new DatasheetUnitHierarchyViewXls(
            $groupViewFactory,
            $formFactory,
            $templatingEngine,
            $layoutTemplateProvider,
            $controllerHelper,
            $datasheetCollectionRepository,
            $datasheetRepository,
            $periodRepository,
            $unitHierarchyRepository,
            $currencyProvider,
        );

        $response = $groupViewXlsAction->__invoke($layoutCollectionId->toBase32(), $layoutId, $periodId, $unitHierarchyId);

        self::assertSame(Response::HTTP_OK, $response->getStatusCode());
        self::assertSame('application/vnd.ms-excel', $response->headers->get('Content-Type'));
        self::assertFalse($response->getContent(), 'Response should stream content not return it');

        // Capture the streamed content
        ob_start();
        $response->sendContent();
        $content = ob_get_clean();
        self::assertNotEmpty($content, 'Response should stream content');
    }
}
