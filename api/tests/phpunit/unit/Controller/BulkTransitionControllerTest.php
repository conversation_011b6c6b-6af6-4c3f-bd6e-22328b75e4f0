<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Controller\BulkTransitionController;
use U2\Controller\Helper;
use U2\Controller\VueHelper;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Security\Voter\VoterAttributes;
use U2\Task\BulkAction\Transition\BulkTransition;
use U2\Task\BulkAction\Transition\SessionFactory;
use U2\Task\TaskTypeKnowledge;
use U2\Util\FlashMessageHandler;

class BulkTransitionControllerTest extends UnitTestCase
{
    public function test_prepare_action_throws_exception_if_user_is_unauthorized(): void
    {
        // Given
        $helper = $this->createMock(Helper::class);
        $helper->method('denyAccessUnlessGranted')
            ->with(VoterAttributes::read, self::anything(), 'You do not have read rights to perform this transition')
            ->willThrowException(new AccessDeniedException());
        $entity = $this->createMock(TaskType::class);
        $repository = $this->createMock(EntityRepository::class);
        $repository->method('findBy')->with(['id' => [1]])->willReturn([$entity]);
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->method('getRepository')->with(OtherDeadline::class)->willReturn($repository);

        $controller = new BulkTransitionController(
            $this->createMock(FlashMessageHandler::class),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(BulkTransition::class),
            $this->createMock(SessionFactory::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(Environment::class),
            $this->createMock(FormFactoryInterface::class),
            $entityManager,
            $helper,
            $this->createMock(VueHelper::class),
        );

        // Then
        $this->expectException(AccessDeniedException::class);

        // When
        $controller->prepare(new Request(['selection' => '1']), TaskTypeKnowledge::taskTypeClassToShortNameMap[OtherDeadline::class]);
    }

    public function test_transition_action_throws_exception_if_user_is_unauthorized(): void
    {
        // Given
        $helper = $this->createMock(Helper::class);
        $helper->method('denyAccessUnlessGranted')
            ->with(VoterAttributes::read, self::anything(), 'You do not have read rights to perform this transition')
            ->willThrowException(new AccessDeniedException());
        $entity = $this->createMock(OtherDeadline::class);
        $repository = $this->createMock(EntityRepository::class);
        $repository->method('findBy')->with(['id' => [1]])->willReturn([$entity]);
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->method('getRepository')->with(OtherDeadline::class)->willReturn($repository);

        $controller = new BulkTransitionController(
            $this->createMock(FlashMessageHandler::class),
            $this->createMock(TranslatorInterface::class),
            $this->createMock(BulkTransition::class),
            $this->createMock(SessionFactory::class),
            $this->createMock(RouterInterface::class),
            $this->createMock(Environment::class),
            $this->createMock(FormFactoryInterface::class),
            $entityManager,
            $helper,
            $this->createMock(VueHelper::class),
        );

        // Then
        $this->expectException(AccessDeniedException::class);

        // When
        $controller->transition(
            new Request(['selection' => '1']),
            TaskTypeKnowledge::taskTypeClassToShortNameMap[OtherDeadline::class]
        );
    }
}
