<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Tests\U2\KernelTestCase;
use U2\Controller\DatasheetUnitViewDataSubmit;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\DatasheetCollectionFactory;
use U2\DataFixtures\Example\DatasheetFactory;
use U2\DataFixtures\Example\FieldFactory;
use U2\DataFixtures\Example\ItemFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\DataFixtures\Example\UnitPeriodFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Datasheets\Item\ItemTypes;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\AuthorizationItem;
use U2\MultiTenancy\ApplicationConfigurator;
use U2\MultiTenancy\Tenant;
use U2\Security\Authorization\AuthorizationRight;
use U2\Workflow\StatusTypes;

class DatasheetUnitViewDataSubmitTest extends KernelTestCase
{
    public function test_submit_unit_view_data_with_period_is_not_in_the_unit_valid_date_range(): void
    {
        // Given
        $tokenStorageInterface = self::getContainer()->get(TokenStorageInterface::class);
        self::getContainer()->get(ApplicationConfigurator::class)->configure(new Tenant('test', [], 'test', 'standard'));
        $tokenStorageInterface->setToken(
            new UsernamePasswordToken(
                $user = UserFactory::createOne(
                    [
                        'authorizations' => [
                            AuthorizationFactory::new(
                                [
                                    'item' => AuthorizationItem::UnitPeriod->value,
                                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                                ]
                            ),
                        ],
                        'units' => [
                            $unit = UnitFactory::createOne([
                                'validTo' => new \DateTime('today'),
                            ]),
                        ],
                    ]
                )->_real(),
                'main',
                $user->getRoles()
            )
        );

        $layout = DatasheetFactory::createOne(['public' => true]);
        $period = PeriodFactory::createOne([
            'endDate' => new \DateTime('tomorrow'),
        ]);

        $layoutCollection = DatasheetCollectionFactory::createOne();

        // When
        $unitId = $unit->getId();
        $request = new Request();
        $request->setSession(new Session());
        $response = self::getContainer()->get(DatasheetUnitViewDataSubmit::class)->__invoke($request, $layout->getId(), $period->getId(), $unitId, $layoutCollection->getId()->toRfc4122());

        // Then
        $responseContentEncoded = $response->getContent();
        \assert(\is_string($responseContentEncoded));

        /** @var array{disabled: bool, messages: array<string, array<string>>} $responseContent . */
        $responseContent = json_decode($responseContentEncoded, true);

        self::assertSame(['error' => ['The form is disabled and cannot be saved.']], $responseContent['messages']);
    }

    public function test_submit_unit_view_data_with_no_permission_to_unit(): void
    {
        // Given
        $tokenStorageInterface = self::getContainer()->get(TokenStorageInterface::class);
        self::getContainer()->get(ApplicationConfigurator::class)->configure(new Tenant('test', [], 'test', 'standard'));
        $tokenStorageInterface->setToken(
            new UsernamePasswordToken(
                $user = UserFactory::createOne(
                    [
                        'authorizations' => [
                            AuthorizationFactory::new(
                                [
                                    'item' => AuthorizationItem::UnitPeriod->value,
                                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                                ]
                            ),
                        ],
                    ]
                )->_real(),
                'main',
                $user->getRoles()
            )
        );

        $layout = DatasheetFactory::createOne(['public' => true]);
        $period = PeriodFactory::createOne();
        $layoutCollection = DatasheetCollectionFactory::createOne();

        // When
        $unitId = UnitFactory::createOne()->getId();

        // Then
        $this->expectException(AccessDeniedException::class);

        // When
        self::getContainer()->get(DatasheetUnitViewDataSubmit::class)->__invoke(new Request(), $layout->getId(), $period->getId(), $unitId, $layoutCollection->getId()->toRfc4122());
    }

    public function test_submit_unit_view_data_with_no_permission_to_layout(): void
    {
        // Given
        $tokenStorageInterface = self::getContainer()->get(TokenStorageInterface::class);
        self::getContainer()->get(ApplicationConfigurator::class)->configure(new Tenant('test', [], 'test', 'standard'));
        $tokenStorageInterface->setToken(
            new UsernamePasswordToken(
                $user = UserFactory::createOne(
                    [
                        'authorizations' => [
                            AuthorizationFactory::new(
                                [
                                    'item' => AuthorizationItem::UnitPeriod->value,
                                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                                ]
                            ),
                        ],
                        'units' => [
                            $unit = UnitFactory::createOne(),
                        ],
                    ]
                )->_real(),
                'main',
                $user->getRoles()
            )
        );

        $layout = DatasheetFactory::createOne([
            'public' => false,
            'userPermissions' => [
                new UserPermissionInput($user, MaskBuilder::MASK_VIEW),
            ],
        ]);
        $period = PeriodFactory::createOne();
        $layoutCollection = DatasheetCollectionFactory::createOne();

        // When
        $unitId = $unit->getId();
        $response = self::getContainer()->get(DatasheetUnitViewDataSubmit::class)->__invoke(new Request(), $layout->getId(), $period->getId(), $unitId, $layoutCollection->getId()->toRfc4122());

        // Then
        $responseContentEncoded = $response->getContent();
        \assert(\is_string($responseContentEncoded));

        /** @var array{disabled: bool, messages: array<string, array<string>>} $responseContent . */
        $responseContent = json_decode($responseContentEncoded, true);

        self::assertSame(['error' => ['The form is disabled and cannot be saved.']], $responseContent['messages']);
    }

    public function test_submit_unit_view_data(): void
    {
        // Given
        $tokenStorageInterface = self::getContainer()->get(TokenStorageInterface::class);
        self::getContainer()->get(ApplicationConfigurator::class)->configure(new Tenant('test', [], 'test', 'standard'));
        $tokenStorageInterface->setToken(
            new UsernamePasswordToken(
                $user = UserFactory::createOne(
                    [
                        'authorizations' => [
                            AuthorizationFactory::new(
                                [
                                    'item' => AuthorizationItem::UnitPeriod->value,
                                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                                ]
                            ),
                        ],
                        'units' => [
                            $unit = UnitFactory::createOne(),
                        ],
                    ]
                )->_real(),
                'main',
                $user->getRoles()
            )
        );

        $period = PeriodFactory::createOne();
        UnitPeriodFactory::createOne([
            'unit' => $unit,
            'period' => $period,
            'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_IN_PROGRESS]),
        ]);

        $item = ItemFactory::new()->percent()->create([
            'editable' => true,
            'type' => ItemTypes::CHECKBOX,
        ]);
        $layout = DatasheetFactory::createOne([
            'fields' => [FieldFactory::createOne([
                'item' => $item->_real(),
                'disabled' => false,
            ])],
            'public' => false,
            'userPermissions' => [
                new UserPermissionInput($user, MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT),
            ],
        ]);
        $layoutCollection = DatasheetCollectionFactory::createOne();

        // When
        $unitId = $unit->getId();
        $request = new Request([], ['unit_view_form' => []]);
        $request->setMethod('POST');
        $response = self::getContainer()->get(DatasheetUnitViewDataSubmit::class)->__invoke($request, $layout->getId(), $period->getId(), $unitId, $layoutCollection->getId()->toRfc4122());

        // Then
        $responseContentEncoded = $response->getContent();
        \assert(\is_string($responseContentEncoded));

        /** @var array{disabled: bool, messages: array<string, array<string>>} $responseContent . */
        $responseContent = json_decode($responseContentEncoded, true);

        self::assertSame(['success' => ['Saved.']], $responseContent['messages']);
    }

    public function test_submit_unit_view_data_with_errors(): void
    {
        // Given
        $tokenStorageInterface = self::getContainer()->get(TokenStorageInterface::class);
        self::getContainer()->get(ApplicationConfigurator::class)->configure(new Tenant('test', [], 'test', 'standard'));
        $tokenStorageInterface->setToken(
            new UsernamePasswordToken(
                $user = UserFactory::createOne(
                    [
                        'authorizations' => [
                            AuthorizationFactory::new(
                                [
                                    'item' => AuthorizationItem::UnitPeriod->value,
                                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                                ]
                            ),
                        ],
                        'units' => [
                            $unit = UnitFactory::createOne(),
                        ],
                    ]
                )->_real(),
                'main',
                $user->getRoles()
            )
        );

        $period = PeriodFactory::createOne();
        UnitPeriodFactory::createOne([
            'unit' => $unit,
            'period' => $period,
            'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_IN_PROGRESS]),
        ]);

        $item = ItemFactory::new()->percent()->create([
            'editable' => true,
            'type' => ItemTypes::PERCENT,
        ]);
        $layout = DatasheetFactory::createOne([
            'fields' => [$field = FieldFactory::createOne([
                'item' => $item->_real(),
                'disabled' => false,
            ])],
            'public' => false,
            'userPermissions' => [
                new UserPermissionInput($user, MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT),
            ],
        ]);
        $layoutCollection = DatasheetCollectionFactory::createOne();

        // When
        $unitId = $unit->getId();
        $request = new Request([], ['unit_view_form' => ["tax_litigation_form[{$field->getName()}]" => 'invalid']]);
        $request->setMethod('POST');
        $response = self::getContainer()->get(DatasheetUnitViewDataSubmit::class)->__invoke($request, $layout->getId(), $period->getId(), $unitId, $layoutCollection->getId()->toRfc4122());

        // Then
        $responseContentEncoded = $response->getContent();
        \assert(\is_string($responseContentEncoded));

        /** @var array{disabled: bool, messages: array<string, array<string>>, violations: array<int, array{message: string, propertyPath: string|null}>} $responseContent . */
        $responseContent = json_decode($responseContentEncoded, true);

        self::assertFalse($responseContent['disabled']);
        self::assertSame([], $responseContent['messages']);

        self::assertSame([
            [
                'message' => 'This form should not contain extra fields.',
                'propertyPath' => '',
            ],
        ], $responseContent['violations']);
    }

    public function test_submit_unit_view_data_with_multiple_tasks_found(): void
    {
        // Given
        $tokenStorageInterface = self::getContainer()->get(TokenStorageInterface::class);
        self::getContainer()->get(ApplicationConfigurator::class)->configure(new Tenant('test', [], 'test', 'standard'));
        $tokenStorageInterface->setToken(
            new UsernamePasswordToken(
                $user = UserFactory::createOne(
                    [
                        'authorizations' => [
                            AuthorizationFactory::new(
                                [
                                    'item' => AuthorizationItem::UnitPeriod->value,
                                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                                ]
                            ),
                        ],
                        'units' => [
                            $unit = UnitFactory::createOne(),
                        ],
                    ]
                )->_real(),
                'main',
                $user->getRoles()
            )
        );

        $period = PeriodFactory::createOne();
        $item = ItemFactory::new()->percent()->create([
            'editable' => true,
            'type' => ItemTypes::CHECKBOX,
        ]);
        $layout = DatasheetFactory::createOne([
            'fields' => [FieldFactory::createOne([
                'item' => $item->_real(),
                'disabled' => false,
            ])],
            'public' => false,
            'userPermissions' => [
                new UserPermissionInput($user, MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT),
            ],
        ]);
        $layoutCollection = DatasheetCollectionFactory::createOne();

        $unitId = $unit->getId();

        $request = new Request([], ['unit_view_form' => []]);
        $request->setMethod('POST');

        // When
        UnitPeriodFactory::createOne([
            'unit' => $unit,
            'period' => $period,
            'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_IN_PROGRESS]),
        ]);
        UnitPeriodFactory::createOne([
            'unit' => $unit,
            'period' => $period,
            'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_IN_PROGRESS]),
        ]);

        $response = self::getContainer()->get(DatasheetUnitViewDataSubmit::class)->__invoke(new Request(), $layout->getId(), $period->getId(), $unitId, $layoutCollection->getId()->toRfc4122());

        // Then
        $responseContentEncoded = $response->getContent();
        \assert(\is_string($responseContentEncoded));

        /** @var array{disabled: bool, messages: array<string, array<string>>} $responseContent . */
        $responseContent = json_decode($responseContentEncoded, true);

        self::assertSame(['error' => ['The form is disabled and cannot be saved.']], $responseContent['messages']);
        self::assertArrayNotHasKey('violations', $responseContent);
    }
}
