<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Entity;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Tests\U2\KernelTestCase;
use U2\Entity\Workflow\Status;
use U2\Workflow\StatusTypes;

class StatusTest extends KernelTestCase
{
    private ValidatorInterface $validator;

    #[DataProvider('provideTypes')]
    public function test_set_type(string $type, bool $isValid): void
    {
        // When
        $violations = $this->validator->validatePropertyValue(Status::class, 'type', $type);

        // Then
        self::assertCount($isValid ? 0 : 1, $violations);
    }

    /**
     * @return array<string, array<string, string|bool>>
     */
    public static function provideTypes(): array
    {
        return [
            'Set status type "open"' => [
                'type' => StatusTypes::TYPE_OPEN,
                'isValid' => true,
            ],
            'Set status type "in progress"' => [
                'type' => StatusTypes::TYPE_IN_PROGRESS,
                'isValid' => true,
            ],
            'Set status type "closed"' => [
                'type' => StatusTypes::TYPE_COMPLETE,
                'isValid' => true,
            ],
            'Set status type "unknown' => [
                'type' => 'Unknown',
                'isValid' => false,
            ],
        ];
    }

    protected function setUp(): void
    {
        $this->validator = static::getContainer()->get(ValidatorInterface::class);
    }
}
