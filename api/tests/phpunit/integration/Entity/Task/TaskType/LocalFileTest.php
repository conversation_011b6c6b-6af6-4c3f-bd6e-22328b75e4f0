<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Entity\Task\TaskType;

use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\KernelTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\LocalFileFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\CurrentUserManipulator;
use U2\Security\Voter\DocumentVoterAttributes;

/**
 * @covers \U2\Entity\Task\TaskType\LocalFile
 */
class LocalFileTest extends KernelTestCase
{
    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }

    public function test_updating_a_record(): void
    {
        $createdUpdatedAtTimestamp = strtotime('2020-01-01T00:00:00');
        ClockMock::withClockMock($createdUpdatedAtTimestamp);
        $localFile = LocalFileFactory::createOne();
        $createdUpdatedAtTimestamp = $localFile->getUpdatedAt()?->getTimestamp();

        self::assertNotNull($localFile->getPeriod());

        self::getEntityManager()->clear();

        $localFileFromDb = self::getEntityManager()->find(LocalFile::class, $localFile->getId());
        \assert($localFileFromDb instanceof LocalFile);

        // When
        $updatedAtTimeStamp = strtotime('2020-01-01T08:00:00');
        ClockMock::withClockMock($updatedAtTimeStamp);

        $previousPeriod = $localFileFromDb->getPeriod();
        $newPeriod = PeriodFactory::createOne();
        $localFileFromDb->setPeriod($newPeriod->_real());

        // When
        self::getEntityManager()->persist($localFileFromDb);

        self::assertSame($createdUpdatedAtTimestamp, $localFileFromDb->getUpdatedAt()?->getTimestamp(), 'The updatedAt should not have been updated yet');
        self::getEntityManager()->flush();

        self::assertNotSame($createdUpdatedAtTimestamp, $localFileFromDb->getUpdatedAt()?->getTimestamp(), 'The updatedAt should have been updated');
        self::getEntityManager()->clear();

        // Then
        $localFileFromDb = self::getEntityManager()->find(LocalFile::class, $localFile->getId());
        self::assertNotSame($previousPeriod?->getId(), $localFileFromDb?->getPeriod()?->getId());
        self::assertNotSame($createdUpdatedAtTimestamp, $localFileFromDb?->getUpdatedAt()?->getTimestamp());
    }

    public function test_record_in_a_closed_period(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => 'TPM_LOCAL_FILE',
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::new()->complete()->create(),
            'createdBy' => $authorizedUser,
            'userPermissions' => [
                new UserPermissionInput($authorizedUser->_real(), MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
            ],
        ]);

        self::getContainer()->get(CurrentUserManipulator::class)->change($authorizedUser->_real());

        self::assertTrue(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::editConfiguration, $localFile->_real()));
        self::assertFalse(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::delete, $localFile->_real()));
        self::assertFalse(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::editContent, $localFile->_real()));
    }
}
