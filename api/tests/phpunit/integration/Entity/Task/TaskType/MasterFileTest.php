<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Entity\Task\TaskType;

use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\KernelTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\MasterFileFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\CurrentUserManipulator;
use U2\Security\Voter\DocumentVoterAttributes;

/**
 * @covers \U2\Entity\Task\TaskType\MasterFile
 */
class MasterFileTest extends KernelTestCase
{
    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }

    public function test_updated_at_is_set_after_flush(): void
    {
        $createdUpdatedAtTimestamp = strtotime('2020-01-01T00:00:00');
        ClockMock::withClockMock($createdUpdatedAtTimestamp);
        $masterFile = MasterFileFactory::createOne();
        $createdUpdatedAtTimestamp = $masterFile->getUpdatedAt()?->getTimestamp();

        self::assertNotNull($masterFile->getPeriod());

        self::getEntityManager()->clear();

        $masterFileFromDb = self::getEntityManager()->find(MasterFile::class, $masterFile->getId());
        \assert($masterFileFromDb instanceof MasterFile);

        // When
        $updatedAtTimeStamp = strtotime('2020-01-01T08:00:00');
        ClockMock::withClockMock($updatedAtTimeStamp);

        $previousPeriod = $masterFileFromDb->getPeriod();
        $newPeriod = PeriodFactory::createOne();
        $masterFileFromDb->setPeriod($newPeriod->_real());

        // When
        self::getEntityManager()->persist($masterFileFromDb);

        self::assertSame($createdUpdatedAtTimestamp, $masterFileFromDb->getUpdatedAt()?->getTimestamp(), 'The updatedAt should not have been updated yet');
        self::getEntityManager()->flush();

        self::assertNotSame($createdUpdatedAtTimestamp, $masterFileFromDb->getUpdatedAt()?->getTimestamp(), 'The updatedAt should have been updated');
        self::getEntityManager()->clear();

        // Then
        $masterFileFromDb = self::getEntityManager()->find(MasterFile::class, $masterFile->getId());
        self::assertNotSame($previousPeriod?->getId(), $masterFileFromDb?->getPeriod()?->getId());
        self::assertNotSame($createdUpdatedAtTimestamp, $masterFileFromDb?->getUpdatedAt()?->getTimestamp());
    }

    public function test_record_in_a_closed_period(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => 'TPM_MASTER_FILE',
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);

        $masterFile = MasterFileFactory::createOne([
            'status' => StatusFactory::new()->complete()->create(),
            'createdBy' => $authorizedUser,
            'userPermissions' => [
                new UserPermissionInput($authorizedUser->_real(), MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
            ],
        ]);

        self::getContainer()->get(CurrentUserManipulator::class)->change($authorizedUser->_real());

        self::assertTrue(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::editConfiguration, $masterFile->_real()));
        self::assertFalse(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::delete, $masterFile->_real()));
        self::assertFalse(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::editContent, $masterFile->_real()));
    }
}
