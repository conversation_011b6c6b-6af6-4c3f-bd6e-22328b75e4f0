<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Entity\Task\TaskType;

use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\KernelTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\CountryByCountryReportFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\CurrentUserManipulator;
use U2\Security\Voter\DocumentVoterAttributes;

/**
 * @covers \U2\Entity\Task\TaskType\CountryByCountryReport
 */
class CountryByCountryFileTest extends KernelTestCase
{
    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }

    public function test_updating_a_record(): void
    {
        $createdUpdatedAtTimestamp = strtotime('2020-01-01T00:00:00');
        ClockMock::withClockMock($createdUpdatedAtTimestamp);
        $countryByCountryReport = CountryByCountryReportFactory::createOne();
        $createdUpdatedAtTimestamp = $countryByCountryReport->getUpdatedAt()?->getTimestamp();

        self::assertNotNull($countryByCountryReport->getPeriod());

        self::getEntityManager()->clear();

        $countryByCountryReportFromDb = self::getEntityManager()->find(CountryByCountryReport::class, $countryByCountryReport->getId());
        \assert($countryByCountryReportFromDb instanceof CountryByCountryReport);

        // When
        $updatedAtTimeStamp = strtotime('2020-01-01T08:00:00');
        ClockMock::withClockMock($updatedAtTimeStamp);

        $previousPeriod = $countryByCountryReportFromDb->getPeriod();
        $newPeriod = PeriodFactory::createOne();
        $countryByCountryReportFromDb->setPeriod($newPeriod->_real());

        // When
        self::getEntityManager()->persist($countryByCountryReportFromDb);

        self::assertSame($createdUpdatedAtTimestamp, $countryByCountryReportFromDb->getUpdatedAt()?->getTimestamp(), 'The updatedAt should not have been updated yet');
        self::getEntityManager()->flush();

        self::assertNotSame($createdUpdatedAtTimestamp, $countryByCountryReportFromDb->getUpdatedAt()?->getTimestamp(), 'The updatedAt should have been updated');
        self::getEntityManager()->clear();

        // Then
        $countryByCountryReportFromDb = self::getEntityManager()->find(CountryByCountryReport::class, $countryByCountryReport->getId());
        self::assertNotSame($previousPeriod?->getId(), $countryByCountryReportFromDb?->getPeriod()?->getId());
        self::assertNotSame($createdUpdatedAtTimestamp, $countryByCountryReportFromDb?->getUpdatedAt()?->getTimestamp());
    }

    public function test_record_in_a_closed_period(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'status' => StatusFactory::new()->complete()->create(),
            'createdBy' => $authorizedUser,
            'userPermissions' => [
                new UserPermissionInput($authorizedUser->_real(), MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
            ],
        ]);

        self::getContainer()->get(CurrentUserManipulator::class)->change($authorizedUser->_real());

        self::assertTrue(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::editConfiguration, $countryByCountryReport->_real()));
        self::assertFalse(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::delete, $countryByCountryReport->_real()));
        self::assertFalse(self::getContainer()->get(Security::class)->isGranted(DocumentVoterAttributes::editContent, $countryByCountryReport->_real()));
    }
}
