<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Security\Voter\Task;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\KernelTestCase;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\Task\TaskTypeVoter;
use U2\Security\Voter\Task\TaskTypeVoterHelper;
use U2\Security\Voter\VoterAttributes;
use U2\Task\TaskType\TransactionInterface;
use U2\Task\TaskTypeKnowledge;
use U2\Unit\Assignment\UserUnitAssignmentChecker;

class TaskTypeVoterTest extends KernelTestCase
{
    public function test_supported_attributes(): void
    {
        $voter = new TaskTypeVoter(
            $this->createMock(TaskTypeVoterHelper::class),
            $this->createMock(UserUnitAssignmentChecker::class)
        );

        self::assertTrue($voter->supportsAttribute(VoterAttributes::addAttachment));
        self::assertTrue($voter->supportsAttribute(VoterAttributes::removeAttachment));
        self::assertTrue($voter->supportsAttribute(VoterAttributes::read));
        self::assertTrue($voter->supportsAttribute(VoterAttributes::write));
        self::assertTrue($voter->supportsAttribute(VoterAttributes::delete));
        self::assertFalse($voter->supportsAttribute('UNSUPPORTED_ATTRIBUTE'));
    }

    public function test_supported_types(): void
    {
        $voter = new TaskTypeVoter(
            $this->createMock(TaskTypeVoterHelper::class),
            $this->createMock(UserUnitAssignmentChecker::class)
        );

        self::assertTrue($voter->supportsType(TaskType::class));
        self::assertFalse($voter->supportsType(\stdClass::class));
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeAttributeMap')]
    public function test_deny_all_when_user_not_logged_in(string $taskTypeClass, string $attribute): void
    {
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->expects($this->never())->method('check');
        $voter = new TaskTypeVoter($this->createMock(TaskTypeVoterHelper::class), $userUnitAssignmentChecker);

        $decision = $voter->vote(
            $this->createMock(TokenInterface::class),
            $this->createMock($taskTypeClass),
            [$attribute]
        );

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision for a new issue');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeAttributeMap')]
    public function test_deny_all_when_tasktype_unit_is_not_assigned_to_current_user(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $unit = new Unit();
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(1);
        $taskType->expects($this->atLeastOnce())->method('getUnit')->willReturn($unit);
        $token = $this->getTokenWithLoggedInUser();
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->expects($this->once())->method('check')->with($token->getUser(), [$unit])->willReturn(false);
        $voter = new TaskTypeVoter($helper, $userUnitAssignmentChecker);

        $decision = $voter->vote($token, $taskType, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision for a user where the issue unit is not assigned');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeTransactionAttributeMap')]
    public function test_deny_all_when_tasktype_partner_unit_is_empty(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $unit = new Unit();
        $token = $this->getTokenWithLoggedInUser();
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(1);
        $taskType->expects($this->atLeastOnce())->method('getUnit')->willReturn($unit);
        $taskType->expects($this->once())->method('getPartnerUnit')->willReturn(null);
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->expects($this->once())->method('check')->with($token->getUser(), [$unit])->willReturn(false);
        $voter = new TaskTypeVoter($helper, $userUnitAssignmentChecker);

        $decision = $voter->vote($token, $taskType, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision for a missing unit');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeTransactionAttributeMap')]
    public function test_deny_all_when_tasktype_partner_unit_is_not_assigned_to_current_user(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $unit = new Unit();
        $partnerUnit = new Unit();
        $token = $this->getTokenWithLoggedInUser();
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(1);
        $taskType->expects($this->atLeastOnce())->method('getUnit')->willReturn($unit);
        $taskType->expects($this->atLeastOnce())->method('getPartnerUnit')->willReturn($partnerUnit);
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->willReturn(false);
        $voter = new TaskTypeVoter($helper, $userUnitAssignmentChecker);

        $decision = $voter->vote($token, $taskType, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision for a missing unit');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeAttributeMap')]
    public function test_grant_all_when_tasktype_is_new(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(null);
        $taskType->expects($this->never())->method('getUnit')->willReturn(new Unit());
        $token = $this->getTokenWithLoggedInUser();
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->expects($this->never())->method('check');
        $voter = new TaskTypeVoter($helper, $userUnitAssignmentChecker);

        $decision = $voter->vote($token, $taskType, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision for a new issue');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeAttributeMap')]
    public function test_grant_all_when_tasktype_has_no_unit(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $token = $this->getTokenWithLoggedInUser();
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(1);
        $taskType->expects($this->once())->method('getUnit')->willReturn(null);
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $voter = new TaskTypeVoter($helper, self::createStub(UserUnitAssignmentChecker::class));

        $decision = $voter->vote($token, $taskType, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision for a missing unit');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeAttributeMap')]
    public function test_grant_all_when_tasktype_unit_is_assigned_to_current_user(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $unit = new Unit();
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(1);
        $taskType->expects($this->atLeastOnce())->method('getUnit')->willReturn($unit);
        $token = $this->getTokenWithLoggedInUser();
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->expects($this->once())->method('check')->with($token->getUser(), [$unit])->willReturn(true);
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $voter = new TaskTypeVoter($helper, $userUnitAssignmentChecker);

        $decision = $voter->vote($token, $taskType, [$attribute]);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter should grant delete to a user that has been assigned the unit');
    }

    /**
     * @param class-string $taskTypeClass
     */
    #[DataProvider('provideTaskTypeTransactionAttributeMap')]
    public function test_grant_when_tasktype_partnerunit_is_assigned_to_current_user(string $taskTypeClass, string $attribute, string $authorizationRight): void
    {
        $unit = new Unit();
        $partnerUnit = new Unit();
        $taskType = $this->createMock($taskTypeClass);
        $taskType->expects($this->once())->method('getId')->willReturn(1);
        $taskType->expects($this->atLeastOnce())->method('getUnit')->willReturn($unit);
        $taskType->expects($this->atLeastOnce())->method('getPartnerUnit')->willReturn($partnerUnit);
        $token = $this->getTokenWithLoggedInUser();
        $userUnitAssignmentChecker = $this->createMock(UserUnitAssignmentChecker::class);
        $userUnitAssignmentChecker->method('check')->willReturnCallback(
            fn ($user, $units): bool => $units === [$partnerUnit] ? true : false
        );
        $helper = $this->createMock(TaskTypeVoterHelper::class);
        $helper->expects($this->once())->method('hasAuthorizationRight')->with($taskType, $token->getUser(), $authorizationRight)->willReturn(true);
        $voter = new TaskTypeVoter($helper, $userUnitAssignmentChecker);

        $decision = $voter->vote($token, $taskType, [$attribute]);

        $expected = VoterInterface::ACCESS_DENIED;

        if (is_a($taskTypeClass, Transaction::class, true) || VoterAttributes::read === $attribute) {
            $expected = VoterInterface::ACCESS_GRANTED;
        }

        self::assertSame($expected, $decision, 'The voter should grant delete to a user that has been assigned the unit');
    }

    /**
     * @return iterable<string, array{class-string<TaskType>, string, string}>
     */
    public static function provideTaskTypeAttributeMap(): iterable
    {
        foreach (TaskTypeKnowledge::taskTypeClasses as $taskTypeClass) {
            $isStructuredDocument = is_subclass_of($taskTypeClass, StructuredDocumentInterface::class);

            yield $taskTypeClass . ' add_attachment' => [$taskTypeClass, VoterAttributes::addAttachment, AuthorizationRight::UPDATE->value];
            yield $taskTypeClass . ' remove_attachment' => [$taskTypeClass, VoterAttributes::removeAttachment, AuthorizationRight::UPDATE->value];
            yield $taskTypeClass . ' read' => [$taskTypeClass, VoterAttributes::read, AuthorizationRight::READ->value];
            yield $taskTypeClass . ' write' => [$taskTypeClass, VoterAttributes::write, AuthorizationRight::UPDATE->value];
            yield $taskTypeClass . ' delete' => [$taskTypeClass, VoterAttributes::delete, AuthorizationRight::DELETE->value];
        }
    }

    /**
     * @return iterable<string, array{class-string<TaskType>, string, string}>
     */
    public static function provideTaskTypeTransactionAttributeMap(): iterable
    {
        // loop over all task types of provideTaskTypeAttributeMap and filter out classes that do not implement TransactionInterface
        foreach (self::provideTaskTypeAttributeMap() as $key => $data) {
            if (!is_a($data[0], TransactionInterface::class, true)) {
                continue;
            }

            yield $key => $data;
        }
    }

    protected function getTokenWithLoggedInUser(): TokenInterface
    {
        $token = $this->createMock(TokenInterface::class);
        $token->method('getUser')->willReturn(new User());

        return $token;
    }
}
