<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\SavedFilter;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\SavedFilterFactory;
use U2\DataFixtures\Example\SavedFilterSubscriptionFactory;
use U2\DataFixtures\Example\UserFactory;

/**
 * @covers \U2\Entity\SavedFilterSubscription
 */
class SavedFilter_SavedFilterSubscriptionGetCollectionTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $savedFilter = SavedFilterFactory::createOne();
        SavedFilterSubscriptionFactory::createMany(2, ['savedFilter' => $savedFilter]);

        // When
        $requestUrl = "/api/saved-filters/{$savedFilter->getId()}/saved-filter-subscriptions";

        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/SavedFilterSubscription',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }
}
