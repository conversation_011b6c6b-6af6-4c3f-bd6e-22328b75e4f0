<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\File;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\CountryByCountryReportFactory;
use U2\DataFixtures\Example\CountryByCountryReportSectionFactory;
use U2\DataFixtures\Example\DocumentTemplateFactory;
use U2\DataFixtures\Example\DocumentTemplateSectionFactory;
use U2\DataFixtures\Example\FileFactory;
use U2\DataFixtures\Example\IncomeTaxPlanningFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\LocalFileFactory;
use U2\DataFixtures\Example\LocalFileSectionFactory;
use U2\DataFixtures\Example\LossCarryForwardFactory;
use U2\DataFixtures\Example\MasterFileFactory;
use U2\DataFixtures\Example\MasterFileSectionFactory;
use U2\DataFixtures\Example\OrganisationalGroupFactory;
use U2\DataFixtures\Example\OtherDeadlineFactory;
use U2\DataFixtures\Example\PermanentEstablishmentFactory;
use U2\DataFixtures\Example\TaxAssessmentMonitorFactory;
use U2\DataFixtures\Example\TaxAuditRiskFactory;
use U2\DataFixtures\Example\TaxConsultingFeeFactory;
use U2\DataFixtures\Example\TaxCreditFactory;
use U2\DataFixtures\Example\TaxFilingMonitorFactory;
use U2\DataFixtures\Example\TaxLitigationFactory;
use U2\DataFixtures\Example\TaxRateFactory;
use U2\DataFixtures\Example\TaxRelevantRestrictionFactory;
use U2\DataFixtures\Example\TransferPricingFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\File;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\File
 */
class File_LinkedEntitiesGetCollectionTest extends ApiTestCase
{
    public function test_get_linked_entities_of_a_file_as_unauthorized(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();
        $authorizedUser = $adminUser;

        static::getContainer()->get(EntityManagerInterface::class);
        $file = FileFactory::createOne(
            [
                'createdBy' => $authorizedUser,
                'description' => 'My awesome file',
            ]
        );

        $unauthorizedUser = UserFactory::createOne();
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $id = $file->getId();
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(File::class, ['id' => $id]) . '/linked-entities'
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_linked_entities(): void
    {
        $authorizedUser = UserFactory::createOne([
            'userRoles' => [UserRoles::User->value, UserRoles::Admin->value],
            'authorizations' => [
                AuthorizationFactory::new(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::Contract->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::UnitPeriod->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxAssessmentStatus->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxAuditRisk->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxLitigation->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TransferPricing->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxRelevantRestriction->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::LossCarryForward->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxCredit->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxRate->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::IncomeTaxPlanning->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxConsultingFee->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxFilingMonitor->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxAssessmentMonitor->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::TaxAuthorityAuditObjection->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::OtherDeadline->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::LocalFile->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::CountryByCountryReport->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::Transaction->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::FinancialData->value, 'rights' => [AuthorizationRight::READ->value]]),
                AuthorizationFactory::new(['item' => AuthorizationItem::MainBusinessActivity->value, 'rights' => [AuthorizationRight::READ->value]]),
            ],
        ]);
        $client = self::createClientWithAuth($authorizedUser);

        $file = FileFactory::createOne(
            [
                'createdBy' => $authorizedUser,
                'description' => 'My awesome file',
            ]
        );

        // Resource the user is authorized to
        $unit = UnitFactory::createOne(['files' => [$file->_real()]]);
        $authorizedUser->addUnit($unit->_real());
        $legalUnit = LegalUnitFactory::createOne(['files' => [$file->_real()]]);
        $authorizedUser->addUnit($legalUnit->_real());
        $organisationalGroup = OrganisationalGroupFactory::createOne(['files' => [$file->_real()]]);
        $authorizedUser->addUnit($organisationalGroup->_real());
        $permanentEstablishment = PermanentEstablishmentFactory::createOne(['files' => [$file->_real()]]);
        $authorizedUser->addUnit($permanentEstablishment->_real());

        $masterFile = MasterFileFactory::createOne([
            'createdBy' => $authorizedUser,
            'sections' => MasterFileSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        $localFile = LocalFileFactory::createOne([
            'createdBy' => $authorizedUser,
            'sections' => LocalFileSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'createdBy' => $authorizedUser,
            'sections' => CountryByCountryReportSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        $documentTemplate = DocumentTemplateFactory::createOne([
            'createdBy' => $authorizedUser,
            'sections' => DocumentTemplateSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        $contract = ContractFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $otherDeadline = OtherDeadlineFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $incomeTaxPlanning = IncomeTaxPlanningFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $lossCarryForward = LossCarryForwardFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxAssessmentMonitor = TaxAssessmentMonitorFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxAuditRisk = TaxAuditRiskFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxConsultingFee = TaxConsultingFeeFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxCredit = TaxCreditFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxFilingMonitor = TaxFilingMonitorFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxLitigation = TaxLitigationFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxRelevantRestriction = TaxRelevantRestrictionFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $taxRate = TaxRateFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);
        $transferPricing = TransferPricingFactory::createOne(['files' => [$file->_real()], 'unit' => $unit]);

        // When
        $requestUrl = '/api/files/' . $file->getId() . '/linked-entities';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
            [
                'json' => [
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        $firstLocalFileSection = $localFile->getSections()->first();
        \assert(false !== $firstLocalFileSection);
        $firstMasterFileSection = $masterFile->getSections()->first();
        \assert(false !== $firstMasterFileSection);
        $firstCountryByCountryReportSection = $countryByCountryReport->getSections()->first();
        \assert(false !== $firstCountryByCountryReportSection);
        $firstDocumentTemplateSection = $documentTemplate->getSections()->first();
        \assert(false !== $firstDocumentTemplateSection);

        self::assertJsonContains([
            '@context' => '/api/contexts/FileLinkedEntity',
            '@id' => $requestUrl,
            'hydra:member' => [
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $contract->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'cm-contract',
                            'displayName' => $contract->getDisplayName(),
                            'taskTypeId' => $contract->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $incomeTaxPlanning->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-income-tax-planning',
                            'displayName' => $incomeTaxPlanning->getDisplayName(),
                            'taskTypeId' => $incomeTaxPlanning->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $lossCarryForward->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-loss-carry-forward',
                            'displayName' => $lossCarryForward->getDisplayName(),
                            'taskTypeId' => $lossCarryForward->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxAuditRisk->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-tax-audit-risk',
                            'displayName' => $taxAuditRisk->getDisplayName(),
                            'taskTypeId' => $taxAuditRisk->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxConsultingFee->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-tax-consulting-fee',
                            'displayName' => $taxConsultingFee->getDisplayName(),
                            'taskTypeId' => $taxConsultingFee->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxCredit->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-tax-credit',
                            'displayName' => $taxCredit->getDisplayName(),
                            'taskTypeId' => $taxCredit->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxLitigation->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-tax-litigation',
                            'displayName' => $taxLitigation->getDisplayName(),
                            'taskTypeId' => $taxLitigation->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxRate->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-tax-rate',
                            'displayName' => $taxRate->getDisplayName(),
                            'taskTypeId' => $taxRate->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxRelevantRestriction->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-tax-relevant-restriction',
                            'displayName' => $taxRelevantRestriction->getDisplayName(),
                            'taskTypeId' => $taxRelevantRestriction->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $transferPricing->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tam-transfer-pricing',
                            'displayName' => $transferPricing->getDisplayName(),
                            'taskTypeId' => $transferPricing->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $otherDeadline->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tcm-other-deadline',
                            'displayName' => $otherDeadline->getDisplayName(),
                            'taskTypeId' => $otherDeadline->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxAssessmentMonitor->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tcm-tax-assessment-monitor',
                            'displayName' => $taxAssessmentMonitor->getDisplayName(),
                            'taskTypeId' => $taxAssessmentMonitor->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/tasks/' . $taxFilingMonitor->getTask()->getId()->toRfc4122(),
                        '@type' => 'Task',
                        'u2:extra' => [
                            'shortName' => 'tcm-tax-filing-monitor',
                            'displayName' => $taxFilingMonitor->getDisplayName(),
                            'taskTypeId' => $taxFilingMonitor->getId(),
                        ],
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/master-file-sections/' . $firstMasterFileSection->getId(),
                        '@type' => 'MasterFileSection',
                        'document' => '/api/master-files/' . $masterFile->getId(),
                        'id' => $firstMasterFileSection->getId(),
                        'displayName' => $firstMasterFileSection->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/local-file-sections/' . $firstLocalFileSection->getId(),
                        '@type' => 'LocalFileSection',
                        'document' => '/api/local-files/' . $localFile->getId(),
                        'id' => $firstLocalFileSection->getId(),
                        'displayName' => $firstLocalFileSection->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/country-by-country-report-sections/' . $firstCountryByCountryReportSection->getId(),
                        '@type' => 'CountryByCountryReportSection',
                        'document' => '/api/country-by-country-reports/' . $countryByCountryReport->getId(),
                        'id' => $firstCountryByCountryReportSection->getId(),
                        'displayName' => $firstCountryByCountryReportSection->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/document-template-sections/' . $firstDocumentTemplateSection->getId(),
                        '@type' => 'DocumentTemplateSection',
                        'document' => '/api/document-templates/' . $documentTemplate->getId(),
                        'id' => $firstDocumentTemplateSection->getId(),
                        'displayName' => $firstDocumentTemplateSection->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/units/' . $unit->getId(),
                        '@type' => 'Unit',
                        'id' => $unit->getId(),
                        'displayName' => $unit->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/legal-units/' . $legalUnit->getId(),
                        '@type' => 'LegalUnit',
                        'id' => $legalUnit->getId(),
                        'displayName' => $legalUnit->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/organisational-groups/' . $organisationalGroup->getId(),
                        '@type' => 'OrganisationalGroup',
                        'id' => $organisationalGroup->getId(),
                        'displayName' => $organisationalGroup->getDisplayName(),
                    ],
                ],
                [
                    'linkedEntity' => [
                        '@id' => '/api/permanent-establishments/' . $permanentEstablishment->getId(),
                        '@type' => 'PermanentEstablishment',
                        'id' => $permanentEstablishment->getId(),
                        'displayName' => $permanentEstablishment->getDisplayName(),
                    ],
                ],
            ],
        ]);
    }

    public function test_get_linked_entities_which_the_user_has_no_read_access_to(): void
    {
        $authorizedUser = UserFactory::createOne(['userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($authorizedUser);
        $file = FileFactory::createOne(['createdBy' => $authorizedUser]);

        // Resources the user is not authorized
        UnitFactory::createOne(['files' => [$file->_real()]]);
        LegalUnitFactory::createOne(['files' => [$file->_real()]]);
        OrganisationalGroupFactory::createOne(['files' => [$file->_real()]]);
        PermanentEstablishmentFactory::createOne(['files' => [$file->_real()]]);
        MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        LocalFileFactory::createOne([
            'sections' => LocalFileSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        DocumentTemplateFactory::createOne([
            'sections' => DocumentTemplateSectionFactory::new(['files' => [$file->_real()]])->many(1),
        ]);
        ContractFactory::createOne(['files' => [$file->_real()]]);
        OtherDeadlineFactory::createOne(['files' => [$file->_real()]]);
        IncomeTaxPlanningFactory::createOne(['files' => [$file->_real()]]);
        LossCarryForwardFactory::createOne(['files' => [$file->_real()]]);
        TaxAssessmentMonitorFactory::createOne(['files' => [$file->_real()]]);
        TaxAuditRiskFactory::createOne(['files' => [$file->_real()]]);
        TaxConsultingFeeFactory::createOne(['files' => [$file->_real()]]);
        TaxCreditFactory::createOne(['files' => [$file->_real()]]);
        TaxFilingMonitorFactory::createOne(['files' => [$file->_real()]]);
        TaxLitigationFactory::createOne(['files' => [$file->_real()]]);
        TaxRelevantRestrictionFactory::createOne(['files' => [$file->_real()]]);
        TaxRateFactory::createOne(['files' => [$file->_real()]]);
        TransferPricingFactory::createOne(['files' => [$file->_real()]]);

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            '/api/files/' . $file->getId() . '/linked-entities',
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertCount(21, $response->toArray(false)['hydra:member']);
    }
}
