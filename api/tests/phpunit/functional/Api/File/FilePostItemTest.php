<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\File;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\ApiTestCase;
use Tests\U2\TestUtils;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\CountryByCountryReportFactory;
use U2\DataFixtures\Example\CountryByCountryReportSectionFactory;
use U2\DataFixtures\Example\FileFactory;
use U2\DataFixtures\Example\FileTypeFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\CountryByCountryReportSection;
use U2\Entity\DocumentSection;
use U2\Entity\File;
use U2\Entity\FileType;
use U2\Entity\LegalUnit;
use U2\Entity\Task\Task;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Repository\FileRepository;
use U2\Security\Authorization\AuthorizationRight;
use U2\Workflow\StatusTypes;

/**
 * @covers \U2\Entity\File
 */
class FilePostItemTest extends ApiTestCase
{
    public function test_user_can_create_a_file(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();
        $authorizedUser = $adminUser;
        $client = self::createClientWithAuth($authorizedUser);

        $fileType2 = FileTypeFactory::createOne(['name' => 'FileType2']);
        $fileType3 = FileTypeFactory::createOne(['name' => 'FileType3']);

        $group = UserGroupFactory::createOne(
            [
                'name' => 'My group',
                'description' => 'My group description',
            ]
        );

        $newFileOwner = UserFactory::createOne();

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');
        $newFileOwnerIri = $this->findIriBy(User::class, ['id' => $newFileOwner->getId()]);
        $groupIri = $this->findIriBy(UserGroup::class, ['id' => $group->getId()]);
        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'types' => json_encode([
                            $this->findIriBy(FileType::class, ['id' => $fileType2->getId()]),
                            $this->findIriBy(FileType::class, ['id' => $fileType3->getId()]),
                        ], \JSON_THROW_ON_ERROR),
                        'userPermissions' => json_encode([
                            [
                                'user' => $newFileOwnerIri,
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'groupPermissions' => json_encode([
                            [
                                'group' => $groupIri,
                                'mask' => MaskBuilder::MASK_VIEW,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                    ],
                ],
            ]
        );

        $responseData = $response->toArray();

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        $createdAt = $responseData['createdAt'];
        \assert(null !== $createdAt);
        $updatedAt = $responseData['updatedAt'];
        \assert(null !== $updatedAt);
        $authorizedUserIri = $this->findIriBy(User::class, ['id' => $authorizedUser->getId()]);
        self::assertJsonContains([
            '@context' => '/api/contexts/File',
            '@type' => 'File',
            '@id' => $responseData['@id'],
            'name' => $responseData['name'],
            'description' => 'My new file',
            'id' => $responseData['id'],
            'types' => [
                $this->findIriBy(FileType::class, ['id' => $fileType2->getId()]),
                $this->findIriBy(FileType::class, ['id' => $fileType3->getId()]),
            ],
            'accessType' => File::SMART_ACCESS,
            'createdBy' => $authorizedUserIri,
            'updatedBy' => $authorizedUserIri,
            'createdAt' => $responseData['createdAt'],
            'updatedAt' => $responseData['updatedAt'],
            'userPermissions' => [
                [
                    'user' => $newFileOwnerIri,
                    'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                ],
            ],
            'groupPermissions' => [
                [
                    'group' => $groupIri,
                    'mask' => MaskBuilder::MASK_VIEW,
                ],
            ],
            'contentType' => $responseData['contentType'],
            'sizeInBytes' => $responseData['sizeInBytes'],
            'downloadPath' => \sprintf('/legacy/file/%s/download', $responseData['id']),
        ]);
    }

    public function test_user_can_create_and_link_a_file_to_a_unit(): void
    {
        // Ensure updated at is some days before, so we can detect if it was updated
        $updatedAt = new \DateTime('-5 days');
        $legalUnit = LegalUnitFactory::createOne(['updatedAt' => $updatedAt]);
        $adminUser = UserFactory::findOrCreate(['username' => 'admin']);
        $adminUser->addUnit($legalUnit->_real());
        $adminUser->_save();
        $client = self::createClientWithAuth($adminUser);
        self::assertSame($updatedAt->format(\DATE_ATOM), $legalUnit->getUpdatedAt()?->format(\DATE_ATOM));

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');

        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'groupPermissions' => '[]',
                        'types' => '[]',
                        'userPermissions' => json_encode([
                            [
                                'user' => $this->findIriBy(User::class, ['id' => $adminUser->getId()]),
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'linkedResource' => $this->findIriBy(Unit::class, ['id' => $legalUnit->getId()]),
                    ],
                ],
            ]
        );

        // IT tries to deserialize the uploaded file :shrug:
        $responseData = $response->toArray();

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        $file = self::getContainer()->get(FileRepository::class)->find($responseData['id']);
        \assert($file instanceof File);

        $authorizedUserIri = $this->findIriBy(User::class, ['id' => $adminUser->getId()]);
        self::assertJsonContains([
            '@context' => '/api/contexts/File',
            '@type' => 'File',
            '@id' => $responseData['@id'],
            'name' => $file->getName(),
            'description' => 'My new file',
            'id' => $file->getId(),
            'types' => [],
            'accessType' => File::SMART_ACCESS,
            'createdBy' => $authorizedUserIri,
            'updatedBy' => $authorizedUserIri,
            'createdAt' => $file->getCreatedAt()?->format(\DATE_W3C),
            'updatedAt' => $file->getUpdatedAt()?->format(\DATE_W3C),
            'userPermissions' => [
                [
                    'user' => $authorizedUserIri,
                    'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                ],
            ],
            'groupPermissions' => [],
            'contentType' => $file->getMimeType(),
            'sizeInBytes' => $file->getFileSize(),
            'downloadPath' => \sprintf('/legacy/file/%d/download', $file->getId()),
        ]);

        self::assertCount(1, $legalUnit->getFiles());

        // Ensure updatedAt has been updated
        $entityManager = self::getEntityManager();
        self::assertNotSame(
            $entityManager->find(LegalUnit::class, $legalUnit->getId())?->getUpdatedAt()?->getTimestamp(),
            $updatedAt->getTimestamp()
        );
    }

    public function test_unauthorized_user_cannot_create_and_link_a_file_to_a_unit(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);
        $legalUnit = LegalUnitFactory::createOne()->_real();

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'groupPermissions' => '[]',
                        'types' => '[]',
                        'userPermissions' => json_encode([
                            [
                                'user' => $this->findIriBy(User::class, ['id' => $user->getId()]),
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'linkedResource' => $this->findIriBy(Unit::class, ['id' => $legalUnit->getId()]),
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN, 'You are not allowed to link a new file to the given record.');
        self::assertResponseHeaderSame('content-type', 'application/problem+json; charset=utf-8');
    }

    public function test_unauthorized_user_cannot_create_and_link_a_file_to_task(): void
    {
        $user = UserFactory::createOne([
            'authorizations' => new ArrayCollection([
                AuthorizationFactory::createOne(
                    [
                        'item' => AuthorizationItem::Contract->value,
                        'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                    ]
                )->_real(),
            ]),
        ]);
        $client = self::createClientWithAuth($user);

        $contract = ContractFactory::createOne(
            [
                'unit' => LegalUnitFactory::createOne()->_real(),
            ]
        )->_real();

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'groupPermissions' => '[]',
                        'types' => '[]',
                        'userPermissions' => json_encode([
                            [
                                'user' => $this->findIriBy(User::class, ['id' => UserFactory::getAdmin()->_real()->getId()]),
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'linkedResource' => $this->findIriBy(Task::class, ['id' => $contract->getTask()->getId()]),
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN, 'You are not allowed to link a new file to the given record.');
        self::assertResponseHeaderSame('content-type', 'application/problem+json; charset=utf-8');
    }

    public function test_authorized_user_can_create_and_link_a_file_to_task_with_status_of_type_complete(): void
    {
        $legalUnit = LegalUnitFactory::createOne();
        $user = UserFactory::createOne([
            'units' => [$legalUnit],
            'authorizations' => new ArrayCollection([
                AuthorizationFactory::createOne(
                    [
                        'item' => AuthorizationItem::Contract->value,
                        'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                    ]
                )->_real(),
            ]),
        ]);
        $client = self::createClientWithAuth($user);

        $contract = ContractFactory::createOne(
            [
                'unit' => $legalUnit,
                'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_COMPLETE]),
            ]
        );

        // Ensure updated at is some days before so we can detect if it was updated
        $updatedAt = new \DateTime('-5 days');
        TestUtils::setProperty($contract->getTask(), 'updatedAt', $updatedAt);
        $contract->_save();
        self::assertSame($updatedAt->format(\DATE_ATOM), $contract->getTask()->getUpdatedAt()->format(\DATE_ATOM));

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'groupPermissions' => '[]',
                        'types' => '[]',
                        'userPermissions' => json_encode([
                            [
                                'user' => $this->findIriBy(User::class, ['id' => UserFactory::getAdmin()->getId()]),
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'linkedResource' => $this->findIriBy(Task::class, ['id' => $contract->getTask()->getId()]),
                    ],
                ],
            ]
        );

        // Then
        self::assertSame(StatusTypes::TYPE_COMPLETE, $contract->getTask()->getStatus()->getType());
        self::assertResponseStatusCodeSame(Response::HTTP_CREATED, 'You are not allowed to link a new file to the given record.');
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        // Ensure updatedAt has been updated
        $entityManager = self::getEntityManager();
        self::assertNotSame(
            $entityManager->find(Task::class, $contract->getTask()->getId())?->getUpdatedAt()?->getTimestamp(),
            $updatedAt->getTimestamp()
        );
    }

    public function test_unauthorized_user_cannot_create_and_link_a_file_to_section(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();

        $unauthorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($unauthorizedUser);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => FileFactory::new()->many(5)])->many(1),
            'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_COMPLETE]),
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'groupPermissions' => '[]',
                        'types' => '[]',
                        'userPermissions' => json_encode([
                            [
                                'user' => $this->findIriBy(User::class, ['id' => $adminUser->getId()]),
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'linkedResource' => $this->findIriBy(CountryByCountryReportSection::class, ['id' => $section->getId()]),
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN, 'You are not allowed to link a new file to the given record.');
        self::assertResponseHeaderSame('content-type', 'application/problem+json; charset=utf-8');
    }

    public function test_authorized_user_can_create_and_link_a_file_to_section_of_a_document_with_status_of_type_complete(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();

        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::UPDATE->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($authorizedUser);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => FileFactory::new()->many(5)])->many(1),
            'status' => StatusFactory::createOne(['type' => StatusTypes::TYPE_COMPLETE]),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        // When
        $uploadedFile = new UploadedFile(__DIR__ . '/../../../../behat/fixtures/test.txt', 'test.txt');

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/files',
            [
                'headers' => ['Content-Type' => 'multipart/form-data'],
                'extra' => [
                    'files' => [
                        'uploadedFile' => $uploadedFile,
                    ],
                    'parameters' => [
                        'accessType' => File::SMART_ACCESS,
                        'description' => 'My new file',
                        'groupPermissions' => '[]',
                        'types' => '[]',
                        'userPermissions' => json_encode([
                            [
                                'user' => $this->findIriBy(User::class, ['id' => $adminUser->getId()]),
                                'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                            ],
                        ], \JSON_THROW_ON_ERROR),
                        'linkedResource' => $this->findIriBy(CountryByCountryReportSection::class, ['id' => $section->getId()]),
                    ],
                ],
            ]
        );

        // Then
        /** @var CountryByCountryReport $structuredDocument */
        $structuredDocument = $section->getDocument();
        self::assertSame(StatusTypes::TYPE_COMPLETE, $structuredDocument->getTask()->getStatus()->getType());
        self::assertResponseStatusCodeSame(Response::HTTP_CREATED, 'You are not allowed to link a new file to the given record.');
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
    }
}
