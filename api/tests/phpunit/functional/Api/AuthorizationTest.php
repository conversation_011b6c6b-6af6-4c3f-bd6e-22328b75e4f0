<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\Authorization;
use U2\Entity\AuthorizationItem;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\Authorization
 */
class AuthorizationTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/authorizations',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        self::assertJsonContains([
            '@context' => '/api/contexts/Authorization',
            '@id' => '/api/authorizations',
            '@type' => 'hydra:Collection',
        ]);
    }

    public function test_get_collection_as_unauthorized(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/authorizations',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_item(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/authorizations/%s', $authorization->getId()),
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@id' => \sprintf('/api/authorizations/%s', $authorization->getId()),
            '@type' => 'Authorization',
            'name' => $authorization->getName(),
            'item' => $authorization->getItem(),
            'rights' => $authorization->getRights(),
        ]);
    }

    public function test_get_item_as_unauthorized(): void
    {
        // Given
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);
        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/authorizations/%s', $authorization->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_delete_item(): void
    {
        $adminUser = UserFactory::getAdmin();
        $client = self::createClientWithAuth($adminUser);

        // Given
        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        // When
        $id = $authorization->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(Authorization::class, ['id' => $id]),
            ['json' => []],
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_delete_item_unauthorized(): void
    {
        // Given
        $unauthorizedUser = UserFactory::new(['username' => 'user'])->create();
        $client = self::createClientWithAuth($unauthorizedUser);
        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        // When
        $id = $authorization->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(Authorization::class, ['id' => $id])
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_updating_a_authorization(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());
        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        $client->request(
            HttpOperation::METHOD_PATCH,
            '/api/authorizations/' . $authorization->getId(),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'My Authorization',
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ],
            ]
        );

        /** @var Authorization $authorization */
        $authorization = static::getContainer()->get(EntityManagerInterface::class)->getRepository(Authorization::class)->findOneBy([
            'name' => 'My Authorization',
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@id' => \sprintf('/api/authorizations/%s', $authorization->getId()),
            '@type' => 'Authorization',
            'name' => 'My Authorization',
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::SUPERVISE->value],
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_update_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);
        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorizations/%s', $authorization->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New Authorization Name',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_creating_a_authorization(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());
        $client->request(
            HttpOperation::METHOD_POST,
            '/api/authorizations',
            [
                'json' => [
                    'name' => 'My Authorization',
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ],
            ]
        );

        /** @var Authorization $authorization */
        $authorization = static::getContainer()->get(EntityManagerInterface::class)->getRepository(Authorization::class)->findOneBy([
            'name' => 'My Authorization',
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@id' => \sprintf('/api/authorizations/%s', $authorization->getId()),
            '@type' => 'Authorization',
            'name' => $authorization->getName(),
            'item' => $authorization->getItem(),
            'rights' => $authorization->getRights(),
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);
    }

    public function test_create_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/authorizations',
            [
                'json' => [
                    'name' => 'My Authorization',
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_assign_users_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorization = AuthorizationFactory::createOne([
            'directUsers' => UserFactory::new()->many(2),
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $id = $authorization->getId();
        $requestUrl = $this->findIriBy(Authorization::class, ['id' => $id]) . '/direct-users';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/User',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }

    public function test_get_assign_users_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // Given
        $authorization = AuthorizationFactory::createOne([
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $id = $authorization->getId();
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(Authorization::class, ['id' => $id]) . '/direct-users',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_inherited_users_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorization = AuthorizationFactory::createOne([
            'directUsers' => UserFactory::new()->many(4),
            'groups' => [UserGroupFactory::createOne(['users' => UserFactory::new()->many(2)])],
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $requestUrl = $this->findIriBy(Authorization::class, ['id' => $authorization->getId()]) . '/inherited-users';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/User',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }

    public function test_get_inherited_users_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        $authorization = AuthorizationFactory::createOne([
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(Authorization::class, ['id' => $authorization->getId()]) . '/inherited-users',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_assign_user_groups_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorization = AuthorizationFactory::createOne([
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
            'groups' => UserGroupFactory::new()->many(2),
        ]);

        // When
        $id = $authorization->getId();
        $requestUrl = $this->findIriBy(Authorization::class, ['id' => $id]) . '/groups';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/UserGroup',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }

    public function test_get_assign_user_groups_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // Given
        $authorization = AuthorizationFactory::createOne([
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $id = $authorization->getId();
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(Authorization::class, ['id' => $id]) . '/groups',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_update_direct_users(): void
    {
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);
        $client = self::createClientWithAuth($admin);

        $authorization = AuthorizationFactory::createOne([
            'directUsers' => UserFactory::new()->many(4),
            'groups' => [UserGroupFactory::createOne(['users' => UserFactory::new()->many(2)])],
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        self::assertCount(4, $authorization->getDirectUsers());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorizations/%s/direct-users', $authorization->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => array_map(static fn (User $user): string => '/api/users/' . $user->getId(), [...$authorization->getDirectUsers(), ...$authorization->getInheritedUsers()]),
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertCount(6, $authorization->getDirectUsers());
    }

    public function test_update_direct_users_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $authorization = AuthorizationFactory::createOne([
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorizations/%s/direct-users', $authorization->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => array_map(static fn (User $user): string => '/api/users/' . $user->getId(), [...$authorization->getDirectUsers(), ...$authorization->getInheritedUsers()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_update_groups(): void
    {
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);
        $client = self::createClientWithAuth($admin);

        $authorization = AuthorizationFactory::createOne([
            'groups' => [UserGroupFactory::createOne()],
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        self::assertCount(1, $authorization->getGroups());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorizations/%s/groups', $authorization->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groups' => array_map(static fn (UserGroup $userGroup): string => '/api/user-groups/' . $userGroup->getId(), [...$authorization->getGroups(), UserGroupFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertCount(2, $authorization->getGroups());
    }

    public function test_update_groups_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $authorization = AuthorizationFactory::createOne([
            'item' => AuthorizationItem::LocalFile->value,
            'rights' => [AuthorizationRight::READ->value],
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorizations/%s/groups', $authorization->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groups' => array_map(static fn (UserGroup $userGroup): string => '/api/user-groups/' . $userGroup->getId(), [...$authorization->getGroups(), UserGroupFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
