<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\FileFactory;
use U2\DataFixtures\Example\MasterFileFactory;
use U2\DataFixtures\Example\MasterFileSectionFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\DocumentSection;
use U2\Entity\File;
use U2\Entity\MasterFileSection;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\MasterFileSection
 */
class MasterFileSectionTest extends ApiTestCase
{
    public function test_get_attachments(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => FileFactory::new(['createdBy' => $authorizedUser, 'updatedBy' => $authorizedUser])->many(5)])->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            "/api/master-file-sections/{$section->getId()}/attachments",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertCount(5, $response->toArray()['hydra:member']);
    }

    public function test_get_attachments_with_an_unauthorized_user(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => FileFactory::new(['createdBy' => UserFactory::createOne()->_real(), 'updatedBy' => UserFactory::createOne()->_real()])->many(1)])->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            "/api/master-file-sections/{$section->getId()}/attachments",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_link_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'accessType' => File::PUBLIC_ACCESS,
                'createdBy' => UserFactory::new(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
            ]
        );

        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::UPDATE->value],
                ]
            ),
        ]])->_real();

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new()->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();
        $client = self::createClientWithAuth($authorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/master-file-sections/%d/attachments', $section->getId()),
            [
                'json' => [
                    'linkedResource' => $this->findIriBy(MasterFileSection::class, ['id' => $section->getId()]),
                    'file' => $this->findIriBy(File::class, ['id' => $file->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        static::getContainer()->get(EntityManagerInterface::class)->refresh($section);

        self::assertCount(1, $section->getFiles());
    }

    public function test_unauthorized_user_cannot_link_an_attachment(): void
    {
        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new()->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();
        $user = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            "/api/master-file-sections/{$section->getId()}/attachments",
            [
                'json' => [
                    'linkedResource' => $this->findIriBy(MasterFileSection::class, ['id' => $section->getId()]),
                    'file' => $this->findIriBy(File::class, ['id' => FileFactory::createOne(
                        [
                            'createdBy' => UserFactory::createOne()->_real(),
                            'path' => 'my-file.ext',
                            'description' => 'My awesome file',
                        ]
                    )->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);

        static::getContainer()->get(EntityManagerInterface::class)->refresh($section);

        self::assertCount(0, $section->getFiles());
    }

    public function test_unlink_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
            ]
        )->_real();

        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::UPDATE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => [$file]])->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $client->request(
            HttpOperation::METHOD_DELETE,
            \sprintf('/api/master-file-sections/%s/attachments/%s', $section->getId(), $file->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        static::getContainer()->get(EntityManagerInterface::class)->refresh($section);

        self::assertCount(0, $section->getFiles());
        self::assertCount(1, FileFactory::all());
    }

    public function test_unauthorized_user_cannot_unlink_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
                'accessType' => File::SMART_ACCESS,
            ]
        )->_real();

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => [$file]])->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $unauthorizedUser = UserFactory::createOne(['authorizations' => new ArrayCollection([
            AuthorizationFactory::createOne(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            )->_real(),
        ])])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_DELETE,
            \sprintf('/api/master-file-sections/%s/attachments/%s', $section->getId(), $file->getId()),
            ['json' => []]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);

        static::getContainer()->get(EntityManagerInterface::class)->refresh($section);

        self::assertCount(1, $section->getFiles());
    }

    public function test_download_an_attachment(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'updatedBy' => UserFactory::createOne()->_real(),
                'description' => 'My awesome file',
                'uploadedFile' => new UploadedFile(__FILE__, 'abc.txt'),
                'accessType' => File::SMART_ACCESS,
            ]
        )->_real();

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => [$file]])->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/master-file-sections/%s/attachments/%s/download', $section->getId(), $file->getId()),
            [
                'headers' => [
                    'Accept' => 'application/octet-stream',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/octet-stream');
    }

    public function test_unauthorized_user_cannot_download_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'updatedBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'access' => File::SMART_ACCESS,
                'description' => 'My awesome file',
            ]
        )->_real();

        $masterFile = MasterFileFactory::createOne([
            'sections' => MasterFileSectionFactory::new(['files' => [$file]])->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $masterFile->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $unauthorizedUser = UserFactory::createOne(['authorizations' => new ArrayCollection([
            AuthorizationFactory::createOne(
                [
                    'item' => AuthorizationItem::MasterFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            )->_real(),
        ])])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/master-file-sections/%s/attachments/%s/download', $section->getId(), $file->getId()),
            [
                'headers' => [
                    'Accept' => 'application/octet-stream',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
