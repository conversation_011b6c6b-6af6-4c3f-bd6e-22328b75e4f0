<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\AuthorizationProfileFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\Authorization;
use U2\Entity\AuthorizationItem;
use U2\Entity\AuthorizationProfile;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\AuthorizationProfile
 */
class AuthorizationProfileTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/authorization-profiles',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        self::assertJsonContains([
            '@context' => '/api/contexts/AuthorizationProfile',
            '@id' => '/api/authorization-profiles',
            '@type' => 'hydra:Collection',
        ]);
    }

    public function test_get_collection_as_unauthorized(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/authorization-profiles',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_item(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/authorization-profiles/%s', $authorizationProfile->getId()),
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@id' => \sprintf('/api/authorization-profiles/%s', $authorizationProfile->getId()),
            '@type' => 'AuthorizationProfile',
            'name' => $authorizationProfile->getName(),
            'authorizations' => $authorizationProfile->getAuthorizations(),
        ]);
    }

    public function test_get_item_as_unauthorized(): void
    {
        // Given
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);
        $authorizationProfile = AuthorizationProfileFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/authorization-profiles/%s', $authorizationProfile->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_delete_item(): void
    {
        $adminUser = UserFactory::getAdmin();
        $client = self::createClientWithAuth($adminUser);

        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne();

        // When
        $id = $authorizationProfile->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(Authorization::class, ['id' => $id]),
            ['json' => []],
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_delete_item_unauthorized(): void
    {
        // Given
        $unauthorizedUser = UserFactory::new(['username' => 'user'])->create();
        $client = self::createClientWithAuth($unauthorizedUser);
        $authorizationProfile = AuthorizationProfileFactory::createOne();

        // When
        $id = $authorizationProfile->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(Authorization::class, ['id' => $id]),
            ['json' => []],
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_updating_a_authorization(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );
        $newAuthorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );

        $authorizationProfile = AuthorizationProfileFactory::createOne(
            [
                'authorizations' => [$authorization],
            ]
        );

        $newAuthorizationIri = '/api/authorizations/' . $newAuthorization->getId();
        $client->request(
            HttpOperation::METHOD_PATCH,
            '/api/authorization-profiles/' . $authorizationProfile->getId(),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'My Authorization Profile',
                    'authorizations' => [$newAuthorizationIri],
                ],
            ]
        );

        /** @var AuthorizationProfile $authorizationProfile */
        $authorizationProfile = static::getContainer()->get(EntityManagerInterface::class)->getRepository(AuthorizationProfile::class)->findOneBy([
            'name' => 'My Authorization Profile',
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@id' => \sprintf('/api/authorization-profiles/%s', $authorizationProfile->getId()),
            '@type' => 'AuthorizationProfile',
            'name' => 'My Authorization Profile',
            'authorizations' => [$newAuthorizationIri],
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_update_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);
        $authorizationProfile = AuthorizationProfileFactory::createOne();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorization-profiles/%s', $authorizationProfile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New Authorization Profile Name',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_creating_a_authorization(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        $authorization = AuthorizationFactory::createOne(
            [
                'item' => AuthorizationItem::MasterFile->value,
                'rights' => [AuthorizationRight::READ->value],
            ]
        );
        $authorizationIri = '/api/authorizations/' . $authorization->getId();

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/authorization-profiles',
            [
                'json' => [
                    'name' => 'My Authorization Profile',
                    'authorizations' => [$authorizationIri],
                ],
            ]
        );

        /** @var AuthorizationProfile $authorizationProfile */
        $authorizationProfile = static::getContainer()->get(EntityManagerInterface::class)->getRepository(AuthorizationProfile::class)->findOneBy([
            'name' => 'My Authorization Profile',
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@id' => \sprintf('/api/authorization-profiles/%s', $authorizationProfile->getId()),
            '@type' => 'AuthorizationProfile',
            'name' => $authorizationProfile->getName(),
            'authorizations' => [
                $authorizationIri,
            ],
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);
    }

    public function test_create_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/authorization-profiles',
            [
                'json' => [
                    'name' => 'My Authorization Profile',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_assign_users_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'directUsers' => UserFactory::new()->many(2),
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $id = $authorizationProfile->getId();
        $requestUrl = $this->findIriBy(AuthorizationProfile::class, ['id' => $id]) . '/direct-users';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/User',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }

    public function test_get_assign_users_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());
        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $id = $authorizationProfile->getId();
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(AuthorizationProfile::class, ['id' => $id]) . '/direct-users',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_inherited_users_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'directUsers' => UserFactory::new()->many(4),
            'groups' => [UserGroupFactory::createOne(['users' => UserFactory::new()->many(2)])],
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $requestUrl = $this->findIriBy(AuthorizationProfile::class, ['id' => $authorizationProfile->getId()]) . '/inherited-users';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/User',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }

    public function test_get_inherited_users_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(AuthorizationProfile::class, ['id' => $authorizationProfile->getId()]) . '/inherited-users',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_assign_user_groups_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'groups' => UserGroupFactory::new()->many(2),
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $id = $authorizationProfile->getId();
        $requestUrl = $this->findIriBy(AuthorizationProfile::class, ['id' => $id]) . '/groups';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/UserGroup',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 2,
        ]);
    }

    public function test_get_assign_user_groups_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // Given
        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $id = $authorizationProfile->getId();
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(AuthorizationProfile::class, ['id' => $id]) . '/groups',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_update_direct_users(): void
    {
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);
        $client = self::createClientWithAuth($admin);

        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'directUsers' => UserFactory::new()->many(4),
            'groups' => [UserGroupFactory::createOne(['users' => UserFactory::new()->many(2)])],
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        self::assertCount(4, $authorizationProfile->getDirectUsers());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorization-profiles/%s/direct-users', $authorizationProfile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => array_map(
                        static fn (User $user): string => '/api/users/' . $user->getId(),
                        [...$authorizationProfile->getDirectUsers(), ...$authorizationProfile->getInheritedUsers()]
                    ),
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertCount(6, $authorizationProfile->getDirectUsers());
    }

    public function test_update_direct_users_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorization-profiles/%s/direct-users', $authorizationProfile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => array_map(static fn (User $user): string => '/api/users/' . $user->getId(), [...$authorizationProfile->getDirectUsers(), ...$authorizationProfile->getInheritedUsers()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_update_groups(): void
    {
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);
        $client = self::createClientWithAuth($admin);

        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'groups' => [UserGroupFactory::createOne()],
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        self::assertCount(1, $authorizationProfile->getGroups());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorization-profiles/%s/groups', $authorizationProfile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groups' => array_map(static fn (UserGroup $userGroup): string => '/api/user-groups/' . $userGroup->getId(), [...$authorizationProfile->getGroups(), UserGroupFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertCount(2, $authorizationProfile->getGroups());
    }

    public function test_update_groups_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $authorizationProfile = AuthorizationProfileFactory::createOne([
            'authorizations' => [AuthorizationFactory::createOne(['item' => AuthorizationItem::MasterFile->value, 'rights' => [AuthorizationRight::READ->value]])],
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/authorization-profiles/%s/groups', $authorizationProfile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groups' => array_map(static fn (UserGroup $userGroup): string => '/api/user-groups/' . $userGroup->getId(), [...$authorizationProfile->getGroups(), UserGroupFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
