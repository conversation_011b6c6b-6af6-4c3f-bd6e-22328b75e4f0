<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\UserGroup;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\UserGroup;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\UserGroup
 */
class UserGroupTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);
        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            '/api/user-groups'
        );

        // Then
        self::assertGreaterThan(0, $response->toArray()['hydra:member']);
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_get_item(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);
        $userGroup = UserGroupFactory::createOne(['name' => 'Test Group'])->_real();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/user-groups/%s', $userGroup->getId())
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEquals([
            '@context' => '/api/contexts/UserGroup',
            '@id' => \sprintf('/api/user-groups/%s', $userGroup->getId()),
            '@type' => 'UserGroup',
            'id' => $userGroup->getId(),
            'name' => $userGroup->getName(),
            'description' => $userGroup->getDescription(),
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_updating_a_user_group(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($admin);
        $userGroup = UserGroupFactory::new(['name' => 'Test Group'])->create();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/user-groups/%s', $userGroup->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New User Group Name',
                    'description' => 'New description',
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEquals([
            '@context' => '/api/contexts/UserGroup',
            '@id' => \sprintf('/api/user-groups/%s', $userGroup->getId()),
            '@type' => 'UserGroup',
            'id' => $userGroup->getId(),
            'name' => 'New User Group Name',
            'description' => 'New description',
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_update_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);
        $userGroup = UserGroupFactory::new(['name' => 'Test Group'])->create();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/user-groups/%s', $userGroup->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New User Group Name',
                    'description' => 'New description',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_creating_a_user_group(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($admin);

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/user-groups',
            [
                'json' => [
                    'name' => 'My User Group',
                    'description' => 'My User Group Description',
                ],
            ]
        );

        $entityManager = static::getContainer()->get(EntityManagerInterface::class);

        /** @var UserGroup $userGroup */
        $userGroup = $entityManager->getRepository(UserGroup::class)->findOneBy([
            'name' => 'My User Group',
            'description' => 'My User Group Description',
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEquals([
            '@context' => '/api/contexts/UserGroup',
            '@id' => \sprintf('/api/user-groups/%s', $userGroup->getId()),
            '@type' => 'UserGroup',
            'id' => $userGroup->getId(),
            'name' => 'My User Group',
            'description' => 'My User Group Description',
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);
    }

    public function test_create_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/user-groups',
            [
                'json' => [
                    'name' => 'My User Group',
                    'description' => 'My User Group Description',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_delete_item(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $authorizedUser = $admin;
        $client = self::createClientWithAuth($authorizedUser);

        $userGroup = UserGroupFactory::new(['name' => 'Test Group'])->create();

        // When
        $id = $userGroup->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(UserGroup::class, ['id' => $id])
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        self::assertNull($entityManager->find(UserGroup::class, $id));
    }

    public function test_delete_item_unauthorized(): void
    {
        $userGroup = UserGroupFactory::createOne(['name' => 'Test Group']);
        $unauthorizedUser = UserFactory::createOne(['username' => 'user']);
        $client = self::createClientWithAuth($unauthorizedUser);
        $id = $userGroup->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(UserGroup::class, ['id' => $id])
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        self::assertNotNull($entityManager->find(UserGroup::class, $id));
    }

    public function test_get_assign_users_as_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::getAdmin());

        // Given
        $userGroup = UserGroupFactory::createOne(['users' => UserFactory::createMany(4)]);

        // When
        $requestUrl = $this->findIriBy(UserGroup::class, ['id' => $userGroup->getId()]) . '/direct-users';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/User',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 4,
        ]);
    }

    public function test_get_assign_users_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // Given
        $userGroup = UserGroupFactory::createOne(['users' => UserFactory::createMany(4)]);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(UserGroup::class, ['id' => $userGroup->getId()]) . '/direct-users',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_update_direct_users(): void
    {
        $userGroup = UserGroupFactory::createOne();
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value, UserRoles::UserGroupAdmin->value]]);
        $client = self::createClientWithAuth($admin);

        $toAssignUser = UserFactory::createOne();
        self::assertCount(0, $userGroup->getUsers());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/user-groups/%s/direct-users', $userGroup->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => ['/api/users/' . $toAssignUser->getId()],
                ],
            ]
        );

        self::assertCount(1, $userGroup->getUsers());

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@context' => '/api/contexts/UserGroup',
            '@type' => 'hydra:Collection',
            '@id' => \sprintf('/api/user-groups/%s/direct-users', $userGroup->getId()),
        ]);
    }

    public function test_update_direct_users_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $userGroup = UserGroupFactory::createOne();

        self::assertCount(0, $userGroup->getUsers());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/user-groups/%s/direct-users', $userGroup->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userGroups' => ['/api/users/' . $notAnAdmin->getId()],
                ],
            ]
        );
        self::assertCount(0, $userGroup->getUsers());

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
