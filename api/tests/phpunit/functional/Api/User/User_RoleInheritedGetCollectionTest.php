<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\User;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\User
 */
class User_RoleInheritedGetCollectionTest extends ApiTestCase
{
    public function test_get_inherited_roles_for_myself(): void
    {
        // Given
        $user = UserFactory::createOne(['roles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($user);
        UserGroupFactory::createOne(['roles' => [UserRoles::Admin->value], 'users' => [$user->_real()]]);
        self::assertCount(1, $user->getUserRoles());
        self::assertCount(1, $user->getGroupRoles());
        self::assertCount(2, $user->getRoles());
        // When
        $requestUrl = $this->findIriBy(User::class, ['id' => $user->getId()]) . '/inherited-roles';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );
        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEquals([
            '@context' => '/api/contexts/Role',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/roles/ROLE_ADMIN',
                    '@type' => 'Role',
                    'name' => UserRoles::Admin->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_API',
                    '@type' => 'Role',
                    'name' => UserRoles::Api->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_USER_GROUP_ADMIN',
                    '@type' => 'Role',
                    'name' => UserRoles::UserGroupAdmin->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_PERIOD_MANAGER',
                    '@type' => 'Role',
                    'name' => UserRoles::PeriodManager->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_UNIT_MANAGER',
                    '@type' => 'Role',
                    'name' => UserRoles::UnitManager->value,
                ],
            ],
            'hydra:totalItems' => 5,
        ]);
    }

    public function test_get_inherited_roles_as_admin(): void
    {
        // Given
        $userGroupAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::UserGroupAdmin->value]])->_real();
        $client = self::createClientWithAuth($userGroupAdmin);

        $user = UserFactory::createOne(['roles' => [UserRoles::User->value]]);
        UserGroupFactory::createOne(['roles' => [UserRoles::Admin->value], 'users' => [$user->_real()]]);

        self::assertCount(1, $user->getUserRoles());
        self::assertCount(1, $user->getGroupRoles());
        self::assertCount(2, $user->getRoles());

        // When
        $requestUrl = $this->findIriBy(User::class, ['id' => $user->getId()]) . '/inherited-roles';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonEquals([
            '@context' => '/api/contexts/Role',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/roles/ROLE_ADMIN',
                    '@type' => 'Role',
                    'name' => UserRoles::Admin->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_API',
                    '@type' => 'Role',
                    'name' => UserRoles::Api->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_USER_GROUP_ADMIN',
                    '@type' => 'Role',
                    'name' => UserRoles::UserGroupAdmin->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_PERIOD_MANAGER',
                    '@type' => 'Role',
                    'name' => UserRoles::PeriodManager->value,
                ],
                [
                    '@id' => '/api/roles/ROLE_UNIT_MANAGER',
                    '@type' => 'Role',
                    'name' => UserRoles::UnitManager->value,
                ],
            ],
            'hydra:totalItems' => 5,
        ]);
    }

    public function test_get_inherited_roles_as_non_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            $this->findIriBy(User::class, ['id' => UserFactory::createOne()->getId()]) . '/inherited-roles',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
