<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bridge\PhpUnit\ClockMock;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\User;
use U2\Repository\UserRepository;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\User
 */
class UserGetItemTest extends ApiTestCase
{
    public function test_user_gets_another_user(): void
    {
        [$user, $user2] = UserFactory::createMany(2);
        $client = self::createClientWithAuth($user2);

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/users/%s', $user->getId())
        );
        $responseContent = json_decode($response->getContent());
        \assert($responseContent instanceof \stdClass);
        $contact = $responseContent->contact;
        \assert($contact instanceof \stdClass);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEqualsExcludeGeneratedIri($response, [
            '@context' => '/api/contexts/User',
            '@type' => 'User',
            '@id' => \sprintf('/api/users/%s', $user->getId()),
            'id' => $user->getId(),
            'contact' => [
                '@type' => 'Contact',
                'id' => $user->getContact()?->getId(),
                'nameFirst' => $user->getContact()?->getNameFirst(),
                'nameLast' => $user->getContact()?->getNameLast(),
                'email' => $user->getContact()?->getEmail(),
            ],
            'username' => $user->getUsername(),
        ]);
        self::assertFalse(property_exists($responseContent, 'authorizations'));
        self::assertFalse(property_exists($responseContent, 'roles'));

        self::assertMatchesResourceItemJsonSchema(resourceClass: User::class, serializationContext: ['groups' => ['user:read']]);
    }

    public function test_user_gets_his_own_user(): void
    {
        $user = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($user);

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/users/%s', $user->getId()),
        );

        $responseContent = json_decode($response->getContent(), false, 512, \JSON_THROW_ON_ERROR);
        \assert($responseContent instanceof \stdClass);
        $contact = $responseContent->contact;
        \assert($contact instanceof \stdClass);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEqualsExcludeGeneratedIri($response,
            [
                '@context' => '/api/contexts/User',
                '@id' => \sprintf('/api/users/%s', $user->getId()),
                '@type' => 'User',
                'id' => $user->getId(),
                'lastLogin' => null,
                'accountExpires' => null,
                'lastActivity' => null,
                'groups' => [],
                'contact' => [
                    '@type' => 'Contact',
                    'id' => $user->getContact()?->getId(),
                    'title' => null,
                    'nameFirst' => $user->getContact()?->getNameFirst(),
                    'company' => $user->getContact()?->getCompany(),
                    'nameLast' => $user->getContact()?->getNameLast(),
                    'email' => $user->getContact()?->getEmail(),
                    'telephone' => $user->getContact()?->getTelephone(),
                    'mobile' => null,
                    'fax' => null,
                    'address' => null,
                    'country' => null,
                ],
                'locked' => false,
                'parentUser' => null,
                'passwordExpires' => $user->getPasswordExpires()?->format('Y-m-d'),
                'roles' => [
                    0 => UserRoles::User->value,
                ],
                'userRoles' => [
                    0 => UserRoles::User->value,
                ],
                'username' => $user->getUsername(),
                'authorizations' => [
                    AuthorizationItem::CountryByCountryReport->value . ':' . AuthorizationRight::READ->value,
                    AuthorizationItem::LocalFile->value . ':' . AuthorizationRight::READ->value,
                ],
            ]
        );

        self::assertMatchesResourceItemJsonSchema(resourceClass: User::class, serializationContext: ['groups' => ['user:read']]);
    }

    public function test_user_and_group_admin_gets_a_user(): void
    {
        $userRepository = static::getContainer()->get(UserRepository::class);
        $anotherUser = UserFactory::createOne(['userRoles' => [UserRoles::Admin->value]])->_real();

        $admin = $userRepository->findOneBy(['username' => 'admin']);
        \assert(null !== $admin);
        $client = self::createClientWithAuth($admin);

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/users/%s', $anotherUser->getId())
        );

        $responseContent = json_decode($response->getContent());
        \assert($responseContent instanceof \stdClass);
        $contact = $responseContent->contact;
        \assert($contact instanceof \stdClass);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEqualsExcludeGeneratedIri($response,
            [
                '@context' => '/api/contexts/User',
                '@id' => \sprintf('/api/users/%s', $anotherUser->getId()),
                '@type' => 'User',
                'id' => $anotherUser->getId(),
                'lastLogin' => null,
                'accountExpires' => null,
                'contact' => [
                    '@type' => 'Contact',
                    'id' => $anotherUser->getContact()?->getId(),
                    'title' => null,
                    'nameFirst' => $anotherUser->getContact()?->getNameFirst(),
                    'company' => $anotherUser->getContact()?->getCompany(),
                    'nameLast' => $anotherUser->getContact()?->getNameLast(),
                    'email' => $anotherUser->getContact()?->getEmail(),
                    'telephone' => $anotherUser->getContact()?->getTelephone(),
                    'mobile' => null,
                    'fax' => null,
                    'address' => null,
                    'country' => null,
                ],
                'locked' => false,
                'parentUser' => null,
                'passwordExpires' => $anotherUser->getPasswordExpires()?->format('Y-m-d'),
                'groups' => [],
                'roles' => [
                    UserRoles::User->value,
                    UserRoles::Admin->value,
                    UserRoles::PeriodManager->value,
                    UserRoles::UnitManager->value,
                    UserRoles::Api->value,
                    UserRoles::UserGroupAdmin->value,
                ],
                'userRoles' => [
                    UserRoles::Admin->value,
                    UserRoles::User->value,
                ],
                'username' => $anotherUser->getUsername(),
                'lastActivity' => null,
            ]
        );

        self::assertMatchesResourceItemJsonSchema(resourceClass: User::class, serializationContext: ['groups' => ['user:read']]);
    }

    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }
}
