<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use ApiPlatform\Metadata\IriConverterInterface;
use Doctrine\ORM\EntityManagerInterface;
use Faker\Factory;
use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\AuditLog\Change\Change;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\Period;
use U2\Entity\PeriodLog;

/**
 * @covers \U2\Entity\Period
 */
class PeriodTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();
        $authorizedUser = $adminUser;
        $client = self::createClientWithAuth($authorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/periods'
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        self::assertJsonContains([
            '@context' => '/api/contexts/Period',
            '@id' => '/api/periods',
            '@type' => 'hydra:Collection',
            'hydra:member' => [],
        ]);
    }

    public function test_get_item(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();
        $authorizedUser = $adminUser;
        $client = self::createClientWithAuth($authorizedUser);

        // Given
        $previousPeriod = PeriodFactory::createOne();
        $period = PeriodFactory::createOne(['previousPeriod' => $previousPeriod, 'createdBy' => UserFactory::createOne()]);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/periods/%s', $period->getId())
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@context' => '/api/contexts/Period',
            '@id' => \sprintf('/api/periods/%s', $period->getId()),
            '@type' => 'Period',
            'id' => $period->getId(),
            'name' => $period->getName(),
            'description' => null,
            'closed' => $period->isClosed(),
            'startDate' => $period->getStartDate()->format('Y-m-d'),
            'endDate' => $period->getEndDate()->format('Y-m-d'),
            'previousPeriod' => static::getContainer()->get(IriConverterInterface::class)->getIriFromResource($previousPeriod->_real()),
        ]);

        // TODO: Re-enable when there is a fix for https://github.com/api-platform/core/issues/3896
        // self::assertMatchesResourceItemJsonSchema(Period::class);
    }

    public function test_delete_item(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();
        $authorizedUser = $adminUser;
        $client = self::createClientWithAuth($authorizedUser);

        // Given
        $period = PeriodFactory::createOne();

        // When
        $id = $period->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(Period::class, ['id' => $id])
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_delete_item_unauthorized(): void
    {
        // Given
        $unauthorizedUser = UserFactory::new(['username' => 'user'])->create();
        $client = self::createClientWithAuth($unauthorizedUser);
        $period = PeriodFactory::createOne();

        // When
        $id = $period->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(Period::class, ['id' => $id])
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_logs(): void
    {
        // Given
        $authorizedUser = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $period = PeriodFactory::createOne();

        $faker = Factory::create();

        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        /*
         * Create some logs with randomised creation times
         * so we can test that returned collection is sorted by the creation date (newest first).
         */
        $logs = [];
        for ($i = 1; $i <= 12; ++$i) {
            ClockMock::withClockMock((string) $faker->unixTime());

            $entityManager->persist(
                $periodLog = new PeriodLog(
                    [
                        new Change($faker->word(), $faker->word(), $faker->word()),
                    ],
                    $period->_real()
                )
            );

            $logs[] = $periodLog;
        }
        $entityManager->flush();

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/periods/%s/logs', $period->getId()),
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@context' => '/api/contexts/PeriodLog',
            '@id' => \sprintf('/api/periods/%s/logs', $period->getId()),
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 12,
            'hydra:view' => [
                '@id' => \sprintf('/api/periods/%s/logs?page=1', $period->getId()),
                '@type' => 'hydra:PartialCollectionView',
                'hydra:first' => \sprintf('/api/periods/%s/logs?page=1', $period->getId()),
                'hydra:last' => \sprintf('/api/periods/%s/logs?page=2', $period->getId()),
                'hydra:next' => \sprintf('/api/periods/%s/logs?page=2', $period->getId()),
            ],
        ]);
        self::assertCount(10, $response->toArray()['hydra:member']);
        self::assertMatchesResourceCollectionJsonSchema(PeriodLog::class);

        // Ensure the array is ordered by direction 'DESC' for timestamp
        usort(
            $logs,
            static fn (PeriodLog $logA, PeriodLog $logB): int => (int) $logB->getTimestamp()->format('U') - (int) $logA->getTimestamp()->format('U')
        );
        self::assertEquals(
            \array_slice(
                array_map(
                    static fn (PeriodLog $periodLog): string => $periodLog->getTimestamp()->format(\DATE_W3C),
                    $logs
                ),
                0,
                10
            ),
            array_map(
                static fn (array $member) => $member['timestamp'],
                $response->toArray()['hydra:member']
            )
        );
    }

    public function test_get_logs_denies_access_for_users_that_cannot_write_a_period(): void
    {
        // Given
        $unauthorizedUser = UserFactory::new(['username' => 'user'])->create();
        $client = self::createClientWithAuth($unauthorizedUser);
        $period = PeriodFactory::createOne();

        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist(
            new PeriodLog(
                [
                    new Change('test', 'Old Value', 'New Value'),
                ],
                $period->_real()
            )
        );
        $entityManager->flush();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/periods/%s/logs', $period->getId())
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_updating_a_period(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($admin);
        $period = PeriodFactory::createOne(['createdBy' => UserFactory::createOne()]);
        $previousPeriod = PeriodFactory::createOne(['createdBy' => UserFactory::createOne()]);
        $previousPeriodIri = static::getContainer()->get(IriConverterInterface::class)->getIriFromResource($previousPeriod->_real());

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/periods/%s', $period->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New Period Name',
                    'description' => 'New description',
                    'startDate' => '2020-01-01',
                    'endDate' => '2020-01-31',
                    'closed' => true,
                    'previousPeriod' => $previousPeriodIri,
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonContains([
            '@context' => '/api/contexts/Period',
            '@id' => \sprintf('/api/periods/%s', $period->getId()),
            'previousPeriod' => static::getContainer()->get(IriConverterInterface::class)->getIriFromResource($previousPeriod->_real()),
            'id' => $period->getId(),
            'startDate' => $period->getStartDate()->format('Y-m-d'),
            'endDate' => $period->getEndDate()->format('Y-m-d'),
            'name' => 'New Period Name',
            'description' => 'New description',
            '@type' => 'Period',
            'closed' => true,
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function test_update_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);
        $period = PeriodFactory::createOne();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/periods/%s', $period->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'name' => 'New Period Name',
                    'description' => 'New description',
                    'startDate' => '2020-01-01',
                    'endDate' => '2020-01-31',
                    'closed' => true,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_creating_a_period(): void
    {
        $user = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($user);
        $previousPeriod = PeriodFactory::createOne();
        $previousPeriodIri = static::getContainer()->get(IriConverterInterface::class)->getIriFromResource($previousPeriod);
        $client->request(
            HttpOperation::METHOD_POST,
            '/api/periods',
            [
                'json' => [
                    'name' => 'My Period',
                    'description' => 'My Period Description',
                    'startDate' => '2020-01-01',
                    'endDate' => '2020-01-31',
                    'closed' => true,
                    'previousPeriod' => $previousPeriodIri,
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        $period = PeriodFactory::find([
            'name' => 'My Period',
            'description' => 'My Period Description',
        ]);

        self::assertJsonContains([
            '@context' => '/api/contexts/Period',
            '@id' => \sprintf('/api/periods/%s', $period->getId()),
            '@type' => 'Period',
            'id' => $period->getId(),
            'name' => $period->getName(),
            'description' => 'My Period Description',
            'closed' => $period->isClosed(),
            'startDate' => $period->getStartDate()->format('Y-m-d'),
            'endDate' => $period->getEndDate()->format('Y-m-d'),
            'previousPeriod' => $previousPeriodIri,
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);
    }

    public function test_create_item_as_an_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne(['username' => 'user'])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_POST,
            '/api/periods',
            [
                'json' => [
                    'name' => 'My Period',
                    'description' => 'My Period Description',
                    'startDate' => '2020-01-01',
                    'endDate' => '2020-01-31',
                    'closed' => true,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }
}
