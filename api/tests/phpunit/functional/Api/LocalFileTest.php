<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\LocalFileFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Dto\Permission\GroupPermissionInput;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\AuthorizationItem;
use U2\Entity\GroupPermission;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Entity\UserPermission;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\Task\TaskType\LocalFile
 */
class LocalFileTest extends ApiTestCase
{
    public function test_get_user_permissions(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
            'userPermissions' => [
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
            ],
        ]);

        // One more permission for the createdBy user of the local file
        self::assertCount(4, $localFile->getPermissions()->getUserPermissions());

        $user = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($user);

        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        /** @var UserPermission $userPermission */
        $userPermission = $localFile->getPermissions()->getUserPermissions()->first();
        self::assertJsonContains([
            '@context' => '/api/contexts/UserPermission',
            '@id' => \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 4,
            'hydra:member' => [
                [
                    '@id' => '/api/user-permissions/' . $userPermission->getId(),
                    '@type' => 'UserPermission',
                    'id' => $userPermission->getId(),
                    'user' => '/api/users/' . $userPermission->getUser()->getId(),
                    'mask' => 1,
                ],
            ],
        ]);
    }

    public function test_update_user_permissions(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
        ]);

        self::assertCount(1, $localFile->getPermissions()->getUserPermissions());

        $user = UserFactory::getAdmin()->_real();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $user->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                    ],
                ],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        // Then
        $userPermissions = $localFile->getPermissions()->getUserPermissions();
        self::assertCount(1, $userPermissions);
        /** @var UserPermission $userPermission */
        $userPermission = $userPermissions->first();
        self::assertSame($user, $userPermission->getUser());
    }

    public function test_update_user_permissions_with_two_permission_referencing_the_same_user_fails(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
        ]);

        self::assertCount(1, $localFile->getPermissions()->getUserPermissions());

        $user = UserFactory::getAdmin()->_real();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $user->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $user->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                    ],
                ],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function test_at_least_one_owner_is_set(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
        ]);

        self::assertCount(1, $localFile->getPermissions()->getUserPermissions());

        UserFactory::getAdmin()->_real();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [],
                ],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function test_update_user_permissions_for_a_document_in_a_closed_period(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
            'period' => PeriodFactory::createOne(['closed' => true]),
        ]);

        self::assertCount(1, $localFile->getPermissions()->getUserPermissions());

        $user = UserFactory::getAdmin()->_real();

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $user->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                    ],
                ],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        // Then
        $userPermissions = $localFile->getPermissions()->getUserPermissions();
        self::assertCount(1, $userPermissions);
        /** @var UserPermission $userPermission */
        $userPermission = $userPermissions->first();
        self::assertSame($user, $userPermission->getUser());
    }

    public function test_update_user_permissions_as_unauthorized_user(): void
    {
        $someOtherUser = UserFactory::createOne();
        $unauthorizedUser = UserFactory::createOne()->_real();

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
        ]);
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/user-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $someOtherUser->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_group_permissions(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
            'groupPermissions' => [
                new GroupPermissionInput(UserGroupFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new GroupPermissionInput(UserGroupFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new GroupPermissionInput(UserGroupFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
            ],
        ]);

        self::assertCount(3, $localFile->getPermissions()->getGroupPermissions());

        $user = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($user);

        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/local-files/%s/group-permissions', $localFile->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        /** @var GroupPermission $groupPermission */
        $groupPermission = $localFile->getPermissions()->getGroupPermissions()->first();

        self::assertJsonContains([
            '@context' => '/api/contexts/GroupPermission',
            '@id' => \sprintf('/api/local-files/%s/group-permissions', $localFile->getId()),
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 3,
            'hydra:member' => [
                [
                    '@id' => '/api/group-permissions/' . $groupPermission->getId(),
                    '@type' => 'GroupPermission',
                    'id' => $groupPermission->getId(),
                    'group' => '/api/user-groups/' . $groupPermission->getGroup()->getId(),
                    'mask' => 1,
                ],
            ],
        ]);
    }

    public function test_update_group_permissions(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
        ]);

        self::assertCount(0, $localFile->getPermissions()->getGroupPermissions());
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/group-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groupPermissions' => [
                        [
                            'group' => $this->findIriBy(UserGroup::class, ['id' => UserGroupFactory::createOne()->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW,
                        ],
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertCount(1, $localFile->getPermissions()->getGroupPermissions());
    }

    public function test_update_group_permissions_for_a_document_in_a_closed_period(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::LocalFile->value,
                    'rights' => [AuthorizationRight::SUPERVISE->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
            'createdBy' => $authorizedUser,
            'period' => PeriodFactory::createOne(['closed' => true]),
        ]);

        self::assertCount(0, $localFile->getPermissions()->getGroupPermissions());
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/group-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groupPermissions' => [
                        [
                            'group' => $this->findIriBy(UserGroup::class, ['id' => UserGroupFactory::createOne()->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW,
                        ],
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertCount(1, $localFile->getPermissions()->getGroupPermissions());
    }

    public function test_update_group_permissions_as_unauthorized_user(): void
    {
        $unauthorizedUser = UserFactory::createOne()->_real();

        $localFile = LocalFileFactory::createOne([
            'status' => StatusFactory::first(),
        ]);

        $userGroup = UserGroupFactory::createOne();
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/local-files/%s/group-permissions', $localFile->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groupPermissions' => [
                        [
                            'group' => $this->findIriBy(UserGroup::class, ['id' => $userGroup->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW,
                        ],
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
