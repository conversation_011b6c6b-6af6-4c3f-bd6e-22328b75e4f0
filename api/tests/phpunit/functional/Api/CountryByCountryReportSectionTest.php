<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\CountryByCountryReportFactory;
use U2\DataFixtures\Example\CountryByCountryReportSectionFactory;
use U2\DataFixtures\Example\FileFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\CountryByCountryReportSection;
use U2\Entity\DocumentSection;
use U2\Entity\File;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\CountryByCountryReportSection
 */
class CountryByCountryReportSectionTest extends ApiTestCase
{
    public function test_get_attachments(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($authorizedUser);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => FileFactory::new(['createdBy' => $authorizedUser, 'updatedBy' => $authorizedUser])->many(5)])->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            "/api/country-by-country-report-sections/{$section->getId()}/attachments",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertCount(5, $response->toArray()['hydra:member']);
    }

    public function test_get_attachments_with_an_unauthorized_user(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => FileFactory::new(['createdBy' => UserFactory::createOne(), 'updatedBy' => UserFactory::createOne()])->many(1)])->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            "/api/country-by-country-report-sections/{$section->getId()}/attachments",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_link_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'accessType' => File::PUBLIC_ACCESS,
                'createdBy' => UserFactory::new(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
            ]
        );

        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::UPDATE->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($authorizedUser);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new()->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/country-by-country-report-sections/%d/attachments', $section->getId()),
            [
                'json' => [
                    'linkedResource' => $this->findIriBy(CountryByCountryReportSection::class, ['id' => $section->getId()]),
                    'file' => $this->findIriBy(File::class, ['id' => $file->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        self::assertCount(1, $section->getFiles());
    }

    public function test_unauthorized_user_cannot_link_an_attachment(): void
    {
        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new()->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();
        $user = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]])->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            "/api/country-by-country-report-sections/{$section->getId()}/attachments",
            [
                'json' => [
                    'file' => $this->findIriBy(File::class, ['id' => FileFactory::createOne(
                        [
                            'createdBy' => UserFactory::createOne()->_real(),
                            'path' => 'my-file.ext',
                            'description' => 'My awesome file',
                        ]
                    )->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        self::assertCount(0, $section->getFiles());
    }

    public function test_unlink_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
            ]
        )->_real();

        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::UPDATE->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($authorizedUser);

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => [$file]])->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $client->request(
            HttpOperation::METHOD_DELETE,
            \sprintf('/api/country-by-country-report-sections/%s/attachments/%s', $section->getId(), $file->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertCount(0, $section->getFiles());
        self::assertCount(1, FileFactory::all());
    }

    public function test_unauthorized_user_cannot_unlink_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
                'accessType' => File::PROTECTED_ACCESS,
            ]
        )->_real();

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => [$file]])->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $unauthorizedUser = UserFactory::createOne(['authorizations' => new ArrayCollection([
            AuthorizationFactory::createOne(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            )->_real(),
        ])]);
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_DELETE,
            \sprintf('/api/country-by-country-report-sections/%s/attachments/%s', $section->getId(), $file->getId()),
            ['json' => []]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);

        self::assertCount(1, $section->getFiles());
    }

    public function test_download_an_attachment(): void
    {
        $authorizedUser = UserFactory::createOne(['authorizations' => [
            AuthorizationFactory::new(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            ),
        ]]);
        $client = self::createClientWithAuth($authorizedUser);

        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'updatedBy' => UserFactory::createOne()->_real(),
                'description' => 'My awesome file',
                'access' => File::SMART_ACCESS,
                'uploadedFile' => new UploadedFile(__FILE__, 'abc.txt'),
            ]
        )->_real();

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => [$file]])->many(1),
            'createdBy' => $authorizedUser,
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/country-by-country-report-sections/%s/attachments/%s/download', $section->getId(), $file->getId()),
            [
                'headers' => [
                    'Accept' => 'application/octet-stream',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/octet-stream');
    }

    public function test_unauthorized_user_cannot_download_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'updatedBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'access' => File::SMART_ACCESS,
                'description' => 'My awesome file',
            ]
        )->_real();

        $countryByCountryReport = CountryByCountryReportFactory::createOne([
            'sections' => CountryByCountryReportSectionFactory::new(['files' => [$file]])->many(1),
        ]);

        /** @var DocumentSection $section */
        $section = $countryByCountryReport->getSections()->first();

        self::assertCount(1, $section->getFiles());

        $unauthorizedUser = UserFactory::createOne(['authorizations' => new ArrayCollection([
            AuthorizationFactory::createOne(
                [
                    'item' => AuthorizationItem::CountryByCountryReport->value,
                    'rights' => [AuthorizationRight::READ->value],
                ]
            )->_real(),
        ])])->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/country-by-country-report-sections/%s/attachments/%s/download', $section->getId(), $file->getId()),
            [
                'headers' => [
                    'Accept' => 'application/octet-stream',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
