<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use ApiPlatform\Symfony\Bundle\Test\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\PermanentEstablishmentFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Security\UserRoles;
use U2\Serializer\UnitExportNormalizer;
use U2\Util\SpreadsheetFactory;

/**
 * @covers \U2\Entity\Unit
 */
class UnitExportTest extends ApiTestCase
{
    public function test_export_all_units_for_a_user(): void
    {
        UnitFactory::createMany(10);
        PermanentEstablishmentFactory::createMany(5);
        $user = UserFactory::createOne(['username' => 'user', 'units' => [
            ...UnitFactory::createMany(35),
            ...PermanentEstablishmentFactory::createMany(4),
        ]]);
        $client = self::createClientWithAuth($user);

        // When
        $response = $client->request(HttpOperation::METHOD_GET, '/api/units', [
            'query' => [
                'pagination' => false,
            ],
            'headers' => [
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ],
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8');
        [$headerRow, $data] = $this->extractExcelData($response);
        self::assertSame(UnitExportNormalizer::headers, $headerRow, 'The header row does not have the right headers');
        self::assertCount(39, $data);
    }

    public function test_export_filtered_units_for_a_user(): void
    {
        UnitFactory::createMany(10);
        PermanentEstablishmentFactory::createMany(5);
        $user = UserFactory::createOne(['username' => 'user', 'units' => [
            ...UnitFactory::createMany(35),
            ...PermanentEstablishmentFactory::createMany(2, ['name' => 'MatchFalse']),
            ...PermanentEstablishmentFactory::createMany(2, ['name' => 'MatchTrue']),
        ]]);
        $client = self::createClientWithAuth($user);

        // When
        $response = $client->request(HttpOperation::METHOD_GET, '/api/units?page=1&itemsPerPage=20&search=MatchTrue&type=permanentestablishment', [
            'query' => [
                'pagination' => false,
            ],
            'headers' => [
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ],
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8');
        [$headerRow, $data] = $this->extractExcelData($response);
        self::assertSame(UnitExportNormalizer::headers, $headerRow, 'The header row does not have the right headers');
        self::assertCount(2, $data, 'Only the units named "MatchTrue" should be found');
    }

    public function test_export_filtered_units_with_no_results_for_a_user(): void
    {
        UnitFactory::createMany(10);
        PermanentEstablishmentFactory::createMany(5);
        $user = UserFactory::createOne(['username' => 'user', 'units' => [
            ...UnitFactory::createMany(35),
            ...PermanentEstablishmentFactory::createMany(2, ['name' => 'MatchFalse']),
            ...PermanentEstablishmentFactory::createMany(2, ['name' => 'MatchTrue']),
        ]]);
        $client = self::createClientWithAuth($user);

        // When
        $response = $client->request(HttpOperation::METHOD_GET, '/api/units?search=MatchNone', [
            'query' => [
                'pagination' => false,
            ],
            'headers' => [
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ],
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8');
        [$headerRow, $data] = $this->extractExcelData($response);
        self::assertSame(UnitExportNormalizer::headers, $headerRow, 'The header row does not have the right headers');
        self::assertCount(0, $data, 'There should be 0 data records');
    }

    public function test_export_all_units_for_a_unit_manager(): void
    {
        UnitFactory::createMany(10);
        $user = UserFactory::createOne([
            'username' => 'user',
            'units' => [
                ...UnitFactory::createMany(35),
            ],
            'userRoles' => [UserRoles::UnitManager->value],
        ]);
        $client = self::createClientWithAuth($user);

        // When
        $response = $client->request(HttpOperation::METHOD_GET, '/api/units', [
            'query' => [
                'pagination' => false,
            ],
            'headers' => [
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ],
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8');
        [$headerRow, $data] = $this->extractExcelData($response);
        self::assertSame(UnitExportNormalizer::headers, $headerRow, 'The header row does not have the right headers');
        self::assertCount(45, $data, 'All units should be found');
    }

    public function test_export_paginated_units_for_a_unit_manager(): void
    {
        UnitFactory::createMany(10);
        $user = UserFactory::createOne([
            'username' => 'user',
            'units' => [
                ...UnitFactory::createMany(35),
            ],
            'userRoles' => [UserRoles::UnitManager->value],
        ]);
        $client = self::createClientWithAuth($user);

        // When
        $response = $client->request(HttpOperation::METHOD_GET, '/api/units', [
            'query' => [
                'pagination' => true,
            ],
            'headers' => [
                'Accept' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ],
        ]);

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8');
        [$headerRow, $data] = $this->extractExcelData($response);
        self::assertSame(UnitExportNormalizer::headers, $headerRow, 'The header row does not have the right headers');
        self::assertCount(30, $data, 'Only the first page of units should be found');
    }

    /**
     * Get an array that contains the header row as the first value and
     * the rest of the row data as the second value.
     *
     * @return array{array<int, string>, array<int, array<int, string>>}
     */
    protected function extractExcelData(ResponseInterface $response): array
    {
        \assert($response instanceof Response);
        $browserKitResponse = $response->getBrowserKitResponse();
        $tmp = tmpfile();
        \assert(false !== $tmp);
        fwrite($tmp, $browserKitResponse->getContent());
        $reader = SpreadsheetFactory::createReader('Xlsx');
        $file_location = stream_get_meta_data($tmp)['uri'];
        $spreadsheet = $reader->load($file_location);
        fclose($tmp);

        $sheet = $spreadsheet->getSheet($spreadsheet->getFirstSheetIndex());
        $data = $sheet->toArray();
        $headerRow = array_shift($data);

        return [$headerRow, $data];
    }
}
