<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\DocumentTemplate;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DocumentTemplateFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\DocumentTemplate;

/**
 * @covers \U2\Entity\DocumentTemplate
 */
class DocumentTemplateDeleteItemTest extends ApiTestCase
{
    public function test_delete_item(): void
    {
        // Given
        $authorizedUser = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $documentTemplate = DocumentTemplateFactory::createOne();

        // When
        $id = $documentTemplate->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(DocumentTemplate::class, ['id' => $id]),
            ['json' => []],
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertNull($documentTemplate->_repository()->find($id));
    }

    public function test_delete_item_unauthorized(): void
    {
        // Given
        $documentTemplate = DocumentTemplateFactory::createOne();
        $unauthorizedUser = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_DELETE,
            (string) $this->findIriBy(DocumentTemplate::class, ['id' => $documentTemplate->getId()]),
            ['json' => []],
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        self::assertNotNull($documentTemplate->_repository()->find($documentTemplate->getId()));
    }
}
