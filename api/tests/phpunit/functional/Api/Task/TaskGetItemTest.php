<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\DatasheetCollectionFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\UnitPeriodFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\Task\CheckState;
use U2\Entity\Task\Task;
use U2\Entity\Workflow\Binding;
use U2\Entity\Workflow\Check;
use U2\Entity\Workflow\Status;
use U2\Entity\Workflow\Workflow;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\Task\Task
 */
class TaskGetItemTest extends ApiTestCase
{
    public function test_get_item_as_admin_without_rights(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($admin);

        $contract = ContractFactory::createOne([
            'unit' => LegalUnitFactory::createOne(),
            'name' => 'Contract name',
        ]);

        $iri = $this->findIriBy(Task::class, ['id' => $contract->getTask()->getId()]);
        \assert(null !== $iri);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            $iri,
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    public function test_get_item_as_user_with_rights(): void
    {
        $legalUnit = LegalUnitFactory::createOne();
        $unitPeriod = UnitPeriodFactory::createOne([
            'unit' => $legalUnit,
            'layoutCollections' => [
                DatasheetCollectionFactory::createOne(),
            ],
        ]);

        $authorizedUser = UserFactory::createOne([
            'authorizations' => [
                AuthorizationFactory::new([
                    'item' => AuthorizationItem::UnitPeriod->value,
                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                ]),
            ],
            'units' => [$legalUnit],
        ]);
        $client = self::createClientWithAuth($authorizedUser->_real());

        // Given
        $workflowWithBinding = new Workflow();
        $workflowWithBinding->setName('name');
        $workflowWithBinding->setInitialStatus(null);
        $workflowWithBinding->setManualReviewEnabled(true);

        $check = new Check($workflowWithBinding, 'Item_title', true);

        $assignedWorkflowBinding = new Binding('test_issue', $workflowWithBinding);

        $workflowWithBinding->setBindings(new ArrayCollection([$assignedWorkflowBinding]));

        $checkState = new CheckState($unitPeriod->getTask(), $check, true);
        $checkState->setCreatedBy($authorizedUser->_real());

        $unitPeriod->getTask()->addCheckState($checkState);

        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($check);
        $entityManager->persist($workflowWithBinding);
        $entityManager->persist($assignedWorkflowBinding);
        $entityManager->persist($checkState);
        $entityManager->flush();

        $iri = $this->findIriBy(Task::class, ['id' => $unitPeriod->getTask()->getId()]);
        \assert(null !== $iri);

        // When
        $response = $client->request(
            HttpOperation::METHOD_GET,
            $iri,
        );

        $status = $unitPeriod->getStatus();

        /** @var array{"u2:extra": array{"@id": string}} $responseContent */
        $responseContent = json_decode($response->getContent(), true);
        // Then
        self::assertResponseIsSuccessful();
        self::assertJsonEquals([
            '@context' => '/api/contexts/Task',
            '@id' => $iri,
            '@type' => 'Task',
            'id' => $unitPeriod->getTask()->getId()->toRfc4122(),
            'reviews' => [],
            'watchers' => [],
            'assignee' => null,
            'layoutCollectionCount' => 1,
            'u2:extra' => [
                '@type' => 'TaskExtra',
                '@id' => $responseContent['u2:extra']['@id'],
                'canAddReview' => true,
                'canAttach' => true,
                'canDelete' => false,
                'canViewConfiguration' => false,
                'canEditConfiguration' => false,
                'canViewDocumentPermissions' => false,
                'canRemoveReview' => false,
                'canViewDocument' => false,
                'canWrite' => true,
                'deletePath' => '/api/tasktype/unit-period/' . $unitPeriod->getId() . '/delete',
                'documentBaseTemplate' => null,
                'duplicatePath' => '/legacy/task/' . $unitPeriod->getTask()->getId()->toRfc4122() . '/duplicate',
                'editDocumentPath' => null,
                'editFormPath' => '/legacy/tasktype/unit-period/' . $unitPeriod->getId() . '/edit/form',
                'editPath' => '/tax-accounting/unit-period/' . $unitPeriod->getId() . '/edit',
                'hasDocument' => false,
                'hasMultipleOptionsForNew' => false,
                'hasUserReviewed' => false,
                'isImported' => false,
                'isUserWatching' => false,
                'listPath' => '/tax-accounting/unit-period',
                'name' => $unitPeriod->getName(),
                'displayName' => $unitPeriod->getDisplayName(),
                'newPath' => '/tax-accounting/unit-period/new',
                'pdfPath' => '/legacy/tasktype/unit-period/' . $unitPeriod->getId() . '/pdf-download',
                'readableTaskType' => 'Datasheet Monitor',
                'reporterId' => null,
                'shortName' => 'unit-period',
                'sourceId' => null,
                'sourcePath' => null,
                'submitEditFormPath' => '/legacy/tasktype/unit-period/' . $unitPeriod->getId() . '/edit/form',
                'taskTypeId' => $unitPeriod->getId(),
                'transactionType' => null,
                'unwatchPath' => '/legacy/unit-period/' . $unitPeriod->getId() . '/un-watch',
                'updatedAt' => $unitPeriod->getUpdatedAt()?->format(\DATE_W3C),
                'updatedBy' => null,
                'createdAt' => $unitPeriod->getCreatedAt()?->format(\DATE_W3C),
                'createdBy' => null,
                'watchPath' => '/legacy/unit-period/' . $unitPeriod->getId() . '/watch',
                'xmlPath' => '/legacy/tasktype/unit-period/' . $unitPeriod->getId() . '/export/xml',
            ],
            'taskType' => $unitPeriod::getWorkflowBindingId(),
            'unit' => '/api/legal-units/' . $unitPeriod->getUnit()?->getId(),
            'period' => '/api/periods/' . $unitPeriod->getPeriod()?->getId(),
            'status' => $this->findIriBy(Status::class, ['id' => $status->getId()]),
            'type' => $unitPeriod->getTask()->getType(),
            'dueDate' => $unitPeriod->getTask()->getDueDate()?->format(\DATE_W3C),
        ]);
    }
}
