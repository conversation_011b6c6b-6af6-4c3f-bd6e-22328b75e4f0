<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use Tests\U2\TestUtils;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\Task\Task;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\Task\Task
 */
class TaskAssignUserTest extends ApiTestCase
{
    public function test_assign_a_user(): void
    {
        $legalUnit = LegalUnitFactory::createOne()->_real();
        $contract = ContractFactory::createOne([
            'unit' => $legalUnit,
        ]);

        $authorizedUser = UserFactory::createOne([
            'authorizations' => [
                AuthorizationFactory::new(
                    [
                        'item' => AuthorizationItem::Contract->value,
                        'rights' => [AuthorizationRight::READ->value, AuthorizationRight::ASSIGN->value, AuthorizationRight::UPDATE->value],
                    ]
                ),
            ],
            'units' => [$legalUnit],
        ]);
        $client = self::createClientWithAuth($authorizedUser);

        self::assertNull($contract->getTask()->getAssignee());

        // Ensure updated at is some days before so we can detect if it was updated
        $updatedAt = new \DateTime('-5 days');
        TestUtils::setProperty($contract->getTask(), 'updatedAt', $updatedAt);
        $contract->_save();
        self::assertSame($updatedAt->format(\DATE_ATOM), $contract->getTask()->getUpdatedAt()->format(\DATE_ATOM));

        // When
        $assigneeIri = \sprintf('/api/users/%s', $authorizedUser->getId());
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/tasks/%s/assign-user', $contract->getTask()->getId()->toRfc4122()),
            [
                'json' => [
                    'assignee' => $assigneeIri,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        static::getContainer()->get(EntityManagerInterface::class)->refresh($contract->getTask());
        self::assertSame($authorizedUser->_real(), $contract->getTask()->getAssignee());

        // Ensure updatedAt has been updated
        $entityManager = self::getEntityManager();
        self::assertNotSame(
            $entityManager->find(Task::class, $contract->getTask()->getId())?->getUpdatedAt()?->getTimestamp(),
            $updatedAt->getTimestamp()
        );
    }

    public function test_unauthorized_user_cannot_assign_a_user(): void
    {
        $contract = ContractFactory::createOne(
            [
                'unit' => LegalUnitFactory::createOne()->_real(),
            ]
        );

        $authorizedUser = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::Contract->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
            ]
        )->_real();
        $client = self::createClientWithAuth($authorizedUser);

        self::assertNull($contract->getTask()->getAssignee());

        $assignUserIri = \sprintf('/api/tasks/%s/assign-user', $contract->getTask()->getId()->toRfc4122());

        // When
        $assigneeIri = \sprintf('/api/users/%s', $authorizedUser->getId());
        $client->request(
            HttpOperation::METHOD_POST,
            $assignUserIri,
            [
                'json' => [
                    'assignee' => $assigneeIri,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        static::getContainer()->get(EntityManagerInterface::class)->refresh($contract->getTask());
        self::assertNull($contract->getTask()->getAssignee());
    }

    public function test_assign_a_user_unauthorized_user_fails(): void
    {
        $legalUnit = LegalUnitFactory::createOne()->_real();
        $contract = ContractFactory::createOne(
            [
                'unit' => $legalUnit,
            ]
        );

        $user = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::Contract->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::ASSIGN->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
                'units' => [$legalUnit],
            ]
        )->_real();
        $client = self::createClientWithAuth($user);

        self::assertNull($contract->getTask()->getAssignee());

        $assignUserIri = \sprintf('/api/tasks/%s/assign-user', $contract->getTask()->getId()->toRfc4122());

        // When
        $assigneeIri = \sprintf('/api/users/%s', UserFactory::createOne()->getId());
        $client->request(
            HttpOperation::METHOD_POST,
            $assignUserIri,
            [
                'json' => [
                    'assignee' => $assigneeIri,
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
