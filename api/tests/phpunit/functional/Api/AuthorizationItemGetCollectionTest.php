<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Repository\UserRepository;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Api\Resource\AuthorizationItem
 */
class AuthorizationItemGetCollectionTest extends ApiTestCase
{
    public function test_get_authorization_item(): void
    {
        // When
        $client = self::createClient();
        $client->loginUser(static::getContainer()->get(UserRepository::class)->findByUsername('admin'));

        $client->request(
            HttpOperation::METHOD_GET,
            '/api/authorization-items',
        );

        // Then
        self::assertJsonContains(
            [
                '@context' => '/api/contexts/AuthorizationItem',
                '@id' => '/api/authorization-items',
                '@type' => 'hydra:Collection',
                'hydra:member' => [
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::ApmTransaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Contract->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Igt1Transaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Igt2Transaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Igt3Transaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Igt4Transaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Igt5Transaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::IncomeTaxPlanning->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::LossCarryForward->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxAssessmentStatus->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxAuditRisk->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxConsultingFee->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxCredit->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxLitigation->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxRate->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxRelevantRestriction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TransferPricing->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::OtherDeadline->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxAssessmentMonitor->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxAuthorityAuditObjection->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::TaxFilingMonitor->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::CountryByCountryReport->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::SUPERVISE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::FinancialData->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::LocalFile->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::SUPERVISE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::MainBusinessActivity->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::MasterFile->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::SUPERVISE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::Transaction->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                    [
                        '@type' => 'AuthorizationItem',
                        'id' => AuthorizationItem::UnitPeriod->value,
                        'rights' => [
                            AuthorizationRight::CREATE->value,
                            AuthorizationRight::READ->value,
                            AuthorizationRight::UPDATE->value,
                            AuthorizationRight::DELETE->value,
                            AuthorizationRight::ASSIGN->value,
                        ],
                    ],
                ],
            ]
        );
    }

    public function test_get_authorization_item_as_unauthorized(): void
    {
        $client = self::createClient();
        $client->loginUser(UserFactory::createOne()->_real());

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/authorization-items',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
