<?php

declare(strict_types=1);
namespace Tests\Migration\U2;

use U2\Migrations\Version20250602085137;
use U2\Migrations\Version20250602085138;

class Version20250602085138Test extends MigrationTestCase
{
    public function test_up(): void
    {
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250602085137::class]);

        $entityManager = self::getEntityManager();
        $connection = $entityManager->getConnection();

        $connection->executeQuery(<<<SQL
                insert into authorization (id, name, item, rights) values (1337, "test", "TPM_COUNTRY_BY_COUNTRY_REPORT", "[\\"ACCESS\\", \\"UPDATE\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1338, "test", "TPM_LOCAL_FILE", "[\\"UPDATE\\", \\"ACCESS\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1339, "test", "TPM_MASTER_FILE", "[\\"UPDATE\\", \\"DELETE\\", \\"ACCESS\\"]");
                insert into authorization (id, name, item, rights) values (1340, "test", "TPM_COUNTRY_BY_COUNTRY_REPORT", "[\\"UPDATE\\", \\"ACCESS\\"]");
                insert into authorization (id, name, item, rights) values (1341, "test", "TPM_LOCAL_FILE", "[\\"UPDATE\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1342, "test", "TPM_MASTER_FILE", "[\\"UPDATE\\"]");
                insert into authorization (id, name, item, rights) values (1343, "test", "CM_CONTRACT", "[\\"UPDATE\\"]");
                insert into authorization (id, name, item, rights) values (1344, "test", "TPM_MAIN_BUSINESS_ACTIVITY", "[\\"UPDATE\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1345, "test", "UNIT_PERIOD", "[\\"UPDATE\\", \\"DELETE\\", \\"ACCESS\\"]");
                insert into authorization (id, name, item, rights) values (1346, "test", "TPM_COUNTRY_BY_COUNTRY_REPORT", "[\\"ACCESS\\"]");
            SQL
        );

        $authorization = $connection->executeQuery('select id, item, rights from authorization where id in (1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345, 1346);')->fetchAllAssociative();
        self::assertSame([
            [
                'id' => 1337,
                'item' => 'TPM_COUNTRY_BY_COUNTRY_REPORT',
                'rights' => '["ACCESS", "UPDATE", "DELETE"]',
            ],
            [
                'id' => 1338,
                'item' => 'TPM_LOCAL_FILE',
                'rights' => '["UPDATE", "ACCESS", "DELETE"]',
            ],
            [
                'id' => 1339,
                'item' => 'TPM_MASTER_FILE',
                'rights' => '["UPDATE", "DELETE", "ACCESS"]',
            ],
            [
                'id' => 1340,
                'item' => 'TPM_COUNTRY_BY_COUNTRY_REPORT',
                'rights' => '["UPDATE", "ACCESS"]',
            ],
            [
                'id' => 1341,
                'item' => 'TPM_LOCAL_FILE',
                'rights' => '["UPDATE", "DELETE"]',
            ],
            [
                'id' => 1342,
                'item' => 'TPM_MASTER_FILE',
                'rights' => '["UPDATE"]',
            ],
            [
                'id' => 1343,
                'item' => 'CM_CONTRACT',
                'rights' => '["UPDATE"]',
            ],
            [
                'id' => 1344,
                'item' => 'TPM_MAIN_BUSINESS_ACTIVITY',
                'rights' => '["UPDATE", "DELETE"]',
            ],
            [
                'id' => 1345,
                'item' => 'UNIT_PERIOD',
                'rights' => '["UPDATE", "DELETE", "ACCESS"]',
            ],
            [
                'id' => 1346,
                'item' => 'TPM_COUNTRY_BY_COUNTRY_REPORT',
                'rights' => '["ACCESS"]',
            ],
        ], $authorization);

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250602085138::class]);

        // Then - Check authorization
        $authorization = $connection->executeQuery('select id, item, rights from authorization where id in (1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345, 1346);')->fetchAllAssociative();
        self::assertSame([
            [
                'id' => 1337,
                'item' => 'TPM_COUNTRY_BY_COUNTRY_REPORT',
                'rights' => '["ACCESS", "UPDATE", "DELETE", "READ"]',
            ],
            [
                'id' => 1338,
                'item' => 'TPM_LOCAL_FILE',
                'rights' => '["UPDATE", "ACCESS", "DELETE", "READ"]',
            ],
            [
                'id' => 1339,
                'item' => 'TPM_MASTER_FILE',
                'rights' => '["UPDATE", "DELETE", "ACCESS", "READ"]',
            ],
            [
                'id' => 1340,
                'item' => 'TPM_COUNTRY_BY_COUNTRY_REPORT',
                'rights' => '["UPDATE", "ACCESS", "READ", "DELETE"]',
            ],
            [
                'id' => 1341,
                'item' => 'TPM_LOCAL_FILE',
                'rights' => '["UPDATE", "DELETE"]',
            ],
            [
                'id' => 1342,
                'item' => 'TPM_MASTER_FILE',
                'rights' => '["UPDATE"]',
            ],
            [
                'id' => 1343,
                'item' => 'CM_CONTRACT',
                'rights' => '["UPDATE"]',
            ],
            [
                'id' => 1344,
                'item' => 'TPM_MAIN_BUSINESS_ACTIVITY',
                'rights' => '["UPDATE", "DELETE"]',
            ],
            [
                'id' => 1345,
                'item' => 'UNIT_PERIOD',
                'rights' => '["UPDATE", "DELETE", "ACCESS"]',
            ],
            [
                'id' => 1346,
                'item' => 'TPM_COUNTRY_BY_COUNTRY_REPORT',
                'rights' => '["ACCESS", "READ", "UPDATE", "DELETE"]',
            ],
        ], $authorization);
    }
}
