<?php

declare(strict_types=1);
namespace Tests\Migration\U2;

use U2\Migrations\Version20250512152033;
use U2\Migrations\Version20250512152034;

class Version20250512152034Test extends MigrationTestCase
{
    public function test_up(): void
    {
        // Given
        $entityManager = self::getEntityManager();
        $connection = $entityManager->getConnection();

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250512152033::class]);
        $connection->executeQuery(" UPDATE tpm_master_file SET due_date = '2019-08-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tpm_local_file SET due_date = '2018-07-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tpm_country_by_country_report SET due_date = '2017-06-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tcm_other_deadline SET due_date = '2016-05-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tcm_tax_assessment_monitor SET due_date = '2015-04-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tcm_tax_authority_audit_objection SET due_date = '2014-03-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tcm_tax_filing_monitor SET due_date = '2013-02-12' WHERE id = 1");
        $connection->executeQuery(" UPDATE tpm_transaction SET due_date = '2012-01-12' WHERE id = 1");

        $connection->executeQuery(" UPDATE tpm_master_file SET due_date = '2012-01-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tpm_local_file SET due_date = '2013-02-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tpm_country_by_country_report SET due_date = '2014-03-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tcm_other_deadline SET due_date = '2015-04-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tcm_tax_assessment_monitor SET due_date = '2016-05-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tcm_tax_authority_audit_objection SET due_date = '2017-06-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tcm_tax_filing_monitor SET due_date = '2018-07-12' WHERE id = 2");
        $connection->executeQuery(" UPDATE tpm_transaction SET due_date = '2019-08-12' WHERE id = 2");

        // Then
        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_master_file.due_date as taskTypeDueDate FROM task t JOIN tpm_master_file ON t.id = tpm_master_file.task_id WHERE tpm_master_file.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2019-08-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_local_file.due_date as taskTypeDueDate FROM task t JOIN tpm_local_file ON t.id = tpm_local_file.task_id WHERE tpm_local_file.id = 1')->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2018-07-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_country_by_country_report.due_date as taskTypeDueDate FROM task t JOIN tpm_country_by_country_report ON t.id = tpm_country_by_country_report.task_id WHERE tpm_country_by_country_report.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2017-06-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_other_deadline.due_date as taskTypeDueDate FROM task t JOIN tcm_other_deadline ON t.id = tcm_other_deadline.task_id WHERE tcm_other_deadline.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2016-05-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_assessment_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_assessment_monitor ON t.id = tcm_tax_assessment_monitor.task_id WHERE tcm_tax_assessment_monitor.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2015-04-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_authority_audit_objection.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_authority_audit_objection ON t.id = tcm_tax_authority_audit_objection.task_id WHERE tcm_tax_authority_audit_objection.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2014-03-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_filing_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_filing_monitor ON t.id = tcm_tax_filing_monitor.task_id WHERE tcm_tax_filing_monitor.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2013-02-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_transaction.due_date as taskTypeDueDate FROM task t JOIN tpm_transaction ON t.id = tpm_transaction.task_id WHERE tpm_transaction.id = 1')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2012-01-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_master_file.due_date as taskTypeDueDate FROM task t JOIN tpm_master_file ON t.id = tpm_master_file.task_id WHERE tpm_master_file.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2012-01-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_local_file.due_date as taskTypeDueDate FROM task t JOIN tpm_local_file ON t.id = tpm_local_file.task_id WHERE tpm_local_file.id = 2')->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2013-02-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_country_by_country_report.due_date as taskTypeDueDate FROM task t JOIN tpm_country_by_country_report ON t.id = tpm_country_by_country_report.task_id WHERE tpm_country_by_country_report.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2014-03-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_other_deadline.due_date as taskTypeDueDate FROM task t JOIN tcm_other_deadline ON t.id = tcm_other_deadline.task_id WHERE tcm_other_deadline.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2015-04-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_assessment_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_assessment_monitor ON t.id = tcm_tax_assessment_monitor.task_id WHERE tcm_tax_assessment_monitor.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2016-05-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_authority_audit_objection.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_authority_audit_objection ON t.id = tcm_tax_authority_audit_objection.task_id WHERE tcm_tax_authority_audit_objection.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2017-06-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_filing_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_filing_monitor ON t.id = tcm_tax_filing_monitor.task_id WHERE tcm_tax_filing_monitor.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2018-07-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_transaction.due_date as taskTypeDueDate FROM task t JOIN tpm_transaction ON t.id = tpm_transaction.task_id WHERE tpm_transaction.id = 2')
            ->fetchAssociative();
        self::assertNull($dueDates['taskDueDate']);
        self::assertSame('2019-08-12', $dueDates['taskTypeDueDate']);

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250512152034::class]);

        // Then

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_master_file.due_date as taskTypeDueDate FROM task t JOIN tpm_master_file ON t.id = tpm_master_file.task_id WHERE tpm_master_file.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2019-08-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_local_file.due_date as taskTypeDueDate FROM task t JOIN tpm_local_file ON t.id = tpm_local_file.task_id WHERE tpm_local_file.id = 1')->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2018-07-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_country_by_country_report.due_date as taskTypeDueDate FROM task t JOIN tpm_country_by_country_report ON t.id = tpm_country_by_country_report.task_id WHERE tpm_country_by_country_report.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2017-06-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_other_deadline.due_date as taskTypeDueDate FROM task t JOIN tcm_other_deadline ON t.id = tcm_other_deadline.task_id WHERE tcm_other_deadline.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2016-05-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_assessment_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_assessment_monitor ON t.id = tcm_tax_assessment_monitor.task_id WHERE tcm_tax_assessment_monitor.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2015-04-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_authority_audit_objection.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_authority_audit_objection ON t.id = tcm_tax_authority_audit_objection.task_id WHERE tcm_tax_authority_audit_objection.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2014-03-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_filing_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_filing_monitor ON t.id = tcm_tax_filing_monitor.task_id WHERE tcm_tax_filing_monitor.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2013-02-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_transaction.due_date as taskTypeDueDate FROM task t JOIN tpm_transaction ON t.id = tpm_transaction.task_id WHERE tpm_transaction.id = 1')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2012-01-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_master_file.due_date as taskTypeDueDate FROM task t JOIN tpm_master_file ON t.id = tpm_master_file.task_id WHERE tpm_master_file.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2012-01-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_local_file.due_date as taskTypeDueDate FROM task t JOIN tpm_local_file ON t.id = tpm_local_file.task_id WHERE tpm_local_file.id = 2')->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2013-02-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_country_by_country_report.due_date as taskTypeDueDate FROM task t JOIN tpm_country_by_country_report ON t.id = tpm_country_by_country_report.task_id WHERE tpm_country_by_country_report.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2014-03-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_other_deadline.due_date as taskTypeDueDate FROM task t JOIN tcm_other_deadline ON t.id = tcm_other_deadline.task_id WHERE tcm_other_deadline.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2015-04-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_assessment_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_assessment_monitor ON t.id = tcm_tax_assessment_monitor.task_id WHERE tcm_tax_assessment_monitor.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2016-05-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_authority_audit_objection.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_authority_audit_objection ON t.id = tcm_tax_authority_audit_objection.task_id WHERE tcm_tax_authority_audit_objection.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2017-06-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tcm_tax_filing_monitor.due_date as taskTypeDueDate FROM task t JOIN tcm_tax_filing_monitor ON t.id = tcm_tax_filing_monitor.task_id WHERE tcm_tax_filing_monitor.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2018-07-12', $dueDates['taskTypeDueDate']);

        /** @var array{taskDueDate: string|null, taskTypeDueDate: string|null} $dueDates */
        $dueDates = $connection->executeQuery('SELECT t.due_date as taskDueDate, tpm_transaction.due_date as taskTypeDueDate FROM task t JOIN tpm_transaction ON t.id = tpm_transaction.task_id WHERE tpm_transaction.id = 2')
            ->fetchAssociative();
        self::assertSame($dueDates['taskDueDate'], $dueDates['taskTypeDueDate']);
        self::assertSame('2019-08-12', $dueDates['taskTypeDueDate']);
    }
}
