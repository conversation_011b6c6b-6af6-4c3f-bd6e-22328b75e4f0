<?php

declare(strict_types=1);
namespace Tests\Migration\U2;

use U2\Migrations\Version20250602085138;
use U2\Migrations\Version20250602085139;

class Version20250602085139Test extends MigrationTestCase
{
    public function test_up(): void
    {
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250602085138::class]);

        $entityManager = self::getEntityManager();
        $connection = $entityManager->getConnection();

        $connection->executeQuery(<<<SQL
                insert into authorization (id, name, item, rights) values (1337, "test", "TPM_LOCAL_FILE", "[\\"UPDATE\\"]");
                insert into authorization (id, name, item, rights) values (1338, "test", "TPM_MASTER_FILE", "[\\"READ\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1339, "test", "CM_CONTRACT", "[\\"READ\\"]");
                insert into authorization (id, name, item, rights) values (1340, "test", "TAM_INCOME_TAX_PLANNING", "[\\"DELETE\\"]");
            SQL
        );

        $authorization = $connection->executeQuery('select id, item, rights from authorization where id in (1337, 1338, 1339, 1340);')->fetchAllAssociative();
        self::assertSame([
            [
                'id' => 1337,
                'item' => 'TPM_LOCAL_FILE',
                'rights' => '["UPDATE"]',
            ],
            [
                'id' => 1338,
                'item' => 'TPM_MASTER_FILE',
                'rights' => '["READ", "DELETE"]',
            ],
            [
                'id' => 1339,
                'item' => 'CM_CONTRACT',
                'rights' => '["READ"]',
            ],
            [
                'id' => 1340,
                'item' => 'TAM_INCOME_TAX_PLANNING',
                'rights' => '["DELETE"]',
            ],
        ], $authorization);

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250602085139::class]);

        // Then - Check authorization
        $authorization = $connection->executeQuery('select id, item, rights from authorization where id in (1337, 1338, 1339, 1340);')->fetchAllAssociative();
        self::assertSame([
            [
                'id' => 1337,
                'item' => 'TPM_LOCAL_FILE',
                'rights' => '["UPDATE", "ASSIGN"]',
            ],
            [
                'id' => 1338,
                'item' => 'TPM_MASTER_FILE',
                'rights' => '["READ", "DELETE", "ASSIGN"]',
            ],
            [
                'id' => 1339,
                'item' => 'CM_CONTRACT',
                'rights' => '["READ", "ASSIGN"]',
            ],
            [
                'id' => 1340,
                'item' => 'TAM_INCOME_TAX_PLANNING',
                'rights' => '["DELETE"]',
            ],
        ], $authorization);
    }
}
