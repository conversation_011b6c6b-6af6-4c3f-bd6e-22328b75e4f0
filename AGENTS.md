# AI Agent Guidelines for Universal Units (U²)

Welcome, 🤖 AI assistant! Please follow these guidelines when contributing to this repository:

## Project Overview

Universal Units (U²) is a sophisticated web-based application for business process management, tracking, and documentation. 
The application features a modular architecture supporting various business domains.

- This is a full-stack application with separate API and client codebases
- Multi-tenant architecture with configurable tenant support
- Enterprise-grade security with JWT authentication and role-based access control
- Containerized development environment using Docker

## Architecture & Technology Stack

### Backend (API)
- **Framework**: Symfony 7.3 with API Platform 4.x for REST API
- **Language**: PHP 8.4+ with strict typing (`declare(strict_types=1)` required)
- **Database**: MySQL 8 with Doctrine ORM and migrations
- **Authentication**: JWT with Lexik JWT Authentication Bundle
- **Testing**: PHPUnit 12.x for unit/integration/functional tests, Behat for E2E
- **Namespace**: `U2\` (PSR-4 autoloading)
- **Code Quality**: PHP CS Fixer, PHPStan, Rector for automated refactoring

### Frontend (Client)
- **Framework**: Vue 3 with Composition API and `<script lang="ts" setup>` syntax
- **Language**: TypeScript in strict mode (no `any` types allowed)
- **Build Tool**: Vite 6.x for fast development and optimized production builds
- **Styling**: Tailwind CSS 3.x (utility-first approach)
- **State Management**: Pinia for reactive state management
- **HTTP Client**: Axios with Vue Query (@tanstack/vue-query) for data fetching
- **Testing**: Vitest with Vue Testing Library, MSW for API mocking
- **Package Manager**: pnpm (required - enforced by preinstall script)

### Development Environment
- **Containerization**: Docker with docker-compose for all services
- **Entry Point**: `./develop` script for all development commands
- **SSL**: Self-signed certificates for HTTPS development at u2.localhost
- **Hot Reload**: Vite dev server with HMR for frontend development
- **Multi-tenancy**: Configurable tenant support

## Development Workflow

### Initial Setup
```bash
# Generate certificates and initialize project
make certs
make init

# Start development environment
./develop up

# Setup database with demo data
./develop console u2:setup --demo --no-interaction

# Access application at https://[tenant].u2.localhost (eg https://dev.u2.localhost)
```

### Daily Development Commands
```bash
# Start/stop services
./develop up -d                    # Start all services in background
./develop down                     # Stop all services

# Backend development
./develop console <command>        # Run Symfony console commands
./develop composer <command>       # Run Composer commands
./develop phpunit                  # Run PHP tests

# Frontend development  
./develop pnpm <command>           # Run pnpm commands
./develop pnpm vitest             # Run frontend tests
./develop pnpm run dev-server     # Start Vite dev server

# Database operations
./develop console doctrine:migrations:migrate
make reset                        # Reset database with demo data
```

## Coding Standards & Best Practices

### PHP Guidelines
- **Always** use `declare(strict_types=1)` at the top of every PHP file
- Follow Symfony coding standards enforced by PHP CS Fixer configuration
- Use strict type hints for all parameters and return types
- Follow PSR-4 autoloading with `U2\` namespace
- Use snake_case for test method names (`php_unit_method_casing`)
- Prefer composition to inheritance
- Use Doctrine entities with proper attributes (not annotations)
- Prefer php attributes to annotations
- Apply these specific Symfony standards:
  - Use Yoda conditions for comparisons (`if (null === $value)`)
  - Always use strict (`===`) comparisons, never loose (`==`)
  - Use braces `{}` in all control structures, even one-liners
  - Add blank line before `return` unless it's the only line in the block
  - Use trailing commas in multi-line arrays
  - Exception messages start with capital letter, end with dot, no backticks

### TypeScript/Vue Guidelines
- **Strict TypeScript**: No `any` types allowed - use proper type definitions
- **Vue 3 Composition API**: Always use `<script lang="ts" setup>` syntax
- **Component Structure**: Single File Components with proper prop typing`
- **Imports**: Use type-only imports where appropriate (`import type`)
- **Naming**: PascalCase for components, camelCase for variables/functions
- **State Management**: Use Pinia stores with proper TypeScript typing
- **API Calls**: Use API helpers (client/src/api/) to interact with the backend and Vue Query for data fetching with proper error handling

### CSS/Styling Guidelines
- **Primary**: Use Tailwind CSS utilities whenever possible
- **Custom CSS**: Only when Tailwind utilities are insufficient
- **Nesting**: Use CSS nesting for better organization
- **Properties**: Alphabetical ordering enforced by Stylelint
- **Responsive**: Mobile-first approach with Tailwind responsive prefixes

## Testing Strategy

### PHP Testing (./develop phpunit)
- **Unit Tests**: `tests/phpunit/unit/` - Test individual classes/methods in isolation
- **Integration Tests**: `tests/phpunit/integration/` - Test service interactions and dependencies
- **Functional Tests**: `tests/phpunit/functional/` - Test HTTP endpoints and API responses
- **Migration Tests**: `tests/phpunit/migration/` - Test database schema changes

### Frontend Testing (./develop pnpm vitest)
- **Unit/Component Tests**: Vitest with Vue Testing Library for component behavior
- **Mock Service Worker**: MSW for API mocking in tests
- **Test Utilities**: Custom helpers in `client/tests/` directory
- **Coverage**: Comprehensive test coverage expected for new features

### E2E Testing (./develop behat)
- **Behat**: Gherkin scenarios for complete user workflows
- **Selenium**: Chromium browser automation with VNC debugging
- **Suites**: Modular test suites organized by business domain
- **Environment**: Requires `APP_ENV=behat` to run

## Code Quality Tools

### PHP Static Analysis
```bash
# Fix code style issues
./develop composer php-cs-fixer

# Run static analysis
./develop composer phpstan

# Update PHPStan baseline (only when absolutely necessary)
./develop composer phpstan-baseline

# Preview Rector refactoring suggestions
./develop composer rector-preview
```

### Frontend Linting & Formatting
```bash
# Fix all frontend code quality issues
./develop pnpm run eslint:fix      # Fix JavaScript/TypeScript issues
./develop pnpm run stylelint:fix   # Fix CSS issues  
./develop pnpm run prettier:fix    # Fix formatting

# Type checking
./develop pnpm run type-check      # Verify TypeScript types
```

### Comprehensive Quality Check
```bash
make static-analysis              # Run all static analysis tools
make static-analysis-api         # API-only static analysis
make static-analysis-client      # Client-only static analysis
```

## API Development Guidelines

### API Platform Best Practices
- Use API Platform attributes for resource configuration
- Implement proper serialization groups for data exposure control
- Add comprehensive OpenAPI documentation with examples
- Use Hydra for standardized collection responses
- Implement proper filtering, pagination, and sorting
- Follow RESTful conventions for endpoint design

### Authentication & Security
- JWT tokens required for all API endpoints (except public ones)
- Use `./develop console lexik:jwt:generate-token <username>` for testing
- API documentation available at `/api/docs` in development environment
- Implement proper input validation using Symfony Validator
- Use Doctrine ORM properly to prevent SQL injection
- Use CSRF protection for forms

## Database & Doctrine Guidelines

### Entity Development
- Use Doctrine attributes (not annotations) for entity configuration
- Implement proper entity relationships with cascade options
- Use Gedmo extensions for timestampable/blameable behavior
- Follow naming conventions: PascalCase for entities, snake_case for tables
- Add proper indexes for performance-critical queries

### Migration Management
```bash
# Generate migrations after entity changes
./develop console doctrine:migrations:diff

# Apply migrations
./develop console doctrine:migrations:migrate

# Always review generated migrations before committing
```

## Module Architecture

The application these main business domains:
- **Tasks**: Tasks with workflows that have types and can be searched and tracked
- **Datasheets**: Spreadsheet like tables for data entry and reporting, with a complex formula system.  
- **Structured documents**: Documents with "sections" of structured content that can be rendered as PDFs 

## Security Best Practices

- **Input Validation**: Use Symfony Validator for all user inputs
- **SQL Injection Prevention**: Always use Doctrine ORM/DBAL properly
- **XSS Prevention**: Twig auto-escaping enabled, sanitize user content
- **CSRF Protection**: Implemented for all forms
- **Authentication**: JWT with proper expiration and refresh tokens
- **Authorization**: Role-based access control with proper permission checks
- **File Uploads**: Validate file types and sizes, scan for malware
- **Sensitive Data**: Never log passwords, tokens, or personal information

## Performance Guidelines

- **Database**: Use proper indexing, avoid N+1 queries, use Doctrine query optimization
- **Frontend**: Implement lazy loading for routes and components. 
- **Caching**: Use Symfony cache for expensive operations
- **Assets**: Vite handles bundling and optimization automatically
- **API**: Implement proper pagination and filtering for large datasets

## Important Notes for AI Agents

1. **Always use the `./develop` script** instead of direct docker commands
3. **Follow existing patterns** - study similar implementations before creating new features
4. **Test thoroughly** - run appropriate test suites after making changes
5. **Use proper typing** - both PHP and TypeScript require strict typing
6. **Consider multi-tenancy** - some features may need tenant-aware implementations
7. **Check feature flags** - functionality may be behind feature toggles in `config/packages/flagception.yaml`
8. **Use package managers properly** - `./develop composer` for PHP, `./develop pnpm` for Node.js
9. **Maintain backward compatibility** - avoid breaking changes without proper migration paths
10. **Document complex logic** - add comments for non-obvious business logic
11. Keep this document up to date when making changes to the project structure or guidelines

## Common Pitfalls to Avoid

- Don't use `any` type in TypeScript - always provide proper type definitions
- Don't bypass the `./develop` script - it handles environment configuration
- Don't modify vendor files directly - use patches in `api/patches/` directory
- Don't commit without running tests - use git hooks for quality checks
- Don't hardcode configuration - use environment variables and feature flags
- Don't ignore PHPStan errors - fix them or update baseline only when necessary

---

Thanks for contributing to Universal Units! 🙇‍♂️
