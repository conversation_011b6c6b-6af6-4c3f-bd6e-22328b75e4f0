<script setup lang="ts">
import { computed, ref, watch, watchEffect } from 'vue'
import { taskTypeApi } from '@js/api/taskTypeApi'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import AppDatePicker from '@js/components/form/AppDatePicker.vue'
import AppInputText from '@js/components/form/AppInputText.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppSelect from '@js/components/form/AppSelect.vue'
import FormLabel from '@js/components/form/FormLabel.vue'
import HelpPanelToggler from '@js/components/help/HelpPanelToggler.vue'
import Url from '@js/components/legacy/table/components/url'
import SvgIcon from '@js/components/SvgIcon.vue'
import UqlFieldSelector from '@js/components/table/UqlFieldSelector.vue'
import UqlHelp from '@js/components/table/UqlHelp.vue'
import UqlInput from '@js/components/table/UqlInput.vue'
import { defaultFieldNames, isFilterElement, useTaskListStore } from '@js/stores/task-list'
import Translator from '@js/translator'
import Cookie from '@js/utilities/cookie'
import type { TableField } from '@js/api/taskTypeApi'
import type { SelectOption } from '@js/types'
import type { Mode } from '@js/stores/task-list'

const booleanChoices = [
  { id: '1', name: Translator.trans('u2_table.true') },
  { id: '0', name: Translator.trans('u2_table.false') },
]

const taskListStore = useTaskListStore()

const defaultFields = computed(() => {
  return taskListStore.fields.filter((field) => defaultFieldNames.includes(field.uniqueName))
})

const selectedFields = ref(new Set<TableField>())
const fieldValues = ref<Record<TableField['uniqueName'], string>>({})
watch(
  () => taskListStore.advancedFilterState.filters.elements,
  (elements) => {
    fieldValues.value = Object.fromEntries(
      elements?.filter(isFilterElement).map(({ field, value }) => [field, value]) || []
    )
    const fieldValueNames = Object.keys(fieldValues.value)
    selectedFields.value = new Set(
      taskListStore.fields
        .filter((field) => !defaultFieldNames.includes(field.uniqueName))
        .filter((field) => fieldValueNames.includes(field.uniqueName))
    )
  },
  { deep: true, immediate: true }
)

const activeFields = computed(() => {
  return [...defaultFields.value, ...selectedFields.value]
})

async function resolveUql() {
  if (taskListStore.queryIsTooComplex) {
    return
  }
  const filter = {
    elements: activeFields.value
      .map((field) => {
        return {
          field: field.uniqueName,
          method: field.type.default,
          value: fieldValues.value[field.uniqueName],
        }
      })
      .filter((element) => element.value),
    logic: 'AND',
  } as const

  const { data } = await taskTypeApi.fetchTaskTableUql(filter)
  taskListStore.advancedFilterState.filters = filter
  taskListStore.advancedFilterState.uql = data.uql
}

function reset() {
  taskListStore.pagination = { itemsPerPage: undefined, currentPage: 0 }
  taskListStore.selectedRecordIds = []
  taskListStore.sort = []
  taskListStore.advancedFilterState.uql = ''
  taskListStore.savedFilterId = undefined
  fieldValues.value = {}
  submit()
}
async function submit() {
  // reset current page to return the first page of search results
  taskListStore.pagination.currentPage = 0
  if (mode.value === 'basic') {
    await resolveUql()
  }
  taskListStore.legacyTableSubmit(Url.build())
}
function buildOptions(choices: Record<string, string>): Array<SelectOption> {
  return Object.keys(choices).map((value) => ({ id: value, name: value, disabled: false }))
}

const previouslyUsedFilterMode = Cookie.read(taskListStore.name + '.mode') as Mode
const mode = ref<Mode>(
  taskListStore.queryIsTooComplex ? 'advanced' : (previouslyUsedFilterMode ?? 'basic')
)

async function switchMode(newMode: Mode) {
  if (newMode === 'advanced') {
    await resolveUql()
  }
  mode.value = newMode
  Cookie.write(taskListStore.name + '.mode', newMode, 365)
}
watchEffect(() => {
  if (taskListStore.queryIsTooComplex && mode.value === 'basic') {
    switchMode('advanced')
  }
})
</script>

<template>
  <form
    v-if="taskListStore.fields.length > 0"
    name="uql-filter-controls"
    class="flex flex-wrap gap-1 gap-y-2"
    @submit.prevent="submit"
  >
    <!-- Advanced Filter -->
    <div
      class="js-table-controls-uql-filters relative grow sm:w-auto"
      :class="{ hidden: mode === 'basic' }"
    >
      <UqlInput @submit="submit" />
      <!-- help button -->
      <HelpPanelToggler
        :tooltip="Translator.trans('u2_table.uql_help')"
        help-panel-id="uql-help"
        class="text-action absolute top-1/2 right-3 h-4 -translate-y-1/2 transform"
      />
    </div>

    <!-- Basic Filter -->
    <div :class="{ hidden: mode === 'advanced' }" class="flex grow flex-nowrap items-center gap-1">
      <template v-for="defaultField in defaultFields" :key="defaultField['uniqueName']">
        <!-- Period Selector-->
        <AppSelect
          v-if="defaultField['uniqueName'] === 'Period'"
          v-model="fieldValues[defaultField.uniqueName]"
          class="w-2/5 max-w-sm"
          :placeholder="Translator.trans('u2_core.no_period_selected')"
          :options="defaultField.choices ? buildOptions(defaultField.choices) : []"
        />
        <!-- Search Bar-->
        <AppSearch
          v-if="defaultField.uniqueName === 'SearchText'"
          v-model="fieldValues[defaultField.uniqueName]"
          :placeholder="Translator.trans('u2.search')"
          class="w-full"
        />
      </template>

      <!-- Field Selector -->
      <UqlFieldSelector v-model="selectedFields" />
    </div>

    <!-- Filter Buttons -->
    <div class="flex items-center gap-1 whitespace-nowrap">
      <span class="inline-flex">
        <ButtonBasic icon="yes-ok" :grouped="true" button-style="solid" type="submit">
          {{ Translator.trans('u2_table.apply') }}
        </ButtonBasic>
        <ButtonBasic icon="refresh" :grouped="true" @click="reset">
          {{ Translator.trans('u2_table.reset') }}
        </ButtonBasic>
      </span>

      <ButtonBasic
        v-if="mode === 'basic'"
        :title="Translator.trans('u2_table.switch_to_advanced_filtering')"
        data-tooltip-position="top"
        @click="switchMode('advanced')"
      >
        {{ Translator.trans('u2_table.advanced') }}
      </ButtonBasic>

      <ButtonBasic
        v-else
        :title="Translator.trans('u2_table.switch_to_basic_filtering')"
        :data-disabled-tooltip="
          Translator.trans(
            'u2_table.uql_filtered_table.filter_too_complex_to_display_in_basic_mode'
          )
        "
        :data-enabled-tooltip="Translator.trans('u2_table.switch_to_basic_filtering')"
        data-tooltip-position="top"
        :disabled="taskListStore.queryIsTooComplex"
        @click="switchMode('basic')"
      >
        {{ Translator.trans('u2_table.basic') }}
      </ButtonBasic>
    </div>

    <!-- Simple Filter Fields -->
    <div
      v-if="mode === 'basic'"
      class="js-table-filter-active-filters-container flex w-full flex-row flex-wrap gap-x-4 gap-y-2"
    >
      <template v-for="field in selectedFields" :key="field.uniqueName">
        <div class="sm:max-w-xs">
          <FormLabel
            v-if="field.type.name !== 'search_text'"
            :for="`filter-${field.uniqueName}`"
            class="block truncate text-gray-500"
          >
            {{ field.metadata.name }}
          </FormLabel>
          <div class="flex items-center">
            <!-- True/False dropdown -->
            <AppSelect
              v-if="field.type.name === 'boolean'"
              v-model="fieldValues[field.uniqueName]"
              :clearable="false"
              :options="booleanChoices"
              :required="true"
            />

            <!-- Datepicker -->
            <AppDatePicker
              v-else-if="field.type.name === 'date'"
              v-model="fieldValues[field.uniqueName]"
              :name="`filter[${field.uniqueName}]`"
              :twig="true"
            />

            <!-- Choice Field -->
            <AppSelect
              v-else-if="
                typeof field.choices === 'object' && Array.isArray(field.choices) === false
              "
              v-model="fieldValues[field.uniqueName]"
              :clearable="false"
              :options="buildOptions(field.choices)"
              :required="true"
            />

            <!-- Text Field -->
            <AppInputText v-else v-model="fieldValues[field.uniqueName]" type="text" />

            <!-- Remove Field Button -->
            <SvgIcon
              icon="cross"
              size="small"
              class="ml-1 inline-block cursor-pointer text-gray-400 duration-300 ease-in-out hover:text-gray-950"
              :data-tablebundle-column="field.uniqueName"
              @click="selectedFields.delete(field)"
            />
          </div>
        </div>
      </template>
    </div>

    <UqlHelp v-if="mode === 'advanced'" id="uql-help" />
  </form>
</template>
