<script
  setup
  lang="ts"
  generic="
    THeaders extends Array<TableHeader>,
    TItem extends Record<TableHeader['id'], TItem[TableHeader['id']]>
  "
>
import type { Period } from '@js/api/periodApi'
import { computed, ref, toRef } from 'vue'
import getNextSortingDirection from '@js/utilities/getNextSortingDirection'
import AppDate from '@js/components/AppDate.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import HorizontalScrollContainer from '@js/components/HorizontalScrollContainer.vue'
import InfoBox from '@js/components/InfoBox.vue'
import UserLabel from '@js/components/UserLabel.vue'
import OverflowText from '@js/components/OverflowText.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import TableControls from '@js/components/table/TableControls.vue'
import Translator from '@js/translator'
import YesNo from '@js/components/YesNo.vue'
import AppChip from '@js/components/AppChip.vue'
import type { Country } from '@js/model/country'
import type { AnyBasicUnit } from '@js/model/unit'
import type { SortingDirection } from '@js/utilities/getNextSortingDirection'
import type { Currency } from '@js/model/currency'
import type { Status } from '@js/model/status'
import type { TableHeader, TableQuery } from '@js/types'
import type { User } from '@js/model/user'

const emit = defineEmits<{
  (event: 'filter', payload: Record<string, string>): void
  (event: 'sort', payload: { property: keyof TItem; direction?: SortingDirection }): void
  (event: 'newColumnSelection', value: Array<string>): void
  (event: 'pageChange' | 'pageSizeChange', value: number): void
}>()

function filter(event: Event, column: THeaders[number]) {
  emit('filter', { [column.id]: (event.target as HTMLInputElement).value })
}

const props = withDefaults(
  defineProps<{
    headers: THeaders
    query?: TableQuery
    items: Array<TItem>
    totalItems?: number
    horizontalScroll?: boolean
    stickyHeader?: boolean
    wrapByDefault?: boolean
    selected?: Array<string>
    uniqueItemKey?: keyof TItem
    hasControls?: boolean
  }>(),
  {
    query: undefined,
    totalItems: undefined,
    horizontalScroll: false,
    stickyHeader: false,
    wrapByDefault: false,
    selected: undefined,
    uniqueItemKey: 'id',
    hasControls: true,
  }
)
const headers = computed(() => props.headers)
const items = toRef(props, 'items')
defineSlots<
  Record<`itemtype-${TableHeader['type']}`, (props: { value: TItem[keyof TItem] }) => unknown> &
    Record<`itemtype-id`, (props: { value: string }) => unknown> &
    Record<`itemtype-number`, (props: { value: number }) => unknown> &
    Record<`itemtype-period`, (props: { value: Period }) => unknown> &
    Record<`itemtype-currency`, (props: { value: { name: string; iso: string } }) => unknown> &
    Record<`itemtype-percentage`, (props: { value: number | { message: string } }) => unknown> &
    Record<`itemtype-workflow_status`, (props: { value: Status }) => unknown> &
    Record<
      `itemtype-user`,
      (props: { value: number | { id: number; username: string } }) => unknown
    > &
    Record<`itemtype-money`, (props: { value: number }) => unknown> &
    Record<`itemtype-unit`, (props: { value: AnyBasicUnit }) => unknown> &
    Record<`itemtype-country`, (props: { value: Country }) => unknown> &
    Record<
      `itemtype-money_full`,
      (props: { value: { amount: number; currency: Currency } }) => unknown
    > &
    Record<`itemtype-${TableHeader['type']}-header`, (props: { header: TableHeader }) => unknown> &
    Record<`item-${string}`, (props: { item: TItem }) => unknown> &
    Record<`item-${string}-header`, (props: { header: TableHeader }) => unknown> & {
      default?: (props: Record<string, never>) => unknown
      table?: (props: Record<string, never>) => unknown
      header?: (props: { headers: Array<THeaders[number]> }) => unknown
      info?: (props: Record<string, never>) => unknown
      pagination?: (props: Record<string, never>) => unknown
      settings?: (props: Record<string, never>) => unknown
      'table-controls'?: (props: {
        columns: THeaders
        query: unknown
        columnsSelected: Array<string> | undefined
      }) => unknown
    }
>()

const hasControls = computed(() => props.query && props.hasControls)
const tableContainer = ref()

const noResultsMessage = computed(() => {
  if (props.query?.filter !== undefined) {
    return Translator.trans('u2_table.search_results_empty.help')
  }
  return Translator.trans('u2.create_new_record')
})

const visibleColumns = computed<Array<THeaders[number]>>(() => {
  const selected = props.selected
  if (!selected) {
    return headers.value.filter((header) => !header.hidden)
  }
  return headers.value.filter(
    (header) => !header.hidden && (header.required || selected.includes(header.id))
  )
})

const onColumnClick = (column: TableHeader) => {
  if (column.isSortable) {
    emit('sort', {
      property: column.id,
      direction: getNextSortingDirection(props.query?.sort?.[column.id]),
    })
  }
}
const totalItems = computed(() => props.totalItems ?? props.items?.length ?? 0)
</script>

<template>
  <div ref="tableContainer">
    <slot name="table-controls" :columns="headers" :query="query" :columns-selected="selected">
      <TableControls
        v-if="hasControls"
        :selected="selected"
        :headers="headers"
        :total-items="totalItems"
        :query="query"
        @page-change="emit('pageChange', $event)"
        @page-size-change="emit('pageSizeChange', $event)"
        @new-column-selection="emit('newColumnSelection', $event)"
      />
    </slot>
    <component :is="horizontalScroll ? HorizontalScrollContainer : 'div'">
      <table class="w-full" :class="{ 'mt-2': $slots['table-controls'] || query || selected }">
        <thead>
          <slot name="header" :headers="visibleColumns">
            <tr>
              <th
                v-for="(column, index) in visibleColumns"
                :key="index"
                class="border-b border-gray-300 bg-gray-100 leading-none"
                :class="[
                  {
                    'sm:sticky sm:top-(--sticky-header-height)': stickyHeader,
                    'sticky-table-header': stickyHeader && items?.length !== 0,
                    [`text-${column.align}`]: column.align,
                    'whitespace-nowrap': !(column.wrap ?? wrapByDefault),
                  },
                  `table-head-${column.type?.toLowerCase().replaceAll('_', '-') ?? 'text'}`,
                ]"
              >
                <div
                  :class="{ 'cursor-pointer': column.isSortable }"
                  :title="column.label"
                  @click="onColumnClick(column)"
                >
                  <slot :name="`item-${column.id}-header`" :header="column">
                    <slot :name="`itemtype-${column.type}-header`" :header="column">
                      {{ column.name }}
                      <template v-if="column.unit">({{ column.unit }})</template>
                    </slot>
                  </slot>
                  <SvgIcon
                    v-show="column.isSortable"
                    icon="arrow-down"
                    class="inline-block transform align-middle transition ease-in-out"
                    :class="{
                      '-rotate-180': query?.sort?.[column.id] === 'DESC',
                      'text-gray-300': query?.sort?.[column.id] === undefined,
                    }"
                  />
                </div>
                <input
                  v-if="column.filter"
                  class="mt-2"
                  type="search"
                  :value="query?.filter?.[column.id]"
                  @input="filter($event, column)"
                />
              </th>
            </tr>
          </slot>
        </thead>

        <tbody>
          <tr
            v-for="item in items"
            :key="item[uniqueItemKey as keyof TItem] as string"
            class="border-b border-gray-300 bg-white transition duration-300 last:border-b-0 hover:bg-orange-200"
          >
            <td
              v-for="(column, columnIndex) in visibleColumns"
              :key="columnIndex"
              class="px-3 py-1"
              :class="[
                {
                  [`text-${column.align}`]: column.align,
                  'whitespace-nowrap': !(column.wrap ?? wrapByDefault),
                },
                `table-data-${column.type?.toLowerCase().replaceAll('_', '-') ?? 'text'}`,
              ]"
            >
              <slot :name="`item-${column.id}`" :item="item">
                <slot :name="`itemtype-${column.type}`" :value="item[column.id]">
                  <UserLabel
                    v-if="column.type === 'user'"
                    :user="
                      typeof item[column.id] === 'number'
                        ? (item[column.id] as User['id'])
                        : undefined
                    "
                    :fallback="
                      typeof item[column.id] === 'string' ? (item[column.id] as string) : undefined
                    "
                  />

                  <AppDate
                    v-else-if="column.type === 'date' && item[column.id]"
                    :date="item[column.id]"
                  />

                  <AppDateTime
                    v-else-if="column.type === 'datetime' && item[column.id]"
                    class="whitespace-nowrap"
                    :date="item[column.id]"
                  />

                  <AppChip v-else-if="column.type === 'count' && item[column.id]" color="gray">
                    {{ item[column.id] }}
                  </AppChip>

                  <YesNo
                    v-else-if="column.type === 'boolean' && item[column.id] !== undefined"
                    :value="item[column.id] as boolean"
                  />

                  <template v-else>
                    <OverflowText
                      class="block"
                      :class="{ 'max-w-96 truncate': !(column.wrap ?? wrapByDefault) }"
                      :text="
                        Array.isArray(item[column.id])
                          ? (item[column.id] as string[]).join(', ')
                          : (item[column.id] as string)
                      "
                    />
                  </template>
                </slot>
              </slot>
            </td>
          </tr>
        </tbody>
      </table>

      <template #fixed>
        <InfoBox
          v-if="items?.length === 0"
          class="my-10"
          icon="hide"
          :title="Translator.trans('u2_table.no_results_that_match_search')"
        >
          <p>{{ noResultsMessage }}</p>
        </InfoBox>
      </template>
      <div class="sticky left-0 max-w-[calc(100vw-var(--scrollbar-width))]">
        <InfoBox
          v-if="!horizontalScroll && items?.length === 0"
          class="my-10"
          icon="hide"
          :title="Translator.trans('u2_table.no_results_that_match_search')"
        >
          <p>{{ noResultsMessage }}</p>
        </InfoBox>
      </div>
    </component>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

@media (min-width: theme(--breakpoint-sm)) {
  .sticky-table-header {
    /* Adding pseudo element with gray border to mimic the sticky th bottom border */
    &:not(:empty)::before {
      border-bottom: 1px solid theme('colors.gray.300');
      border-top: 1px solid theme('colors.gray.100');
      content: '';
      height: calc(100% + 2px);
      left: -1px;
      position: absolute;
      top: -1px;
      width: calc(100% + 2px);
      z-index: -1;
    }
  }
}
</style>
