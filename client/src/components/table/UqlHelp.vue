<script setup lang="ts">
import HelpPanel from '@js/components/help/HelpPanel.vue'
import { useTaskListStore } from '@js/stores/task-list'
import Translator from '@js/translator'
import type { TableColumn } from '@js/stores/task-list'
import type { TranslationId } from '@js/translator'

const taskListStore = useTaskListStore()
const getFieldName = (column: TableColumn) => {
  return taskListStore.fields.find((field) => column.id === field.uniqueName)?.type.name
}
</script>
<template>
  <HelpPanel>
    <h2>
      {{ Translator.trans('u2_table.available_fields') }}
    </h2>
    <div class="flex flex-none flex-wrap gap-2">
      <template v-for="column in taskListStore.columns">
        <span
          v-if="column.filterable"
          :key="column.id"
          class="inline-block rounded-sm border border-gray-200 bg-white p-1.5 pb-2 leading-none"
        >
          <strong>{{ Translator.trans(column.name as TranslationId) }}</strong>
          <span class="text-gray-700">[{{ getFieldName(column) }}]</span>:
          <kbd>{{ column.id }}</kbd>
        </span>
      </template>
    </div>

    <h2 class="mt-3">
      {{ Translator.trans('u2_table.available_functions') }}
    </h2>
    <div class="flex flex-none flex-wrap gap-2">
      <span
        v-for="func in taskListStore.functions"
        :key="func.name"
        class="inline-block rounded-sm border border-gray-200 bg-white p-1.5 pb-2 leading-none"
      >
        <strong>{{ func.name }}</strong
        >: <kbd>{{ func.string }}</kbd>
      </span>
    </div>
  </HelpPanel>
</template>
