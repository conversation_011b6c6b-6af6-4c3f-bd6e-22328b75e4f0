<script setup lang="ts">
import { evaluateTaskUql } from '@js/api/taskApi'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import $ from 'jquery'
import debounce from 'lodash/debounce'
import Autocomplete from '@js/components/legacy/table/uql/autocomplete'
import { useTaskListStore } from '@js/stores/task-list'
import SuggestContext from '@js/components/legacy/table/uql/SuggestContext'
import SvgIcon from '@js/components/SvgIcon.vue'
import UqlSuggestion from '@js/components/table/UqlSuggestion.vue'
import { useTaskListInfoStore } from '@js/stores/task-list-info'
import vClickOutside from '@js/directives/click-outside'

const taskListStore = useTaskListStore()
const uqlState = ref(taskListStore.advancedFilterState)
const autocompleteEngine = computed(
  () => new Autocomplete(taskListStore.fields, taskListStore.functions)
)
const selected = ref()
const isAutocompleteVisible = ref(false)

function closeSuggestions() {
  isAutocompleteVisible.value = false
}

const emit = defineEmits<(event: 'submit') => void>()
const onKeyDown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowUp':
      event.preventDefault()
      movementGenerator(-1)
      if (!selected.value) {
        isAutocompleteVisible.value = false
      }
      break
    case 'ArrowDown':
      event.preventDefault()
      isAutocompleteVisible.value = true
      movementGenerator(1)
      break
    case 'Escape':
      event.preventDefault()
      isAutocompleteVisible.value = false
      selected.value = undefined
      break
    case 'Enter':
      event.preventDefault()
      if (!autoComplete()) {
        isAutocompleteVisible.value = false
        emit('submit')
      }
      break
  }
}

function autoComplete() {
  const autocompletion = getAutocompletion()
  if (autocompletion) {
    uqlState.value.uql = autocompletion
    return true
  }
  return false
}

const onSuggestionClick = (i: number) => {
  setSelected(i + 1)
  const autocompletion = getAutocompletion()
  if (autocompletion) {
    uqlState.value.uql = autocompletion
  }
  $('.js-table-filter-uql-input').trigger('focus') // Dirtyness required to avoid too much coupling with events
}

const movementGenerator = (n: 1 | -1) => {
  if (autocompletion.value.suggestions.length === 0) {
    setSelected(undefined)
    return
  }
  setSelected(((selected.value || 0) + n) % (autocompletion.value.suggestions.length + 1))
}

const onKeyUp = (event: KeyboardEvent) => {
  event.preventDefault()
  if (['Tab', 'Enter', 'Escape', 'ArrowUp', 'ArrowDown'].indexOf(event.key) === -1) {
    uqlState.value.uql = (event.target as HTMLInputElement).value
  }
}

const taskListInfoStore = useTaskListInfoStore()
const shortName = computed(() => {
  if (taskListInfoStore.taskListInfo) {
    return taskListInfoStore.taskListInfo.shortName
  }
  throw new Error('Task info must be defined!')
})

const evaluateUql = debounce(async (uql: string) => {
  const uqlResponse = await evaluateTaskUql(uql, shortName.value)
  taskListStore.advancedFilterState.status = uqlResponse.data.valid ? 'success' : 'error'
  taskListStore.advancedFilterState.filters = uqlResponse.data.filters
  taskListStore.advancedFilterState.valid = uqlResponse.data.valid
  taskListStore.advancedFilterState.error = uqlResponse.data.error
  taskListStore.advancedFilterState.compatible = uqlResponse.data.compatible
}, 1000)

function setSelected(index?: number) {
  if (!index || index < 1 || index > autocompletion.value.suggestions.length) {
    index = undefined
  }
  selected.value = index
  nextTick(() => {
    const current = $('.js-selected-suggestion')
    if (!current.length) {
      return
    }
    const parent = current.parent()
    if (Number(parent.height()) <= current.position().top + Number(current.outerHeight())) {
      parent.scrollTop(
        current.position().top +
          Number(parent.scrollTop()) -
          Number(parent.outerHeight()) +
          Number(current.outerHeight())
      )
    } else if (current.position().top <= 0) {
      parent.scrollTop(current.position().top + Number(parent.scrollTop()))
    }
  })
}

/**
 * Get the autocompletion result, based on the currently selected choice, etc
 */
function getAutocompletion(): string | false {
  if (!isAutocompleteVisible.value) {
    return false
  }

  if (autocompletion.value.suggestions.length) {
    if (selected.value) {
      return (
        autocompletion.value.existingUql +
        autocompletion.value.suggestions[selected.value - 1] +
        ' '
      )
    }

    const commonStartingSuggestions = filterStartingWithPartial(
      autocompletion.value.suggestions,
      autocompletion.value.partialWord
    )

    if (!commonStartingSuggestions.length) {
      return false
    }

    if (commonStartingSuggestions.length === 1) {
      return autocompletion.value.existingUql + commonStartingSuggestions[0] + ' '
    }

    const longestCommonSubstring = getLongestCommonStartingSubstring(
      autocompletion.value.suggestions,
      autocompletion.value.partialWord
    )

    if (!longestCommonSubstring.length) {
      return false
    }

    if (longestCommonSubstring.toUpperCase() === autocompletion.value.partialWord.toUpperCase()) {
      return false
    }

    return autocompletion.value.existingUql + longestCommonSubstring
  }

  return false
}

/**
 * Given a set of strings, and a partial string, return the longest common substring starting with partial.
 */
function getLongestCommonStartingSubstring(strings: Array<string>, partial: string) {
  // Filter out string not starting with partial
  strings = filterStartingWithPartial(strings, partial)

  if (!strings.length) {
    return partial
  }

  const getLongestCommonStartingSubstringIndex = (strings: Array<string>) => {
    const orderedStrings = strings.slice(0).sort((a, b) => (a.length > b.length ? -1 : 1))
    const shortest = orderedStrings.shift() || ''

    for (let i = 0; i < shortest.length; i++) {
      for (const string of orderedStrings) {
        if (shortest[i] !== string[i]) {
          return i
        }
      }
    }

    return shortest.length - 1
  }
  return strings.length === 1
    ? strings[0]
    : strings[0].slice(0, getLongestCommonStartingSubstringIndex(strings))
}

/**
 * Gets the autocompletion to suggest to the user
 */
function getSuggestion(suggestions: Array<string>, partialWord: string): string | false {
  if (suggestions.length) {
    const suggestionsContainingPartial = suggestions
      // filter those that contain partial
      .filter((string) => string.toUpperCase().indexOf(partialWord.toUpperCase()) >= 0)

    if (!suggestionsContainingPartial.length) {
      return false
    }

    if (suggestionsContainingPartial.length === 1) {
      return suggestionsContainingPartial[0]
    }

    const longestCommonSubstring = getLongestCommonStartingSubstring(suggestions, partialWord)

    if (!longestCommonSubstring.length) {
      return false
    }

    return longestCommonSubstring
  }

  return false
}

function getSuggestionSelectionIndex(suggestions: Array<string>, partialWord: string) {
  const suggestion = getSuggestion(suggestions, partialWord)
  if (suggestion === false) {
    return
  }

  const index = suggestions.indexOf(suggestion)
  return index === -1 ? undefined : index + 1
}

function filterStartingWithPartial(strings: Array<string>, partial: string) {
  return strings.filter((string) => string.toUpperCase().indexOf(partial.toUpperCase()) === 0)
}

const autocompletion = computed(() => {
  const suggestContext = new SuggestContext(taskListStore.advancedFilterState.uql)
  const suggestions = autocompleteEngine.value.suggest(suggestContext)
  return {
    suggestions,
    existingUql: suggestContext.prev,
    partialWord: suggestContext.partial,
    bestSuggestion: getSuggestionSelectionIndex(suggestions, suggestContext.partial),
  }
})

watch(
  () => uqlState.value.uql,
  (uql) => {
    taskListStore.advancedFilterState.status = 'evaluating'
    setSelected(autocompletion.value.bestSuggestion)
    if (uql.length && $('.js-table-filter-uql-input').is(':focus')) {
      isAutocompleteVisible.value = true
    }
    evaluateUql(uql)
  }
)

onMounted(() => {
  evaluateUql(uqlState.value.uql)
})
</script>

<template>
  <div v-click-outside="closeSuggestions" class="js-uql-autocomplete-wrapper">
    <!-- Filter Uql -->
    <span
      v-tooltip="uqlState.status === 'error' ? uqlState.error : uqlState.status"
      class="absolute top-2.5 right-8 size-4"
    >
      <SvgIcon v-if="uqlState.status === 'error'" icon="no" class="text-bad w-full" />
      <SvgIcon v-else-if="uqlState.status === 'success'" icon="yes-ok" class="text-good w-full" />
      <SvgIcon
        v-else-if="uqlState.status === 'evaluating'"
        icon="config"
        class="text-action w-full animate-[spin_3s_linear_infinite]"
      />
    </span>
    <input
      v-model="uqlState.uql"
      autocomplete="off"
      :class="[
        'js-table-filter-uql-input w-full pr-12',
        { 'has-errors text-bad': uqlState.status === 'error' },
      ]"
      name="uql"
      type="text"
      @keydown="onKeyDown"
      @keyup="onKeyUp"
    />
    <!-- Auto-Complete Suggestions -->
    <div class="absolute w-full">
      <div
        v-if="isAutocompleteVisible && autocompletion.suggestions.length"
        class="bt-0 max-h-72 overflow-x-auto rounded-b border bg-white p-2 shadow-sm"
        tabindex="-1"
      >
        <p
          v-for="(suggestion, key) in autocompletion.suggestions"
          :key="suggestion"
          :class="[
            'm-0 overflow-hidden rounded-xs px-1 text-ellipsis whitespace-nowrap hover:cursor-pointer hover:bg-blue-100',
            {
              'js-selected-suggestion bg-action hover:bg-action-darker text-white':
                selected === key + 1,
            },
          ]"
          @click="onSuggestionClick(key)"
        >
          <UqlSuggestion :suggestion="suggestion" :partial="autocompletion.partialWord" />
        </p>
      </div>
    </div>
  </div>
</template>
