<script setup lang="ts" generic="THeaders extends Array<TableHeader>">
import { computed, ref } from 'vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import SelectColumnsDialog from '@js/components/table/SelectColumnsDialog.vue'
import TableInfo from '@js/components/table/TableInfo.vue'
import TablePagination from '@js/components/table/TablePagination.vue'
import Translator from '@js/translator'
import type { TableHeader, TableQuery } from '@js/types'

defineSlots<{
  info?: (props: Record<string, never>) => unknown
  pagination?: (props: Record<string, never>) => unknown
  settings?: (props: Record<string, never>) => unknown
}>()

const props = withDefaults(
  defineProps<{
    query?: TableQuery
    selected?: Array<THeaders[number]['id']>
    headers?: THeaders
    totalItems?: number
  }>(),
  {
    query: undefined,
    selected: undefined,
    headers: undefined,
    totalItems: 0,
  }
)

const emit = defineEmits<{
  (event: 'pageChange' | 'pageSizeChange', value: number): void
  (event: 'newColumnSelection', value: Array<string>): void
}>()

const isColumnSelectorDialogShown = ref(false)
const selectableColumns = computed(() => {
  if (!props.headers) {
    return []
  }
  return props.headers.filter((header) => !header.required)
})
</script>

<template>
  <div class="mt-6 flex flex-col items-center gap-2 sm:flex-row">
    <div class="basis-1/3">
      <slot name="info">
        <TableInfo :record-count="totalItems" />
      </slot>
    </div>

    <div class="inline-flex grow basis-1/3 justify-center">
      <slot name="pagination">
        <TablePagination
          v-if="query"
          :current-page="query.page"
          :items-per-page="query.itemsPerPage"
          :total-items="totalItems"
          :max-visible-pages="query.maxVisiblePages"
          @page-change="emit('pageChange', $event)"
        />
      </slot>
    </div>

    <div class="flex w-auto basis-1/3 items-center justify-end gap-0.5 whitespace-nowrap">
      <slot name="settings">
        <template v-if="query">
          {{ Translator.trans('u2_table.show') }}
          <ButtonBasic
            v-for="pageSize in [5, 10, 20, 50, 100]"
            :key="pageSize"
            :color="pageSize === query.itemsPerPage ? 'white' : 'action'"
            :button-style="pageSize === query.itemsPerPage ? 'solid' : 'text'"
            :disabled="pageSize === query.itemsPerPage"
            @click="emit('pageSizeChange', pageSize)"
          >
            {{ pageSize }}
          </ButtonBasic>
        </template>

        <ButtonSpacer v-if="query && selected" />

        <template v-if="selected">
          <ButtonBasic @click="isColumnSelectorDialogShown = true">
            {{ Translator.trans('u2_table.columns') }}
          </ButtonBasic>

          <SelectColumnsDialog
            v-if="isColumnSelectorDialogShown"
            :columns="selectableColumns"
            :selected-columns="selected"
            @new-column-selection="emit('newColumnSelection', $event)"
            @close="isColumnSelectorDialogShown = false"
          />
        </template>
      </slot>
    </div>
  </div>
</template>
