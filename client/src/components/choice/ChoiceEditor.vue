<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { computed, toRefs } from 'vue'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { choiceApi } from '@js/api/choiceApi'
import FieldInputText from '@js/components/form/FieldInputText.vue'
import FieldTextarea from '@js/components/form/FieldTextarea.vue'
import FieldToggle from '@js/components/form/FieldToggle.vue'
import FormFieldset from '@js/components/form/FormFieldset.vue'
import useForm from '@js/composable/useForm'
import { rulesSchema } from '@js/model/choice'
import Translator from '@js/translator'
import type { Choice, ChoiceCollectionEndpoint } from '@js/model/choice'

const props = withDefaults(
  defineProps<{
    choice: Choice | Pick<Choice, '@type' | 'name' | 'enabled' | 'rules'>
    resourceCollectionEndpoint: ChoiceCollectionEndpoint
  }>(),
  {}
)
const emit = defineEmits<{
  (event: 'saved', payload: Choice): void
  (event: 'error' | 'saving'): void
}>()
const { choice } = toRefs(props)

const formatRules = (rules: Choice['rules']) => {
  return JSON.stringify(
    rules,
    function (key, value) {
      if (key === 'value') {
        return JSON.stringify(value)
      }
      return value
    },
    2
  )
    .replace(/\\/g, '')
    .replace(/"\[/g, '[')
    .replace(/\]"/g, ']')
}

const { handleSubmit } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      name: z.string().min(1).max(120),
      enabled: z.boolean(),
      rules: z.string().refine((value: string) => {
        try {
          return rulesSchema.safeParse(value ? JSON.parse(value) : null)
        } catch {
          // If JSON parsing fails, return false
          return false
        }
      }, Translator.trans('u2.error')),
    })
  ),
  initialValues: {
    name: choice.value?.name ?? '',
    enabled: choice.value?.enabled ?? true,
    rules: choice.value?.rules ? formatRules(choice.value.rules) : '',
  },
})

const ruleHelp = computed(() => JSON.stringify(zodToJsonSchema(rulesSchema), null, 2))

const save = handleSubmit(async (values) => {
  emit('saving')
  const updatedChoice = {
    ...choice.value,
    ...values,
    rules: values.rules ? JSON.parse(values.rules) : null,
  }
  if ('id' in updatedChoice) {
    await choiceApi
      .updateChoice(updatedChoice)
      .then((response) => {
        emit('saved', response.data)
      })
      .catch(() => {
        emit('error')
      })
    return
  }

  await choiceApi
    .createChoice(updatedChoice)
    .then((response) => {
      emit('saved', response.data)
    })
    .catch(() => {
      emit('error')
    })
})
</script>

<template>
  <form id="choice" name="choice" class="max-w-full" @submit.prevent="save">
    <FieldInputText
      name="name"
      :label="Translator.trans('u2.name')"
      :required="true"
      maxlength="120"
    />

    <FieldToggle :label="Translator.trans('u2.enabled')" name="enabled" />

    <FormFieldset :label="Translator.trans('u2.advanced')" :collapsible="true" :collapsed="true">
      <FieldTextarea
        class="w-full"
        name="rules"
        :help-tooltip="Translator.trans('u2.show_schema')"
        :label="Translator.trans('u2.rules')"
        :rows="5"
      >
        <template #help>
          <h5 class="text-action">JSON Schema</h5>
          <pre class="overflow-scroll rounded-sm p-2 font-mono text-sm">{{ ruleHelp }}</pre>
        </template>
      </FieldTextarea>
    </FormFieldset>
  </form>
</template>
