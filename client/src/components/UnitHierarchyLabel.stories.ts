import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { createUnitHierarchy } from '@tests/__factories__/createUnitHierarchy'
import { faker } from '@faker-js/faker/locale/en'
import { createUser } from '@tests/__factories__/createUser'
import UnitHierarchyLabel from '@js/components/UnitHierarchyLabel.vue'
import { useAuthStore } from '@js/stores/auth'
import BaseCheckbox from '@js/components/form/BaseCheckbox.vue'
import type { Meta, StoryObj } from '@storybook/vue3'
import type { UnitHierarchyStructureTreeNode } from '@js/model/unit_hierarchy'

const unitHierarchyRecord = createUnitHierarchy({ id: 1 })

function createTreeNode(
  children: Array<UnitHierarchyStructureTreeNode> = []
): UnitHierarchyStructureTreeNode {
  return {
    id: faker.number.int(),
    children,
    icon: 'icon-legal-unit',
    title: faker.lorem.words(2),
  }
}

const meta: Meta<typeof UnitHierarchyLabel> = {
  title: 'Label/Unit Hierarchy',
  parameters: {
    msw: {
      handlers: [
        http.get('/api/unit-hierarchies/1', async () => {
          return HttpResponse.json(unitHierarchyRecord, { status: StatusCodes.OK })
        }),
        http.get('/api/unit-hierarchies/1/structure/:date', async (req) => {
          const { date } = req.params
          return HttpResponse.json(
            {
              date,
              unitHierarchy: unitHierarchyRecord['@id'],
              tree: [
                createTreeNode([
                  createTreeNode([createTreeNode([createTreeNode()])]),
                  createTreeNode([createTreeNode(), createTreeNode()]),
                  createTreeNode(),
                ]),
                createTreeNode([createTreeNode([createTreeNode([createTreeNode()])])]),
                createTreeNode([
                  createTreeNode([createTreeNode([createTreeNode([createTreeNode()])])]),
                  createTreeNode(),
                  createTreeNode([createTreeNode([createTreeNode([createTreeNode()])])]),
                  createTreeNode(),
                ]),
                createTreeNode(),
                createTreeNode(),
              ],
            },
            { status: StatusCodes.OK }
          )
        }),
      ],
    },
  },
  argTypes: {},
  args: {
    unitHierarchy: unitHierarchyRecord,
    date: new Date(),
  },
}

export default meta

export const withPermission: StoryObj<typeof UnitHierarchyLabel> = {
  render: (args) => ({
    components: { UnitHierarchyLabel, BaseCheckbox },
    setup() {
      const authStore = useAuthStore()
      authStore.user = createUser({ id: 1, roles: ['ROLE_UNIT_MANAGER'] })

      return { args, authStore }
    },
    template: `
      <UnitHierarchyLabel v-bind="args"/>
    `,
  }),
}

export const withoutPermission: StoryObj<typeof UnitHierarchyLabel> = {
  render: (args) => ({
    components: { UnitHierarchyLabel, BaseCheckbox },
    setup() {
      return { args }
    },
    template: `
      <UnitHierarchyLabel v-bind="args"/>
    `,
  }),
}
