<script setup lang="ts">
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { SystemMessage } from '@js/model/system_message'

defineProps<{
  message: SystemMessage
}>()

defineEmits<(event: 'close', payload: { element: HTMLElement; id: SystemMessage['id'] }) => void>()
</script>

<template>
  <div
    :class="message.type === 'WARNING' ? 'bg-orange-200' : 'bg-blue-200'"
    class="flex max-h-full items-center border-t border-white leading-tight first:border-t-0"
  >
    <div
      :class="[
        'flex items-center self-stretch border-t border-transparent px-2 text-white first:border-t-0',
        message.type === 'WARNING' ? 'bg-alert' : 'bg-blue-500',
      ]"
    >
      <SvgIcon v-if="message.type === 'WARNING'" icon="alert" />
      <SvgIcon v-else icon="info" />
    </div>
    <div class="grow px-3 py-1">
      {{ message.content }}
    </div>
    <div class="self-start p-0.5">
      <ButtonBasic
        icon="cross"
        :class="[
          message.type === 'WARNING'
            ? 'text-alert hover:bg-orange-300 hover:text-orange-900 focus:bg-orange-300 focus:text-orange-900'
            : 'text-blue-500 hover:bg-blue-300 hover:text-blue-900 focus:bg-blue-300 focus:text-blue-900',
        ]"
        @click="$emit('close', { element: $el, id: message.id })"
      />
    </div>
  </div>
</template>
