<script setup lang="ts">
import type { Period } from '@js/api/periodApi'
import { computed, toRefs } from 'vue'
import Translator from '@js/translator'
import PeriodPopupCard from '@js/components/PeriodPopupCard.vue'
import PeriodStatus from '@js/components/period/PeriodStatus.vue'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'

const props = defineProps<{
  period?: Period | null
  fallback?: string
}>()

const { period } = toRefs(props)

const fallback = computed(() => props.fallback ?? Translator.trans('u2.unknown'))

defineSlots<{
  default: () => unknown
}>()
</script>

<template>
  <LabelWithMenu :disabled="!period">
    <template #default>
      <slot name="default">
        <PeriodStatus v-if="period" :is-closed="period.closed" :period-name="period.name" />
        <span v-else class="text-gray-500 lowercase italic" v-text="fallback" />
      </slot>
    </template>

    <template #content>
      <PeriodPopupCard v-if="period" :period="period" />
    </template>
  </LabelWithMenu>
</template>
