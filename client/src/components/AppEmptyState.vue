<script setup lang="ts">
defineSlots<{
  default: (props: Record<string, never>) => unknown
  title?: (props: Record<string, never>) => unknown
  action?: (props: Record<string, never>) => unknown
}>()
</script>

<template>
  <div class="flex flex-col items-center gap-3 rounded-md border border-gray-200 bg-white/70 p-5">
    <h3
      v-if="!!$slots.title"
      class="leading-tight font-normal text-pretty break-words text-gray-700"
    >
      <slot name="title" />
    </h3>

    <div class="space-y-2 text-center leading-tight text-pretty text-gray-500">
      <slot name="default" />
    </div>

    <div v-if="!!$slots.action" class="inline-block">
      <slot name="action" />
    </div>
  </div>
</template>
