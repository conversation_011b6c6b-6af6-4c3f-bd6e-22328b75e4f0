<script setup lang="ts">
import CollapsibleElement from '@js/components/CollapsibleElement.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { Icon } from '@js/utilities/name-lists'

withDefaults(
  defineProps<{
    headline: string
    collapsible?: boolean
    collapsed?: boolean
    icon?: Icon
    unmountOnHide?: boolean
  }>(),
  {
    collapsible: true,
    collapsed: false,
    icon: undefined,
    unmountOnHide: false,
  }
)
</script>

<template>
  <CollapsibleElement
    class="rounded-md bg-linear-to-tr from-gray-50 to-gray-100 shadow-sm"
    as="section"
    :disabled="!collapsible"
    :unmount-on-hide
    :default-open="!collapsed"
  >
    <template #default="{ open }">
      <div
        class="group flex w-full items-center justify-between border-b border-gray-300 p-4"
        :class="{
          'cursor-pointer': collapsible,
          'pointer-events-none cursor-default': !collapsible,
        }"
      >
        <h2 class="flex items-center gap-x-2 text-xl transition-all ease-in-out sm:text-2xl">
          <SvgIcon v-if="icon" :key="icon" :icon="icon" size="large" class="text-gray-500" />
          {{ headline }}
          <SvgIcon
            v-if="collapsible"
            :class="[
              'transform text-gray-400 opacity-20 transition ease-in-out group-hover:opacity-100',
              { '-rotate-90': !open },
            ]"
            icon="arrow-down"
          />
        </h2>

        <span class="flex" @click.stop="">
          <slot name="button" />
        </span>
      </div>
    </template>

    <template #content>
      <div class="p-4">
        <slot />
      </div>
    </template>
  </CollapsibleElement>
</template>
