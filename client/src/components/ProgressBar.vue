<script setup lang="ts">
import { computed } from 'vue'
import type { Color } from '@js/utilities/name-lists'

const props = withDefaults(
  defineProps<{
    max: number
    value: number
    color?: Color
    mode?: 'compact' | 'expanded'
  }>(),
  {
    color: undefined,
    mode: 'compact',
  }
)

const progressInPercent = computed(() => Math.round((props.value / props.max) * 100))
</script>

<template>
  <div
    :class="[
      'border-grey-30 relative flex items-center rounded-full border bg-gray-200',
      {
        'h-4': mode === 'compact',
        'h-6': mode === 'expanded',
      },
    ]"
  >
    <div
      :style="{ width: `${progressInPercent}%` }"
      :class="[
        'flex h-full rounded-full duration-500 ease-in-out',
        color ? 'bg-' + color : 'striped-progress-bar',
      ]"
    />
    <div
      v-if="mode === 'expanded'"
      :class="['absolute', progressInPercent > 50 ? 'text-white' : 'text-black']"
      :style="{ left: '50%', transform: 'translateX(-50%)' }"
    >
      {{ progressInPercent }}%
    </div>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

@keyframes progress-bar-stripes {
  from {
    background-position: 10px 0;
  }

  to {
    background-position: 0 0;
  }
}

.striped-progress-bar {
  animation: progress-bar-stripes 1s linear infinite;
  background-color: var(--color-action-lighter);
  background-image: linear-gradient(
    45deg,
    var(--color-action-lighter) 25%,
    var(--color-action) 25%,
    var(--color-action) 50%,
    var(--color-action-lighter) 50%,
    var(--color-action-lighter) 75%,
    var(--color-action) 75%,
    var(--color-action)
  );
  background-size: 10px 10px;
}
</style>
