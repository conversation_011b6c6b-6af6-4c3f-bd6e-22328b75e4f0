import Operators from '@js/components/legacy/table/uql/operators.js'

export default class SuggestContext {
  public prev: string
  public partial: string
  public assocIdentifier: string
  public before?: string

  private all: string

  constructor(uql: string) {
    const isInQuotes = this._isStringInQuotes(uql)
    let parts
    let prev
    let partial
    if (isInQuotes) {
      parts = uql.split(isInQuotes)
      partial = '"' + parts.pop()
      prev = uql.substring(0, uql.length - partial.length)
      parts = prev.replace('(', '(<|||>').split(/ |<\|\|\|>|(?=\))/)
    } else {
      parts = uql.replace('(', '(<|||>').split(/ |<\|\|\|>|(?=\))/)
      partial = parts.pop() ?? ''
      prev = uql.substring(0, uql.length - partial.length)
    }
    let before = parts.pop()
    while (before === '') {
      before = parts.pop()
    }
    let assocIdentifier = ''

    const operatorRegex = new RegExp(Operators.regex + '$', 'g')
    const operatorMatches = prev.match(operatorRegex)
    if (operatorMatches) {
      assocIdentifier =
        prev
          .substring(0, prev.length - operatorMatches[operatorMatches.length - 1].length)
          .trim()
          .replace('(', '(<|||>')
          .split(/ |<\|\|\|>|(?=\))/)
          .pop()
          ?.trim() ?? ''
    }

    this.all = uql
    this.assocIdentifier = assocIdentifier
    this.before = before
    this.partial = partial
    this.prev = prev
  }

  _isStringInQuotes(string: string) {
    let inSingleQuotes = false
    let inDoubleQuotes = false
    for (const value of string) {
      if (value === "'" && !inDoubleQuotes) {
        inSingleQuotes = !inSingleQuotes
      } else if (value === '"' && !inSingleQuotes) {
        inDoubleQuotes = !inDoubleQuotes
      }
    }

    if (inSingleQuotes) {
      return "'"
    }

    if (inDoubleQuotes) {
      return '"'
    }

    return false
  }
}
