<script setup lang="ts">
import { computed } from 'vue'
import Translator from '@js/translator'
import useUnitHierarchyQuery from '@js/composable/useUnitHierarchyQuery'
import UnitHierarchyPopupCard from '@js/components/UnitHierarchyPopupCard.vue'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'

const props = withDefaults(
  defineProps<{
    unitHierarchy?: UnitHierarchy | UnitHierarchy['id'] | null
    date?: Date
  }>(),
  {
    unitHierarchy: undefined,
    date: undefined,
  }
)

const resolvedLayoutId = computed(() => {
  return !props.unitHierarchy
    ? undefined
    : typeof props.unitHierarchy === 'number'
      ? props.unitHierarchy
      : props.unitHierarchy.id
})

const initialData = computed(() =>
  props.unitHierarchy && typeof props.unitHierarchy === 'object' ? props.unitHierarchy : undefined
)

const {
  data: unitHierarchyFromQuery,
  isLoading,
  isError,
} = useUnitHierarchyQuery(resolvedLayoutId, {
  initialData: initialData.value,
})
</script>

<template>
  <LabelWithMenu :disabled="isLoading || isError">
    <template #default>
      <span
        v-if="isLoading"
        class="animate-pulse text-gray-500 lowercase italic"
        v-text="Translator.trans('u2.loading')"
      />
      <span v-else-if="unitHierarchyFromQuery" v-text="unitHierarchyFromQuery.name" />
      <span v-else-if="isError" v-text="Translator.trans('u2.unknown')" />
    </template>

    <template #content>
      <UnitHierarchyPopupCard
        v-if="unitHierarchyFromQuery"
        :unit-hierarchy="unitHierarchyFromQuery"
        :date="date ?? new Date()"
      />
    </template>
  </LabelWithMenu>
</template>
