<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useInfiniteScroll } from '@vueuse/core'

const props = defineProps<{
  currentYear: number
}>()

const emit = defineEmits<{
  (event: 'selectYear', payload: number): void
  (event: 'escape'): void
}>()
const years = ref<HTMLElement | null>(null)

const numberOfYears = 50

const yearsData = ref(
  Array.from({ length: numberOfYears }, (_, i) => new Date().getFullYear() - numberOfYears / 2 + i)
)

const yearElement = ref<Array<HTMLElement> | null>(null)

function setFocusToCurrentYear() {
  const index = yearsData.value.findIndex((year) => year === props.currentYear)
  const currentYearButton = yearElement.value?.[index]
  if (currentYearButton) {
    currentYearButton.focus()
  }
}

defineExpose({
  setFocusToCurrentYear,
})

onMounted(() => {
  setFocusToCurrentYear()
})

// To top
useInfiniteScroll(
  years,
  () => {
    yearsData.value.unshift(
      ...Array.from({ length: 30 }, (_, i) => yearsData.value[0] - i - 1).reverse()
    )
  },
  {
    distance: 100,
    direction: 'top',
  }
)

// To bottom
useInfiniteScroll(
  years,
  () => {
    yearsData.value.push(
      ...Array.from({ length: 30 }, (_, i) => yearsData.value[yearsData.value.length - 1] + i + 1)
    )
  },
  { distance: 100 }
)

function onFocus(event: FocusEvent) {
  event.preventDefault()
  event.stopPropagation()
  if (event.target instanceof HTMLButtonElement) {
    event.target.focus({ preventScroll: true })
    event.target.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
  }
}

function blur(event: KeyboardEvent) {
  if (event.target instanceof HTMLButtonElement) {
    event.target.blur()
    emit('escape')
  }
}
</script>

<template>
  <div
    ref="years"
    class="mt-6 h-60 snap-y snap-mandatory overflow-y-scroll scroll-smooth text-center focus:outline-hidden"
    tabindex="0"
    @focus="setFocusToCurrentYear"
  >
    <button
      v-for="year in yearsData"
      :key="year"
      ref="yearElement"
      type="button"
      :data-testId="`year-${year}`"
      class="mx-1/12 w-11/12 rounded-lg bg-transparent text-lg text-gray-800 transition delay-150 duration-300 focus:bg-blue-100 focus:outline-hidden"
      @mousedown="emit('selectYear', year)"
      @keydown.enter="emit('selectYear', year)"
      @keydown.esc="blur"
      @focus="onFocus"
    >
      <span class="inline-block w-full rounded-lg px-8 py-3 hover:bg-blue-100">
        {{ year }}
      </span>
    </button>
  </div>
</template>
