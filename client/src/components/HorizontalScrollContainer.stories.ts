import HorizontalScrollContainer from './HorizontalScrollContainer.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof HorizontalScrollContainer> = {
  component: HorizontalScrollContainer,
  title: 'Horizontal Scroll Container',
}
export default meta

export const Default: StoryObj<typeof HorizontalScrollContainer> = {
  render: (args) => ({
    components: { HorizontalScrollContainer },
    setup() {
      return args
    },
    template: `
      <HorizontalScrollContainer class="bg-linear-to-r from-indigo-100 via-purple-100 to-pink-100">
      <div class="flex items-center h-48">
        <p class="whitespace-nowrap flex-1 font-bold text-purple-500 text-2xl uppercase">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc at elit volutpat, tempor
          ligula et, interdum nibh. Aenean non leo tincidunt, scelerisque metus sed, placerat nunc.
          Aliquam a est at mi pulvinar laoreet vel id nulla. In sit amet lectus consequat arcu
          ornare placerat.
        </p>
      </div>
      <template #fixed>
        <p class="text-8xl text-purple-50/80 grid place-content-center font-bold">This is fixed content</p>
      </template>
      </HorizontalScrollContainer>
    `,
  }),
}
