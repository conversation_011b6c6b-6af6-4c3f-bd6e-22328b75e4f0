<script setup lang="ts" generic="TOption extends Record<string, any>">
import { ComboboxItem, ComboboxItemIndicator } from 'reka-ui'
import SvgIcon from '@js/components/SvgIcon.vue'
import OverflowText from '@js/components/OverflowText.vue'
import type { Icon } from '@js/utilities/name-lists'

const { option, icon = undefined } = defineProps<{
  option: TOption
  disabled: boolean
  label: string
  icon?: Icon | undefined
}>()

defineSlots<{
  option?: (props: {
    option: TOption
    disabled: boolean
    icon: Icon | undefined
    label: string
  }) => unknown
  label?: (props: { option: TOption; disabled: boolean; label: string }) => unknown
  icon?: (props: { option: TOption; disabled: boolean; icon: Icon | undefined }) => unknown
}>()
</script>

<template>
  <ComboboxItem
    as="dd"
    :value="option"
    :disabled="disabled"
    class="flex h-8 cursor-pointer items-center gap-x-2 py-2 select-none data-disabled:pointer-events-none data-disabled:bg-gray-300 data-highlighted:bg-blue-600 data-highlighted:text-white"
    :aria-disabled="disabled"
  >
    <slot name="option" :option="option" :disabled="disabled" :icon="icon" :label="label">
      <div class="flex grow items-center gap-x-2 truncate">
        <slot name="icon" :option="option" :disabled="disabled" :icon="icon">
          <SvgIcon v-if="icon" class="form-add-on-color pointer-events-none" :icon="icon as Icon" />
        </slot>
        <slot name="label" :option="option" :disabled="disabled" :label="label">
          <OverflowText :text="label" class="grow truncate" />
        </slot>
      </div>
    </slot>

    <ComboboxItemIndicator
      :as="SvgIcon"
      icon="check-rounded"
      class="flex-none"
      aria-hidden="true"
    />
  </ComboboxItem>
</template>
