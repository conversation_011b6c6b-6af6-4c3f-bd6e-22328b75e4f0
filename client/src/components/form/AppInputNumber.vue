<script setup lang="ts">
import AppInputText from '@js/components/form/AppInputText.vue'

withDefaults(
  defineProps<{
    hasErrors?: boolean
    disabled?: boolean
  }>(),
  {
    hasErrors: false,
    disabled: false,
  }
)

const modelValue = defineModel<string | number>()
</script>

<template>
  <AppInputText
    v-model="modelValue"
    :class="['app-input-number', { disabled: disabled }]"
    :has-errors="hasErrors"
    :disabled="disabled"
  />
</template>
