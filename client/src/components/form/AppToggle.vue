<script lang="ts">
export interface AppToggleProps {
  id?: string
  name?: string
  disabled?: boolean
  hasErrors?: boolean
  required?: boolean
}
</script>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { SwitchRoot, SwitchThumb } from 'reka-ui'
import SvgIcon from '@js/components/SvgIcon.vue'

const {
  id = undefined,
  name = undefined,
  disabled = false,
  hasErrors = false,
  required = false,
} = defineProps<AppToggleProps>()

const myInput = ref()
const modelValue = defineModel<boolean>({ default: false })

const textValue = computed(() => (modelValue.value ? '1' : '0'))
watch(textValue, (newValue) => {
  myInput.value.value = newValue
  myInput.value.dispatchEvent(new Event('change', { bubbles: true }))
})
</script>

<template>
  <SwitchRoot
    v-model="modelValue"
    :disabled="disabled"
    :required="required"
    :class="[
      disabled ? 'bg-gray-200' : modelValue ? 'bg-blue-100' : 'bg-gray-200',
      'relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border border-transparent p-px transition-colors duration-200 ease-in-out focus:border-blue-600 focus:ring-1 focus:ring-blue-600 focus:outline-hidden',
      hasErrors ? 'ring-bad ring-2 outline-hidden' : '',
    ]"
  >
    <SwitchThumb
      :class="[
        modelValue ? 'translate-x-5' : 'translate-x-0',
        'pointer-events-none relative inline-block size-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out',
      ]"
    >
      <span
        :class="[
          modelValue ? 'opacity-0 duration-100 ease-out' : 'opacity-100 duration-200 ease-in',
          'absolute inset-0 flex h-full w-full items-center justify-center transition-opacity',
        ]"
        aria-hidden="true"
      >
        <SvgIcon
          icon="cross-rounded"
          size="small"
          :class="disabled ? 'text-gray-300' : 'text-gray-400'"
        />
      </span>
      <span
        :class="[
          modelValue ? 'opacity-100 duration-200 ease-in' : 'opacity-0 duration-100 ease-out',
          'absolute inset-0 flex h-full w-full items-center justify-center transition-opacity',
        ]"
        aria-hidden="true"
      >
        <SvgIcon
          size="small"
          icon="check-rounded"
          :class="disabled ? 'text-gray-400' : 'text-action'"
        />
      </span>
    </SwitchThumb>
    <input
      :id="id"
      ref="myInput"
      v-model="textValue"
      :name="name"
      type="text"
      class="hidden-no-sr behat-toggle-button-hidden-input"
      tabindex="-1"
      :required="required"
    />
  </SwitchRoot>
</template>
