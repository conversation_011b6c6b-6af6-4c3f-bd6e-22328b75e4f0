<script setup lang="ts">
import FormRow from '@js/components/form/FormRow.vue'

withDefaults(
  defineProps<{
    label?: string | false
    required?: boolean
    helpTooltip?: string
    warningTooltip?: string
    errors?: Array<string>
    loose?: boolean
  }>(),
  {
    label: false,
    required: false,
    helpTooltip: undefined,
    warningTooltip: undefined,
    errors: () => [],
  }
)
</script>

<template>
  <FormRow
    :required="required"
    :help-tooltip="helpTooltip"
    :warning-tooltip="warningTooltip"
    :label="label"
    :errors="errors"
    :class="{ 'p-0': !loose }"
    role="group"
  >
    <div class="flex flex-col" :class="[loose ? 'gap-(--app-form-field-spacing)' : 'gap-y-1.5']">
      <slot />
    </div>
  </FormRow>
</template>
