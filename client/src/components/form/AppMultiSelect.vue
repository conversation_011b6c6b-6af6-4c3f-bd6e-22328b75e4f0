<script setup lang="ts" generic="TOption extends Record<string, any>">
import {
  ComboboxAnchor,
  ComboboxCancel,
  ComboboxContent,
  ComboboxGroup,
  ComboboxInput,
  ComboboxLabel,
  ComboboxPortal,
  ComboboxRoot,
  ComboboxTrigger,
  ComboboxViewport,
  useFilter,
} from 'reka-ui'
import { computed, ref, useTemplateRef, watch } from 'vue'
import { debouncedRef, useFocus } from '@vueuse/core'
import isObject from 'lodash/isObject'
import Translator from '@js/translator'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppSelectItem from '@js/components/form/AppSelectItem.vue'
import AppMessage from '@js/components/AppMessage.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppChip from '@js/components/AppChip.vue'
import { useComboboxOptions } from '@js/composable/useComboboxOptions'
import OverflowText from '@js/components/OverflowText.vue'
import type { ModelRef, Ref } from 'vue'
import type { Icon } from '@js/utilities/name-lists'
import type {
  MultiSelectSlots,
  SelectGroupConfigurations,
  SelectIcon,
  SelectLabel,
  SelectSort,
} from '@js/model/select'

defineSlots<MultiSelectSlots<TOption>>()

const {
  allowCreate = false,
  disabled = false,
  hasErrors = false,
  id = undefined,
  name = undefined,
  options = [],
  placeholder = Translator.trans('u2.select_option'),
  readonly = false,
  loading = false,
  required = false,

  groupConfigurations = {},

  disabledKey = (option: TOption) =>
    isObject(option) && 'disabled' in option && option.disabled === true,

  valueKey = 'id',
  iconKey = undefined,
  labelKey = (option: TOption) =>
    isObject(option) && 'name' in option ? (option.name as string) : undefined,

  sortKey = undefined,

  displayValueCallback = undefined,
  clearable = true,
  maximumNumberOfOptions = 150,
  hideSelected = false,
} = defineProps<{
  hideSelected?: boolean
  allowCreate?: boolean
  clearable?: boolean
  disabled?: boolean
  hasErrors?: boolean
  id?: string
  name?: string
  placeholder?: string
  readonly?: boolean
  loading?: boolean
  required?: boolean

  options?: Array<TOption>

  groupConfigurations?: SelectGroupConfigurations<TOption>

  displayValueCallback?: (option: TOption) => string

  disabledKey?: keyof TOption | ((option: TOption) => boolean)
  iconKey?: SelectIcon<TOption>
  labelKey?: SelectLabel<TOption>
  sortKey?: SelectSort<TOption>
  valueKey?: keyof TOption

  maximumNumberOfOptions?: number
}>()

defineEmits<{
  (event: 'update:modelValue', payload: typeof modelValue): void
  (event: 'update:internalSearchQuery', payload: typeof internalSearchQuery): void
}>()

const modelValue = defineModel<Array<TOption[keyof TOption]>>('modelValue', {
  required: false,
  default: [],
})
const searchQuery = defineModel<string | undefined>('searchQuery', { required: false })

const selectedOptions = ref([]) as Ref<Array<TOption>>
const originalSelectedOptions = ref<Array<TOption[keyof TOption]>>(modelValue.value)

const comboboxInput = ref()
const { focused: isComboBoxFocused } = useFocus(comboboxInput)

const useDebouncedSearchQueryModelValue = (searchQuery: ModelRef<unknown>) => {
  const internalSearchQuery = ref<string>('')
  const debouncedSearchQuery = debouncedRef(internalSearchQuery, 400)
  watch(debouncedSearchQuery, (newSearchQuery) => {
    searchQuery.value = newSearchQuery
  })

  return { internalSearchQuery }
}
const { internalSearchQuery } = useDebouncedSearchQueryModelValue(searchQuery)

function resolveDisabledState(option: TOption) {
  return typeof disabledKey === 'function'
    ? (disabledKey(option) ?? false)
    : (option[disabledKey] ?? false)
}

function resolveLabel(option: TOption): string {
  const labelFallback = option[valueKey] ?? displayValueCallback?.(option) ?? ''

  return typeof labelKey === 'function'
    ? (labelKey(option) ?? labelFallback)
    : (option[labelKey] ?? labelFallback)
}

function resolveIcon(option: TOption): Icon | undefined {
  return iconKey ? (typeof iconKey === 'function' ? iconKey(option) : option[iconKey]) : undefined
}

const open = ref(false)
const showClearIcon = computed(() => selectedOptions.value.length > 0 && !disabled && clearable)

const { contains } = useFilter({ sensitivity: 'base' })
const filteredOptions = computed(() =>
  options.filter((option) => contains(resolveLabel(option), internalSearchQuery.value))
)

const comboboxOptions = useComboboxOptions(
  computed(() => {
    if (hideSelected) {
      return filteredOptions.value.filter((option) => !modelValue.value.includes(option[valueKey]))
    }

    const missingOptions = selectedOptions.value.filter(
      (option) => !filteredOptions.value.find((o) => o[valueKey] === option[valueKey])
    )

    return [...filteredOptions.value, ...missingOptions]
  }),
  computed(() => sortKey),
  computed(() => groupConfigurations),
  computed(() => maximumNumberOfOptions)
)

const optionsCountExceedsMaximumNumberOfOptions = computed(() => {
  return filteredOptions.value.length > maximumNumberOfOptions
})

watch(selectedOptions, (selectedOptionsNew) => {
  modelValue.value = selectedOptionsNew.map((option) => option[valueKey])
  internalSearchQuery.value = ''
})

const modelValueAsOptions = computed(() => {
  return modelValue.value
    .map((value) => options.find((option) => value === option[valueKey]))
    .filter((option) => !!option)
})
watch(
  modelValueAsOptions,
  (modelValueAsOptionsNew) => {
    if (
      modelValue.value.length === 0 ||
      selectedOptions.value.length > 0 ||
      modelValueAsOptionsNew.length === 0
    ) {
      return
    }

    selectedOptions.value = modelValueAsOptionsNew ?? []
  },
  { immediate: true }
)

const iconBySelectedOptionValue = computed(() => {
  return new Map(selectedOptions.value.map((option) => [option[valueKey], resolveIcon(option)]))
})

function onClear() {
  originalSelectedOptions.value = selectedOptions.value.map((option) => option[valueKey])
  selectedOptions.value = []

  if (comboboxButton.value && !open.value) {
    comboboxButton.value.$el.click()
  }
}

function onComboboxInputEsc() {
  if (open.value) {
    return
  }
  if (originalSelectedOptions.value.length > 0) {
    selectedOptions.value = originalSelectedOptions.value
      .map((value) => options.find((option) => option[valueKey] === value))
      .filter((option) => !!option)
  }
}

function onComboboxInputDelete() {
  if (selectedOptions.value.length > 0 && comboboxInput.value.$el.selectionEnd === 0) {
    if (activeOption.value) {
      const activeOptionIndex = selectedOptions.value.findIndex(
        (selectedOption) => selectedOption[valueKey] === activeOption.value?.[valueKey]
      )
      selectedOptions.value = selectedOptions.value.filter(
        (option) => option[valueKey] !== activeOption.value?.[valueKey]
      )
      activeOption.value = selectedOptions.value[activeOptionIndex]
    } else {
      const lastOption = selectedOptions.value[selectedOptions.value.length - 1]
      selectedOptions.value = selectedOptions.value.filter(
        (option) => option[valueKey] !== lastOption[valueKey]
      )
      activeOption.value = undefined
    }
  }
  searchQuery.value = undefined
}

function onComboboxInputFocus() {
  if (typeof comboboxInput.value.$el.select === 'function' && comboboxButton.value && !open.value) {
    comboboxInput.value.$el.select()
  }
  activeOption.value = undefined
}

const comboboxButton = useTemplateRef('comboboxButton')
function onClickComboboxInput() {
  if (activeOption.value) {
    activeOption.value = undefined
  }

  if (comboboxButton.value && !open.value) {
    comboboxButton.value.$el.click()
  }
}

const onComboboxAnchorMouseDown = (event: MouseEvent) => {
  // Do not inline isTextSelected, we need to know if text is selected before the input is focused
  const isTextSelected =
    comboboxInput.value.$el.selectionEnd !== comboboxInput.value.$el.selectionStart
  if ((event.target as HTMLElement).tagName !== 'INPUT') {
    event.preventDefault()
    comboboxInput.value.$el.focus()
  }

  if (isTextSelected) {
    unselectInputText()
  }
}

function onComboboxInputBlur() {
  originalSelectedOptions.value = []
  // We need to clear search query on blur only in case we don't have any search results with the current search query.
  // Blur event is triggered when an option is selected with a click. If we clear search query before the click happens, model value doesn't get updated in some cases. We need to make sure that blur or focus event handlers clear search query only when it is needed (e.g. on click outside or after model value is updated)
  if (comboboxOptions.value.length === 0) {
    // Clearing search query needs to be delayed to prevent flickering of the options list on click outside
    setTimeout(() => {
      internalSearchQuery.value = ''
    }, 400)
  }
  activeOption.value = undefined
  originalSelectedOptions.value = modelValue.value
}

const showSearchIcon = computed(() => {
  return showPlaceholder.value || !!internalSearchQuery.value
})
const activeOption = ref<TOption>()
const isReadonly = computed(() => readonly || loading || !!activeOption.value)

const optionsWithoutSelected = computed(() =>
  options.filter((option) => !modelValue.value.includes(option[valueKey]))
)
const showEmptyMessage = computed(
  () => (hideSelected ? optionsWithoutSelected.value.length : comboboxOptions.value.length) === 0
)
const deselectOption = (option: TOption) => {
  const newActiveOptionIndex = activeOption.value
    ? selectedOptions.value.findIndex(
        (selectedOption) => selectedOption[valueKey] === activeOption.value?.[valueKey]
      )
    : undefined

  selectedOptions.value = selectedOptions.value.filter(
    (selectedOption) => selectedOption[valueKey] !== option[valueKey]
  )

  activeOption.value =
    newActiveOptionIndex !== undefined ? selectedOptions.value[newActiveOptionIndex] : undefined
}

function onArrowKeyLeft() {
  if (selectedOptions.value.length > 0 && activeOption.value) {
    const index = selectedOptions.value.findIndex(
      (option) => option[valueKey] === activeOption.value?.[valueKey]
    )
    if (index === 0) {
      activeOption.value = undefined
      return
    }
    activeOption.value = selectedOptions.value[index - 1]
    return
  }
  if (selectedOptions.value.length > 0 && comboboxInput.value.$el.selectionEnd === 0) {
    activeOption.value = selectedOptions.value[selectedOptions.value.length - 1]
  }
}

function onArrowKeyRight() {
  if (selectedOptions.value.length > 0 && activeOption.value) {
    const index = selectedOptions.value.findIndex(
      (option) => option[valueKey] === activeOption.value?.[valueKey]
    )
    if (index === selectedOptions.value.length - 1) {
      activeOption.value = undefined
      return
    }
    activeOption.value = selectedOptions.value[index + 1]
    return
  }
  if (selectedOptions.value.length > 0 && comboboxInput.value.$el.selectionEnd === 0) {
    if (activeOption.value) {
      activeOption.value = undefined
    }
  }
}
const { focused: isComboboxFocused } = useFocus(comboboxInput)

const showPlaceholder = computed(() => {
  return (
    (isComboboxFocused.value && !internalSearchQuery.value) ||
    (selectedOptions.value.length === 0 && !isComboboxFocused.value)
  )
})

function unselectInputText() {
  comboboxInput.value.$el.selectionStart = comboboxInput.value.$el.selectionEnd
}

const setActiveOption = (option: TOption) => {
  if (activeOption.value?.[valueKey] !== option[valueKey]) {
    activeOption.value = option
    unselectInputText()
    return
  }
  activeOption.value = undefined
}

const showNonExistingOption = computed(() => {
  if (!allowCreate) {
    return false
  }

  if (internalSearchQuery.value.trim()) {
    return !options.some((option) => resolveLabel(option) === internalSearchQuery.value)
  }

  return false
})
</script>

<template>
  <ComboboxRoot
    v-model="selectedOptions"
    v-model:open="open"
    :disabled="disabled"
    multiple
    class="relative"
    :by="valueKey as string"
    :ignore-filter="true"
  >
    <ComboboxAnchor
      :data-has-errors="hasErrors"
      class="js-enableable-container rounded-skin-base border-skin-base-width shadow-skin-base relative flex px-4 py-2"
      :class="[
        {
          'has-errors': hasErrors,
          'border-skin-focus ring-skin-base hover:border-skin-focus ring-1':
            !hasErrors && isComboBoxFocused,
          'border-bad ring-bad hover:border-bad ring-1': hasErrors && isComboBoxFocused,
          'border-skin-base hover:border-skin-hover': !hasErrors && !isComboBoxFocused,
        },
        disabled ? 'bg-skin-disabled' : 'bg-white',
      ]"
      @mousedown="onComboboxAnchorMouseDown"
    >
      <div
        class="inline-flex h-auto grow flex-wrap content-start items-center gap-1.5 overflow-hidden"
      >
        <template v-for="option in selectedOptions" :key="option[valueKey] + '-selected-value'">
          <div @click="setActiveOption(option)">
            <slot
              name="selectedValue"
              :option="option"
              :active-option="activeOption"
              :clear="deselectOption"
            >
              <AppChip
                :aria-label="resolveLabel(option)"
                :class="[
                  'cursor-pointer gap-1',
                  { 'brightness-90': activeOption?.[valueKey] === option[valueKey] },
                ]"
                :close="!disabled"
                :title="resolveLabel(option)"
                class="truncate"
                @close="deselectOption(option)"
              >
                <span
                  v-if="iconBySelectedOptionValue.get(option[valueKey])"
                  class="pointer-events-none flex"
                >
                  <slot
                    name="optionIcon"
                    :option="activeOption"
                    :icon="iconBySelectedOptionValue.get(option[valueKey])"
                    :disabled="false"
                  >
                    <SvgIcon
                      class="form-add-on-color pointer-events-none"
                      :icon="iconBySelectedOptionValue.get(option[valueKey]) as Icon"
                    />
                  </slot>
                </span>

                <span class="truncate">
                  {{ resolveLabel(option) }}
                </span>
              </AppChip>
            </slot>
          </div>
        </template>

        <span v-if="showSearchIcon" class="pointer-events-none flex">
          <SvgIcon icon="search" class="form-add-on-color" />
        </span>

        <ComboboxInput
          :id="id"
          ref="comboboxInput"
          v-model="internalSearchQuery"
          :aria-label="name + '-combobox'"
          :aria-multiselectable="true"
          :class="[
            { 'text-gray-400': showPlaceholder },
            'min-w-0 flex-1 truncate rounded-none border-none bg-inherit p-0 shadow-none hover:border-transparent focus:border-none focus:shadow-none focus:ring-0',
          ]"
          :readonly="isReadonly"
          :placeholder="showPlaceholder ? placeholder : undefined"
          @click="onClickComboboxInput"
          @keydown.delete="onComboboxInputDelete"
          @keydown.esc="onComboboxInputEsc"
          @keydown.left="onArrowKeyLeft"
          @keydown.right="onArrowKeyRight"
          @focus="onComboboxInputFocus"
          @blur="onComboboxInputBlur"
        >
        </ComboboxInput>
        <!-- Support for html forms-->
        <input
          v-if="modelValue.length === 0"
          type="text"
          class="hidden-no-sr"
          :name="name"
          :required="required"
          :disabled="disabled"
          tabindex="-1"
        />
        <input
          v-for="option in modelValue"
          :key="option + '-hidden'"
          type="text"
          class="hidden-no-sr"
          :name="name"
          :value="option"
          :required="required"
          :disabled="disabled"
          tabindex="-1"
        />
      </div>

      <AppLoader v-if="loading" size="small" />

      <ComboboxCancel
        v-if="showClearIcon"
        :aria-label="Translator.trans('u2.clear_all')"
        class="js-enableable-cross -my-2 flex cursor-pointer items-center rounded-r-md py-2 pl-2 text-gray-400 hover:text-gray-800 focus:outline-hidden"
        @click="onClear"
      >
        <SvgIcon icon="cross" />
      </ComboboxCancel>

      <div class="-my-2 flex cursor-pointer items-center py-2 text-gray-400 hover:text-gray-800">
        <ComboboxTrigger
          ref="comboboxButton"
          :as="SvgIcon"
          :disabled="disabled"
          :class="[disabled ? 'cursor-not-allowed' : 'cursor-pointer']"
          :icon="open ? 'arrow-up' : 'arrow-down'"
          @click.stop
        />
      </div>
    </ComboboxAnchor>

    <ComboboxPortal>
      <ComboboxContent
        :side-offset="4"
        align="start"
        position="popper"
        class="combobox-content-min-width max-h-[30vw] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden"
      >
        <ComboboxViewport as="dl">
          <div v-if="showEmptyMessage" class="w-full rounded-none">
            <AppMessage class="w-full rounded-none">
              {{ Translator.trans('u2_table.no_results_that_match_search') }}
            </AppMessage>
          </div>

          <template v-for="group in comboboxOptions" :key="group.label">
            <ComboboxGroup v-if="group.label">
              <ComboboxLabel as="dd" class="h-8 px-4 py-2">
                <span class="font-bold text-gray-700">{{ group.label }}</span>
              </ComboboxLabel>

              <AppSelectItem
                v-for="option in group.options"
                :key="option[valueKey]"
                :option="option"
                :icon="resolveIcon(option)"
                :label="resolveLabel(option)"
                :disabled="resolveDisabledState(option)"
                class="pr-4 pl-6"
              >
                <template v-if="!!$slots.optionIcon" #icon="slotProps">
                  <slot name="optionIcon" v-bind="slotProps" />
                </template>
                <template v-if="!!$slots.optionLabel" #label="slotProps">
                  <slot name="optionLabel" v-bind="slotProps" />
                </template>
                <template v-if="!!$slots.option" #option="slotProps">
                  <slot name="option" v-bind="slotProps" />
                </template>
              </AppSelectItem>
            </ComboboxGroup>

            <AppSelectItem
              v-for="option in group.options"
              v-else
              :key="option[valueKey]"
              :option="option"
              :icon="resolveIcon(option)"
              :label="resolveLabel(option)"
              class="px-4"
              :disabled="resolveDisabledState(option)"
            >
              <template v-if="!!$slots.optionIcon" #icon="slotProps">
                <slot name="optionIcon" v-bind="slotProps" />
              </template>
              <template v-if="!!$slots.optionLabel" #label="slotProps">
                <slot name="optionLabel" v-bind="slotProps" />
              </template>
              <template v-if="!!$slots.option" #option="slotProps">
                <slot name="option" v-bind="slotProps" />
              </template>
            </AppSelectItem>
          </template>

          <AppSelectItem
            v-if="showNonExistingOption"
            :key="internalSearchQuery"
            :option="
              {
                [valueKey]: internalSearchQuery,
              } as TOption
            "
            :label="internalSearchQuery"
            class="px-4"
            :disabled="false"
          >
            <template v-if="!!$slots.optionIcon" #icon="slotProps">
              <slot name="optionIcon" v-bind="slotProps" />
            </template>
            <template #label>
              <OverflowText :text="internalSearchQuery" class="grow truncate" />
            </template>
            <template v-if="!!$slots.option" #option="slotProps">
              <slot name="option" v-bind="slotProps" />
            </template>
          </AppSelectItem>
        </ComboboxViewport>
        <div
          v-if="optionsCountExceedsMaximumNumberOfOptions"
          class="w-full rounded-none bg-blue-100 p-1 text-sm text-blue-900"
        >
          {{ Translator.trans('u2.search.refine_search') }}
        </div>
      </ComboboxContent>
    </ComboboxPortal>
  </ComboboxRoot>
</template>

<style scoped>
:deep(.combobox-content-min-width) {
  max-width: 50vw;

  /* noinspection CssUnresolvedCustomProperty */
  min-width: var(--reka-combobox-trigger-width);
}

[data-reka-combobox-viewport] {
  scrollbar-width: thin !important;
}
</style>
