<script setup lang="ts" generic="TOption extends Record<string, any>">
import {
  ComboboxAnchor,
  ComboboxCancel,
  ComboboxContent,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxInput,
  ComboboxLabel,
  ComboboxPortal,
  ComboboxRoot,
  ComboboxTrigger,
  ComboboxViewport,
  useFilter,
} from 'reka-ui'
import { computed, nextTick, onBeforeMount, ref, useTemplateRef, watch } from 'vue'
import type { ModelRef, Ref } from 'vue'
import { debouncedRef, useFocus } from '@vueuse/core'
import isObject from 'lodash/isObject'
import Translator from '@js/translator'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppSelectItem from '@js/components/form/AppSelectItem.vue'
import AppMessage from '@js/components/AppMessage.vue'
import { useComboboxOptions } from '@js/composable/useComboboxOptions'
import OverflowText from '@js/components/OverflowText.vue'
import type {
  SelectGroupConfigurations,
  SelectIcon,
  SelectLabel,
  SelectSlots,
  SelectSort,
} from '@js/model/select'
import type { Icon } from '@js/utilities/name-lists'

const {
  allowCreate = false,
  disabled = false,
  enableFilter = true,
  hasErrors = false,
  id = undefined,
  name = undefined,
  options = [],
  placeholder = Translator.trans('u2.select_option'),
  readonly = false,
  required = false,
  loading = false,

  groupConfigurations = {},

  disabledKey = (option: TOption) =>
    isObject(option) && 'disabled' in option && option.disabled === true,

  valueKey = 'id',
  iconKey = undefined,
  labelKey = (option: TOption) =>
    isObject(option) && 'name' in option ? String(option.name) : undefined,

  sortKey = undefined,
  displayValueCallback = undefined,
  maximumNumberOfOptions = 150,
} = defineProps<{
  allowCreate?: boolean
  disabled?: boolean
  enableFilter?: boolean
  hasErrors?: boolean
  id?: string
  name?: string
  placeholder?: string
  readonly?: boolean
  required?: boolean
  loading?: boolean

  options?: Array<TOption>

  groupConfigurations?: SelectGroupConfigurations<TOption>

  displayValueCallback?: (option: TOption) => string

  disabledKey?: keyof TOption | ((option: TOption) => boolean)
  iconKey?: SelectIcon<TOption>
  labelKey?: SelectLabel<TOption>
  sortKey?: SelectSort<TOption>
  valueKey?: keyof TOption

  maximumNumberOfOptions?: number
}>()

const slots = defineSlots<SelectSlots<TOption>>()

const modelValue = defineModel<TOption[keyof TOption] | null>('modelValue', {
  required: false,
  default: null,
})
const searchQuery = defineModel<string>('searchQuery', { required: false, default: '' })
const open = defineModel<boolean>('open', { required: false, default: false })

const selectedOption = ref<TOption | undefined>()

const comboboxInput = ref()
const { focused: isComboBoxFocused } = useFocus(comboboxInput)
const originalSelectedOption = ref()

const useDebouncedSearchQueryModelValue = (
  searchQuery: ModelRef<unknown>,
  selectedOption: Ref<TOption | undefined>,
  getDisplayValue: (selectedValue: TOption | undefined) => string
) => {
  const internalSearchQuery = ref<string | undefined>()
  const debouncedSearchQuery = debouncedRef(internalSearchQuery, 400)
  watch(debouncedSearchQuery, (newSearchQuery) => {
    searchQuery.value =
      newSearchQuery === getDisplayValue(selectedOption.value) ? '' : newSearchQuery
  })

  return { internalSearchQuery }
}
const { internalSearchQuery } = useDebouncedSearchQueryModelValue(
  searchQuery,
  selectedOption,
  getDisplayValue
)

const showSelectedValue = computed(
  () => hasSelectedValueSlot.value && selectedOption.value && !internalSearchQuery.value
)
const resolvedPlaceholder = computed(() =>
  selectedOption.value || !!internalSearchQuery.value ? undefined : placeholder
)

const showClearButton = computed(() => selectedOption.value && !disabled)
const showNoResultsMessage = computed(() => !loading && !allowCreate && options.length === 0)
const showNonExistingOption = computed(() => {
  if (!allowCreate || !internalSearchQuery.value) {
    return false
  }

  return !options.some((option) => resolveLabel(option) === internalSearchQuery.value)
})

function resolveDisabledState(option: TOption) {
  return typeof disabledKey === 'function'
    ? (disabledKey(option) ?? false)
    : (option[disabledKey] ?? false)
}

function resolveLabel(option: TOption): string {
  const labelFallback = option[valueKey] ?? displayValueCallback?.(option) ?? ''

  return typeof labelKey === 'function'
    ? (labelKey(option) ?? labelFallback)
    : (option[labelKey] ?? labelFallback)
}

function resolveIcon(option: TOption): Icon | undefined {
  if (!option) {
    return undefined
  }

  if (!iconKey) {
    return undefined
  }

  if (typeof iconKey === 'function') {
    return iconKey(option)
  }

  return option[iconKey]
}
const selectedOptionIcon = computed(() => selectedOption.value && resolveIcon(selectedOption.value))

const { contains } = useFilter({ sensitivity: 'base' })

const selectedValueNotInOptions = computed(() => {
  if (!modelValue.value) {
    return false
  }

  if (options.find((option) => modelValue.value === option[valueKey])) {
    return false
  }

  return !allowCreate
})

const filteredOptions = computed(() => {
  const internalSearch = internalSearchQuery.value
  if (!enableFilter || !internalSearch || selectedValueNotInOptions.value) {
    return options
  }

  if (internalSearch === getDisplayValue(selectedOption.value)) {
    return options
  }

  return options.filter((option) => contains(resolveLabel(option), internalSearch.trim()))
})

const optionsCountExceedsMaximumNumberOfOptions = computed(
  () => filteredOptions.value.length > maximumNumberOfOptions
)

const comboboxOptions = useComboboxOptions(
  filteredOptions,
  computed(() => sortKey),
  computed(() => groupConfigurations),
  computed(() => maximumNumberOfOptions)
)

function setSelectionOptionFromModelValue(
  newModelValue: TOption[keyof TOption] | null,
  newOptions: Array<TOption>
) {
  if (!newModelValue) {
    selectedOption.value = undefined
    return
  }

  if (selectedOption.value && selectedOption.value[valueKey] === newModelValue) {
    return
  }
  selectedOption.value = newOptions.find((option) => newModelValue === option[valueKey])
}
onBeforeMount(() => {
  setSelectionOptionFromModelValue(modelValue.value, options)
})

watch([modelValue, () => options], ([newModelValue, newOptions]) => {
  setSelectionOptionFromModelValue(newModelValue, newOptions)
})

function onValueSelected(option: TOption | undefined) {
  modelValue.value = option?.[valueKey] ?? null
}

const hasSelectedValueSlot = computed(() => !!slots.selectedValue)
const isValueInSelectedValueSlotHighlighted = computed(
  () =>
    hasSelectedValueSlot.value &&
    !!selectedOption.value &&
    isComboBoxFocused.value &&
    !internalSearchQuery.value
)

function onClear() {
  originalSelectedOption.value = selectedOption.value
  selectedOption.value = undefined
  searchQuery.value = ''
  internalSearchQuery.value = ''

  if (comboboxButton.value && !open.value) {
    comboboxButton.value.$el.click()
  }
}

function onEsc(event: KeyboardEvent) {
  if (open.value) {
    return
  }

  if (originalSelectedOption.value && !selectedOption.value) {
    nextTick(() => {
      selectedOption.value = originalSelectedOption.value
      originalSelectedOption.value = undefined
    })
    event.preventDefault()
  }
}

function onDelete() {
  if (
    comboboxInput.value.$el.selectionStart === 0 &&
    comboboxInput.value.$el.selectionEnd === comboboxInput.value.$el.value.length
  ) {
    originalSelectedOption.value = selectedOption.value
    selectedOption.value = undefined
    searchQuery.value = ''
  }
}

function onFocus() {
  if (typeof comboboxInput.value.$el.select === 'function') {
    comboboxInput.value.$el.select()
  }
}

const comboboxButton = useTemplateRef('comboboxButton')
function onClick() {
  if (comboboxButton.value && !open.value) {
    comboboxButton.value.$el.click()
  }
}

function onBlur() {
  originalSelectedOption.value = undefined
}

function getDisplayValue(selectedValue: TOption | undefined) {
  if (selectedValueNotInOptions.value) {
    return Translator.trans('u2.no_matching_option_found')
  }

  if (hasSelectedValueSlot.value || !selectedValue) {
    return ''
  }

  return displayValueCallback
    ? displayValueCallback(selectedValue)
    : (resolveLabel(selectedValue) ?? '')
}

const comboboxRoot = useTemplateRef('comboboxRoot')

/**
 * This is an intermediate solution to fix https://github.com/unovue/reka-ui/issues/1853
 */
watch(comboboxOptions, (newOptions) => {
  if (
    comboboxRoot.value &&
    newOptions.length > 0 &&
    comboboxRoot.value.highlightFirstItem !== undefined
  ) {
    comboboxRoot.value.highlightFirstItem()
  }
})

/**
 * This is an intermediate solution to fix https://github.com/unovue/reka-ui/issues/1873
 */
function getDisplayValueForInputAndUpdateSearchQuery(selectedValue: TOption | undefined) {
  const displayValue = getDisplayValue(selectedValue)
  if (displayValue !== internalSearchQuery.value) {
    internalSearchQuery.value = displayValue
  }
  return displayValue
}
</script>

<template>
  <ComboboxRoot
    ref="comboboxRoot"
    v-model="selectedOption"
    v-model:open="open"
    :disabled="disabled"
    class="relative"
    :by="valueKey as string"
    :ignore-filter="true"
    @update:model-value="onValueSelected"
  >
    <ComboboxAnchor
      :data-has-errors="hasErrors"
      class="js-enableable-container rounded-skin-base border-skin-base-width shadow-skin-base relative flex px-4 py-2"
      :class="[
        {
          'has-errors': hasErrors,
          'border-skin-focus ring-skin-base hover:border-skin-focus ring-1':
            !hasErrors && isComboBoxFocused,
          'border-bad ring-bad hover:border-bad ring-1': hasErrors && isComboBoxFocused,
          'border-skin-base hover:border-skin-hover': !hasErrors && !isComboBoxFocused,
        },
        disabled ? 'bg-skin-disabled' : 'bg-white',
      ]"
      @click="onClick"
    >
      <div class="inline-flex h-auto min-w-0 grow content-start items-center gap-1.5">
        <span v-if="open || !modelValue" class="pointer-events-none flex">
          <SvgIcon icon="search" class="form-add-on-color" />
        </span>
        <span
          v-else-if="selectedOptionIcon || !!$slots.optionIcon"
          class="pointer-events-none flex"
        >
          <slot
            name="optionIcon"
            :option="selectedOption"
            :icon="selectedOptionIcon"
            :disabled="false"
          >
            <SvgIcon
              v-if="selectedOptionIcon"
              class="form-add-on-color pointer-events-none"
              :icon="selectedOptionIcon"
            />
          </slot>
        </span>

        <span v-if="selectedValueNotInOptions && !open" class="flex">
          <SvgIcon
            v-tooltip="Translator.trans('u2.no_matching_option_found.help')"
            icon="alert"
            class="form-add-on-color"
          />
        </span>

        <span
          v-if="selectedOption && showSelectedValue"
          :aria-label="selectedOption.name"
          class="truncate"
        >
          <slot
            name="selectedValue"
            :option="selectedOption"
            :is-highlighted="isValueInSelectedValueSlotHighlighted"
          />
        </span>

        <ComboboxInput
          :id="id"
          ref="comboboxInput"
          v-model="internalSearchQuery"
          :aria-label="name + '-combobox'"
          :class="[
            { 'caret-transparent': isValueInSelectedValueSlotHighlighted },
            { 'text-gray-400': !!resolvedPlaceholder },
            'min-w-0 flex-1 truncate rounded-none border-none bg-inherit p-0 shadow-none hover:border-transparent focus:border-none focus:shadow-none focus:ring-0',
          ]"
          :readonly="readonly"
          :placeholder="resolvedPlaceholder"
          :display-value="getDisplayValueForInputAndUpdateSearchQuery"
          @keydown.delete="onDelete"
          @keydown.esc="onEsc"
          @focus="onFocus"
          @blur="onBlur"
        />

        <!-- Support for html forms-->
        <input
          :key="modelValue + '-hidden'"
          type="text"
          class="hidden-no-sr"
          :name="name"
          :value="modelValue"
          :required="required"
          :disabled="disabled"
          tabindex="-1"
        />

        <AppLoader v-if="loading" size="small" />

        <ComboboxCancel
          v-show="showClearButton"
          class="js-enableable-cross -my-2 flex cursor-pointer items-center rounded-r-md py-2 pl-2 text-gray-400 hover:text-gray-800 focus:outline-hidden"
          @click="onClear"
        >
          <SvgIcon icon="cross" />
        </ComboboxCancel>

        <ComboboxTrigger
          ref="comboboxButton"
          :disabled="disabled"
          :class="[
            disabled ? 'cursor-not-allowed' : 'cursor-pointer',
            'js-enableable-button flex items-center rounded-r-md text-gray-400 focus:outline-hidden',
          ]"
          @click.stop
        >
          <SvgIcon :icon="open ? 'arrow-up' : 'arrow-down'" />
        </ComboboxTrigger>
      </div>
    </ComboboxAnchor>

    <ComboboxPortal>
      <ComboboxContent
        :side-offset="4"
        align="start"
        position="popper"
        class="combobox-content-min-width max-h-[25vw] overflow-auto rounded-md bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden"
      >
        <ComboboxViewport as="dl">
          <ComboboxEmpty v-if="showNoResultsMessage" class="w-full rounded-none">
            <AppMessage class="w-full rounded-none">
              {{ Translator.trans('u2_table.no_results_that_match_search') }}
            </AppMessage>
          </ComboboxEmpty>

          <template v-for="group in comboboxOptions" :key="group.label">
            <ComboboxGroup v-if="group.label">
              <ComboboxLabel as="dd" class="h-8 px-4 py-2">
                <span class="font-bold text-gray-700">{{ group.label }}</span>
              </ComboboxLabel>
              <AppSelectItem
                v-for="option in group.options"
                :key="option[valueKey]"
                :option="option"
                :icon="resolveIcon(option)"
                :label="resolveLabel(option)"
                :disabled="resolveDisabledState(option)"
                class="pr-4 pl-6"
              >
                <template v-if="!!$slots.optionIcon" #icon="slotProps">
                  <slot name="optionIcon" v-bind="slotProps" />
                </template>
                <template v-if="!!$slots.optionLabel" #label="slotProps">
                  <slot name="optionLabel" v-bind="slotProps" />
                </template>
                <template v-if="!!$slots.option" #option="slotProps">
                  <slot name="option" v-bind="slotProps" />
                </template>
              </AppSelectItem>
            </ComboboxGroup>

            <AppSelectItem
              v-for="option in group.options"
              v-else
              :key="option[valueKey]"
              :option="option"
              :icon="resolveIcon(option)"
              :label="resolveLabel(option)"
              class="px-4"
              :disabled="resolveDisabledState(option)"
            >
              <template v-if="!!$slots.optionIcon" #icon="slotProps">
                <slot name="optionIcon" v-bind="slotProps" />
              </template>
              <template v-if="!!$slots.optionLabel" #label="slotProps">
                <slot name="optionLabel" v-bind="slotProps" />
              </template>
              <template v-if="!!$slots.option" #option="slotProps">
                <slot name="option" v-bind="slotProps" />
              </template>
            </AppSelectItem>
          </template>

          <AppSelectItem
            v-if="showNonExistingOption && internalSearchQuery"
            :key="internalSearchQuery"
            :option="
              {
                [valueKey]: internalSearchQuery,
              } as TOption
            "
            :label="internalSearchQuery"
            class="px-4"
            :disabled="false"
          >
            <template v-if="!!$slots.optionIcon" #icon="slotProps">
              <slot name="optionIcon" v-bind="slotProps" />
            </template>
            <template #label>
              <OverflowText :text="internalSearchQuery" class="grow truncate" />
            </template>
            <template v-if="!!$slots.option" #option="slotProps">
              <slot name="option" v-bind="slotProps" />
            </template>
          </AppSelectItem>
        </ComboboxViewport>
        <div
          v-if="optionsCountExceedsMaximumNumberOfOptions"
          class="w-full rounded-none bg-blue-100 p-1 text-sm text-blue-900"
        >
          {{ Translator.trans('u2.search.refine_search') }}
        </div>
      </ComboboxContent>
    </ComboboxPortal>
  </ComboboxRoot>
</template>

<style scoped>
:deep(.combobox-content-min-width) {
  max-width: 50vw;

  /* noinspection CssUnresolvedCustomProperty */
  min-width: var(--reka-combobox-trigger-width);
}

[data-reka-combobox-viewport] {
  scrollbar-width: thin !important;
}
</style>
