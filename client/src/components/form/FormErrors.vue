<script setup lang="ts">
import { computed } from 'vue'
import SvgIcon from '@js/components/SvgIcon.vue'

const props = defineProps<{
  errors?: Array<string>
}>()

const errors = computed(() => props.errors)
</script>

<template>
  <div v-if="errors?.length">
    <div
      v-for="error in errors"
      :key="error"
      class="text-bad my-1 px-0 text-sm leading-normal"
      role="alert"
    >
      <SvgIcon icon="no" size="small" class="align-middle" />
      <span class="align-middle">{{ error }}</span>
    </div>
  </div>
</template>
