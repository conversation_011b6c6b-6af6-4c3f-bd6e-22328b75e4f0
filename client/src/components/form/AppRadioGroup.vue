<script lang="ts">
export interface RadioOption<TModel extends string | number> {
  id: TModel
  name: string
  disabled?: boolean
  warning?: string | false
  help?: string | false
}

export interface AppRadioGroupProps<TModel extends string | number> {
  id?: string
  name?: string
  horizontal?: boolean
  disabled?: boolean
  options: Array<RadioOption<TModel>>
}
</script>

<script setup lang="ts" generic="TModel extends string | number">
import FormLabel from '@js/components/form/FormLabel.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

const {
  id = undefined,
  name = undefined,
  horizontal = false,
  disabled = false,
} = defineProps<AppRadioGroupProps<TModel>>()

const modelValue = defineModel<TModel>({ required: true })
</script>

<template>
  <div
    :class="['inline-flex gap-3 py-1 leading-none', { 'flex-col': !horizontal }]"
    role="radiogroup"
  >
    <div
      v-for="option in options"
      :key="option.id"
      class="inline-flex items-center gap-1 whitespace-nowrap"
    >
      <input
        :id="`${id ?? option.id}_${option.id}`"
        v-model="modelValue"
        type="radio"
        :name="name"
        :value="option.id"
        :disabled="option.disabled || disabled"
      />

      <FormLabel
        class="text-off-black inline align-middle text-base font-normal"
        :for="`${id ?? option.id}_${option.id}`"
      >
        {{ option.name }}
      </FormLabel>

      <SvgIcon
        v-if="option.help"
        v-tooltip="option.help"
        icon="help-outline"
        size="small"
        class="align-middle text-gray-500"
        :tabindex="0"
      />

      <SvgIcon
        v-if="option.warning"
        v-tooltip="option.warning"
        icon="alert"
        size="small"
        class="text-alert align-middle"
        :tabindex="0"
      />
    </div>
  </div>
</template>
