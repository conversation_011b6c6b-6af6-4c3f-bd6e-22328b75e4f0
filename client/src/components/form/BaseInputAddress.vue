<script setup lang="ts">
import { computed, useId } from 'vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import FormRow from '@js/components/form/FormRow.vue'
import Translator from '@js/translator'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import type { SelectOption } from '@js/types'
import type { Country } from '@js/model/country'
import type { Address } from '@js/model/address'
import type { FormErrors } from '@js/helper/form/mergeErrors'

const address = defineModel<Partial<Address>>({ required: true })

const props = withDefaults(
  defineProps<{
    id?: string
    name?: string
    label?: string | false
    disabled?: boolean
    required?: boolean
    helpTooltip?: string
    warningTooltip?: string
    errors?: FormErrors<Address>
  }>(),
  {
    id: undefined,
    name: undefined,
    label: undefined,
    disabled: false,
    required: false,
    helpTooltip: undefined,
    warningTooltip: undefined,
    errors: () => ({
      line1: [],
      line2: [],
      line3: [],
      city: [],
      state: [],
      postcode: [],
      country: [],
    }),
  }
)

const { items: countries } = useCountriesAllQuery()
const countryOptions = computed(
  (): Array<SelectOption> =>
    countries.value.map((country: Country) => ({
      id: country['@id'],
      name: country.nameShort,
    }))
)

const id = computed(() => props.id ?? useId())
</script>

<template>
  <FormRow
    :for="`${id}_line1`"
    :required="required"
    :help-tooltip="helpTooltip"
    :warning-tooltip="warningTooltip"
    :label="label"
  >
    <div class="grid grid-cols-1 gap-(--app-form-field-spacing)">
      <!-- Line 1 -->
      <BaseInputText
        :id="`${id}_line1`"
        v-model="address.line1"
        :name="name ? `${name}[line1]` : undefined"
        :required="required"
        :disabled="disabled"
        :label="Translator.trans('u2.address.line_1')"
        :errors="errors.line1"
      />
      <!-- Line 2 -->
      <BaseInputText
        v-model="address.line2"
        :name="name ? `${name}[line2]` : undefined"
        :disabled="disabled"
        :label="Translator.trans('u2.address.line_2')"
        :errors="errors.line2"
      />
      <!-- Line 3 -->
      <BaseInputText
        v-model="address.line3"
        :name="name ? `${name}[line3]` : undefined"
        :disabled="disabled"
        :label="Translator.trans('u2.address.line_3')"
        :errors="errors.line3"
      />
      <!-- Postcode -->
      <BaseInputText
        v-model="address.postcode"
        :name="name ? `${name}[postcode]` : undefined"
        :disabled="disabled"
        :label="Translator.trans('u2.address.post_code')"
        :errors="errors.postcode"
      />
      <!-- City -->
      <BaseInputText
        v-model="address.city"
        :name="name ? `${name}[city]` : undefined"
        :required="required"
        :disabled="disabled"
        :label="Translator.trans('u2.address.city')"
        :errors="errors.city"
      />
      <!-- State -->
      <BaseInputText
        v-model="address.state"
        :name="name ? `${name}[state]` : undefined"
        :disabled="disabled"
        :label="Translator.trans('u2.address.state')"
        :errors="errors.state"
      />
      <!--     Country -->
      <BaseSelect
        :id="`${id}_country`"
        v-model="address.country"
        :name="name ? `${name}[country]` : undefined"
        :required="required"
        :disabled="disabled"
        :label="Translator.trans('u2.country')"
        :placeholder="Translator.trans('u2.select_country')"
        :errors="errors.country"
        :options="countryOptions"
      />
    </div>
  </FormRow>
</template>
