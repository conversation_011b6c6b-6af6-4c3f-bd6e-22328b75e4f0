import { createCountry } from '@tests/__factories__/createCountry'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import BaseRadioGroup from '@js/components/form/BaseRadioGroup.vue'
import FieldDateTimePicker from '@js/components/form/FieldDateTimePicker.vue'
import BaseInputMail from '@js/components/form/BaseInputMail.vue'
import BaseMultiSelect from '@js/components/form/BaseMultiSelect.vue'
import FormFieldGroup from '@js/components/form/FormFieldGroup.vue'
import FormRowEnableable from '@js/components/form/FormRowEnableable.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import FormBaseFieldset from '@js/components/form/FormBaseFieldset.vue'
import BaseColorPicker from '@js/components/form/BaseColorPicker.vue'
import BaseDatePicker from '@js/components/form/BaseDatePicker.vue'
import BaseInputAddress from '@js/components/form/BaseInputAddress.vue'
import BaseInputFile from '@js/components/form/BaseInputFile.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseTextarea from '@js/components/form/BaseTextarea.vue'
import FormFieldset from '@js/components/form/FormFieldset.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const countries = [createCountry(), createCountry(), createCountry()]
const meta: Meta<{ formSpacing: number }> = {
  title: 'Form/Examples',
  parameters: {
    msw: {
      handlers: [
        http.get('/api/countries', async () => {
          return HttpResponse.json(
            { 'hydra:member': countries },
            {
              status: StatusCodes.OK,
            }
          )
        }),
        http.get('/api/system-settings', async () => {
          return HttpResponse.json([], {
            status: StatusCodes.OK,
          })
        }),
      ],
    },
    docs: {
      description: {
        component: `
Since form is globally set to \`display: grid\` we can use CSS Grid utility classes to define the form layout. 
For example, to create a two-column layout on small screens and above:

\`\`\`html
<form class="sm:grid-cols-2">
  <BaseInputText label="First Name" />
  <BaseInputText label="Last Name" />
  <BaseTextarea label="Address" class="sm:col-span-full"/>
</form>
\`\`\`

## Spacing between Form Fields

The \`--app-form-field-spacing\` variable is used to define the spacing between form fields.

\`.fieldset-grid\` component class uses \`--app-form-field-spacing\` to set a standard gap between the form fields. 


## Grouping Fields

Use the \`FormFieldset\` component to group related form fields into a section:

\`\`\`html
<FormFieldset label="Personal Information">
  <div class="fields-grid">
    <BaseInputText label="First Name" />
    <BaseInputText label="Last Name" />
  </div>
</FormFieldset>
\`\`\`

Use the \`FormFieldGroup\` component for visual grouping of fields that are closely related or dependent:

\`\`\`html
<FormFieldGroup label="Address">
  <div class="fields-grid">
    <BaseInputText label="Line 1" />
    <BaseInputText label="City" />
    <BaseInputText label="Country" />
  </div>
</FormFieldGroup>
\`\`\`
`,
      },
    },
  },
}

export default meta

export const Basic: StoryObj<typeof meta> = {
  render: () => ({
    components: {
      BaseColorPicker,
      BaseDatePicker,
      BaseInputAddress,
      BaseInputFile,
      BaseInputText,
      BaseSelect,
      BaseTextarea,
      FormFieldset,
    },
    setup() {
      return {
        countries,
      }
    },
    template: `
      <form>
        <BaseInputText label="Base Input Text" />
        <BaseSelect
          label="Country"
          :model-value="countries[0]['@id']"
          help-tooltip="Help"
          warning-tooltip="Warning"
          :options="countries.map((country) => ({ id: country['@id'], name: country.nameShort }))"
        />
        <BaseInputText label="Text input with errors" :errors="['error 1', 'error 2']"/>
        <BaseColorPicker label="Base Color Picker" />
        <BaseTextarea label="Base Textarea" />
        <BaseDatePicker label="Base Datepicker" />
      </form>
    `,
  }),
}
export const Complex: StoryObj<typeof meta> = {
  render: () => ({
    components: {
      BaseColorPicker,
      BaseDatePicker,
      BaseInputAddress,
      BaseInputFile,
      BaseInputMail,
      BaseInputText,
      BaseMultiSelect,
      BaseRadioGroup,
      BaseSelect,
      BaseTextarea,
      BaseToggle,
      FieldDateTimePicker,
      FormBaseFieldset,
      FormFieldset,
      FormFieldGroup,
      FormRowEnableable,
    },
    template: `
      <form class="sm:grid-cols-2">
        <FormBaseFieldset label="Base" class="col-span-2">
          <BaseToggle label="Base toggle"></BaseToggle>
        </FormBaseFieldset>

        <FormRowEnableable class="col-span-2">
          <BaseInputText label="Input Text Enableable"/>
        </FormRowEnableable>

        <BaseInputText label="Base Input Text"/>
        <BaseTextarea label="Base Textarea"/>
        <BaseDatePicker label="Base Datepicker" class="sm:col-start-2"/>
        <BaseInputText label="Base Input Text"/>
        <BaseInputText
          label="Base Input Text"
          class="sm:col-start-1"
          help-tooltip="Help"
          warning-tooltip="Warning"
        />
        <BaseInputText
          label="Base Input Text"
          :errors="['error 1', 'error 2']"
        />
        <BaseInputFile
          label="Base Input File"
          :errors="['File error 1', 'File error 2']"
        />
        <BaseInputText label="Base Input Text" class="col-span-full"/>

        <FormFieldset class="col-span-2" label="Basic section">
          <div class="fields-grid">
            <BaseInputText label="Base Input Text"/>
            <BaseTextarea label="Base Textarea"/>
            <BaseDatePicker label="Base Datepicker"/>
          </div>
        </FormFieldset>

        <FormFieldset class="col-span-2" label="Two columns section">
          <div class="fields-grid sm:grid-cols-2">
            <BaseInputText label="Base Input Text"/>
            <BaseInputText label="Base Input Text"/>
            <BaseInputText label="Base Input Text"/>
            <BaseInputText label="Base Input Text"/>
            <BaseInputText label="Base Input Text" class="sm:col-start-2"/>
            <BaseInputText label="Base Input Text" class="sm:col-start-1"/>
          </div>
        </FormFieldset>

        <div class="grid col-span-2 sm:grid-cols-2 gap-(--app-form-field-spacing)">
          <FormFieldset label="Section 1">
            <div class="fields-grid sm:grid-cols-2">
              <BaseInputText label="Base Input Text"/>
              <BaseInputText label="Base Input Text"/>
              <BaseInputText label="Base Input Text"/>
              <FieldDateTimePicker label="Datetime Picker" name="datetimepicker"/>
              <BaseInputText label="Base Input Text" class="sm:col-start-2"/>
              <BaseInputText label="Base Input Text" class="sm:col-start-1"/>
            </div>
          </FormFieldset>

          <FormFieldset label="Section 2" class="col-span-1 place-content-start mt-0">
            <div class="fields-grid sm:grid-cols-2 ">
              <BaseInputText label="Base Input Text"/>
              <BaseInputText label="Base Input Text"/>
              <BaseInputMail label="Base Input Mail" class="sm:col-start-2"/>
              <BaseInputText label="Base Input Text" class="sm:col-start-2"/>
            </div>
          </FormFieldset>
        </div>

        <FormFieldGroup :label="false" class="col-span-full">
          <BaseInputText label="Base Input Text"/>
          <BaseRadioGroup :options="[{id: 1, name: 'Option 1'}, {id: 2, name: 'Option 2'}]" :model-value="2"/>
        </FormFieldGroup>
        
        <FormFieldGroup label="Group" collapsible>
          <BaseInputText label="Base Input Text"/>
          <BaseColorPicker label="Base Color Picker"/>
          <BaseToggle label="Base Toggle"/>
        </FormFieldGroup>

        <FormFieldset label="Section with groups" class="col-span-2" collapsible>
          <div class="fields-grid sm:grid-cols-2">
            <FormFieldGroup label="Group 1">
              <div class="fields-grid sm:grid-cols-2">
                <BaseInputText label="Base Input Text"/>
                <BaseInputText label="Base Input Text"/>
                <BaseInputText label="Base Input Text"/>
                <BaseInputText label="Base Input Text"/>
                <BaseMultiSelect label="Base Multiselect" class="sm:col-start-2"
                                 :options="[{id: 1, name: 'Option 1'}, {id: 2, name: 'Option 2'}]"/>
                <BaseInputText label="Base Input Text" class="sm:col-start-1"/>
              </div>
            </FormFieldGroup>

            <FormFieldGroup label="Group 2" class="col-span-1 place-content-start mt-0">
              <div class="fields-grid">
                <BaseSelect label="Base Select" :options="[{id: 1, name: 'Option 1'}, {id: 2, name: 'Option 2'}]"/>
                <BaseInputText label="Base Input Text"/>
                <BaseInputText label="Base Input Text"/>
                <BaseToggle label="Base Toggle"/>
              </div>
            </FormFieldGroup>
          </div>
        </FormFieldset>
      </form>
    `,
  }),
}
