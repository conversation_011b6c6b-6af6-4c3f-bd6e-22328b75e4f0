<script lang="ts" setup>
import kebabCase from 'lodash/kebabCase'
import { nextTick, onMounted, ref } from 'vue'
import AppTree from '@js/components/AppTree.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonPopover from '@js/components/buttons/ButtonPopover.vue'
import BaseCheckbox from '@js/components/form/BaseCheckbox.vue'
import BaseCheckboxGroup from '@js/components/form/BaseCheckboxGroup.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import UnitPopupCard from '@js/components/unit/UnitPopupCard.vue'
import Translator from '@js/translator'
import type { AnyBasicUnit } from '@js/model/unit'
import type { Tree } from '@js/types'
import type { Icon } from '@js/utilities/name-lists'
import { useQueryClient } from '@tanstack/vue-query'
import { queries } from '@js/query'

type UnitHierarchyTree = Tree<{
  id: number
  countryId: number
  className: string
}>

const {
  unitHierarchy = [],
  selectedIds = [],
  name = undefined,
} = defineProps<{
  disabled?: boolean
  unitHierarchy?: UnitHierarchyTree
  // This should become modelValue when submit forms in Vue
  selectedIds?: Array<UnitHierarchyTree[number]['id']>
  name?: string
  hasErrors?: boolean
}>()

const toggleCheckForElementAndAllChildren = (element: HTMLElement, isChecked: boolean) => {
  if (element.classList.contains('disabled')) {
    return
  }

  const checkboxes = element.closest('li')?.querySelectorAll('input[type="checkbox"]') as
    | Array<HTMLInputElement>
    | undefined

  if (!checkboxes) {
    return
  }

  checkboxes.forEach((checkbox) => {
    const isNotDocumentCountry =
      checkbox
        .closest('[data-js-snapshot-unit-selector-unit]')
        ?.classList.contains('is-not-document-country') ?? false

    if (isChecked && isNotDocumentCountry) {
      return
    }
    checkbox.checked = isChecked
  })
}

function updateUnitsStateAccordingToDocumentCountry(
  documentCountry: string,
  units: NodeListOf<HTMLElement>
) {
  units.forEach((unit) => {
    if (documentCountry === '' || unit.dataset.unitCountryId === documentCountry) {
      unit.classList.remove('is-not-document-country')
      return
    }

    unit.classList.add('is-not-document-country')
  })
}

onMounted(() => {
  const units = document.querySelectorAll(
    '[data-js-snapshot-unit-selector-unit]'
  ) as NodeListOf<HTMLElement>
  if (units.length === 0) {
    return
  }

  const selectAll = document.querySelectorAll('[data-js-snapshot-unit-selector-select-all]')
  selectAll.forEach((element) => {
    element.addEventListener('click', (event) => {
      event.preventDefault()
      if (event.target instanceof HTMLElement) {
        toggleCheckForElementAndAllChildren(event.target, true)
      }
    })
  })

  document.querySelectorAll('[data-js-snapshot-unit-selector-select-none]').forEach((element) => {
    element.addEventListener('click', (event) => {
      event.preventDefault()
      if (event.target instanceof HTMLElement) {
        toggleCheckForElementAndAllChildren(event.target, false)
      }
    })
  })

  const documentCountry = document.querySelector('[data-js-document-country]')
  if (documentCountry instanceof HTMLElement) {
    const input = documentCountry.querySelector('input.hidden-no-sr') as HTMLInputElement

    updateUnitsStateAccordingToDocumentCountry(input.value, units)

    documentCountry.addEventListener('input', () => {
      nextTick(() => {
        const input = documentCountry.querySelector('input.hidden-no-sr') as HTMLInputElement
        updateUnitsStateAccordingToDocumentCountry(input.value, units)
      })
    })
  }
})

const shortUnit = ref<AnyBasicUnit>()
const queryClient = useQueryClient()
const fetchUnit = async (id: number) => {
  shortUnit.value = await queryClient.fetchQuery(queries.units.single(id)._ctx.basic)
}
</script>

<template>
  <AppTree :tree="unitHierarchy" class="structure-tree" data-sentry-block>
    <template #node="{ node: unit }">
      <BaseCheckboxGroup
        v-if="unit.id"
        data-js-snapshot-unit-selector-unit
        :data-unit-country-id="unit.countryId"
      >
        <div class="group inline-flex items-center gap-1">
          <BaseCheckbox
            :id="`${name}_${unit.id}`"
            :model-value="selectedIds.includes(unit.id)"
            :label="unit.label"
            :value="unit.id.toString()"
            :name="name + '[]'"
            :disabled="disabled"
            :icon="kebabCase(unit.className) as Icon"
            :has-errors
          />

          <ButtonPopover
            icon="info"
            class="opacity-0 duration-300 ease-in-out group-hover:opacity-100 focus:opacity-100"
            @click="fetchUnit(unit.id)"
          >
            <template #body>
              <UnitPopupCard v-if="shortUnit?.id === unit.id" :unit="shortUnit" />
              <AppLoader v-else size="small" />
            </template>
          </ButtonPopover>

          <span v-if="unit.children.length > 0" class="flex">
            <ButtonBasic
              :grouped="true"
              :disabled="disabled"
              data-js-snapshot-unit-selector-select-all
            >
              {{ Translator.trans('u2.all') }}
            </ButtonBasic>

            <ButtonBasic
              :grouped="true"
              :disabled="disabled"
              data-js-snapshot-unit-selector-select-none
            >
              {{ Translator.trans('u2_core.none') }}
            </ButtonBasic>
          </span>
        </div>
      </BaseCheckboxGroup>
    </template>
  </AppTree>
</template>

<style scoped>
@reference "@css/app.css";

.is-not-document-country {
  color: theme('colors.gray.300');
}
</style>

<style>
@reference "@css/app.css";

.structure-tree {
  background: theme('colors.white');
  padding: 0;

  &,
  & ul {
    list-style: none;
    margin: 0;
  }

  ul {
    list-style: none;
    margin-left: 8px;
    padding: 0;
    position: relative;

    &::before {
      border-left: 1px solid theme('colors.gray.400');
      bottom: 0;
      content: '';
      display: block;
      left: 0;
      position: absolute;
      top: 0;
      width: 0;
    }

    li {
      line-height: theme('lineHeight.loose');
      margin: 0;
      padding: 0 12px;
      position: relative;

      &::before {
        border-top: 1px solid theme('colors.gray.400');
        content: '';
        display: block;
        height: 0;
        left: 0;
        position: absolute;
        top: 1em;
        width: 8px;
      }

      &:last-child::before {
        background: theme('colors.white');
        bottom: 0;
        height: auto;
        top: 1em;
      }
    }
  }
}
</style>
