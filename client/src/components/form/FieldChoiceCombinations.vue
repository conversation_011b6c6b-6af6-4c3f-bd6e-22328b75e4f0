<script
  setup
  lang="ts"
  generic="TOption1 extends Record<string, any>, TOption2 extends Record<string, any>"
>
import { useField, useFieldArray } from 'vee-validate'
import { UniqueID } from '@js/utilities/unique-id'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import FieldSelect from '@js/components/form/FieldSelect.vue'
import FormRow from '@js/components/form/FormRow.vue'

export type ChoiceCombination = { id: string | number; left: string; right: string }

const {
  options1 = [],
  options2 = [],
  required = false,
  disabled = false,
  name,
  valueKey1 = undefined,
  valueKey2 = undefined,
  labelKey1 = undefined,
  labelKey2 = undefined,
} = defineProps<{
  title1: string
  options1?: Array<TOption1>
  options1Loading?: boolean
  title2: string
  options2?: Array<TOption2>
  options2Loading?: boolean
  required?: boolean
  disabled?: boolean
  noChoicesText: string
  name: string
  valueKey1?: keyof TOption1
  valueKey2?: keyof TOption2
  labelKey1?: keyof TOption1 | ((option: TOption1) => string | undefined)
  labelKey2?: keyof TOption2 | ((option: TOption2) => string | undefined)
}>()

const { fields, remove, push } = useFieldArray<ChoiceCombination>(() => name)

function add() {
  push({ id: UniqueID(), left: '', right: '' })
}

const { errors } = useField(() => name, undefined, {
  bails: false,
})
</script>

<template>
  <FormRow :errors :label="false">
    <div
      class="grid w-full grid-cols-[1fr_1fr_minmax(4rem,auto)] grid-rows-[auto] items-center gap-x-4 gap-y-0"
    >
      <div class="form-label">
        {{ title1 }}
      </div>

      <div class="form-label">
        {{ title2 }}
      </div>

      <div class="flex items-center justify-center">
        <ButtonNew button-style="text" :show-text="false" :disabled="disabled" @click="add" />
      </div>

      <div
        v-for="(choiceCombination, key) in fields"
        :key="choiceCombination.value.id"
        class="fields-grid col-span-full -mx-2 w-full grid-cols-subgrid p-2 hover:bg-orange-200"
      >
        <FieldSelect
          :label="false"
          :disabled="disabled"
          :required="required"
          :loading="options1Loading"
          :options="options1"
          :value-key="valueKey1"
          :label-key="labelKey1"
          :name="`${name}[${key}].left`"
        />

        <FieldSelect
          :label="false"
          :disabled="disabled"
          :required="required"
          :loading="options2Loading"
          :options="options2"
          :value-key="valueKey2"
          :label-key="labelKey2"
          :name="`${name}[${key}].right`"
        />

        <div class="flex items-center justify-center">
          <ButtonDelete
            :disabled="disabled"
            button-style="text"
            :show-text="false"
            @click="remove(key)"
          />
        </div>
      </div>
      <div v-if="fields.length === 0" class="col-span-full text-gray-600">
        {{ noChoicesText }}
      </div>
    </div>
  </FormRow>
</template>
