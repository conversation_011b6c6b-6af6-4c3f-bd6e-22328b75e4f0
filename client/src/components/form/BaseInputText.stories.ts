import AppInputText from '@js/components/form/AppInputText.vue'
import { fetchStates } from '@js/types'
import BaseInputText from './BaseInputText.vue'
import type { Meta, StoryObj } from '@storybook/vue3'
import type { ComponentProps } from 'vue-component-type-helpers'

type PropsAndCustomArgs = ComponentProps<typeof BaseInputText> & {
  showErrors: boolean
  showHelpTooltip: boolean
  showWarningTooltip: boolean
}
const meta: Meta<PropsAndCustomArgs> = {
  title: 'Form/Text',
  argTypes: {
    'onUpdate:modelValue': { action: 'update:modelValue' },
    errors: { if: { arg: 'showErrors' } },
    helpTooltip: { if: { arg: 'showHelpTooltip' } },
    warningTooltip: { if: { arg: 'showWarningTooltip' } },
    state: {
      options: [fetchStates.loading, fetchStates.resolved, fetchStates.error, fetchStates.idle],
      control: {
        type: 'radio',
      },
    },
  },
  args: {
    modelValue: 'Some text to edit',
    showErrors: false,
    errors: ['Error 1', 'Error 2'],
    showHelpTooltip: false,
    helpTooltip: 'Here is some help text',
    showWarningTooltip: false,
    warningTooltip: 'Here is some warning text',
    label: 'BaseInputText label',
    required: false,
    disabled: false,
    state: fetchStates.idle,
  },
}

export default meta
export const Default: StoryObj<typeof AppInputText> = {
  render: (args) => ({
    components: { AppInputText },
    setup() {
      return { args }
    },
    template: '<AppInputText v-bind="args" />',
  }),
}

export const FormRow: StoryObj<typeof BaseInputText> = {
  render: (args) => ({
    components: { BaseInputText },
    setup() {
      return { args }
    },
    template: `<BaseInputText v-bind="args"/>`,
  }),
}
