<script setup lang="ts">
import * as zxcvbnCommonPackage from '@zxcvbn-ts/language-common'
import * as zxcvbnDePackage from '@zxcvbn-ts/language-de'
import * as zxcvbnEnPackage from '@zxcvbn-ts/language-en'
import { zxcvbn, zxcvbnOptions } from '@zxcvbn-ts/core'
import { computed } from 'vue'
import Translator from '@js/translator'

const props = defineProps<{
  password: string
}>()

const width = computed(() => (score.value / descriptions.value.length) * 100)

const descriptions = computed(() => [
  {
    color: 'bg-linear-to-r from-red-500 to-orange-500',
    label: Translator.trans('u2.password_very_weak'),
  },
  {
    color: 'bg-linear-to-r from-orange-500 to-yellow-500',
    label: Translator.trans('u2.password_weak'),
  },
  {
    color: 'bg-linear-to-r from-yellow-400 to-green-400',
    label: Translator.trans('u2.password_moderate'),
  },
  {
    color: 'bg-linear-to-r from-green-400 to-emerald-500',
    label: Translator.trans('u2.password_good'),
  },
  {
    color: 'bg-emerald-500',
    label: Translator.trans('u2.password_strong'),
  },
])

const description = computed(() =>
  props.password.length > 0
    ? descriptions.value[score.value - 1]
    : {
        color: 'bg-transparent',
        label: '',
      }
)

const score = computed(() => {
  if (!(props.password.length > 0)) {
    return 0
  }
  return zxcvbn(props.password).score + 1
})

zxcvbnOptions.setOptions({
  dictionary: {
    ...zxcvbnCommonPackage.dictionary,
    ...zxcvbnEnPackage.dictionary,
    ...zxcvbnDePackage.dictionary,
  },
  graphs: zxcvbnCommonPackage.adjacencyGraphs,
  translations: zxcvbnEnPackage.translations,
})
</script>
<template>
  <div v-if="password">
    <div class="border-grey-30 flex h-2 rounded-sm border">
      <div
        :style="{ width: `${width}%` }"
        :class="['flex h-full rounded-sm duration-500 ease-in-out', description.color]"
      />
    </div>

    <p class="mt-1 text-sm text-gray-600">
      {{ description.label }}
    </p>
  </div>
</template>
