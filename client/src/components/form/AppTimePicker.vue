<script lang="ts" setup>
import { useBindAttrs } from '@js/composable/useBindAttrs'
import { useVModel } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import SvgIcon from '@js/components/SvgIcon.vue'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

const timePickerInput = ref()

const props = withDefaults(
  defineProps<{
    id?: string
    disabled?: boolean
    modelValue?: string
    hasErrors?: boolean
  }>(),
  {
    id: undefined,
    disabled: false,
    modelValue: undefined,
    hasErrors: false,
  }
)
const emit = defineEmits<(event: 'update:modelValue', payload: string | undefined) => void>()

const input = useVModel(props, 'modelValue', emit)
onMounted(() => {
  if (input.value && !/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/.test(input.value)) {
    throw Error('Invalid time')
  }
})
</script>

<template>
  <div :id class="hover-effect relative flex w-24 items-center" v-bind="rootAttrs">
    <input
      ref="timePickerInput"
      type="time"
      :value="input"
      v-bind="bindAttrs"
      :disabled="disabled"
      :class="{ 'has-errors': hasErrors }"
      @input="input = ($event.target as HTMLInputElement).value"
    />
    <SvgIcon
      icon="clock"
      class="form-add-on-color absolute inset-y-0 right-0 my-auto mr-3 cursor-pointer"
      @click="timePickerInput.focus()"
    />
  </div>
</template>

<style scoped>
@reference "@css/app.css";

/* Hide clock indicator in Chrome */
input[type='time']::-webkit-calendar-picker-indicator,
input[type='time']::-webkit-calendar-picker-indicator:focus {
  display: none;
}

.hover-effect:hover [type='time'] {
  border-color: theme('colors.gray.400');
}

.hover-effect:hover div {
  color: theme('colors.gray.600');
}

.hover-effect [type='time']:focus {
  & + div {
    color: theme('colors.gray.600');
  }

  border-color: theme('borderColor.skin.focus');
}
</style>
