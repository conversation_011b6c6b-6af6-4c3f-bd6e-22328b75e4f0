<script lang="ts">
export interface AppDatePickerProps {
  id?: string
  name?: string
  disabled?: boolean
  errorReference?: string
  required?: boolean
  hasErrors?: boolean
}
</script>

<script setup lang="ts">
import BasePopover from '@js/components/BasePopover.vue'
import { useBindAttrs } from '@js/composable/useBindAttrs'
import { computed, ref, watch } from 'vue'
import { formatISO, lightFormat } from 'date-fns'
import { helpers } from '@vuelidate/validators'
import { useVuelidate } from '@vuelidate/core'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import MiniCalendar from '@js/components/calendar/MiniCalendar.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

const {
  id = undefined,
  name = undefined,
  disabled = false,
  errorReference = undefined,
  required = false,
  hasErrors = false,
} = defineProps<AppDatePickerProps>()

const date = defineModel<string | null>('modelValue', { required: true, default: null })
defineEmits<(event: 'click') => void>()

const isCalendarOpen = ref(false)

function show() {
  isCalendarOpen.value = true
}

function hide() {
  isCalendarOpen.value = false
}

const resolveInput = (value: string | null) => {
  if (value && /^\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])$/.test(value)) {
    const [year, month, day] = value.split('-')
    return lightFormat(new Date(+year, +month - 1, +day), 'dd.MM.yyyy')
  }
  return value
}

const resolveModelValue = (value: string | null) => {
  if (value === null || value.length === 0) {
    return null
  }

  if (/^(0[1-9]|[12][0-9]|3[01])[.](0[1-9]|1[012])[.]\d{4}$/.test(value)) {
    const [day, month, year] = value.split('.')
    return formatISO(new Date(+year, +month - 1, +day), { representation: 'date' })
  }
  return value
}

const input = ref(resolveInput(date.value))
const hiddenInput = ref()
const datepickerInput = ref<HTMLInputElement>()

watch(
  () => date.value,
  (newValue) => {
    input.value = resolveInput(newValue)
    if (newValue === null || newValue === '') {
      date.value = null
    }
  }
)

const errorReferenceProp = computed(() => errorReference)
const vuelidate = useVuelidate(
  {
    input: {
      germanDateFormat: {
        $validator: helpers.regex(/^(0[1-9]|[12][0-9]|3[01])[.](0[1-9]|1[012])[.]\d{4}$/),
        $message: defaultValidationMessages.germanDateFormat(),
      },
    },
  },
  { input },
  { $registerAs: errorReferenceProp.value }
)
const invalid = computed(() => hasErrors || vuelidate.value.$invalid)

const processInput = (event: Event) => {
  vuelidate.value.$touch()

  const value = (event.target as HTMLInputElement).value

  date.value = resolveModelValue(value)

  if (value.length === 0) {
    show()
  }
}

const selectDate = (newDate: string | undefined) => {
  date.value = resolveModelValue(newDate ?? null)
  hide()
}
</script>

<template>
  <div class="js-enableable-container group relative inline-flex items-center" v-bind="rootAttrs">
    <input
      :id
      ref="datepickerInput"
      :disabled="disabled"
      type="text"
      maxlength="10"
      :value="input"
      :required="required"
      :data-proxy="name"
      :class="{ 'has-errors group-hover:border-bad focus:group-hover:border-bad': invalid }"
      placeholder="dd.mm.yyyy"
      class="group-hover:border-(--app-input-color-border-hover) focus:group-hover:border-(--app-input-color-border-focus)"
      @input="processInput"
      @keydown.down="show"
      @keydown.esc="hide"
    />

    <input
      v-bind="bindAttrs"
      ref="hiddenInput"
      type="hidden"
      :name="name"
      :value="date"
      data-proxy-field
    />

    <BasePopover v-model:open="isCalendarOpen" :disabled>
      <button
        type="button"
        class="js-enableable-button form-add-on-color group pointer-events-auto absolute inset-y-0 right-0 my-auto mr-3 inline-flex cursor-pointer items-center rounded-xs outline-hidden group-hover:text-gray-600"
      >
        <SvgIcon
          icon="calendar"
          class="group-focus:ring-skin-base rounded-xs group-focus:text-gray-600 group-focus:ring-2"
        />
      </button>

      <template #content>
        <MiniCalendar :model-value="date ? date : undefined" @update:model-value="selectDate" />
      </template>
    </BasePopover>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

.group:has(input:focus) .form-add-on-color {
  color: theme('colors.gray.600');
}
</style>
