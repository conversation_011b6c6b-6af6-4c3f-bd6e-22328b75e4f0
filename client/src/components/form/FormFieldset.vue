<script setup lang="ts">
import CollapsibleElement from '@js/components/CollapsibleElement.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

withDefaults(
  defineProps<{
    label: string
    collapsible?: boolean
    collapsed?: boolean
  }>(),
  {
    collapsible: false,
    collapsed: false,
  }
)
</script>

<template>
  <CollapsibleElement
    as="fieldset"
    :default-open="!collapsed"
    class="w-full border-0 border-t border-gray-500 px-0 py-1.5"
  >
    <template #default="{ open }">
      <legend
        class="group pr-1.5"
        :class="{ 'cursor-pointer': collapsible, 'pointer-events-none': !collapsible }"
      >
        <button
          type="button"
          :disabled="!collapsible"
          :class="{ 'cursor-default': !collapsible }"
          class="flex items-center gap-2 py-1"
        >
          <span class="text-sm leading-none font-medium text-gray-500 uppercase">
            {{ label }}
          </span>
          <SvgIcon
            v-if="collapsible"
            :class="[
              'shrink-0 transform text-gray-400 opacity-20 transition ease-in-out group-hover:opacity-100',
              { '-rotate-90': !open },
            ]"
            icon="arrow-down"
          />
        </button>
      </legend>
    </template>
    <template #content>
      <slot />
    </template>
  </CollapsibleElement>
</template>
