<script lang="ts">
import type { FormRowProps } from '@js/components/form/FormRow.vue'

export interface BaseInputFileProps extends FormRowProps {
  disabled?: boolean
  supportedMimeTypes?: Array<string>
  maxFileUploadSize?: number
  progress?: {
    amount: number
    max: number
  }
  name?: string
}
</script>

<script setup lang="ts">
import { useBindAttrs } from '@js/composable/useBindAttrs'
import { computed, ref } from 'vue'
import FormRow from '@js/components/form/FormRow.vue'
import Translator from '@js/translator'
import FilePreview from '@js/components/file/FilePreview.vue'
import ProgressBar from '@js/components/ProgressBar.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import PrettyBytes from '@js/components/PrettyBytes.vue'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import { maxUploadSize } from '@js/model/system_setting'
import useLocaleNumberFormatter from '@js/composable/useLocaleNumberFormatter'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

const {
  label = false,
  required = false,
  disabled = false,
  supportedMimeTypes = [],
  maxFileUploadSize = undefined,
  errors = [],
  progress = undefined,
  name = undefined,
} = defineProps<BaseInputFileProps>()

const inputErrors = ref([] as Array<string>)

const modelValue = defineModel<File | undefined>()

const systemSettingsQuery = useSystemSettingsAllQuery()
const systemMaxFileUploadSize = computed(() => {
  const maxUploadFileSize = systemSettingsQuery.systemSettings.value?.maxUploadSize
  return maxUploadFileSize ? Number(maxUploadFileSize) * 1000 : undefined
})

const maxFileSize = computed(() => {
  const allowedMaxUploadSize =
    (systemMaxFileUploadSize.value ?? 0) > maxUploadSize
      ? maxUploadSize
      : (systemMaxFileUploadSize.value ?? maxUploadSize)

  return maxFileUploadSize && maxFileUploadSize > allowedMaxUploadSize
    ? allowedMaxUploadSize
    : maxFileUploadSize || allowedMaxUploadSize
})

const { formatBytes } = useLocaleNumberFormatter()
const validateFile = (file: File) => {
  inputErrors.value = []

  if (!file && required) {
    inputErrors.value.push(Translator.trans('u2.no_file_selected', {}, 'validators'))
    return
  }

  if (file.size > maxFileSize.value) {
    inputErrors.value.push(
      Translator.trans(
        'u2.upload_file_too_big',
        { fileSize: formatBytes(file.size), name: file.name },
        'validators'
      )
    )
  }
}

const fileInput = ref()

const updateModelValue = (file: File | undefined) => {
  inputErrors.value = []

  if (file === undefined) {
    // Empty the file list of the hidden input
    syncInput(file)

    modelValue.value = undefined

    return
  }

  validateFile(file)

  if (inputErrors.value.length > 0) {
    return
  }

  modelValue.value = inputErrors.value.length > 0 ? undefined : file

  // Ensure the file input has the assigned file
  // Dropping a file into the input won't cause an update of its files list
  // Therefore, we need to manually update the files list
  const isInputUpToDate = fileInput.value.files.length === 1 && fileInput.value.files[0] === file
  if (!isInputUpToDate) {
    syncInput(file)
  }
}

/**
 * There is no way to create a new FileList object, so we need to use a DataTransfer object
 * and add the file to it. Then we can set the files property of the file input to the files
 * property of the DataTransfer object.
 */
const syncInput = (file: File | undefined) => {
  const dataTransfer = new DataTransfer()
  if (file) {
    dataTransfer.items.add(file)
  }

  // It is impossible to mock a FileList. Therefore, skip this assignment whenever the
  // list is not real (e.g. in testing environment)
  if (dataTransfer.files instanceof FileList) {
    fileInput.value.files = dataTransfer.files
  }
}

function onSelectFile() {
  if (disabled) {
    return
  }
  fileInput.value.click()
}

const addFileOnDrag = (event: DragEvent) => {
  if (disabled) {
    return
  }
  updateModelValue([...(event.dataTransfer?.files ?? [])][0])
}

const addFileOnChange = (event: Event) => {
  if (disabled) {
    return
  }
  updateModelValue([...((event.target as HTMLInputElement).files ?? [])][0])
}

function onFileRemoved() {
  if (disabled) {
    return
  }
  updateModelValue(undefined)
}
</script>

<template>
  <FormRow
    :id
    :required
    :help-tooltip
    :warning-tooltip
    :label
    :errors="[...inputErrors, ...errors]"
    v-bind="rootAttrs"
  >
    <template #default="{ fieldId }">
      <div
        class="relative w-full rounded-xs border border-dashed border-gray-300 p-4 text-center text-gray-700 transition"
        @drop.stop.prevent="addFileOnDrag"
        @dragover.stop.prevent
      >
        <input
          :id="fieldId"
          ref="fileInput"
          :name="name"
          type="file"
          class="hidden-no-sr"
          tabindex="-1"
          :accept="supportedMimeTypes.join(',')"
          v-bind="bindAttrs"
          @change.stop.prevent="addFileOnChange"
        />
        <template v-if="!modelValue">
          <p>
            {{ Translator.trans('u2_core.upload_file.drop_a_file_here_to_attach_it') }}
            <br />
            {{ Translator.trans('u2_core.or') }}
            <br />
            <ButtonBasic :disabled="disabled" @click="onSelectFile">
              {{ Translator.trans('u2.select_a_file') }}
            </ButtonBasic>
            <br />
            <small class="mt-3 inline-block text-gray-500">
              {{ Translator.trans('u2_core.maximum_file_size') }}
              <PrettyBytes :bytes="maxFileSize" />
            </small>
          </p>
        </template>
        <template v-else>
          <FilePreview :key="modelValue.name" :file="modelValue">
            <template #buttons>
              <ButtonDelete
                :disabled="disabled"
                button-style="solid"
                :show-text="false"
                @click="onFileRemoved"
              />
            </template>
          </FilePreview>
          <ProgressBar
            v-if="progress"
            class="mt-2"
            :value="progress.amount"
            :max="progress.max"
            mode="expanded"
          />
        </template>
      </div>
    </template>
  </FormRow>
</template>
