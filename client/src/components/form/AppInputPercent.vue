<script setup lang="ts">
import { useBindAttrs } from '@js/composable/useBindAttrs'
import AppInputNumber from '@js/components/form/AppInputNumber.vue'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

withDefaults(
  defineProps<{
    hasErrors?: boolean
    disabled?: boolean
  }>(),
  {
    hasErrors: false,
    disabled: false,
  }
)

const modelValue = defineModel<string>()
</script>
<template>
  <div
    class="app-input-percent relative transition-colors"
    :class="{ 'bg-skin-disabled': disabled, 'has-errors': hasErrors }"
    v-bind="rootAttrs"
  >
    <AppInputNumber
      v-model="modelValue"
      :has-errors="hasErrors"
      :disabled="disabled"
      v-bind="bindAttrs"
    />
    <span class="form-add-on-color absolute inset-y-0 right-0 mr-3 inline-flex items-center">
      %
    </span>
  </div>
</template>
