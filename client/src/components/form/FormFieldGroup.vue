<script setup lang="ts">
import { ref, toRef, watch } from 'vue'
import CollapsibleElement from '@js/components/CollapsibleElement.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

const props = withDefaults(
  defineProps<{
    label: string | false
    collapsible?: boolean
    collapsed?: boolean
  }>(),
  {
    collapsible: false,
    collapsed: false,
  }
)

const collapsed = ref(toRef(props, 'collapsed'))
const isOpen = ref(!collapsed.value)
watch(collapsed, (newValue) => {
  isOpen.value = !newValue
})

defineSlots<{
  default: () => unknown
}>()
</script>

<template>
  <CollapsibleElement v-model:open="isOpen" :default-open="!collapsed" as="fieldset">
    <legend v-if="label" class="form-label group leading-none font-medium">
      <button
        type="button"
        :disabled="!collapsible"
        :class="{ 'cursor-default': !collapsible }"
        class="flex items-center gap-2 py-1"
      >
        <span class="text-sm">
          {{ label }}
        </span>

        <SvgIcon
          v-if="collapsible"
          class="shrink-0 transform rounded-sm text-gray-400 opacity-20 transition ease-in-out group-hover:opacity-100"
          :class="{ '-rotate-90': !isOpen }"
          icon="arrow-down"
        />
      </button>
    </legend>

    <template #content>
      <div class="fields-grid mt-1 border-l-4 border-gray-200 pl-3">
        <slot />
      </div>
    </template>
  </CollapsibleElement>
</template>
