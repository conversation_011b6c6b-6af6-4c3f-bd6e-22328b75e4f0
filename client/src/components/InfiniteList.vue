<script lang="ts">
export const virtualListOptions = {
  estimatedItemSize: 300,
  overscan: 5,
}
</script>

<script setup lang="ts" generic="T extends ApiResource, TError">
import AppLoader from '@js/components/loader/AppLoader.vue'
import type { ApiResource, HydraCollectionResponse } from '@js/types'
import type { InfiniteData, UseInfiniteQueryReturnType } from '@tanstack/vue-query'
import { useVirtualizer } from '@tanstack/vue-virtual'
import { computed, nextTick, useTemplateRef, watch } from 'vue'

const { query } = defineProps<{
  query: UseInfiniteQueryReturnType<InfiniteData<HydraCollectionResponse<T>>, TError>
}>()

defineSlots<{
  item: (props: { item: T }) => unknown
}>()

const scrollContainer = useTemplateRef('scrollContainer')
const data = computed<InfiniteData<HydraCollectionResponse<T>, number | unknown> | undefined>(
  (previousItems) => {
    const newData = query.data.value
    if (newData && JSON.stringify(newData) === JSON.stringify(previousItems)) {
      return previousItems
    }
    return newData
  }
)
const isFetchingNextPage = computed(() => query.isFetchingNextPage.value)

const items = computed(() => {
  return data.value?.pages.map((page) => page['hydra:member']).flat() || []
})

const rowVirtualizerOptions = computed(() => ({
  count: items.value.length,
  getScrollElement: () => scrollContainer.value,
  estimateSize: () => virtualListOptions.estimatedItemSize,
  overscan: virtualListOptions.overscan, // The number of items to render above and below the visible area
  getItemKey: (index: number) => {
    return items.value[index]['@id']
  },
}))
const rowVirtualizer = useVirtualizer(rowVirtualizerOptions)
const virtualItems = computed(() => {
  return rowVirtualizer.value.getVirtualItems()
})
const totalSize = computed(() => rowVirtualizer.value.getTotalSize())
const measureElement = (element: HTMLElement) => {
  if (!element) {
    return
  }
  rowVirtualizer.value.measureElement(element)
  return undefined
}

const lastItem = computed(() => {
  const [lastItem] = [...virtualItems.value].reverse()
  return lastItem
})
const startOffset = computed(() => `${virtualItems.value[0]?.start ?? 0}px`)

watch(lastItem, (newValue) => {
  if (!newValue) {
    return
  }
  if (
    newValue.index >= items.value.length - 1 &&
    query.hasNextPage.value &&
    !isFetchingNextPage.value
  ) {
    query.fetchNextPage()
  }
})
/**
 * We need to watch 2 values to handle the following scenarios:
 * - When query data contains one page (pageParams changes to [1])
 * - When the first page content changes and pageParams remain [1] (e.g., when there is a single page with large entries
 *   that exceed the container height and the user scrolled down to the bottom).
 */
watch([() => data.value?.pages[0], () => data.value?.pageParams], ([, newPageParams]) => {
  if (newPageParams && newPageParams.length > 1) {
    return
  }

  if (newPageParams && newPageParams[0] === 1) {
    nextTick(() => {
      if (!scrollContainer.value) {
        return
      }
      scrollContainer.value.scrollTop = 0
    })
  }
})
</script>

<template>
  <div class="relative">
    <AppLoader
      v-if="isFetchingNextPage"
      class="absolute top-full left-full -mt-8 -ml-4 inline-flex"
      size="small"
    />

    <div
      ref="scrollContainer"
      class="scroll-container -m-4 max-h-96 overflow-y-auto p-4 contain-content"
      data-testId="scroll-container"
    >
      <div :style="{ height: `${totalSize}px` }" class="relative">
        <div class="absolute top-0 left-0" :style="`transform: translateY(${startOffset})`">
          <div
            v-for="virtualItem in virtualItems"
            :key="virtualItem.key as string"
            :ref="measureElement"
            :data-index="virtualItem.index"
          >
            <slot v-if="items[virtualItem.index]" name="item" :item="items[virtualItem.index]" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.scroll-container {
  overflow-anchor: none;
}
</style>
