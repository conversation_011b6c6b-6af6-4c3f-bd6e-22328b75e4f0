<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import { computed, ref, toRefs } from 'vue'
import { z } from 'zod'
import { auditor<PERSON><PERSON> } from '@js/api/auditorApi'
import FieldInputAddress from '@js/components/form/FieldInputAddress.vue'
import FieldInputText from '@js/components/form/FieldInputText.vue'
import FieldToggle from '@js/components/form/FieldToggle.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import useForm from '@js/composable/useForm'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { addressSchema } from '@js/model/address'
import Translator from '@js/translator'
import type { Auditor } from '@js/model/auditor'

const props = defineProps<{
  auditor?: Auditor
}>()

const { auditor } = toRefs(props)

const emit = defineEmits<{ (event: 'saved', payload: Auditor): void; (event: 'loaded'): void }>()
const { handleSubmit, setResponseErrors, unmappedErrors, setValues } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      name: z.string().min(1),
      enabled: z.boolean(),
      address: addressSchema.omit({ '@id': true }).optional().nullable(),
    })
  ),
  initialValues: {
    name: auditor.value?.name ?? '',
    enabled: auditor.value ? auditor.value.enabled : true,
    address: auditor.value?.address ?? null,
  },
})

const { resolveNotification } = useHandleAxiosErrorResponse()

const isSaving = ref(false)
const save = handleSubmit(async (values) => {
  isSaving.value = true
  try {
    if (auditor.value?.id) {
      const response = await auditorApi.updateAuditor({
        ...auditor.value,
        ...values,
      })
      const { name, enabled, address } = response.data
      setValues({ name, enabled, address })
      emit('saved', response.data)
      return
    }
    const response = await auditorApi.createAuditor(values)

    emit('saved', response.data)
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  } finally {
    isSaving.value = false
  }
})

const isInSavingState = computed(() => state.value === 'saving')
const state = computed(() => (isSaving.value ? 'saving' : 'ready'))
defineExpose({ state })
</script>

<template>
  <form id="auditor" name="auditor" @submit.prevent="save">
    <FormErrors :errors="unmappedErrors" />

    <FieldInputText
      :label="Translator.trans('u2_core.name')"
      name="name"
      required
      :disabled="isInSavingState"
    />

    <FieldInputAddress
      :disabled="isInSavingState"
      :label="Translator.trans('u2_core.address')"
      name="address"
    />

    <FieldToggle
      :disabled="isInSavingState"
      :label="Translator.trans('u2.enabled')"
      required
      name="enabled"
    />
  </form>
</template>
