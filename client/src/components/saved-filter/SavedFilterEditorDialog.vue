<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import AppDialog from '@js/components/AppDialog.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import SavedFilterEditor from '@js/components/saved-filter/SavedFilterEditor.vue'
import Translator from '@js/translator'
import type { SavedFilter } from '@js/model/saved-filter'

const emit = defineEmits<{ (event: 'saved', payload: SavedFilter): void; (event: 'close'): void }>()

defineProps<{
  title?: string
  savedFilter?: SavedFilter
}>()

function close() {
  emit('close')
}

const onSave = (savedFilter: SavedFilter) => {
  emit('saved', savedFilter)
}

const savedFilterEditor = useTemplateRef('savedFilterEditor')
</script>

<template>
  <AppDialog :title="title" @close="$emit('close')">
    <SavedFilterEditor
      ref="savedFilterEditor"
      class="w-96 max-w-full"
      :disabled="false"
      :saved-filter="savedFilter"
      @saved="onSave"
    />

    <template #buttons>
      <ButtonBasic @click="close">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonSave form="saved_filter" :state="savedFilterEditor?.state" />
    </template>
  </AppDialog>
</template>
