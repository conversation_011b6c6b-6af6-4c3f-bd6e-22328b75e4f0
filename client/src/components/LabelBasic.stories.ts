import LabelBasic from '@js/components/LabelBasic.vue'
import { icons, labelColors } from '@js/utilities/name-lists'
import type { Meta, StoryObj } from '@storybook/vue3'
import type { ComponentProps } from 'vue-component-type-helpers'

type PropsAndCustomArgs = ComponentProps<typeof LabelBasic> & {
  text?: string
}
const meta: Meta<PropsAndCustomArgs> = {
  title: 'Label',
  argTypes: {
    icon: {
      options: icons,
      control: {
        type: 'select',
      },
    },
    text: {
      control: {
        type: 'text',
      },
    },
    rounded: {
      options: ['none', 'half', 'full'],
      control: {
        type: 'select',
      },
    },
  },
  args: {
    text: undefined,
    rounded: 'half',
  },
}

export default meta

export const basic: StoryObj<typeof LabelBasic> = {
  render: (args, { argTypes }) => ({
    components: { LabelBasic },
    props: Object.keys(argTypes),
    setup() {
      return {
        args,
        labelColors,
      }
    },
    template: `
      <div class="flex flex-col gap-1 size-96">
        <div
            v-for="color in labelColors"
            :key="color"
        >
          <LabelBasic :color="color" :rounded="args.rounded-sm" :icon="args.icon">
            {{ args.text ? args.text : 'Color: "' + color + ' and some additional content that should push the label really wide. It is used to see if the overflow behaviour is correctly applied."' }}
          </LabelBasic>
        </div>
        <span class="pt-10">
        Surrounding the label with a span restricted to w-20 so truncate kicks in‚ <span class="overflow-hidden w-20"> <LabelBasic :color="color" :rounded="args.rounded-sm" :icon="args.icon" class="overflow-hidden w-20">
            {{ args.text ? args.text : 'Color: "' + color + ' and some additional content that should push the label really wide. It is used to see if the overflow behaviour is correctly applied."' }}
          </LabelBasic></span> And some text behind the label
      </span>
      </div>
    `,
  }),
}
