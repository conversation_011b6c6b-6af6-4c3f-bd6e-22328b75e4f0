<script setup lang="ts">
import LabelBasic from '@js/components/LabelBasic.vue'
import Translator from '@js/translator'
import type { labelColors } from '@js/utilities/name-lists'

withDefaults(
  defineProps<{
    color?: (typeof labelColors)[number]
    rounded?: 'none' | 'half' | 'full'
  }>(),
  {
    color: 'white',
    rounded: 'half',
  }
)
</script>

<template>
  <LabelBasic
    :color="color"
    :rounded="rounded"
    class="animate-pulse text-gray-500 lowercase italic"
  >
    <slot>{{ Translator.trans('u2.loading') }}</slot>
  </LabelBasic>
</template>
