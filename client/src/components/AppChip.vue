<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'

withDefaults(
  defineProps<{
    color?: 'gray' | 'red' | 'green'
    close?: boolean
  }>(),
  {
    color: 'gray',
    close: false,
  }
)
defineEmits<(event: 'close') => void>()
</script>

<template>
  <span
    :class="`bg-${color}-200 inline-flex items-center rounded-3xl px-3 py-0.5 whitespace-nowrap text-gray-800`"
  >
    <slot />

    <button
      v-if="close"
      :aria-label="Translator.trans('u2.clear')"
      type="button"
      class="inline-flex items-center text-gray-500 hover:text-gray-800 hover:no-underline focus:text-gray-800 focus:no-underline active:text-gray-500"
      @click.stop="$emit('close')"
    >
      <SvgIcon icon="cross" size="small" class="ml-1" />
    </button>
  </span>
</template>
