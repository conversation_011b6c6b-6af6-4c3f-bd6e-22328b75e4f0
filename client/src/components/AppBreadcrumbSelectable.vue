<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { BreadcrumbOption, BreadcrumbOptionGroup } from '@js/types'

type FlatBreadcrumbOption = BreadcrumbOption & {
  level?: number
}

type FlatBreadcrumbOptionGroup = Omit<BreadcrumbOptionGroup, 'options'> & {
  level?: number
}

function isBreadcrumbOptionGroup(
  option: BreadcrumbOptionGroup | BreadcrumbOption
): option is BreadcrumbOptionGroup {
  return 'label' in option
}
function isFlatBreadcrumbOptionGroup(
  option: FlatBreadcrumbOptionGroup | FlatBreadcrumbOption
): option is FlatBreadcrumbOptionGroup {
  return 'label' in option
}

const props = defineProps<{
  name: string
  options: Array<BreadcrumbOptionGroup | BreadcrumbOption>
}>()

const flatOptions = computed(() => flattenOptions(props.options))

function flattenOptions(options: Array<BreadcrumbOptionGroup | BreadcrumbOption>) {
  return options
    .map((option) => {
      if (isBreadcrumbOptionGroup(option)) {
        return [
          { label: option.label, level: 1 },
          ...option.options.map((option: BreadcrumbOption) => ({ ...option, level: 1 })),
        ]
      }
      return { ...option, level: 0 }
    })
    .flat()
}

const router = useRouter()
</script>

<template>
  <div class="flex items-center">
    <SvgIcon class="shrink-0 text-gray-400" icon="chevron-right" />

    <ButtonDropdown
      :arrow="false"
      class="ml-4 cursor-pointer text-sm font-medium text-gray-500 hover:text-gray-700"
    >
      {{ name }}

      <template #body>
        <div class="w-auto">
          <template v-for="option in flatOptions">
            <dt v-if="isFlatBreadcrumbOptionGroup(option)" :key="option.label" class="px-4 py-2">
              <span class="font-bold text-gray-700">{{ option.label }}</span>
            </dt>
            <ButtonDropdownItem
              v-else
              :key="option.name"
              :class="[
                option.level === 1 ? 'pr-4 pl-6' : 'px-4',
                { 'text-action-darker font-bold': option.current },
              ]"
              :disabled="option.current"
              :text="option.name"
              @click="router.push(option.to)"
            />
          </template>
        </div>
      </template>
    </ButtonDropdown>
  </div>
</template>
