<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import Translator from '@js/translator'
import AppLink from '@js/components/buttons/AppLink.vue'
import PopupCard from '@js/components/PopupCard.vue'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import { isUserWithAllProperties } from '@js/model/user'
import { queries } from '@js/query'
import type { User } from '@js/model/user'
import type { userLabelColors } from '@js/utilities/name-lists'

const props = withDefaults(
  defineProps<{
    color?: (typeof userLabelColors)[number]
    fallback?: string
    user?: User | User['id'] | null
  }>(),
  {
    color: 'gray',
    fallback: undefined,
    user: null,
  }
)

const { user } = toRefs(props)

const userId = computed(() => {
  if (typeof user.value === 'number') {
    return user.value
  }
  return user.value ? user.value.id : undefined
})

const useFallback = computed(() => !userId.value)
const fallback = computed(() => props.fallback ?? Translator.trans('u2.unknown'))

const { data: resolvedUser, isLoading } = useQuery({
  ...queries.users.single(userId),
  /*
  TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
    See: https://github.com/lukemorales/query-key-factory/issues/100
 */
  queryFn: computed(() => (userId.value ? queries.users.single(userId).queryFn : skipToken)),
  initialData:
    user.value && typeof user.value === 'object' && isUserWithAllProperties(user.value)
      ? user.value
      : undefined,
})
</script>

<template>
  <LabelWithMenu :disabled="useFallback" class="transition-colors duration-700" :color="color">
    <template #default>
      <span
        v-if="isLoading"
        class="animate-pulse text-gray-500 lowercase italic"
        v-text="Translator.trans('u2.loading')"
      />
      <span v-else-if="useFallback" class="text-gray-500 lowercase italic" v-text="fallback" />
      <span v-else-if="resolvedUser" v-text="resolvedUser.username" />
    </template>

    <template #content>
      <PopupCard>
        <template v-if="resolvedUser" #header>
          <div class="font-medium">
            <template v-if="resolvedUser.contact.nameFirst">
              {{ resolvedUser.contact.nameFirst }}
            </template>
            {{ resolvedUser.contact.nameLast }}
          </div>
          <div v-if="isUserWithAllProperties(resolvedUser)" class="mt-1 text-sm">
            {{ resolvedUser.contact.company }}
          </div>
        </template>

        <div v-if="resolvedUser" class="flex flex-col">
          <AppLink icon="mail" :to="'mailto:' + resolvedUser.contact.email">
            {{ Translator.trans('u2_core.send_email') }}
          </AppLink>
          <AppLink icon="user" :to="{ name: 'UserEdit', params: { id: resolvedUser.id } }">
            {{ Translator.trans('u2_core.view_profile') }}
          </AppLink>
        </div>
        <template v-else>
          <em>{{ Translator.trans('u2.loading') }}</em>
        </template>
      </PopupCard>
    </template>
  </LabelWithMenu>
</template>
