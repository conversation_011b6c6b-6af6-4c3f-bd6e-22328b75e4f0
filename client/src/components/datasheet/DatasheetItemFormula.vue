<script setup lang="ts">
import { toRefs } from 'vue'
import DatasheetItemFormulaElement from '@js/components/datasheet/DatasheetItemFormulaElement.vue'
import { splitFormulaIntoElements } from '@js/helper/datasheets/formula'
import Translator from '@js/translator'
import type { DataSheetNavigationContext, LayoutItem } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    item: LayoutItem
    context?: DataSheetNavigationContext
    colorize?: boolean
    showValues?: boolean
  }>(),
  {
    context: () => ({
      type: 'unit',
      unitId: undefined,
      periodId: undefined,
    }),
    colorize: false,
    showValues: false,
  }
)

const { item } = toRefs(props)

const formulaElements = splitFormulaIntoElements(item.value.formula ?? '')

defineSlots<{
  'element-dropdown-extra'?: (props: {
    item: LayoutItem
    context: DataSheetNavigationContext
  }) => unknown
}>()
</script>

<template>
  <span class="inline-flex flex-wrap gap-1">
    <i v-if="item.editable"> {{ Translator.trans('u2_table.default') }}: </i>

    <template v-for="(element, index) in formulaElements" :key="index + element">
      <DatasheetItemFormulaElement
        v-if="element.startsWith('{') && element.endsWith('}')"
        :formula-element="element"
        :colorize="colorize"
        :context="context"
        :show-value="showValues"
      >
        <template
          #element-dropdown-extra="{ item: itemFromDropdownSlot, context: contextFromDropdownSlot }"
        >
          <slot
            name="element-dropdown-extra"
            :item="itemFromDropdownSlot"
            :context="contextFromDropdownSlot"
          />
        </template>
      </DatasheetItemFormulaElement>

      <span v-else>{{ element }}</span>
    </template>
  </span>
</template>
