<script setup lang="ts">
import { computed, toRefs } from 'vue'
import Translator from '@js/translator'
import useItemQuery from '@js/composable/useItemQuery'
import DatasheetItemPopupCard from '@js/components/datasheet/DatasheetItemPopupCard.vue'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import type { DataSheetNavigationContext, LayoutItem } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    item: LayoutItem | LayoutItem['id']
    context?: DataSheetNavigationContext
  }>(),
  {
    context: () => ({
      type: 'unit',
      unitId: undefined,
      periodId: undefined,
    }),
  }
)

const { item, context } = toRefs(props)

defineSlots<{
  default?: (props: { item: LayoutItem }) => unknown
  'popupcard-dropdown-extra'?: (props: {
    item: LayoutItem
    context: DataSheetNavigationContext
  }) => unknown
}>()

const resolvedItemId = computed(() => {
  return !item.value ? undefined : typeof item.value === 'number' ? item.value : item.value.id
})

const initialData = computed(() =>
  item.value && typeof item.value === 'object' ? item.value : undefined
)

const {
  data: itemFromQuery,
  isLoading,
  isError,
} = useItemQuery(resolvedItemId, {
  initialData: initialData.value,
})
</script>

<template>
  <LabelWithMenu :disabled="isLoading || isError">
    <template #default>
      <span
        v-if="isLoading"
        class="animate-pulse text-gray-500 lowercase italic"
        v-text="Translator.trans('u2.loading')"
      />
      <span v-else-if="itemFromQuery">
        <slot :item="itemFromQuery">
          {{ itemFromQuery.refId }}
        </slot>
      </span>
      <span v-else-if="isError" v-text="Translator.trans('u2.unknown')" />
    </template>

    <template #content>
      <DatasheetItemPopupCard
        v-if="itemFromQuery"
        :item="itemFromQuery"
        :context="context"
        class="text-left"
      >
        <template
          #dropdown-extra="{ item: itemFromDropdownSlot, context: contextFromDropdownSlot }"
        >
          <slot
            name="popupcard-dropdown-extra"
            :item="itemFromDropdownSlot"
            :context="contextFromDropdownSlot"
          >
          </slot>
        </template>
      </DatasheetItemPopupCard>
    </template>
  </LabelWithMenu>
</template>
