<script setup lang="ts">
import { computed, ref, toRefs } from 'vue'
import { useQueryClient } from '@tanstack/vue-query'
import DraggableHandle from '@js/components/DraggableHandle.vue'
import DraggableList from '@js/components/DraggableList.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import { datasheetCollectionApi } from '@js/api/datasheetCollectionApi'
import SelectDatasheetsDialog from '@js/components/datasheet/SelectDatasheetsDialog.vue'
import Translator from '@js/translator'
import { vAnimate } from '@js/directives/animate'
import SvgIcon from '@js/components/SvgIcon.vue'
import { queries } from '@js/query'
import type { Datasheet } from '@js/model/datasheet'
import type { DatasheetCollection, DatasheetCollectionForm } from '@js/model/datasheetCollection'

const props = defineProps<{
  layoutCollection?: DatasheetCollection
  assignedLayouts?: Array<Datasheet>
}>()

const { layoutCollection, assignedLayouts } = toRefs(props)

const emit = defineEmits<{
  (event: 'loaded' | 'loading'): void
  (event: 'saved' | 'created', payload: DatasheetCollection): void
  (event: 'publicChanged', payload: boolean): void
}>()

const form = ref<DatasheetCollectionForm>({
  name: layoutCollection?.value?.name ?? '',
  public: layoutCollection?.value?.public ?? false,
})

function save() {
  emit('loading')
  if (layoutCollection?.value && '@id' in layoutCollection.value) {
    datasheetCollectionApi
      .updateDatasheetCollection(layoutCollection.value, {
        public: form.value.public,
        name: form.value.name,
      })
      .then(async ({ data: updatedLayoutCollection }) => {
        await datasheetCollectionApi.updateDatasheetCollectionAssignedLayouts(
          updatedLayoutCollection.id,
          selectedLayoutIris.value
        )
        emit('saved', updatedLayoutCollection)
      })
      .finally(() => emit('loaded'))
    return
  }

  datasheetCollectionApi
    .createDatasheetCollection({
      public: form.value.public,
      name: form.value.name,
    })
    .then(async ({ data: createdLayoutCollection }) => {
      await datasheetCollectionApi.updateDatasheetCollectionAssignedLayouts(
        createdLayoutCollection.id,
        selectedLayoutIris.value
      )

      emit('created', createdLayoutCollection)
    })
    .finally(() => emit('loaded'))
}

const showSelectLayoutsDialog = ref(false)
const layoutsFromServer = ref<Array<Datasheet>>([])

type LayoutGroup = {
  id: string
  name: string
  layouts: Array<Datasheet>
}
const layoutsByGroup = ref<Array<LayoutGroup>>([])

const queryClient = useQueryClient()

await queryClient.fetchQuery(queries.datasheets.all).then((data) => {
  layoutsFromServer.value = data['hydra:member']
  if (assignedLayouts?.value !== undefined) {
    layoutsByGroup.value = Object.values(
      assignedLayouts.value.reduce(
        (grouped, layout) => {
          const groupName = layout.group
          if (!grouped[groupName]) {
            grouped[groupName] = {
              id: groupName,
              name: groupName,
              layouts: [],
            }
          }

          grouped[groupName].layouts.push(layout)

          return grouped
        },
        {} as Record<string, LayoutGroup>
      )
    )
  }
})

const selectedLayoutIris = computed<Array<Datasheet['@id']>>(() => {
  return layoutsByGroup.value.map((group) => group.layouts.map((layout) => layout['@id'])).flat()
})
const handleSelectedLayouts = (layoutIris: Array<string>) => {
  layoutsByGroup.value = Object.values(
    layoutIris
      .map((layoutIri) => layoutsFromServer.value.find((layout) => layout['@id'] === layoutIri))
      .filter((layout): layout is Datasheet => layout !== undefined)
      .reduce(
        (grouped, layout) => {
          const groupName = layout.group
          if (!grouped[groupName]) {
            grouped[groupName] = { id: groupName, name: groupName, layouts: [] }
          }

          grouped[groupName].layouts.push(layout)

          return grouped
        },
        {} as Record<string, LayoutGroup>
      )
  )
  showSelectLayoutsDialog.value = false
}

const handlePublicChanged = (newValue: boolean) => {
  form.value.public = newValue
  emit('publicChanged', newValue)
}
</script>

<template>
  <form id="layout_collection_form" name="layout_collection_form" @submit.prevent="save">
    <BaseInputText
      v-model="form.name"
      name="layout_collection_form[name]"
      :label="Translator.trans('u2.name')"
      :required="true"
    />

    <BaseToggle
      v-model="form.public"
      :label="Translator.trans('u2.visible_to_all_users')"
      name="layout_collection_form[public]"
      @update:model-value="handlePublicChanged"
    />

    <HeaderWithAction icon="comment" underline>
      {{ Translator.trans('u2.datasheets.datasheet') }} ({{ selectedLayoutIris.length }})
      <template #button>
        <ButtonBasic @click="showSelectLayoutsDialog = true"> Select Layouts</ButtonBasic>
      </template>
    </HeaderWithAction>

    <template v-if="layoutsByGroup">
      <div v-animate>
        <DraggableList
          v-model="layoutsByGroup"
          group="layout-groups"
          :animation="300"
          item-key="name"
        >
          <template #item="{ item: group }">
            <div class="mt-1">
              <h2 class="flex items-center gap-2">
                <DraggableHandle :tag="SvgIcon" icon="drag-handle-dots" />
                {{ group.name }}
              </h2>
              <DraggableList
                v-model="group.layouts"
                item-key="id"
                :group="group.name"
                :animation="300"
                class="inline-flex flex-col items-start"
              >
                <template #item="{ item: element }">
                  <div class="ml-5 inline-flex items-center gap-2">
                    <DraggableHandle :tag="SvgIcon" icon="drag-handle-dots" class="shrink-0" />
                    {{ element.name }}
                  </div>
                </template>
              </DraggableList>
            </div>
          </template>
        </DraggableList>
      </div>
    </template>
  </form>

  <SelectDatasheetsDialog
    v-if="showSelectLayoutsDialog"
    :layouts="selectedLayoutIris"
    @layouts-selected="handleSelectedLayouts"
    @close="showSelectLayoutsDialog = false"
  />
</template>
