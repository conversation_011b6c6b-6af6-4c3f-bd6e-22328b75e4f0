<script setup lang="ts">
import { useConfirmDialog } from '@vueuse/core'
import { computed, ref, useTemplateRef } from 'vue'
import { useQueryClient } from '@tanstack/vue-query'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import Translator from '@js/translator'
import DatasheetFieldPopupCard from '@js/components/datasheet/DatasheetFieldPopupCard.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import { datasheetFieldApi } from '@js/api/datasheetFieldApi'
import { useNotificationsStore } from '@js/stores/notifications'
import { queries } from '@js/query'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import AppDialog from '@js/components/AppDialog.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import FieldEditor from '@js/components/FieldEditor.vue'
import LabelBasic from '@js/components/LabelBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import type { DataSheetNavigationContext, Datasheet, Field } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    field?: Field
    datasheet: Datasheet
    context?: DataSheetNavigationContext
    fallback?: string
    fieldNameSuggestions?: Array<string>
  }>(),
  {
    field: undefined,
    context: () => ({
      type: 'unit',
      unitId: undefined,
      periodId: undefined,
    }),
    fallback: undefined,
    fieldNameSuggestions: () => [],
  }
)

const {
  isRevealed: isConfirmDeleteFieldRevealed,
  reveal: revealConfirmDeleteField,
  confirm: confirmDeleteField,
  cancel: cancelDeleteField,
} = useConfirmDialog()

const { resolveNotification } = useHandleAxiosErrorResponse()

const deleteField = async (field: Field) => {
  const { isCanceled } = await revealConfirmDeleteField()
  if (!isCanceled) {
    try {
      await datasheetFieldApi.deleteDatasheetField(field.id, props.datasheet.id)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      invalidateQueries()
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const queryClient = useQueryClient()
function invalidateQueries() {
  queryClient.invalidateQueries({
    queryKey: queries.datasheets.single(props.datasheet.id)._ctx.fields.queryKey,
  })
}

const showFieldDialog = ref(false)
const fieldEditor = useTemplateRef<InstanceType<typeof FieldEditor>>('fieldEditor')
const isSaving = computed(() => fieldEditor.value?.state === 'saving')
const selectedField = ref()
function closeFieldDialog() {
  selectedField.value = undefined
  showFieldDialog.value = false
}
</script>

<template>
  <LabelBasic
    v-if="!field"
    v-tooltip="Translator.trans('u2.field.click_to_create')"
    class="inline cursor-pointer transition-colors duration-700 hover:opacity-70"
    tabindex="0"
    color="alert"
    :text="fallback"
    @click="
      () => {
        selectedField = {
          name: fallback,
        }
        showFieldDialog = true
      }
    "
  />

  <LabelWithMenu
    v-else
    :tooltip="field.disabled ? Translator.trans('u2.datasheets.field_disabled') : undefined"
    :color="field.disabled ? 'gray' : undefined"
  >
    <template #default>
      <span v-if="field">
        {{ field.name }}
      </span>
      <span v-else>
        {{ fallback }}
      </span>
    </template>

    <template #content>
      <DatasheetFieldPopupCard
        :field="field"
        :datasheet="datasheet"
        :context="context"
        class="text-left"
      >
        <template #dropdown-extra="{ field: fieldFromDropdownSlot }">
          <ButtonEdit
            @click="
              () => {
                selectedField = fieldFromDropdownSlot
                showFieldDialog = true
              }
            "
          />
          <ButtonDelete :show-text="false" @click="deleteField(fieldFromDropdownSlot)" />
        </template>
      </DatasheetFieldPopupCard>
    </template>
  </LabelWithMenu>

  <ConfirmationDialog
    v-if="isConfirmDeleteFieldRevealed"
    @close="cancelDeleteField"
    @confirm="confirmDeleteField"
  >
    {{
      Translator.trans('u2.datasheets.field.delete.confirm', {
        fieldId: field.name,
        layoutName: datasheet?.name,
      })
    }}
  </ConfirmationDialog>

  <AppDialog
    v-if="showFieldDialog && datasheet"
    class="w-full max-w-5xl"
    :title="
      selectedField.id
        ? `${Translator.trans('u2.field')} #${selectedField.name}`
        : Translator.trans('u2.new_entity_type_name', {
            entity_type_name: Translator.trans('u2.field'),
          })
    "
    @close="closeFieldDialog"
  >
    <FieldEditor
      ref="fieldEditor"
      :datasheet="datasheet"
      :field="selectedField"
      @saved="closeFieldDialog"
    />
    <template #buttons>
      <ButtonBasic @click="closeFieldDialog">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonSave form="field_form" button-style="solid" :disabled="isSaving" />
    </template>
  </AppDialog>
</template>
