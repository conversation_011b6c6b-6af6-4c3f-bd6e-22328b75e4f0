<script setup lang="ts">
import { computed, ref, unref, watch } from 'vue'
import { isAxiosError } from 'axios'
import { saveAs } from 'file-saver'
import { useHead } from '@vueuse/head'
import invariant from 'tiny-invariant'
import { groupViewDataApi } from '@js/api/groupViewDataApi'
import LayoutCollectionLayoutBrowser from '@js/components/datasheet/DatasheetCollectionDatasheetBrowser.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import GroupViewToolbar from '@js/components/datasheet/GroupViewToolbar.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Routing from '@js/Routing'
import Translator from '@js/translator'
import useLayoutCollectionAssignedLayoutsQuery from '@js/composable/useLayoutCollectionAssignedLayoutsQuery'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import DatasheetFieldInspector from '@js/components/datasheet/DatasheetFieldInspector.vue'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import useImmediateRouteQuery from '@js/composable/useImmediateRouteQuery'
import { numberToString, stringToNumber } from '@js/utilities/data-transformers'
import { skipToken, useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import useLayoutCollectionQuery from '@js/composable/useLayoutCollectionQuery'
import AppError from '@js/pages/error/AppError.vue'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import { getIdFromIri } from '@js/utilities/api-resource'
import RenderedHierarchyViewTemplate from '@js/components/datasheet/RenderedHierarchyViewTemplate.vue'
import InfoBox from '@js/components/InfoBox.vue'
import { StatusCodes } from 'http-status-codes'
import useDatasheetTemplate from '@js/composable/datasheet/useDatasheetTemplate'
import AppLoader from '@js/components/loader/AppLoader.vue'
import { itemUnitHierarchyValueApi } from '@js/api/itemUnitHierarchyValueApi'
import type { ItemUnitHierarchyValue } from '@js/model/datasheet'

const datasheetParamStore = useDatasheetParametersStore()
const sheetId = computed(() => datasheetParamStore.parameters.layout)
const sheetCollectionId = computed(() => datasheetParamStore.parameters.layoutCollection)

const { data: sheetCollection, suspense } = useLayoutCollectionQuery(sheetCollectionId)
await suspense()

useImmediateRouteQuery('unitHierarchy', datasheetParamStore.parameters.unitHierarchy, {
  get: stringToNumber,
  set: numberToString,
})

const { items: layouts, isLoading } = useLayoutCollectionAssignedLayoutsQuery(sheetCollectionId)

const sheet = computed(() => layouts.value.find((sheet) => sheet.id === sheetId.value))

datasheetParamStore.parameters.selectedView = 'group'
const datasheetCollectionId = computed(() => sheetCollection.value?.id)
const datasheetId = computed(() => sheet.value?.id)
const periodId = computed(() => datasheetParamStore.parameters.period)
const unitHierarchyId = computed(() => datasheetParamStore.parameters.unitHierarchy)

const isParametersValid = computed(
  () => periodId.value !== undefined && unitHierarchyId.value !== undefined
)

const { data: period } = useQuery({
  ...queries.periods.single(periodId),
  /*
    TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
      See: https://github.com/lukemorales/query-key-factory/issues/100
   */
  queryFn: computed(() => (periodId.value ? queries.periods.single(periodId).queryFn : skipToken)),
})

useHead({
  title: () => (sheet.value ? `${sheet.value.name} - Group Layout` : 'Group Layout'),
})

const { resolveNotification } = useHandleAxiosErrorResponse()

async function downloadXls() {
  try {
    invariant(periodId.value && unitHierarchyId.value && sheet.value && datasheetCollectionId.value)
    const { data } = await groupViewDataApi.downloadUnitHierarchyViewDataXls({
      datasheetCollection: datasheetCollectionId.value,
      datasheet: sheet.value.id,
      period: periodId.value,
      unitHierarchy: unitHierarchyId.value,
    })

    saveAs(data, `${sheet.value.name} - Unit Hierarchy View.xlsx`)
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    if (error.response.data.detail) {
      error.response.data.detail.split('\n').forEach((detail: string) => {
        useNotificationsStore().addError(detail)
      })
    }
  }
}

const fieldInspectorStore = useFieldInspectorStore()

function isNavigationContextValid(navigationContext: {
  unitHierarchyId?: number
  periodId?: number
}) {
  return !!navigationContext.unitHierarchyId && !!navigationContext.periodId
}
const { datasheetTemplateHtml } = useDatasheetTemplate(datasheetId)

const { data: allFieldsData } = useQuery({
  refetchOnWindowFocus: false,
  ...queries.datasheets.single(datasheetId)._ctx.fields,
  /*
   TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
     See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() => {
    return unref(datasheetId)
      ? queries.datasheets.single(datasheetId)._ctx.fields.queryFn
      : skipToken
  }),
})

const fields = computed(() => allFieldsData.value?.data?.['hydra:member'])

const itemUnitHierarchyValues = ref<Array<ItemUnitHierarchyValue>>()
const areItemUnitHierarchyValuesInitialLoading = ref(true)
const itemUnitHierarchyValuesError = ref()
async function fetchItemUnitHierarchyValues() {
  if (!periodId.value || !unitHierarchyId.value) {
    itemUnitHierarchyValues.value = undefined
    return
  }

  await itemUnitHierarchyValueApi
    .fetchItemUnitHierarchyValuesByQuery({
      period: periodId.value,
      unitHierarchy: unitHierarchyId.value,
      datasheet: datasheetId.value,
      pagination: false,
    })
    .then((response) => {
      itemUnitHierarchyValues.value = response.data['hydra:member']
    })
    .catch((error) => {
      if (error.response?.status === StatusCodes.NOT_FOUND) {
        itemUnitHierarchyValues.value = []
        return
      }

      itemUnitHierarchyValuesError.value = error
    })
    .finally(() => {
      areItemUnitHierarchyValuesInitialLoading.value = false
    })
}

watch([periodId, unitHierarchyId], async () => {
  areItemUnitHierarchyValuesInitialLoading.value = true
  fetchItemUnitHierarchyValues()
})

fetchItemUnitHierarchyValues()

const { data: systemSettingsAll } = useSystemSettingsAllQuery()
const currencyId = computed(() => {
  const iri = systemSettingsAll.value?.applicationCurrency

  return iri ? getIdFromIri(iri) : undefined
})

const { data: currencyFromServer } = useQuery({
  ...queries.currencies.single(currencyId),
  /*
   TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
     See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() =>
    currencyId.value ? queries.currencies.single(currencyId).queryFn : skipToken
  ),
  enabled: computed(() => !!unref(currencyId)),
})
const moneyScale = computed(() => currencyFromServer.value?.scale)
</script>

<template>
  <AppPageWide>
    <template #header>
      <PageHeader v-if="sheetCollection">
        <template #title>
          <PageHeaderTitle :subtitle="sheet?.name ?? ''" :title="sheetCollection.name" />
        </template>

        <template #default>
          <LayoutCollectionLayoutBrowser
            class="grow justify-end print:hidden"
            :layouts="layouts"
            :layout-collection="sheetCollection"
            :unit-hierarchy-id="unitHierarchyId"
          />
          <ButtonDropdownEllipsis>
            <template #items>
              <ButtonDropdownItem
                :disabled="!isParametersValid"
                icon="document-xls"
                target="_blank"
                @click="downloadXls"
              >
                {{ Translator.trans('u2.datasheets.datasheet') }} (Excel)
              </ButtonDropdownItem>

              <ButtonDropdownItem
                icon="document-csv"
                :disabled="!period"
                :to="Routing.generate('u2_itemunitvalue_export', { id: period?.id })"
                target="_blank"
              >
                {{ Translator.trans('u2.export.item_values.csv') }}
              </ButtonDropdownItem>
            </template>
          </ButtonDropdownEllipsis>
        </template>
        <template #more>
          <GroupViewToolbar
            :layout-collection-id="sheetCollection.id"
            editable
            has-unit-view-switch
            has-field-inspector-control
          />
          <DatasheetFieldInspector
            :context="{
              periodId,
              unitHierarchyId,
            }"
          >
            <template
              #dropdown-extra="{ item: itemFromDropdownSlot, context: contextFromDropdownSlot }"
            >
              <ButtonDropdownItem
                v-tooltip="
                  !isNavigationContextValid(contextFromDropdownSlot)
                    ? Translator.trans('u2.inspect.insufficient_context')
                    : undefined
                "
                :disabled="!isNavigationContextValid(contextFromDropdownSlot)"
                icon="breakdown"
                :text="Translator.trans('u2.breakdown')"
                :to="{
                  name: 'DatasheetCollectionGroupViewBreakdown',
                  query: {
                    item: itemFromDropdownSlot.id,
                    sheetCollection: datasheetCollectionId,
                    layout: datasheetParamStore.parameters.layout,
                    period: contextFromDropdownSlot.periodId,
                    unitHierarchy:
                      'unitHierarchyId' in contextFromDropdownSlot
                        ? contextFromDropdownSlot.unitHierarchyId
                        : undefined,
                  },
                }"
              />
              <ButtonDropdownItem
                icon="view"
                :disabled="
                  !isNavigationContextValid(contextFromDropdownSlot) ||
                  !fieldInspectorStore.fieldByItemId.has(itemFromDropdownSlot['id']) ||
                  fieldInspectorStore.fieldByItemId.get(itemFromDropdownSlot['id'])?.id ===
                    fieldInspectorStore.field?.id
                "
                :tooltip="
                  !isNavigationContextValid(contextFromDropdownSlot)
                    ? Translator.trans('u2.inspect.insufficient_context')
                    : fieldInspectorStore.buildFieldInspectButtonTooltip(itemFromDropdownSlot)
                "
                :text="Translator.trans('u2.inspect')"
                :to="{
                  name: 'DatasheetCollectionSheetView',
                  params: {
                    id: datasheetCollectionId,
                    sheetId: datasheetId,
                  },
                  query: {
                    period: contextFromDropdownSlot.periodId,
                    unitHierarchy:
                      'unitHierarchyId' in contextFromDropdownSlot
                        ? contextFromDropdownSlot.unitHierarchyId
                        : undefined,
                    field: fieldInspectorStore.fieldByItemId.get(itemFromDropdownSlot['id'])?.id,
                  },
                }"
              />
            </template>
          </DatasheetFieldInspector>
        </template>
      </PageHeader>
    </template>
    <template v-if="itemUnitHierarchyValuesError">
      <InfoBox
        v-if="
          isAxiosError(itemUnitHierarchyValuesError) &&
          itemUnitHierarchyValuesError.response?.status === StatusCodes.FORBIDDEN
        "
        icon="blocked"
        :title="Translator.trans('u2.access_denied')"
      />
      <InfoBox v-else icon="alert" :title="Translator.trans('u2.error')" />
    </template>
    <template v-else-if="!sheet && !isLoading">
      <AppError code="404" :title="Translator.trans('u2.page_not_found')" icon="search">
        <p>{{ Translator.trans('u2.http_404') }}</p>
      </AppError>
    </template>
    <template v-else-if="!isParametersValid">
      <InfoBox icon="alert" :title="Translator.trans('u2.datasheet.missing_configuration')" />
    </template>
    <AppLoader v-else-if="areItemUnitHierarchyValuesInitialLoading" />
    <RenderedHierarchyViewTemplate
      v-else-if="
        moneyScale !== undefined &&
        fields &&
        datasheetTemplateHtml &&
        itemUnitHierarchyValues !== undefined
      "
      :money-scale="moneyScale"
      :template="datasheetTemplateHtml"
      :fields="fields"
      :values="itemUnitHierarchyValues"
    />
  </AppPageWide>
</template>
