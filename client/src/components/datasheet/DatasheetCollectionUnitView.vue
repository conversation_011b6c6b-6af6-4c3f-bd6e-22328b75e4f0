<script lang="ts" setup>
import type { Period } from '@js/api/periodApi'
import $ from 'jquery'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import { computed, ref, unref, watch } from 'vue'
import { saveAs } from 'file-saver'
import { StatusCodes } from 'http-status-codes'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppMessage from '@js/components/AppMessage.vue'
import { unitViewDataApi } from '@js/api/unitViewDataApi'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import DropdownMenuDivider from '@js/components/buttons/DropdownMenuDivider.vue'
import InfoBox from '@js/components/InfoBox.vue'
import LayoutCollectionLayoutBrowser from '@js/components/datasheet/DatasheetCollectionDatasheetBrowser.vue'
import DatasheetFieldInspector from '@js/components/datasheet/DatasheetFieldInspector.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import Routing from '@js/Routing'
import TransitionButton from '@js/components/task/TransitionButton.vue'
import Translator from '@js/translator'
import UnitViewToolbar from '@js/components/datasheet/UnitViewToolbar.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import useLayoutCollectionAssignedLayoutsQuery from '@js/composable/useLayoutCollectionAssignedLayoutsQuery'
import useLayoutCollectionUnitViewDataMutation from '@js/composable/useLayoutCollectionUnitViewDataMutation'
import { queries } from '@js/query'
import { useAuthStore } from '@js/stores/auth'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import { useNotificationsStore } from '@js/stores/notifications'
import { usePageStore } from '@js/stores/page'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import type { ItemUnitValue } from '@js/model/datasheet'
import type { Unit } from '@js/model/unit'
import useImmediateRouteQuery from '@js/composable/useImmediateRouteQuery'
import { numberToString, stringToNumber, transformIfDefined } from '@js/utilities/data-transformers'
import RenderedUnitViewTemplate from '@js/components/datasheet/RenderedUnitViewTemplate.vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import useDatasheetTemplate from '@js/composable/datasheet/useDatasheetTemplate'
import { getIdFromIri } from '@js/utilities/api-resource'
import AppLoader from '@js/components/loader/AppLoader.vue'
import useLayoutCollectionUnitViewDataQuery from '@js/composable/useLayoutCollectionUnitViewDataQuery'
import { itemUnitValueApi } from '@js/api/itemUnitValueApi'
import useLayoutCollectionQuery from '@js/composable/useLayoutCollectionQuery'
import AppError from '@js/pages/error/AppError.vue'

const datasheetParamStore = useDatasheetParametersStore()
const sheetId = computed(() => datasheetParamStore.parameters.layout)
const sheetCollectionId = computed(() => datasheetParamStore.parameters.layoutCollection)

const { data: sheetCollection, suspense } = useLayoutCollectionQuery(sheetCollectionId)
await suspense()

useImmediateRouteQuery('unit', datasheetParamStore.parameters.unit, {
  get: stringToNumber,
  set: numberToString,
})

const { items: layouts, isLoading } = useLayoutCollectionAssignedLayoutsQuery(sheetCollectionId)

const sheet = computed(() => layouts.value.find((sheet) => sheet.id === sheetId.value))

const tasks = computed(() => pageData.value?.tasks ?? [])

datasheetParamStore.parameters.selectedView = 'unit'
const datasheetCollectionId = computed(() => sheetCollection.value?.id)
const datasheetId = computed(() => sheet.value?.id)
const periodId = computed(() => datasheetParamStore.parameters.period)
const unitId = computed(() => datasheetParamStore.parameters.unit)

const { data: unit } = useQuery({
  ...queries.units.single(unitId),
  /*
    TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
      See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() => (unitId.value ? queries.units.single(unitId).queryFn : skipToken)),
})
const { data: period } = useQuery({
  ...queries.periods.single(periodId),
  /*
    TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
      See: https://github.com/lukemorales/query-key-factory/issues/100
   */
  queryFn: computed(() => (periodId.value ? queries.periods.single(periodId).queryFn : skipToken)),
})

const isParametersValid = computed(() => !!(periodId.value && unitId.value))

useHead({
  title: () => (sheet.value ? `${sheet.value.name} - Unit Layout` : 'Unit Layout'),
})

const { mutate, isPending } = useLayoutCollectionUnitViewDataMutation()

const pageStore = usePageStore()
watch(isPending, (newValue) => {
  pageStore.loading = newValue
})

async function save() {
  const form = $('form', '#content')[0] as HTMLFormElement

  if (!isParametersValid.value) {
    return
  }

  if ('reportValidity' in form && !form.checkValidity()) {
    form.reportValidity()
    return
  }

  invariant(periodId.value && unitId.value && datasheetId.value && datasheetCollectionId.value)
  const formData = new window.FormData(form)
  mutate(
    {
      layoutUnitViewDataParams: {
        layoutId: datasheetId.value,
        periodId: periodId.value,
        unitId: unitId.value,
        layoutCollectionId: datasheetCollectionId.value,
      },
      formData,
    },
    {
      async onSuccess(data) {
        useNotificationsStore().addByType(data.messages)

        fetchItemUnitValues()
      },
    }
  )
}

const parametersExist = computed(() => !!unit.value?.id && !!period.value?.id)

const {
  isRevealed: isConfirmRecalculateRevealed,
  reveal: revealConfirmRecalculate,
  confirm: confirmRecalculate,
  cancel: cancelRecalculate,
} = useConfirmDialog()

const { resolveNotification } = useHandleAxiosErrorResponse()

async function recalculate() {
  const { isCanceled } = await revealConfirmRecalculate()
  if (!isCanceled) {
    pageStore.loading = true
    const notificationStore = useNotificationsStore()
    try {
      invariant(unit.value && period.value)
      const response = await unitViewDataApi.recalculateDatasheetUnitPeriod({
        unit: unit.value.id,
        period: period.value.id,
      })
      notificationStore.addByType(response.data.messages)
      await fetchPageData()
      fetchItemUnitValues()
    } catch (error) {
      resolveNotification(error)
    } finally {
      pageStore.loading = false
    }
  }
}

async function downloadXls() {
  try {
    invariant(periodId.value && unitId.value && sheet.value && datasheetCollectionId.value)
    const { data } = await unitViewDataApi.downloadDatasheetUnitViewXls({
      period: periodId.value,
      unit: unitId.value,
      datasheet: sheet.value.id,
    })

    saveAs(data, `${sheet.value.name} - Unit View.xlsx`)
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    if (error.response.data.detail) {
      error.response.data.detail.split('\n').forEach((detail: string) => {
        useNotificationsStore().addError(detail)
      })
    }
  }
}

const authStore = useAuthStore()
const isAdmin = computed(() => authStore.hasRole('ROLE_ADMIN'))
const fieldInspectorStore = useFieldInspectorStore()

const layoutCollectionUnitViewDataParams = computed(() => ({
  layoutId: datasheetId.value,
  periodId: periodId.value,
  unitId: unitId.value,
  layoutCollectionId: datasheetCollectionId.value,
}))

const {
  data: pageData,
  error: pageDataError,
  refetch: fetchPageData,
  suspense: suspensePageData,
} = useLayoutCollectionUnitViewDataQuery(layoutCollectionUnitViewDataParams)

const taskExists = computed(() => !!pageData.value?.task)
const itemUnitValues = ref<Array<ItemUnitValue>>()

const areItemUnitValuesInitialLoading = ref(true)
async function fetchItemUnitValues() {
  if (!periodId.value || !unitId.value) {
    return
  }

  if (!taskExists.value) {
    itemUnitValues.value = []
    areItemUnitValuesInitialLoading.value = false
    return
  }

  const itemUnitValuesResponse = await itemUnitValueApi
    .fetchItemUnitValuesByQuery({
      'period.id': periodId.value,
      'unit.id': unitId.value,
      'datasheet.id': datasheetId.value,
      pagination: false,
    })
    .then((response) => response.data)

  itemUnitValues.value = itemUnitValuesResponse['hydra:member']
  areItemUnitValuesInitialLoading.value = false
}

watch([periodId, unitId], async () => {
  areItemUnitValuesInitialLoading.value = true
  await fetchPageData()
  fetchItemUnitValues()
})

function isNavigationContextValid(navigationContext: {
  unitId?: Unit['id']
  periodId?: Period['id']
}) {
  return !!navigationContext.unitId && !!navigationContext.periodId
}

if (unitId.value && periodId.value) {
  await suspensePageData()
  fetchItemUnitValues()
}

const errors = computed(() => pageData.value?.violations ?? [])
const isFormDisabled = computed(() => pageData.value?.isFormDisabled ?? true)

const { datasheetTemplateHtml } = useDatasheetTemplate(datasheetId)

const { data: allFieldsData } = useQuery({
  refetchOnWindowFocus: false,
  ...queries.datasheets.single(datasheetId)._ctx.fields,
  /*
   TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
     See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() => {
    return unref(datasheetId)
      ? queries.datasheets.single(datasheetId)._ctx.fields.queryFn
      : skipToken
  }),
})

const fields = computed(() => allFieldsData.value?.data?.['hydra:member'])

const { data: unitFromServer, isLoading: isUnitLoading } = useQuery({
  refetchOnWindowFocus: false,
  ...queries.units.single(unitId),
  /*
   TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
     See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() => (unitId.value ? queries.units.single(unitId).queryFn : skipToken)),
})

const currencyId = computed(() =>
  transformIfDefined(unitFromServer.value?.currency, (iri) => (iri ? getIdFromIri(iri) : undefined))
)

const { data: currencyFromServer } = useQuery({
  refetchOnWindowFocus: false,
  ...queries.currencies.single(currencyId),
  /*
   TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
     See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() =>
    currencyId.value ? queries.currencies.single(currencyId.value).queryFn : skipToken
  ),
})
const moneyScale = computed(() =>
  !isUnitLoading.value ? (currencyFromServer.value?.scale ?? 0) : undefined
)
</script>

<template>
  <AppPageWide>
    <template #header>
      <PageHeader v-if="sheetCollection">
        <template #title>
          <PageHeaderTitle :subtitle="sheet?.name ?? ''" :title="sheetCollection.name" />
        </template>
        <template #default>
          <ButtonBasic
            v-if="tasks.length === 1"
            :to="tasks[0]['u2:extra'].editPath"
            icon="edit"
            :tooltip="Translator.trans('u2.edit_configuration')"
          >
            {{ tasks[0]['u2:extra'].displayName }}
          </ButtonBasic>

          <ButtonDropdown
            v-if="tasks.length > 1"
            :tooltip="
              Translator.trans('u2.more_than_one_unit_period_found_matching_this_configuration')
            "
          >
            {{ Translator.trans('u2.update_tasks') }}
            <template #body>
              <ButtonDropdownItem
                v-for="task in tasks"
                :key="task['@id']"
                :text="task['u2:extra']?.displayName"
                :to="{
                  name: 'UnitPeriodEdit',
                  params: { id: task['u2:extra']?.taskTypeId },
                }"
              />

              <DropdownMenuDivider />

              <ButtonDropdownItem
                v-if="pageData"
                icon="list"
                :text="Translator.trans('u2.show_on_list')"
                :to="{
                  name: 'UnitPeriodList',
                  query: {
                    q: 'Id in [%s]'.replace(
                      '%s',
                      pageData.tasks.map((task) => task['u2:extra']?.taskTypeId).join(',')
                    ),
                  },
                }"
              />
            </template>
          </ButtonDropdown>

          <TransitionButton
            v-if="pageData?.tasks.length === 1"
            :status="pageData.status"
            :task="pageData.task"
            :transitions="pageData.transitions"
            @success="
              () => {
                fetchPageData()
                fetchItemUnitValues()
              }
            "
          />

          <ButtonSpacer />

          <LayoutCollectionLayoutBrowser
            class="print:hidden"
            :layouts="layouts"
            :layout-collection="sheetCollection"
            :unit-id="unitId"
          />

          <ButtonNew
            v-if="pageData?.tasks.length === 0"
            id="new-button"
            :to="{ name: 'UnitPeriodNew' }"
          />

          <ButtonSave
            v-if="pageData"
            :disabled="!pageData.canSave"
            :state="isPending ? 'loading' : 'ready'"
            @click="save"
          />

          <ButtonDropdownEllipsis>
            <template #items>
              <ButtonDropdownItem
                icon="refresh"
                :text="Translator.trans('u2.recalculate')"
                :disabled="!taskExists"
                @click="recalculate"
              />

              <ButtonDropdownItem
                v-if="isAdmin && sheet"
                icon="config"
                :text="Translator.trans('u2.edit_configuration')"
                :to="{ name: 'DatasheetEdit', params: { id: sheet.id } }"
              />

              <DropdownMenuDivider text="Export" />

              <ButtonDropdownItem
                icon="document-xls"
                :disabled="!(parametersExist && taskExists)"
                target="_blank"
                @click="downloadXls"
              >
                Excel
              </ButtonDropdownItem>

              <ButtonDropdownItem
                v-if="pageData"
                icon="document-csv"
                :disabled="!unit || !period"
                :to="
                  Routing.generate('u2_itemunitvalue_exportforunit', {
                    periodId: period?.id,
                    unitId: unit?.id,
                  })
                "
                target="_blank"
              >
                {{ Translator.trans('u2.export.item_values.csv') }}
              </ButtonDropdownItem>
            </template>
          </ButtonDropdownEllipsis>
        </template>
        <template #more>
          <UnitViewToolbar :layout-collection-id="sheetCollection.id" has-field-inspector-control />
          <DatasheetFieldInspector
            :context="{
              unitId,
              periodId,
            }"
          >
            <template
              #dropdown-extra="{ item: itemFromDropdownSlot, context: contextFromDropdownSlot }"
            >
              <ButtonDropdownItem
                icon="view"
                :disabled="
                  !isNavigationContextValid(contextFromDropdownSlot) ||
                  !fieldInspectorStore.fieldByItemId.get(itemFromDropdownSlot['id']) ||
                  fieldInspectorStore.fieldByItemId.get(itemFromDropdownSlot['id'])?.id ===
                    fieldInspectorStore.field?.id
                "
                :tooltip="
                  !isNavigationContextValid(contextFromDropdownSlot)
                    ? Translator.trans('u2.inspect.insufficient_context')
                    : fieldInspectorStore.buildFieldInspectButtonTooltip(itemFromDropdownSlot)
                "
                :text="Translator.trans('u2.inspect')"
                :to="{
                  name: 'DatasheetCollectionSheetView',
                  params: {
                    id: datasheetCollectionId,
                    sheetId: datasheetId,
                  },
                  query: {
                    period: contextFromDropdownSlot.periodId,
                    unit:
                      'unitId' in contextFromDropdownSlot
                        ? contextFromDropdownSlot.unitId
                        : undefined,
                    field: fieldInspectorStore.fieldByItemId.get(itemFromDropdownSlot['id'])?.id,
                  },
                }"
              />
            </template>
          </DatasheetFieldInspector>
        </template>
      </PageHeader>
    </template>
    <template v-if="pageDataError">
      <InfoBox
        v-if="
          isAxiosError(pageDataError) && pageDataError.response?.status === StatusCodes.FORBIDDEN
        "
        icon="blocked"
        :title="Translator.trans('u2.access_denied')"
      />
      <InfoBox v-else icon="alert" :title="Translator.trans('u2.error')" />
    </template>
    <template v-else-if="!sheet && !isLoading">
      <AppError code="404" :title="Translator.trans('u2.page_not_found')" icon="search">
        <p>{{ Translator.trans('u2.http_404') }}</p>
      </AppError>
    </template>
    <template v-else-if="!isParametersValid">
      <InfoBox icon="alert" :title="Translator.trans('u2.datasheet.missing_configuration')" />
    </template>
    <AppLoader v-else-if="areItemUnitValuesInitialLoading" />
    <RenderedUnitViewTemplate
      v-else-if="
        datasheetTemplateHtml && fields && itemUnitValues !== undefined && moneyScale !== undefined
      "
      :errors="errors"
      :template="datasheetTemplateHtml"
      :fields="fields"
      :values="itemUnitValues"
      :money-scale="moneyScale"
      :is-form-disabled="isFormDisabled"
    />
    <ConfirmationDialog
      v-if="isConfirmRecalculateRevealed && pageData"
      :title="Translator.trans('u2.are_you_sure')"
      :accept-text="Translator.trans('u2.confirm')"
      @confirm="confirmRecalculate"
      @close="cancelRecalculate"
    >
      <p>
        {{
          Translator.trans('u2.recalculate_unit_period.confirmation.text', {
            unit: unit?.name,
            period: period?.name,
          })
        }}
      </p>
      <AppMessage type="warning">
        {{ Translator.trans('u2.warning_unsaved_changes_will_be_lost') }}
      </AppMessage>
    </ConfirmationDialog>
  </AppPageWide>
</template>
