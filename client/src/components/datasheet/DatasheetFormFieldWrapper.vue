<script lang="ts">
import type { Field } from '@js/model/datasheet'

export interface DatasheetFormFieldWrapperProps {
  disabled: boolean
  errors?: Array<string>
  field: Field
}
</script>

<script lang="ts" setup>
import { computed, onMounted, ref, shallowRef } from 'vue'
import { useElementVisibility } from '@vueuse/core'
import { useRoute } from 'vue-router'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import Translator from '@js/translator'
import ContextMenuItem from '@js/components/ContextMenuItem.vue'
import ContextMenu from '@js/components/ContextMenu.vue'
import DropdownMenuDivider from '@js/components/buttons/DropdownMenuDivider.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import { generateUniqueColorForString, getContrastTextColor } from '@js/utilities/color'
import DatasheetFieldInspectorFeatureToggles from '@js/components/datasheet/DatasheetFieldInspectorFeatureToggles.vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import { getIdFromIri } from '@js/utilities/api-resource'

const layoutParametersStore = useDatasheetParametersStore()

const { errors = [], disabled, field } = defineProps<DatasheetFormFieldWrapperProps>()

const item = computed(() => field.item)

const route = useRoute()
const fieldInspectorStore = useFieldInspectorStore()
const element = ref<HTMLElement>()
const targetIsVisible = useElementVisibility(element)

const showColors = computed(() => fieldInspectorStore.showColours)
const isFieldSelected = computed(() => layoutParametersStore.parameters.field === field.id)
const fieldInFormula = computed(() => {
  const formulaElement = fieldInspectorStore.formulaElementByFieldId.get(field.id)
  if (!formulaElement) {
    return false
  }

  return !formulaElement.isPreviousPeriod
})

onMounted(() => {
  if (!isFieldSelected.value || targetIsVisible.value) {
    return
  }

  const elementValue = element.value
  if (!elementValue) {
    return
  }

  requestAnimationFrame(() => {
    elementValue.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    })
  })
})

function copyValue(hide: () => void) {
  const input = element.value?.querySelector('input')
  if (input) {
    navigator.clipboard.writeText(input.value)
  }
  hide()
}

function pasteValue(hide: () => void) {
  const input = element.value?.querySelector('input')
  if (input) {
    navigator.clipboard.readText().then((text) => {
      input.value = text
      input.dispatchEvent(new Event('input', { bubbles: true }))
    })
  }
  hide()
}

const isCurrentPageGroupView = computed(() => {
  return route.name === 'DatasheetCollectionSheetView' && 'unitHierarchy' in route.query
})

const breakdownLink = computed(() => {
  if (!isCurrentPageGroupView.value) {
    return undefined
  }

  return {
    name: 'DatasheetCollectionGroupViewBreakdown',
    query: {
      item: getIdFromIri(item.value),
      layoutCollection: layoutParametersStore.parameters.layoutCollection,
      layout: layoutParametersStore.parameters.layout,
      period: layoutParametersStore.parameters.period,
      unitHierarchy: layoutParametersStore.parameters.unitHierarchy,
      field: field.id,
    },
  }
})

const borderColored = computed(() => showColors.value && fieldInFormula.value)
const customColor = shallowRef(item.value ? generateUniqueColorForString(item.value) : undefined)
const contrastTextColor = computed(() =>
  customColor.value ? getContrastTextColor(customColor.value) : undefined
)
const hasErrors = computed(() => (errors ? errors.length > 0 : false))

const isElementHovered = ref(false)

const itemId = computed(() => (item.value ? getIdFromIri(item.value) : undefined))

function onMouseOver() {
  isElementHovered.value = true
  fieldInspectorStore.hoveredItemId = itemId.value
}

function onMouseLeave() {
  isElementHovered.value = false
  fieldInspectorStore.hoveredItemId = undefined
}

const backgroundColored = computed(() => {
  return (
    showColors.value &&
    fieldInFormula.value &&
    itemId.value &&
    itemId.value !== fieldInspectorStore.fieldItem?.id &&
    fieldInspectorStore.hoveredItemId === itemId.value &&
    !isElementHovered.value &&
    customColor.value
  )
})
const datasheetParameterStore = useDatasheetParametersStore()
</script>

<template>
  <ContextMenu as-child class="wrapper size-full">
    <div
      ref="element"
      class="form-widget-item-value"
      :class="[
        {
          'ring-1 ring-skin-base': isFieldSelected && !hasErrors,
          'has-errors ring-1 ring-bad': hasErrors,
          'colored-border': borderColored,
          'colored-background': backgroundColored,
        },
      ]"
      :tabindex="disabled ? 0 : undefined"
      @mouseover="onMouseOver"
      @mouseleave="onMouseLeave"
      @focusin="datasheetParameterStore.parameters.field = field.id"
      @click="datasheetParameterStore.parameters.field = field.id"
    >
      <VMenu
        :triggers="[]"
        :shown="isFieldSelected"
        :auto-hide="false"
        :disabled="!hasErrors"
        class="flex size-full items-center justify-end"
      >
        <slot />

        <template #popper>
          <FormErrors class="p-2" :errors="errors" />
        </template>
      </VMenu>
    </div>

    <template #content="{ hide }">
      <ContextMenuItem
        v-if="breakdownLink"
        icon="breakdown"
        :text="Translator.trans('u2.breakdown')"
        :to="breakdownLink"
      />

      <DatasheetFieldInspectorFeatureToggles />

      <DropdownMenuDivider />

      <ContextMenuItem icon="copy" @click="copyValue(hide)">
        {{ Translator.trans('u2.copy') }}
      </ContextMenuItem>

      <ContextMenuItem
        icon="inbox"
        :disabled="disabled"
        :tooltip="disabled ? Translator.trans('u2.paste.field_is_disabled') : undefined"
        @click="pasteValue(hide)"
      >
        {{ Translator.trans('u2.paste') }}
      </ContextMenuItem>
    </template>
  </ContextMenu>
</template>

<style scoped>
.form-widget-item-value {
  align-items: center;
  display: flex;
  justify-content: flex-end;

  &.colored-border {
    box-shadow: inset 0 0 0 2px v-bind('customColor');

    :deep(.bg-skin-disabled),
    :deep(*) {
      background-color: transparent;
      color: inherit;
    }
  }

  &.colored-background {
    background-color: v-bind('customColor');
    color: v-bind('contrastTextColor');
    opacity: 0.8;
  }

  &.has-errors::after {
    border-color: transparent theme('colors.bad') transparent transparent;
    border-style: solid;
    border-width: 0 10px 10px 0;
    content: '';
    height: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 0;
  }
}

.wrapper :deep(*) {
  outline: none;

  input[type='text'],
  textarea {
    border: none;
    border-radius: 0;
  }
}
</style>
