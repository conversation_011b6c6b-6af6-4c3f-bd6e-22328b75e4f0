<script setup lang="ts">
import { computed, ref } from 'vue'
import DatasheetToolbar from '@js/components/datasheet/DatasheetToolbar.vue'
import UnitViewConfigDialog from '@js/components/datasheet/UnitViewConfigDialog.vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import UnitLabel from '@js/components/unit/UnitLabel.vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import usePeriodQuery from '@js/composable/usePeriodQuery'
import useUnitQuery from '@js/composable/useUnitQuery'
import useCurrencyQuery from '@js/composable/useCurrencyQuery'
import { getIdFromIri } from '@js/utilities/api-resource'
import PeriodLabel from '@js/components/PeriodLabel.vue'
import DatasheetFieldInspectorControl from '@js/components/datasheet/DatasheetFieldInspectorControl.vue'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import AppSkeleton from '@js/components/AppSkeleton.vue'

defineProps<{
  layoutCollectionId: DatasheetCollection['id']
  hasFieldInspectorControl?: boolean
}>()

const layoutParamStore = useDatasheetParametersStore()

const { data: unit, isLoading: isUnitLoading } = useUnitQuery(
  () => layoutParamStore.parameters.unit
)

const currencyId = computed(() => {
  const iri = unit.value?.currency

  return iri ? getIdFromIri(iri) : undefined
})

const { data: currency, isLoading: isCurrencyLoading } = useCurrencyQuery(currencyId)

const { data: period, isLoading: isPeriodLoading } = usePeriodQuery(
  () => layoutParamStore.parameters.period
)

const showUnitViewParametersDialog = ref(
  !layoutParamStore.parameters.period || !layoutParamStore.parameters.unit
)
</script>

<template>
  <DatasheetToolbar>
    <template #default>
      <div class="flex items-center space-x-2 whitespace-nowrap">
        <span class="font-bold"> {{ Translator.trans('u2_core.period') }}: </span>
        <PeriodLabel v-if="period" :period="period" class="min-w-24" />
        <AppSkeleton v-else-if="isPeriodLoading" />
        <span v-else class="italic">
          {{ Translator.trans('u2_core.none_selected') }}
        </span>
      </div>

      <div class="flex items-center space-x-2 whitespace-nowrap">
        <span class="font-bold"> {{ Translator.trans('u2.unit') }}: </span>
        <UnitLabel v-if="unit" color="white" :unit="unit" />
        <AppSkeleton v-else-if="isUnitLoading" class="min-w-24" />
        <span v-else class="italic">
          {{ Translator.trans('u2_core.none_selected') }}
        </span>
      </div>

      <div class="flex items-center space-x-2 whitespace-nowrap">
        <span class="font-bold"> {{ Translator.trans('u2_core.currency') }}: </span>
        <span v-if="currency" v-tooltip="currency.name">
          {{ currency.iso4217code }}
        </span>
        <AppSkeleton v-else-if="isCurrencyLoading" class="min-w-10" />
        <template v-else>
          {{ Translator.trans('u2.n_a') }}
        </template>
      </div>

      <div class="flex">
        <ButtonBasic
          class="print:hidden"
          icon="config"
          :tooltip="Translator.trans('u2.change_parameters')"
          @click="showUnitViewParametersDialog = true"
        />

        <ButtonBasic
          class="print:hidden"
          :to="
            buildDatasheetRoute({
              layoutCollectionId: layoutCollectionId,
              hierarchyId: layoutParamStore.parameters.unitHierarchy ?? '',
              layoutId: layoutParamStore.parameters.layout,
            })
          "
        >
          {{ Translator.trans('u2.switch_to_group_view') }}
        </ButtonBasic>
      </div>

      <UnitViewConfigDialog
        v-if="showUnitViewParametersDialog"
        @close="showUnitViewParametersDialog = false"
      />
    </template>

    <template #right>
      <DatasheetFieldInspectorControl v-if="hasFieldInspectorControl" />
    </template>
  </DatasheetToolbar>
</template>
