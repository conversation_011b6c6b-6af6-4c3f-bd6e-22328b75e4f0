<script setup lang="ts">
import { computed } from 'vue'
import Translator from '@js/translator'
import DatasheetCollectionPopupCard from '@js/components/datasheet/DatasheetCollectionPopupCard.vue'
import useLayoutCollectionQuery from '@js/composable/useLayoutCollectionQuery'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { DataSheetNavigationContext } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    layoutCollection?: DatasheetCollection | DatasheetCollection['id'] | null
    context?: DataSheetNavigationContext
  }>(),
  {
    layoutCollection: undefined,
    context: () => ({
      unitId: undefined,
      periodId: undefined,
    }),
  }
)

const resolvedLayoutCollectionId = computed(() => {
  return !props.layoutCollection
    ? undefined
    : typeof props.layoutCollection === 'string'
      ? props.layoutCollection
      : props.layoutCollection.id
})

const initialData = computed(() => {
  return props.layoutCollection && typeof props.layoutCollection === 'object'
    ? props.layoutCollection
    : undefined
})

const {
  data: layoutCollectionFromQuery,
  isLoading,
  isError,
} = useLayoutCollectionQuery(resolvedLayoutCollectionId, {
  initialData: initialData.value,
})
</script>

<template>
  <LabelWithMenu :disabled="isLoading || isError">
    <template #default>
      <span
        v-if="isLoading"
        class="animate-pulse text-gray-500 lowercase italic"
        v-text="Translator.trans('u2.loading')"
      />
      <span v-else-if="layoutCollectionFromQuery" v-text="layoutCollectionFromQuery.name" />
      <span v-else-if="isError" v-text="Translator.trans('u2.unknown')" />
    </template>

    <template #content>
      <DatasheetCollectionPopupCard
        v-if="layoutCollectionFromQuery"
        :layout-collection="layoutCollectionFromQuery"
        :context="context"
      />
    </template>
  </LabelWithMenu>
</template>
