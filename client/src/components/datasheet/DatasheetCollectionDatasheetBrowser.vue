<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import Translator from '@js/translator'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import type { DatasheetRouteParameters } from '@js/router/datasheetCollections'
import type { RouteLocationRaw } from 'vue-router'
import type { Datasheet } from '@js/model/datasheet'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { Unit } from '@js/model/unit'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'

const props = defineProps<{
  layoutCollection: DatasheetCollection
  layouts: Array<Datasheet>
  unitId?: Unit['id']
  unitHierarchyId?: UnitHierarchy['id']
}>()

const {
  layoutCollection: currentLayoutCollection,
  layouts,
  unitId,
  unitHierarchyId,
} = toRefs(props)

const layoutParamStore = useDatasheetParametersStore()

type DropdownItems = Record<
  string,
  {
    label: string
    options: Array<{
      group: string
      name: string
      current: boolean
      to: RouteLocationRaw
    }>
  }
>

const flatOptions = computed(() => {
  const groupedOptions = layouts.value.reduce((grouped, layout) => {
    const groupName = layout.group

    if (!grouped[groupName]) {
      grouped[groupName] = { label: groupName, options: [] }
    }

    const parameters: DatasheetRouteParameters = {
      layoutCollectionId: currentLayoutCollection.value.id,
      layoutId: layout.id,
    }

    if (unitId.value || !unitHierarchyId.value) {
      parameters.unitId = unitId.value
    }

    if (!unitId.value && unitHierarchyId.value) {
      parameters.hierarchyId = unitHierarchyId.value
    }

    grouped[groupName].options.push({
      group: layout.group,
      name: layout.name,
      current: layout.id === Number(layoutParamStore.parameters.layout),
      to: buildDatasheetRoute(parameters),
    })

    return grouped
  }, {} as DropdownItems)

  return Object.values(groupedOptions)
    .map((option) => {
      return [
        { label: option.label, level: 1 },
        ...option.options.map((option) => ({ ...option, level: 1 })),
      ]
    })
    .flat()
})

const routeQueryParameter = computed(() => ({
  layoutCollectionId: currentLayoutCollection.value.id,
  unitId: unitId.value,
  hierarchyId: unitHierarchyId.value,
  periodId: layoutParamStore.parameters.period,
}))

const previousLayout = computed(() => {
  const currentLayoutIndex = layouts.value.findIndex((layout) => {
    const current = flatOptions.value.find((entry) => 'current' in entry && entry.current)
    return current && 'name' in current && layout.name === current?.name
  })
  return currentLayoutIndex === -1 ? undefined : layouts.value[currentLayoutIndex - 1]
})
const previousLayoutRoute = computed(() =>
  previousLayout.value
    ? buildDatasheetRoute({
        ...routeQueryParameter.value,
        layoutId: previousLayout.value?.id,
      })
    : undefined
)
const previousLayoutTooltip = computed(() =>
  previousLayout.value
    ? Translator.trans('u2.datasheets.previous') + ': ' + previousLayout.value.name
    : undefined
)

const nextLayout = computed(() => {
  const currentLayoutIndex = layouts.value.findIndex((layout) => {
    const current = flatOptions.value.find((entry) => 'current' in entry && entry.current)
    return current && 'name' in current && layout.name === current?.name
  })
  return currentLayoutIndex === -1 ? undefined : layouts.value[currentLayoutIndex + 1]
})
const nextLayoutRoute = computed(() =>
  nextLayout.value
    ? buildDatasheetRoute({
        ...routeQueryParameter.value,
        layoutId: nextLayout.value?.id,
      })
    : undefined
)
const nextLayoutTooltip = computed(() =>
  nextLayout.value
    ? Translator.trans('u2.datasheets.next') + ': ' + nextLayout.value.name
    : undefined
)
</script>

<template>
  <div class="flex">
    <ButtonBasic
      icon="chevron-left"
      grouped
      :disabled="!previousLayoutRoute"
      :to="previousLayoutRoute"
      :tooltip="previousLayoutTooltip"
    />

    <ButtonDropdown
      :tooltip="Translator.trans('u2.datasheets.select_a_datasheet')"
      grouped
      :arrow="false"
    >
      {{ Translator.trans('u2.datasheets.plural') }}
      <template #body>
        <template v-for="option in flatOptions">
          <dt v-if="'label' in option" :key="option.label" class="px-4 py-2">
            <span class="font-bold text-gray-700">{{ option.label }}</span>
          </dt>
          <ButtonDropdownItem
            v-else
            :key="option.name"
            :class="[
              option.level === 1 ? 'pr-4 pl-6' : 'px-4',
              { 'text-action-darker font-bold': option.current },
            ]"
            :disabled="option.current"
            :text="option.name"
            :to="option.to"
          />
        </template>
      </template>
    </ButtonDropdown>

    <ButtonBasic
      grouped
      icon="chevron-right"
      :disabled="!nextLayoutRoute"
      :to="nextLayoutRoute"
      :tooltip="nextLayoutTooltip"
    />
  </div>
</template>
