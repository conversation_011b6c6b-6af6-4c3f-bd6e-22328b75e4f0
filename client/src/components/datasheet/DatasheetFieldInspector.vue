<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { refDebounced } from '@vueuse/core'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import DatasheetItemFormula from '@js/components/datasheet/DatasheetItemFormula.vue'
import DatasheetItemFormulaElement from '@js/components/datasheet/DatasheetItemFormulaElement.vue'
import Translator from '@js/translator'
import type { DataSheetNavigationContext, LayoutItem } from '@js/model/datasheet'
import LabelBasic from '@js/components/LabelBasic.vue'

const fieldInspectorStore = useFieldInspectorStore()
const fieldFromStore = computed(() => fieldInspectorStore.field)
const field = refDebounced(fieldFromStore, 400)
const fieldItem = computed(() => fieldInspectorStore.fieldItem)

const props = defineProps<{
  context: DataSheetNavigationContext
}>()

const { context } = toRefs(props)

defineSlots<{
  'dropdown-extra'?: (props: { item: LayoutItem; context: DataSheetNavigationContext }) => unknown
}>()
</script>

<template>
  <div
    v-if="fieldInspectorStore.isEnabled"
    class="h-24 overflow-auto border-b border-gray-200 bg-linear-to-b from-gray-100 to-5% py-2 hover:resize-y"
  >
    <div v-if="!field" class="flex h-full w-full items-center justify-center">
      <h4>{{ Translator.trans('u2.field_inspector.select_a_field') }}</h4>
    </div>

    <div v-else>
      <p class="flex gap-1">
        <template v-if="fieldItem">
          <DatasheetItemFormulaElement
            :key="fieldItem?.id"
            class="self-start"
            :context="context"
            :formula-element="'{' + fieldItem?.id + '}'"
            :colorize="false"
            :show-value="fieldInspectorStore.showValues"
          >
            <template
              #element-dropdown-extra="{
                item: itemFromDropdownSlot,
                context: contextFromDropdownSlot,
              }"
            >
              <slot
                name="dropdown-extra"
                :item="itemFromDropdownSlot"
                :context="contextFromDropdownSlot"
              />
            </template>
          </DatasheetItemFormulaElement>
          =
          <DatasheetItemFormula
            v-if="fieldItem?.formula"
            :key="fieldItem?.formula"
            :item="fieldItem"
            :context="context"
            :colorize="fieldInspectorStore.showColours"
            :show-values="fieldInspectorStore.showValues"
          >
            <template
              #element-dropdown-extra="{
                item: itemFromDropdownSlot,
                context: contextFromDropdownSlot,
              }"
            >
              <slot
                name="dropdown-extra"
                :item="itemFromDropdownSlot"
                :context="contextFromDropdownSlot"
              />
            </template>
          </DatasheetItemFormula>
          <span v-else>
            {{ Translator.trans('u2.datasheets.item_has_no_formula') }}
          </span>
        </template>
        <LabelBasic v-else class="rounded-r-none" color="white">
          <span
            class="animate-pulse text-gray-500 lowercase italic"
            v-text="Translator.trans('u2.loading')"
          />
        </LabelBasic>
      </p>

      <p v-if="field.helpText" :key="field.helpText" class="text-gray-600">
        {{ field.helpText }}
      </p>
    </div>
  </div>
</template>
