<script setup lang="ts">
import { computed } from 'vue'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import AppSelect from '@js/components/form/AppSelect.vue'
import BasePopover from '@js/components/BasePopover.vue'
import DatasheetFieldInspectorFeatureToggles from '@js/components/datasheet/DatasheetFieldInspectorFeatureToggles.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'

const fieldInspectorStore = useFieldInspectorStore()

const isEnabled = computed(() => fieldInspectorStore.isEnabled)

const fieldOptions = computed(() => {
  return fieldInspectorStore.fields.map((field) => ({
    id: field.id,
    name: field.name,
  }))
})
const layoutParameterStore = useDatasheetParametersStore()
</script>

<template>
  <div class="flex gap-1">
    <AppSelect
      v-if="fieldInspectorStore.showSelect"
      v-model="layoutParameterStore.parameters.field"
      class="w-64 rounded-e-none text-sm"
      :options="fieldOptions"
      :placeholder="Translator.trans('u2.select_a_field')"
    />
    <BasePopover>
      <template #default>
        <ButtonBasic
          class="border-skin-base shadow-skin-base py-2.5 print:hidden"
          :icon="isEnabled ? 'hide' : 'view'"
          button-style="outlined"
          :tooltip="Translator.trans('u2.field_inspector.configure_field_inspector')"
        />
      </template>

      <template #content>
        <DatasheetFieldInspectorFeatureToggles />
      </template>
    </BasePopover>
  </div>
</template>
