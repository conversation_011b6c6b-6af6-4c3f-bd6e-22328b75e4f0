<script setup lang="ts">
import { computed, ref, toRefs } from 'vue'
import LabelBasic from '@js/components/LabelBasic.vue'
import DatasheetItemLabel from '@js/components/datasheet/DatasheetItemLabel.vue'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import { generateUniqueColorForString, getContrastTextColor } from '@js/utilities/color'
import useItemQuery from '@js/composable/useItemQuery'
import Translator from '@js/translator'
import usePeriodQuery from '@js/composable/usePeriodQuery'
import { getIdFromIri } from '@js/utilities/api-resource'
import useItemFormulaElementValue from '@js/composable/useItemFormulaElementValue'
import type { DataSheetNavigationContext, LayoutItem } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    formulaElement: string
    context?: DataSheetNavigationContext
    colorize?: boolean
    showValue?: boolean
  }>(),
  {
    context: () => ({
      unitId: undefined,
      periodId: undefined,
    }),
    colorize: false,
    showValue: false,
  }
)
const { formulaElement, context, colorize, showValue } = toRefs(props)

const itemString = computed(() => formulaElement.value.replace('{', '').replace('}', ''))
const itemId = computed(() => Number(itemString.value.replace('P', '')))
const isPreviousPeriodItem = computed(() => itemString.value.startsWith('P'))

defineSlots<{
  default?: (props: { item: LayoutItem }) => unknown
  'element-dropdown-extra'?: (props: {
    item: LayoutItem
    context: DataSheetNavigationContext
  }) => unknown
}>()

const { data: currentPeriod } = usePeriodQuery(() => props.context.periodId)
const previousPeriodId = computed(() =>
  currentPeriod.value?.previousPeriod
    ? getIdFromIri(currentPeriod.value?.previousPeriod)
    : undefined
)

const fieldInspectorStore = useFieldInspectorStore()

const { data: itemFromQuery } = useItemQuery(itemId)

const isFieldOnDataSheet = computed(
  () => !isPreviousPeriodItem.value && fieldInspectorStore.fieldByItemId.get(itemId.value)
)
const colorizeField = computed(
  () =>
    colorize.value &&
    fieldInspectorStore.showColours &&
    itemFromQuery.value &&
    isFieldOnDataSheet.value
)

const isFieldInTableHovered = computed(
  () => fieldInspectorStore.hoveredItemId === itemId.value && !isElementHovered.value
)
const customColor = computed(() =>
  colorizeField.value && itemFromQuery.value
    ? generateUniqueColorForString(itemFromQuery.value['@id'])
    : undefined
)
const isElementHovered = ref(false)

const showValues = computed(() => showValue.value && fieldInspectorStore.showValues)

const { value, isLoading } = useItemFormulaElementValue(
  itemId,
  {
    ...context.value,
    periodId: isPreviousPeriodItem.value ? previousPeriodId.value : context.value.periodId,
  },
  () => fieldInspectorStore.showValues
)
const contrastTextColor = computed(() =>
  customColor.value ? getContrastTextColor(customColor.value) : undefined
)

const borderColored = computed(() => colorizeField.value && customColor.value)
const backgroundColored = computed(() => isFieldInTableHovered.value && customColor.value)

function onMouseOver() {
  fieldInspectorStore.hoveredItemId = itemId.value
  isElementHovered.value = true
}

function onMouseLeave() {
  fieldInspectorStore.hoveredItemId = undefined
  isElementHovered.value = false
}
</script>

<template>
  <span
    class="datasheet-item-formula-element inline-flex whitespace-nowrap"
    @mouseover="onMouseOver"
    @mouseleave="onMouseLeave"
  >
    <LabelBasic
      v-if="isPreviousPeriodItem"
      v-tooltip="Translator.trans('u2.item.formula.element.previous_period_value')"
      class="rounded-r-none"
      text="P"
      color="action"
    />
    <DatasheetItemLabel
      :item="itemId"
      :context="{
        ...context,
        periodId: isPreviousPeriodItem ? previousPeriodId : context.periodId,
      }"
      :class="[
        {
          'rounded-l-none': isPreviousPeriodItem,
          'colored-background': backgroundColored,
          'colored-border': borderColored,
          'border-current': fieldInspectorStore.showColours && isPreviousPeriodItem,
        },
      ]"
    >
      <template v-if="showValues" #default>
        <span
          v-if="isLoading"
          class="animate-pulse text-gray-500 lowercase italic"
          v-text="Translator.trans('u2.loading')"
        />
        <span v-else>
          {{ value ?? Translator.trans('u2.n_a') }}
        </span>
      </template>

      <template
        #popupcard-dropdown-extra="{ item: itemFromDropdownSlot, context: contextFromDropdownSlot }"
      >
        <slot
          name="element-dropdown-extra"
          :item="itemFromDropdownSlot"
          :context="contextFromDropdownSlot"
        />
      </template>
    </DatasheetItemLabel>
  </span>
</template>

<style scoped>
.datasheet-item-formula-element {
  :deep(.colored-border) {
    box-shadow: inset 0 0 0 2px v-bind('customColor');
  }

  :deep(.colored-background) {
    background-color: v-bind('customColor');
    color: v-bind('contrastTextColor');
    opacity: 0.8;
  }

  span > span + div :deep(span) {
    border-bottom-left-radius: 0;
    border-left-width: 0;
    border-top-left-radius: 0;
  }
}
</style>
