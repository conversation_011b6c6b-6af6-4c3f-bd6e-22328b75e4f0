<script setup lang="ts">
import { computed } from 'vue'
import Translator from '@js/translator'
import useLayoutQuery from '@js/composable/useLayoutQuery'
import DatasheetPopupCard from '@js/components/datasheet/DatasheetPopupCard.vue'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import type { DataSheetNavigationContext, Datasheet } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    layout: Datasheet | Datasheet['id'] | null
    context?: DataSheetNavigationContext
  }>(),
  {
    context: () => ({
      type: 'unit',
      unitId: undefined,
      periodId: undefined,
    }),
  }
)

const resolvedLayoutId = computed(() => {
  return !props.layout
    ? undefined
    : typeof props.layout === 'number'
      ? props.layout
      : props.layout.id
})

const initialData = computed(() =>
  props.layout && typeof props.layout === 'object' ? props.layout : undefined
)

const {
  data: layoutFromQuery,
  isLoading,
  isError,
} = useLayoutQuery(resolvedLayoutId, {
  initialData: initialData.value,
})
</script>

<template>
  <LabelWithMenu :disabled="isLoading || isError">
    <template #default>
      <span
        v-if="isLoading"
        class="animate-pulse text-gray-500 lowercase italic"
        v-text="Translator.trans('u2.loading')"
      />
      <span v-else-if="layoutFromQuery">
        {{ layoutFromQuery.name }}
      </span>
      <span v-else-if="isError">
        {{ Translator.trans('u2.unknown') }}
      </span>
    </template>

    <template #content>
      <DatasheetPopupCard v-if="layoutFromQuery" :context="context" :layout="layoutFromQuery" />
    </template>
  </LabelWithMenu>
</template>
