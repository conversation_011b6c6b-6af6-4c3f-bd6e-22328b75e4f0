<script setup lang="ts">
import { computed, ref } from 'vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import DatasheetToolbar from '@js/components/datasheet/DatasheetToolbar.vue'
import GroupViewConfigDialog from '@js/components/datasheet/GroupViewConfigDialog.vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import useUnitHierarchyQuery from '@js/composable/useUnitHierarchyQuery'
import useCurrencyQuery from '@js/composable/useCurrencyQuery'
import { getIdFromIri } from '@js/utilities/api-resource'
import PeriodLabel from '@js/components/PeriodLabel.vue'
import UnitHierarchyLabel from '@js/components/UnitHierarchyLabel.vue'
import DatasheetFieldInspectorControl from '@js/components/datasheet/DatasheetFieldInspectorControl.vue'
import { queries } from '@js/query'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import AppSkeleton from '@js/components/AppSkeleton.vue'

defineProps<{
  layoutCollectionId: DatasheetCollection['id']
  editable: boolean
  hasUnitViewSwitch?: boolean
  hasFieldInspectorControl?: boolean
}>()

const layoutParamStore = useDatasheetParametersStore()

const { data: systemSettingsAll, isLoading: isSystemSettingsLoading } = useSystemSettingsAllQuery()

const currencyId = computed(() => {
  const iri = systemSettingsAll.value?.applicationCurrency

  return iri ? getIdFromIri(iri) : undefined
})
const { data: currency, isLoading: isCurrencyLoading } = useCurrencyQuery(currencyId)

const { data: unitHierarchy, isLoading: isUnitHierarchyLoading } = useUnitHierarchyQuery(
  () => layoutParamStore.parameters.unitHierarchy
)

const periodId = computed(() => layoutParamStore.parameters.period)
const { data: period, isLoading: isPeriodLoading } = useQuery({
  ...queries.periods.single(periodId),
  /*
    TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
      See: https://github.com/lukemorales/query-key-factory/issues/100
   */
  queryFn: computed(() => (periodId.value ? queries.periods.single(periodId).queryFn : skipToken)),
})

const showGroupViewParametersDialog = ref(
  !layoutParamStore.parameters.period || !layoutParamStore.parameters.unitHierarchy
)
</script>

<template>
  <DatasheetToolbar>
    <template #default>
      <div class="flex items-center space-x-2 whitespace-nowrap">
        <span class="font-bold"> {{ Translator.trans('u2_core.period') }}: </span>
        <PeriodLabel v-if="period" :period="period" />
        <AppSkeleton v-else-if="isPeriodLoading" class="min-w-24" />
        <span v-else class="italic">
          {{ Translator.trans('u2_core.none_selected') }}
        </span>
      </div>

      <div class="flex items-center space-x-2 whitespace-nowrap">
        <span class="whitespace-nowrap font-bold"
          >{{ Translator.trans('u2.unit_hierarchy') }}:
        </span>
        <UnitHierarchyLabel
          v-if="unitHierarchy"
          :fallback="Translator.trans('u2_core.none_selected')"
          :unit-hierarchy="unitHierarchy"
          :date="period?.endDate ? new Date(period?.endDate) : undefined"
        />
        <AppSkeleton v-else-if="isUnitHierarchyLoading" class="min-w-24" />
        <span v-else class="italic">
          {{ Translator.trans('u2_core.none_selected') }}
        </span>
      </div>

      <div class="flex items-center space-x-2 whitespace-nowrap">
        <span class="font-bold"> {{ Translator.trans('u2_core.currency') }}: </span>
        <span v-if="currency" v-tooltip="currency.name">
          {{ currency.iso4217code }}
        </span>
        <AppSkeleton v-else-if="isSystemSettingsLoading || isCurrencyLoading" class="min-w-10" />
        <template v-else>
          {{ Translator.trans('u2.n_a') }}
        </template>
      </div>

      <div class="flex">
        <ButtonBasic
          v-if="editable"
          class="print:hidden"
          icon="config"
          :tooltip="Translator.trans('u2.change_parameters')"
          @click="showGroupViewParametersDialog = true"
        />

        <ButtonBasic
          v-if="hasUnitViewSwitch"
          class="print:hidden"
          :to="
            buildDatasheetRoute({
              layoutCollectionId: layoutCollectionId,
              layoutId: layoutParamStore.parameters.layout,
              unitId: layoutParamStore.parameters.unit ?? '',
            })
          "
        >
          {{ Translator.trans('u2.switch_to_unit_view') }}
        </ButtonBasic>
      </div>

      <GroupViewConfigDialog
        v-if="showGroupViewParametersDialog"
        @close="showGroupViewParametersDialog = false"
      />
    </template>
    <template #right>
      <DatasheetFieldInspectorControl v-if="hasFieldInspectorControl" />
    </template>
  </DatasheetToolbar>
</template>
