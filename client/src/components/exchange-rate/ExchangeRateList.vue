<script lang="ts" setup>
import type { Period } from '@js/api/periodApi'
import { computed, ref } from 'vue'
import { watchDebounced } from '@vueuse/core'
import { exchangeRateApi } from '@js/api/exchangeRateApi'
import { exchangeRateTypes } from '@js/model/exchangeRate'
import useTable from '@js/composable/useTable'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import { flattenObject } from '@js/utilities/flattenObject'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import useCurrenciesAllQuery from '@js/composable/useCurrenciesAllQuery'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { ApiQuery } from '@js/composable/useTable'
import type { ExchangeRate } from '@js/model/exchangeRate'
import useLocaleNumberFormatter from '@js/composable/useLocaleNumberFormatter'

const props = defineProps<{ period: Period }>()
const loading = ref(true)
const showDeleteDialog = ref(false)
const totalItems = ref(0)
const authStore = useAuthStore()

const period = computed(() => props.period)
const { resolveNotification } = useHandleAxiosErrorResponse()

const { apiQuery, columns, query, changePage, changePageSize, sort } = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.type'),
      id: 'exchangeRateType',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.input_currency'),
      id: 'inputCurrency.iso4217code',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.output_currency'),
      id: 'outputCurrency.iso4217code',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.direct_quotation'),
      id: 'directRate',
    },
    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.indirect_quotation'),
      id: 'indirectRate',
    },
    {
      align: 'right',
      name: '',
      id: 'actions',
    },
  ],
  { filter: { search: '', periodId: period.value.id } }
)
const allCurrenciesQuery = useCurrenciesAllQuery()
const exchangeRates = ref<Array<ExchangeRate>>([])
function fetchExchangeRates(query: ApiQuery) {
  loading.value = true

  exchangeRateApi
    .fetchExchangeRatesByQuery({
      ...query,
      periodId: period.value.id,
    })
    .then(({ data }) => {
      exchangeRates.value = data['hydra:member']
      totalItems.value = data['hydra:totalItems']
    })
    .catch(resolveNotification)
    .finally(() => {
      loading.value = false
    })
}

const exchangeRatesData = computed(() => {
  return exchangeRates.value.map((item) =>
    flattenObject({
      ...item,
      exchangeRateType:
        item.exchangeRateType === exchangeRateTypes.current
          ? Translator.trans('u2.money.exchange_rate.types.current')
          : Translator.trans('u2.money.exchange_rate.types.average'),
      inputCurrency: allCurrenciesQuery.items.value.find(
        (currency) => currency['@id'] === item.inputCurrency
      ),
      outputCurrency: allCurrenciesQuery.items.value.find(
        (currency) => currency['@id'] === item.outputCurrency
      ),
    })
  )
})

watchDebounced(
  query,
  (newValue) => {
    if (JSON.stringify(newValue) !== JSON.stringify(query.value)) {
      query.value = newValue
    }
    fetchExchangeRates(apiQuery.value)
  },
  { deep: true, debounce: 400 }
)

fetchExchangeRates(apiQuery.value)

type Item = (typeof exchangeRatesData.value)[number]
const getName = (exchangeRate?: Item) => {
  return exchangeRate
    ? `${exchangeRate['inputCurrency.iso4217code']} – ${exchangeRate['outputCurrency.iso4217code']}: ${exchangeRate.exchangeRateType}`
    : ''
}

const selectedExchangeRate = ref<Item>()
function confirmDelete(exchangeRate?: Item) {
  if (exchangeRate === undefined) {
    return
  }
  selectedExchangeRate.value = exchangeRate
  showDeleteDialog.value = true
}

function deleteExchangeRate(selectedExchangeRate?: Item) {
  if (selectedExchangeRate === undefined) {
    return
  }
  exchangeRateApi
    .deleteExchangeRate(selectedExchangeRate.id)
    .then(() => {
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      fetchExchangeRates(apiQuery.value)
    })
    .catch(resolveNotification)
    .finally(() => {
      showDeleteDialog.value = false
    })
}
const { format } = useLocaleNumberFormatter()
</script>

<template>
  <div>
    <HeaderWithAction>
      {{ Translator.trans('u2_core.exchange_rates') }}
      <template #button>
        <ButtonBasic
          icon="upload"
          :to="{ name: 'ImportStart', params: { configurationKeySlug: 'exchange-rate' } }"
          :tooltip="Translator.trans('u2_core.import_exchange_rates')"
        >
          {{ Translator.trans('u2.import.import') }}
        </ButtonBasic>

        <ButtonNew
          :disabled="period?.closed || !authStore.hasRole('ROLE_PERIOD_MANAGER')"
          :to="{ name: 'ExchangeRateNew', params: { id: period.id } }"
          :tooltip="Translator.trans('u2_core.add_new_exchange_rate')"
        />
      </template>
    </HeaderWithAction>

    <AppSearch v-model="query.filter.search" class="mt-4 w-96 max-w-full" />
    <AppLoader v-if="loading" class="h-24" />
    <AppTable
      v-else
      horizontal-scroll
      :headers="columns"
      :items="exchangeRatesData"
      :query="query"
      :total-items="totalItems"
      @sort="sort"
      @page-change="changePage"
      @page-size-change="changePageSize"
    >
      <template #item-directRate="{ item }">
        {{ format(+item.directRate, '0,0.0000000000') }}
      </template>
      <template #item-indirectRate="{ item }">
        {{ format(1 / +item.directRate, '0,0.0000000000') }}
      </template>
      <template #item-actions="{ item }">
        <ButtonEdit
          :to="{ name: 'ExchangeRateEdit', params: { exchangeRateId: item.id, id: period.id } }"
          :tooltip="
            Translator.trans('u2_core.edit_exchange_rate_with_given_name', {
              exchange_rate_name: getName(item),
            })
          "
        />

        <ButtonDelete
          :tooltip="
            Translator.trans('u2_core.delete_exchange_rate_with_given_name', {
              exchange_rate_name: getName(item),
            })
          "
          :disabled="!authStore.hasRole('ROLE_PERIOD_MANAGER') || period.closed"
          :show-text="false"
          @click="confirmDelete(item)"
        />
      </template>
    </AppTable>

    <ConfirmationDialog
      v-if="showDeleteDialog"
      @confirm="deleteExchangeRate(selectedExchangeRate)"
      @close="showDeleteDialog = false"
    >
      {{
        Translator.trans('u2_core.delete_exchange_rate_with_given_name.confirmation', {
          exchange_rate_name: getName(selectedExchangeRate),
        })
      }}
    </ConfirmationDialog>
  </div>
</template>
