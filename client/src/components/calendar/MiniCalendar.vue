<script setup lang="ts" generic="TModel extends string | [string, string | undefined]">
import {
  addDays,
  eachDayOfInterval,
  endOfMonth,
  endOfWeek,
  format,
  formatISO,
  getMonth,
  isAfter,
  isSameDay,
  isToday,
  isValid,
  isWithinInterval,
  startOfMonth,
  startOfWeek,
} from 'date-fns'
import { computed, ref } from 'vue'
import useDateFormat from '@js/composable/useDateFormat'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import { useAnimate } from '@js/composable/useAnimate'
import { vAnimate } from '@js/directives/animate'
import YearsScroll from '@js/components/YearsScroll.vue'

const { getLocale } = useDateFormat()

const modelValue = defineModel<TModel>()

const nextButton = ref()

const [parentElement, enableAnimation] = useAnimate()
const isRange = computed(() => Array.isArray(modelValue.value))

const startOfThisWeek = startOfWeek(new Date(), { weekStartsOn: 1 })

const weekDays = Array.from({ length: 7 }, (_, i) =>
  format(addDays(startOfThisWeek, i), 'eeeee', { locale: getLocale() })
)
const selectedDate = computed(() => {
  return endDate.value ?? startDate.value
})

const startDate = computed<Date | undefined>(() => {
  if (!modelValue.value) {
    return undefined
  }
  if (isRange.value) {
    const dateString = (modelValue.value as [string, string | undefined])[0]
    return new Date(dateString)
  }

  if (isValid(new Date(modelValue.value as string))) {
    return new Date(modelValue.value as string)
  }

  return undefined
})

const endDate = computed<Date | undefined>(() => {
  if (!(modelValue.value && isRange.value)) {
    return undefined
  }
  const dateString = (modelValue.value as [string, string | undefined])[1]
  return dateString ? new Date(dateString) : undefined
})

const currentDate = ref(selectedDate.value ? new Date(selectedDate.value) : new Date())

const currentMonth = computed(() => getMonth(currentDate.value))

const todaysMonth = computed(() => getMonth(new Date()))
interface Day {
  unformattedDate: Date
  date: string
  isCurrentMonth: boolean
  isToday: boolean
  isSelected: boolean
  isInRange: boolean
  isRangeStart: boolean
}

const days = computed(() => {
  const startOfWeekDate = startOfWeek(new Date(startOfMonth(new Date(currentDate.value))), {
    weekStartsOn: 1,
  })

  const endOfWeekDate = endOfWeek(new Date(endOfMonth(new Date(currentDate.value))), {
    weekStartsOn: 1,
  })

  return eachDayOfInterval({ start: startOfWeekDate, end: endOfWeekDate }).map((day): Day => {
    const hasStart = startDate.value !== undefined
    const isStart = hasStart ? isSameDay(new Date(startDate.value), day) : false
    const hasEnd = endDate.value !== undefined
    const isEnd = hasEnd ? isSameDay(new Date(endDate.value), day) : false
    const isInRange =
      isStart ||
      isEnd ||
      (hasStart && !hasEnd && isAfter(day, startDate.value)) ||
      (hasStart &&
        hasEnd &&
        isWithinInterval(day, {
          start: startDate.value,
          end: endDate.value,
        }))

    return {
      unformattedDate: day,
      date: format(day, 'd'),
      isCurrentMonth: currentMonth.value === getMonth(day),
      isToday: isToday(day),
      isSelected: isStart || isEnd,
      isInRange: isRange.value && isInRange,
      isRangeStart: isRange.value && isStart,
    }
  })
})

const currentMonthAndYear = computed(() =>
  format(new Date(currentDate.value), 'LLLL yyyy', { locale: getLocale() })
)

function getNext(date: Date) {
  enableAnimation(false)
  if (currentView.value === 'months') {
    return new Date(date.getFullYear() + 1, 0)
  }
  return new Date(date.getFullYear(), date.getMonth() + 1, 1)
}

function getPrevious(date: Date) {
  enableAnimation(false)
  if (currentView.value === 'months') {
    return new Date(date.getFullYear() - 1, 0)
  }
  return new Date(date.getFullYear(), date.getMonth() - 1, 1)
}

function selectDate(day: Day) {
  const isoDate = formatISO(day.unformattedDate, { representation: 'date' })
  if (!isRange.value) {
    modelValue.value = isoDate as TModel
    return
  }

  if (
    !endDate.value &&
    startDate.value &&
    (isSameDay(day.unformattedDate, startDate.value) ||
      isAfter(day.unformattedDate, startDate.value))
  ) {
    modelValue.value = [formatISO(startDate.value, { representation: 'date' }), isoDate] as TModel
    return
  }
  modelValue.value = [isoDate, undefined] as TModel
}

const months = computed(() =>
  [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((month) => ({
    number: month,
    name: format(new Date(new Date(currentDate.value.getFullYear(), month)), 'LLL', {
      locale: getLocale(),
    }),
  }))
)

const currentYear = computed(() => currentDate.value.getFullYear())
const goToMonth = (month: { name: string; number: number }) => {
  currentDate.value = new Date(currentYear.value, month.number)
  switchView('month')
}

function goToYear(year: number) {
  //
  currentDate.value = new Date(year, currentMonth.value)
  switchView('months')
}
function goToToday() {
  currentDate.value = new Date()
  switchView('month')
}

const yearsScroller = ref()

const currentView = ref<'month' | 'months' | 'years'>('month')
const switchView = (view: 'month' | 'months' | 'years') => {
  enableAnimation(true)
  currentView.value = view
  if (view === 'years') {
    yearsScroller.value?.setFocusToCurrentYear()
  }
}
</script>

<template>
  <div v-animate class="size-80 max-w-full flex-none p-4 md:block">
    <!-- Navigation -->
    <div
      ref="parentElement"
      class="inline-flex h-9 w-full items-center justify-between text-gray-900"
    >
      <div v-if="currentView === 'months' || currentView === 'years'" class="flex-1">
        <button
          :key="currentYear"
          type="button"
          data-testId="go-to-years"
          class="text-action text-left text-4xl font-semibold hover:text-blue-600"
          @click="switchView('years')"
        >
          {{ currentYear }}
        </button>
      </div>

      <div v-else class="flex-1">
        <button
          type="button"
          data-testId="go-to-months"
          class="text-action text-left text-xl font-semibold hover:text-blue-600"
          @click="switchView('months')"
        >
          {{ currentMonthAndYear }}
        </button>
      </div>

      <div v-if="currentView !== 'years'" class="inline-flex items-center gap-x-1">
        <button
          type="button"
          class="flex-auto cursor-pointer px-1.5 font-semibold text-gray-600 hover:text-gray-950"
          @click="goToToday"
          @keydown.enter="goToToday"
        >
          {{ Translator.trans('u2.date.today') }}
        </button>

        <!-- Chevron buttons-->
        <button
          type="button"
          class="-m-1.5 ml-2 flex flex-none items-center justify-center p-1.5 text-gray-500 hover:text-gray-950"
          @keydown.enter="currentDate = getPrevious(currentDate)"
        >
          <span class="sr-only">Previous</span>
          <SvgIcon
            data-testId="previous"
            icon="chevron-left"
            aria-hidden="true"
            @click="currentDate = getPrevious(currentDate)"
          />
        </button>
        <button
          ref="nextButton"
          type="button"
          class="-m-1.5 ml-2 flex flex-none items-center justify-center p-1.5 text-gray-500 hover:text-gray-950"
          @keydown.enter="currentDate = getNext(currentDate)"
        >
          <span class="sr-only">Next</span>
          <SvgIcon
            data-testId="next"
            icon="chevron-right"
            aria-hidden="true"
            @click="currentDate = getNext(currentDate)"
          />
        </button>
      </div>
    </div>

    <!-- Years -->
    <YearsScroll
      v-if="currentView === 'years'"
      ref="yearsScroller"
      :current-year="currentYear"
      @select-year="goToYear"
      @escape="nextButton.focus()"
    />

    <!-- Months -->
    <div v-else-if="currentView === 'months'" class="mt-6 grid h-48 w-full grid-cols-3 gap-3">
      <button
        v-for="month in months"
        :key="month.number"
        type="button"
        :data-testId="`month-${month.number}`"
        :class="[
          'inline-block cursor-pointer rounded-lg p-2 text-center text-lg font-medium',
          selectedDate &&
          month.number === getMonth(selectedDate) &&
          selectedDate.getFullYear() === currentDate.getFullYear()
            ? 'bg-action text-white hover:bg-blue-600'
            : 'hover:bg-blue-100',
          month.number === todaysMonth &&
            new Date().getFullYear() === currentDate.getFullYear() &&
            'border-action text-action border-2',
        ]"
        @click="goToMonth(month)"
      >
        {{ month.name }}
      </button>
    </div>

    <!-- Days -->
    <template v-else>
      <div class="mt-6 grid grid-cols-7 text-center text-sm leading-loose text-gray-500">
        <div v-for="day in weekDays" :key="day">{{ day }}</div>
      </div>
      <div class="isolate mt-2 grid grid-cols-7 gap-y-px text-sm">
        <button
          v-for="day in days"
          :key="day.unformattedDate.toString()"
          type="button"
          :class="[
            'focus:z-10',
            (day.isSelected || day.isToday) && 'font-semibold',
            day.isSelected && 'text-white',
            !day.isSelected && day.isCurrentMonth && !day.isToday && 'text-gray-900',
            !day.isSelected && !day.isCurrentMonth && !day.isToday && 'text-gray-400',
            day.isToday && !day.isSelected && 'text-action',
            day.isInRange ? 'bg-blue-100' : day.isCurrentMonth ? 'bg-white' : 'bg-gray-50',
            day.isInRange &&
              day.isSelected &&
              day.isRangeStart &&
              'rounded-tl-full rounded-bl-full bg-blue-100 bg-clip-content pl-6',
            day.isInRange &&
              day.isSelected &&
              !day.isRangeStart &&
              'rounded-tr-full rounded-br-full bg-blue-100 bg-clip-content pr-6',
          ]"
          @keydown.enter="selectDate(day)"
        >
          <time
            :datetime="day.unformattedDate.toISOString()"
            :class="[
              'inline-flex size-8 items-center justify-center rounded-full',
              !day.isSelected && 'hover:bg-gray-100',
              day.isSelected && 'bg-action hover:bg-blue-600',
              day.isInRange && !day.isSelected && 'hover:bg-blue-200',
              day.isSelected && day.isToday && 'text-white',
              !day.isSelected && day.isToday && 'border-action border-2',
              day.isInRange && day.isSelected && day.isRangeStart && '-ml-6',
              day.isInRange && day.isSelected && !day.isRangeStart && '-mr-6',
            ]"
            @click="selectDate(day)"
          >
            {{ day.date }}
          </time>
        </button>
      </div>
    </template>
  </div>
</template>
