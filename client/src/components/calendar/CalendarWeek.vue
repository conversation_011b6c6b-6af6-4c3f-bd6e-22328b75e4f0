<script setup lang="ts">
import { format, formatISO } from 'date-fns'
import CalendarEntry from '@js/components/calendar/CalendarEntry.vue'
import useDateFormat from '@js/composable/useDateFormat'
import type { CalendarEntry as CalendarEntryType } from '@js/model/calendar'

defineProps<{
  entries: Record<string, Array<CalendarEntryType>>
}>()

const { getLocale } = useDateFormat()
const formatDay = (day: string) => format(new Date(day), 'EEE d', { locale: getLocale() })
const today = formatISO(new Date(), { representation: 'date' })
const isToday = (day: string) => today === day
</script>
<template>
  <div
    id="calendar"
    class="calendar flex flex-col flex-wrap border-r border-b border-gray-200 sm:flex-row"
  >
    <div
      v-for="day in Object.keys(entries)"
      :key="day"
      class="border-t border-l sm:w-40 sm:flex-auto"
      :class="{ 'calendar-day-is-today bg-red-100': isToday(day) }"
    >
      <div
        class="p-1.5 text-center font-bold whitespace-nowrap"
        :class="[today === day ? 'bg-red-200' : 'bg-gray-100']"
      >
        {{ formatDay(day) }}
      </div>
      <div class="sm:min-h-[50px] sm:min-h-[200px]">
        <CalendarEntry v-for="entry in entries[day]" :key="entry.url" :calendar-entry="entry" />
      </div>
    </div>
  </div>
</template>
