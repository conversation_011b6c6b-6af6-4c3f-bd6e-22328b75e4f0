<script setup lang="ts">
import CalendarEntryPopup from '@js/components/calendar/CalendarEntryPopup.vue'
import type { CalendarEntry } from '@js/model/calendar'

defineProps<{
  calendarEntry: CalendarEntry
}>()
</script>

<template>
  <VMenu
    :delay="{
      show: 300,
    }"
  >
    <template #default>
      <router-link :to="calendarEntry.url" class="hover:no-underline">
        <div
          :class="[
            calendarEntry.calendar.canonicalName,
            calendarEntry.closed
              ? 'bg-gray-100 text-gray-400'
              : 'text-off-black bg-white hover:bg-orange-100',
          ]"
          class="calendar-entry m-px flex overflow-hidden rounded-xs border"
        >
          <span
            class="calendar-entry-color w-3 flex-none border-r"
            :class="calendarEntry.calendar.canonicalName"
          />
          <span class="calendar-entry-name flex-auto truncate px-2 py-1.5">
            {{ calendarEntry.name }}
          </span>
        </div>
      </router-link>
    </template>
    <template #popper>
      <CalendarEntryPopup :calendar-entry="calendarEntry" />
    </template>
  </VMenu>
</template>

<style scoped>
.calendar-entry {
  &.tpm-country-by-country-report {
    border-color: #df754c;
  }

  &.tpm-local-file {
    border-color: #cbdf4c;
  }

  &.tpm-master-file {
    border-color: #4edf4c;
  }

  &.tcm-other-deadline {
    border-color: #4cdfc8;
  }

  &.tcm-tax-assessment-monitor {
    border-color: #4c78df;
  }

  &.tcm-tax-authority-audit-objection {
    border-color: #9e4cdf;
  }

  &.tcm-tax-filing-monitor {
    border-color: #df4ca1;
  }
}

.calendar-entry-color {
  &.tpm-country-by-country-report {
    background-color: #e69678;
  }

  &.tpm-local-file {
    background-color: #d8e678;
  }

  &.tpm-master-file {
    background-color: #79e678;
  }

  &.tcm-other-deadline {
    background-color: #78e6d5;
  }

  &.tcm-tax-assessment-monitor {
    background-color: #7898e6;
  }

  &.tcm-tax-authority-audit-objection {
    background-color: #b678e6;
  }

  &.tcm-tax-filing-monitor {
    background-color: #e678b8;
  }
}
</style>
