<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { useClipboard, useConfirmDialog } from '@vueuse/core'
import set from 'lodash/set'
import { useAsyncComponent } from '@js/composable/useAsyncComponent'
import { api<PERSON>ey<PERSON>pi } from '@js/api/apiKeyApi'
import AppDate from '@js/components/AppDate.vue'
import AppInputText from '@js/components/form/AppInputText.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { ApiKey } from '@js/model/api-key'

const { defineAsyncComponentWithFallback } = useAsyncComponent()

const ApiKeyRegenerateDialog = defineAsyncComponentWithFallback(
  () => import('@js/components/ApiKeyRegenerateDialog.vue')
)
const ConfirmationDialog = defineAsyncComponentWithFallback(
  () => import('@js/components/ConfirmationDialog.vue')
)
const ApiKeyEditorDialog = defineAsyncComponentWithFallback(
  () => import('@js/components/ApiKeyEditorDialog.vue')
)

const { copy, copied, isSupported, text } = useClipboard()

const isDialogOpen = ref(false)
const dialogOptions = ref<{ item: ApiKey | undefined; title: string | undefined }>({
  item: undefined,
  title: undefined,
})

const isRegenerateDialogOpen = ref(false)
const regenerateDialogOptionsData = ref<ApiKey | undefined>(undefined)

const openApiKeyRegenerateDialog = (apiKey?: ApiKey) => {
  regenerateDialogOptionsData.value = apiKey ?? undefined
  isRegenerateDialogOpen.value = true
}

const openApiKeyEditorDialog = (apiKey?: ApiKey) => {
  dialogOptions.value = {
    item: apiKey,
    title: apiKey
      ? Translator.trans('u2_core.edit_api_key')
      : Translator.trans('u2_core.create_api_key'),
  }
  isDialogOpen.value = true
}
const refreshApiKey = (apiKey: ApiKey) => {
  set(items.value, items.value.map((item) => item['@id']).indexOf(apiKey['@id']), apiKey)
  closeApiKeyRegenerateDialog()
}

function closeApiKeyEditorDialog() {
  dialogOptions.value = {
    item: undefined,
    title: undefined,
  }
  isDialogOpen.value = false
}

function closeApiKeyRegenerateDialog() {
  regenerateDialogOptionsData.value = undefined
  isRegenerateDialogOpen.value = false
}

function onApiKeySaved() {
  closeApiKeyEditorDialog()
  fetchApiKeys()
}
const onApiKeyCreated = (apikey: ApiKey) => {
  closeApiKeyEditorDialog()
  items.value.unshift(apikey)
}

const items = ref<Array<ApiKey>>([])
function fetchApiKeys() {
  apiKeyApi.fetchAllApiKeys().then(({ data }) => {
    items.value = data['hydra:member']
  })
}

const { resolveNotification } = useHandleAxiosErrorResponse()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const deleteApiKey = async (item: ApiKey) => {
  const { isCanceled } = await revealConfirmDelete()
  if (isCanceled) {
    return
  }

  apiKeyApi
    .deleteApiKeyById(item.id)
    .then(() => {
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))
      fetchApiKeys()
    })
    .catch(resolveNotification)
}

watchEffect(() => {
  fetchApiKeys()
})
</script>

<template>
  <div class="max-w-2xl">
    <HeaderWithAction>
      {{ Translator.trans('u2.api_key.plural') }}
      <template #button>
        <ButtonNew @click="openApiKeyEditorDialog()" />
      </template>
    </HeaderWithAction>

    <ul role="list" class="divide-y divide-gray-100">
      <li v-for="item in items" :key="item.id" class="flex justify-between py-5">
        <div class="w-full flex-col">
          <div class="flex">
            <div class="grow">
              <span>
                {{ item.name }}
              </span>
              <span
                :class="[
                  {
                    'bg-green-50 text-green-700 ring-green-600/20': item.enabled,
                    'bg-gray-50 text-gray-600 ring-gray-500/10': !item.enabled,
                  },
                  'mt-0.5 rounded-md px-1.5 py-0.5 text-xs font-medium whitespace-nowrap ring-1 ring-inset',
                ]"
                >{{
                  item.enabled ? Translator.trans('u2.enabled') : Translator.trans('u2.disabled')
                }}
              </span>
            </div>

            <div class="ml-4 flex items-center justify-end break-words">
              <div class="mr-2 text-gray-500">
                {{ Translator.trans('u2.last_used_at') }}

                <template v-if="item.lastUsedAt">
                  <AppDate :date="item.lastUsedAt" />
                </template>
                <template v-else>
                  {{ Translator.trans('u2_core.never') }}
                </template>
              </div>
              <ButtonDropdownEllipsis>
                <template #items>
                  <ButtonDropdownItem
                    icon="edit"
                    :text="Translator.trans('u2.edit')"
                    @click.prevent="openApiKeyEditorDialog(item)"
                  />

                  <ButtonDropdownItem
                    icon="refresh"
                    :text="Translator.trans('u2.regenerate')"
                    @click.prevent="openApiKeyRegenerateDialog(item)"
                  />

                  <ButtonDropdownItem
                    icon="delete"
                    :text="Translator.trans('u2.delete')"
                    @click="deleteApiKey(item)"
                  />
                </template>
              </ButtonDropdownEllipsis>
            </div>
          </div>

          <div v-if="isSupported && item.readableApiKey">
            <div class="clear-both table p-2 text-xs break-words text-gray-500">
              <div class="text-neutral-800">
                <div class="mb-2">
                  <span class="flex items-center">
                    <AppInputText v-model="item.readableApiKey" :disabled="true" class="w-full" />

                    <ButtonBasic
                      :disabled="copied && text === item.readableApiKey"
                      @click="copy(item.readableApiKey)"
                    >
                      <SvgIcon
                        v-if="copied && text === item.readableApiKey"
                        icon="check-rounded"
                        size="large"
                      />
                      <SvgIcon v-else icon="copy" size="large" />
                    </ButtonBasic>
                  </span>
                </div>
                <p class="text-warning mt-0 mb-2 text-sm leading-normal">
                  {{ Translator.trans('u2_core.api_key.copy_warning') }}
                </p>
              </div>
            </div>
          </div>

          <div class="flex">
            <div class="grow leading-6 break-words italic">
              {{ Translator.trans('u2_core.created_at') }}
              <AppDate :relative="false" :date="item.createdAt" />
            </div>
            <div class="leading-6 break-words italic">
              {{ Translator.trans('u2.expires') }}
              <template v-if="item.expiresAt">
                <AppDate :relative="false" :date="item.expiresAt" />
              </template>
              <template v-else>
                {{ Translator.trans('u2_core.never') }}
              </template>
            </div>
          </div>
        </div>
      </li>
    </ul>

    <ApiKeyEditorDialog
      v-if="isDialogOpen"
      :api-key="dialogOptions.item"
      :title="dialogOptions.title!"
      @close="closeApiKeyEditorDialog"
      @saved="onApiKeySaved"
      @created="onApiKeyCreated"
    />

    <ApiKeyRegenerateDialog
      v-if="isRegenerateDialogOpen"
      :api-key="regenerateDialogOptionsData!"
      @close="closeApiKeyRegenerateDialog"
      @refreshed="refreshApiKey"
    />

    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @close="cancelDelete"
      @confirm="confirmDelete"
    >
      {{ Translator.trans('u2_core.delete_entry.confirmation') }}
    </ConfirmationDialog>
  </div>
</template>
