<script setup lang="ts">
import { computed, useId } from 'vue'
import ExpandingTextarea from '@js/components/form/ExpandingTextarea.vue'
import FormLabel from '@js/components/form/FormLabel.vue'

const props = withDefaults(
  defineProps<{
    id?: string
    disabled?: boolean
    label?: string | false
    name?: string
    placeholder?: string
    required?: boolean
    rows?: number
  }>(),
  {
    id: undefined,
    disabled: false,
    label: undefined,
    name: undefined,
    placeholder: undefined,
    required: false,
    rows: 3,
  }
)
const modelValue = defineModel<string>({
  required: true,
})

const id = computed(() => props.id ?? useId())
</script>

<template>
  <div class="rounded-skin-base border-skin-base border leading-tight shadow-xs">
    <span
      v-if="!!$slots.quote"
      class="m-2 mb-2 block border-l-2 border-gray-500 pl-1 whitespace-pre-wrap text-gray-500"
    >
      <slot name="quote" />
    </span>

    <FormLabel class="sr-only" :for="id">{{ label }}</FormLabel>

    <ExpandingTextarea
      :id
      v-model="modelValue"
      class="m-0 w-full border-transparent bg-transparent shadow-none hover:border-transparent hover:shadow-none focus:border-transparent focus:shadow-none focus:ring-0"
      :rows
      :required
      :disabled
      :placeholder
      :name
    />
  </div>
</template>
