<script setup lang="ts">
import { nextTick, ref, toRefs } from 'vue'
import ExpandingTextarea from '@js/components/form/ExpandingTextarea.vue'
import FormRow from '@js/components/form/FormRow.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import CurrentUserAssignedUserGroupSelect from '@js/components/form/CurrentUserAssignedUserGroupSelect.vue'
import type { Task } from '@js/model/task'

const props = withDefaults(
  defineProps<{
    task: Task
    errors?: {
      groups?: Array<string>
      commentContent?: Array<string>
    }
    isFormDisabled?: boolean
  }>(),
  {
    errors: () => ({
      groups: [],
      commentContent: [],
    }),
    isFormDisabled: false,
  }
)

const { task } = toRefs(props)

const form = ref({
  assignee: task.value.assignee,
  commentContent: undefined,
  userGroup: undefined,
})

const authStore = useAuthStore()

const emit = defineEmits<{
  (event: 'submitted', payload: typeof form.value): void
  (event: 'loaded'): void
}>()
function submit() {
  emit('submitted', form.value)
}

// Show and focus comment input on click/focus
const commentInput = ref()
const isCommentFormShown = ref(false)

function showCommentForm() {
  isCommentFormShown.value = true
  nextTick(() => {
    commentInput.value.$el.focus()
  })
}

emit('loaded')
</script>

<template>
  <form id="review_task_form" name="review_task_form" @submit.prevent="submit">
    <div class="w-96 max-w-full">
      <!-- Comment Placeholder -->
      <div
        v-if="false === isCommentFormShown"
        class="test-new-comment-placeholder mx-2 flex-1 self-center"
        tabindex="0"
        @focus="showCommentForm"
      >
        <div
          class="rounded-skin-base border-skin-base flex items-center border bg-gray-50 p-3 leading-tight shadow-xs"
        >
          <span class="text-gray-600">{{
            Translator.trans('u2.comment.add_comment_field_placeholder')
          }}</span>
        </div>
      </div>

      <!-- Comment -->
      <FormRow v-else :errors="errors.commentContent">
        <div class="flex flex-col gap-1">
          <div class="flex gap-1">
            <div class="flex basis-1/2 items-center gap-1">
              <UserAvatar />

              <div class="leading-normal font-medium">
                {{ authStore.user?.username }}
              </div>
            </div>

            <!-- Group Select -->
            <div class="flex basis-1/2 justify-end">
              <CurrentUserAssignedUserGroupSelect
                id="user-groups-options-new"
                v-model="form.userGroup"
                :placeholder="Translator.trans('u2_comment.unrestricted')"
              />
            </div>
          </div>

          <!-- Comment Content -->
          <ExpandingTextarea
            ref="commentInput"
            v-model="form.commentContent"
            :disabled="isFormDisabled"
            :placeholder="Translator.trans('u2.comment.add_comment_field_placeholder')"
          />
        </div>
      </FormRow>
    </div>
  </form>
</template>
