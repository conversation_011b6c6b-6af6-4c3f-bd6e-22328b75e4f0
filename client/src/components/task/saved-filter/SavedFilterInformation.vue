<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { getReadableTaskTypeByShortName, getTaskType } from '@js/model/task'
import AsideSection from '@js/components/AsideSection.vue'
import InformationGrid from '@js/components/InformationGrid.vue'
import InformationGridRow from '@js/components/InformationGridRow.vue'
import UserLabel from '@js/components/UserLabel.vue'
import Translator from '@js/translator'
import useUserAllQuery from '@js/composable/useUserAllQuery'
import type { SavedFilter } from '@js/model/saved-filter'

const props = defineProps<{
  savedFilter: SavedFilter
}>()

const router = useRouter()

const { allUsers } = useUserAllQuery()
const owner = computed(() => {
  return allUsers.value.find((user) => user['@id'] === props.savedFilter.owner)?.id
})

const filterTargetUrl = computed(() => {
  if (props.savedFilter) {
    return router.resolve({
      name: getTaskType(props.savedFilter.taskShortName) + 'List',
      query: { f: props.savedFilter.id },
    }).href
  }
  return undefined
})
</script>

<template>
  <AsideSection icon="filter" :headline="Translator.trans('u2_core.filter_information')">
    <InformationGrid>
      <!--      Name-->
      <InformationGridRow :label="Translator.trans('u2_core.name')">
        <router-link :to="{ name: 'SavedFilterEdit', params: { id: savedFilter.id } }">
          {{ savedFilter.name }}
        </router-link>
      </InformationGridRow>

      <!--      Owner-->
      <InformationGridRow :label="Translator.trans('u2_core.owner')">
        <UserLabel :user="owner" color="white" />
      </InformationGridRow>

      <!--      Target-->
      <InformationGridRow v-if="filterTargetUrl" :label="Translator.trans('u2_core.filter_target')">
        <router-link :to="filterTargetUrl">{{
          getReadableTaskTypeByShortName(savedFilter.taskShortName)
        }}</router-link>
      </InformationGridRow>

      <!--      Uql-->
      <InformationGridRow label="Uql">
        <code>{{ savedFilter.uql }}</code>
      </InformationGridRow>
    </InformationGrid>
  </AsideSection>
</template>
