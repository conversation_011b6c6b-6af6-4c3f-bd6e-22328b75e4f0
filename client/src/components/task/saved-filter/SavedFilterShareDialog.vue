<script setup lang="ts">
import { computed } from 'vue'
import { vOnClickOutside } from '@vueuse/components'
import Translator from '@js/translator'

const props = defineProps<{
  path?: string
}>()
const emit = defineEmits<(event: 'close') => void>()
const url = computed(() => props.path ?? window.location)
function close() {
  emit('close')
}
</script>

<template>
  <div
    v-on-click-outside="close"
    class="absolute inset-x-0 top-[10vh] z-30 mx-auto w-1/2 max-w-lg rounded-sm bg-gray-950 p-2.5 leading-normal text-white shadow-lg"
  >
    <h4 class="m-0 text-current">
      {{ Translator.trans('u2.share_filter') }}
    </h4>
    <p>
      <small>{{ Translator.trans('u2.share_filter.help') }}</small>
    </p>
    <input ref="shareBoxLink" class="text-off-black w-full" type="text" :value="url" readonly />
  </div>
</template>
