<script setup lang="ts">
import { computed, nextTick, ref, toRefs, watch, watchEffect } from 'vue'
import TaskCommentTextarea from '@js/components/task/TaskCommentTextarea.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useCommentsStore } from '@js/stores/comments'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import CurrentUserAssignedUserGroupSelect from '@js/components/form/CurrentUserAssignedUserGroupSelect.vue'
import type { Comment } from '@js/model/comment'

const props = withDefaults(
  defineProps<{
    modelValue: Partial<Comment>
    openNewCommentPanel?: boolean
    isSaving?: boolean
  }>(),
  {
    openNewCommentPanel: false,
    /*
     * TODO: Use a button loader instead
     * We are planing to solve this with this ticket with add loader inside button
     * https://universalunits.atlassian.net/browse/UU-5581
     */
    isSaving: false,
  }
)

const { modelValue } = toRefs(props)

const form = ref({
  ...modelValue.value,
  content: modelValue.value.content || '',
})
const emit = defineEmits<{
  (event: 'cancel' | 'open'): void
  (event: 'save', payload: Partial<Comment>): void
}>()

const currentUserName = computed(() => {
  return useAuthStore().user?.username
})

const commentsStore = useCommentsStore()
const quotedContent = computed(() =>
  form.value.quote ? commentsStore.getCommentByIri(form.value.quote)?.content : undefined
)

function addComment() {
  const newComment = {
    ...form.value,
    group: form.value.group === '' ? null : form.value.group,
  }
  emit('save', newComment)
}
function closeCommentPanel() {
  emit('cancel')
}
function openCommentPanel() {
  emit('open')
}

const newCommentContent = ref()
const focus = () =>
  nextTick(() => {
    newCommentContent.value.$el.querySelector('textarea').focus()
    window.scrollTo(0, document.body.scrollHeight + 500)
  })
defineExpose({ focus })

watchEffect(() => {
  if (props.openNewCommentPanel) {
    focus()
  }
})

watch(
  () => props.modelValue,
  () => {
    form.value = { ...props.modelValue, content: modelValue.value.content || '' }
  }
)
</script>

<template>
  <form class="flex items-start gap-0" @submit.prevent="addComment">
    <UserAvatar />

    <!-- placeholder -->
    <div v-if="!openNewCommentPanel" id="new-comment-placeholder" class="ml-2 flex-1 self-center">
      <div
        class="rounded-skin-base border-skin-base flex h-8 items-center border bg-gray-50 p-3 leading-tight shadow-xs"
        @click="openCommentPanel"
      >
        <span class="text-gray-600">{{
          Translator.trans('u2.comment.add_comment_field_placeholder')
        }}</span>
      </div>
    </div>

    <!-- new comment -->
    <div v-else id="new-comment-form" class="my-auto ml-2 flex-1">
      <div>
        <!-- header -->
        <div class="flex h-8 items-center justify-between">
          <div class="flex basis-1/2 items-baseline space-x-2">
            <div class="leading-normal font-medium">
              {{ currentUserName }}
            </div>
            <AppDateTime
              class="inline-block align-baseline text-sm leading-none whitespace-nowrap text-gray-500"
            />
          </div>
          <div class="flex basis-1/2 justify-end">
            <CurrentUserAssignedUserGroupSelect
              id="user-groups-options-new"
              v-model="form.group"
              :placeholder="Translator.trans('u2_comment.unrestricted')"
            />
          </div>
        </div>
        <!-- body -->
        <TaskCommentTextarea
          ref="newCommentContent"
          v-model="form.content"
          name="comment"
          class="mt-2"
          :rows="3"
          required
          :placeholder="Translator.trans('u2.comment.add_comment_field_placeholder')"
          :label="Translator.trans('u2_comment.comment')"
        >
          <template v-if="quotedContent" #quote>
            {{ quotedContent }}
          </template>
        </TaskCommentTextarea>
      </div>
      <!-- buttons -->
      <ButtonBasic
        class="mt-2 shadow-lg"
        :disabled="isSaving"
        button-style="solid"
        color="good"
        type="submit"
      >
        <span class="flex items-center space-x-2">
          <span>{{ Translator.trans('u2.post_comment') }}</span>
          <SvgIcon class="mt-1 text-white" icon="post-comment" />
        </span>
      </ButtonBasic>
      <ButtonBasic id="close-comment" button-style="text" @click="closeCommentPanel">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>
    </div>
  </form>
</template>
