<script setup lang="ts">
import { computed, ref } from 'vue'
import * as TaskApi from '@js/api/taskApi'
import { fetchStates } from '@js/types'
import AppMessage from '@js/components/AppMessage.vue'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import UserLabel from '@js/components/UserLabel.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import TaskReviewForm from '@js/components/task/TaskReviewForm.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useCommentsStore } from '@js/stores/comments'
import { useNotificationsStore } from '@js/stores/notifications'
import { useTaskStore } from '@js/stores/task'
import { useWorkflowStore } from '@js/stores/workflow'
import { getIdFromIri } from '@js/utilities/api-resource'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Task } from '@js/model/task'

const props = defineProps<{ task: Task }>()

const isDialogOpen = ref(false)
const loading = ref(true)
const isSaving = ref(false)

const workflowStore = useWorkflowStore()
const isManualReviewEnabled = computed(
  (): boolean => workflowStore.workflow?.manualReviewEnabled ?? false
)
const isWorkflowLoading = computed(
  (): boolean => workflowStore.fetchStates.workflow === fetchStates.loading
)
const isAddReviewButtonDisabled = computed(
  () =>
    !(props.task['u2:extra']?.canAddReview ?? true) ||
    !isManualReviewEnabled.value ||
    isSaving.value
)
const isRemoveReviewButtonDisabled = computed(
  () =>
    !(props.task['u2:extra']?.canRemoveReview ?? true) ||
    !isManualReviewEnabled.value ||
    isSaving.value
)
const authStore = useAuthStore()
const hasUserReviewed = computed(() =>
  props.task.reviews.some((review) => review.user === authStore.user?.['@id'])
)

const serverValidationErrors = ref({})
const taskStore = useTaskStore()
const commentStore = useCommentsStore()
const { resolveNotification } = useHandleAxiosErrorResponse()
const save = async (formData: {
  commentContent: string | undefined
  userGroup: undefined | string
}) => {
  isSaving.value = true

  const reviewComment = {
    userGroup: formData.commentContent ? (formData.userGroup ?? null) : null,
    commentContent: formData.commentContent,
  }

  try {
    if (hasUserReviewed.value) {
      await TaskApi.removeTaskReview(props.task, reviewComment)
      useNotificationsStore().addSuccess(Translator.trans('u2_core.remove_review.success'))
    } else {
      await TaskApi.addTaskReview(props.task, reviewComment)
      useNotificationsStore().addSuccess(Translator.trans('u2_core.review.success'))
    }
    serverValidationErrors.value = {}
    commentStore.fetchComments()
    await taskStore.refresh()
  } catch (error) {
    resolveNotification(error)
  } finally {
    isSaving.value = false
    isDialogOpen.value = false
  }
}
</script>

<template>
  <AsideSection id="entity-reviews" icon="review" :headline="Translator.trans('u2_core.reviews')">
    <template #button>
      <ButtonBasic
        v-if="hasUserReviewed"
        id="remove-review"
        :disabled="isRemoveReviewButtonDisabled"
        icon="no"
        :tooltip="Translator.trans('u2_core.click_to_remove_review')"
        class="mt-1"
        @click="isDialogOpen = true"
      />
      <ButtonBasic
        v-else
        id="add-review"
        :disabled="isAddReviewButtonDisabled"
        icon="yes-ok"
        class="mt-1"
        :tooltip="Translator.trans('u2_core.click_to_review_this_given_entity_type')"
        @click="isDialogOpen = true"
      />

      <AppDialog
        v-if="isDialogOpen"
        :title="
          hasUserReviewed
            ? Translator.trans('u2_core.remove_review')
            : Translator.trans('u2_core.add_a_review')
        "
        :loading="isSaving"
        @close="isDialogOpen = false"
      >
        <p>
          {{
            hasUserReviewed
              ? Translator.trans('u2_core.review.remove_review.confirm')
              : Translator.trans('u2_core.review.submit_review.confirm')
          }}
        </p>

        <TaskReviewForm
          :task="task"
          :is-form-disabled="loading"
          :errors="serverValidationErrors"
          class="w-96 max-w-full"
          @loaded="loading = false"
          @submitted="save"
        />

        <template #buttons>
          <ButtonBasic @click="isDialogOpen = false">
            {{ Translator.trans('u2.cancel') }}
          </ButtonBasic>

          <ButtonSave form="review_task_form" :state="isSaving ? 'saving' : 'ready'">
            {{
              hasUserReviewed
                ? Translator.trans('u2_core.remove_review')
                : Translator.trans('u2_core.add_a_review')
            }}
          </ButtonSave>
        </template>
      </AppDialog>
    </template>

    <ul v-if="task.reviews.length > 0" class="list-none p-0 pl-1">
      <li
        v-for="review in task.reviews"
        :key="review.id"
        class="mb-3 inline-flex items-center gap-1 last:mb-0"
      >
        <SvgIcon icon="yes-ok" class="text-good align-middle" />
        <UserLabel
          :user="review.user ? getIdFromIri(review.user) : undefined"
          color="white"
          class="align-middle"
        />
        <AppDateTime :relative="true" :date="review.stamp" class="align-middle" />
      </li>
    </ul>

    <AppEmptyState v-else-if="isManualReviewEnabled" class="table-data-text">
      <template #title>
        {{ Translator.trans('u2.review.no_reviews') }}
      </template>
      {{ Translator.trans('u2.review.task_not_reviewed') }}
      <template v-if="!isAddReviewButtonDisabled" #action>
        <ButtonBasic @click="isDialogOpen = true">
          {{ Translator.trans('u2_core.add_a_review') }}
        </ButtonBasic>
      </template>
    </AppEmptyState>

    <AppMessage
      v-else-if="!isWorkflowLoading"
      :text="Translator.trans('u2.review.manual_review_disabled_info')"
    />
  </AsideSection>
</template>
