<script setup lang="ts">
import AppDateTime from '@js/components/AppDateTime.vue'
import DeletedAvatar from '@js/components/user/DeletedAvatar.vue'
import Translator from '@js/translator'
import type { Comment } from '@js/model/comment'

defineProps<{
  comment: Comment
}>()
</script>

<template>
  <article class="flex flex-wrap overflow-hidden">
    <DeletedAvatar />

    <div class="ml-2 flex">
      <!-- header -->
      <span class="inline-flex items-center space-x-2">
        <span class="inline-block align-baseline leading-normal font-medium text-gray-800">
          {{ Translator.trans('u2_comment.deleted_comment') }}
        </span>

        <AppDateTime
          :date="comment.updatedAt"
          class="align-baseline text-sm leading-none text-gray-500"
        />
      </span>
    </div>
  </article>
</template>
