<script setup lang="ts">
import { computed, ref } from 'vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import extractErrorsFromResponse from '@js/helper/form/extractApiErrors'
import { fetchStates } from '@js/types'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import TaskComment from '@js/components/task/TaskComment.vue'
import TaskCommentDeleted from '@js/components/task/TaskCommentDeleted.vue'
import TaskCommentEdit from '@js/components/task/TaskCommentEdit.vue'
import TaskCommentNew from '@js/components/task/TaskCommentNew.vue'
import Translator from '@js/translator'
import { useCommentsStore } from '@js/stores/comments'
import { useNotificationsStore } from '@js/stores/notifications'
import { useTaskStore } from '@js/stores/task'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Comment } from '@js/model/comment'

const taskStore = useTaskStore()
const task = computed(() => {
  if (taskStore.task) {
    return taskStore.task
  }
  throw new Error('Task is not loaded')
})

const newComment = ref<Partial<Comment>>({
  task: task.value['@id'],
  content: '',
  quote: undefined,
  group: null,
})

const formErrors = ref()
const commentsStore = useCommentsStore()
const comments = computed((): Array<Comment> => commentsStore.comments)
const notificationsStore = useNotificationsStore()
const { resolveNotification } = useHandleAxiosErrorResponse()

const isNewCommentBeingSaved = ref(false)
const isNewCommentOpen = ref(false)
function createComment(comment: Partial<Comment>) {
  isNewCommentBeingSaved.value = true
  commentsStore
    .save(comment)
    .then(() => {
      isNewCommentOpen.value = false
      notificationsStore.addSuccess(Translator.trans('u2.add_comment_successful'))
    })
    .catch(async (error) => {
      await resolveNotification(error)
      formErrors.value = extractErrorsFromResponse(error.response)
    })
    .finally(() => {
      isNewCommentBeingSaved.value = false
    })
}
function updateComment(comment: Comment) {
  commentsStore
    .save(comment)
    .then(() => {
      notificationsStore.addSuccess(Translator.trans('u2.edit_comment_successful'))
    })
    .catch(async (error) => {
      await resolveNotification(error)
      formErrors.value = extractErrorsFromResponse(error.response)
    })
    .finally(() => {
      commentsStore.removeEditingState(comment.id)
    })
}

const currentComment = ref<Comment>()
const showDeleteCommentDialog = ref(false)
function confirmDelete(comment: Comment) {
  showDeleteCommentDialog.value = true
  currentComment.value = comment
}
function deleteComment(commentId: NonNullable<Comment['id']>): void {
  commentsStore.deleteComment(commentId).finally(() => {
    showDeleteCommentDialog.value = false
    currentComment.value = undefined
  })
}
function quote(comment: Comment) {
  newComment.value = {
    ...newComment.value,
    task: task.value['@id'],
    quote: comment['@id'],
    group: null,
  }
  isNewCommentOpen.value = true
  expandingTextareaFocus()
}
function openNewCommentPanel() {
  newComment.value = {
    task: task.value['@id'],
    content: '',
    quote: undefined,
    group: null,
  }
  isNewCommentOpen.value = true
  expandingTextareaFocus()
}

const taskNewComment = ref()
function expandingTextareaFocus() {
  return taskNewComment.value.focus()
}
function edit(comment: Comment) {
  return commentsStore.setEditingState(comment.id)
}
</script>

<template>
  <div class="mt-10 mb-10">
    <HeaderWithAction icon="comment" underline>
      {{ Translator.trans('u2_core.comments') }}
      <template v-if="commentsStore.count"> ({{ commentsStore.count }})</template>
      <template #button>
        <ButtonNew
          :tooltip="Translator.trans('u2_comment.new_comment')"
          @click="openNewCommentPanel"
        />
      </template>
    </HeaderWithAction>
    <AppLoader
      v-if="commentsStore.commentsFetchState !== fetchStates.resolved && comments.length === 0"
      class="h-44"
    />
    <div v-else>
      <template v-for="comment in comments">
        <TaskCommentDeleted
          v-if="comment.deleted"
          :key="comment.id + '-deleted'"
          :comment="comment"
          class="mt-6"
        />

        <TaskCommentEdit
          v-else-if="commentsStore.editedComments.includes(comment.id)"
          :key="comment.id + '-edit'"
          :comment="comment"
          class="mt-6"
          @cancel="commentsStore.removeEditingState(comment.id)"
          @save="updateComment"
        />

        <TaskComment
          v-else
          :key="comment.id"
          :comment="comment"
          class="mt-6"
          @delete="confirmDelete(comment)"
          @edit="edit(comment)"
          @quote="quote(comment)"
        />
      </template>
      <TaskCommentNew
        ref="taskNewComment"
        v-model="newComment"
        :open-new-comment-panel="isNewCommentOpen"
        :is-saving="isNewCommentBeingSaved"
        class="mt-6"
        @save="createComment"
        @cancel="isNewCommentOpen = false"
        @open="openNewCommentPanel"
      />
    </div>
    <ConfirmationDialog
      v-if="showDeleteCommentDialog && currentComment"
      @close="showDeleteCommentDialog = false"
      @confirm="deleteComment(currentComment.id)"
    >
      {{ Translator.trans('u2_comment.delete_comment.confirmation') }}
    </ConfirmationDialog>
  </div>
</template>
