<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import invariant from 'tiny-invariant'
import AppDateTime from '@js/components/AppDateTime.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import { queries } from '@js/query'
import { getIdFromIri } from '@js/utilities/api-resource'
import type { Comment } from '@js/model/comment'

const props = defineProps<{
  comment: Comment
}>()

const { comment } = toRefs(props)

const authorId = computed(() => {
  invariant(comment.value.author)
  return getId<PERSON>rom<PERSON>ri(comment.value.author) as number
})
const groupId = computed(() =>
  comment.value.group ? getIdFromIri(comment.value.group) : undefined
)
const quoteId = computed(() =>
  comment.value.quote ? getIdFromIri(comment.value.quote) : undefined
)

const { data: author } = useQuery(queries.users.single(authorId))

const { data: group } = useQuery({
  ...queries.userGroups.single(groupId),
  /*
  TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
    See: https://github.com/lukemorales/query-key-factory/issues/100
 */
  queryFn: computed(() => (groupId.value ? queries.userGroups.single(groupId).queryFn : skipToken)),
})

const { data: quote } = useQuery({
  ...queries.comments.single(quoteId),
  /*
  TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
    See: https://github.com/lukemorales/query-key-factory/issues/100
 */
  queryFn: computed(() => (quoteId.value ? queries.comments.single(quoteId).queryFn : skipToken)),
})

const emit = defineEmits<(event: 'delete' | 'edit' | 'quote') => void>()
</script>

<template>
  <article class="flex flex-wrap overflow-hidden">
    <UserAvatar />

    <div class="ml-2 flex-1">
      <!-- header -->
      <span class="inline-flex h-8 flex-wrap items-center space-x-2">
        <span class="inline-block align-baseline leading-none font-medium text-gray-800">
          {{ author?.username }}
        </span>

        <AppDateTime
          :date="comment.createdAt"
          class="inline-block align-baseline text-sm leading-none whitespace-nowrap text-gray-500"
        />

        <span v-if="group" class="inline-block align-middle text-sm font-medium text-gray-800">
          <SvgIcon icon="lock-closed" size="small" class="align-middle" />
          <span class="inline-block align-middle">{{
            Translator.trans('u2_comment.restricted_to_given_group', { group_name: group.name })
          }}</span>
        </span>
      </span>

      <!-- body -->
      <div>
        <div
          v-if="quote"
          class="mb-2 block border-l-2 border-gray-500 pl-1 whitespace-pre-wrap text-gray-500"
          v-text="quote.content"
        />
        <span class="whitespace-pre-wrap" v-text="comment.content" />
      </div>

      <!-- buttons -->
      <span class="mt-2 flex">
        <ButtonBasic
          icon="quote"
          :tooltip="Translator.trans('u2.comment.add_quote')"
          class="flex items-center"
          @click="emit('quote')"
        >
          {{ Translator.trans('u2_comment.quote') }}
        </ButtonBasic>

        <ButtonEdit
          v-if="comment['u2:extra']?.canWrite"
          :tooltip="Translator.trans('u2_comment.edit_comment')"
          :show-text="true"
          @click="emit('edit')"
        />

        <ButtonDelete
          v-if="comment['u2:extra']?.canDelete"
          :tooltip="Translator.trans('u2_comment.delete_comment')"
          @click="emit('delete')"
        />
      </span>
    </div>
  </article>
</template>
