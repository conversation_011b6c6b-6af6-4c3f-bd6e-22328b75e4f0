<script setup lang="ts">
import * as TaskApi from '@js/api/taskApi'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Task } from '@js/model/task'
import { useNotificationsStore } from '@js/stores/notifications'
import Translator from '@js/translator'
import { useRouter } from 'vue-router'

const { taskName, taskId } = defineProps<{
  taskName: Task['u2:extra']['name']
  taskId: Task['id']
}>()

const notificationsStore = useNotificationsStore()
const { resolveNotification } = useHandleAxiosErrorResponse()
const router = useRouter()
async function duplicate() {
  try {
    const response = await TaskApi.duplicateTask(taskId)
    notificationsStore.addByType(response.data.messages)
    await router.push(response.data.redirect)
  } catch (error) {
    await resolveNotification(error)
  }
}
</script>

<template>
  <ConfirmationDialog
    :title="Translator.trans('u2.duplicate.dialog.title')"
    :accept-text="Translator.trans('u2.duplicate')"
    @confirm="duplicate"
  >
    {{ Translator.trans('u2.duplicate.dialog.text', { document_name: taskName }) }}
  </ConfirmationDialog>
</template>
