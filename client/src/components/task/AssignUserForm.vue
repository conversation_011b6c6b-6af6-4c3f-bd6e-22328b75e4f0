<script setup lang="ts">
import { computed, nextTick, ref } from 'vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import AppSelect from '@js/components/form/AppSelect.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ExpandingTextarea from '@js/components/form/ExpandingTextarea.vue'
import FormLabel from '@js/components/form/FormLabel.vue'
import FormRow from '@js/components/form/FormRow.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import CurrentUserAssignedUserGroupSelect from '@js/components/form/CurrentUserAssignedUserGroupSelect.vue'
import { queries } from '@js/query'
import { getIdFromIri } from '@js/utilities/api-resource'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { Task } from '@js/model/task'
import type { FormErrors as FormErrorsModel } from '@js/helper/form/mergeErrors'
import type { UserWithRestrictedProperties } from '@js/model/user'

const {
  task,
  isFormDisabled = false,
  name = undefined,
  errors,
} = defineProps<{
  task: Task
  errors: FormErrorsModel<{ assignee: string; content: string; group: string }>
  isFormDisabled?: boolean
  name?: string
}>()

type FormData = {
  assignee?: string
  content?: string
  group?: string
}

const formData = defineModel<FormData>('formData', { required: true })

const authStore = useAuthStore()

const { data: permittedUsersResponse, isLoading: isPermittedUsersLoading } = useQuery(
  queries.tasks.single(computed(() => task.id))._ctx.permittedUsers
)
const initialSelectedUserId = computed(() =>
  task.assignee ? getIdFromIri(task.assignee) : undefined
)
const { data: initialSelectedUser } = useQuery({
  ...queries.users.single(initialSelectedUserId),

  /*
  TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
    See: https://github.com/lukemorales/query-key-factory/issues/100
  */
  queryFn: computed(() =>
    initialSelectedUserId.value ? queries.users.single(initialSelectedUserId).queryFn : skipToken
  ),
})

const isLoading = computed(
  () =>
    isPermittedUsersLoading.value ||
    (initialSelectedUserId.value && !initialSelectedUser.value) === true
)

const permittedUsersFromServer = computed(
  () => permittedUsersResponse.value?.['hydra:member'] ?? []
)
const permittedUsers = computed(() => {
  if (isPermittedUsersLoading.value && initialSelectedUser.value) {
    return [initialSelectedUser.value]
  }

  if (
    !initialSelectedUser.value ||
    permittedUsersFromServer.value.find(
      (user) => user['@id'] === initialSelectedUser.value?.['@id']
    )
  ) {
    return permittedUsersFromServer.value
  }

  return [{ ...initialSelectedUser.value, disabled: true }, ...permittedUsersFromServer.value]
})

const showInsufficientPermissionsWarning = computed(
  () => (option: UserWithRestrictedProperties | undefined) =>
    initialSelectedUser.value &&
    option?.['@id'] === initialSelectedUser.value['@id'] &&
    !hasInitialSelectedUserPermission.value
)

const hasInitialSelectedUserPermission = computed(() =>
  permittedUsersFromServer.value.some((user) => user.id === initialSelectedUser.value?.id)
)
const isInitialSelectionCurrentSelection = computed(() => task.assignee === formData.value.assignee)
const assigneeErrors = computed(() => {
  const assigneeErrors = errors.assignee ?? []
  if (
    assigneeErrors.length === 0 &&
    isInitialSelectionCurrentSelection.value &&
    !hasInitialSelectedUserPermission.value
  ) {
    assigneeErrors.push(
      Translator.trans(
        'u2_core.assign_to_user.user_has_insufficient_permissions_to_view_record',
        {},
        'validators'
      )
    )
  }

  return assigneeErrors
})

// Show and focus comment input on click/focus
const commentInput = ref()
const isCommentFormShown = ref(false)

function showCommentForm() {
  isCommentFormShown.value = true
  nextTick(() => {
    commentInput.value.$el.focus()
  })
}

const isAssignedToMe = computed(() => formData.value.assignee === authStore.user?.['@id'])

function assignToMe() {
  formData.value.assignee = authStore.user?.['@id']
}
</script>

<template>
  <form>
    <div class="grid w-96 max-w-full grid-cols-1 gap-(--app-form-field-spacing)">
      <!-- Assignee Form Row -->
      <FormRow :errors="assigneeErrors">
        <template #label>
          <div class="flex items-center gap-1">
            <FormLabel class="basis-1/2">
              {{ Translator.trans('u2_core.assignee') }}
            </FormLabel>
            <div class="flex basis-1/2 justify-end">
              <ButtonBasic
                :class="{ invisible: isAssignedToMe }"
                class="test-assign-to-me-button"
                @click="assignToMe"
              >
                {{ Translator.trans('u2_core.assign_to_me') }}
              </ButtonBasic>
            </div>
          </div>
        </template>
        <AppSelect
          v-model="formData.assignee"
          :name="name + '[assignee]'"
          class="test-assignee-select"
          :disabled="isFormDisabled"
          :has-errors="assigneeErrors.length > 0"
          :options="permittedUsers"
          label-key="username"
          value-key="@id"
          :loading="isLoading"
        >
          <template #optionIcon="{ option }">
            <SvgIcon
              v-if="showInsufficientPermissionsWarning(option)"
              v-tooltip="
                Translator.trans('u2.insufficient_user_permissions_to_view_entity', {
                  entity_name: task['u2:extra'].readableTaskType,
                })
              "
              size="small"
              class="text-warning ml-1 inline-flex"
              icon="alert"
            />
          </template>
        </AppSelect>
      </FormRow>

      <!-- Comment Placeholder -->
      <div
        v-if="false === isCommentFormShown"
        class="flex-1 cursor-pointer self-center"
        tabindex="0"
        data-testid="new-comment-trigger"
        @focus="showCommentForm"
      >
        <div
          class="rounded-skin-base border-skin-base flex items-center border bg-gray-50 p-3 leading-tight shadow-xs"
        >
          <span class="text-gray-600">
            {{ Translator.trans('u2.comment.add_comment_field_placeholder') }}
          </span>
        </div>
      </div>

      <!-- Comment -->
      <FormRow v-else :errors="[...(errors.group ?? []), ...(errors.content ?? [])]" :label="false">
        <div class="flex flex-col gap-1">
          <div class="flex gap-1">
            <div class="flex basis-1/2 items-center gap-1">
              <UserAvatar />

              <div class="leading-normal font-medium">
                {{ authStore.user?.username }}
              </div>
            </div>

            <!-- Group Select -->
            <div class="flex basis-1/2 justify-end">
              <CurrentUserAssignedUserGroupSelect
                v-model="formData.group"
                :name="name + '[userGroup]'"
                :disabled="isFormDisabled"
                :placeholder="Translator.trans('u2_comment.unrestricted')"
              />
            </div>
          </div>

          <!-- Comment Content -->
          <ExpandingTextarea
            ref="commentInput"
            v-model="formData.content"
            :name="name + '[comment]'"
            :disabled="isFormDisabled"
            :placeholder="Translator.trans('u2.comment.add_comment_field_placeholder')"
            aria-label="assign_to_user_form[comment]"
          />
        </div>
      </FormRow>
    </div>
  </form>
</template>
