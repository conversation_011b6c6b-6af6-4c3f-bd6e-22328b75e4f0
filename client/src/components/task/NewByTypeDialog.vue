<script setup lang="ts">
import AppDialog from '@js/components/AppDialog.vue'
import Translator from '@js/translator'

defineProps<{
  newPath: string
  optionsForNew: object
}>()

defineEmits<(event: 'close') => void>()
</script>

<template>
  <AppDialog :title="Translator.trans('u2.new')" @close="$emit('close')">
    <div class="list-group w-96 max-w-full">
      <router-link
        v-for="(name, type) in optionsForNew"
        :key="type"
        :to="{ path: newPath, query: { type: type } }"
        class="list-group-item flex items-center justify-between"
      >
        {{ name }}
      </router-link>
    </div>
  </AppDialog>
</template>
