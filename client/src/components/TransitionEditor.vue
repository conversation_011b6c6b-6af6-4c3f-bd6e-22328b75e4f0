<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { toTypedSchema } from '@vee-validate/zod'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import { computed, ref, toRefs, watchEffect } from 'vue'
import { z } from 'zod'
import FieldTextarea from '@js/components/form/FieldTextarea.vue'
import FieldWorkflowStatusSelect from '@js/components/form/FieldWorkflowStatusSelect.vue'
import FieldInputText from '@js/components/form/FieldInputText.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { useNotificationsStore } from '@js/stores/notifications'
import { usePageStore } from '@js/stores/page'
import useForm from '@js/composable/useForm'
import { queries } from '@js/query'
import Translator from '@js/translator'
import { saveWorkflowTransition } from '@js/api/workflowTransitionApi'
import type { TransitionWithEmbeddedStatus } from '@js/api/workflowTransitionApi'

const props = defineProps<{ transition: Partial<TransitionWithEmbeddedStatus> }>()

const { data, isLoading } = useQuery(queries.statuses.all)
const allStatuses = computed(() => data.value?.['hydra:member'] ?? [])

const emit = defineEmits<{
  (event: 'saved', payload: TransitionWithEmbeddedStatus): void
  (event: 'loaded'): void
}>()

const { transition } = toRefs(props)

const { handleSubmit, setResponseErrors } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      name: z.string().min(1),
      originStatus: z.string().min(1),
      destinationStatus: z.string().min(1),
      description: z.string(),
    })
  ),
  initialValues: {
    name: transition.value.name ?? '',
    originStatus: transition.value.originStatus?.['@id'],
    destinationStatus: transition.value.destinationStatus?.['@id'],
    description: transition.value.description ?? '',
  },
})

const pageStore = usePageStore()
const { resolveNotification } = useHandleAxiosErrorResponse()
const notificationsStore = useNotificationsStore()
const save = handleSubmit(async (values) => {
  pageStore.loading = true
  const updatedTransition = ref({
    ...transition.value,
    ...values,
  })

  try {
    const { data } = await saveWorkflowTransition(updatedTransition.value)
    emit('saved', data)
    notificationsStore.addSuccess(Translator.trans('u2_core.success'))
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  } finally {
    pageStore.loading = false
  }
})

watchEffect(() => {
  if (!isLoading.value) {
    emit('loaded')
  }
})
</script>

<template>
  <form
    id="transition_form"
    name="transition"
    class="grid gap-(--app-form-field-spacing) sm:grid-cols-2"
    @submit.prevent="save"
  >
    <FieldInputText
      class="sm:col-span-2"
      :label="Translator.trans('u2.name')"
      name="name"
      :required="true"
      maxlength="120"
      :disabled="isLoading"
    />

    <FieldWorkflowStatusSelect
      :label="Translator.trans('u2_core.workflow.origin_status')"
      name="originStatus"
      :disabled="isLoading"
      :required="true"
      :statuses="allStatuses"
    />

    <FieldWorkflowStatusSelect
      :label="Translator.trans('u2_core.workflow.destination_status')"
      name="destinationStatus"
      :required="true"
      :statuses="allStatuses"
      :disabled="isLoading"
    />

    <FieldTextarea
      class="sm:col-span-2"
      :label="Translator.trans('u2.description')"
      :disabled="isLoading"
      name="description"
    />
  </form>
</template>
