<script setup lang="ts">
import { invalidateUnitQueries } from '@js/composable/useUnitUpdateMutation'
import { isAxiosError } from 'axios'
import { computed, ref, toRefs } from 'vue'
import invariant from 'tiny-invariant'
import { taxNumberApi } from '@js/api/taxNumberApi'
import AppMessage from '@js/components/AppMessage.vue'
import AppChip from '@js/components/AppChip.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import AppDialog from '@js/components/AppDialog.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import extractErrorsFromResponse from '@js/helper/form/extractApiErrors'
import { useNotificationsStore } from '@js/stores/notifications'
import Translator from '@js/translator'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { SelectOption } from '@js/types'
import type { FormErrors as FormErrorsType } from '@js/helper/form/mergeErrors'
import type { Country } from '@js/model/country'
import type { TaxNumber } from '@js/model/taxNumber'
import type { AnyUnit } from '@js/model/unit'

const props = defineProps<{
  unit?: AnyUnit
}>()

const notificationsStore = useNotificationsStore()
const allCountriesQuery = useCountriesAllQuery()

const countryOptions = computed((): Array<SelectOption> => {
  return allCountriesQuery.items.value?.map((country: Country) => ({
    id: country['@id'],
    name: country.nameShort,
  }))
})

type TaxNumberData = Omit<TaxNumber, 'country'> & {
  country: Country | undefined
}
const taxNumberData = computed<Array<TaxNumberData>>(() => {
  return taxNumbers.value
    ?.map((taxNumber) => ({
      ...taxNumber,
      country: allCountriesQuery.items.value?.find(
        (country) => country['@id'] === taxNumber.country
      ),
    }))
    .sort((a, b) => {
      if (a.country?.nameShort && b.country?.nameShort) {
        return a.country.nameShort.localeCompare(b.country.nameShort)
      }
      return 0
    })
})
const currentTaxNumber = ref<TaxNumberData>()
const form = ref<{
  value: TaxNumber['value']
  country: TaxNumber['country']
}>({ value: '', country: '' })
const errors = ref<FormErrorsType<typeof form.value>>({})
const showDialog = ref(false as boolean)

const showConfirmationDialog = ref(false as boolean)

const { unit } = toRefs(props)

const addedTaxNumbers = ref<Array<TaxNumber>>([])
const deletedTaxNumberIds = ref<Array<TaxNumber['id']>>([])
const taxNumbers = computed<Array<TaxNumber>>(() => {
  if (!unit.value) {
    return []
  }

  return unit.value.taxNumbers
    .concat(addedTaxNumbers.value)
    .filter((taxNumber) => !deletedTaxNumberIds.value.includes(taxNumber.id))
})
const openDeleteDialog = (taxNumber: TaxNumberData) => {
  showConfirmationDialog.value = true
  currentTaxNumber.value = taxNumber
}

async function deleteTaxNumber(taxNumber: TaxNumberData) {
  try {
    if (!unit.value) {
      return
    }
    await taxNumberApi.deleteTaxNumberById(taxNumber.id)
    notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
    deletedTaxNumberIds.value.push(taxNumber.id)
    invalidateUnitQueries(unit.value.id)
  } catch (error) {
    resolveNotification(error)
  } finally {
    showConfirmationDialog.value = false
  }
}

const isSaving = ref(false)
const { resolveNotification } = useHandleAxiosErrorResponse()
async function save() {
  try {
    if (!unit.value) {
      return
    }
    isSaving.value = true
    const { data: newTaxNumber } = await taxNumberApi.createTaxNumber({
      ...form.value,
      unit: unit.value['@id'],
    })
    addedTaxNumbers.value.push(newTaxNumber)
    notificationsStore.addSuccess(Translator.trans('u2_core.success'))
    invalidateUnitQueries(unit.value.id)

    clear()
    isSaving.value = false
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    errors.value = extractErrorsFromResponse(error.response)
    isSaving.value = false
  }
}

function clear() {
  showDialog.value = false
  form.value = {
    value: '',
    country: '',
  }
  errors.value = {}
}
</script>

<template>
  <div v-if="unit === undefined" class="text-action flex items-center gap-1.5">
    <AppMessage>
      {{ Translator.trans('u2.new_record.save_first') }}
    </AppMessage>
  </div>
  <div v-else class="inline-flex flex-wrap items-center gap-1">
    <AppChip
      v-for="item in taxNumberData"
      :key="item.id"
      class="mt-1.5 flex items-center"
      :close="true"
      @close="openDeleteDialog(item)"
    >
      <span v-tooltip="item.country?.nameShort">
        {{ item.country?.iso3166code }}
        {{ item.value }}
      </span>
    </AppChip>

    <!-- Add button -->
    <ButtonBasic
      button-style="solid"
      icon="add"
      class="mt-1.5 ml-1.5 whitespace-nowrap"
      @click="showDialog = true"
    >
      {{ Translator.trans('u2.add') }}
    </ButtonBasic>

    <!-- Delete confirmation dialog -->
    <ConfirmationDialog
      v-if="showConfirmationDialog && currentTaxNumber"
      @close="showConfirmationDialog = false"
      @confirm="deleteTaxNumber(currentTaxNumber)"
    >
      {{
        Translator.trans('u2_core.delete_given_entity_type_with_given_name.confirmation', {
          entity_type_name: Translator.trans('u2_core.tax_number'),
          entity_name: currentTaxNumber.country?.iso3166code + ' ' + currentTaxNumber?.value,
        })
      }}
    </ConfirmationDialog>
  </div>

  <AppDialog v-if="showDialog" :title="Translator.trans('u2.tax_number.add')" @close="clear">
    <div class="w-96 max-w-full">
      <FormErrors :errors="errors['']" />
      <form id="add-tax-number" class="flex flex-col" @submit.prevent="save">
        <!-- Country -->
        <BaseSelect
          v-model="form.country"
          name="country"
          :label="Translator.trans('u2.country')"
          :required="true"
          :errors="errors['country']"
          :options="countryOptions"
        />

        <!-- Tax number -->
        <BaseInputText
          v-model="form.value"
          name="value"
          :label="Translator.trans('u2_core.tax_number')"
          :required="true"
          maxlength="30"
          :errors="errors['value']"
        />
      </form>
    </div>
    <template #buttons>
      <ButtonBasic button-style="text" @click="clear">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonSave form="add-tax-number" :state="isSaving ? 'saving' : 'ready'" />
    </template>
  </AppDialog>
</template>
