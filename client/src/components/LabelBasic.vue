<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'
import type { Icon, labelColors } from '@js/utilities/name-lists'

withDefaults(
  defineProps<{
    color?: (typeof labelColors)[number]
    rounded?: 'none' | 'half' | 'full'
    text?: string
    icon?: Icon
  }>(),
  {
    color: 'white',
    rounded: 'half',
    text: '',
    icon: undefined,
  }
)
</script>

<template>
  <span
    :class="[
      'inline-flex max-w-full items-center gap-x-1 border px-2',
      { 'rounded-none': 'none' === rounded },
      { rounded: 'half' === rounded },
      { 'rounded-full': 'full' === rounded },
      { 'text-off-black border-gray-200 bg-white': 'white' === color },
      { 'bg-alert border-orange-500 text-white': 'alert' === color },
      { 'bg-action border-blue-600 text-white': 'action' === color },
      { 'bg-bad border-red-700 text-white': 'bad' === color },
      { 'bg-good border-green-600 text-white': 'good' === color },
      { 'border-u2-darker bg-u2 text-white': 'u2' === color },
      { 'border-u2-inverse-darker bg-u2-inverse text-white': 'u2-inverse' === color },
      { 'text-off-black border-gray-300 bg-gray-200': 'gray' === color },
    ]"
  >
    <SvgIcon v-if="icon" :icon="icon" class="text-gray-600" />
    <span class="truncate">
      <slot>{{ text }}</slot>
    </span>
  </span>
</template>
