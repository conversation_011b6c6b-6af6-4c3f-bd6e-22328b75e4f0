<script lang="ts" setup>
import { getIdFromIri } from '@js/utilities/api-resource'
import { ref } from 'vue'
import AppList from '@js/components/AppList.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import { useWorkflowStore } from '@js/stores/workflow'
import type { Workflow } from '@js/api/workflowApi'
import type { Transition, TransitionWithEmbeddedStatus } from '@js/api/workflowTransitionApi'
import type { TableHeader } from '@js/types'

withDefaults(
  defineProps<{
    loading?: boolean
    transitions?: Array<TransitionWithEmbeddedStatus>
    workflow: Workflow
  }>(),
  {
    loading: false,
    transitions: () => [],
  }
)
const showDeleteDialog = ref(false)
const tableHeaders: Array<TableHeader> = [
  {
    align: 'left',
    name: Translator.trans('u2_core.name'),
    id: 'name',
  },
  {
    align: 'center',
    name: Translator.trans('u2.transition'),
    id: 'transition',
  },
  {
    align: 'left',
    name: Translator.trans('u2.description'),
    id: 'description',
  },
  {
    align: 'left',
    name: Translator.trans('u2_core.workflow.conditions'),
    id: 'conditions',
  },
  {
    align: 'left',
    name: Translator.trans('u2_core.workflow.actions'),
    id: 'actions',
  },
  {
    name: '',
    id: 'actionButtons',
  },
]
const transitionId = ref()
const confirmDelete = (id: NonNullable<Transition['id']>) => {
  transitionId.value = id
  showDeleteDialog.value = true
}
const deleteTransition = async (transitionId: NonNullable<Transition['id']>) => {
  try {
    const workflowStore = useWorkflowStore()
    await workflowStore.deleteTransitionItem(transitionId)
    useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))
  } finally {
    showDeleteDialog.value = false
  }
}
</script>

<template>
  <div>
    <HeaderWithAction>
      {{ Translator.trans('u2_core.workflow.transitions') }}
      <template #button>
        <ButtonNew
          :to="{ name: 'WorkflowTransitionNew', params: { id: workflow.id } }"
          :tooltip="Translator.trans('u2_core.workflow.add_transition')"
        />
      </template>
    </HeaderWithAction>

    <AppLoader v-if="loading" class="h-24" />
    <AppTable v-else horizontal-scroll :headers="tableHeaders" :items="transitions">
      <template #item-actionButtons="{ item }">
        <span class="flex justify-end">
          <ButtonEdit
            :to="{
              name: 'WorkflowTransitionEdit',
              params: { id: getIdFromIri(item.workflow), transitionId: item.id },
            }"
            :tooltip="
              Translator.trans('u2_core.workflow.edit_transition_with_given_name', {
                transition_name: item.name,
              })
            "
          />

          <ButtonDelete
            :key="item.id"
            :tooltip="
              Translator.trans('u2_core.workflow.delete_transition_with_given_name', {
                transition_name: item.name,
              })
            "
            :show-text="false"
            @click="confirmDelete(item.id)"
          />
        </span>
      </template>

      <template #item-name="{ item }">
        <span class="font-bold whitespace-nowrap">
          {{ item.name }}
        </span>
      </template>

      <template #item-transition="{ item }">
        <span v-if="item" class="whitespace-nowrap">
          <StatusBadge :status="item.originStatus" />

          <SvgIcon icon="arrow-right" size="small" class="mx-1 align-middle" />

          <StatusBadge :status="item.destinationStatus" />
        </span>
      </template>

      <template #item-conditions="{ item }">
        <AppList :items="item.conditions" :compact="true">
          <template #default="{ listItem }">
            <li class="leading-tight">
              {{ listItem.description }}
            </li>
          </template>
        </AppList>
      </template>

      <template #item-actions="{ item }">
        <AppList :items="item.actions" :compact="true">
          <template #default="{ listItem }">
            <li class="leading-tight">
              {{ listItem.description }}
            </li>
          </template>
        </AppList>
      </template>
    </AppTable>

    <ConfirmationDialog
      v-if="showDeleteDialog"
      @confirm="deleteTransition(transitionId)"
      @close="showDeleteDialog = false"
    >
      {{ Translator.trans('u2_core.workflow.delete_transition.confirmation') }}
    </ConfirmationDialog>
  </div>
</template>
