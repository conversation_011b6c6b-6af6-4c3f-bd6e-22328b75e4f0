<script lang="ts" setup>
import { fieldStateApi } from '@js/api/fieldStateApi'
import { queries } from '@js/query'
import { useQueryClient } from '@tanstack/vue-query'
import { computed, inject, ref, watch } from 'vue'
import { fieldStateStatuses } from '@js/model/fieldState'
import AppCheckbox from '@js/components/form/AppCheckbox.vue'
import AppInputRadio from '@js/components/form/AppInputRadio.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppSelect from '@js/components/form/AppSelect.vue'
import AppTable from '@js/components/table/AppTable.vue'
import { fetchStates } from '@js/types'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import useSearchRecords from '@js/composable/useSearchRecords'
import useTable from '@js/composable/useTable'
import type { FieldConfiguration } from '@js/model/fieldConfiguration'
import type { FieldState } from '@js/model/fieldState'
import type { Ref } from 'vue'

const taskTypeToTranslationKeyMap = {
  'U2\\Entity\\Task\\TaskType\\ApmTransaction': 'APM Transactions',
  'U2\\Entity\\Task\\TaskType\\Contract': Translator.trans('u2_contractmanagement.contract'),
  'U2\\Entity\\Task\\TaskType\\CountryByCountryReport': Translator.trans(
    'u2_tpm.country_by_country_report'
  ),
  'U2\\Entity\\Task\\TaskType\\FinancialData': Translator.trans('u2_tpm.financial_data'),
  'U2\\Entity\\Task\\TaskType\\Igt1Transaction': 'IGT 1',
  'U2\\Entity\\Task\\TaskType\\Igt2Transaction': 'IGT 2',
  'U2\\Entity\\Task\\TaskType\\Igt3Transaction': 'IGT 3',
  'U2\\Entity\\Task\\TaskType\\Igt4Transaction': 'IGT 4',
  'U2\\Entity\\Task\\TaskType\\Igt5Transaction': 'IGT 5',
  'U2\\Entity\\Task\\TaskType\\IncomeTaxPlanning': Translator.trans('u2_tam.income_tax_planning'),
  'U2\\Entity\\Task\\TaskType\\LocalFile': Translator.trans('u2_tpm.local_file'),
  'U2\\Entity\\Task\\TaskType\\LossCarryForward': Translator.trans('u2_tam.loss_carry_forward'),
  'U2\\Entity\\Task\\TaskType\\MainBusinessActivity': Translator.trans(
    'u2_tpm.main_business_activity'
  ),
  'U2\\Entity\\Task\\TaskType\\MasterFile': Translator.trans('u2_tpm.master_file'),
  'U2\\Entity\\Task\\TaskType\\OtherDeadline': Translator.trans('u2_tcm.other_deadline'),
  'U2\\Entity\\Task\\TaskType\\TaxAssessmentMonitor': Translator.trans(
    'u2_tcm.tax_assessment_monitor'
  ),
  'U2\\Entity\\Task\\TaskType\\TaxAssessmentStatus': Translator.trans(
    'u2_tam.tax_assessment_status'
  ),
  'U2\\Entity\\Task\\TaskType\\TaxAuditRisk': Translator.trans('u2_tam.tax_audit_risk'),
  'U2\\Entity\\Task\\TaskType\\TaxAuthorityAuditObjection': Translator.trans(
    'u2_tcm.tax_authority_audit_objection'
  ),
  'U2\\Entity\\Task\\TaskType\\TaxConsultingFee': Translator.trans('u2_tam.tax_consulting_fee'),
  'U2\\Entity\\Task\\TaskType\\TaxCredit': Translator.trans('u2_tam.tax_credit'),
  'U2\\Entity\\Task\\TaskType\\TaxFilingMonitor': Translator.trans('u2_tcm.tax_filing_monitor'),
  'U2\\Entity\\Task\\TaskType\\TaxLitigation': Translator.trans('u2_tam.tax_litigation'),
  'U2\\Entity\\Task\\TaskType\\TaxRate': Translator.trans('u2_tam.tax_rate'),
  'U2\\Entity\\Task\\TaskType\\TaxRelevantRestriction': Translator.trans(
    'u2_tam.tax_relevant_restriction'
  ),
  'U2\\Entity\\Task\\TaskType\\Transaction': Translator.trans('u2_tpm.transaction'),
  'U2\\Entity\\Task\\TaskType\\TransferPricing': Translator.trans('u2_tam.transfer_pricing'),
  'U2\\Entity\\Task\\TaskType\\UnitPeriod': Translator.trans('u2.datasheets.status_monitor'),
} as const

const fieldConfiguration = inject('fieldConfiguration') as Ref<FieldConfiguration>
const fieldStates = inject('fieldStates') as Ref<Array<FieldState>>

const queryClient = useQueryClient()
const saveStates: Ref<Record<string, string>> = ref({})
const updateFieldState = async (item: FieldState, newState: FieldState['state']) => {
  if (item.field === undefined) {
    return
  }
  if (newState === item.state || item.field in saveStates.value) {
    return
  }

  saveStates.value[item.field] = fetchStates.loading

  if (item['@id'] !== undefined) {
    if (newState === fieldStateStatuses.visible) {
      /*
      Do not keep field states with state `visible`.
      Properties without field state are interpreted as `visible`.
      */
      await fieldStateApi.deleteFieldState(item)
    }
    if (newState !== fieldStateStatuses.visible) {
      await fieldStateApi.updateFieldState({
        ...item,
        state: newState,
      })
    }
  }

  if (item['@id'] === undefined && newState !== fieldStateStatuses.visible) {
    await fieldStateApi.createFieldState({
      ...item,
      fieldConfiguration: fieldConfiguration.value['@id'],
      state: newState,
    })
  }

  saveStates.value[item.field] = fetchStates.resolved
  queryClient.invalidateQueries({
    queryKey: queries.fieldConfigurations.single(fieldConfiguration.value.id).queryKey,
  })
  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
  delete saveStates.value[item.field]
}

const taskTypeOptions = computed(() => {
  return Object.entries(taskTypeToTranslationKeyMap).map(([id, name]) => {
    return { id, name }
  })
})

const { columns, query } = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2.name'),
      id: 'field',
    },
    {
      align: 'right',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2.enabled'),
      id: 'state-visible',
    },
    {
      align: 'center',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2.disabled'),
      id: 'state-disabled',
    },
    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2.hidden'),
      id: 'state-hidden',
    },
    {
      name: '',
      id: 'loading-state',
    },
  ],
  {
    filter: {
      search: '',
      showVisible: true,
      showHidden: true,
      showDisabled: true,
      taskType: '',
    },
  }
)

const filteredByTypeAndVisibility = computed(() => {
  return fieldStates.value.filter((fieldState: FieldState) => {
    return (
      ((query.value.filter?.showHidden && fieldState.state === fieldStateStatuses.hidden) ||
        (query.value.filter?.showVisible && fieldState.state === fieldStateStatuses.visible) ||
        (query.value.filter?.showDisabled && fieldState.state === fieldStateStatuses.disabled)) &&
      (!query.value.filter?.taskType ||
        fieldState.taskTypes.includes(query.value.filter.taskType.toString()))
    )
  })
})

const { searchQuery, filteredRecords } = useSearchRecords(
  filteredByTypeAndVisibility,
  ['field'],
  query.value.filter?.search
)
watch(searchQuery, (newValue: string) => {
  query.value.filter.search = newValue
})

const isEditable = (fieldState: FieldState) => {
  return (
    fieldState.field in saveStates.value &&
    (saveStates.value[fieldState.field] === fetchStates.loading ||
      saveStates.value[fieldState.field] === fetchStates.resolved)
  )
}
const isSaving = (fieldState: FieldState) => {
  return (
    fieldState.field in saveStates.value &&
    saveStates.value[fieldState.field] === fetchStates.loading
  )
}
const hasBeenSaved = (fieldState: FieldState) => {
  return (
    fieldState.field in saveStates.value &&
    saveStates.value[fieldState.field] === fetchStates.resolved
  )
}
</script>

<template>
  <div class="mt-4 min-w-144 px-4">
    <fieldset class="rounded-br-lg rounded-bl-lg bg-gray-100">
      <legend>{{ Translator.trans('u2.filter') }}</legend>
      <div class="flex flex-row">
        <div class="ml-1 w-2/5">
          <div class="flex items-center">
            <AppSearch
              v-model="searchQuery"
              class="w-full"
              :placeholder="Translator.trans('u2.search')"
            />
          </div>

          <div class="mt-4 w-full">
            <AppSelect
              v-model="query.filter.taskType"
              :clearable="false"
              :options="taskTypeOptions"
              :placeholder="Translator.trans('u2.select_a_task_type')"
              class="mt-4 w-full"
            />
          </div>
        </div>
        <div class="ml-4">
          <div class="flex items-center">
            <AppCheckbox v-model="query.filter.showVisible" />
            <span class="ml-1">
              {{ Translator.trans('u2.enabled') }}
            </span>
          </div>

          <div class="flex items-center">
            <AppCheckbox v-model="query.filter.showDisabled" />
            <span class="ml-1">
              {{ Translator.trans('u2.disabled') }}
            </span>
          </div>

          <div class="flex items-center">
            <AppCheckbox v-model="query.filter.showHidden" />
            <span class="ml-1">
              {{ Translator.trans('u2.hidden') }}
            </span>
          </div>
        </div>
      </div>
      <div class="mr-2 text-right">
        Showing {{ filteredRecords.length }} out of {{ fieldStates.length }}
      </div>
    </fieldset>

    <AppTable
      class="mt-2 min-w-full whitespace-nowrap"
      :headers="columns"
      :query="query"
      :items="filteredRecords"
      :has-controls="false"
    >
      <template #item-state-hidden="{ item }">
        <AppInputRadio
          :key="'set-hidden-button-' + item.field"
          :disabled="isEditable(item)"
          :name="item.field"
          value="hidden"
          :model-value="item.state"
          @update:model-value="updateFieldState(item, fieldStateStatuses.hidden)"
        />
      </template>
      <template #item-state-disabled="{ item }">
        <AppInputRadio
          :key="'set-disabled-button-' + item.field"
          :disabled="isEditable(item)"
          :name="item.field"
          value="disabled"
          :model-value="item.state"
          @update:model-value="updateFieldState(item, fieldStateStatuses.disabled)"
        />
      </template>
      <template #item-state-visible="{ item }">
        <AppInputRadio
          :key="'set-visible-button-' + item.field"
          :disabled="isEditable(item)"
          :name="item.field"
          value="visible"
          :model-value="item.state"
          @update:model-value="updateFieldState(item, fieldStateStatuses.visible)"
        />
      </template>
      <template #item-loading-state="{ item }">
        <div class="size-4">
          <svg
            v-if="isSaving(item)"
            :id="`loader-field-configuration-${item.field}`"
            class="size-4 animate-spin text-gray-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            />
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <SvgIcon v-if="hasBeenSaved(item)" icon="yes-ok" />
        </div>
      </template>
    </AppTable>
  </div>
</template>
