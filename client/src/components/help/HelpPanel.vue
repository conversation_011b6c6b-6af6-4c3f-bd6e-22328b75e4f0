<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { computed } from 'vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import { useHelpPanelStore } from '@js/stores/help-panel'

const props = withDefaults(
  defineProps<{
    title?: string
    text?: string
    id?: string
    modelValue?: boolean
  }>(),
  {
    id: undefined,
    modelValue: false,
    title: '',
    text: '',
  }
)

const emit = defineEmits<(event: 'toggle') => void>()
const show = useVModel(props, 'modelValue', emit)

function toggle() {
  if (props.id) {
    helpPanelStore.toggle(props.id)
    return
  }
  show.value = !show.value
}

const helpPanelStore = useHelpPanelStore()
if (props.id && !helpPanelStore.helpPanel[props.id]) {
  helpPanelStore.helpPanel[props.id] = false
}

const isVisible = computed(() => {
  if (props.id) {
    return helpPanelStore.helpPanel[props.id]
  }
  return show.value
})
</script>

<template>
  <transition
    enter-active-class="ease-out duration-300"
    leave-active-class="ease-in duration-300"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
    name="fade"
  >
    <div v-show="isVisible" class="flex flex-col rounded-sm bg-blue-100 p-8 leading-normal">
      <a
        href="#"
        class="text-action hover:text-action-darker focus:text-action-darker -mt-5 -mr-4 border-b-0 text-right leading-none hover:no-underline"
        @click="toggle"
      >
        <SvgIcon icon="cross" size="small" />
      </a>

      <h5 v-if="title" class="text-action">
        {{ title }}
      </h5>

      <slot>
        {{ text }}
      </slot>
    </div>
  </transition>
</template>
