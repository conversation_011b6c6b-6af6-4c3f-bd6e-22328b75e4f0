<script lang="ts" setup>
import Translator from '@js/translator'

withDefaults(defineProps<{ text?: string }>(), {
  text: () => Translator.trans('u2_core.show_more'),
})
</script>

<template>
  <button
    id="show-more"
    class="mt-2 w-full rounded-xs border border-gray-600 py-2 text-center text-sm tracking-wider text-gray-800 uppercase hover:bg-gray-200 hover:text-gray-950 focus:bg-gray-200 focus:text-gray-950"
  >
    {{ text }}
  </button>
</template>

<style scoped>
button {
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}
</style>
