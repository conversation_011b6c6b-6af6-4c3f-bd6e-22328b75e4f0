<script setup lang="ts">
import OverflowText from '@js/components/OverflowText.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import { ComboboxItem } from 'reka-ui'
import type { SearchResult } from '@js/composable/useGlobalSearch'

defineProps<{ item: SearchResult }>()
</script>

<template>
  <ComboboxItem :value="item" as-child>
    <li :class="['cursor-pointer py-2 pr-4 pl-6 select-none data-highlighted:bg-blue-50']">
      <a
        :href="item.url"
        class="pointer-events-auto -my-2 -mr-4 -ml-6 block py-2 pr-4 pl-6 hover:no-underline"
        @click.prevent
      >
        <span class="pointer-events-auto -my-2 -mr-4 -ml-6 flex w-full items-center py-2 pr-4 pl-6">
          <SvgIcon
            :icon="item.icon"
            size="large"
            class="flex-none text-blue-300"
            aria-hidden="true"
          />
          <OverflowText :text="item.name" class="ml-3 flex-auto truncate text-slate-700" />
        </span>
      </a>
    </li>
  </ComboboxItem>
</template>
