<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'
import type { Icon } from '@js/utilities/name-lists'

defineProps<{
  icon?: Icon
  title: string
}>()
</script>

<template>
  <div class="@container">
    <div
      class="relative mx-auto flex w-full max-w-2xl flex-col items-center justify-around gap-4 overflow-hidden rounded-sm border border-gray-100 p-6 @sm:flex-row @sm:p-10"
    >
      <div class="text-center @sm:text-left">
        <h3
          class="w-full text-lg leading-tight font-normal break-words text-gray-700 transition-all @xs:text-2xl"
        >
          {{ title }}
        </h3>

        <div v-if="!!$slots.default" class="leading-tight font-normal text-gray-500">
          <slot />
        </div>
      </div>

      <span v-if="icon" class="p-0 text-gray-300">
        <SvgIcon
          :icon="icon"
          size="manual"
          class="size-20 transition-all @md:size-24 @lg:size-32"
        />
      </span>
    </div>
  </div>
</template>
