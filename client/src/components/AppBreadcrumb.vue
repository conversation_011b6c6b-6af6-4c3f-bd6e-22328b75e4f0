<script setup lang="ts">
import AppLink from '@js/components/buttons/AppLink.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { RouteLocationRaw } from 'vue-router'

defineProps<{
  name: string
  to?: RouteLocationRaw
}>()
</script>

<template>
  <div class="flex items-center">
    <SvgIcon class="shrink-0 text-gray-400" icon="chevron-right" />
    <AppLink v-if="to" :to="to" class="text-sm font-medium text-gray-500 hover:text-gray-700">
      {{ name }}
    </AppLink>
    <span v-else class="text-sm font-medium text-gray-500 hover:text-gray-700">
      {{ name }}
    </span>
  </div>
</template>
