<script setup lang="ts">
import { choices } from '@js/model/choice'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { configurationDataApi } from '@js/api/configurationDataApi'
import AppSidebar from '@js/components/AppSidebar.vue'
import Translator from '@js/translator'
import { usePageStore } from '@js/stores/page'
import type { ConfigurationDataSidebarMenu } from '@js/api/configurationDataApi'

const pageStore = usePageStore()
const isCollapsed = computed(() => pageStore.sidebarCollapsed)
const route = useRoute()
const sidebarMenuRaw = ref<ConfigurationDataSidebarMenu>()

const sidebarMenu = computed(() => {
  return sidebarMenuRaw.value?.children.map((headerMenu) => {
    return {
      ...headerMenu,
      children:
        headerMenu.name === 'ChoiceFields'
          ? choices.map((choice) => {
              return {
                name: choice.slug,
                label: choice.title(),
                to: { name: 'ChoiceList', params: { slug: choice.slug } },
                current: choice.slug === route.params.slug,
              }
            })
          : headerMenu.children.map((menuItem) => {
              return {
                ...menuItem,
                label: Translator.trans(menuItem.label),
                to: menuItem.extras.vueRoute,
                current: menuItem.extras.vueRoute.name === route.name,
              }
            }),
    }
  })
})

onMounted(async () => {
  const response = await configurationDataApi.fetchAdminAreaSidebarMenu()
  sidebarMenuRaw.value = response.data
})
</script>

<template>
  <AppSidebar>
    <div v-if="!isCollapsed" class="pt-3 pl-3">
      <nav v-if="sidebarMenu">
        <template v-for="header in sidebarMenu" :key="header.name">
          <span class="mt-3 inline-block font-bold first:mt-0">
            {{ Translator.trans(header.label) }}
          </span>
          <ul>
            <li v-for="item in header.children" :key="item.name" class="pl-3 leading-relaxed">
              <router-link
                v-if="item.to"
                :to="item.to"
                :class="{ 'text-action-darker font-bold': item.current }"
              >
                {{ item.label }}
              </router-link>
            </li>
          </ul>
        </template>
      </nav>
    </div>
  </AppSidebar>
</template>
