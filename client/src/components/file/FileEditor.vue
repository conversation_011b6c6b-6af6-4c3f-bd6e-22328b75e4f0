<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import invariant from 'tiny-invariant'
import { computed, reactive, ref, toRef } from 'vue'
import { z } from 'zod'
import { isAxiosError } from 'axios'
import FormFieldGroup from '@js/components/form/FormFieldGroup.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import * as FileApi from '@js/api/fileApi'
import useUserGroupsQuery from '@js/composable/useUserGroupsQuery'
import FieldTextarea from '@js/components/form/FieldTextarea.vue'
import FieldChoiceCombinations from '@js/components/form/FieldChoiceCombinations.vue'
import useForm from '@js/composable/useForm'
import FieldRadioGroup from '@js/components/form/FieldRadioGroup.vue'
import FieldMultiSelect from '@js/components/form/FieldMultiSelect.vue'
import FieldInputFile from '@js/components/form/FieldInputFile.vue'
import FormFieldset from '@js/components/form/FormFieldset.vue'
import { PermissionMasks, permissionOptions } from '@js/model/permission'
import FormErrors from '@js/components/form/FormErrors.vue'
import Translator from '@js/translator'
import { UniqueID } from '@js/utilities/unique-id'
import { useAuthStore } from '@js/stores/auth'
import useFileTypesQuery from '@js/composable/useFileTypesQuery'
import { useMyPermission } from '@js/composable/useMyPermission'
import { useNotificationsStore } from '@js/stores/notifications'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import useUserAllQuery from '@js/composable/useUserAllQuery'
import { apiResourceIdSchema } from '@js/types'
import { fileAccessTypes } from '@js/model/file'
import type { FileUploadProgressEvent } from '@js/api/fileApi'
import type { FileAccessType, FileEntity } from '@js/model/file'
import type { FileType } from '@js/model/fileType'
import type { GroupPermission, UserPermission } from '@js/model/permission'
import type { ChoiceCombination } from '@js/components/form/BaseChoiceCombinations.vue'
import type { ApiResource, SelectOption } from '@js/types'

const props = withDefaults(
  defineProps<{
    file?: FileEntity
    resource?: ApiResource
  }>(),
  { file: undefined, resource: undefined }
)
const file = toRef(props, 'file')

const emit = defineEmits<{ (event: 'saved', payload: FileEntity): void; (event: 'loaded'): void }>()

const currentUserIri = useAuthStore().user?.['@id']
invariant(currentUserIri, 'There must be a user')

const accessOptions: Array<{
  id: FileAccessType
  name: string
  help: string
}> = [
  {
    id: 'public',
    name: Translator.trans('u2.access_type.public'),
    help: Translator.trans('u2.access_type.public.help'),
  },
  {
    id: 'smart',
    name: Translator.trans('u2_core.file.access_type_smart'),
    help: Translator.trans('u2_core.file.access_type_smart.help'),
  },
  {
    id: 'protected',
    name: Translator.trans('u2_core.file.access_type_protected'),
    help: Translator.trans('u2_core.file.access_type_protected.help'),
  },
]

const { handleSubmit, values, unmappedErrors, setResponseErrors } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      types: z.array(apiResourceIdSchema),
      userPermissions: z
        .array(
          z.object({
            left: z.string().min(1),
            right: z.string().min(1),
          })
        )
        .min(1, { message: defaultValidationMessages.required() }),
      groupPermissions: z.array(
        z.object({
          left: z.string().min(1),
          right: z.string().min(1),
        })
      ),
      accessType: z.enum(fileAccessTypes),
      description: z.string().nullable(),
      uploadedFile: z
        .custom<File>()
        .refine<File>((uploadedFile): uploadedFile is File => !!file.value?.id || !!uploadedFile, {
          message: defaultValidationMessages.required(),
        }),
    })
  ),
  initialValues: {
    types: file.value?.types || [],
    userPermissions: file.value
      ? file.value.userPermissions.map((permission: UserPermission) => {
          return {
            id: permission['@id'],
            left: permission.user,
            right: permission.mask.toString(),
          }
        })
      : [{ id: UniqueID(), left: currentUserIri, right: '141' }],
    groupPermissions: file.value
      ? file.value.groupPermissions.map((permission: GroupPermission) => {
          return {
            id: permission['@id'],
            left: permission.group,
            right: permission.mask.toString(),
          }
        })
      : [],
    accessType: file.value?.accessType || 'smart',
    description: file.value?.description || '',
    uploadedFile: undefined,
  },
})

const userPermissions = computed(() => (file.value?.id ? file.value.userPermissions : []))
const groupPermissions = computed(() => (file.value?.id ? file.value.groupPermissions : []))
const { iHavePermission } = useMyPermission(userPermissions, groupPermissions)
const canWrite = computed(() => (file?.value ? iHavePermission(PermissionMasks.EDIT) : true))
const canManage = computed(() => (file?.value ? iHavePermission(PermissionMasks.MANAGE) : true))

const { allFileTypes, isLoading: fileTypesLoading } = useFileTypesQuery()
const fileTypeOptions = computed(
  (): Array<SelectOption> =>
    allFileTypes.value?.map((type: FileType) => ({
      id: type['@id'],
      name: type.name,
    })) ?? []
)

const { allUsers, isLoading: isUsersLoading } = useUserAllQuery()

const { items: userGroups, isLoading: isUserGroupsLoading } = useUserGroupsQuery()

const systemSettingsQuery = useSystemSettingsAllQuery()

const maxFileUploadSize = computed(() => {
  const maxUploadFileSize = systemSettingsQuery.systemSettings.value?.maxUploadSize
  return maxUploadFileSize ? Number(maxUploadFileSize) * 1000 : undefined
})

async function handleFileUpdate() {
  const updatedFile = ref<Partial<FileEntity>>({
    ...file.value,
    types: values.types ?? [],
    description: values.description ?? null,
    accessType: values.accessType,
    userPermissions: values.userPermissions.map((option: ChoiceCombination) => {
      return {
        user: option.left,
        mask: +option.right,
      } as UserPermission
    }),
    groupPermissions: values.groupPermissions.map((option: ChoiceCombination) => {
      return {
        group: option.left,
        mask: +option.right,
      } as GroupPermission
    }),
  })

  const response = await FileApi.updateFile(updatedFile.value)
  emit('saved', response.data)
}

const progress = reactive({
  amount: 0,
  max: 100,
})

const isSaving = ref(false)
const { resolveNotification } = useHandleAxiosErrorResponse()
const save = handleSubmit(async (values) => {
  isSaving.value = true
  if (file.value?.id) {
    try {
      await handleFileUpdate()
    } catch (error) {
      await resolveNotification(error)
      invariant(isAxiosError(error) && error.response)
      setResponseErrors(error.response)
    } finally {
      isSaving.value = false
    }
    return
  }
  const notificationsStore = useNotificationsStore()
  invariant(values.uploadedFile)
  FileApi.createFile(
    {
      ...values,
      groupPermissions: values.groupPermissions.map((option: ChoiceCombination) => ({
        group: option.left,
        mask: +option.right,
      })),
      userPermissions: values.userPermissions.map((option: ChoiceCombination) => ({
        user: option.left,
        mask: +option.right,
      })),
      linkedResource: props.resource?.['@id'],
      uploadedFile: values.uploadedFile,
    },
    (event: FileUploadProgressEvent) => {
      progress.amount = event.loaded
      progress.max = event.total ?? 100
    }
  )
    .then((response) => {
      if (response) {
        notificationsStore.addSuccess(Translator.trans('u2_core.create_file.success'))
        emit('saved', response.data)
      }
    })
    .catch((error) => {
      notificationsStore.addError(Translator.trans('u2_core.upload_file.error'))
      setResponseErrors(error.response)
    })
    .finally(() => {
      progress.amount = 0
      progress.max = 100
      isSaving.value = false
    })
})

const state = computed(() => (isSaving.value ? 'saving' : 'ready'))
defineExpose({ state })
</script>

<template>
  <form id="file" name="file" @submit.prevent="save">
    <FormErrors :errors="unmappedErrors" />
    <FormFieldset :label="Translator.trans('u2_core.details')">
      <div class="fields-grid">
        <FieldInputFile
          v-if="!file?.id"
          name="uploadedFile"
          required
          :max-file-upload-size="maxFileUploadSize"
          :progress="progress"
        />

        <FieldMultiSelect
          :loading="fileTypesLoading"
          :label="Translator.trans('u2_core.types')"
          :placeholder="
            fileTypesLoading
              ? Translator.trans('u2.loading')
              : Translator.trans('u2.select_file_types')
          "
          :options="fileTypeOptions"
          :disabled="!canWrite"
          name="types"
        />
      </div>
    </FormFieldset>

    <FormFieldset :label="Translator.trans('u2_core.security')">
      <div class="fields-grid">
        <FieldRadioGroup
          class="max-w-fit"
          name="accessType"
          :options="accessOptions"
          :disabled="!canManage"
          :label="false"
        />

        <FormFieldGroup
          :label="Translator.trans('u2_core.assign_extra_permissions')"
          collapsible
          :collapsed="values.accessType === 'public'"
        >
          <!-- User permissions -->
          <FieldChoiceCombinations
            name="userPermissions"
            :options1="allUsers"
            :options1-loading="isUsersLoading"
            value-key1="@id"
            label-key1="username"
            :title1="Translator.trans('u2_core.user')"
            :title2="Translator.trans('u2_core.permissions')"
            :options2="permissionOptions"
            :disabled="!canManage"
            :no-choices-text="
              Translator.trans('u2_core.no_permissions_assigned_with_given_permission_type', {
                permission_type: 'user',
              })
            "
          />

          <!-- Group permissions -->
          <FieldChoiceCombinations
            class="mt-5"
            name="groupPermissions"
            :options1="userGroups"
            value-key1="@id"
            :title1="Translator.trans('u2_core.group')"
            :title2="Translator.trans('u2_core.permissions')"
            :options2="permissionOptions"
            :options1-loading="isUserGroupsLoading"
            :disabled="!canManage"
            :no-choices-text="
              Translator.trans('u2_core.no_permissions_assigned_with_given_permission_type', {
                permission_type: 'group',
              })
            "
          />
        </FormFieldGroup>
      </div>
    </FormFieldset>

    <FormFieldset :label="Translator.trans('u2_core.description')">
      <FieldTextarea
        :label="false"
        :rows="2"
        :disabled="!canWrite"
        name="description"
        :aria-label="Translator.trans('u2.description')"
      />
    </FormFieldset>
  </form>
</template>
