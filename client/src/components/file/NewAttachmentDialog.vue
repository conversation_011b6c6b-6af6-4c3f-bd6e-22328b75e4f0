<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { attachmentApi } from '@js/api/attachmentApi'
import AppDialog from '@js/components/AppDialog.vue'
import AppInputRadio from '@js/components/form/AppInputRadio.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import FileAccessTypeIcon from '@js/components/file/FileAccessTypeIcon.vue'
import FileEditor from '@js/components/file/FileEditor.vue'
import { flattenObject } from '@js/utilities/flattenObject'
import FormLabel from '@js/components/form/FormLabel.vue'
import OverflowText from '@js/components/OverflowText.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useTable from '@js/composable/useTable'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import useAttachmentsQuery from '@js/composable/useAttachmentsQuery'
import useFilesQuery from '@js/composable/useFilesQuery'
import { getIdFromIri } from '@js/utilities/api-resource'
import type { FileEntity } from '@js/model/file'
import type { ApiResource } from '@js/types'

const props = defineProps<{
  resource: ApiResource
}>()
const emit = defineEmits<(event: 'fileAttached' | 'close') => void>()

const activeTab = ref<'new' | 'link'>('new')

const resourceIri = computed(() => props.resource['@id'])
const {
  attachments,
  isFetching,
  refetch: refetchAttachmentsQuery,
} = useAttachmentsQuery(resourceIri)
const alreadyAttachedFiles = computed(() => attachments.value)

function showNewFileTab() {
  activeTab.value = 'new'
}

function showLinkFileTab() {
  if (activeTab.value === 'link') {
    return
  }
  activeTab.value = 'link'
}

const files = computed(() => {
  return filesFromServer.value.map((file) => {
    return flattenObject({
      ...file,
      'createdBy.username': file.createdBy ? getIdFromIri(file.createdBy) : null,
    })
  })
})

const { columns, query, sort, changePage, changePageSize, apiQuery } = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: '',
      wrap: false,
      id: 'radio',
    },
    {
      align: 'left',
      wrap: false,
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.id'),
      id: 'id',
    },

    {
      align: 'left',
      wrap: false,
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.name'),
      id: 'name',
    },

    {
      align: 'left',
      wrap: false,
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.created_by'),
      id: 'createdBy.username',
      type: 'user',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.created_at'),
      id: 'createdAt',
      type: 'datetime',
    },
  ],
  { filter: { search: '' } }
)
const { files: filesFromServer, totalRecordCount: totalItems } = useFilesQuery(apiQuery)

const notificationsStore = useNotificationsStore()
const selectedFileToLink = ref<FileEntity['@id']>()
const { resolveNotification } = useHandleAxiosErrorResponse()

const isLoading = ref(false)
async function link() {
  if (!selectedFileToLink.value) {
    notificationsStore.addError(Translator.trans('u2.file_required_link'))
    return
  }

  isLoading.value = true
  try {
    await attachmentApi.linkAttachment({
      resourceIri: props.resource['@id'],
      file: selectedFileToLink.value,
    })

    useNotificationsStore().addSuccess(Translator.trans('u2_core.link_file_to_entity.success'))

    close()

    refetchAttachmentsQuery()

    emit('fileAttached')
  } catch (error) {
    resolveNotification(error)
  } finally {
    isLoading.value = false
  }
}
function close() {
  emit('close')
}

onMounted(() => {
  showNewFileTab()
})
</script>

<template>
  <AppDialog
    :title="Translator.trans('u2.add_attachment')"
    :loading="isLoading"
    class="w-3xl max-w-full"
    @close="close"
  >
    <div class="">
      <div
        class="page-tab-container flex items-stretch justify-center border-b border-gray-200 bg-white"
      >
        <div
          :class="{
            'active page-tab -mb-px flex-auto rounded-tl-sm rounded-tr-sm rounded-br-none rounded-bl-none bg-white text-center font-bold':
              activeTab === 'new',
            'page-tab -mb-px flex-auto text-center': activeTab !== 'new',
          }"
        >
          <button
            type="button"
            :title="Translator.trans('u2_core.upload_a_new_file')"
            @click.prevent="showNewFileTab"
          >
            {{ Translator.trans('u2_core.upload_a_new_file') }}
          </button>
        </div>
        <div
          :class="{
            'active page-tab -mb-px flex-auto rounded-tl-sm rounded-tr-sm rounded-br-none rounded-bl-none bg-white text-center font-bold':
              activeTab === 'link',
            'page-tab -mb-px flex-auto text-center': activeTab !== 'link',
          }"
        >
          <button
            type="button"
            :title="Translator.trans('u2_core.link_an_existing_file')"
            @click.prevent="showLinkFileTab"
          >
            {{ Translator.trans('u2_core.link_an_existing_file') }}
          </button>
        </div>
      </div>

      <div v-if="activeTab === 'link'">
        <AppLoader v-if="isFetching" class="h-56" />
        <template v-else>
          <AppSearch
            v-model="query.filter.search"
            class="w-full"
            :placeholder="Translator.trans('u2.search')"
          />
          <AppTable
            v-if="files"
            class="mt-2"
            :headers="columns"
            :query="query"
            :items="files"
            :total-items="totalItems"
            horizontal-scroll
            @sort="sort"
            @page-change="changePage"
            @page-size-change="changePageSize"
          >
            <template #item-radio="{ item }">
              <span
                v-tooltip="
                  alreadyAttachedFiles.find((attachment) => attachment.file === item['@id']) !==
                  undefined
                    ? Translator.trans('u2.file_already_linked')
                    : undefined
                "
              >
                <AppInputRadio
                  :id="`file-to-link-${item.id}`"
                  :key="`selected-record-${item.id}`"
                  name="file_to_link"
                  :value="item['@id']"
                  :disabled="
                    alreadyAttachedFiles.find((attachment) => attachment.file === item['@id']) !==
                    undefined
                  "
                  :model-value="selectedFileToLink"
                  @update:model-value="selectedFileToLink = item['@id']"
                />
              </span>
            </template>
            <template #item-id="{ item }">
              <router-link
                :to="{ name: 'FileEdit', params: { id: item.id } }"
                class="flex items-center gap-x-1 hover:no-underline"
              >
                <FileAccessTypeIcon :access-type="item.accessType" />
                {{ item.id }}
              </router-link>
            </template>
            <template #item-name="{ item }">
              <FormLabel :for="`file-to-link-${item.id}`" class="w-full align-middle">
                <OverflowText class="max-w-xxs block truncate" :text="item.name" />
              </FormLabel>
            </template>
          </AppTable>
        </template>
      </div>

      <template v-else-if="activeTab === 'new'">
        <FileEditor v-show="!isLoading" :resource="resource" @saved="emit('fileAttached')" />
      </template>
    </div>
    <template #buttons>
      <ButtonBasic @click="close">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <!-- Link Button -->
      <ButtonBasic
        v-if="activeTab === 'link'"
        :disabled="isLoading"
        color="action"
        button-style="solid"
        @click="link"
      >
        {{ Translator.trans('u2_core.link') }}
      </ButtonBasic>

      <!-- Upload Button -->
      <ButtonBasic
        v-else
        :disabled="isLoading"
        color="action"
        form="file"
        button-style="solid"
        type="submit"
      >
        {{ Translator.trans('u2_core.upload') }}
      </ButtonBasic>
    </template>
  </AppDialog>
</template>

<style scoped>
@reference "@css/app.css";

.page-tab-container {
  margin: 0 0 10px;
  padding: 0 10px;
}

.page-tab {
  padding: 10px;

  &.active {
    border: 1px solid theme('colors.gray.200');
    border-bottom-color: theme('colors.white');
  }
}
</style>
