<script setup lang="ts">
import { useQueryClient } from '@tanstack/vue-query'
import invariant from 'tiny-invariant'
import { computed, ref } from 'vue'
import { saveAs } from 'file-saver'
import { attachmentApi } from '@js/api/attachmentApi'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import { queries } from '@js/query'
import useAttachmentsQuery from '@js/composable/useAttachmentsQuery'
import AppDateTime from '@js/components/AppDateTime.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import FileAccessTypeIcon from '@js/components/file/FileAccessTypeIcon.vue'
import FilePermissions from '@js/components/file/FilePermissions.vue'
import LabelBasic from '@js/components/LabelBasic.vue'
import UserLabel from '@js/components/UserLabel.vue'
import RequestPermissionsDialog from '@js/components/file/RequestPermissionsDialog.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import useFileTypesQuery from '@js/composable/useFileTypesQuery'
import { useNotificationsStore } from '@js/stores/notifications'
import { usePageStore } from '@js/stores/page'
import useUserAllQuery from '@js/composable/useUserAllQuery'
import { vAnimate } from '@js/directives/animate'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { isUnrestrictedAttachment } from '@js/model/attachment'
import type { Attachment } from '@js/model/attachment'
import type { ApiResourceId } from '@js/types'
import type { FileType } from '@js/model/fileType'
import type { FileAccessType } from '@js/model/file'

const { resourceIri, sortBy = 'createdAt' } = defineProps<{
  resourceIri: string
  sortBy?: Extract<keyof Attachment, 'createdAt' | 'name'>
}>()
type AttachmentData = {
  attachment: Attachment
  file: ApiResourceId
  accessType: FileAccessType
  name: string
  unattachPath: string | null
  fileEditPath: string | null
  downloadPath: string | null
  createdById: number | null
  updatedById: number | null
  updatedAt: string
  createdAt: string
  types: Array<FileType>
  isOpen: boolean
}
const currentAttachment = ref<Attachment>()
const isRequestPermissionsDialogOpen = ref(false)
const attachmentCount = computed(() => attachments.value?.length ?? 0)
const { attachments, isLoading: isAttachmentsLoading } = useAttachmentsQuery(() => resourceIri)
const loading = computed(() => isAttachmentsLoading.value || isFileTypesLoading.value)
const attachmentsData = computed<Array<AttachmentData>>(
  () =>
    attachments.value
      ?.map((attachment) => {
        return {
          attachment,
          accessType: attachment.accessType,
          name: attachment.name ?? Translator.trans('u2_core.the_name_of_this_file_is_restricted'),
          file: attachment.file,
          unattachPath: attachment.links.unattachPath,
          fileEditPath: attachment.links.fileEditPath,
          downloadPath: attachment.links.downloadPath,
          createdById:
            allUsers.value.find((user) => user['@id'] === attachment.createdBy)?.id ?? null,
          updatedById:
            allUsers.value.find((user) => user['@id'] === attachment.createdBy)?.id ?? null,
          updatedAt: attachment.updatedAt,
          createdAt: attachment.createdAt,
          types: attachment.types.map((type) => {
            const fileType = allFileTypes.value.find((fileType) => fileType['@id'] === type)
            invariant(fileType, 'File type not found')
            return fileType
          }),
          isOpen: attachmentsWithOpenInfo.value.includes(attachment['@id']),
        }
      })
      .sort((a, b) => {
        const valueA = a.attachment[sortBy] ?? ''
        const valueB = b.attachment[sortBy] ?? ''
        if (sortBy === 'name') {
          return valueA.localeCompare(valueB)
        }
        return valueA > valueB ? -1 : 1
      }) ?? []
)

const isRemoveDialogShown = ref(false)
function openRemoveDialog(attachmentData: AttachmentData) {
  currentAttachment.value = attachments.value?.find(
    (attachment) => attachment['@id'] === attachmentData.attachment['@id']
  )
  isRemoveDialogShown.value = true
}
function openRequestPermissionsDialog(attachmentData: AttachmentData) {
  currentAttachment.value = attachments.value?.find(
    (attachment) => attachment['@id'] === attachmentData.attachment['@id']
  )
  isRequestPermissionsDialogOpen.value = true
}
function closeRequestPermissionsDialog() {
  currentAttachment.value = undefined
  isRequestPermissionsDialogOpen.value = false
}

const pageStore = usePageStore()
const queryClient = useQueryClient()

function unlink(attachment?: AttachmentData['attachment']) {
  if (!attachment || !isUnrestrictedAttachment(attachment) || !attachment.links.unattachPath) {
    return
  }
  pageStore.loading = true

  attachmentApi
    .deleteAttachment(attachment)
    .then(() => {
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))
      queryClient.invalidateQueries({
        queryKey: queries.attachments.all(resourceIri).queryKey,
      })
    })
    .catch(async (error) => {
      await resolveNotification(error)
      useNotificationsStore().addError(Translator.trans('u2.removing_attachment_unsuccessful'))
    })
    .finally(() => {
      isRemoveDialogShown.value = false
      pageStore.loading = false
    })
}

const { resolveNotification } = useHandleAxiosErrorResponse()
function downloadFile(attachment: AttachmentData) {
  invariant(attachment)
  if (isUnrestrictedAttachment(attachment.attachment)) {
    attachmentApi
      .downloadAttachment(attachment.attachment)
      .then(({ data }) => saveAs(data, attachment.name))
      .catch(resolveNotification)
  }
}

const { allUsers } = useUserAllQuery()
const { allFileTypes, isLoading: isFileTypesLoading } = useFileTypesQuery()
const expandedCount = computed(() => attachmentsWithOpenInfo.value.length)

const attachmentsWithOpenInfo = ref<Array<ApiResourceId>>([])
function toggleInfo(attachmentIri: Attachment['@id']) {
  if (attachmentsWithOpenInfo.value.includes(attachmentIri)) {
    attachmentsWithOpenInfo.value = attachmentsWithOpenInfo.value.filter(
      (iri) => iri !== attachmentIri
    )
    return
  }
  attachmentsWithOpenInfo.value.push(attachmentIri)
}
function expandAll() {
  attachmentsWithOpenInfo.value = attachmentsData.value.map(
    (attachment) => attachment.attachment['@id']
  )
}
function collapseAll() {
  attachmentsWithOpenInfo.value = []
}
defineExpose({ attachmentCount, collapseAll, expandAll, expandedCount })
</script>
<template>
  <div>
    <AppLoader v-if="loading" class="h-24" />
    <ul v-else-if="attachmentsData.length > 0" class="js-attachments-list-container list-none p-0">
      <li
        v-for="(attachment, index) in attachmentsData"
        :key="attachment.attachment['@id']"
        class="flex flex-col justify-between py-2"
      >
        <div class="flex w-full items-center justify-between">
          <span class="inline-flex min-w-0 items-center gap-x-1.5">
            <button
              v-tooltip="Translator.trans('u2.toggle_attachment_info')"
              type="button"
              class="flex"
              :aria-expanded="attachment.isOpen"
              :aria-label="Translator.trans('u2.toggle_attachment_info')"
              :aria-controls="attachment.isOpen ? `attachment-list-item-${index}` : undefined"
              @click="toggleInfo(attachment.attachment['@id'])"
            >
              <SvgIcon
                icon="chevron-right"
                :class="[
                  'transform text-gray-600 transition duration-300 ease-in-out',
                  { 'rotate-90': attachment.isOpen },
                ]"
              />
            </button>

            <FileAccessTypeIcon :access-type="attachment.accessType" class="flex-none" />
            <ButtonBasic
              v-if="attachment.downloadPath"
              class="block truncate font-medium"
              :title="
                Translator.trans('u2_core.download_file_name', { file_name: attachment.name })
              "
              @click="downloadFile(attachment)"
            >
              {{ attachment.name }}
            </ButtonBasic>
            <span v-else class="block truncate">
              {{ attachment.name }}
            </span>
          </span>
          <ButtonDropdown
            placement="bottom-end"
            :arrow="false"
            class="px-1"
            :aria-label="Translator.trans('u2.open_menu')"
          >
            <SvgIcon icon="dots" size="large" class="rotate-90 text-gray-500" aria-hidden="true" />
            <template #body>
              <ButtonDropdownItem
                icon="edit"
                :disabled="!attachment.fileEditPath"
                :text="Translator.trans('u2_core.edit_file')"
                :to="attachment.fileEditPath ?? undefined"
              />
              <ButtonDropdownItem
                icon="cross"
                :disabled="!attachment.unattachPath"
                :text="Translator.trans('u2_core.remove_file')"
                @click="openRemoveDialog(attachment)"
              />
            </template>
          </ButtonDropdown>
        </div>
        <div v-animate>
          <div
            v-if="attachment.isOpen"
            :id="`attachment-list-item-${index}`"
            role="region"
            class="flex w-full flex-col gap-y-1 pr-4 pl-6 leading-normal"
          >
            <span class="inline-flex flex-wrap items-center gap-x-1 gap-y-0.5">
              <span class="form-label">{{ Translator.trans('u2_core.created') }}:</span>
              <AppDateTime :date="attachment.createdAt" />
              <UserLabel :user="attachment.createdById" />
            </span>
            <span
              v-if="attachment.types.length > 0"
              class="inline-flex flex-wrap items-start gap-x-1 gap-y-0.5"
            >
              <span class="form-label self-center">{{ Translator.trans('u2_core.tags') }}:</span>
              <LabelBasic
                v-for="type in attachment.types.slice(0, 5)"
                :key="type['@id']"
                color="white"
                class="text-sm"
              >
                {{ type.name }}
              </LabelBasic>
              <router-link
                v-if="attachment.types.length > 5 && attachment.fileEditPath"
                :to="attachment.fileEditPath"
                class="self-center text-sm font-medium"
              >
                +
                {{
                  Translator.trans('u2.number_more', { number: attachment.types.length - 5 })
                }}</router-link
              >
            </span>
            <span class="inline-flex flex-wrap items-center gap-x-1 gap-y-0.5">
              <span class="form-label">{{ Translator.trans('u2_core.my_permissions') }}:</span>
              <FilePermissions
                :file="attachment.attachment"
                @send-permissions-request="openRequestPermissionsDialog(attachment)"
              />
            </span>
          </div>
        </div>
      </li>
    </ul>
    <AppEmptyState v-else>
      <template #title>
        {{ Translator.trans('u2.no_attachments') }}
      </template>
      {{ Translator.trans('u2.no_attachments_description') }}
      <template v-if="$slots['add-attachment']" #action>
        <slot name="add-attachment" />
      </template>
    </AppEmptyState>
    <AppDialog
      v-if="isRemoveDialogShown && currentAttachment"
      :title="Translator.trans('u2_core.remove_file')"
      @close="isRemoveDialogShown = false"
    >
      <p class="h-20 w-96 max-w-full">
        {{
          Translator.trans('u2_core.unlink_file_with_given_name_from_entity.confirmation', {
            file_name:
              currentAttachment.name ??
              Translator.trans('u2_core.the_name_of_this_file_is_restricted'),
          })
        }}
      </p>
      <template #buttons>
        <ButtonBasic @click="isRemoveDialogShown = false">
          {{ Translator.trans('u2.cancel') }}
        </ButtonBasic>
        <ButtonBasic button-style="solid" @click="unlink(currentAttachment)">
          {{ Translator.trans('u2_core.confirm_remove') }}
        </ButtonBasic>
      </template>
    </AppDialog>
    <RequestPermissionsDialog
      v-if="isRequestPermissionsDialogOpen && currentAttachment"
      :file="currentAttachment"
      @close="closeRequestPermissionsDialog"
    />
  </div>
</template>
