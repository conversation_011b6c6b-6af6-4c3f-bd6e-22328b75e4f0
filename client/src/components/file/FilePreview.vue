<script setup lang="ts">
import invariant from 'tiny-invariant'
import { computed, ref, toRef } from 'vue'
import PrettyBytes from '@js/components/PrettyBytes.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

const props = withDefaults(
  defineProps<{
    file: File
    width?: number
  }>(),
  {
    width: 250,
  }
)

const file = toRef(props, 'file')

const imageSrc = ref('')

const isImage = computed(() => file.value.type.startsWith('image/'))
if (isImage.value) {
  const reader = new FileReader()
  reader.onload = (e: ProgressEvent<FileReader>) => {
    const target = e.target
    invariant(target)
    imageSrc.value = target.result as string
  }
  reader.readAsDataURL(file.value)
}
</script>

<template>
  <div
    class="relative flex flex-col items-center justify-center rounded-sm border bg-white p-8 text-center shadow-xs"
  >
    <img v-if="isImage" :src="imageSrc" class="size-40" :alt="'Image ' + file.name" />
    <SvgIcon v-else class="mt-4 size-20 shrink-0 text-gray-400" icon="document" />

    <div v-if="$slots.buttons" class="absolute right-0 bottom-0 p-1">
      <slot name="buttons"> </slot>
    </div>

    <div class="my-3 break-words">
      <div class="font-semibold">{{ file.name }}</div>
      <div class="text-sm text-gray-500">
        <PrettyBytes :bytes="file.size" />
      </div>
      <div class="text-sm text-gray-500">{{ file.type }}</div>
    </div>
  </div>
</template>
