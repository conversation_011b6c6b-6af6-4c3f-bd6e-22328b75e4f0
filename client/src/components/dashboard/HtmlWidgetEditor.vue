<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import Translator from '@js/translator'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import extractVuelidateErrors from '@js/helper/form/extractVuelidateErrors'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseTextareaWysiwyg from '@js/components/form/BaseTextareaWysiwyg.vue'
import { UniqueID } from '@js/utilities/unique-id'
import useWidgetConfiguration from '@js/composable/useWidgetConfiguration'
import BaseRadioGroup from '@js/components/form/BaseRadioGroup.vue'
import type { HtmlWidget } from '@js/model/dashboard'

const modelValue = defineModel<HtmlWidget>({
  required: true,
})

const { sizeSelectOptions } = useWidgetConfiguration(modelValue)

const formData = ref<HtmlWidget['parameters']>({
  id: modelValue.value.parameters?.id ?? UniqueID(),
  size: modelValue.value.parameters?.size ?? 1,
  title: modelValue.value.parameters?.title ?? Translator.trans('u2.dashboard.widget.html'),
  content: modelValue.value.parameters?.content ?? '',
})

const vuelidate = useVuelidate(
  {
    size: { required: { ...required, $message: defaultValidationMessages.required() } },
    title: { required: { ...required, $message: defaultValidationMessages.required() } },
    // Currently the default system widget 'welcome' will fallback with its content, why it is not required.
    // But all other widgets need to have a content. Therefore only require it if the to be edit widget is not the default system widget 'welcome'.
    content:
      modelValue.value.parameters?.id === 'welcome'
        ? {}
        : {
            required: { ...required, $message: defaultValidationMessages.required() },
          },
  },
  formData
)

const errors = computed(() => extractVuelidateErrors<HtmlWidget['parameters']>(vuelidate.value))

const updateModelValue = (value: HtmlWidget['parameters']) => {
  vuelidate.value.$touch()

  modelValue.value = { ...modelValue.value, parameters: value }
}

onMounted(() => {
  // Populate defaults set by the form up the chain
  updateModelValue(formData.value)
})
</script>

<template>
  <div class="grid grid-cols-1 gap-(--app-form-field-spacing)">
    <BaseInputText
      v-model="formData.title"
      :label="Translator.trans('u2.title')"
      :required="true"
      maxlength="120"
      :errors="errors.title"
      @blur="vuelidate.title.$touch()"
      @update:model-value="
        updateModelValue({ ...formData, title: $event ? $event.toString() : '' })
      "
    />

    <BaseRadioGroup
      v-model="formData.size"
      :required="true"
      horizontal
      :label="Translator.trans('u2.size')"
      class="flex max-w-fit flex-col gap-2"
      :options="sizeSelectOptions"
      :errors="errors.size"
      @update:model-value="updateModelValue({ ...formData, size: $event ?? 1 })"
    />

    <BaseTextareaWysiwyg
      v-model="formData.content"
      :label="Translator.trans('u2.content')"
      :required="true"
      :errors="errors.content"
      @update:model-value="updateModelValue({ ...formData, content: $event })"
    />
  </div>
</template>
