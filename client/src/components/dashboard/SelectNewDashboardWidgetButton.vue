<script setup lang="ts">
import { ref } from 'vue'
import Translator from '@js/translator'
import { WidgetTypes } from '@js/model/dashboard'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import { UniqueID } from '@js/utilities/unique-id'
import AppDialog from '@js/components/AppDialog.vue'
import type { Widget, WidgetType } from '@js/model/dashboard'
import type { Color } from '@js/utilities/name-lists'

withDefaults(
  defineProps<{
    buttonStyle?: 'outlined' | 'solid' | 'text'
    color?: Color
    disabled?: boolean
    href?: string
    showText?: boolean
    to?: string | object
    tooltip?: string
  }>(),
  {
    buttonStyle: 'solid',
    color: 'action',
    disabled: false,
    href: undefined,
    showText: true,
    to: undefined,
    tooltip: undefined,
  }
)

const emit = defineEmits<(event: 'widgetSelected', payload: Pick<Widget, 'id' | 'name'>) => void>()

const isDialogOpen = ref(false)
const types = [
  { id: WidgetTypes.html, name: Translator.trans('u2.dashboard.widget.html') },
  { id: WidgetTypes.filterResults, name: Translator.trans('u2.dashboard.widget.filter_results') },
  {
    id: WidgetTypes.filterStatistics,
    name: Translator.trans('u2.dashboard.widget.filter_statistics'),
  },
  { id: WidgetTypes.upNext, name: Translator.trans('u2.dashboard.widget.up_next') },
  {
    id: WidgetTypes.currentWeekOverview,
    name: Translator.trans('u2.dashboard.widget.current_week_overview'),
  },
]

const emitSelectedType = (widgetType: WidgetType) => {
  emit('widgetSelected', {
    ...{ id: UniqueID() },
    name: widgetType,
  })
  isDialogOpen.value = false
}
</script>

<template>
  <ButtonNew @click="isDialogOpen = true">
    {{ Translator.trans('u2.dashboard_widget.new') }}
  </ButtonNew>

  <AppDialog
    v-if="isDialogOpen"
    class="max-w-4xl"
    :title="Translator.trans('u2.dashboard.widget.new')"
    @close="isDialogOpen = false"
  >
    <h2>{{ Translator.trans('u2_core.available_options') }}</h2>
    <div class="list-group">
      <a
        v-for="type of types"
        :key="type.id"
        class="list-group-item flex items-center"
        @click="emitSelectedType(type.id)"
      >
        {{ type.name }}
      </a>
    </div>

    <template #buttons>
      <ButtonBasic @click="isDialogOpen = false">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>
    </template>
  </AppDialog>
</template>
