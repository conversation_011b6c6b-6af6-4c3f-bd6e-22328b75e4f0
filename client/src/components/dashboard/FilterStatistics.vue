<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { Pie } from 'vue-chartjs'
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  Title,
  Tooltip,
} from 'chart.js'
import { useRouter } from 'vue-router'
import ChartDataLabels from 'chartjs-plugin-datalabels'
import isNumber from 'lodash/isNumber'
import { chartSizes, chartTypes, widgetSizes } from '@js/model/dashboard'
import Translator from '@js/translator'
import InfoBox from '@js/components/InfoBox.vue'
import type { ChartSize, ChartType } from '@js/model/dashboard'
import type { Context } from 'chartjs-plugin-datalabels'
import type { ChartData, ChartEvent, ChartOptions, LegendElement, LegendItem } from 'chart.js'

ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale, LinearScale, ChartDataLabels)

const props = withDefaults(
  defineProps<{
    data: Array<{
      label: string
      color: string
      savedFilterId: number
      result: number
      listPath: string
    }>
    size?: ChartSize
    type?: ChartType
    showPercent?: boolean
  }>(),
  {
    size: widgetSizes.medium,
    type: chartTypes.doughnut,
    showPercent: false,
  }
)

const total = computed(() =>
  Object.values(data.value)
    .map((entry) => entry.result)
    .reduce((a, b) => a + b, 0)
)

const { data, size, type, showPercent } = toRefs(props)
const chartData = computed(
  (): ChartData<'pie'> => ({
    labels: data.value.map((entry) => entry.label + ' (' + entry.result + ')'),
    datasets: [
      {
        backgroundColor: data.value.map((entry) => entry.color),
        data: Object.values(data.value).map((entry) => entry.result),
        datalabels: {
          display: function (context: Context) {
            const value = context.dataset.data[context.dataIndex]
            return showPercent.value && isNumber(value) ? (value * 100) / total.value > 5 : false
          },
          anchor: 'center' as const,
          color: 'black',
          formatter: (value: unknown) => {
            if (!isNumber(value) || value === 0) {
              return ''
            }

            return ((value * 100) / total.value).toFixed(0) + '%'
          },
        },
      },
    ],
  })
)

const options = computed(
  (): ChartOptions<'pie'> => ({
    maintainAspectRatio: false,
    cutout: type.value === 'pie' ? '0%' : '40%',
    plugins: {
      legend: {
        onHover: function handleHover(
          evt: ChartEvent,
          item: LegendItem,
          legend: LegendElement<'pie'>
        ) {
          const backgroundColor = chartData.value.datasets[0].backgroundColor as Array<string>
          backgroundColor.forEach((color: string, index: number, colors: Array<string>) => {
            colors[index] =
              index === (item as LegendItem).index || color.length === 9 ? color : color + '4D'
          })
          legend.chart.update()
        },
        onLeave: function handleLeave(
          evt: ChartEvent,
          item: LegendItem,
          legend: LegendElement<'pie'>
        ) {
          const backgroundColor = chartData.value.datasets[0].backgroundColor as Array<string>
          backgroundColor.forEach((color: string, index: number, colors: Array<string>) => {
            colors[index] = color.length === 9 ? color.slice(0, -2) : color
          })
          legend.chart.update()
        },
        position: 'bottom' as const,
        onClick: (evt: ChartEvent, item: LegendItem) => {
          const index = (item as LegendItem).index
          if (!isNumber(index)) return
          router.push(data.value[index].listPath)
        },
        labels: {
          usePointStyle: true,
        },
      },
    },
  })
)

const router = useRouter()

const style = computed(() => {
  if (size.value === chartSizes.small) {
    return 'width: 12rem; height: 12rem;'
  }

  if (size.value === chartSizes.large) {
    return 'width: 24rem; height: 24rem;'
  }

  return 'width: 18rem; height: 18rem;'
})
</script>

<template>
  <div class="w-full">
    <Pie v-if="total > 0" :key="style" :options="options" :data="chartData" :style="style" />
    <InfoBox
      v-else
      class="my-10"
      icon="hide"
      :title="Translator.trans('u2_table.no_results_that_match_search')"
    >
      <p>{{ Translator.trans('u2_table.search_results_empty.help') }}</p>
    </InfoBox>
  </div>
</template>
