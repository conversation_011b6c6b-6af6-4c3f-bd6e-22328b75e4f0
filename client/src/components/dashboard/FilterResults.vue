<script setup lang="ts">
import { computed, ref } from 'vue'
import Translator from '@js/translator'
import AppChip from '@js/components/AppChip.vue'
import AppTable from '@js/components/table/AppTable.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import PeriodStatus from '@js/components/period/PeriodStatus.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import { taskTypeToTaskTypeKey } from '@js/model/task'
import useFilterResultsQuery from '@js/composable/useFilterResultsQuery'
import InfoBox from '@js/components/InfoBox.vue'
import TablePagination from '@js/components/table/TablePagination.vue'
import UserLabel from '@js/components/UserLabel.vue'
import type { TaskType } from '@js/model/task'
import type { TableHeader } from '@js/types'
import type { FilterResultsWidget } from '@js/model/dashboard'
import useLocaleNumberFormatter from '@js/composable/useLocaleNumberFormatter'

const props = defineProps<{
  widget: FilterResultsWidget
}>()

const page = ref(1)
const savedFilterConfiguration = computed(() => props.widget.parameters)
const { data } = useFilterResultsQuery(savedFilterConfiguration, page)

const listPath = computed(() => data.value?.listPath)
const taskType = computed(() => data.value?.taskType as TaskType)
const records = computed(() => data.value?.data.records ?? [])
const totalItems = computed(() => data.value?.data.totalItems)
const columns = computed(() => data.value?.data.columns.filter((column) => !column.hidden) ?? [])
const selectedColumns = computed(() => data.value?.data.state.clmns ?? [])

const headers = computed(() => {
  return columns.value
    ?.filter((column) => selectedColumns.value.includes(column.id))
    .map(
      (column) =>
        ({
          id: column.id,
          type: column.type,
          name: column.label ? column.label : column.name,
          hidden: column.hidden,
          label: column.label && column.label !== column.name ? column.name : undefined,
          unit: column.vars?.unit,
        }) as TableHeader
    )
})

const { format } = useLocaleNumberFormatter()
</script>

<template>
  <template v-if="listPath && totalItems !== undefined">
    <AppTable :headers="headers" :items="records" horizontal-scroll unique-item-key="Id">
      <!-- header slots -->
      <template #item-ReviewCount-header="{ header }">
        <SvgIcon v-tooltip="header.name" icon="review" class="align-middle text-current" />
      </template>

      <template #item-Files-header="{ header }">
        <SvgIcon v-tooltip="header.name" icon="document" class="align-middle text-current" />
      </template>

      <!-- header type slots -->

      <template #itemtype-currency-header="{ header }">
        <SvgIcon v-tooltip="header.name" icon="currency" class="align-middle text-current" />
      </template>

      <!-- cell type slots -->
      <template #itemtype-id="{ value }">
        <router-link
          :to="{ name: taskTypeToTaskTypeKey(taskType) + 'Edit', params: { id: value } }"
        >
          {{ value }}
        </router-link>
      </template>

      <template #itemtype-count="{ value }">
        <AppChip color="gray">{{ value }}</AppChip>
      </template>

      <template #itemtype-currency="{ value }">
        <span v-if="value" v-tooltip="value.name">{{ value.iso }}</span>
      </template>

      <template #itemtype-money="{ value }">
        <template v-if="value">
          <em
            v-if="typeof value === 'object'"
            v-tooltip="Translator.trans('u2_core.exchange_rate_unavailable')"
          >
            {{ Translator.trans('u2_core.n_a') }}
          </em>
          <span v-else>{{ format(value) }}</span>
        </template>
      </template>

      <template #itemtype-money_full="{ value }">
        {{ value.amount ? format(value.amount) : Translator.trans('u2_core.n_a') }}
        {{ value.currency }}
      </template>

      <template #itemtype-number="{ value }">
        <template v-if="value">
          {{ format(value, '0,0') }}
        </template>
      </template>

      <template #itemtype-percentage="{ value }">
        <em
          v-if="typeof value === 'object'"
          v-tooltip="Translator.trans('u2_core.exchange_rate_unavailable')"
        >
          {{ Translator.trans('u2_core.n_a') }}
        </em>
        <span v-else>{{ format(value * 100, '0.0000') }}</span>
      </template>

      <template #itemtype-period="{ value }">
        <div class="flex justify-center">
          <PeriodStatus v-if="value" :period-name="value.name" :is-closed="value.closed" />
        </div>
      </template>

      <template #itemtype-user="{ value }">
        <UserLabel v-if="value" :user="typeof value === 'object' ? value.id : value" />
      </template>

      <template #itemtype-workflow_status="{ value }">
        <StatusBadge v-if="value" :status="{ name: value.name, type: value.type }" />
        <SvgIcon
          v-else
          icon="no"
          class="size-3 align-text-top text-gray-700"
          :title="Translator.trans('u2_core.workflow.no_status_has_been_set')"
        />
      </template>
    </AppTable>
    <div class="mt-2 flex justify-between">
      <TablePagination
        :current-page="page"
        :items-per-page="widget.parameters.records_per_page"
        :total-items="totalItems"
        :max-visible-pages="1"
        @page-change="page = $event"
      />

      <a :href="listPath">
        {{
          Translator.transChoice('u2_core.showing_count_of_total_count_records', totalItems ?? 0, {
            showing_count: records.length,
            count: totalItems,
          })
        }}
      </a>
    </div>
  </template>
  <InfoBox v-else icon="alert" :title="Translator.trans('u2.dashboard.widget.not_configure_yet')" />
</template>
