<script setup lang="ts">
import { isAxiosError } from 'axios'
import { computed } from 'vue'
import { StatusCodes as HttpStatusCodes } from 'http-status-codes'
import { useAsyncComponent } from '@js/composable/useAsyncComponent'
import BaseWidget from '@js/components/dashboard/BaseWidget.vue'
import Translator from '@js/translator'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import InfoBox from '@js/components/InfoBox.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import useWidgetConfiguration from '@js/composable/useWidgetConfiguration'
import useFilterResultsQuery from '@js/composable/useFilterResultsQuery'
import type { FilterResultsWidget } from '@js/model/dashboard'

const { defineAsyncComponentWithFallback } = useAsyncComponent()
const FilterResultsWidgetEditor = defineAsyncComponentWithFallback(
  () => import('@js/components/dashboard/FilterResultsWidgetEditor.vue')
)
const FilterResults = defineAsyncComponentWithFallback(
  () => import('@js/components/dashboard/FilterResults.vue')
)

withDefaults(
  defineProps<{
    editable?: boolean
    draggable?: boolean
  }>(),
  {
    draggable: false,
  }
)

const emit = defineEmits<(event: 'delete') => void>()

const widget = defineModel<FilterResultsWidget>({
  required: true,
})

const { size, title } = useWidgetConfiguration(widget)

const savedFilterConfiguration = computed(() => widget.value.parameters)

const { isError, data, isLoading, failureReason, isFetching } =
  useFilterResultsQuery(savedFilterConfiguration)

const listPath = computed(() => data.value?.listPath)
const columns = computed(() => data.value?.data.columns.filter((column) => !column.hidden) ?? [])

const widgetIsLoading = computed(
  () => isLoading.value && savedFilterConfiguration.value.filter_id !== undefined
)
const maxColumnCount = 6
const hasError = computed(
  () =>
    isError.value ||
    savedFilterConfiguration.value === undefined ||
    savedFilterConfiguration.value.filter_id === undefined ||
    savedFilterConfiguration.value.columns.length === 0 ||
    savedFilterConfiguration.value.columns.length > maxColumnCount
)
const errorReason = computed(() => {
  if (!hasError.value && !failureReason.value) return undefined

  if (isAxiosError(failureReason.value)) {
    const response = failureReason.value.response
    if (response?.status === HttpStatusCodes.FORBIDDEN) {
      return Translator.trans('u2_core.widget.insufficient_permissions', {
        widget_name:
          savedFilterConfiguration.value?.title ??
          Translator.trans('u2.dashboard.widget.filter_results'),
      })
    }
  }

  return Translator.trans('u2.dashboard.widget.filter_results.error_while_fetching_data')
})
</script>

<template>
  <BaseWidget
    :is-edit="!modelValue.parameters"
    :draggable="draggable"
    :editable="editable"
    :is-loaded="!widgetIsLoading"
    :size="size"
    :has-error="hasError"
  >
    <template #title>
      <span :title="title">
        {{ title }}
      </span>
    </template>

    <template #buttons-edit-mode>
      <ButtonDelete :show-text="false" @click="emit('delete')" />
    </template>

    <template #buttons>
      <ButtonBasic icon="list" :to="listPath">
        {{ Translator.trans('u2.list') }}
      </ButtonBasic>
    </template>

    <template #content-edit-mode>
      <FilterResultsWidgetEditor v-model="widget" :disabled="isFetching" :columns="columns" />
    </template>

    <template #content>
      <FilterResults :widget="widget" />
    </template>

    <template #error>
      <InfoBox v-if="errorReason" icon="alert" :title="errorReason" />
    </template>
  </BaseWidget>
</template>
