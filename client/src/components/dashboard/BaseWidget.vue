<script setup lang="ts">
import { ref, toRef } from 'vue'
import DraggableHandle from '@js/components/DraggableHandle.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import Translator from '@js/translator'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

const props = withDefaults(
  defineProps<{
    size?: number
    editable?: boolean
    draggable?: boolean
    isLoaded?: boolean
    hasError?: boolean
    isEdit?: boolean
  }>(),
  {
    size: 1,
    editable: false,
    draggable: false,
    isLoaded: true,
    hasError: false,
    isEdit: false,
  }
)

const isEdit = ref(toRef(props, 'isEdit').value)
</script>

<template>
  <section
    class="mx-0 my-3 w-full bg-white transition-all"
    :class="[
      isLoaded || isEdit ? 'widget' : 'placeholder',
      'widget-size-' + size,
      { 'border-2 border-dotted p-2': editable },
      { 'border-bad': hasError },
    ]"
  >
    <div v-if="!isLoaded && !isEdit" class="flex h-full flex-col px-1 pt-0 pb-4">
      <div class="m-0 h-8 w-full border-b border-gray-200 pt-1 text-2xl font-bold">██████████</div>

      <AppLoader class="h-full opacity-25" />
    </div>
    <slot v-else name="default">
      <div
        class="mb-3 flex h-10 items-center"
        :class="{ 'border-b border-gray-700': !hasError || editable }"
      >
        <h2
          v-if="!hasError || editable"
          class="grow gap-2 truncate text-xl transition-all sm:text-2xl"
        >
          <DraggableHandle v-if="draggable" :tag="SvgIcon" icon="drag-handle-dots" />
          <slot name="title" />
        </h2>

        <div class="flex">
          <template v-if="editable">
            <ButtonEdit v-if="!isEdit" @click="isEdit = true" />
            <ButtonBasic v-else icon="hide" @click="isEdit = false">
              {{ Translator.trans('u2.preview') }}
            </ButtonBasic>
            <slot name="buttons-edit-mode" />
          </template>

          <slot v-else-if="!hasError" name="buttons" />
        </div>
      </div>

      <div>
        <slot v-if="editable && isEdit" name="content-edit-mode" />
        <slot v-else-if="hasError" name="error" />
        <slot v-else name="content" />
      </div>
    </slot>
  </section>
</template>

<style scoped>
@reference "@css/app.css";

.widget,
.placeholder {
  @media (min-width: theme(--breakpoint-sm)) {
    margin: 10px;
    width: calc(1 / 2 * 100% - 20px);
  }

  @media (width >= 1100px) {
    &.widget-size-1 {
      width: calc(1 / 3 * 100% - 20px);
    }

    &.widget-size-2 {
      width: calc(2 / 3 * 100% - 20px);
    }

    &.widget-size-3 {
      margin: 10px 0;
      width: 100%;
    }
  }
}

.placeholder {
  border-color: var(--color-gray-200);
  border-radius: var(--radius-sm);
  border-style: dashed;
  border-width: 1px;
  color: var(--color-gray-100);
  height: --spacing(64);
}
</style>
