<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { helpers, required } from '@vuelidate/validators'
import Translator from '@js/translator'
import {
  chartSizes,
  chartTypeToReadableNameCallback,
  chartTypes,
  widgetOptionToReadableNameCallback,
} from '@js/model/dashboard'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import extractVuelidateErrors from '@js/helper/form/extractVuelidateErrors'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseRadioGroup from '@js/components/form/BaseRadioGroup.vue'
import useSavedFilterAllQuery from '@js/composable/useSavedFilterAllQuery'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import { UniqueID } from '@js/utilities/unique-id'
import BaseColorPicker from '@js/components/form/BaseColorPicker.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import type { SavedFilter } from '@js/model/saved-filter'
import type {
  ChartSize,
  FilterStatisticConfigurationEntry,
  FilterStatisticsWidget,
} from '@js/model/dashboard'

type FilterStatisticsFormData = {
  showPercent: boolean
  chartSize: ChartSize
  type: 'pie' | 'doughnut'
  title: string
  entries: Array<{ savedFilterId?: number; label?: string; color?: string; id: string }>
}

defineProps<{
  disabled: boolean
}>()

const modelValue = defineModel<FilterStatisticsWidget>({
  required: true,
})

const updateModelValue = (value: FilterStatisticsFormData) => {
  vuelidate.value.$touch()
  modelValue.value = {
    ...modelValue.value,
    parameters: {
      size: 1,
      type: value.type,
      showPercent: value.showPercent,
      chartSize: value.chartSize,
      title: value.title,
      entries: value.entries.map(
        (entry) =>
          ({
            savedFilterId: entry.savedFilterId,
            label: entry.label,
            color: entry.color,
          }) as FilterStatisticConfigurationEntry
      ),
    },
  }
}

const defaultColors = [
  '#1A73B3',
  '#369BE2',
  '#CC7700',
  '#FF9F1A',
  '#99000D',
  '#E60013',
  '#0B8E4D',
  '#11D473',
  '#0e3c52',
  '#1a6c93',
]

const formData = ref<FilterStatisticsFormData>({
  showPercent: modelValue.value.parameters?.showPercent ?? false,
  chartSize: modelValue.value.parameters?.chartSize ?? 1,
  type: modelValue.value.parameters?.type ?? chartTypes.doughnut,
  title:
    modelValue.value.parameters?.title ?? Translator.trans('u2.dashboard.widget.filter_statistics'),
  entries:
    modelValue.value.parameters?.entries.length > 0
      ? modelValue.value.parameters?.entries.map((entry) => ({ ...entry, id: UniqueID() }))
      : [
          {
            id: UniqueID(),
            savedFilterId: undefined,
            label: undefined,
            color: defaultColors[(modelValue.value.parameters?.entries.length ?? 0) + 1],
          },
        ],
})

const vuelidate = useVuelidate(
  {
    chartSize: { required: { ...required, $message: defaultValidationMessages.required() } },
    type: { required: { ...required, $message: defaultValidationMessages.required() } },
    title: { required: { ...required, $message: defaultValidationMessages.required() } },
    entries: {
      $each: helpers.forEach({
        savedFilterId: {
          required: { ...required, $message: defaultValidationMessages.required() },
        },
        color: {
          required: { ...required, $message: defaultValidationMessages.required() },
        },
      }),
    },
  },
  formData
)

const errors = computed(() => extractVuelidateErrors<FilterStatisticsFormData>(vuelidate.value))

const savedFiltersQuery = useSavedFilterAllQuery()
const savedFilterOptions = computed<Array<{ id: SavedFilter['id']; name: string }>>(() => {
  return savedFiltersQuery.items.value.map((savedFilter) => ({
    name: `#${savedFilter.id} ${savedFilter.name}`,
    id: savedFilter.id,
  }))
})

function add() {
  formData.value.entries = [
    ...formData.value.entries,
    {
      id: UniqueID(),
      savedFilterId: undefined,
      label: undefined,
      color: defaultColors[formData.value.entries.length + 1],
    },
  ]
}
const remove = (index: number) => {
  formData.value.entries = formData.value.entries.filter((_, i) => i !== index)
}

const isAddButtonDisabled = computed(() => modelValue.value.parameters?.entries.length >= 10)
const addButtonDisabledTooltip = computed(() =>
  isAddButtonDisabled.value
    ? Translator.trans('u2.widget.filter_statistic.new_entry_disabled_reason')
    : undefined
)

const chartSizeSelectOptions = computed(() => {
  return Object.entries(chartSizes).map(([, value]) => ({
    id: value,
    name: widgetOptionToReadableNameCallback[value](),
  }))
})
const chartTypeSelectOptions = computed(() => {
  return Object.entries(chartTypes).map(([, value]) => ({
    id: value,
    name: chartTypeToReadableNameCallback[value](),
  }))
})

watch(
  formData,
  (formDataNewValue) => {
    updateModelValue(formDataNewValue)
  },
  { deep: true }
)

onMounted(() => {
  updateModelValue(formData.value)
})
</script>

<template>
  <div class="grid grid-cols-1 gap-(--app-form-field-spacing)">
    <BaseInputText
      v-model="formData.title"
      :label="Translator.trans('u2.title')"
      :required="true"
      maxlength="120"
      :errors="errors.title"
      @blur="vuelidate.title.$touch()"
    />

    <BaseRadioGroup
      v-model="formData.chartSize"
      :required="true"
      horizontal
      :label="Translator.trans('u2.chart_size')"
      class="flex max-w-fit flex-col gap-2"
      :options="chartSizeSelectOptions"
      :errors="errors.chartSize"
    />

    <BaseToggle
      v-model="formData.showPercent"
      :required="true"
      horizontal
      :label="Translator.trans('u2.show_percent')"
      class="flex max-w-fit flex-col gap-2"
      :errors="errors.showPercent"
    />

    <BaseRadioGroup
      v-model="formData.type"
      class="w-full"
      :required="true"
      horizontal
      :label="Translator.trans('u2.chart_type')"
      :options="chartTypeSelectOptions"
      :errors="errors.type"
    />

    <div class="w-full gap-x-2">
      <div class="flex items-center justify-between border-b py-1">
        <h3 class="flex items-center space-x-2">
          <span>
            {{ Translator.trans('u2.saved_filters') }}
          </span>
        </h3>
        <div class="" @click.stop="">
          <ButtonNew
            button-style="text"
            :show-text="false"
            :disabled="disabled || isAddButtonDisabled"
            :tooltip="addButtonDisabledTooltip"
            @click="add"
          />
        </div>
      </div>
      <template v-for="(configurationEntry, key) in formData.entries" :key="configurationEntry.id">
        <div class="relative mt-2 border-2 border-dotted p-3 hover:bg-orange-100">
          <button
            type="button"
            class="text-action hover:text-action-darker absolute top-0 right-0 p-2"
            @click="remove(key)"
          >
            <SvgIcon icon="delete" />
          </button>
          <div class="align-center flex flex-row items-center">
            <BaseSelect
              v-model="configurationEntry.savedFilterId"
              class="w-full"
              :required="true"
              :label="Translator.trans('u2.saved_filter')"
              :options="savedFilterOptions"
              :errors="errors?.entries?.[key]?.savedFilterId"
            />
          </div>
          <div class="align-center flex flex-row">
            <BaseInputText
              v-model="configurationEntry.label"
              :label="Translator.trans('u2.label')"
              class="w-full"
              :help-tooltip="Translator.trans('u2.widget.filter_statistic.label_help_text')"
              :placeholder="
                configurationEntry.savedFilterId
                  ? savedFiltersQuery.items.value.find(
                      (element) => element.id === configurationEntry.savedFilterId
                    )?.name
                  : undefined
              "
              maxlength="120"
              :errors="errors?.entries?.[key]?.label"
            />
            <BaseColorPicker
              v-model="configurationEntry.color"
              class="max-w-full"
              :label="Translator.trans('u2.color')"
              :errors="errors?.entries?.[key]?.color"
            />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
