<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { computed, onMounted, ref, toRef } from 'vue'
import { integer, maxLength, minLength, required } from '@vuelidate/validators'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import AppMessage from '@js/components/AppMessage.vue'
import BaseCheckbox from '@js/components/form/BaseCheckbox.vue'
import BaseCheckboxGroup from '@js/components/form/BaseCheckboxGroup.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseRadioGroup from '@js/components/form/BaseRadioGroup.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import extractVuelidateErrors from '@js/helper/form/extractVuelidateErrors'
import FormErrors from '@js/components/form/FormErrors.vue'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import Translator from '@js/translator'
import useSavedFilterAllQuery from '@js/composable/useSavedFilterAllQuery'
import useWidgetConfiguration from '@js/composable/useWidgetConfiguration'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import type { FilterResultsWidget } from '@js/model/dashboard'
import type { SavedFilter } from '@js/model/saved-filter'
import type { TableColumn } from '@js/stores/task-list'

const props = defineProps<{
  disabled: boolean
  columns: Array<TableColumn>
}>()

const columns = toRef(props, 'columns')
const modelValue = defineModel<FilterResultsWidget>({
  required: true,
})

const { sizeSelectOptions } = useWidgetConfiguration(modelValue)

const disabled = toRef(props, 'disabled')
type FormData = FilterResultsWidget['parameters'] & {
  sortColumn: string | undefined
  sortDirection: string | undefined
}

const formData = ref<FormData>({
  ...modelValue.value.parameters,
  records_per_page: modelValue.value.parameters?.records_per_page ?? 5,
  size: modelValue.value.parameters?.size ?? 1,
  title:
    modelValue.value.parameters?.title ?? Translator.trans('u2.dashboard.widget.filter_results'),
  columns: [...(modelValue.value.parameters?.columns ?? [])],
  sortColumn: modelValue.value.parameters?.sort?.split(' ')[0] ?? 'ASC',
  sortDirection: modelValue.value.parameters?.sort?.split(' ')[1] ?? 'Id',
})

const invalidColumns = computed(() =>
  formData.value.columns.filter((column) => !columns.value.find((c) => c.id === column))
)

function removeInvalidColumns() {
  formData.value.columns = formData.value.columns.filter((column) =>
    columns.value.find((c) => c.id === column)
  )
  updateModelValue(formData.value)
}

const toggleOption = (id: string) => {
  // Find if id is in array of strings. If so remove it if not add it to it
  const index = formData.value.columns.indexOf(id)
  if (index !== -1) {
    formData.value.columns.splice(index, 1)
  } else {
    formData.value.columns.push(id)
  }

  updateModelValue(formData.value)
}

const sortColumnOptions = computed(() =>
  formData.value.columns.map((column) => ({
    id: column,
    name: column,
  }))
)

const savedFiltersQuery = useSavedFilterAllQuery()
const savedFilterOptions = computed<Array<{ id: SavedFilter['id']; name: string }>>(() => {
  return savedFiltersQuery.items.value.map((savedFilter) => ({
    name: `#${savedFilter.id} ${savedFilter.name}`,
    id: savedFilter.id,
  }))
})

const vuelidate = useVuelidate(
  {
    title: { required: { ...required, $message: defaultValidationMessages.required() } },
    sortDirection: { required: { ...required, $message: defaultValidationMessages.required() } },
    filter_id: {
      integer: { ...integer },
      required: { ...required, $message: defaultValidationMessages.required() },
    },
    columns: {
      maxLength: {
        ...maxLength(5),
        $message: defaultValidationMessages.arrayMaxLength(5),
      },
      minLength: {
        ...minLength(1),
        $message: defaultValidationMessages.arrayMinLength(1),
      },
    },
  },
  // @ts-expect-error We should not be forced to add a vuelidate entry for each property
  formData
)

const errors = computed(() => extractVuelidateErrors<FormData>(vuelidate.value))

const updateModelValue = (
  value: FilterResultsWidget['parameters'] & { sortColumn?: string; sortDirection?: string }
) => {
  vuelidate.value.$touch()

  modelValue.value = {
    ...modelValue.value,
    parameters: {
      ...value,
      columns: value.columns
        .map(
          (selectColumn) =>
            sortColumnOptions.value.find((availableColumn) => availableColumn.id === selectColumn)
              ?.id
        )
        .sort()
        .filter((column) => column !== undefined) as Array<string>,
      sort:
        value.sortColumn && value.columns.includes(value.sortColumn)
          ? `${value.sortColumn} ${value.sortDirection}`
          : undefined,
    },
  }
}

onMounted(() => {
  // Populate defaults set by the form up the chain
  updateModelValue(formData.value)
})
</script>

<template>
  <div class="grid grid-cols-1 gap-(--app-form-field-spacing)">
    <BaseInputText
      v-model="formData.title"
      :label="Translator.trans('u2.title')"
      :required="true"
      maxlength="120"
      :errors="errors.title"
      @update:model-value="
        updateModelValue({ ...formData, title: $event ? $event.toString() : '' })
      "
    />

    <BaseRadioGroup
      v-model="formData.size"
      :required="true"
      horizontal
      :label="Translator.trans('u2.size')"
      class="flex max-w-fit flex-col gap-2"
      :options="sizeSelectOptions"
      :errors="errors.size"
      @update:model-value="updateModelValue({ ...formData, size: $event ?? 1 })"
    />

    <BaseSelect
      v-model="formData.records_per_page"
      :required="true"
      horizontal
      :label="Translator.trans('u2.results_per_page')"
      class="flex max-w-fit flex-col gap-2"
      :options="[
        { id: 5, name: '5' },
        { id: 10, name: '10' },
      ]"
      :errors="errors.records_per_page"
      @update:model-value="
        updateModelValue({ ...formData, records_per_page: $event ? ($event as number) : 5 })
      "
    />

    <BaseSelect
      v-model="formData.filter_id"
      :required="true"
      class="w-96 max-w-full"
      :label="Translator.trans('u2.saved_filter')"
      :options="savedFilterOptions"
      :errors="errors.filter_id"
      @update:model-value="
        updateModelValue({ ...formData, filter_id: $event ? ($event as number) : undefined })
      "
    />

    <template v-if="formData.filter_id">
      <div class="w-full">
        <HeaderWithAction>
          {{ Translator.trans('u2.columns') }}
        </HeaderWithAction>
        <section class="mt-1 flex w-full flex-col gap-1">
          <AppMessage v-if="invalidColumns.length" type="warning">
            <div>
              <div>
                {{
                  Translator.trans(
                    'u2.dashboard.widget.filter_results.the_following_invalid_columns'
                  )
                }}
                <strong>{{ invalidColumns.join(', ') }}</strong
                >.
              </div>
              <div class="text-right">
                <ButtonBasic
                  class="align-right"
                  type="button"
                  color="action"
                  @click="removeInvalidColumns"
                >
                  {{
                    Translator.trans('u2.dashboard.widget.filter_results.remove_invalid_columns')
                  }}
                </ButtonBasic>
              </div>
            </div>
          </AppMessage>
          <BaseCheckboxGroup>
            <BaseCheckbox
              v-for="column in columns"
              :key="column.id"
              :disabled="disabled"
              :model-value="formData.columns.indexOf(column.id) !== -1"
              :label="column.name"
              @update:model-value="toggleOption(column.id)"
            />
          </BaseCheckboxGroup>
        </section>
        <FormErrors :errors="errors.columns" />
      </div>

      <div class="flex flex-col">
        <HeaderWithAction>
          {{ Translator.trans('u2.sort') }}
        </HeaderWithAction>
        <BaseSelect
          v-model="formData.sortColumn"
          :label="Translator.trans('u2.saved_filter.sort.column')"
          :options="sortColumnOptions"
          :errors="errors.sortColumn"
          class="mt-1"
          @update:model-value="updateModelValue({ ...formData, sortColumn: $event ?? undefined })"
        />
        <BaseSelect
          v-if="formData.sortColumn"
          v-model="formData.sortDirection"
          :required="true"
          :errors="errors.sortDirection"
          :label="Translator.trans('u2.saved_filter.sort.direction')"
          :options="[
            { id: 'ASC', name: 'ASC' },
            { id: 'DESC', name: 'DESC' },
          ]"
          @update:model-value="
            updateModelValue({ ...formData, sortDirection: $event ?? undefined })
          "
        />
      </div>
    </template>
  </div>
</template>
