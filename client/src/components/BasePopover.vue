<script setup lang="ts">
import { <PERSON>overArrow, <PERSON>overContent, PopoverPortal, PopoverRoot, PopoverTrigger } from 'reka-ui'

const { disabled = false } = defineProps<{
  disabled?: boolean
}>()

defineSlots<{
  default: (props: { disabled: boolean }) => unknown
  content: (props: { close: () => void }) => unknown
}>()

const open = defineModel<boolean>('open')

function hidePopover() {
  open.value = false
}

function updateOpen(newValue: boolean) {
  if (disabled) {
    return
  }

  open.value = newValue
}
</script>

<template>
  <PopoverRoot :open="open" @update:open="updateOpen">
    <PopoverTrigger as-child>
      <slot name="default" :disabled="disabled" />
    </PopoverTrigger>
    <PopoverPortal>
      <PopoverContent
        side="bottom"
        :side-offset="5"
        :trap-focus="true"
        class="overflow-hidden rounded-sm bg-white shadow-lg ring-1 ring-gray-200"
      >
        <slot name="content" :close="hidePopover" />
        <PopoverArrow class="fill-white stroke-gray-200" />
      </PopoverContent>
    </PopoverPortal>
  </PopoverRoot>
</template>
