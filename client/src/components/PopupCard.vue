<script setup lang="ts">
defineSlots<{
  default: () => unknown
  header: () => unknown
  context: () => unknown
  'header-title': () => unknown
  'header-chips'?: () => unknown
  'header-actions'?: () => unknown
}>()
</script>

<template>
  <div class="z-0 max-h-96 max-w-lg overflow-auto">
    <div class="isolate">
      <div class="sticky top-0 z-10 bg-white/90 ring-1 ring-gray-200 backdrop-blur-xs">
        <div class="px-2 py-5 sm:p-4">
          <slot name="header">
            <div class="space-y-2">
              <div class="flex items-center justify-between gap-5">
                <h3 v-if="!!$slots['header-title']">
                  <slot name="header-title" />
                </h3>
                <div v-if="!!$slots['header-actions']" class="space-x-1">
                  <slot name="header-actions" />
                </div>
              </div>
              <div v-if="!!$slots['header-chips']" class="flex flex-wrap items-center gap-1">
                <slot name="header-chips" />
              </div>
            </div>
          </slot>
        </div>
        <div v-if="!!$slots['context']" class="ring-gray-200">
          <slot name="context" />
        </div>
      </div>

      <div class="z-0 px-2 py-5 sm:px-4">
        <slot name="default" />
      </div>
    </div>
  </div>
</template>
