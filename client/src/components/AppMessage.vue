<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'

withDefaults(
  defineProps<{
    text?: string
    showIcon?: boolean
    type?: 'info' | 'warning'
  }>(),
  {
    text: '',
    type: 'info',
    showIcon: true,
  }
)
</script>

<template>
  <div
    :class="[
      type === 'info' ? 'bg-blue-100 text-blue-900' : 'bg-orange-100 text-orange-900',
      'rounded-md p-3 leading-normal',
    ]"
  >
    <div v-if="showIcon" class="flex gap-x-2">
      <div class="flex size-5 items-center justify-center">
        <SvgIcon
          :icon="type === 'info' ? 'info' : 'alert'"
          size="small"
          :class="[type === 'info' ? 'text-blue-500' : 'text-orange-500', 'shrink-0']"
        />
      </div>
      <slot>
        <span>{{ text }}</span>
      </slot>
    </div>
    <div v-else class="leading-normal">
      <slot>
        {{ text }}
      </slot>
    </div>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

kbd {
  background-color: theme('colors.action-transparent');
}
</style>
