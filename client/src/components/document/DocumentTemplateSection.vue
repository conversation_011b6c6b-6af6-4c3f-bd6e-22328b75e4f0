<script setup lang="ts">
import useForm from '@js/composable/useForm'
import { computed, toRefs } from 'vue'
import BaseDocumentSection from '@js/components/document/BaseDocumentSection.vue'
import DocumentSectionTitle from '@js/components/document/DocumentSectionTitle.vue'
import TwigComponent from '@js/components/form/TwigComponent.vue'
import Translator from '@js/translator'
import type { HierarchicalSection } from '@js/helper/document/transformSectionsToHierarchy'

const props = defineProps<{
  hierarchicalSection: HierarchicalSection
  isParentExcluded?: boolean
}>()

const { hierarchicalSection } = toRefs(props)

const isExcluded = computed(
  () => !props.hierarchicalSection.section.include || props.isParentExcluded
)

useForm({
  initialValues: {
    'document_section_form[name]': hierarchicalSection.value.section.name,
  },
})
</script>

<template>
  <div>
    <div class="relative">
      <BaseDocumentSection
        :id="'section-' + hierarchicalSection.section.id"
        :excluded="isExcluded"
        :level="hierarchicalSection.section.level > 6 ? 6 : hierarchicalSection.section.level"
      >
        <template #title>
          <DocumentSectionTitle
            ref="titleInput"
            name="document_section_form[name]"
            required
            :editable="false"
            :disabled="false"
            :aria-label="Translator.trans('u2_structureddocument.title')"
            :readonly="true"
            :errors="[]"
          >
            <template #numbering>
              {{ hierarchicalSection.tocId }}
            </template>
          </DocumentSectionTitle>
        </template>

        <template #default>
          <div class="relative -mt-1 pt-1">
            <TwigComponent :html="hierarchicalSection.renderedContent ?? ''" />
          </div>
        </template>
      </BaseDocumentSection>
    </div>

    <div>
      <DocumentTemplateSection
        v-for="section in hierarchicalSection.subHierarchicalSections"
        :key="section.section.id"
        :is-parent-excluded="isExcluded"
        :hierarchical-section="section"
      />
    </div>
  </div>
</template>
