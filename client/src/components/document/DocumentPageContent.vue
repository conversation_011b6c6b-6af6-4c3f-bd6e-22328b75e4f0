<script setup lang="ts"></script>

<template>
  <div class="document-page mx-auto mt-6">
    <div class="document-page-content print:mx-0 print:break-before-page">
      <slot />
    </div>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

.document-page {
  /*
  A close enough value to represent the width of the content in the document PDF.
  With this value the content should match as near as possible with the PDF.
  */
  --pdf-content-width: 45rem;

  /* The minimum margin width to ensure that the margin buttons are visible */
  --document-page-min-margin: theme('spacing.8');

  max-width: calc(
    var(--pdf-content-width) + (2 * var(--document-section-padding)) +
      (2 * var(--document-page-min-margin))
  );
}

.document-page-content {
  margin-left: var(--document-page-min-margin);
  margin-right: var(--document-page-min-margin);
}
</style>
