<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { computed, ref, toRef } from 'vue'
import { queries } from '@js/query'
import { useDocumentStore } from '@js/stores/document'
import InformationGrid from '@js/components/InformationGrid.vue'
import InformationGridRow from '@js/components/InformationGridRow.vue'
import { getIdFromIri } from '@js/utilities/api-resource'
import useUserQuery from '@js/composable/useUserQuery'
import AppDateTime from '@js/components/AppDateTime.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AttachmentList from '@js/components/file/AttachmentList.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import UserLabel from '@js/components/UserLabel.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'

const props = defineProps<{
  section: DocumentSection
}>()

const emit = defineEmits<(event: 'close') => void>()

const createdById = computed(() => {
  return createdBy.value?.id
})
const updatedById = computed(() => {
  return updatedBy.value?.id
})
function close() {
  emit('close')
}

const section = toRef(props, 'section')

const { data: sectionInfo, isLoading: loading } = useQuery(queries.document.sections(section))
const sectionIri = computed(() => {
  return sectionInfo.value?.['@id']
})
const documentStore = useDocumentStore()
const sectionNameWithNumber = computed(() => {
  return documentStore.buildSectionNameWithNumbering(props.section.id)
})
const { data: createdBy } = useUserQuery(() =>
  sectionInfo.value?.createdBy ? getIdFromIri(sectionInfo.value?.createdBy) : undefined
)
const { data: updatedBy } = useUserQuery(() =>
  sectionInfo.value?.updatedBy ? getIdFromIri(sectionInfo.value?.updatedBy) : undefined
)

const attachmentList = ref()
const attachmentsExpanded = computed(() => attachmentList.value.expandedCount > 0)
const attachmentCount = computed(() => attachmentList.value?.attachmentCount ?? 0)
</script>

<template>
  <AppDialog :title="sectionNameWithNumber" :loading="loading" class="max-w-11/12" @close="close">
    <InformationGrid v-if="sectionInfo" class="min-w-96">
      <!--Created-->
      <InformationGridRow :label="Translator.trans('u2_structureddocument.created')">
        <AppDateTime :date="sectionInfo.createdAt" :relative="true" />
        <UserLabel :user="createdById" class="ml-1" />
      </InformationGridRow>

      <!--Updated-->
      <InformationGridRow :label="Translator.trans('u2_structureddocument.updated')">
        <template v-if="sectionInfo.updatedAt">
          <AppDateTime :date="sectionInfo.updatedAt" :relative="true" />
          <UserLabel :user="updatedById" class="ml-1" />
        </template>

        <template v-else>
          {{ Translator.trans('u2_structureddocument.not_changed') }}
        </template>
      </InformationGridRow>

      <!--Editable-->
      <InformationGridRow :label="Translator.trans('u2.editable')">
        <SvgIcon
          size="small"
          :icon="sectionInfo.editable ? 'yes-ok' : 'no'"
          :class="`mr-1 ${sectionInfo.editable ? 'text-good' : 'text-bad'}`"
        />
        {{ sectionInfo.editable ? Translator.trans('u2.yes') : Translator.trans('u2.no') }}
      </InformationGridRow>

      <!--Required-->
      <InformationGridRow :label="Translator.trans('u2_structureddocument.required')">
        <SvgIcon
          size="small"
          :icon="sectionInfo.required ? 'yes-ok' : 'no'"
          :class="`mr-1 ${sectionInfo.required ? 'text-good' : 'text-bad'}`"
        />
        {{ sectionInfo.required ? Translator.trans('u2.yes') : Translator.trans('u2.no') }}
      </InformationGridRow>

      <!--Included-->
      <InformationGridRow :label="Translator.trans('u2_structureddocument.included')">
        <SvgIcon
          size="small"
          :icon="sectionInfo.include ? 'yes-ok' : 'no'"
          :class="`mr-1 ${sectionInfo.include ? 'text-good' : 'text-bad'}`"
        />
        {{ sectionInfo.include ? Translator.trans('u2.yes') : Translator.trans('u2.no') }}
      </InformationGridRow>
    </InformationGrid>

    <!--Attachments-->
    <div class="mt-4 w-full">
      <span
        class="inline-flex w-full items-center justify-between border-b border-gray-700 pb-2 font-bold"
      >
        <span>
          <SvgIcon icon="document" size="small" class="mr-1 text-gray-500" />
          {{ Translator.trans('u2_structureddocument.attachments') }}
        </span>

        <span v-if="attachmentCount > 0" class="inline-flex">
          <ButtonBasic
            :icon="attachmentsExpanded ? 'collapse' : 'expand'"
            :tooltip="
              attachmentsExpanded ? Translator.trans('u2.collapse') : Translator.trans('u2.expand')
            "
            @click="attachmentsExpanded ? attachmentList.collapseAll() : attachmentList.expandAll()"
          />
        </span>
      </span>
      <AttachmentList
        v-if="sectionIri"
        ref="attachmentList"
        :resource-iri="sectionIri"
        class="mt-2.5"
      />
    </div>
  </AppDialog>
</template>
