<script setup lang="ts">
withDefaults(
  defineProps<{
    level?: number
  }>(),
  {
    level: 1,
  }
)
</script>

<template>
  <component :is="`h${level}`" class="relative normal-case">
    <span v-if="!!$slots.before" class="absolute top-0 -left-9 flex h-lh w-1 print:hidden">
      <slot name="before" />
    </span>
    <slot name="title" />
    <span
      v-if="!!$slots.controls"
      class="document-section-controls absolute top-0 right-0 flex h-lh w-1 shrink-0 items-center font-normal print:hidden"
    >
      <slot name="controls" />
    </span>
  </component>
</template>
