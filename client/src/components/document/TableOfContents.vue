<script setup lang="ts">
import Translator from '@js/translator'
import { useDocumentStore } from '@js/stores/document'
import ScrollTo from '@js/utilities/scroll-to'
import type { DocumentSection } from '@js/model/document'
import { numberToRomanNumeral } from '@js/utilities/number-formatter'
import { nextTick } from 'vue'

defineProps<{
  collapsed: boolean
  sectionToTreePositionMap: Map<DocumentSection['id'], string>
}>()

const emit = defineEmits<(event: 'sectionClick', payload: DocumentSection) => void>()

const documentStore = useDocumentStore()
</script>

<template>
  <div
    v-if="sectionToTreePositionMap.size > 0"
    class="table-of-contents print:hidden"
    :class="{ 'collapsed print:hidden': collapsed }"
  >
    <ul>
      <li
        v-for="[sectionId, numbering] in sectionToTreePositionMap"
        :key="sectionId"
        :class="{
          excluded: numbering.includes('-'),
        }"
      >
        <template v-if="!(collapsed && (numbering.includes('-') || numbering.length > 2))">
          <a
            :class="[
              documentStore.isSectionEdited(documentStore.getSectionById(sectionId))
                ? 'text-action'
                : 'text-off-black',
            ]"
            :href="`#section-${sectionId}`"
            :title="documentStore.getSectionById(sectionId).name"
            @click="
              () => {
                emit('sectionClick', documentStore.getSectionById(sectionId))
                nextTick(() => ScrollTo.scrollTo(`#section-${sectionId}`))
              }
            "
          >
            <strong> {{ numbering }} </strong>
            <template v-if="!collapsed">{{
              documentStore.getSectionById(sectionId).name
            }}</template>
          </a>
        </template>
      </li>
      <li>
        <a
          class="text-off-black"
          href="#section-attachments"
          :title="Translator.trans('u2_core.attachments')"
          @click="ScrollTo.scrollTo('#section-attachments')"
        >
          {{ numberToRomanNumeral(1) }}
          <template v-if="!collapsed">{{ Translator.trans('u2_core.attachments') }}</template>
        </a>
      </li>
    </ul>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

.table-of-contents {
  > ul {
    width: calc(100% - 10px);
  }

  ul {
    list-style: none;
    padding: 0;
  }

  li {
    line-height: theme('lineHeight.normal');
    overflow: hidden;
    padding: 0;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.excluded,
    &.excluded a {
      color: theme('colors.gray.400');
      text-decoration: line-through;
    }
  }

  &.collapsed {
    > ul {
      text-align: left;
      width: 100%;
    }
  }
}

@media print {
  .table-of-contents {
    break-before: avoid !important;
    display: inline !important;

    li {
      display: block !important;

      &.excluded {
        display: none !important;
      }
    }
  }
}
</style>
