<script setup lang="ts">
import { useField } from 'vee-validate'
import { ref, toRef, watch } from 'vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import FormRow from '@js/components/form/FormRow.vue'

const props = withDefaults(
  defineProps<{
    disabled?: boolean
    editable?: boolean
    errors?: Array<string>
    name: string
    placeholder?: string
    readonly?: boolean
    required?: boolean
  }>(),
  {
    disabled: false,
    editable: false,
    errors: () => [],
    placeholder: undefined,
    readonly: false,
    required: false,
  }
)

const emit = defineEmits<(event: 'cancel' | 'click') => void>()

const { value: inputValue, errors } = useField<string>(() => props.name, undefined, {
  bails: false,
})

const text = ref(inputValue.value)
const editable = toRef(props, 'editable')
const shouldRerender = ref(0)
watch(inputValue, (value) => {
  if (editable.value) {
    return
  }
  text.value = value

  // We need to rerender the text if it is the same as modelValue. Otherwise, when cancelling edits the text is not reset
  shouldRerender.value++
})
const input = ref()
function focus() {
  input.value.focus()
}
defineExpose({ focus })
</script>

<template>
  <FormRow :required="required" :label="false" :errors="errors" class="p-0" @click="emit('click')">
    <slot name="numbering" />

    <span
      :key="shouldRerender"
      ref="input"
      :contenteditable="editable && !disabled && !readonly"
      :role="editable && !disabled && !readonly ? 'textbox' : undefined"
      :aria-label="inputValue"
      class="bg-inherit text-[length:inherit] leading-[inherit] focus-visible:outline-hidden"
      :class="{
        'bg-skin-disabled cursor-default text-gray-600': disabled,
        'cursor-default': readonly,
      }"
      :data-placeholder="placeholder"
      :data-proxy="name"
      @input="inputValue = ($event.target as HTMLElement).textContent ?? ''"
      @keydown.enter.prevent
      @keydown.esc="emit('cancel')"
      v-text="text"
    />

    <!-- Support for html forms-->
    <input
      v-model="inputValue"
      type="hidden"
      :name="name"
      :required="required"
      :disabled="disabled"
      data-proxy-field
    />

    <slot name="icon" />

    <template #errors>
      <FormErrors class="font-normal" :errors="errors" />
    </template>
  </FormRow>
</template>

<style scoped>
@reference "@css/app.css";

[contenteditable]:empty:not(:focus)::before {
  color: theme('colors.gray.400');
  content: attr(data-placeholder);
  font-weight: theme('fontWeight.normal');
}
</style>
