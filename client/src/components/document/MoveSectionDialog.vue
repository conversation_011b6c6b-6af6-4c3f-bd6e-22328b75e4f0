<script lang="ts" setup>
import { moveDocumentSection } from '@js/api/documentApi'
import FieldSelect from '@js/components/form/FieldSelect.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import useForm from '@js/composable/useForm'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { useNotificationsStore } from '@js/stores/notifications'
import { toTypedSchema } from '@vee-validate/zod'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import { computed, ref } from 'vue'
import AppDialog from '@js/components/AppDialog.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import { useDocumentStore } from '@js/stores/document'
import Translator from '@js/translator'
import type { DocumentSection as DocumentSectionModel } from '@js/model/document'
import { z } from 'zod'

const props = defineProps<{
  section: DocumentSectionModel
}>()

const emit = defineEmits<(event: 'close' | 'moved') => void>()

const sectionMoveForm = ref()

function onClose() {
  emit('close')
}

const documentStore = useDocumentStore()
const sectionNameWithNumbering = computed(() => {
  return documentStore.buildSectionNameWithNumbering(props.section)
})

const referenceSectionOptions = computed(() => {
  const section = documentStore.sections?.find((section) => section.id === props.section.id)
  if (!section) {
    return []
  }

  const subSectionIds = documentStore.getSubsectionIds(section.id)
  return documentStore.sections?.map((section) => ({
    id: section.id,
    name: documentStore.buildSectionNameWithNumbering(section),
    disabled:
      section.id === props.section.id ||
      subSectionIds.includes(section.id),
  }))
})

const { handleSubmit, setResponseErrors, unmappedErrors } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      'document_section_move_form[placement]': z.string().min(1),
      'document_section_move_form[referenceSection]': z.number().min(1),
    })
  ),
})
const { resolveNotification } = useHandleAxiosErrorResponse()
const saving = ref(false)
const loading = computed(() => saving.value)
const notificationsStore = useNotificationsStore()
const save = handleSubmit(async (values) => {
  saving.value = true
  try {
    const response = await moveDocumentSection({
      id: props.section.id,
      type: props.section['@type'],
      placement: values['document_section_move_form[placement]'],
      referenceSection: values['document_section_move_form[referenceSection]'],
    })
    if ('messages' in response.data) {
      notificationsStore.addByType(response.data.messages)
    }
    emit('moved')
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  } finally {
    saving.value = false
  }
})
</script>

<template>
  <AppDialog
    :title="
      Translator.trans('u2_structureddocument.move_given_section', {
        section_name: sectionNameWithNumbering,
      })
    "
    @close="onClose"
  >
    <form
      id="document_section_move_form"
      ref="sectionMoveForm"
      name="document_section_move_form"
      class="min-h-40 w-3xl max-w-full"
      @submit.prevent="save"
    >
      <FormErrors :errors="unmappedErrors" />

      <FieldSelect
        :disabled="loading"
        :options="[
          { id: 'after', name: Translator.trans('u2_structureddocument.after') },
          { id: 'before', name: Translator.trans('u2_structureddocument.before') },
          { id: 'subsection-of', name: Translator.trans('u2_structureddocument.subsection_of') },
        ]"
        name="document_section_move_form[placement]"
        :label="Translator.trans('u2_structureddocument.placement')"
        required
      />

      <FieldSelect
        :disabled="!referenceSectionOptions || loading"
        :options="referenceSectionOptions ?? []"
        name="document_section_move_form[referenceSection]"
        :label="Translator.trans('u2_structureddocument.section')"
        :placeholder="Translator.trans('u2_structureddocument.select_a_section')"
        required
      />
    </form>

    <template #buttons>
      <ButtonBasic :disabled="loading" @click="onClose">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonBasic
        :disabled="loading"
        button-style="solid"
        form="document_section_move_form"
        type="submit"
      >
        {{ Translator.trans('u2.save') }}
      </ButtonBasic>
    </template>
  </AppDialog>
</template>
