<script setup lang="ts">
import { downloadDocumentAttachments } from '@js/api/documentApi'
import { saveAs } from 'file-saver'
import { StatusCodes } from 'http-status-codes'
import { toRefs } from 'vue'
import { useDocumentStore } from '@js/stores/document'
import ButtonDownload from '@js/components/buttons/ButtonDownload.vue'
import BaseDocumentSection from '@js/components/document/BaseDocumentSection.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import AttachmentInfoButtonPopover from '@js/components/file/AttachmentInfoButtonPopover.vue'
import PrintContainer from '@js/components/PrintContainer.vue'
import { attachmentApi } from '@js/api/attachmentApi'
import { isUnrestrictedAttachment } from '@js/model/attachment'
import type { UnrestrictedAttachment } from '@js/model/attachment'
import type { TaskShortName } from '@js/model/task'
import { numberToRomanNumeral } from '@js/utilities/number-formatter'

const props = defineProps<{
  documentId: number
  shortName: TaskShortName
  readonly: boolean
}>()

const { documentId, shortName } = toRefs(props)
const { resolveNotification } = useHandleAxiosErrorResponse()

const documentStore = useDocumentStore()
async function downloadAttachments() {
  try {
    const { data } = await downloadDocumentAttachments({
      shortName: shortName.value,
      documentId: documentId.value,
    })

    saveAs(new Blob([data]), 'document-' + documentId.value + '-attachments.zip')
  } catch (error) {
    resolveNotification(error, async (response) => {
      const responseContent = JSON.parse(await response.data.text())
      if (responseContent.detail && response.status === StatusCodes.UNPROCESSABLE_ENTITY) {
        useNotificationsStore().addError(responseContent.detail)
        return true
      }
      return false
    })
  }
}

const downloadFile = (attachment: UnrestrictedAttachment) => {
  attachmentApi
    .downloadAttachment(attachment)
    .then(({ data }) => {
      saveAs(new Blob([data]), attachment.name)
    })
    .catch(resolveNotification)
}
</script>

<template>
  <BaseDocumentSection>
    <template #title>
      {{ numberToRomanNumeral(1) + '. ' + Translator.trans('u2_core.attachments') }}
    </template>

    <template #controls>
      <ButtonDownload
        :tooltip="
          Translator.trans('u2_structureddocument.download_all_accessible_attachments.help')
        "
        @click="downloadAttachments"
      />
    </template>

    <template #default>
      <ul>
        <li v-for="attachment in documentStore.attachments" :key="attachment.id">
          <span class="align-middle">
            <strong class="break-words">
              <a v-if="isUnrestrictedAttachment(attachment)" @click="downloadFile(attachment)">
                {{ attachment.name }}
              </a>
              <template v-else>
                {{ Translator.trans('u2_core.the_name_of_this_file_is_restricted') }}
              </template>
            </strong>
            <PrintContainer class="inline-block">
              <AttachmentInfoButtonPopover :attachment="attachment" class="inline align-middle" />
            </PrintContainer>
          </span>
          &mdash;
          <span class="align-middle">
            <em>
              {{
                documentStore.getSectionNumber(documentStore.sectionByAttachment.get(attachment)!.id)
              }}.
              {{ documentStore.sectionByAttachment.get(attachment)?.name }}
            </em>
          </span>
        </li>
      </ul>
    </template>
  </BaseDocumentSection>
</template>
