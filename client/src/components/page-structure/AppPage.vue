<script setup lang="ts"></script>

<template>
  <div
    id="content"
    class="main-content flex-auto px-(--main-content-padding) pt-0 pb-(--main-content-padding)"
  >
    <div class="sticky z-10 max-w-full bg-white sm:top-0">
      <slot name="header"></slot>
    </div>
    <section class="page-content flex-auto">
      <slot />
    </section>
  </div>
</template>

<style scoped>
.main-content {
  /* Make sure the main content does not go wider than the page when containing wide tables, the flex box model will expand it to fill the page as needed */
  width: 250px;
}

@media print {
  .main-content {
    margin: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    width: 100% !important;
  }
}
</style>
