<script setup lang="ts">
import { useCssVar, useElementBounding } from '@vueuse/core'
import { ref, watch } from 'vue'
import AppSkeleton from '@js/components/AppSkeleton.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'

withDefaults(
  defineProps<{
    title?: string
    showPageControlsPlaceholder?: boolean
  }>(),
  {
    title: '',
    showPageControlsPlaceholder: false,
  }
)

defineSlots<{
  default?: (props: Record<string, never>) => unknown
  title?: (props: Record<string, never>) => unknown
  breadcrumbs?: (props: Record<string, never>) => unknown
  more?: (props: Record<string, never>) => unknown
}>()

const pageHeader = ref()
const { height } = useElementBounding(pageHeader)
const cssVarHeight = useCssVar('--sticky-header-height')
// This is used to reset the "top" value for table sticky headers when the page header height changes, e.g. on window resize
watch(height, (newValue) => {
  cssVarHeight.value = `${newValue}px`
})
</script>

<template>
  <div>
    <section id="page-header" ref="pageHeader" class="page-header">
      <div>
        <div class="pt-3 pl-1">
          <slot name="breadcrumbs" />
        </div>

        <div
          class="m-0 flex w-full flex-wrap items-center gap-1 border-b border-gray-200 px-0 pb-3"
        >
          <div class="m-0 flex grow items-center">
            <slot name="title">
              <PageHeaderTitle :title="title" />
            </slot>
          </div>
          <div
            v-if="!!$slots.default"
            id="page-controls"
            class="page-header-block flex flex-wrap items-center gap-1.5 px-0 print:hidden"
          >
            <AppSkeleton v-if="showPageControlsPlaceholder" />
            <slot v-else />
          </div>
        </div>
      </div>
      <slot name="more" />
    </section>
  </div>
</template>

<style scoped>
@reference "@css/app.css";

.page-header {
  clear: both;
}

::v-deep(#page-controls) {
  .button:not([class*='colored-button'], :focus, :hover, :disabled) {
    color: theme('colors.off-black');
  }
}

@media print {
  .page-header {
    .structured-document & {
      display: none !important;
    }
  }
}
</style>
