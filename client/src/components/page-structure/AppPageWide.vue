<script setup lang="ts"></script>

<template>
  <div id="content" class="main-content max-w-full flex-auto">
    <div
      class="sticky left-0 z-20 w-[calc(100vw-var(--scrollbar-width))] max-w-full bg-white px-(--main-content-padding) sm:top-0 print:static print:w-11/12"
    >
      <slot name="header"></slot>
    </div>

    <div
      v-if="!!$slots.beforeWideContent"
      class="sticky left-0 z-10 mt-4 w-[calc(100vw-var(--scrollbar-width))] max-w-full bg-white px-(--main-content-padding)"
    >
      <slot name="beforeWideContent"> </slot>
    </div>
    <section
      class="page-content flex-auto px-(--main-content-padding) pb-(--main-content-padding) print:px-0"
    >
      <slot name="default" />
    </section>
  </div>
</template>
