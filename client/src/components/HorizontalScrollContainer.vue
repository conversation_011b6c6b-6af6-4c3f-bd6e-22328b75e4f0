<script setup lang="ts">
import { onUpdated, ref, watch } from 'vue'
import $ from 'jquery'
import { useElementBounding } from '@vueuse/core'

const emit = defineEmits<(event: 'containerScroll', payload: number) => void>()

const container = ref()
const isLeftShadowVisible = ref()
const isRightShadowVisible = ref()
const fixedContainer = ref()

const updateShadows = (scrollLeft: number) => {
  const overflowWidth =
    Math.floor(container.value?.scrollWidth) -
    Math.floor(container.value?.getBoundingClientRect().width)
  // 1px  difference can be ignored when we decide to show/hide the shadow
  const bias = 1
  isRightShadowVisible.value =
    scrollLeft - bias > overflowWidth || overflowWidth > scrollLeft + bias
  isLeftShadowVisible.value = container.value?.scrollLeft !== 0
}

function onUpdateScroll() {
  const scrollLeft = Math.floor(container.value?.scrollLeft)
  updateShadows(scrollLeft)
  emit('containerScroll', scrollLeft)
  // Trigger jquery scroll on the sticky table header
  $('.data-table-wrapper').trigger('scroll.stickyTableHeader')
}

const { height: fixedContainerHeight } = useElementBounding(fixedContainer)

onUpdated(() => {
  const scrollLeft = Math.floor(container.value?.scrollLeft)
  updateShadows(scrollLeft)
})

watch(fixedContainerHeight, () => {
  if (!(container.value?.style || fixedContainer.value?.style)) {
    return
  }
  container.value.style.removeProperty('height')
  fixedContainer.value.style.marginTop = ''
  if (fixedContainerHeight.value > 0) {
    const containerHeight = container.value.getBoundingClientRect().height
    fixedContainer.value.style.marginTop = `${containerHeight}px`
    container.value.style.height = `${containerHeight + fixedContainerHeight.value}px`
  }
})
</script>

<template>
  <div class="relative">
    <div ref="container" class="isolate overflow-x-auto" @scroll="onUpdateScroll">
      <slot />
    </div>
    <div ref="fixedContainer" class="pointer-events-none absolute top-0 w-full">
      <slot name="fixed"></slot>
    </div>
    <div v-if="isLeftShadowVisible" class="shadow-right absolute top-0 -left-3.5 h-full bg-white">
      <div class="w-3.5"></div>
    </div>
    <div v-if="isRightShadowVisible" class="shadow-left absolute top-0 -right-3.5 h-full bg-white">
      <div class="w-3.5"></div>
    </div>
  </div>
</template>

<style scoped>
.shadow-left {
  box-shadow: -5px 0 6px -3px rgb(0 0 0 / 10%);
}

.shadow-right {
  box-shadow: 5px 0 6px -3px rgb(0 0 0 / 10%);
}
</style>
