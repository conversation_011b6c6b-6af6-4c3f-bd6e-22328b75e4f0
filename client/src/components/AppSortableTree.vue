<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { computed, ref } from 'vue'
import DraggableList from '@js/components/DraggableList.vue'
import Translator from '@js/translator'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import AppDropZone from '@js/components/AppDropZone.vue'
import AppSortableTreeNode from '@js/components/AppSortableTreeNode.vue'
import type { SortableEvent } from 'sortablejs'
import type { TreeNode } from '@js/types'

const props = withDefaults(
  defineProps<{
    modelValue: Array<TreeNode>
    dragAndDropGroup?: string
    level?: number
    disabled?: boolean
    readonly?: boolean
  }>(),
  {
    level: 1,
    dragAndDropGroup: 'group',
    disabled: false,
    readonly: false,
  }
)

defineSlots<{ item?: (props: { item: TreeNode }) => unknown }>()
const emit = defineEmits<{
  (event: 'update:modelValue', payload: Array<TreeNode>): void
  (event: 'clickNode', payload: TreeNode): void
  (event: 'dragStart', payload: SortableEvent): void
}>()

const value = useVModel(props, 'modelValue', emit, {
  passive: true,
  deep: true,
})

const limitChunkSize = 100
const childLimit = ref(limitChunkSize)

const limitedValues = computed(() => {
  return value.value.slice(0, childLimit.value)
})

function onClickNode(item: TreeNode) {
  emit('clickNode', item)
}
function onDragStart(event: SortableEvent) {
  emit('dragStart', event)
}

const isDraggingDisabled = computed(() => props.readonly || props.disabled)
</script>

<template>
  <div class="relative">
    <DraggableList
      v-model="value"
      :group="dragAndDropGroup"
      :aria-disabled="disabled"
      :disabled="isDraggingDisabled"
      :swap-threshold="0.65"
      :animation="0"
      tag="ul"
      :role="$attrs.role ?? 'tree'"
      item-key="id"
      :class="[
        'm-0 h-full w-full list-none',
        modelValue?.length === 0 && level === 1 ? 'absolute -mt-2 -mr-6 px-6 py-4' : 'pl-0',
      ]"
      @start="onDragStart"
    >
      <AppSortableTreeNode
        v-for="item in limitedValues"
        :key="item.label"
        :level="level"
        :item="item"
        :group="dragAndDropGroup"
        :disabled="disabled"
        :readonly="readonly"
        :aria-expanded="!item.isCollapsed"
        :aria-label="item.label"
        @click-node="onClickNode"
        @drag-start="onDragStart"
      >
        <template #default="{ item: child }">
          <slot name="item" :item="child" />
        </template>
      </AppSortableTreeNode>
      <ButtonBasic
        v-if="value.length > limitedValues.length"
        @click="childLimit = childLimit + limitChunkSize"
      >
        + {{ Translator.trans('u2.number_more', { number: value.length - limitedValues.length }) }}
      </ButtonBasic>
    </DraggableList>
    <AppDropZone v-if="modelValue?.length === 0 && level === 1 && !readonly" />
  </div>
</template>
