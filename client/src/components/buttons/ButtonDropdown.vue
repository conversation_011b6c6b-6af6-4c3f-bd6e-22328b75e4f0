<script setup lang="ts">
import {
  DropdownMenuContent,
  DropdownMenuPortal,
  DropdownMenuRoot,
  DropdownMenuTrigger,
} from 'reka-ui'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import type { Color, Icon } from '@js/utilities/name-lists'

defineOptions({ inheritAttrs: false })

withDefaults(
  defineProps<{
    buttonStyle?: 'outlined' | 'solid' | 'text'
    color?: Color
    disabled?: boolean
    arrow?: boolean
    grouped?: boolean
    icon?: Icon
    tooltip?: string
    placement?: 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end'
  }>(),
  {
    buttonStyle: undefined,
    color: 'action',
    disabled: false,
    arrow: true,
    grouped: false,
    icon: undefined,
    tooltip: undefined,
    placement: 'bottom-start',
  }
)

const open = defineModel<boolean>('open')
</script>

<template>
  <DropdownMenuRoot v-model:open="open" :modal="false">
    <DropdownMenuTrigger
      :as="ButtonBasic"
      :color="color"
      class="component-button-dropdown"
      :button-style="buttonStyle"
      :disabled="disabled"
      :grouped="grouped"
      :icon="icon"
      :tooltip="tooltip"
      v-bind="$attrs"
    >
      <slot name="default" :open="open" />
      <SvgIcon
        v-if="arrow"
        class="transform transition duration-300 ease-in-out"
        :class="[{ 'rotate-180': !open }, { 'ml-1': !!$slots.default }]"
        icon="arrow-up"
      />
    </DropdownMenuTrigger>

    <DropdownMenuPortal>
      <DropdownMenuContent
        class="mt-1 min-w-full rounded-sm bg-white text-left whitespace-nowrap shadow-lg ring-1 ring-black/5 focus:outline-hidden"
      >
        <div class="inline-flex w-full flex-col p-1">
          <slot name="body" />
        </div>
      </DropdownMenuContent>
    </DropdownMenuPortal>
  </DropdownMenuRoot>
</template>
