<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'
import ButtonBase from '@js/components/buttons/ButtonBase.vue'
import type { Icon } from '@js/utilities/name-lists'

withDefaults(
  defineProps<{
    active?: boolean
    disabled?: boolean
    icon?: Icon
    to?: string | object
    tooltip?: string
  }>(),
  {
    active: false,
    disabled: false,
    icon: undefined,
    to: undefined,
    tooltip: undefined,
  }
)
const emit = defineEmits<(event: 'click', payload: MouseEvent) => void>()
</script>

<template>
  <ButtonBase
    :to="to"
    :disabled
    :tooltip
    class="dropdown-entry hover:bg-action block w-full rounded-xs border-none px-1 py-0 text-left leading-loose transition-colors hover:cursor-pointer hover:text-white hover:no-underline"
    :class="[
      active && 'bg-action text-white',
      disabled ? 'disabled text-gray-300' : 'text-gray-900',
    ]"
    role="menuitem"
    @click="emit('click', $event)"
  >
    <span
      class="dropdown-entry flex w-full items-center"
      :class="{ 'cursor-not-allowed bg-transparent text-gray-500': disabled }"
    >
      <SvgIcon v-if="icon" :icon="icon" class="mr-1 shrink-0" />
      <slot name="default" />
      <span class="ml-2 grow text-right text-gray-500">
        <slot name="shortcut" />
      </span>
    </span>
  </ButtonBase>
</template>
