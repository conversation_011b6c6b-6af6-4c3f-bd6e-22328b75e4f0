<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { computed, ref } from 'vue'
import DraggableHandle from '@js/components/DraggableHandle.vue'
import AppSortableTree from '@js/components/AppSortableTree.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import { vAnimate } from '@js/directives/animate'
import type { SortableEvent } from 'sortablejs'
import type { Ref } from 'vue'
import type { TreeNode } from '@js/types'

const props = withDefaults(
  defineProps<{
    item: TreeNode
    group: string
    level?: number
    disabled?: boolean
    readonly?: boolean
  }>(),
  {
    level: 1,
    dragAndDropGroup: 'group',
    disabled: false,
    readonly: false,
  }
)

defineSlots<{ default?: (props: { item: TreeNode }) => unknown }>()
const emit = defineEmits<{
  (event: 'update:modelValue', payload: { children: Array<TreeNode> }): void
  (event: 'clickNode', payload: TreeNode): void
  (event: 'dragStart', payload: SortableEvent): void
}>()

const item = useVModel(props, 'item', emit, {
  shouldEmit: () => false,
}) as Ref<TreeNode>

const hasChildren = computed(() => {
  return item.value.children.length > 0
})
const isEnabled = computed(() => {
  return !props.disabled && !props.readonly
})

function toggleCollapse() {
  item.value.isCollapsed = !item.value.isCollapsed
}

const toggleButton = ref()

// We need to set parent's background on the toggle button and nested lists to hide  unnecessary parts of connector lines
const parentBackgroundColor = computed(() => {
  let element = toggleButton.value
  while (element) {
    const style = window.getComputedStyle(element)
    if (style.backgroundColor !== 'rgba(0, 0, 0, 0)' && style.backgroundColor !== 'transparent') {
      return style.backgroundColor
    }
    element = element.parentElement
  }
  return 'transparent'
})
</script>

<template>
  <li
    :key="item.label"
    role="treeitem"
    :aria-label="item.label"
    :aria-disabled="disabled"
    :class="[
      'relative block max-w-fit list-none border-gray-300 last:border-transparent',
      level === 1 ? 'root-node' : 'tree-node',
      { 'ml-5': level === 1 && !hasChildren },
      { 'has-children': hasChildren },
    ]"
  >
    <div :class="[{ '-ml-5': hasChildren && level !== 1 }, 'item-container']">
      <DraggableHandle
        v-animate
        :disabled="!isEnabled"
        class="flex h-7 flex-1 items-center gap-x-1"
        @click="emit('clickNode', item)"
      >
        <button
          v-if="hasChildren"
          ref="toggleButton"
          type="button"
          class="toggle-button flex items-center"
          @click.stop="toggleCollapse"
        >
          <SvgIcon
            icon="arrow-right"
            :class="[
              'transform transition duration-300 ease-in-out',
              { 'rotate-90': !item.isCollapsed },
            ]"
          />
        </button>
        <slot :item="item">
          <div
            :class="[
              { 'text-gray-500': disabled },
              item.isSelected ? 'border-red-500' : 'border-transparent',
              'flex items-center gap-x-1 rounded-sm border-2 border-dotted px-0.5 leading-normal',
            ]"
          >
            <SvgIcon
              v-if="item.icon"
              role="img"
              :icon="item.icon"
              :class="disabled ? 'text-gray-500' : 'text-gray-600'"
            />
            <span
              :class="[
                'whitespace-nowrap',
                {
                  'item-content': level !== 1,
                  'with-children': hasChildren,
                },
              ]"
              >{{ item.label }}</span
            >
          </div>
        </slot>
      </DraggableHandle>

      <div v-animate class="nested-tree">
        <template v-if="!item.isCollapsed">
          <AppSortableTree
            v-model="item.children"
            :level="level + 1"
            :drag-and-drop-group="group"
            class="ml-10 w-full flex-none"
            :disabled="disabled"
            :readonly="readonly"
            role="group"
            :aria-expanded="!item.isCollapsed"
            @click-node="emit('clickNode', $event)"
            @drag-start="emit('dragStart', $event)"
          >
            <template #item="{ item: child }">
              <slot :item="child" />
            </template>
          </AppSortableTree>
        </template>
      </div>
    </div>
  </li>
</template>

<style scoped>
@reference "@css/app.css";

li .sortable-drag {
  border: 0;
}

.sortable-ghost button {
  display: none;
}

.sortable-chosen {
  cursor: grabbing;
}

.sortable-ghost .nested-tree {
  display: none;
}

.sortable-drag {
  .nested-tree {
    display: none;
  }

  .item-content::after,
  .item-content.with-children::after,
  .nested-tree::before {
    border: 0;
  }

  .toggle-button {
    background: transparent;
  }
}

.sortable-ghost:last-child {
  border: theme('borderWidth.2') theme('borderColor.gray.300') dashed;
}

.sortable-ghost {
  .item-content.with-children::after,
  .nested-tree::before,
  .item-content::after {
    border: 0;
  }

  .item-container {
    background: transparent !important;
  }

  .toggle-button {
    background: transparent;
  }
}

.item-content::after {
  border-bottom: 1px solid theme('colors.gray.400');
  content: '';
  display: block;
  height: 21px;
  left: -12px;
  position: absolute;
  top: -6px;
  width: theme('width[2.5]');
}

.item-content.with-children::after {
  border-left: 1px solid theme('colors.gray.400');
  height: 8px;
  left: 7px;
  top: -2px;
  width: theme('width.0');
}

.nested-tree::before {
  border-left: 1px solid theme('colors.gray.400');
  bottom: 0;
  content: '';
  display: block;
  left: 27px;
  margin-bottom: 13px;
  position: absolute;
  top: 0;
  width: theme('width.0');
}

.tree-node.has-children:last-child:not(.root-node) > .item-container {
  /* stylelint-disable value-keyword-case */
  background: v-bind(parentBackgroundColor);
}

.toggle-button {
  background: v-bind(parentBackgroundColor);
  /* stylelint-enable value-keyword-case */
}
</style>
