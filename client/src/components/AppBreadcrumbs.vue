<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
</script>

<template>
  <nav class="flex" aria-label="Breadcrumb">
    <div role="list" class="flex items-center gap-x-2">
      <ButtonBasic
        :to="{ name: 'AppDefaultDashboard' }"
        class="flex items-center text-gray-400 hover:text-gray-500"
      >
        <SvgIcon class="shrink-0" icon="home" />
        <span class="sr-only">Home</span>
      </ButtonBasic>
      <slot />
    </div>
  </nav>
</template>
