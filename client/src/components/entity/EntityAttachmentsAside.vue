<script setup lang="ts">
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import { useQueryClient } from '@tanstack/vue-query'
import { computed, ref, useTemplateRef } from 'vue'
import { queries } from '@js/query'
import AsideSection from '@js/components/AsideSection.vue'
import AttachmentList from '@js/components/file/AttachmentList.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import NewAttachmentDialog from '@js/components/file/NewAttachmentDialog.vue'
import Translator from '@js/translator'
import type { ApiResource } from '@js/types'

const { disabled = false, resource } = defineProps<{ resource: ApiResource; disabled?: boolean }>()

const attachmentList = useTemplateRef('attachmentList')

const attachmentCount = computed(() => attachmentList.value?.attachmentCount ?? 0)

const attachmentsExpanded = computed(() => (attachmentList.value?.expandedCount ?? 0) > 0)

const sortBy = ref<InstanceType<typeof AttachmentList>['sortBy']>('createdAt')

const isAttachmentDialogShown = ref(false)
const queryClient = useQueryClient()
function onFileAttached() {
  isAttachmentDialogShown.value = false
  queryClient.invalidateQueries({
    queryKey: queries.attachments.all(resource['@id']).queryKey,
  })
}
</script>

<template>
  <div>
    <AsideSection
      icon="document"
      :headline="`${Translator.trans('u2_core.attachments')} (${attachmentCount ?? 0})`"
    >
      <template #button>
        <ButtonDropdown>
          <template #default>Sort</template>
          <template #body>
            <ButtonDropdownItem @click="sortBy = 'createdAt'">{{
              Translator.trans('u2_core.created')
            }}</ButtonDropdownItem>
            <ButtonDropdownItem @click="sortBy = 'name'">{{
              Translator.trans('u2_core.name')
            }}</ButtonDropdownItem>
          </template>
        </ButtonDropdown>

        <ButtonBasic
          v-if="attachmentCount > 0"
          :icon="attachmentsExpanded ? 'collapse' : 'expand'"
          :tooltip="
            attachmentsExpanded ? Translator.trans('u2.collapse') : Translator.trans('u2.expand')
          "
          @click="attachmentsExpanded ? attachmentList?.collapseAll() : attachmentList?.expandAll()"
        />

        <ButtonNew
          id="new-attachment-dialog-trigger"
          button-style="text"
          :disabled="disabled"
          :show-text="false"
          :tooltip="Translator.trans('u2.add_attachment')"
          @click="isAttachmentDialogShown = true"
        />
      </template>

      <template #default>
        <AttachmentList ref="attachmentList" :resource-iri="resource['@id']" :sort-by="sortBy">
          <template #add-attachment>
            <ButtonBasic :disabled="disabled" @click="isAttachmentDialogShown = true">{{
              Translator.trans('u2.add_attachment')
            }}</ButtonBasic>
          </template>
        </AttachmentList>
      </template>
    </AsideSection>

    <NewAttachmentDialog
      v-if="isAttachmentDialogShown && resource"
      :resource="resource"
      @close="isAttachmentDialogShown = false"
      @file-attached="onFileAttached"
    />
  </div>
</template>
