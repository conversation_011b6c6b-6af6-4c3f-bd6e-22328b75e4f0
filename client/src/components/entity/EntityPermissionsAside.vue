<script setup lang="ts">
import { computed, ref, toRefs } from 'vue'
import fetchGroupPermissions from '@js/helper/task/fetchGroupPermissions'
import useUserGroupsQuery from '@js/composable/useUserGroupsQuery'
import fetchUserPermissions from '@js/helper/task/fetchUserPermissions'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import EditUserGroupPermissionsDialog from '@js/components/EditUserGroupPermissionsDialog.vue'
import EditUserPermissionsDialog from '@js/components/EditUserPermissionsDialog.vue'
import { PermissionMasks } from '@js/model/permission'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import AppMessage from '@js/components/AppMessage.vue'
import { isMaskSet, useMyPermission } from '@js/composable/useMyPermission'
import LabelBasic from '@js/components/LabelBasic.vue'
import UserLabel from '@js/components/UserLabel.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import useUserAllQuery from '@js/composable/useUserAllQuery'
import { vAnimate } from '@js/directives/animate'
import type { PermissionableTask } from '@js/model/task'
import type { GroupPermission, UserPermission } from '@js/model/permission'
import type { Icon } from '@js/utilities/name-lists'

const props = withDefaults(
  defineProps<{
    userHeadline?: string
    groupHeadline?: string
    resource: PermissionableTask
    editPermissionMask?: number
    isPublic?: boolean
  }>(),
  {
    userHeadline: () => Translator.trans('u2.user_permissions'),
    groupHeadline: () => Translator.trans('u2.group_permissions'),
    editPermissionMask: 0,
    isPublic: false,
  }
)

const isUserPermissionDialogOpen = ref(false)
const isGroupPermissionDialogOpen = ref(false)

const { allUsers, isLoading: isUserDataLoading } = useUserAllQuery()

const loading = ref(false)

const { resource } = toRefs(props)

const userPermissions = ref<Array<UserPermission>>([])
const groupPermissions = ref<Array<GroupPermission>>([])
const iHavePermission = useMyPermission(userPermissions, groupPermissions).iHavePermission

const { items: userGroups, isLoading: isUserGroupsDataLoading } = useUserGroupsQuery()
function getData() {
  loading.value = true
  Promise.all([fetchGroupPermissions(resource.value), fetchUserPermissions(resource.value)])
    .then(([groupPermissionsData, userPermissionsData]) => {
      groupPermissions.value = groupPermissionsData.data['hydra:member']
      userPermissions.value = userPermissionsData.data['hydra:member']
    })
    .finally(() => {
      loading.value = false
    })
}
const canEdit = computed(() => {
  return loading.value === false && iHavePermission(props.editPermissionMask)
})

const emit = defineEmits<(event: 'updated') => void>()

const permissionTitlesTranslations = {
  [PermissionMasks.VIEW]: Translator.trans('u2_core.permission_mask.1'),
  [PermissionMasks.EDIT]: Translator.trans('u2_core.permission_mask.4'),
  [PermissionMasks.DELETE]: Translator.trans('u2_core.permission_mask.8'),
  [PermissionMasks.MANAGE]: Translator.trans('u2_core.permission_mask.128'),
} as const

type PermissionData = {
  type: 'user' | 'group'
  sectionHeadline: string
  isPublicText: string
  editPermissionsText: string
  noPermissionsText: string
  icon: Icon
  permissions: Array<(UserPermission | GroupPermission) & { label: string }>
}

const userPermissionsData = computed(() => {
  return {
    type: 'user',
    sectionHeadline: props.userHeadline,
    isPublicText: Translator.trans('u2.user_permissions.record_is_public'),
    editPermissionsText: Translator.trans('u2.edit_user_permissions'),
    noPermissionsText: Translator.trans(
      'u2_core.no_permissions_assigned_with_given_permission_type',
      { permission_type: 'user' }
    ),
    icon: 'user',
    permissions: (userPermissions.value ?? []).map((permission: UserPermission) => {
      const userIri = permission.user
      const user = allUsers.value.find((user) => user['@id'] === userIri)

      return {
        ...permission,
        label: user?.username ?? '',
        userId: user?.id ?? undefined,
      }
    }),
  } satisfies PermissionData
})

const groupPermissionsData = computed(() => {
  return {
    type: 'group',
    sectionHeadline: props.groupHeadline,
    isPublicText: Translator.trans('u2.group_permissions.record_is_public'),
    editPermissionsText: Translator.trans('u2.edit_group_permissions'),
    noPermissionsText: Translator.trans(
      'u2_core.no_permissions_assigned_with_given_permission_type',
      {
        permission_type: 'group',
      }
    ),
    icon: 'users',
    permissions: (groupPermissions.value ?? []).map((permission: GroupPermission) => {
      const groupIri = permission.group
      return {
        ...permission,
        label: userGroups.value.find((userGroup) => userGroup['@id'] === groupIri)?.name ?? '',
      }
    }),
  } satisfies PermissionData
})

const noGroupPermissionsText = computed(() => {
  if (resource.value['@id'].includes('layouts')) {
    return Translator.trans('u2.datasheets.no_user_group_permissions_description')
  }
  if (resource.value['@id'].includes('layout-collections')) {
    return Translator.trans(
      'u2.datasheets.datasheet_collection.no_user_group_permissions_description'
    )
  }
  if (resource.value['@id'].includes('document-templates')) {
    return Translator.trans('u2.document_template.no_user_group_permissions_description')
  }
  return Translator.trans('u2.document.no_user_group_permissions_description')
})

const noUserPermissionsText = computed(() => {
  if (resource.value['@id'].includes('layouts')) {
    return Translator.trans('u2.datasheets.no_user_permissions_description')
  }
  if (resource.value['@id'].includes('layout-collections')) {
    return Translator.trans('u2.datasheets.datasheet_collection.no_user_permissions_description')
  }
  if (resource.value['@id'].includes('document-templates')) {
    return Translator.trans('u2.document_template.no_user_permissions_description')
  }
  return Translator.trans('u2.document.no_user_permissions_description')
})

const openDialog = (
  type: (typeof userPermissionsData.value)['type'] | (typeof groupPermissionsData.value)['type']
) => {
  if (type === 'user') {
    isUserPermissionDialogOpen.value = true
    return
  }
  isGroupPermissionDialogOpen.value = true
}

function refresh() {
  getData()
  isUserPermissionDialogOpen.value = false
  isGroupPermissionDialogOpen.value = false
  emit('updated')
}

getData()
</script>

<template>
  <AsideSection
    v-for="item in [userPermissionsData, groupPermissionsData]"
    :key="item.type"
    :icon="item.icon"
    :headline="item.sectionHeadline"
  >
    <template #button>
      <ButtonEdit
        :tooltip="item.editPermissionsText"
        :disabled="!canEdit"
        @click="openDialog(item.type)"
      />
    </template>
    <div v-animate>
      <AppMessage v-if="isPublic" class="mb-3" :text="item.isPublicText" />
    </div>
    <AppLoader v-if="loading || isUserDataLoading || isUserGroupsDataLoading" class="h-24" />
    <template v-else>
      <table v-if="item.permissions.length > 0" class="min-w-full">
        <colgroup>
          <col width="28%" />
          <col width="18%" />
          <col width="18%" />
          <col width="18%" />
          <col width="18%" />
        </colgroup>
        <thead>
          <tr class="border-b border-gray-200">
            <th></th>
            <th
              v-for="mask in permissionTitlesTranslations"
              :key="mask"
              class="form-label p-2 text-center"
            >
              {{ mask }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y">
          <tr v-for="permission in item.permissions" :key="permission['@id']">
            <td class="p-2 whitespace-nowrap" :data-testid="permission.label">
              <UserLabel v-if="'userId' in permission" :user="permission.userId" color="white" />
              <LabelBasic v-else> {{ permission.label }} </LabelBasic>
            </td>

            <td
              v-for="mask in PermissionMasks"
              :key="mask"
              class="p-2 text-center whitespace-nowrap"
            >
              <SvgIcon
                v-if="isMaskSet(mask, permission.mask)"
                icon="yes-ok"
                class="text-good align-text-top"
              />
            </td>
          </tr>
        </tbody>
      </table>
      <AppEmptyState v-else-if="!isPublic && item.type === 'group'">
        <template #title>{{ Translator.trans('u2.no_user_group_permissions') }}</template>
        <span>{{ noGroupPermissionsText }}</span>
        <template v-if="canEdit">
          {{ Translator.trans('u2.assign_user_group_permissions_admin') }}
        </template>
        <template v-else>{{ Translator.trans('u2.contact_admin') }}</template>
        <template v-if="canEdit" #action>
          <ButtonBasic @click="isGroupPermissionDialogOpen = true">
            {{ Translator.trans('u2.assign_user_group_permissions') }}
          </ButtonBasic>
        </template>
      </AppEmptyState>
      <AppEmptyState v-else-if="!isPublic">
        <template #title>{{ Translator.trans('u2.no_user_permissions') }}</template>
        <span>{{ noUserPermissionsText }}</span>
        <template v-if="canEdit">
          {{ Translator.trans('u2.assign_user_permissions_admin') }}
        </template>
        <template v-else>{{ Translator.trans('u2.contact_admin') }}</template>
        <template v-if="canEdit" #action>
          <ButtonBasic @click="isUserPermissionDialogOpen = true">
            {{ Translator.trans('u2.assign_user_permissions') }}
          </ButtonBasic>
        </template>
      </AppEmptyState>
    </template>
  </AsideSection>
  <EditUserPermissionsDialog
    v-if="isUserPermissionDialogOpen"
    :title="userPermissionsData.editPermissionsText"
    :resource="resource"
    @close="isUserPermissionDialogOpen = false"
    @saved="refresh"
  />

  <EditUserGroupPermissionsDialog
    v-if="isGroupPermissionDialogOpen"
    :title="groupPermissionsData.editPermissionsText"
    :resource="resource"
    @close="isGroupPermissionDialogOpen = false"
    @saved="refresh"
  />
</template>
