import tinyMCE from 'tinymce'
import 'tinymce/icons/default'
import 'tinymce/plugins/autoresize'
import 'tinymce/plugins/charmap'
import 'tinymce/plugins/code'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import 'tinymce/skins/ui/oxide/skin.css'
import 'tinymce/plugins/visualblocks'
/* Required TinyMCE components */
import 'tinymce/themes/silver/theme'
import 'tinymce/models/dom/model'
/* The default content CSS can be changed or replaced with appropriate CSS for the editor content. */
import contentCss from '@css/tinymce.css?url'
import type { Editor, RawEditorOptions } from 'tinymce'

const TinyMceConfig = (() => {
  const initialize = (config: RawEditorOptions = {}) => {
    const selector = 'textarea[data-js-wysiwyg-editor]'

    const getConfigOption = (
      option: keyof RawEditorOptions
    ): RawEditorOptions[keyof RawEditorOptions] | null => {
      if (option in config) {
        return config[option]
      }

      return null
    }

    const getConfiguration = () => {
      return {
        license_key: 'gpl',
        autoresize_overflow_padding: 0,
        branding: false,
        browser_spellcheck: false,
        custom_elements: '~widget',
        skin: false,
        content_css: contentCss,
        extended_valid_elements:
          'span[*],widget,svg[*],defs[*],pattern[*],desc[*],metadata[*],g[*],mask[*],path[*],line[*],marker[*],rect[*],circle[*],ellipse[*],polygon[*],polyline[*],linearGradient[*],radialGradient[*],stop[*],image[*],view[*],text[*],textPath[*],title[*],tspan[*],glyph[*],symbol[*],switch[*],use[*]',
        forced_root_block: 'p',
        image_dimensions: false,
        inline: false,
        max_height: 500,
        menubar: false,
        min_height: 400,
        mobile: {
          theme: 'silver',
        },
        plugins: (() => {
          let plugins = 'autoresize charmap link lists table visualblocks'
          plugins += getConfigOption('code_button') ? ' code' : ''
          plugins +=
            !getConfigOption('image_widget') && getConfigOption('image_button') ? ' image' : ''
          plugins += getConfigOption('media_button') ? ' media' : ''

          return plugins
        })(),
        relative_urls: false,
        selector,

        statusbar: false,
        style_formats: [
          {
            items: (() => {
              if (getConfigOption('reduced_header_styles')) {
                return [
                  { format: 'h3', title: 'Header 1' },
                  { format: 'h4', title: 'Header 2' },
                  { format: 'h5', title: 'Header 3' },
                  { format: 'h6', title: 'Header 4' },
                ]
              }

              return [
                { format: 'h1', title: 'Header 1' },
                { format: 'h2', title: 'Header 2' },
                { format: 'h3', title: 'Header 3' },
                { format: 'h4', title: 'Header 4' },
                { format: 'h5', title: 'Header 5' },
                { format: 'h6', title: 'Header 6' },
              ]
            })(),
            title: 'Headers',
          },
          {
            items: [
              { format: 'bold', icon: 'bold', title: 'Bold' },
              { format: 'italic', icon: 'italic', title: 'Italic' },
              { format: 'underline', icon: 'underline', title: 'Underline' },
              { format: 'strikethrough', icon: 'strikethrough', title: 'Strikethrough' },
              { format: 'superscript', icon: 'superscript', title: 'Superscript' },
              { format: 'subscript', icon: 'subscript', title: 'Subscript' },
              { format: 'code', icon: 'code', title: 'Code' },
            ],
            title: 'Text',
          },
          { format: 'p', title: 'Paragraph' },
        ],
        table_advtab: false,
        table_cell_class_list: [
          { title: 'Text', value: 'table-data-text' },
          { title: 'Number', value: 'table-data-number' },
          { title: 'Right Aligned', value: 'table-data-right-aligned' },
          { title: 'Other', value: 'table-data-other' },
        ],
        table_class_list: [
          { title: 'No borders', value: '' },
          { title: 'Data Table', value: 'data-table' },
          { title: 'Bordered', value: 'bordered-table horizontal vertical out' },
          { title: 'Bordered Horizontal', value: 'bordered-table horizontal' },
          { title: 'Bordered Vertical', value: 'bordered-table vertical' },
        ],
        toolbar1:
          'undo redo | styles visualblocks | bold italic underline forecolor backcolor removeformat | alignleft aligncenter alignright alignjustify',
        ...config,
        setup: (editor: Editor) => {
          if (config.setup) {
            config.setup(editor)
          }
          editor.on('change', () => {
            editor.save()
          })
        },
        toolbar2: (() => {
          let toolbar2 = 'paste pastetext | bullist numlist outdent indent | link unlink'
          toolbar2 += getConfigOption('image_button') ? ' image' : ''
          toolbar2 += getConfigOption('media_button') ? ' media' : ''
          if (getConfigOption('toolbar2')) {
            toolbar2 += ' ' + config.toolbar2
            return toolbar2
          }
          toolbar2 += ' table charmap'
          toolbar2 += getConfigOption('code_button') ? ' | code' : ''
          return toolbar2
        })(),
      }
    }

    return tinyMCE.init(getConfiguration())
  }
  return {
    initialize,
  }
})()

export default TinyMceConfig
