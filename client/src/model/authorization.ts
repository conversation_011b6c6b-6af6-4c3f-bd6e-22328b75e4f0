import type { ApiResource } from '@js/types'

export type Authorization = ApiResource & {
  id: number
  name: string
  item: string
  rights: Array<string>
}

export type AuthorizationProfile = ApiResource & {
  id: number
  name: string
  authorizations: Array<Authorization['@id']>
}

export type AuthorizationItem = ApiResource & {
  id: string
  rights: Array<string>
}

export function isAuthorization(entity: ApiResource): entity is Authorization {
  return entity['@type'] === 'Authorization'
}

export function isAuthorizationProfile(entity: ApiResource): entity is AuthorizationProfile {
  return entity['@type'] === 'AuthorizationProfile'
}

export const authorizationStrings = [
  'APM_TRANSACTION:READ',
  'CM_CONTRACT:READ',
  'IGT_IGT1_TRANSACTION:READ',
  'IGT_IGT2_TRANSACTION:READ',
  'IGT_IGT3_TRANSACTION:READ',
  'IGT_IGT4_TRANSACTION:READ',
  'IGT_IGT5_TRANSACTION:READ',
  'TAM_INCOME_TAX_PLANNING:READ',
  'TAM_LOSS_CARRY_FORWARD:READ',
  'TAM_TAX_ASSESSMENT_STATUS:READ',
  'TAM_TAX_AUDIT_RISK:READ',
  'TAM_TAX_CONSULTING_FEE:READ',
  'TAM_TAX_CREDIT:READ',
  'TAM_TAX_LITIGATION:READ',
  'TAM_TAX_RATE:READ',
  'TAM_TAX_RELEVANT_RESTRICTION:READ',
  'TAM_TRANSFER_PRICING:READ',
  'TCM_OTHER_DEADLINE:READ',
  'TCM_TAX_ASSESSMENT_MONITOR:READ',
  'TCM_TAX_AUTHORITY_AUDIT_OBJECTION:READ',
  'TCM_TAX_FILING_MONITOR:READ',
  'TPM_COUNTRY_BY_COUNTRY_REPORT:READ',
  'TPM_FINANCIAL_DATA:READ',
  'TPM_LOCAL_FILE:READ',
  'TPM_MAIN_BUSINESS_ACTIVITY:READ',
  'TPM_MASTER_FILE:READ',
  'TPM_TRANSACTION:READ',
  'UNIT_PERIOD:READ',
] as const

export type AuthorizationString = (typeof authorizationStrings)[number]
