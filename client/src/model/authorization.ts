import type { ApiResource } from '@js/types'

export type Authorization = ApiResource & {
  id: number
  name: string
  item: string
  rights: Array<string>
}

export type AuthorizationProfile = ApiResource & {
  id: number
  name: string
  authorizations: Array<Authorization['@id']>
}

export type AuthorizationItem = ApiResource & {
  id: string
  rights: Array<string>
}

export const authorizationItems = [
  'APM_TRANSACTION',
  'CM_CONTRACT',
  'IGT_IGT1_TRANSACTION',
  'IGT_IGT2_TRANSACTION',
  'IGT_IGT3_TRANSACTION',
  'IGT_IGT4_TRANSACTION',
  'IGT_IGT5_TRANSACTION',
  'TAM_INCOME_TAX_PLANNING',
  'TAM_LOSS_CARRY_FORWARD',
  'TAM_TAX_ASSESSMENT_STATUS',
  'TAM_TAX_AUDIT_RISK',
  'TAM_TAX_CONSULTING_FEE',
  'TAM_TAX_CREDIT',
  'TAM_TAX_LITIGATION',
  'TAM_TAX_RATE',
  'TAM_TAX_RELEVANT_RESTRICTION',
  'TAM_TRANSFER_PRICING',
  'TCM_OTHER_DEADLINE',
  'TCM_TAX_ASSESSMENT_MONITOR',
  'TCM_TAX_AUTHORITY_AUDIT_OBJECTION',
  'TCM_TAX_FILING_MONITOR',
  'TPM_COUNTRY_BY_COUNTRY_REPORT',
  'TPM_FINANCIAL_DATA',
  'TPM_LOCAL_FILE',
  'TPM_MAIN_BUSINESS_ACTIVITY',
  'TPM_MASTER_FILE',
  'TPM_TRANSACTION',
  'UNIT_PERIOD',
] as const
export const authorizationRights = [
  'CREATE',
  'READ',
  'UPDATE',
  'DELETE',
  'TRANSFER',
  'ASSIGN',
  'SUPERVISE',
] as const

type AuthorizationItemString = (typeof authorizationItems)[number]
type AuthorizationRightString = (typeof authorizationRights)[number]

export type AuthorizationString = `${AuthorizationItemString}:${AuthorizationRightString}`
