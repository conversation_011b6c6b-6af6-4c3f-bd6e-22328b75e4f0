import Translator from '@js/translator'
import type { ApiResource } from '@js/types'

export const roles = [
  'ROLE_USER',
  'ROLE_ADMIN',
  'ROLE_USER_GROUP_ADMIN',
  'ROLE_PERIOD_MANAGER',
  'R<PERSON><PERSON>_UNIT_MANAGER',
  'ROLE_API',
] as const

export type Role = (typeof roles)[number]

export type RoleResource = ApiResource & {
  name: Role
}
export const roleNameMap = {
  ROLE_API: () => Translator.trans('u2.security.roles.api'),
  ROLE_USER: () => Translator.trans('u2.security.roles.user'),
  ROLE_ADMIN: () => Translator.trans('u2.security.roles.admin'),
  ROLE_USER_GROUP_ADMIN: () => Translator.trans('u2.security.roles.user_group_admin'),
  ROLE_PERIOD_MANAGER: () => Translator.trans('u2.security.roles.period_manager'),
  <PERSON><PERSON><PERSON>_UNIT_MANAGER: () => Translator.trans('u2.security.roles.unit_manager'),
} as const
