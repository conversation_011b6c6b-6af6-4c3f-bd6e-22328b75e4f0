import { fetchUnitById } from '@js/api/unitApi'
import { useQueryClient } from '@tanstack/vue-query'
import BaseLayout from '@js/layouts/BaseLayout.vue'
import Translator from '@js/translator'
import { queries } from '@js/query'
import { logDeprecatedRouteUsage } from '@js/router/helpers'
import type { RouteLocation, RouteRecordRaw } from 'vue-router'

export default [
  {
    component: () => import('@js/pages/units/index.vue'),
    meta: {
      layout: BaseLayout,
      layoutFormat: 'wide',
      globalSearch: {
        icon: 'unit',
        name: () => Translator.trans('u2.unit.plural'),
      },
    },
    name: 'UnitList',
    path: '/units',
  },
  {
    component: () => import('@js/pages/units/[id].vue'),
    meta: {
      layout: BaseLayout,
      title: (params: { id: number }) => Translator.trans('u2.unit') + ` #${params.id}`,
    },
    name: 'UnitEdit',
    path: '/units/:id',
    props: (to: RouteLocation) => ({ unit: to.meta.unit }),
    beforeEnter: async (to: RouteLocation) => {
      const queryClient = useQueryClient()
      await Promise.all([
        fetchUnitById(Number(to.params.id)).then(async ({ data }) => {
          to.meta.unit = data
        }),
        queryClient.fetchQuery(queries.unitProperties.all),
      ])
    },
  },
  {
    component: () => import('@js/pages/units/new.vue'),
    meta: {
      auth: 'ROLE_UNIT_MANAGER',
      layout: BaseLayout,
    },
    name: 'UnitNew',
    path: '/units/new',
  },
  {
    name: 'UnitListLegacy',
    path: '/unit',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'UnitList',
      }
    },
  },
  {
    name: 'UnitNewLegacy',
    path: '/unit/new',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'UnitNew',
      }
    },
  },
  {
    name: 'UnitEditLegacy',
    path: '/unit/:id/edit',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'UnitEdit',
        params: {
          id: to.params.id,
        },
      }
    },
  },
] as Array<RouteRecordRaw>
