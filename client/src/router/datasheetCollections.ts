import ConfigurationDataLayout from '@js/layouts/ConfigurationDataLayout.vue'
import Translator from '@js/translator'
import BaseLayout from '@js/layouts/BaseLayout.vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import type { Period } from '@js/api/periodApi'
import type { SelectedView } from '@js/stores/datasheet-parameters'
import type { RouteLocation, RouteRecordRaw } from 'vue-router'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { Datasheet, Field } from '@js/model/datasheet'
import type { Unit } from '@js/model/unit'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'

export default [
  {
    component: () => import('@js/pages/configuration/datasheets/collections/[id].vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
    },
    name: 'DatasheetCollectionEdit',
    path: '/configuration/datasheets/collections/:id',
    props: (route: RouteLocation) => ({
      id: route.params.id,
    }),
  },

  {
    component: () => import('@js/pages/configuration/datasheets/collections/new.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
    },
    name: 'DatasheetCollectionNew',
    path: '/configuration/datasheets/collections/new',
  },
  {
    component: () => import('@js/pages/configuration/datasheets/collections/index.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      globalSearch: {
        icon: 'config',
        name: () => Translator.trans('u2.datasheets.datasheet_collection.plural'),
      },
    },
    name: 'DatasheetCollectionList',
    path: '/configuration/datasheets/collections',
  },

  {
    component: () => import('@js/pages/datasheets/breakdown/unit-hierarchy/by-unit.vue'),
    meta: {
      layout: BaseLayout,
      layoutFormat: 'wide',
    },
    name: 'DatasheetCollectionGroupViewBreakdown',
    path: '/datasheets/breakdown/unit-hierarchy/by-unit',
  },
  {
    component: () => import('@js/pages/datasheets/breakdown/unit-hierarchy/by-country.vue'),
    meta: {
      layout: BaseLayout,
      layoutFormat: 'wide',
    },
    name: 'ItemCountryReport',
    path: '/datasheets/breakdown/unit-hierarchy/by-country',
  },

  {
    component: () => import('@js/pages/datasheets/collections/[id]/sheets/[sheetId].vue'),
    meta: {
      layout: BaseLayout,
      layoutFormat: 'wide',
    },
    name: 'DatasheetCollectionSheetView',
    path: '/datasheets/collections/:id/sheets/:sheetId',
  },
  {
    component: () => import('@js/pages/datasheets/collections/[id]/index.vue'),
    name: 'DatasheetCollectionNoSheetSelected',
    path: '/datasheets/collections/:id',
    meta: {
      layout: BaseLayout,
    },
  },
] as Array<RouteRecordRaw>

export type DatasheetRouteParameters = {
  layoutCollectionId: DatasheetCollection['id']
  layoutId?: Datasheet['id']
  fieldId?: Field['id']
  unitId?: Unit['id'] | '' // Allow empty string to keep the parameter in the URL. `null` or `undefined` would cause the parameter to be removed.
  periodId?: Period['id']
  hierarchyId?: UnitHierarchy['id'] | '' // Allow empty string to keep the parameter in the URL. `null` or `undefined` would cause the parameter to be removed.
}

function resolveView(parmeters: DatasheetRouteParameters) {
  if ('unitId' in parmeters && parmeters.unitId) {
    return 'unit'
  }

  return 'hierarchyId' in parmeters ? 'group' : 'unit'
}

export const buildDatasheetRoute = (parameters: DatasheetRouteParameters) => {
  const layoutParamStore = useDatasheetParametersStore()

  const queryParameters = {
    field: parameters.fieldId ?? layoutParamStore.parameters.field,
    period: parameters.periodId ?? layoutParamStore.parameters.period,
  } as {
    period: Period['id']
    unit?: Unit['id'] | ''
    unitHierarchy?: UnitHierarchy['id'] | ''
    field?: Field['id']
  }

  const view = resolveView(parameters)
  if (view === 'unit') {
    queryParameters.unit = parameters.unitId ?? layoutParamStore.parameters.unit
  }

  if (view === 'group') {
    queryParameters.unitHierarchy =
      parameters.hierarchyId ?? layoutParamStore.parameters.unitHierarchy
  }

  if (parameters.layoutId === undefined) {
    return {
      name: 'DatasheetCollectionNoSheetSelected',
      params: {
        id: parameters.layoutCollectionId,
      },
      query: queryParameters,
    }
  }

  return {
    name: 'DatasheetCollectionSheetView',
    params: {
      id: parameters.layoutCollectionId,
      sheetId: parameters.layoutId,
    },
    query: queryParameters,
  }
}

export const buildNoLayoutSelectedRoute = (view?: SelectedView) => {
  const layoutParamStore = useDatasheetParametersStore()
  const queryParameters = {
    period: layoutParamStore.parameters.period,
  } as {
    period: Period['id']
    unit?: Unit['id'] | ''
    unitHierarchy?: UnitHierarchy['id'] | ''
    field?: Field['id']
  }

  if ((view ?? layoutParamStore.parameters.selectedView) === 'unit') {
    queryParameters.unit = layoutParamStore.parameters.unit
  }

  if ((view ?? layoutParamStore.parameters.selectedView) === 'group') {
    queryParameters.unitHierarchy = layoutParamStore.parameters.unitHierarchy
  }

  return {
    name: 'DatasheetCollectionNoSheetSelected',
    params: {
      id: layoutParamStore.parameters.layoutCollection,
    },
    query: queryParameters,
  }
}
