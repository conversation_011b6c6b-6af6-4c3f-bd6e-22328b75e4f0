import BaseLayout from '@js/layouts/BaseLayout.vue'
import { logDeprecatedRouteUsage } from '@js/router/helpers'
import Translator from '@js/translator'
import type { RouteLocation, RouteRecordRaw } from 'vue-router'

export default [
  {
    component: () => import('@js/pages/periods/[id]/index.vue'),
    meta: {
      auth: 'ROLE_PERIOD_MANAGER',
      layout: BaseLayout,
      title: (params: { id: number }) => {
        return `${Translator.trans('u2_core.period')} #${params.id}`
      },
    },
    name: 'PeriodEdit',
    path: '/periods/:id',
    props: (route: RouteLocation) => ({ id: +route.params.id }),
  },
  {
    component: () => import('@js/pages/periods/new.vue'),
    meta: {
      auth: 'ROLE_PERIOD_MANAGER',
      layout: BaseLayout,
    },
    name: 'PeriodNew',
    path: '/periods/new',
  },
  {
    component: () => import('@js/pages/periods/index.vue'),
    meta: {
      layout: BaseLayout,
      globalSearch: {
        icon: 'list',
        name: () => Translator.trans('u2.periods'),
      },
    },
    name: 'PeriodList',
    path: '/periods',
  },
  {
    component: () => import('@js/pages/periods/[id]/exchange-rates/[exchangeRateId].vue'),
    meta: {
      auth: 'ROLE_PERIOD_MANAGER',
      layout: BaseLayout,
      title: (params: { id: number }) => {
        return `${Translator.trans('u2_core.exchange_rate')} #${params.id}`
      },
    },
    name: 'ExchangeRateEdit',
    path: '/periods/:id/exchange-rates/:exchangeRateId',
    props: (route: RouteLocation) => ({
      id: +route.params.exchangeRateId,
      periodId: +route.params.id,
    }),
  },
  {
    component: () => import('@js/pages/periods/[id]/exchange-rates/new.vue'),
    meta: {
      auth: 'ROLE_PERIOD_MANAGER',
      layout: BaseLayout,
    },
    name: 'ExchangeRateNew',
    path: '/periods/:id/exchange-rates/new',
    props: (route: RouteLocation) => ({ periodId: +route.params.id }),
  },
  {
    name: 'PeriodListLegacy',
    path: '/period',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'PeriodList',
      }
    },
  },
  {
    name: 'PeriodNewLegacy',
    path: '/period/new',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'PeriodNew',
      }
    },
  },
  {
    name: 'PeriodEditLegacy',
    path: '/period/:id/edit',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'PeriodEdit',
        params: {
          id: to.params.id,
        },
      }
    },
  },
  {
    name: 'ExchangeRateEditLegacy',
    path: '/period/:periodId/exchange-rate/:id/edit',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'ExchangeRateEdit',
        params: {
          id: to.params.periodId,
          exchangeRateId: to.params.id,
        },
      }
    },
  },
  {
    name: 'ExchangeRateNewLegacy',
    path: '/period/:id/exchange-rate/new',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'ExchangeRateNew',
        params: {
          id: to.params.id,
        },
      }
    },
  },
] as Array<RouteRecordRaw>
