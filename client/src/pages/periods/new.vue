<script setup lang="ts">
import type { Period } from '@js/api/periodApi'
import { useHead } from '@vueuse/head'
import { useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PeriodEditor from '@js/components/period/PeriodEditor.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'

const periodEditor = useTemplateRef('periodEditor')

useHead({ title: Translator.trans('u2.new_period') })

const router = useRouter()
const notificationsStore = useNotificationsStore()

const onSave = (newPeriod: Period) => {
  notificationsStore.addSuccess(Translator.trans('u2_core.success'))
  router.push({ name: 'PeriodEdit', params: { id: newPeriod.id } })
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2.new_period')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'PeriodList' }"
          :tooltip="Translator.trans('u2_core.period_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave :state="periodEditor?.state" form="period" />
      </PageHeader>
    </template>

    <PeriodEditor ref="periodEditor" @saved="onSave" />
  </AppPageWithAside>
</template>
