<script setup lang="ts">
import { fetchPeriodsByQuery } from '@js/api/periodApi'
import type { Period } from '@js/api/periodApi'
import { computed, ref } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useHead } from '@vueuse/head'
import { watchDebounced } from '@vueuse/core'
import AppPage from '@js/components/page-structure/AppPage.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import { flattenObject } from '@js/utilities/flattenObject'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import usePeriodsAllQuery from '@js/composable/usePeriodsAllQuery'
import useTable from '@js/composable/useTable'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { ApiQuery } from '@js/composable/useTable'

const totalItems = ref(0)
useHead({ title: Translator.trans('u2.periods') })

const allPeriodsQuery = usePeriodsAllQuery()
const allPeriods = computed(() => allPeriodsQuery.items.value)

const items = ref<Array<Period>>([])
const fetchPeriods = async (query: ApiQuery) => {
  const { data } = await fetchPeriodsByQuery(query)
  items.value = data['hydra:member']
  totalItems.value = data['hydra:totalItems']
}

const periods = computed(() =>
  items.value.map((item) =>
    flattenObject({
      ...item,
      previousPeriod: allPeriods.value.find((period) => period['@id'] === item.previousPeriod),
    })
  )
)

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const {
  columns,
  query,
  changePage,
  changePageSize,
  fromLocationQuery,
  apiQuery,
  toLocationQuery,
  sort,
} = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.name'),
      id: 'name',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.description'),
      id: 'description',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.start_date'),
      id: 'startDate',
      type: 'date',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.end_date'),
      id: 'endDate',
      type: 'date',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.previous_period'),
      id: 'previousPeriod.name',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.closed'),
      id: 'closed',
      type: 'boolean',
    },
    {
      align: 'right',
      name: '',
      id: 'actions',
    },
  ],
  { sort: { startDate: 'DESC' }, filter: { search: '' } }
)
watchDebounced(
  query,
  (newValue) => {
    router.push({ query: toLocationQuery(newValue) })
  },
  { deep: true, debounce: 400 }
)

const newQuery = fromLocationQuery(route.query)
if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
  query.value = newQuery
}

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
try {
  await fetchPeriods(apiQuery.value)
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

onBeforeRouteUpdate(async (to) => {
  const newQuery = fromLocationQuery(to.query)
  if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
    query.value = newQuery
  }

  try {
    await fetchPeriods(apiQuery.value)
  } catch (error) {
    if (
      !isAxiosError(error) ||
      (error.response && !(await handleAxiosErrorResponse(error.response)))
    ) {
      throw error
    }
  }
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.period_management')">
        <ButtonNew
          :disabled="!authStore.hasRole('ROLE_PERIOD_MANAGER')"
          :to="{ name: 'PeriodNew' }"
          :tooltip="Translator.trans('u2_core.add_new_period')"
        />
      </PageHeader>
    </template>

    <AppSearch v-model="query.filter.search" class="w-96 max-w-full" />
    <AppTable
      :query="query"
      :headers="columns"
      :items="periods ?? []"
      :total-items="totalItems"
      horizontal-scroll
      @sort="sort"
      @page-change="changePage"
      @page-size-change="changePageSize"
    >
      <template #item-name="{ item }">
        <span class="font-bold">{{ item.name }}</span>
      </template>
      <template #item-actions="{ item }">
        <ButtonEdit
          :disabled="!authStore.hasRole('ROLE_PERIOD_MANAGER')"
          :to="{ name: 'PeriodEdit', params: { id: item.id } }"
          :tooltip="Translator.trans('u2_core.edit_given_period', { period: item.name })"
        />
      </template>
    </AppTable>
  </AppPage>
</template>
