<script setup lang="ts">
import invariant from 'tiny-invariant'
import { computed, ref, toRefs } from 'vue'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import { useUserSettingsStore } from '@js/stores/user-settings'
import AppLoader from '@js/components/loader/AppLoader.vue'
import usePeriodQuery from '@js/composable/usePeriodQuery'
import { useNotificationsStore } from '@js/stores/notifications'
import { exchangeRateApi } from '@js/api/exchangeRateApi'
import { useAuthStore } from '@js/stores/auth'
import useCurrencyQuery from '@js/composable/useCurrencyQuery'
import { getIdFromIri } from '@js/utilities/api-resource'
import useExchangeRateQuery from '@js/composable/useExchangeRateQuery'
import ExchangeRateEditor from '@js/components/exchange-rate/ExchangeRateEditor.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import CurrencyConversion from '@js/components/exchange-rate/CurrencyConversion.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import ExchangeRateInformation from '@js/components/exchange-rate/ExchangeRateInformation.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { formatFromTo } from '@js/utilities/number-formatter'

const props = defineProps<{
  periodId: number
  id: number
}>()

useHead({ title: () => `${Translator.trans('u2_core.exchange_rate')} #${props.id}` })

const isLoading = ref(false)
const authStore = useAuthStore()
const canManageExchangeRate = computed(() => {
  if (isPageLoading.value) {
    return false
  }
  if (period.value?.closed) {
    return false
  }
  return authStore.hasRole('ROLE_PERIOD_MANAGER')
})
const { id, periodId } = toRefs(props)
const { data: exchangeRate, isLoading: isExchangeRateLoading } = useExchangeRateQuery(id)
const inputCurrencyId = computed(() =>
  exchangeRate.value ? getIdFromIri(exchangeRate.value.inputCurrency) : undefined
)
const outputCurrencyId = computed(() =>
  exchangeRate.value ? getIdFromIri(exchangeRate.value.outputCurrency) : undefined
)
const { data: inputCurrency, isLoading: isInputCurrencyLoading } = useCurrencyQuery(inputCurrencyId)
const { data: outputCurrency, isLoading: isOutputCurrencyLoading } =
  useCurrencyQuery(outputCurrencyId)

const { data: period, isLoading: isPeriodLoading } = usePeriodQuery(periodId)

const createdBy = computed(() =>
  exchangeRate.value?.createdBy ? getIdFromIri(exchangeRate.value.createdBy) : undefined
)
const updatedBy = computed(() =>
  exchangeRate.value?.updatedBy ? getIdFromIri(exchangeRate.value.updatedBy) : undefined
)

const router = useRouter()

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()

const { resolveNotification } = useHandleAxiosErrorResponse()

async function deleteExchangeRate() {
  if (exchangeRate.value === undefined) {
    return
  }
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      invariant(exchangeRate.value.id, 'Exchange rate id is not defined')
      await exchangeRateApi.deleteExchangeRate(exchangeRate.value.id)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'PeriodEdit', params: { id: props.periodId } })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const inputCurrencyIsoCode = ref<string | undefined>(undefined)
const outputCurrencyIsoCode = ref<string | undefined>(undefined)
const directRate = ref<string | undefined>()

const conversionInputCurrencyIsoCode = computed(() => {
  if (inputCurrencyIsoCode.value === undefined) {
    return inputCurrency.value?.iso4217code ?? ''
  }
  return inputCurrencyIsoCode.value
})

const conversionOutputCurrencyIsoCode = computed(() => {
  if (outputCurrencyIsoCode.value === undefined) {
    return outputCurrency.value?.iso4217code ?? ''
  }
  return outputCurrencyIsoCode.value
})
const userSettingsStore = useUserSettingsStore()

function getFormattedDirectRate(directRate: string) {
  return userSettingsStore.locale === 'de'
    ? formatFromTo(directRate, '0,0.0000000000', 'en', 'de')
    : directRate
}

const conversionDirectRate = computed(() => {
  if (directRate.value) {
    return directRate.value
  }

  return exchangeRate.value ? getFormattedDirectRate(exchangeRate.value.directRate) : ''
})

const isPageLoading = computed(
  () =>
    isExchangeRateLoading.value ||
    isInputCurrencyLoading.value ||
    isOutputCurrencyLoading.value ||
    isPeriodLoading.value ||
    isLoading.value
)

const deleteButtonTooltip = computed(() => {
  if (period.value?.closed) {
    return Translator.trans('u2_core.entities_cannot_be_deleted_in_closed_period', {
      period_name: period.value.name,
    })
  }
  if (inputCurrency.value && outputCurrency.value) {
    return Translator.trans('u2_core.delete_exchange_rate_with_given_name', {
      exchange_rate_name: inputCurrency.value.iso4217code + '-' + outputCurrency.value.iso4217code,
    })
  }
  return undefined
})
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle :id="id" :title="Translator.trans('u2_core.exchange_rate')" />
        </template>

        <!-- The period id should be taken from the url when we use vue router -->
        <ButtonBasic :to="{ name: 'PeriodEdit', params: { id: periodId } }">
          {{ Translator.trans('u2_core.period') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonBasic
          icon="upload"
          :to="{ name: 'ImportStart', params: { configurationKeySlug: 'exchange-rate' } }"
          :tooltip="Translator.trans('u2_core.import_exchange_rates')"
        >
          {{ Translator.trans('u2.import.import') }}
        </ButtonBasic>

        <ButtonNew
          :disabled="!canManageExchangeRate"
          :to="{ name: 'ExchangeRateNew', params: { id: periodId } }"
          :tooltip="
            period?.closed
              ? Translator.trans('u2_core.entities_cannot_be_created_in_closed_period', {
                  period_name: period.name,
                })
              : Translator.trans('u2_core.add_new_exchange_rate')
          "
        />

        <ButtonSpacer />

        <ButtonDelete
          :disabled="!canManageExchangeRate"
          :tooltip="deleteButtonTooltip"
          @click="deleteExchangeRate"
        />

        <ButtonSave :disabled="isPageLoading || period?.closed" form="exchange_rate_form" />
      </PageHeader>
    </template>

    <template #asideBefore>
      <ExchangeRateInformation
        v-if="period"
        :period="period"
        :created-at="exchangeRate?.createdAt"
        :created-by-id="createdBy"
        :updated-at="exchangeRate?.updatedAt"
        :updated-by-id="updatedBy"
      />
    </template>
    <template #default>
      <ExchangeRateEditor
        v-if="exchangeRate && period"
        :exchange-rate="exchangeRate"
        :period="period['@id']"
        :disabled="period.closed"
        @loading="isLoading = $event"
        @input-currency-updated="inputCurrencyIsoCode = $event"
        @output-currency-updated="outputCurrencyIsoCode = $event"
        @direct-rate-updated="directRate = $event"
      />
      <AppLoader v-else class="mt-10" />
    </template>
    <template #asideAfter>
      <CurrencyConversion
        v-if="inputCurrency && outputCurrency"
        :input-currency="conversionInputCurrencyIsoCode"
        :output-currency="conversionOutputCurrencyIsoCode"
        :direct-rate="conversionDirectRate"
      />
    </template>
  </AppPageWithAside>
  <ConfirmationDialog v-if="isConfirmDeleteRevealed" @close="cancelDelete" @confirm="confirmDelete">
    {{
      Translator.trans('u2_core.delete_exchange_rate_with_given_name.confirmation', {
        exchange_rate_name: `${inputCurrency!.iso4217code}-${outputCurrency!.iso4217code}`,
      })
    }}
  </ConfirmationDialog>
</template>
