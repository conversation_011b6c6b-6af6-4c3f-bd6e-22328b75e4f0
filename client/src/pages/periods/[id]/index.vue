<script setup lang="ts">
import type { Period } from '@js/api/periodApi'
import AsideSection from '@js/components/AsideSection.vue'
import PeriodAuditLogs from '@js/components/period/PeriodAuditLogs.vue'
import { computed, ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import * as PeriodApi from '@js/api/periodApi'
import AppLoader from '@js/components/loader/AppLoader.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import ExchangeRateList from '@js/components/exchange-rate/ExchangeRateList.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PeriodEditor from '@js/components/period/PeriodEditor.vue'
import PeriodInformation from '@js/components/period/PeriodInformation.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{
  id: number
}>()

useHead({ title: () => `${Translator.trans('u2_core.period')} #${props.id}` })

const isCurrentUserAdmin = computed(() => {
  return useAuthStore().hasRole('ROLE_ADMIN')
})

const authStore = useAuthStore()
const canWrite = computed(() => {
  return authStore.hasRole('ROLE_PERIOD_MANAGER')
})

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()

const notificationsStore = useNotificationsStore()
const { resolveNotification } = useHandleAxiosErrorResponse()

async function deletePeriod() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await PeriodApi.deletePeriod(props.id)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await router.push({ name: 'PeriodList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const router = useRouter()
const period = ref<Period>()

const { id } = toRefs(props)
function onSave(updatedPeriod: Period) {
  notificationsStore.addSuccess(Translator.trans('u2_core.success'))
  period.value = updatedPeriod
}

const periodEditor = useTemplateRef<typeof PeriodEditor>('periodEditor')

const { data } = await PeriodApi.fetchPeriodById(id.value)
period.value = data

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    try {
      period.value = (await PeriodApi.fetchPeriodById(Number(to.params.id))).data
    } catch (error) {
      if (
        !isAxiosError(error) ||
        (error.response && !(await handleAxiosErrorResponse(error.response)))
      ) {
        throw error
      }
    }
  }
})
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle :id="id" :title="Translator.trans('u2_core.period')" />
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'PeriodList' }"
          :tooltip="Translator.trans('u2_core.period_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :disabled="!canWrite"
          :to="{ name: 'PeriodNew' }"
          :tooltip="Translator.trans('u2_core.add_new_period')"
        />

        <ButtonSpacer />

        <ButtonSave :state="periodEditor?.state" form="period" />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              icon="delete"
              :text="Translator.trans('u2.delete')"
              :tooltip="
                Translator.trans('u2_core.delete_given_entity_type', {
                  entity_type_name: Translator.trans('u2_core.period'),
                })
              "
              :disabled="!canWrite"
              @click="deletePeriod"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #asideBefore>
      <PeriodInformation v-if="period" :period="period" />
    </template>
    <template #default>
      <AppLoader v-if="!period" />
      <PeriodEditor v-else ref="periodEditor" :period="period" @saved="onSave" />

      <ExchangeRateList v-if="period" class="mt-10 max-w-5xl" :period="period" />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{ Translator.trans('u2_core.delete_entry.confirmation') }}
      </ConfirmationDialog>
    </template>
    <template #asideAfter>
      <AsideSection
        v-if="isCurrentUserAdmin"
        icon="history"
        :headline="Translator.trans('u2_core.changes')"
        :collapsed="true"
        unmount-on-hide
      >
        <PeriodAuditLogs v-if="period" :period="period" />
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
