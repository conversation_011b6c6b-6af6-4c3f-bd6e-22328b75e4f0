<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'
import { onBeforeRouteUpdate, useRoute } from 'vue-router'
import { queries } from '@js/query'
import AppPage from '@js/components/page-structure/AppPage.vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import UnitViewToolbar from '@js/components/datasheet/UnitViewToolbar.vue'
import GroupViewToolbar from '@js/components/datasheet/GroupViewToolbar.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import Translator from '@js/translator'
import useLayoutCollectionAssignedLayoutsQuery from '@js/composable/useLayoutCollectionAssignedLayoutsQuery'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import type { DatasheetRouteParameters } from '@js/router/datasheetCollections'
import useImmediateRouteQuery from '@js/composable/useImmediateRouteQuery'
import { numberToString, stringToNumber } from '@js/utilities/data-transformers'

const route = useRoute()

const layoutParamStore = useDatasheetParametersStore()
layoutParamStore.parameters.layoutCollection = String(route.params.id)
layoutParamStore.parameters.layout = undefined

layoutParamStore.updateFromLocationQuery(route.query)
onBeforeRouteUpdate((to) => {
  layoutParamStore.updateFromLocationQuery(to.query)
})

const { data: layoutCollection } = useQuery(
  queries.datasheetCollections.single(layoutParamStore.parameters.layoutCollection)
)

const layoutCollectionAssignedLayoutsQuery = useLayoutCollectionAssignedLayoutsQuery(
  () => layoutParamStore.parameters.layoutCollection
)
const layouts = computed(() => layoutCollectionAssignedLayoutsQuery.items.value)

const { data: itemCountryReportsData } = useQuery(
  queries.datasheetCollections.single(layoutParamStore.parameters.layoutCollection)._ctx
    .itemCountryReports
)

const itemCountryReports = computed(() => itemCountryReportsData.value?.['hydra:member'] ?? [])

const showUnitView = computed(() => {
  return 'unit' === layoutParamStore.parameters.selectedView
})

useImmediateRouteQuery('period', layoutParamStore.parameters.period, {
  get: stringToNumber,
  set: numberToString,
})

useImmediateRouteQuery('unit', showUnitView.value ? layoutParamStore.parameters.unit : undefined, {
  get: stringToNumber,
  set: numberToString,
})

useImmediateRouteQuery(
  'unitHierarchy',
  !showUnitView.value ? layoutParamStore.parameters.unitHierarchy : undefined,
  {
    get: stringToNumber,
    set: numberToString,
  }
)

const layoutOptionsGrouped = computed<
  Array<{ name: string; layouts: Array<{ name: string; to: object }> }>
>(() => {
  return Object.values(
    layouts.value.reduce(
      (grouped, layout) => {
        const groupIndex = grouped.findIndex((entry) => entry.name === layout.group)
        const routeParameter = {
          layoutCollectionId: layoutParamStore.parameters.layoutCollection,
          layoutId: layout.id,
        } as DatasheetRouteParameters

        if (showUnitView.value) {
          routeParameter.unitId = route.query.unit ? Number(route.query.unit) : undefined
        } else {
          routeParameter.hierarchyId = route.query.unitHierarchy
            ? Number(route.query.unitHierarchy)
            : undefined
        }

        if (groupIndex !== -1) {
          grouped[groupIndex].layouts.push({
            name: layout.name,
            to: buildDatasheetRoute(routeParameter),
          })

          return grouped
        }

        grouped.push({
          name: layout.group,
          layouts: [
            {
              name: layout.name,
              to: buildDatasheetRoute(routeParameter),
            },
          ],
        })

        return grouped
      },
      [] as Array<{ name: string; layouts: Array<{ name: string; to: object }> }>
    )
  )
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader v-if="layoutCollection">
        <template #title>
          <PageHeaderTitle :title="layoutCollection.name" />
        </template>

        <template #more>
          <UnitViewToolbar v-if="showUnitView" :layout-collection-id="layoutCollection.id" />
          <GroupViewToolbar
            v-else
            :layout-collection-id="layoutCollection.id"
            :editable="true"
            :has-unit-view-switch="true"
          />
        </template>
      </PageHeader>
    </template>

    <div class="mx-auto w-96 max-w-full">
      <div class="mt-5">
        <h3>{{ Translator.trans('u2.datasheets.choose_a_datasheet') }}</h3>
        <div class="list-group">
          <template v-for="layoutGroup in layoutOptionsGrouped" :key="layoutGroup.name">
            <dt class="px-4 py-2">
              <span class="font-bold text-gray-700">{{ layoutGroup.name }}</span>
            </dt>
            <router-link
              v-for="layoutData in layoutGroup.layouts"
              :key="layoutData.name"
              :to="layoutData.to"
              class="list-group-item flex items-center justify-between"
            >
              {{ layoutData.name }}
            </router-link>
          </template>
        </div>
      </div>

      <div v-if="itemCountryReports.length > 0 && !('unit' in route.query)" class="mt-5">
        <h3>{{ Translator.trans('u2.datasheets.choose_an_item_country_report') }}</h3>
        <div class="list-group w-96 max-w-full">
          <template v-for="report in itemCountryReports" :key="report['@id']">
            <router-link
              :to="{
                name: 'ItemCountryReport',
                query: {
                  period: layoutParamStore.parameters.period,
                  unitHierarchy: layoutParamStore.parameters.unitHierarchy,
                  layoutCollection: layoutParamStore.parameters.layoutCollection,
                  report: report.id,
                },
              }"
              class="list-group-item flex items-center justify-between"
            >
              {{ report.name }}
            </router-link>
          </template>
        </div>
      </div>
    </div>
  </AppPage>
</template>
