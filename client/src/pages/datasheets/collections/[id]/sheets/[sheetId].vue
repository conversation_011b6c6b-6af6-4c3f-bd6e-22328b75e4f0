<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { computed, watch } from 'vue'
import DatasheetCollectionGroupView from '@js/components/datasheet/DatasheetCollectionGroupView.vue'
import DatasheetCollectionUnitView from '@js/components/datasheet/DatasheetCollectionUnitView.vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import useImmediateRouteQuery from '@js/composable/useImmediateRouteQuery'
import { numberToString, stringToNumber } from '@js/utilities/data-transformers'
import useLayoutCollectionQuery from '@js/composable/useLayoutCollectionQuery'
import { useRouteQuery } from '@vueuse/router'

const route = useRoute()

const datasheetParametersStore = useDatasheetParametersStore()
datasheetParametersStore.updateFromLocationQuery(route.query)
watch(
  () => route.query,
  () => {
    datasheetParametersStore.updateFromLocationQuery(route.query)
  }
)

const datasheetCollectionId = computed(() => route.params.id as string)

const { data: sheetCollection, suspense } = useLayoutCollectionQuery(datasheetCollectionId)

await suspense()
if (sheetCollection.value === undefined) {
  useRouter().push({
    name: 'Error404',
    params: { pathMatch: route.path.substring(1).split('/') },
    query: route.query,
    hash: route.hash,
  })
}

const sheetId = computed(() => Number(route.params.sheetId))
datasheetParametersStore.parameters.layout = sheetId.value
datasheetParametersStore.parameters.layoutCollection = String(datasheetCollectionId.value)

useImmediateRouteQuery('period', datasheetParametersStore.parameters.period, {
  get: stringToNumber,
  set: numberToString,
})

const fieldId = useRouteQuery<string, number | null | undefined>('field', undefined, {
  transform(value) {
    return value ? Number(value) : null
  },
})

datasheetParametersStore.parameters.field = fieldId.value ? Number(fieldId.value) : undefined

watch(
  () => datasheetParametersStore.parameters.field,
  (newFieldId) => {
    if (newFieldId === fieldId.value) {
      return
    }

    fieldId.value = newFieldId
  }
)
</script>

<template>
  <DatasheetCollectionUnitView v-if="datasheetParametersStore.parameters.selectedView === 'unit'" />
  <DatasheetCollectionGroupView v-else />
</template>
