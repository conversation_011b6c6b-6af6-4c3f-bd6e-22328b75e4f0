<script setup lang="ts">
import type { Period } from '@js/api/periodApi'
import InfoBox from '@js/components/InfoBox.vue'
import type { LayoutItem } from '@js/model/datasheet'
import { useQueries, useQuery } from '@tanstack/vue-query'
import { useHead } from '@vueuse/head'
import { isAxiosError } from 'axios'
import { StatusCodes } from 'http-status-codes'
import invariant from 'tiny-invariant'
import { computed, ref, toValue, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useRouteQuery } from '@vueuse/router'
import { queries } from '@js/query'
import YesNo from '@js/components/YesNo.vue'
import { fetchItemCountryBreakdownData } from '@js/api/itemCountryBreakdownApi'
import { fetchItemCountryReport } from '@js/api/itemCountryReportApi'
import GroupViewToolbar from '@js/components/datasheet/GroupViewToolbar.vue'
import DatasheetItemLabel from '@js/components/datasheet/DatasheetItemLabel.vue'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import AppTable from '@js/components/table/AppTable.vue'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import useCurrencyQuery from '@js/composable/useCurrencyQuery'
import usePeriodQuery from '@js/composable/usePeriodQuery'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import useUnitHierarchyQuery from '@js/composable/useUnitHierarchyQuery'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import Translator from '@js/translator'
import { getIdFromIri } from '@js/utilities/api-resource'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import type { ItemCountryReport } from '@js/api/itemCountryReportApi'
import type { CountryBreakdownData, CountryBreakdownValue } from '@js/api/itemCountryBreakdownApi'
import type { RouteLocation } from 'vue-router'
import type { TableHeader } from '@js/types'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'
import useDatasheetHierarchyValueFormatters from '@js/composable/datasheet/useDatasheetHierarchyValueFormatters'

const route = useRoute()

const layoutParamStore = useDatasheetParametersStore()

function updateLayoutParamStoreFromRoute(route: RouteLocation) {
  layoutParamStore.parameters.layoutCollection = String(route.query.layoutCollection)
  layoutParamStore.parameters.layout = Number(route.query.layout) || undefined
  layoutParamStore.parameters.period = Number(route.query.period) || undefined
  layoutParamStore.parameters.unitHierarchy = Number(route.query.unitHierarchy) || undefined
}
updateLayoutParamStoreFromRoute(route)

watch(route, (newRoute) => {
  updateLayoutParamStoreFromRoute(newRoute)
})

const reportId = computed(() => route.query.report as string)

const { data: itemCountryReport, suspense } = useQuery({
  queryKey: ['itemCountryReport', reportId],
  queryFn: () => {
    return fetchItemCountryReport(toValue(reportId)).then((response) => {
      return response.data
    })
  },
})

await suspense()
const { countriesByIri } = useCountriesAllQuery()

useHead({
  title: () =>
    `${itemCountryReport.value?.name ?? ''} - ${Translator.trans('u2.datasheets.item_country_report')}`,
})

const periodId = useRouteQuery<string, number | undefined>('period', undefined, {
  transform: (value) => (value ? Number(value) : undefined),
})
invariant(periodId.value)
const { data: period } = usePeriodQuery(periodId)

const hierarchyId = useRouteQuery<string, number | undefined>('unitHierarchy', undefined, {
  transform: (value) => (value ? Number(value) : undefined),
})
invariant(hierarchyId.value)
const { data: unitHierarchy } = useUnitHierarchyQuery(hierarchyId)

const systemSettingsAllQuery = useSystemSettingsAllQuery()
const { data: groupCurrency } = useCurrencyQuery(() => {
  const id = systemSettingsAllQuery.systemSettings.value?.applicationCurrency
  return id ? getIdFromIri(id) : undefined
})

const layoutQueryDefinitions = computed(() => {
  if (!itemCountryReport.value) {
    return []
  }

  return itemCountryReport.value.items.map((itemIri) => {
    const itemId = getIdFromIri(itemIri)
    return {
      queryKey: ['groupItemCountryBreakdown', itemId, periodId, hierarchyId],
      queryFn: () => {
        invariant(periodId.value && hierarchyId.value)
        return fetchItemCountryBreakdownData(itemId, periodId.value, hierarchyId.value).then(
          (response) => {
            return response.data
          }
        )
      },
    }
  })
})
const countryBreakdownQueries = useQueries({ queries: layoutQueryDefinitions })

const countryBreakdownDataCollection = computed(() => {
  return countryBreakdownQueries.value
    .filter((query) => !query.isLoading)
    .map((query) => {
      return query.data
    })
})
const dataError = computed(() => {
  return countryBreakdownQueries.value.find((query) => query.error)?.error
})

const itemQueryDefinitions = computed(() => {
  if (!itemCountryReport.value) {
    return []
  }

  return itemCountryReport.value.items.map((itemIri) => {
    const itemId = ref(getIdFromIri(itemIri))
    return queries.items.single(itemId)
  })
})

const itemQueries = useQueries({ queries: itemQueryDefinitions })

const itemsByIri = computed(() => {
  const items = itemQueries.value
    .filter((query): query is (typeof itemQueries.value)[number] => !query.isLoading)
    .map((query): LayoutItem => {
      invariant(query.data)
      return query.data
    })
  return new Map(items.map((item) => [item['@id'], item]))
})

const headers = computed<Array<TableHeader>>(() => {
  return [
    {
      id: 'country',
      name: 'country',
    },
    ...(itemCountryReport.value?.items.map((itemIri) => {
      return {
        id: itemIri,
        name: itemIri,
        type: 'layoutitem',
        align: 'right',
      } satisfies TableHeader
    }) ?? []),
  ]
})

type CountryData = {
  country: string
  [itemIri: string]: boolean | string | null
}
function processData(report: ItemCountryReport) {
  if (!countryBreakdownDataCollection.value) {
    return []
  }
  const countryDataMap: Record<string, CountryData> = {}
  const groupValues: Record<string, CountryBreakdownData['groupValue']> = {}
  report.items?.forEach((itemIri) => {
    const itemData = countryBreakdownDataCollection.value.find((data) => data?.item === itemIri)
    if (!itemData || itemData.breakdownValues.length === 0) {
      return
    }

    itemData.breakdownValues.forEach((breakdownValue) => {
      const countryIri = breakdownValue.country
      if (!countryDataMap[countryIri]) {
        countryDataMap[countryIri] = { country: countryIri }
      }
      countryDataMap[countryIri][itemIri] = formatValue(breakdownValue)
    })

    /*
      We want the same formatting for the group values as for the single values. Therefore,
      fake it as one of them and format it accordingly.
     */
    groupValues[itemIri] = formatValue({
      ...itemData.breakdownValues[0],
      value: itemData.groupValue,
    } as CountryBreakdownValue)
  })

  const result = Object.values(countryDataMap)
  const groupValueRow = { country: 'Total', ...groupValues }
  result.push(groupValueRow)
  return result
}

const datasheetHierarchyValueFormatters = useDatasheetHierarchyValueFormatters()
function formatValue(countryBreakdownValue: CountryBreakdownValue) {
  switch (countryBreakdownValue['@type']) {
    case 'CountryCheckboxValue':
      return datasheetHierarchyValueFormatters.transformCheckboxValue(countryBreakdownValue.value)
    case 'CountryDiffValue':
    case 'CountryMoneyValue':
      return datasheetHierarchyValueFormatters.transformMoneyValue(
        countryBreakdownValue.value,
        groupCurrency.value?.scale
      )
    case 'CountryPercentValue':
      return (
        datasheetHierarchyValueFormatters.transformPercentValue(countryBreakdownValue.value) +
        (countryBreakdownValue.value ? ' %' : '')
      )
    case 'CountryNumberValue':
      return datasheetHierarchyValueFormatters.transformCommentValue(countryBreakdownValue.value)
    case 'CountryTextValue':
      return datasheetHierarchyValueFormatters.transformCommentValue(countryBreakdownValue.value)
    default:
      throw new Error(`Unknown country breakdown value type: ${countryBreakdownValue['@type']}`)
  }
}

const data = computed(() => {
  if (
    !itemCountryReport.value ||
    !groupCurrency.value ||
    countryBreakdownDataCollection.value.length === 0
  ) {
    return undefined
  }
  return processData(itemCountryReport.value)
})

function isNavigationContextValid(navigationContext: {
  unitHierarchyId?: UnitHierarchy['id']
  periodId?: Period['id']
}) {
  return !!navigationContext.unitHierarchyId && !!navigationContext.periodId
}
</script>

<template>
  <AppPageWide
    v-if="layoutParamStore.parameters.layoutCollection && countryBreakdownDataCollection"
    :key="`${unitHierarchy?.id ?? 'unitHierarchy'}-${period?.id ?? 'period'}`"
  >
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle
            :title="Translator.trans('u2.datasheets.item_country_report')"
            :subtitle="itemCountryReport?.name"
          />
        </template>
      </PageHeader>

      <GroupViewToolbar
        :layout-collection-id="layoutParamStore.parameters.layoutCollection"
        :editable="true"
        :has-unit-view-switch="false"
      />
    </template>

    <template v-if="dataError">
      <InfoBox
        v-if="isAxiosError(dataError) && dataError.response?.status === StatusCodes.FORBIDDEN"
        icon="blocked"
        :title="Translator.trans('u2.access_denied')"
      />
      <InfoBox v-else icon="alert" :title="Translator.trans('u2.error')" />
    </template>
    <template v-else-if="data">
      <AppTable :headers="headers" :items="data">
        <template #itemtype-layoutitem-header="{ header }">
          <div class="flex flex-col gap-2 text-center whitespace-normal">
            <span>
              {{ itemsByIri.get(header.name as string)?.name }}
            </span>
            <DatasheetItemLabel
              class="text-left leading-normal"
              :item="getIdFromIri(header.name as string)"
              :context="{
                unitHierarchyId: hierarchyId,
                periodId: periodId,
              }"
            >
              <template
                #popupcard-dropdown-extra="{
                  item: itemFromDropdownSlot,
                  context: contextFromDropdownSlot,
                }"
              >
                <ButtonDropdownItem
                  icon="breakdown"
                  :text="Translator.trans('u2.breakdown')"
                  :disabled="!isNavigationContextValid(contextFromDropdownSlot)"
                  :tooltip="
                    !isNavigationContextValid(contextFromDropdownSlot)
                      ? Translator.trans('u2.inspect.insufficient_context')
                      : undefined
                  "
                  :to="{
                    name: 'DatasheetCollectionGroupViewBreakdown',
                    query: {
                      item: itemFromDropdownSlot.id,
                      layoutCollection: layoutParamStore.parameters.layoutCollection,
                      layout: layoutParamStore.parameters.layout,
                      period: contextFromDropdownSlot.periodId,
                      unitHierarchy:
                        'unitHierarchyId' in contextFromDropdownSlot
                          ? contextFromDropdownSlot.unitHierarchyId
                          : undefined,
                    },
                  }"
                />
              </template>
            </DatasheetItemLabel>
          </div>
        </template>
        <template #item-country="{ item }">
          {{ countriesByIri.get(item.country)?.nameShort ?? item.country }}
        </template>
        <template #itemtype-layoutitem="{ value }">
          <span v-if="value === null">{{ Translator.trans('u2.n_a') }}</span>
          <YesNo v-else-if="typeof value === 'boolean'" :value="value" />
          <span v-else>{{ value }}</span>
        </template>
      </AppTable>
    </template>
  </AppPageWide>
</template>
