<script lang="ts" setup>
import { deleteDatasheetById } from '@js/api/datasheetApi'
import { computed, ref, toRefs, useTemplateRef } from 'vue'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import { useQueryClient } from '@tanstack/vue-query'
import { saveAs } from 'file-saver'
import { StatusCodes } from 'http-status-codes'
import EntityPermissionsAside from '@js/components/entity/EntityPermissionsAside.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import InformationAsideSection from '@js/components/InformationAsideSection.vue'
import InformationGridRow from '@js/components/InformationGridRow.vue'
import UserLabel from '@js/components/UserLabel.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import { getIdFromIri } from '@js/utilities/api-resource'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import useLayoutQuery from '@js/composable/useLayoutQuery'
import { queries } from '@js/query'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import FieldEditor from '@js/components/FieldEditor.vue'
import AppDialog from '@js/components/AppDialog.vue'
import DatasheetEditor from '@js/components/datasheet/DatasheetEditor.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import RenderedDatasheetTemplate from '@js/components/RenderedDatasheetTemplate.vue'
import DatasheetUnassignedFieldsAsideSection from '@js/components/datasheet/DatasheetUnassignedFieldsAsideSection.vue'
import AppMessage from '@js/components/AppMessage.vue'
import ButtonDownload from '@js/components/buttons/ButtonDownload.vue'
import * as DatasheetApi from '@js/api/datasheetApi'
import DatasheetTemplateEditor from '@js/components/datasheet/DatasheetTemplateEditor.vue'
import type {
  DatasheetUnitHierarchyViewNavigationContext,
  DatasheetUnitViewNavigationContext,
} from '@js/model/datasheet'
import useDatasheetTemplateWithFields from '@js/composable/datasheet/useDatasheetTemplateWithFields'

const props = defineProps<{
  id: number
}>()

const { id } = toRefs(props)
const { data: datasheet } = useLayoutQuery(id)
useHead({ title: computed(() => datasheet.value?.name + ' #' + id.value) })

const queryClient = useQueryClient()

const router = useRouter()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()

const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()

const datasheetEditorRef =
  useTemplateRef<InstanceType<typeof DatasheetEditor>>('datasheetEditorRef')
const showPublicVisibilityInfo = computed(() => datasheetEditorRef.value?.isLayoutPublic)
const loading = computed(
  () =>
    datasheetEditorRef.value?.state === 'saving' ||
    datasheetTemplateEditorRef.value?.state === 'saving'
)

async function deleteLayout() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await deleteDatasheetById(id.value)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'DatasheetList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const notificationStore = useNotificationsStore()

function invalidateQueries() {
  queryClient.invalidateQueries({
    queryKey: queries.datasheets.single(id.value).queryKey,
  })
}

const showFieldDialog = ref(false)
const fieldEditor = useTemplateRef<InstanceType<typeof FieldEditor>>('fieldEditor')
const isSaving = computed(() => fieldEditor.value?.state === 'saving')
const selectedField = ref()

function closeFieldDialog() {
  selectedField.value = undefined
  showFieldDialog.value = false
}

try {
  await queryClient.fetchQuery(queries.datasheets.single(id))
} catch (error) {
  if (!isAxiosError(error) || (error.response && !handleAxiosErrorResponse(error.response))) {
    throw error
  }
}

const invalidateMenuQuery = () => {
  queryClient.invalidateQueries({ queryKey: queries.menu.mainMenuJson.queryKey })
}

const datasheetParameterStore = useDatasheetParametersStore()

const datasheetHasTemplate = computed(() => {
  return !isTemplateLoading.value && !isTemplateError.value
})

const isLoading = computed(() => isTemplateLoading.value || isFieldsLoading.value)

const {
  isTemplateError,
  isTemplateLoading,
  datasheetTemplateHtml,
  fieldsNotReferencedInTemplate,
  missingFields,
  isFieldsLoading,
  fields,
} = useDatasheetTemplateWithFields(id)

const datasheetNavigationContext = computed(() => {
  if (datasheetParameterStore.parameters.selectedView === 'group') {
    return {
      periodId: datasheetParameterStore.parameters.period,
      unitHierarchyId: datasheetParameterStore.parameters.unitHierarchy,
    } satisfies DatasheetUnitHierarchyViewNavigationContext
  }

  return {
    unitId: datasheetParameterStore.parameters.unit,
    periodId: datasheetParameterStore.parameters.period,
  } satisfies DatasheetUnitViewNavigationContext
})

// Template logic

const showDeleteTemplateDialog = ref(false)
const showFileInput = ref(false)

const notificationsStore = useNotificationsStore()
const deleteTemplate = async () => {
  if (!datasheet.value) {
    return
  }

  try {
    await DatasheetApi.deleteDatasheetTemplate(id.value)
    notificationsStore.add({
      message: Translator.trans('u2.delete_successful'),
      type: 'success',
    })
    invalidateQueries()
  } catch (error) {
    await resolveNotification(error)
    notificationsStore.add({
      message: Translator.trans('u2.delete_unsuccessful'),
      type: 'error',
    })
  } finally {
    showDeleteTemplateDialog.value = false
  }
}

const downloadTemplate = async () => {
  if (!datasheet.value) {
    return
  }

  DatasheetApi.downloadDatasheetTemplate(id.value)
    .then(({ data }) => {
      saveAs(new Blob([data]), datasheet.value.name + '.txt')
    })
    .catch((error) => {
      resolveNotification(error, async (response) => {
        const responseContent = JSON.parse(await response.data.text())
        if (responseContent.detail && response.status === StatusCodes.UNPROCESSABLE_ENTITY) {
          useNotificationsStore().addError(responseContent.detail)
          return true
        }
        return false
      })
    })
}

const datasheetTemplateEditorRef = ref()

const submitForms = async () => {
  if (datasheetEditorRef.value) {
    await datasheetEditorRef.value.save()
  }

  if (datasheetTemplateEditorRef.value) {
    await datasheetTemplateEditorRef.value.uploadTemplate()
  }
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle v-if="datasheet" :id="datasheet.id" :title="datasheet.name" />
        </template>

        <ButtonBasic
          icon="list"
          :to="{ name: 'DatasheetList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: 'Layout',
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'DatasheetNew' }"
          :tooltip="
            Translator.trans('u2_core.add_new_given_entity_type', {
              entity_type_name: 'Layout',
            })
          "
        />

        <ButtonSpacer />

        <ButtonDelete
          :tooltip="
            Translator.trans('u2_core.delete_given_entity_type', {
              entity_type_name: 'Layout',
            })
          "
          @click="deleteLayout"
        />

        <ButtonSave :state="loading ? 'saving' : 'ready'" @click="submitForms" />
      </PageHeader>
    </template>
    <template v-if="datasheet" #asideBefore>
      <InformationAsideSection>
        <InformationGridRow :label="Translator.trans('u2_core.id')">
          <router-link :to="{ name: 'DatasheetEdit', params: { id: id } }">{{ id }}</router-link>
        </InformationGridRow>
        <InformationGridRow :label="Translator.trans('u2_core.created')">
          <AppDateTime :date="datasheet.createdAt" :relative="true" />
          <UserLabel
            :user="datasheet.createdBy ? getIdFromIri(datasheet.createdBy) : undefined"
            color="white"
          />
        </InformationGridRow>
        <InformationGridRow :label="Translator.trans('u2_core.updated')">
          <AppDateTime :date="datasheet.updatedAt" :relative="true" />
          <UserLabel
            :user="datasheet.updatedBy ? getIdFromIri(datasheet.updatedBy) : undefined"
            color="white"
          />
        </InformationGridRow>
      </InformationAsideSection>

      <DatasheetUnassignedFieldsAsideSection
        :datasheet="datasheet"
        :fields="fields"
        :unused-fields="
          showFileInput && datasheetHasTemplate ? fields : fieldsNotReferencedInTemplate
        "
        :context="datasheetNavigationContext"
        :has-template="!!datasheetHasTemplate && !showFileInput"
        :field-name-suggestions="missingFields"
      />
    </template>

    <template v-if="datasheet">
      <DatasheetEditor
        ref="datasheetEditorRef"
        :datasheet="datasheet"
        @saved="
          () => {
            notificationStore.addSuccess(Translator.trans('u2_core.success_saved'))
          }
        "
      />

      <HeaderWithAction underline>
        {{ Translator.trans('u2.template') }}
        <template #button>
          <div v-if="datasheetHasTemplate && !showFileInput" class="flex items-center space-x-1">
            <ButtonDownload @click="downloadTemplate" />
            <ButtonBasic v-if="!showFileInput" icon="upload" @click="showFileInput = true" />
            <ButtonDelete :show-text="false" @click="showDeleteTemplateDialog = true" />
          </div>
          <ButtonBasic
            v-else-if="!isLoading && datasheetHasTemplate"
            icon="cross"
            @click="showFileInput = false"
          >
            {{ Translator.trans('u2.cancel') }}
          </ButtonBasic>
        </template>
      </HeaderWithAction>

      <AppMessage
        v-if="!isLoading && fieldsNotReferencedInTemplate.length > 0 && !showFileInput"
        type="warning"
        class="mt-1 mb-2"
      >
        <div class="flex items-center gap-x-2">
          {{ Translator.trans('u2.datasheets.unassigned_fields') }}
          ({{ fieldsNotReferencedInTemplate.length }})
        </div>
      </AppMessage>

      <AppMessage v-if="!isLoading && missingFields?.length > 0" type="warning" class="mb-2">
        <div class="flex items-center gap-x-2">
          {{ Translator.trans('u2.datasheets.missing_fields') }} ({{ missingFields.length }})
        </div>
      </AppMessage>

      <AppLoader v-if="isLoading" class="h-56" />

      <DatasheetTemplateEditor
        v-else-if="showFileInput || !datasheetHasTemplate"
        ref="datasheetTemplateEditorRef"
        :datasheet="datasheet"
        :field-name-suggestions="missingFields"
        @saved="
          () => {
            invalidateQueries()
            showFileInput = false
          }
        "
      />

      <RenderedDatasheetTemplate
        v-else-if="datasheetTemplateHtml && fields"
        :fields="fields"
        :raw-html="datasheetTemplateHtml"
        :datasheet="datasheet"
        :context="datasheetNavigationContext"
      />
    </template>

    <template #asideAfter>
      <EntityPermissionsAside
        v-if="datasheet"
        :resource="datasheet"
        :is-public="showPublicVisibilityInfo"
        @updated="invalidateMenuQuery"
      />
    </template>

    <AppDialog
      v-if="showFieldDialog && datasheet"
      class="w-full max-w-5xl"
      :title="
        selectedField
          ? `${Translator.trans('u2.field')} #${selectedField.name}`
          : Translator.trans('u2.new_entity_type_name', {
              entity_type_name: Translator.trans('u2.field'),
            })
      "
      @close="closeFieldDialog"
    >
      <FieldEditor
        ref="fieldEditor"
        :datasheet="datasheet"
        :field="selectedField"
        @saved="closeFieldDialog"
      />

      <template #buttons>
        <ButtonBasic @click="closeFieldDialog">
          {{ Translator.trans('u2.cancel') }}
        </ButtonBasic>

        <ButtonSave form="field_form" button-style="solid" :disabled="isSaving" />
      </template>
    </AppDialog>

    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @close="cancelDelete"
      @confirm="confirmDelete"
    >
      {{
        Translator.trans('u2_core.delete_given_entity_type.confirmation', {
          entity_type_name: 'Layout',
        })
      }}
    </ConfirmationDialog>

    <ConfirmationDialog
      v-if="showDeleteTemplateDialog"
      @confirm="deleteTemplate"
      @close="showDeleteTemplateDialog = false"
    />
  </AppPageWithAside>
</template>
