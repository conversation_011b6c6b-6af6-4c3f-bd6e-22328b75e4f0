<script setup lang="ts">
import { useHead } from '@vueuse/head'
import FormRow from '@js/components/form/FormRow.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import DynamicAssetEditor from '@js/components/system-settings/DynamicAssetEditor.vue'

useHead({ title: Translator.trans('u2_core.edit_system_images') })
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.edit_system_images')" />
    </template>

    <div class="grid grid-cols-1 gap-(--app-form-field-spacing)">
      <FormRow :label="Translator.trans('u2_core.login_logo')">
        <template #help-tooltip>
          <!-- eslint-disable-next-line vue/no-v-html -->
          <span v-html="Translator.trans('u2_core.login_logo.help')" />
        </template>

        <DynamicAssetEditor
          id="login-logo"
          :supported-mime-types="[
            'image/svg',
            'image/svg+xml',
            'image/png',
            'image/apng',
            'image/vnd.mozilla.apng',
            'image/jpeg',
            'image/pjpeg',
          ]"
          :max-file-upload-size="500000"
        />
      </FormRow>

      <FormRow :label="Translator.trans('u2_core.corp_logo')">
        <template #help-tooltip>
          <!-- eslint-disable-next-line vue/no-v-html -->
          <span v-html="Translator.trans('u2_core.corp_logo.help')" />
        </template>

        <DynamicAssetEditor
          id="corp-logo"
          :max-file-upload-size="500000"
          :supported-mime-types="['image/svg', 'image/svg+xml']"
        />
      </FormRow>
      <FormRow :label="Translator.trans('u2_core.login_background')">
        <template #help-tooltip>
          <!-- eslint-disable-next-line vue/no-v-html -->
          <span v-html="Translator.trans('u2_core.login_background.help')" />
        </template>

        <DynamicAssetEditor id="background-image" :max-file-upload-size="3000000" />
      </FormRow>
      <FormRow :label="Translator.trans('u2_core.documentation_corp_logo')">
        <template #help-tooltip>
          <!-- eslint-disable-next-line vue/no-v-html -->
          <span v-html="Translator.trans('u2_core.documentation_corp_logo.help')" />
        </template>

        <DynamicAssetEditor id="documentation-corp-logo" :max-file-upload-size="500000" />
      </FormRow>
      <FormRow :label="Translator.trans('u2_core.documentation_cover_picture')">
        <template #help-tooltip>
          <!-- eslint-disable-next-line vue/no-v-html -->
          <span v-html="Translator.trans('u2_core.documentation_cover_picture.help')" />
        </template>

        <DynamicAssetEditor id="documentation-cover-picture" :max-file-upload-size="3000000" />
      </FormRow>
    </div>
  </AppPage>
</template>
