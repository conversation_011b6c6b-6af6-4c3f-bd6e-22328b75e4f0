<script lang="ts" setup>
import { deleteCountryById } from '@js/api/countryApi'
import AppLoader from '@js/components/loader/AppLoader.vue'
import { queries } from '@js/query'
import { useQueryClient } from '@tanstack/vue-query'
import { useRouteParams } from '@vueuse/router'
import { ref, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import CountryEditor from '@js/components/CountryEditor.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Country } from '@js/model/country'

const router = useRouter()
const readableName = Translator.trans('u2.country')
const countryId = useRouteParams('id', undefined, { transform: (value: string) => Number(value) })
const queryClient = useQueryClient()
const country = ref<Country>()
const isLoading = ref(true)
const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
async function fetchCountry() {
  try {
    isLoading.value = true
    country.value = await queryClient.fetchQuery(queries.countries.single(countryId))
  } catch (error) {
    if (!isAxiosError(error) || (error.response && !handleAxiosErrorResponse(error.response))) {
      throw error
    }
  } finally {
    isLoading.value = false
  }
}

watchEffect(() => fetchCountry())
useHead({ title: () => `${Translator.trans('u2.country')} #${countryId.value}` })

const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
async function deleteCountry() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await deleteCountryById(countryId.value)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'CountryList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

function onSave() {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle v-if="country" :id="country.id" :title="readableName" />
        </template>

        <ButtonBasic
          icon="list"
          :to="{ name: 'CountryList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: readableName,
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'CountryNew' }"
          :tooltip="
            Translator.trans('u2_core.add_new_given_entity_type', {
              entity_type_name: readableName,
            })
          "
        />

        <ButtonSpacer />

        <ButtonDelete
          :tooltip="
            Translator.trans('u2_core.delete_given_entity_type', {
              entity_type_name: readableName,
            })
          "
          @click="deleteCountry"
        />

        <ButtonSave form="country" />
      </PageHeader>
    </template>
    <AppLoader v-if="isLoading" class="mt-10" />
    <CountryEditor v-else :country="country" @saved="onSave" />
    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @confirm="confirmDelete"
      @close="cancelDelete"
    >
      {{
        Translator.trans('u2_core.delete_given_entity_type.confirmation', {
          entity_type_name: readableName,
        })
      }}
    </ConfirmationDialog>
  </AppPageWithAside>
</template>
