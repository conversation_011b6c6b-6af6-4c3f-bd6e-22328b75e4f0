<script setup lang="ts">
import { useTemplateRef } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import StatusEditor from '@js/components/workflow/StatusEditor.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import type { Status } from '@js/model/status'

const router = useRouter()
const notificationsStore = useNotificationsStore()
useHead({ title: Translator.trans('u2.new_status') })
const statusEditor = useTemplateRef('statusEditor')

const onSave = (newStatus: Status) => {
  notificationsStore.addSuccess(Translator.trans('u2_core.success'))
  router.push({ name: 'StatusEdit', params: { id: newStatus.id } })
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2.new_status')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'StatusList' }"
          :tooltip="Translator.trans('u2_core.status_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave :state="statusEditor?.state" form="status" />
      </PageHeader>
    </template>
    <StatusEditor ref="statusEditor" @saved="onSave" />
  </AppPageWithAside>
</template>
