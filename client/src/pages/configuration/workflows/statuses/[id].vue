<script setup lang="ts">
import * as StatusApi from '@js/api/statusApi'
import invariant from 'tiny-invariant'
import { ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import StatusEditor from '@js/components/workflow/StatusEditor.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Status } from '@js/model/status'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{
  id: number
}>()

useHead({ title: () => `${Translator.trans('u2_core.status')} #${props.id}` })

const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()

async function deleteStatus() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await StatusApi.deleteStatusById(props.id)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await router.push({ name: 'StatusList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const router = useRouter()
const status = ref<Status>()

function onSave() {
  notificationsStore.addSuccess(Translator.trans('u2_core.success'))
  invariant(status.value)

  router.push({ name: 'StatusEdit', params: { id: status.value.id } })
}

const { id } = toRefs(props)

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
try {
  status.value = (await StatusApi.fetchStatusById(id.value)).data
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

const statusEditor = useTemplateRef('statusEditor')

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    try {
      status.value = (await StatusApi.fetchStatusById(Number(to.params.id))).data
    } catch (error) {
      if (
        !isAxiosError(error) ||
        (error.response && !(await handleAxiosErrorResponse(error.response)))
      ) {
        throw error
      }
    }
  }
})
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle :id="id" :title="Translator.trans('u2_core.status')" />
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'StatusList' }"
          :tooltip="Translator.trans('u2_core.status_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonNew
          :to="{ name: 'StatusNew' }"
          :tooltip="Translator.trans('u2_core.add_new_status')"
        />

        <ButtonSave :state="statusEditor?.state" form="status" />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              icon="delete"
              :text="Translator.trans('u2.delete')"
              :tooltip="Translator.trans('u2_core.delete_status')"
              @click="deleteStatus"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #default>
      <StatusEditor v-if="status" ref="statusEditor" :status="status" @saved="onSave" />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{ Translator.trans('u2_core.delete_status.confirmation') }}
      </ConfirmationDialog>
    </template>
  </AppPageWithAside>
</template>
