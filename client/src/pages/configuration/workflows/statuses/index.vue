<script setup lang="ts">
import { deleteStatus } from '@js/api/statusApi'
import { keepPreviousData, useQuery } from '@tanstack/vue-query'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { computed, ref } from 'vue'
import { useConfirmDialog, watchDebounced } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { queries } from '@js/query'
import AppPage from '@js/components/page-structure/AppPage.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useTable from '@js/composable/useTable'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Status } from '@js/model/status'

const router = useRouter()
const route = useRoute()
useHead({ title: Translator.trans('u2_core.statuses') })

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()
const toDeleteStatus = ref()
const deleteDashboard = async (status: Status) => {
  toDeleteStatus.value = status
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await deleteStatus(status)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      refetch()
    } catch (error) {
      resolveNotification(error)
    }
  }
  toDeleteStatus.value = null
}

const {
  columns,
  query,
  changePage,
  changePageSize,
  fromLocationQuery,
  apiQuery,
  toLocationQuery,
  sort,
} = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.status'),
      id: 'name',
    },
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.type'),
      id: 'type',
    },
    {
      align: 'right',
      name: '',
      id: 'actions',
    },
  ],
  { filter: { search: '' } }
)
const { data: statusesData, refetch } = useQuery({
  ...queries.statuses.list(apiQuery),
  placeholderData: keepPreviousData,
})
const statuses = computed(() => statusesData.value?.['hydra:member'])
const totalItems = computed(() => statusesData.value?.['hydra:totalItems'])

watchDebounced(
  query,
  (newValue) => {
    router.push({ query: toLocationQuery(newValue) })
  },
  { deep: true, debounce: 400 }
)

const newQuery = fromLocationQuery(route.query)
if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
  query.value = newQuery
}

onBeforeRouteUpdate(async (to) => {
  const newQuery = fromLocationQuery(to.query)
  if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
    query.value = newQuery
  }
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.statuses')">
        <ButtonNew
          :to="{ name: 'StatusNew' }"
          :tooltip="Translator.trans('u2_core.add_new_status')"
        />
      </PageHeader>
    </template>
    <AppSearch v-model="query.filter.search" class="w-96 max-w-full" />
    <AppTable
      v-if="statuses"
      :query="query"
      :headers="columns"
      :items="statuses"
      :total-items="totalItems"
      horizontal-scroll
      @sort="sort"
      @page-change="changePage"
      @page-size-change="changePageSize"
    >
      <template #item-name="{ item }">
        <StatusBadge :status="item" />
      </template>
      <template #item-actions="{ item }">
        <ButtonEdit
          :to="{ name: 'StatusEdit', params: { id: item.id } }"
          :tooltip="
            Translator.trans('u2_core.edit_status_with_given_name', {
              status_name: item.name,
            })
          "
        />

        <ButtonDelete
          :tooltip="
            Translator.trans('u2_core.delete_status_with_given_name', {
              status_name: item.name,
            })
          "
          @click="deleteDashboard(item)"
        />
      </template>
    </AppTable>
    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @close="cancelDelete"
      @confirm="confirmDelete"
    >
      {{
        Translator.trans('u2_core.delete_status_with_given_name.confirmation', {
          status_name: toDeleteStatus.name,
        })
      }}
    </ConfirmationDialog>
  </AppPage>
</template>
