<script setup lang="ts">
import { useMutation, useQueries, useQuery, useQueryClient } from '@tanstack/vue-query'
import { toTypedSchema } from '@vee-validate/zod'
import { useHead } from '@vueuse/head'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import { computed, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { z } from 'zod'
import uniqBy from 'lodash/uniqBy'
import FieldWorkflowStatusSelect from '@js/components/form/FieldWorkflowStatusSelect.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import { useNotificationsStore } from '@js/stores/notifications'
import FieldSelect from '@js/components/form/FieldSelect.vue'
import { workflowBindingApi } from '@js/api/workflowBindingApi'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import useForm from '@js/composable/useForm'
import useWorkflowQuery from '@js/composable/useWorkflowQuery'
import { queries } from '@js/query'
import Translator from '@js/translator'
import { getIdFromIri } from '@js/utilities/api-resource'
import type { StatusMapping } from '@js/api/workflowBindingApi'
import type { WorkflowBinding } from '@js/api/workflowApi'

const props = defineProps<{
  bindingId: WorkflowBinding['bindingId']
}>()

const { bindingId } = toRefs(props)

const queryClient = useQueryClient()
const workflowBindingData = await queryClient.fetchQuery(queries.workflowBindings.single(bindingId))
const workflowBinding = ref<WorkflowBinding>(workflowBindingData)
useHead({
  title: computed(() =>
    Translator.trans('u2_core.workflow.edit_workflow_assignment_with_given_name', {
      workflow_name: workflowBinding.value?.readableName || '',
    })
  ),
})
const { data: workflow, isLoading: isWorkflowLoading } = useWorkflowQuery(
  computed(() => (workflowBinding.value ? getIdFromIri(workflowBinding.value.workflow) : undefined))
)
const { data: allWorkflows, isLoading: isAllWorkflowsLoading } = useQuery(
  queries.workflows.list({ pagination: false })
)
const loading = computed(() => isWorkflowLoading.value || isAllWorkflowsLoading.value)
const targetWorkflowOptions = computed(() => {
  return (
    allWorkflows.value?.['hydra:member']?.map((workflow) => ({
      id: workflow['@id'],
      name: workflow.name,
    })) ?? []
  )
})

const statusMappingSchema = z.object({
  destination: z.string().min(1),
})

const { handleSubmit, setResponseErrors, values, unmappedErrors } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      targetWorkflow: z.string().min(1),
      statusMappings: z.array(statusMappingSchema),
    })
  ),
  initialValues: {
    targetWorkflow: workflowBinding.value?.workflow ?? '',
    statusMappings: [] as Array<{ source: string; destination: string }>,
  },
})

const currentWorkflowTransitionsData = useQueries({
  queries: computed(() => {
    if (
      !workflow.value ||
      workflow.value.transitions.length === 0 ||
      workflow.value['@id'] === values.targetWorkflow
    ) {
      return []
    }
    return workflow.value?.transitions.map((transitionIri) =>
      queries.workflowTransitions.single(() => getIdFromIri(transitionIri))
    )
  }),
})

const targetWorkflowData = useQuery({
  ...queries.workflows.single(
    computed(() => (values.targetWorkflow ? getIdFromIri(values.targetWorkflow) : undefined))
  ),
  enabled: computed(
    () => !!values.targetWorkflow || workflow.value?.['@id'] !== values.targetWorkflow
  ),
})

const targetWorkflowTransitionsData = useQueries({
  queries: computed(() => {
    const targetWorkflow = targetWorkflowData.data.value
    if (
      !targetWorkflow ||
      targetWorkflow.transitions.length === 0 ||
      workflow.value?.['@id'] === targetWorkflow['@id']
    ) {
      return []
    }
    return targetWorkflow.transitions.map((transitionIri) =>
      queries.workflowTransitions.single(getIdFromIri(transitionIri))
    )
  }),
})

const isTransitionsLoading = computed(() => {
  return (
    currentWorkflowTransitionsData.value.some((query) => query.isLoading) ||
    targetWorkflowTransitionsData.value.some((query) => query.isLoading)
  )
})

const targetStatuses = computed(() => {
  return uniqBy(
    targetWorkflowTransitionsData.value
      .map((query) => (query.data ? query.data.originStatus : undefined))
      .filter((status) => status !== undefined),
    '@id'
  )
})

const missingStatuses = computed(() => {
  if (values.targetWorkflow === workflowBinding.value?.workflow || isTransitionsLoading.value) {
    return []
  }

  const currentStatuses = uniqBy(
    [
      ...currentWorkflowTransitionsData.value
        .flatMap((query) => [query.data?.originStatus, query.data?.destinationStatus])
        .filter((status) => status !== undefined),
    ],
    '@id'
  )

  return currentStatuses.filter((currentStatus) =>
    targetStatuses.value.every((targetStatus) => targetStatus['@id'] !== currentStatus['@id'])
  )
})

const { resolveNotification } = useHandleAxiosErrorResponse()
const notificationStore = useNotificationsStore()
const router = useRouter()
const { mutate: saveWorkflowBindingMutation } = useMutation({
  mutationFn: (values: {
    targetWorkflow: WorkflowBinding['workflow']
    statusMappings: Array<StatusMapping>
  }) => {
    return workflowBindingApi.updateWorkflowBinding({
      binding: workflowBinding.value['@id'],
      ...values,
    })
  },
  onSuccess: (data) => {
    notificationStore.addSuccess(Translator.trans('u2_core.workflow.change_workflow.success'))
    workflowBinding.value = data.data
    queryClient.invalidateQueries(queries.workflowBindings.single(bindingId))
    router.push({ name: 'WorkflowBindingList' })
  },
  onError: (error) => {
    resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    const errorPropertyPathMapping: Record<string, string> = {}
    statusMappings.value.forEach((_, index) => {
      errorPropertyPathMapping[`statusMappings[${index}].destination`] = ''
    })
    setResponseErrors(error.response, errorPropertyPathMapping)
  },
})

const statusMappings = computed(() => {
  return missingStatuses.value.map((status, index) => ({
    source: status['@id'],
    destination: values.statusMappings?.[index].destination,
  }))
})

const save = handleSubmit((values) => {
  saveWorkflowBindingMutation({
    ...values,
    statusMappings: statusMappings.value,
  })
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader
        v-if="workflowBinding"
        :title="
          Translator.trans('u2_core.workflow.edit_workflow_assignment_with_given_name', {
            workflow_name: workflowBinding.readableName,
          })
        "
      >
        <ButtonBasic :to="{ name: 'WorkflowBindingList' }" icon="link">
          {{ Translator.trans('u2_core.workflow_assignments') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave :disabled="loading" form="binding_workflow_transition_mapping_form" />
      </PageHeader>
    </template>

    <AppLoader v-if="loading" />

    <form
      v-else
      id="binding_workflow_transition_mapping_form"
      name="binding_workflow_transition_mapping_form"
      class="block"
      @submit.prevent="save"
    >
      <div
        class="inline-grid w-full grid-rows-[auto] items-start gap-y-2 sm:w-auto sm:auto-rows-[minmax(5rem,1fr)] sm:grid-cols-[auto_1fr_minmax(20rem,auto)]"
      >
        <div class="hidden border-b py-2 font-bold whitespace-nowrap sm:block">
          {{ Translator.trans('u2_core.workflow.current_workflow') }}
        </div>

        <div class="hidden h-full border-b sm:block"></div>

        <div class="hidden border-b py-2 font-bold whitespace-nowrap sm:block">
          {{ Translator.trans('u2_core.workflow.destination_workflow') }}
        </div>

        <div class="sm:py-2">
          <div class="block text-sm font-medium whitespace-nowrap text-gray-500 sm:hidden">
            {{ Translator.trans('u2_core.workflow.current_workflow') }}
          </div>

          <div class="inline-flex flex-col items-center gap-y-2">
            <template v-if="workflow?.name">
              {{ workflow.name }}
            </template>
            <em v-else>{{ Translator.trans('u2_core.none') }}</em>
            <div class="text-gray-700 sm:hidden">&downarrow;</div>
          </div>
        </div>

        <div class="hidden text-gray-700 sm:block sm:px-4 sm:py-2">&rightarrow;</div>

        <div>
          <div class="block py-1 text-sm font-medium whitespace-nowrap text-gray-500 sm:hidden">
            {{ Translator.trans('u2_core.workflow.destination_workflow') }}
          </div>
          <FieldSelect
            :label="false"
            :options="targetWorkflowOptions"
            name="targetWorkflow"
            :aria-label="Translator.trans('u2_core.workflow.destination_workflow')"
          />
        </div>
      </div>

      <FormErrors :errors="unmappedErrors" />

      <AppLoader v-if="isTransitionsLoading" class="mt-6" />

      <template v-else-if="missingStatuses.length > 0 && targetStatuses.length > 0">
        <h2 class="mt-6">{{ Translator.trans('u2_core.workflow.remap_missing_statuses') }}</h2>

        <p>
          {{ Translator.trans('u2_core.workflow.select_destination_status_for_assigned_objects') }}
        </p>

        <div
          class="inline-grid w-full grid-rows-[auto] items-start gap-x-2 sm:w-auto sm:auto-rows-[minmax(5rem,1fr)] sm:grid-cols-[auto_1fr_minmax(20rem,auto)] sm:gap-x-0"
        >
          <div class="hidden border-b py-2 font-bold whitespace-nowrap sm:block">
            {{ Translator.trans('u2_core.workflow.current_status') }}
          </div>

          <div class="hidden h-full border-b p-4 sm:block"></div>

          <div class="hidden border-b py-2 font-bold whitespace-nowrap sm:block">
            {{ Translator.trans('u2_core.workflow.destination_status') }}
          </div>
          <template v-for="(status, index) in missingStatuses" :key="index">
            <div class="sm:py-6">
              <div
                class="block pt-4 pb-1 text-sm font-medium whitespace-nowrap text-gray-500 sm:hidden"
              >
                {{ Translator.trans('u2_core.workflow.current_status') }}
              </div>
              <div class="inline-flex flex-col items-center gap-y-2">
                <StatusBadge :status="status" />
                <div class="text-gray-700 sm:hidden">&downarrow;</div>
              </div>
            </div>

            <div class="hidden text-gray-700 sm:block sm:px-2 sm:py-6">&rightarrow;</div>

            <div
              class="block pt-2 pb-1 text-sm font-medium whitespace-nowrap text-gray-500 sm:hidden"
            >
              {{ Translator.trans('u2_core.workflow.destination_status') }}
            </div>
            <FieldWorkflowStatusSelect
              :label="false"
              :statuses="targetStatuses"
              :name="`statusMappings[${index}].destination`"
              class="border-b pb-6 last:border-b-0 sm:border-b-0 sm:py-4"
            />
          </template>
        </div>
      </template>
    </form>
  </AppPage>
</template>
