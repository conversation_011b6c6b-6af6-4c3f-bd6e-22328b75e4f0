<script lang="ts" setup>
import { auditorApi } from '@js/api/auditorApi'
import AppLoader from '@js/components/loader/AppLoader.vue'
import { queries } from '@js/query'
import { useQueryClient } from '@tanstack/vue-query'
import { useRouteParams } from '@vueuse/router'
import { isAxiosError } from 'axios'
import { computed, ref, useTemplateRef, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AuditorEditor from '@js/components/AuditorEditor.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Auditor } from '@js/model/auditor'

const readableName = Translator.trans('u2_core.auditor')
const auditorId = useRouteParams('id', undefined, { transform: (value: string) => Number(value) })
const queryClient = useQueryClient()
const auditor = ref<Auditor>()
const isLoading = ref(true)
const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
async function fetchAuditor() {
  try {
    isLoading.value = true
    auditor.value = await queryClient.fetchQuery(queries.auditors.single(auditorId))
  } catch (error) {
    if (!isAxiosError(error) || (error.response && !handleAxiosErrorResponse(error.response))) {
      throw error
    }
  } finally {
    isLoading.value = false
  }
}

watchEffect(() => fetchAuditor())

useHead({ title: () => `${Translator.trans('u2_core.auditor')} #${auditorId.value}` })
const auditorEditor = useTemplateRef<InstanceType<typeof AuditorEditor>>('auditorEditor')

const state = computed(() => (isLoading.value ? 'loading' : auditorEditor.value?.state))
const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const router = useRouter()
async function deleteAuditor() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await auditorApi.deleteAuditorById(auditorId.value)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'AuditorList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

function onSave() {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle v-if="auditor" :id="auditor.id" :title="readableName" />
        </template>

        <ButtonBasic
          icon="list"
          :to="{ name: 'AuditorList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: readableName,
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'AuditorNew' }"
          :tooltip="
            Translator.trans('u2_core.add_new_given_entity_type', {
              entity_type_name: readableName,
            })
          "
        />

        <ButtonSpacer />

        <ButtonDelete
          :tooltip="
            Translator.trans('u2_core.delete_given_entity_type', {
              entity_type_name: readableName,
            })
          "
          @click="deleteAuditor"
        />

        <ButtonSave form="auditor" :state="state" />
      </PageHeader>
    </template>
    <AppLoader v-if="isLoading" class="mt-10" />

    <AuditorEditor v-else ref="auditorEditor" :auditor="auditor" @saved="onSave" />
    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @confirm="confirmDelete"
      @close="cancelDelete"
    >
      {{
        Translator.trans('u2_core.delete_given_entity_type.confirmation', {
          entity_type_name: readableName,
        })
      }}
    </ConfirmationDialog>
  </AppPageWithAside>
</template>
