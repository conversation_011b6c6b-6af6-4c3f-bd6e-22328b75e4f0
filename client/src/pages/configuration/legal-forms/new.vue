<script lang="ts" setup>
import { useHead } from '@vueuse/head'
import { useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import LegalFormEditor from '@js/components/LegalFormEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import type { LegalForm } from '@js/api/legalFormApi'

const router = useRouter()
useHead({ title: Translator.trans('u2.new_entity_type_name', { entity_type_name: 'Legal Form' }) })
const legalFormEditor = useTemplateRef('legalFormEditor')

const onSave = (legalForm: LegalForm) => {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
  router.push({ name: 'LegalFormEdit', params: { id: legalForm.id } })
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.legal_form')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'LegalFormList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: Translator.trans('u2_core.legal_form'),
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="legal_form" :state="legalFormEditor?.state" />
      </PageHeader>
    </template>

    <LegalFormEditor ref="legalFormEditor" @saved="onSave" />
  </AppPageWithAside>
</template>
