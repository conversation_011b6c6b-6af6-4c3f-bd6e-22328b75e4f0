<script lang="ts" setup>
import * as LegalFormApi from '@js/api/legalFormApi'
import { ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import LegalFormEditor from '@js/components/LegalFormEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{
  id: number
}>()
const legalForm = ref()
const router = useRouter()
const readableName = Translator.trans('u2_core.legal_form')
useHead({ title: () => `${Translator.trans('u2_core.legal_form')} #${props.id}` })
const legalFormEditor = useTemplateRef('legalFormEditor')

const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()

async function deleteLegalForm() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await LegalFormApi.deleteLegalFormById(props.id)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'LegalFormList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

function onSave() {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}

const { id } = toRefs(props)

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
try {
  legalForm.value = (await LegalFormApi.fetchLegalFormById(id.value)).data
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    try {
      legalForm.value = (await LegalFormApi.fetchLegalFormById(Number(to.params.id))).data
    } catch (error) {
      if (
        !isAxiosError(error) ||
        (error.response && !(await handleAxiosErrorResponse(error.response)))
      ) {
        throw error
      }
    }
  }
})
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle v-if="legalForm" :id="legalForm.id" :title="readableName" />
        </template>

        <ButtonBasic
          icon="list"
          :to="{ name: 'LegalFormList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: readableName,
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'LegalFormNew' }"
          :tooltip="
            Translator.trans('u2_core.add_new_given_entity_type', {
              entity_type_name: readableName,
            })
          "
        />

        <ButtonSpacer />

        <ButtonDelete
          :tooltip="
            Translator.trans('u2_core.delete_given_entity_type', {
              entity_type_name: readableName,
            })
          "
          @click="deleteLegalForm"
        />

        <ButtonSave form="legal_form" :state="legalFormEditor?.state" />
      </PageHeader>
    </template>

    <LegalFormEditor
      v-if="legalForm"
      ref="legalFormEditor"
      :legal-form="legalForm"
      @saved="onSave"
    />

    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @confirm="confirmDelete"
      @close="cancelDelete"
    >
      {{
        Translator.trans('u2_core.delete_given_entity_type.confirmation', {
          entity_type_name: readableName,
        })
      }}
    </ConfirmationDialog>
  </AppPageWithAside>
</template>
