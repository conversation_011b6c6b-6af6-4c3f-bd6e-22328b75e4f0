<script setup lang="ts">
import { unref, useTemplateRef } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import FileEditor from '@js/components/file/FileEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import type { FileEntity } from '@js/model/file'

useHead({ title: Translator.trans('u2.new_file') })
const router = useRouter()
const fileEditor = useTemplateRef('fileEditor')
const onSaved = async (updatedFile: FileEntity) => {
  await router.push({ name: 'FileEdit', params: { id: unref(updatedFile).id } })
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2.new_file')">
        <ButtonBasic :to="{ name: 'FileList' }" icon="list">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="file" :state="fileEditor?.state" />
      </PageHeader>
    </template>

    <FileEditor ref="fileEditor" @saved="onSaved" />
  </AppPageWithAside>
</template>
