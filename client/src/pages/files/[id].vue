<script setup lang="ts">
import * as FileApi from '@js/api/fileApi'
import { computed, ref, toRefs, useTemplateRef } from 'vue'
import { saveAs } from 'file-saver'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import AppDateTime from '@js/components/AppDateTime.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import { fetchStates } from '@js/types'
import FileEditor from '@js/components/file/FileEditor.vue'
import FilePermissions from '@js/components/file/FilePermissions.vue'
import InformationAsideSection from '@js/components/InformationAsideSection.vue'
import InformationGridRow from '@js/components/InformationGridRow.vue'
import UserLabel from '@js/components/UserLabel.vue'
import LinkedEntities from '@js/components/file/LinkedEntities.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import { PermissionMasks } from '@js/model/permission'
import PrettyBytes from '@js/components/PrettyBytes.vue'
import RequestPermissionsDialog from '@js/components/file/RequestPermissionsDialog.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import useFileLinkedEntities from '@js/composable/useFileLinkedEntities'
import { useMyPermission } from '@js/composable/useMyPermission'
import { useNotificationsStore } from '@js/stores/notifications'
import { getIdFromIri } from '@js/utilities/api-resource'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { FileEntity } from '@js/model/file'
import useLocaleNumberFormatter from '@js/composable/useLocaleNumberFormatter'

const props = defineProps<{ file: FileEntity }>()

const { file } = toRefs(props)

const isRequestPermissionsDialogOpen = ref(false)
const userPermissions = computed(() => file.value?.userPermissions)
const groupPermissions = computed(() => file.value?.groupPermissions)
const { iHavePermission } = useMyPermission(userPermissions, groupPermissions)
const canWrite = computed(() => iHavePermission(PermissionMasks.EDIT))
const canDelete = computed(() => iHavePermission(PermissionMasks.DELETE))
useHead({ title: `${Translator.trans('u2.file')}: #${file.value?.id}` })
const fileEditor = useTemplateRef('fileEditor')

const createdBy = computed(() => file.value?.createdBy)
const updatedBy = computed(() => file.value?.updatedBy)
const numberFormatter = useLocaleNumberFormatter()
const fileSizeReadable = computed(() =>
  file.value?.sizeInBytes ? numberFormatter.formatBytes(file.value.sizeInBytes) : undefined
)

const router = useRouter()
const notificationStore = useNotificationsStore()
async function deleteFile() {
  try {
    await FileApi.deleteFileById(file.value.id)

    notificationStore.add({ type: 'success', message: Translator.trans('u2_core.success') })

    router.push({ name: 'FileList' })
  } catch (error) {
    await resolveNotification(error)
  }
}

const { unauthorizedEntityCount, loadingState, linksByType, fetchLinkedEntities } =
  useFileLinkedEntities(file)
const isLinkedEntitiesDialogShown = ref(false)
function hideLinkedEntitiesDialog() {
  isLinkedEntitiesDialogShown.value = false
}
function showLinkedEntitiesDialog() {
  isLinkedEntitiesDialogShown.value = true
  fetchLinkedEntities()
}

const onSave = (updatedFile: FileEntity) => {
  file.value = updatedFile
  useNotificationsStore().add({ type: 'success', message: Translator.trans('u2_core.success') })
}
const { resolveNotification } = useHandleAxiosErrorResponse()
async function downloadFile() {
  try {
    const downloadResponse = await FileApi.downloadFile(file.value)

    saveAs(downloadResponse.data, props.file.name)
  } catch (error) {
    resolveNotification(error)
  }
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle :title="`${Translator.trans('u2.file')}:`" :subtitle="file.name" />
        </template>

        <ButtonBasic :to="{ name: 'FileList' }" icon="list">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonBasic
          :tooltip="Translator.trans('u2_core.show_entities_linked_with_this_file')"
          icon="link"
          @click="showLinkedEntitiesDialog"
        >
          {{ Translator.trans('u2_core.linked_entities') }}
        </ButtonBasic>

        <ButtonBasic
          icon="download"
          :download="file.name"
          :title="Translator.trans('u2_core.download_file_name', { file_name: file.name })"
          @click="downloadFile"
        >
          {{ Translator.trans('u2_core.download') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew :to="{ name: 'FileNew' }" />

        <ButtonSpacer />

        <ButtonDelete
          id="button-delete-file"
          :confirm="true"
          :disabled="!canDelete"
          @click="deleteFile"
        />

        <ButtonSave :disabled="!canWrite" form="file" :state="fileEditor?.state" />

        <AppDialog
          v-if="isLinkedEntitiesDialogShown"
          :loading="loadingState === fetchStates.loading"
          :title="
            Translator.trans('u2_core.entities_linked_with_given_file', { file_name: file.name })
          "
          @close="hideLinkedEntitiesDialog"
        >
          <LinkedEntities
            v-if="loadingState === fetchStates.resolved"
            data-testid="linked-entities"
            :unauthorized-entity-count="unauthorizedEntityCount"
            :links-by-type="linksByType"
            class="w-96 max-w-full"
          />
        </AppDialog>
      </PageHeader>
    </template>

    <template #asideBefore>
      <InformationAsideSection>
        <!-- File Size -->
        <InformationGridRow :label="Translator.trans('u2.file_size')">
          <PrettyBytes v-if="fileSizeReadable" :bytes="file.sizeInBytes" />
          <span v-else>
            <SvgIcon icon="no" size="small" class="text-bad align-text-top" />
            <em class="ml-1">{{ Translator.trans('u2_core.file_not_found') }}</em>
          </span>
        </InformationGridRow>

        <!-- File MIME Content-Type -->
        <InformationGridRow :label="Translator.trans('u2_core.file_mime_content_type')">
          <span v-if="file.contentType">{{ file.contentType }}</span>
          <span v-else>
            <SvgIcon icon="no" size="small" class="text-bad align-text-top" />
            <em class="ml-1">{{ Translator.trans('u2_core.file_not_found') }}</em>
          </span>
        </InformationGridRow>

        <!-- My Permissions -->
        <InformationGridRow :label="Translator.trans('u2_core.my_permissions')">
          <FilePermissions
            :file="file"
            @send-permissions-request="isRequestPermissionsDialogOpen = true"
          />
        </InformationGridRow>

        <!-- Created -->
        <InformationGridRow
          v-if="file.createdAt || file.createdBy"
          :label="Translator.trans('u2_core.created')"
        >
          <AppDateTime v-if="file.createdAt" :date="file.createdAt" :relative="true" />

          <UserLabel :user="createdBy ? getIdFromIri(createdBy) : undefined" color="white" />
        </InformationGridRow>

        <!-- Updated -->
        <InformationGridRow
          v-if="file.updatedAt || file.updatedBy"
          :label="Translator.trans('u2_core.updated')"
        >
          <AppDateTime v-if="file.updatedAt" :date="file.updatedAt" :relative="true" />

          <UserLabel :user="updatedBy ? getIdFromIri(updatedBy) : undefined" color="white" />
        </InformationGridRow>
      </InformationAsideSection>
    </template>
    <template #default>
      <FileEditor ref="fileEditor" :file="file" @saved="onSave" />

      <RequestPermissionsDialog
        v-if="isRequestPermissionsDialogOpen"
        :file="file"
        @close="isRequestPermissionsDialogOpen = false"
      />
    </template>
    <template #asideAfter />
  </AppPageWithAside>
</template>
