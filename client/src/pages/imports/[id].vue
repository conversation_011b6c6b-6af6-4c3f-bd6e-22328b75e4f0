<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue'
import { useHead } from '@vueuse/head'
import { useIntervalFn } from '@vueuse/core'
import { importApi } from '@js/api/importApi'
import AppDateTime from '@js/components/AppDateTime.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import AppMessage from '@js/components/AppMessage.vue'
import LabelStatus from '@js/components/LabelStatus.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import useImportTypeQuery from '@js/composable/useImportTypeQuery'
import type { Import, ImportData, ImportTypeConfiguration } from '@js/model/import'

const { id } = defineProps<{
  id: number
}>()

useHead({ title: () => Translator.trans('u2_core.import.result') + ' #' + id })

const importResource = ref<Import>()
const { pause, isActive } = useIntervalFn(
  async () => {
    const response = await importApi.fetchImportById(id)
    importResource.value = response.data

    if (importResource.value.completedAt !== null && isActive.value) {
      pause()
    }
  },
  3000,
  { immediate: true, immediateCallback: true }
)

const { data: importConfiguration } = useImportTypeQuery(
  () => importResource.value?.configurationKey
)

const importData = ref<ImportData>()
watchEffect(() => {
  importApi.fetchImportDataById(id).then((response) => {
    importData.value = response.data
  })
})

const fieldLabelsByIdMap = computed(() => {
  if (!importConfiguration.value) {
    return new Map()
  }
  const configuration: ImportTypeConfiguration = importConfiguration.value.configuration
  return new Map(configuration.fields.map((field) => [field.id, field.label]))
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle :id="id" :title="Translator.trans('u2_core.import.result')" />
        </template>

        <ButtonBasic icon="list" :to="{ name: 'ImportList' }">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonBasic
          v-if="importResource"
          button-style="solid"
          icon="upload"
          :to="{
            name: 'ImportStart',
            params: { configurationKeySlug: importResource.configurationKey.replaceAll('_', '-') },
          }"
        >
          {{ Translator.trans('u2_core.import.new') }}
        </ButtonBasic>
      </PageHeader>
    </template>

    <AppLoader v-if="!importResource" />
    <template v-else>
      <AppMessage v-if="importResource.dryRun" class="mb-2" :show-icon="false">
        <div class="text-center">
          <span class="text-3xl font-extrabold">
            ** {{ Translator.trans('u2.simulated').toUpperCase() }} **
          </span>
          <br />
          {{ Translator.trans('u2.simulated.help') }}
        </div>
      </AppMessage>

      <div class="mb-5 flex flex-wrap gap-2 rounded-xs border border-gray-200 bg-gray-100 p-2">
        <div class="grow">
          <table class="aside-information-table">
            <tbody>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.type') }}</th>
                <td class="table-data-text">{{ importConfiguration?.readableName }}</td>
              </tr>
              <tr>
                <th class="table-data-name">
                  {{ Translator.trans('u2_core.import.uploaded_file') }}
                </th>
                <td class="table-data-text">{{ importResource.reference }}</td>
              </tr>
              <tr>
                <th class="table-data-name">
                  {{ Translator.trans('u2.records') }}
                </th>
                <td class="table-data-text">
                  {{ importResource.dataCount }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="grow">
          <table class="aside-information-table">
            <tbody>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.status') }}</th>
                <td class="table-data-text">
                  <LabelStatus :status="importResource.status" />
                </td>
              </tr>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.result') }}</th>
                <td class="table-data-text">
                  <LabelStatus v-if="importResource.result" :status="importResource.result" />
                  <template v-else>
                    {{ Translator.trans('u2_core.n_a') }}
                  </template>
                </td>
              </tr>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.progress') }}</th>
                <td class="table-data-text">
                  {{ importResource.progress !== null ? importResource.progress + '%' : '-' }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="grow">
          <table class="aside-information-table">
            <tbody>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.created_at') }}</th>
                <td class="table-data-text">
                  <AppDateTime
                    v-if="importResource.createdAt !== null"
                    :relative="true"
                    :date="importResource.createdAt"
                  />
                </td>
              </tr>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.started_at') }}</th>
                <td class="table-data-text">
                  <AppDateTime
                    v-if="importResource.startedAt !== null"
                    :relative="true"
                    :date="importResource.startedAt"
                  />
                  <template v-else>
                    {{ Translator.trans('u2_core.n_a') }}
                  </template>
                </td>
              </tr>
              <tr>
                <th class="table-data-name">{{ Translator.trans('u2_core.completed_at') }}</th>
                <td class="table-data-text">
                  <AppDateTime
                    v-if="importResource.completedAt !== null"
                    :relative="true"
                    :date="importResource.completedAt"
                  />
                  <template v-else>
                    {{ Translator.trans('u2_core.n_a') }}
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <h2 v-if="importResource.result === 'fail'">
        {{ Translator.trans('u2_core.details') }}
      </h2>
      <template v-if="importResource.errors.general.length > 0">
        <AppMessage
          v-for="(error, index) in importResource.errors.general"
          :key="index"
          type="warning"
        >
          {{ error }}
        </AppMessage>
      </template>

      <template v-if="Object.keys(importResource.errors.record).length > 0">
        <table class="data-table w-full">
          <thead>
            <tr>
              <th class="table-head-name w-10">index</th>
              <th class="table-head-text">{{ Translator.trans('u2_core.message') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(recordError, index) in importResource.errors.record" :key="index">
              <td class="table-data-name text-center">
                {{ index }}
              </td>
              <td class="table-data-text">
                <code v-if="importData">
                  <template v-for="(value, key) in importData.records[index]" :key="key">
                    <span
                      :class="{
                        'text-error': Object.keys(recordError.field).includes(
                          fieldLabelsByIdMap.get(key)
                        ),
                      }"
                    >
                      {{ value }}
                    </span>
                    <span v-if="key !== Object.keys(importData.records[index]).pop()">, </span>
                  </template>
                </code>

                <hr class="my-1 border-t border-dashed border-gray-200" />

                <p v-for="(message, generalIndex) in recordError.general" :key="generalIndex">
                  {{ message }}
                </p>
                <p v-for="(messages, field) in recordError.field" :key="field">
                  <kbd>{{ field }}</kbd
                  >: {{ messages.join('. ') }}
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </template>
    </template>
  </AppPage>
</template>
