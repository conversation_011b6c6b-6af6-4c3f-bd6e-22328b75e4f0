<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { ref, watch } from 'vue'
import { StatusCodes } from 'http-status-codes'
import { useHead } from '@vueuse/head'
import { useSessionStorage } from '@vueuse/core'
import FieldCheckbox from '@js/components/form/FieldCheckbox.vue'
import FieldInputText from '@js/components/form/FieldInputText.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import FormLabel from '@js/components/form/FormLabel.vue'
import useForm from '@js/composable/useForm'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'

const loading = ref(false)
useHead({ title: Translator.trans('u2.security.verify_login') })

const route = useRoute()
const authCode = ref<string>(route.query._auth_code?.toString() ?? '')

const router = useRouter()
const authStore = useAuthStore()

watch(
  () => authStore.isTokenValid(),
  async (isValid) => {
    if (isValid) {
      await router.push({ name: 'AppDefaultDashboard' })
    }
  }
)

const { handleSubmit, setFieldError } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      authCode: z.string().min(1),
      trustDevice: z.boolean().optional(),
    })
  ),
  initialValues: {
    authCode: authCode.value,
    trustDevice: false,
  },
})

const { resolveNotification } = useHandleAxiosErrorResponse()
const save = handleSubmit(async (values) => {
  loading.value = true

  try {
    await authStore.confirmTwoFactor(values)
  } catch (error) {
    await resolveNotification(error, async (response) => {
      if (
        response.status === StatusCodes.UNAUTHORIZED &&
        response.data.message === 'Invalid code'
      ) {
        setFieldError('authCode', Translator.trans('u2.authentication.two_factor.invalid_code'))
        return true
      }
      await router.push({ name: 'AppLogout' })
      return false
    })
    return
  } finally {
    loading.value = false
  }

  const redirectUrl = useSessionStorage<string | null>('redirect-url', null)
  const targetUrl = redirectUrl.value ?? '/'
  redirectUrl.value = null

  return router.push(targetUrl)
})
</script>

<template>
  <form class="no-required-stars" @submit.prevent="save">
    <h3>
      {{ Translator.trans('u2.security.verify_login') }}
    </h3>

    <FormLabel for="authCode-field" class="text-base leading-normal font-normal">
      {{ Translator.trans('u2.security.two_factor.code.label') }}
    </FormLabel>

    <FieldInputText
      id="authCode"
      :label="false"
      name="authCode"
      autocomplete="one-time-code"
      autofocus="autofocus"
      inputmode="numeric"
      class="max-w-36 flex-1 text-gray-900"
      :placeholder="Translator.trans('u2.security.two_factor.code.placeholder')"
    />

    <FieldCheckbox
      name="trustDevice"
      :label="Translator.trans('u2.security.two_factor.trust_device.label')"
    />

    <ButtonBasic
      button-style="solid"
      type="submit"
      class="h-8 flex-1 px-8 leading-none"
      :disabled="loading"
    >
      {{ Translator.trans('u2.security.finish_login') }}
    </ButtonBasic>
  </form>
  <div class="mt-2 text-center">
    <router-link :to="{ name: 'AppLogout' }">
      {{ Translator.trans('u2.return_to_the_login_page') }}
    </router-link>
  </div>
</template>
