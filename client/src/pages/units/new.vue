<script setup lang="ts">
import { computed, useTemplateRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useHead } from '@vueuse/head'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import LegalUnitEditor from '@js/components/unit/LegalUnitEditor.vue'
import OrganisationalGroupEditor from '@js/components/unit/OrganisationalGroupEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import PermanentEstablishmentEditor from '@js/components/unit/PermanentEstablishmentEditor.vue'
import Translator from '@js/translator'
import UnitEditor from '@js/components/unit/UnitEditor.vue'
import { useNotificationsStore } from '@js/stores/notifications'
import type { LegalUnit, OrganisationalGroup, PermanentEstablishment, Unit } from '@js/model/unit'
import type { Icon } from '@js/utilities/name-lists'

const route = useRoute()

const unitType = computed(() => route.query.type as string)

const unitIcon = computed<Icon>(() => {
  switch (unitType.value) {
    case 'unit':
      return 'unit'
    case 'legal-unit':
      return 'legal-unit'
    case 'permanent-establishment':
      return 'permanent-establishment'
    case 'organisational-group':
      return 'organisational-group'
    default:
      throw new Error(`Unknown unit type: ${unitType.value}`)
  }
})

useHead({
  title: computed(() => {
    switch (unitType.value) {
      case 'legal-unit':
        return `${Translator.trans('u2.new')} ${Translator.trans('u2.legal_unit')}`

      case 'permanent-establishment':
        return `${Translator.trans('u2.new')} ${Translator.trans('u2.permanent_establishment')}`

      case 'organisational-group':
        return `${Translator.trans('u2.new')} ${Translator.trans('u2.organisational_group')}`

      default:
        return `${Translator.trans('u2.new')} ${Translator.trans('u2.unit')}`
    }
  }),
})

const unitTypeReadable = computed(() => {
  switch (unitType.value) {
    case 'legal-unit':
      return Translator.trans('u2.legal_unit')
    case 'unit':
      return Translator.trans('u2.unit')
    case 'permanent-establishment':
      return Translator.trans('u2.permanent_establishment')
    case 'organisational-group':
      return Translator.trans('u2.organisational_group')
  }

  return Translator.trans('u2.n_a')
})

const router = useRouter()
const organisationalGroupEditor = useTemplateRef('organisationalGroupEditor')
const unitEditor = useTemplateRef('unitEditor')
const permanentEstablishmentEditor = useTemplateRef('permanentEstablishmentEditor')
const legalUnitEditor = useTemplateRef('legalUnitEditor')

const saveState = computed(() => {
  if (unitType.value === 'legal-unit') {
    return legalUnitEditor.value?.state
  }
  if (unitType.value === 'permanent-establishment') {
    return permanentEstablishmentEditor.value?.state
  }
  if (unitType.value === 'organisational-group') {
    return organisationalGroupEditor.value?.state
  }
  if (unitType.value === 'unit') {
    return unitEditor.value?.state
  }

  return 'ready'
})
const onSave = async (data: Unit | LegalUnit | OrganisationalGroup | PermanentEstablishment) => {
  await router.push({ name: 'UnitEdit', params: { id: data.id } })

  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle
            :title="Translator.trans('u2.new_given_unit_type', { unit_type: unitTypeReadable })"
          >
            <template #image>
              <SvgIcon :icon="unitIcon" size="x-large" class="rounded-sm border text-gray-500" />
            </template>
          </PageHeaderTitle>
        </template>

        <ButtonBasic
          icon="list"
          :to="{ name: 'UnitList' }"
          :tooltip="Translator.trans('u2.unit_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="unit" :state="saveState" />
      </PageHeader>
    </template>

    <OrganisationalGroupEditor
      v-if="unitType === 'organisational-group'"
      ref="organisationalGroupEditor"
      @saved="onSave"
    />

    <PermanentEstablishmentEditor
      v-if="unitType === 'permanent-establishment'"
      ref="permanentEstablishmentEditor"
      @saved="onSave"
    />

    <LegalUnitEditor v-if="unitType === 'legal-unit'" ref="legalUnitEditor" @saved="onSave" />

    <UnitEditor v-if="unitType === 'unit'" ref="unitEditor" @saved="onSave" />
  </AppPage>
</template>
