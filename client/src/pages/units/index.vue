<script setup lang="ts">
import { downloadUnitXls } from '@js/api/unitApi'
import { keepPreviousData, useQueries, useQuery } from '@tanstack/vue-query'
import { watchDebounced } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { saveAs } from 'file-saver'
import { computed, ref } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import AppChip from '@js/components/AppChip.vue'
import AppSkeleton from '@js/components/AppSkeleton.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import DropdownMenuDivider from '@js/components/buttons/DropdownMenuDivider.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppSelect from '@js/components/form/AppSelect.vue'
import LabelLoading from '@js/components/LabelLoading.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppTable from '@js/components/table/AppTable.vue'
import TableControls from '@js/components/table/TableControls.vue'
import ImportDialog from '@js/components/unit/ImportDialog.vue'
import NewByTypeDialog from '@js/components/unit/NewByTypeDialog.vue'
import UnitLabel from '@js/components/unit/UnitLabel.vue'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import useCurrenciesAllQuery from '@js/composable/useCurrenciesAllQuery'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import useTable from '@js/composable/useTable'
import getReadableName, { unitTypes } from '@js/model/unit'
import { queries } from '@js/query'
import { useAuthStore } from '@js/stores/auth'
import Translator from '@js/translator'
import { getIdFromIri } from '@js/utilities/api-resource'
import type { ApiResourceId } from '@js/types'
import type { TaxNumber } from '@js/model/taxNumber'
import type { Address } from '@js/model/address'

const route = useRoute()
const router = useRouter()
const isNewByTypeDialogOpen = ref(false)
const isNewImportDialogOpen = ref(false)
const authStore = useAuthStore()
const isUnitManager = computed(() => authStore.hasRole('ROLE_UNIT_MANAGER'))

useHead({ title: Translator.trans('u2.unit.plural') })
const { resolveNotification } = useHandleAxiosErrorResponse()
async function downloadXls() {
  try {
    const downloadResponse = await downloadUnitXls(apiQuery.value)
    saveAs(downloadResponse.data, 'unit-export.xlsx')
  } catch (error) {
    resolveNotification(error)
  }
}

const unitTypeOptions = [
  { id: 'legalunit', name: Translator.trans('u2.legal_unit') },
  {
    id: 'permanentestablishment',
    name: Translator.trans('u2.permanent_establishment'),
  },
  { id: 'organisationalgroup', name: Translator.trans('u2.organisational_group') },
]

const {
  columns,
  query,
  apiQuery,
  fromLocationQuery,
  toLocationQuery,
  sort,
  setSelectedColumns,
  changePage,
  changePageSize,
} = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.type'),
      id: 'type',
      hidden: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.id'),
      id: 'id',
      hidden: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.ref_id'),
      id: 'refId',
      required: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.name'),
      id: 'name',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.country'),
      id: 'country',
      selectedByDefault: true,
      type: 'country',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.currency'),
      id: 'currency',
    },

    // LU,PE,OG - START
    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.auditor'),
      id: 'auditor',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.tax_advisor'),
      id: 'taxAdvisor',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.valid_from'),
      id: 'validFrom',
      type: 'date',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.valid_to'),
      id: 'validTo',
      type: 'date',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.contact_user'),
      id: 'contactUser',
      type: 'user',
    },

    // LU,PE,OG - END

    // LU,PE - START

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.legal_name'),
      id: 'legalName',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.country_founded'),
      id: 'countryFounded',
      type: 'country',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.legal_form'),
      id: 'legalForm',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.group_parent_income_tax'),
      id: 'parentLegalUnitIncomeTax',
      type: 'unit',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_financial.group_parent_vat'),
      id: 'parentLegalUnitVat',
      type: 'unit',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.parent_legal_unit'),
      id: 'parentLegalUnit',
      type: 'unit',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.branch'),
      id: 'branch',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.vat_number'),
      id: 'vatNumber',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.register_number'),
      id: 'registerNumber',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.registry_place'),
      id: 'registryPlace',
    },

    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.billing_address'),
      id: 'billingAddress',
    },

    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.postal_address'),
      id: 'postalAddress',
    },

    // LU,PE - END

    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2.tax_numbers'),
      id: 'taxNumbers',
      type: 'list',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.created_by'),
      id: 'createdBy',
      type: 'user',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.created_at'),
      id: 'createdAt',
      type: 'datetime',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.updated_by'),
      id: 'updatedBy',
      type: 'user',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.updated_at'),
      id: 'updatedAt',
      type: 'datetime',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.description'),
      id: 'description',
      selectedByDefault: true,
    },

    {
      align: 'center',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.verified'),
      id: 'verified',
      type: 'boolean',
    },

    {
      align: 'center',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.number_of_attachments'),
      type: 'count',
      id: 'attachmentCount',
    },

    {
      name: '',
      id: 'actions',
      required: true,
    },
  ],
  { filter: { search: '', type: undefined } } as {
    filter: { search: string; type: string | undefined }
  },
  { cacheKey: 'unit' }
)

const { data: unitsData, isLoading } = useQuery({
  ...queries.units.list(apiQuery),
  placeholderData: keepPreviousData,
})
const totalItems = computed(() => unitsData.value?.['hydra:totalItems'] ?? 0)
const units = computed(() => unitsData.value?.['hydra:member'])

const { data: auditorsData, isLoading: auditorsIsLoading } = useQuery(
  queries.auditors.list({ pagination: false })
)
const auditors = computed(() => auditorsData.value?.['hydra:member'] ?? [])

const { data: legalFormsData, isLoading: legalFormsIsLoading } = useQuery(
  queries.legalForms.list({ pagination: false })
)
const legalForms = computed(() => legalFormsData.value?.['hydra:member'] ?? [])

const { data: branchesData, isLoading: branchesIsLoading } = useQuery(
  queries.branches.list({ pagination: false })
)
const branches = computed(() => branchesData.value?.['hydra:member'] ?? [])

const { countriesByIri, isLoading: countriesByIriIsLoading } = useCountriesAllQuery()
const { items: allCurrencies, isLoading: allCurrenciesIsLoading } = useCurrenciesAllQuery()

function addressToString(address: Address) {
  return (
    (address.line1 ? address.line1 + ', ' : '') +
    (address.line2 ? address.line2 + ', ' : '') +
    (address.line3 ? address.line3 + ', ' : '') +
    (address.postcode ? address.postcode + ' ' : '') +
    (address.city ? address.city + ', ' : '') +
    (address.state ? address.state + ', ' : '') +
    (address.country ? (countriesByIri.value.get(address.country)?.nameShort ?? '') : '')
  )
}

const pageUnitIris = computed(() => {
  const iris = new Set<ApiResourceId>()

  for (const unit of units.value ?? []) {
    if ('parentLegalUnitIncomeTax' in unit && unit.parentLegalUnitIncomeTax) {
      iris.add(unit.parentLegalUnitIncomeTax)
    }
    if ('parentLegalUnitVat' in unit && unit.parentLegalUnitVat) {
      iris.add(unit.parentLegalUnitVat)
    }
    if ('parentLegalUnit' in unit && unit.parentLegalUnit) {
      iris.add(unit.parentLegalUnit)
    }
  }

  return iris
})

const pageUnitQueryDefinitions = computed(() =>
  pageUnitIris.value.size
    ? Array.from(pageUnitIris.value).map((iri) => queries.units.single(getIdFromIri(iri)))
    : []
)

const pageUnitQueries = useQueries({ queries: pageUnitQueryDefinitions })

const pageUnitIrisToUnits = computed(() => {
  return new Map(pageUnitQueries.value?.map((result) => [result.data?.['@id'], result.data]) ?? [])
})

const pageUnitIdsToLoading = computed(() => {
  return new Map(
    pageUnitQueries.value?.map((result, index) => {
      const key = pageUnitQueryDefinitions.value[index].queryKey[2]
      return [key, result.isLoading]
    }) ?? []
  )
})

const tableItems = computed(() => {
  return units.value?.map((unit) => {
    return {
      ...unit,
      updatedBy: unit.updatedBy ? getIdFromIri(unit.updatedBy) : undefined,
      createdBy: unit.createdBy ? getIdFromIri(unit.createdBy) : undefined,
      country: unit.country ? countriesByIri.value.get(unit.country) : undefined,
      taxNumbers: unit.taxNumbers.map((taxNumber: TaxNumber) => taxNumber.value),
      currency: allCurrencies.value.find((item) => item['@id'] === unit.currency)?.iso4217code,
      legalForm: legalForms.value.find(
        (item) => 'legalForm' in unit && item['@id'] === unit.legalForm
      )?.name,
      auditor: auditors.value.find((item) => 'auditor' in unit && item['@id'] === unit.auditor)
        ?.name,
      taxAdvisor: auditors.value.find(
        (item) => 'taxAdvisor' in unit && item['@id'] === unit.taxAdvisor
      )?.name,
      branch: branches.value.find((item) => 'branch' in unit && item['@id'] === unit.branch)?.name,
      countryFounded:
        'countryFounded' in unit && unit.countryFounded
          ? countriesByIri.value.get(unit.countryFounded)
          : undefined,
      parentLegalUnitIncomeTax:
        'parentLegalUnitIncomeTax' in unit && unit.parentLegalUnitIncomeTax
          ? pageUnitIrisToUnits.value.get(unit.parentLegalUnitIncomeTax) || {
              '@id': unit.parentLegalUnitIncomeTax,
            }
          : undefined,
      parentLegalUnitVat:
        'parentLegalUnitVat' in unit && unit.parentLegalUnitVat
          ? pageUnitIrisToUnits.value.get(unit.parentLegalUnitVat) || {
              '@id': unit.parentLegalUnitVat,
            }
          : undefined,
      parentLegalUnit:
        'parentLegalUnit' in unit && unit.parentLegalUnit
          ? pageUnitIrisToUnits.value.get(unit.parentLegalUnit) || {
              '@id': unit.parentLegalUnit,
            }
          : undefined,
      postalAddress:
        'postalAddress' in unit && unit.postalAddress
          ? addressToString(unit.postalAddress)
          : undefined,
      billingAddress:
        'billingAddress' in unit && unit.billingAddress
          ? addressToString(unit.billingAddress)
          : undefined,
      contactUser:
        'contactUser' in unit && unit.contactUser ? getIdFromIri(unit.contactUser) : undefined,
    }
  })
})

watchDebounced(
  query,
  (newValue) => {
    router.push({ query: toLocationQuery(newValue) })
  },
  { deep: true, debounce: 400 }
)
const newQuery = fromLocationQuery(route.query)
if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
  query.value = newQuery
}

onBeforeRouteUpdate(async (to) => {
  const newQuery = fromLocationQuery(to.query)
  if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
    query.value = newQuery
  }
})
</script>

<template>
  <AppPageWide>
    <template #header>
      <PageHeader :title="Translator.trans('u2.unit.plural')">
        <ButtonNew
          :disabled="!isUnitManager"
          :tooltip="Translator.trans('u2.select_unit_type_to_create')"
          @click="isNewByTypeDialogOpen = true"
        />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              icon="upload"
              :disabled="!isUnitManager"
              :text="Translator.trans('u2.import.import')"
              @click="isNewImportDialogOpen = true"
            />
            <DropdownMenuDivider text="Export" />

            <ButtonDropdownItem text="Excel" @click="downloadXls" />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #beforeWideContent>
      <div class="flex gap-1">
        <AppSelect
          v-model="query.filter.type"
          class="w-96 max-w-full"
          name="UnitType"
          :placeholder="Translator.trans('u2.select_unit_type')"
          :options="unitTypeOptions"
        />
        <AppSearch
          v-model="query.filter.search"
          class="flex-grow"
          :placeholder="Translator.trans('u2.search')"
        />
      </div>

      <TableControls
        v-if="!isLoading"
        :headers="columns"
        :query="query"
        :selected="query.selectedColumns"
        :total-items="totalItems"
        @page-change="changePage"
        @page-size-change="changePageSize"
        @new-column-selection="setSelectedColumns"
      />
    </template>

    <AppLoader v-if="isLoading" class="mt-10" />
    <AppTable
      v-else
      :headers="columns"
      :query="query"
      :selected="query.selectedColumns"
      :items="tableItems ?? []"
      :total-items="totalItems"
      :has-controls="false"
      sticky-header
      @sort="sort"
    >
      <template #itemtype-count="{ value }">
        <AppChip color="gray">{{ value }}</AppChip>
      </template>

      <template #item-attachmentCount-header="{ header }">
        <SvgIcon icon="document" class="align-middle text-current" :title="header.name" />
      </template>

      <template #item-name="{ item }">
        <span class="font-bold">{{ item.name }}</span>
      </template>

      <template #item-refId="{ item }">
        <router-link
          class="flex items-center gap-1 hover:no-underline"
          :to="{ name: 'UnitEdit', params: { id: item.id } }"
        >
          <SvgIcon
            v-tooltip="getReadableName(item['@type'])"
            :icon="unitTypes.get(item['@type'])!.type"
            class="text-gray-500"
          />
          {{ item.refId }}
        </router-link>
      </template>

      <template #item-auditor="{ item }">
        <AppSkeleton v-if="auditorsIsLoading" />
        <span>{{ item.auditor }}</span>
      </template>

      <template #item-taxAdvisor="{ item }">
        <AppSkeleton v-if="auditorsIsLoading" />
        <span>{{ item.taxAdvisor }}</span>
      </template>

      <template #item-branch="{ item }">
        <AppSkeleton v-if="branchesIsLoading" />
        <span v-else>
          {{ item.branch }}
        </span>
      </template>

      <template #itemtype-country="{ value }">
        <AppSkeleton v-if="countriesByIriIsLoading" />
        <span v-else>{{ value?.nameShort }}</span>
      </template>

      <template #item-currency="{ item }">
        <AppSkeleton v-if="allCurrenciesIsLoading" />
        <span>{{ item.currency }}</span>
      </template>

      <template #item-legalForm="{ item }">
        <AppSkeleton v-if="legalFormsIsLoading" />
        <span>{{ item.legalForm }}</span>
      </template>

      <template #itemtype-unit="{ value }">
        <LabelLoading
          v-if="value && pageUnitIdsToLoading.get(getIdFromIri(value['@id']))"
          color="gray"
        />
        <UnitLabel v-if="value?.id" :unit="value" />
      </template>

      <template #item-actions="{ item }">
        <span class="flex justify-end">
          <ButtonEdit :to="{ name: 'UnitEdit', params: { id: item.id } }" />
        </span>
      </template>
    </AppTable>

    <NewByTypeDialog v-if="isNewByTypeDialogOpen" @close="isNewByTypeDialogOpen = false" />

    <ImportDialog v-if="isNewImportDialogOpen" @close="isNewImportDialogOpen = false" />
  </AppPageWide>
</template>
