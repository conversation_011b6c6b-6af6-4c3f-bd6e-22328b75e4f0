<script setup lang="ts">
import { deleteUnitHierarchyById } from '@js/api/unitHierarchyApi'
import { TabsContent, TabsList, TabsRoot, TabsTrigger } from 'reka-ui'
import { useQueryClient } from '@tanstack/vue-query'
import { useRouteQuery } from '@vueuse/router'
import { computed, nextTick, ref, toRef, watch } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { breakpointsTailwind, useBreakpoints, useConfirmDialog, useRefHistory } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import invariant from 'tiny-invariant'
import useDateFormat from '@js/composable/useDateFormat'
import { queries } from '@js/query'
import useUnitHierarchyStructureUpdateMutation from '@js/composable/useUnitHierarchyStructureUpdateMutation'
import useUnitHierarchyStructureQuery from '@js/composable/useUnitHierarchyStructureQuery'
import AppList from '@js/components/AppList.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import AppSortableTree from '@js/components/AppSortableTree.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDatePicker from '@js/components/buttons/ButtonDatePicker.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import AppSortableTreePool from '@js/components/AppSortableTreePool.vue'
import useUnitHierarchyQuery from '@js/composable/useUnitHierarchyQuery'
import { pascalToDashed } from '@js/utilities/string-formatter'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import useServerValidationErrors from '@js/composable/useServerValidationErrors'
import FormErrors from '@js/components/form/FormErrors.vue'
import extractApiErrors from '@js/helper/form/extractApiErrors'
import useUnitAllQuery from '@js/composable/useUnitAllQuery'
import type { TreeNode } from '@js/types'
import type {
  UnitHierarchy,
  UnitHierarchyStructure,
  UnitHierarchyStructureTreeNode,
} from '@js/model/unit_hierarchy'
import type { Icon } from '@js/utilities/name-lists'
import type { AnyBasicUnit, Unit } from '@js/model/unit'

const { getDashedDate, getLongDate } = useDateFormat()

const props = defineProps<{
  id: number
  date: Date
}>()

const router = useRouter()
const route = useRoute()

const unitHierarchyId = ref(toRef(props, 'id'))
const { data: unitHierarchyData } = useUnitHierarchyQuery(unitHierarchyId)
const unitHierarchy = computed(() => unitHierarchyData?.value as UnitHierarchy | undefined)

const date = computed(() => getDashedDate(props.date))
const { data: unitHierarchyStructure } = useUnitHierarchyStructureQuery(unitHierarchyId, date)
const { items: allUnits } = useUnitAllQuery()
const poolData = ref<Array<TreeNode>>([])
invariant(unitHierarchyStructure.value, 'unitHierarchyStructure is undefined')
const treeData = ref<Array<TreeNode>>(transformFromApiStructure(unitHierarchyStructure.value.tree))
function syncPoolData() {
  const usedUnitIds = (treeData.value?.flatMap(flattenTreeNode) ?? []).map((option) => option.id)
  poolData.value =
    allUnits.value
      ?.filter((unit: AnyBasicUnit) => !usedUnitIds.includes(unit.id))
      .map((unit) => ({
        id: unit.id,
        label: unit.refId + ' - ' + unit.name,
        icon: pascalToDashed(unit['@type']) as Icon,
        children: [],
      }))
      .sort((a, b) =>
        a.label.localeCompare(b.label, undefined, { numeric: true, sensitivity: 'base' })
      ) ?? []
}
syncPoolData()
watch(unitHierarchyStructure, (newValue: UnitHierarchyStructure) => {
  invariant(newValue, 'unitHierarchyStructure is undefined')
  treeData.value = transformFromApiStructure(newValue.tree) ?? []
  syncPoolData()
  nextTick(() => {
    clearTreeHistory()
  })
})

const toggleSelectNode = (item: TreeNode) => {
  const toggle = (node: TreeNode) => {
    node.isSelected = node.id === item.id ? !node.isSelected : false
    node.children.forEach(toggle)
  }
  treeData.value.forEach(toggle)
}
function deselectAll() {
  const toggle = (node: TreeNode) => {
    node.isSelected = false
    node.children.forEach(toggle)
  }
  treeData.value.forEach(toggle)
}

const addNodes = (nodes: Array<TreeNode>) => {
  if (nodes.length === 0) {
    return
  }

  const find = (nodes: Array<TreeNode>): TreeNode | undefined => {
    return nodes.reduce((result: TreeNode | undefined, node) => {
      if (node.isSelected === true) {
        result = node
      }
      return result || find(node.children)
    }, undefined)
  }

  const selectedNode = find(treeData.value)

  if (selectedNode) {
    selectedNode.children.push(...nodes)
    syncPoolData()
    return
  }
  treeData.value.push(...nodes)
  syncPoolData()
}

const breakpoints = useBreakpoints(breakpointsTailwind)
const smAndLarger = breakpoints.greaterOrEqual('sm')
const activeTab = ref<'structure' | 'pool' | 'changes'>(smAndLarger.value ? 'pool' : 'changes')

const selectedTab = useRouteQuery<string, string>(
  'selectedTab',
  smAndLarger.value ? 'pool' : 'structure'
)

watch(smAndLarger, () => {
  if (selectedTab.value === 'pool' && !smAndLarger.value) {
    selectedTab.value = 'structure'
    return
  }
  if (selectedTab.value !== 'structure') {
    return
  }
  selectedTab.value = smAndLarger.value ? 'pool' : 'structure'
})

useHead({
  title: computed(() =>
    Translator.trans('u2_core.edit_attributes_of_given_unit_hierarchy', {
      unit_hierarchy_name: unitHierarchy.value?.name,
    })
  ),
})

const longDate = computed(() => getLongDate(props.date))

function flattenTreeNode(node: TreeNode): Array<TreeNode> {
  return [{ ...node, children: [] }, ...node.children.flatMap(flattenTreeNode)]
}

function transformFromApiStructure(
  structureTree: Array<UnitHierarchyStructureTreeNode>
): Array<TreeNode> {
  const transform = (node: UnitHierarchyStructureTreeNode): TreeNode => {
    invariant(node.title && node.icon, 'Attr and data must be defined')
    return {
      label: node.title,
      id: node.id,
      icon: node.icon.replace('icon-', '') as Icon,
      children: node.children.length > 0 ? node.children.flatMap(transform) : [],
    }
  }
  return structureTree.flatMap(transform)
}

const changeList = computed(() => {
  return unitHierarchyStructure.value?.changeList
})

function resolveUnitsByIris(changeList?: Array<Unit['@id']>) {
  if (!changeList) {
    return []
  }
  return changeList
    .map((unitIri) => allUnits.value.find((unit) => unit['@id'] === unitIri))
    .filter((unit): unit is Unit => unit !== undefined)
}

const additions = computed(() => resolveUnitsByIris(changeList.value?.added))
const removals = computed(() => resolveUnitsByIris(changeList.value?.removed))
const moved = computed(() => resolveUnitsByIris(changeList.value?.moved))

const hasNoChanges = computed(
  () =>
    !changeList.value ||
    (changeList.value.added.length === 0 &&
      changeList.value.moved.length === 0 &&
      changeList.value.removed.length === 0)
)

const nextChangeDate = computed(() => unitHierarchyStructure.value?.nextChangeDate)
const lastChangeDate = computed(() => unitHierarchyStructure.value?.lastChangeDate)

const isConfirmationDialogOpen = ref(false)
const notificationsStore = useNotificationsStore()

function transformToApiStructure(
  structureTree: Array<TreeNode>
): Array<UnitHierarchyStructureTreeNode> {
  const transform = (node: TreeNode): UnitHierarchyStructureTreeNode => {
    return {
      id: node.id,
      children: node.children.length > 0 ? node.children.flatMap(transform) : [],
    }
  }

  return structureTree.flatMap(transform)
}

const { mutate, isPending: isSaving } = useUnitHierarchyStructureUpdateMutation()
const { resolveNotification } = useHandleAxiosErrorResponse()
const { errors, setErrors } = useServerValidationErrors<{ '': unknown }>()
async function save() {
  invariant(treeData.value, 'treeData is undefined')
  invariant(unitHierarchyStructure.value, 'unitHierarchyStructure is undefined')

  mutate(
    {
      unitHierarchyStructure: {
        ...unitHierarchyStructure.value,
        tree: JSON.stringify(transformToApiStructure(treeData.value)),
      },
      unitHierarchyId: unitHierarchyId.value,
    },
    {
      onSuccess: async () => {
        notificationsStore.addSuccess(Translator.trans('u2_core.success'))
        await nextTick(() => {
          clearTreeHistory()
        })
      },
      onError: async (error) => {
        await resolveNotification(error)
        invariant(isAxiosError(error) && error.response)
        setErrors(extractApiErrors(error.response))
      },
      onSettled: () => {
        isConfirmationDialogOpen.value = false
      },
    }
  )
}

const goToDate = (date: string | undefined) => {
  router.push({
    name: 'HierarchyEdit',
    params: { date, id: props.id },
    query: { selectedTab: selectedTab.value },
  })
}

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
async function deleteUnitHierarchy() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await deleteUnitHierarchyById(props.id)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'HierarchyList' })
    } catch (error) {
      await resolveNotification(error)
    }
  }
}

const {
  undo: undoTreeChanges,
  redo: redoTreeChanges,
  canUndo: canUndoTreeChanges,
  canRedo: canRedoTreeChanges,
  clear: clearTreeHistory,
} = useRefHistory(treeData, { deep: true })

function undo() {
  undoTreeChanges()
  syncPoolData()
}

function redo() {
  redoTreeChanges()
  syncPoolData()
}

const readOnly = ref(false)
const isInactive = computed(() => readOnly.value || isSaving.value)

const queryClient = useQueryClient()
onBeforeRouteUpdate(async (to) => {
  readOnly.value = true
  await Promise.all([
    queryClient.ensureQueryData(queries.unitHierarchies.single(+to.params.id)),
    queryClient.fetchQuery(
      queries.unitHierarchies.single(+to.params.id)._ctx.structure(to.params.date as string)
    ),
    queryClient.prefetchQuery(queries.units.all),
  ])
  readOnly.value = false
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle v-if="unitHierarchy" :subtitle="longDate" :title="unitHierarchy?.name" />
        </template>
        <ButtonBasic
          icon="list"
          :tooltip="Translator.trans('u2.unit_hierarchy_list')"
          :to="{ name: 'HierarchyList' }"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonDatePicker :date="date ?? undefined" @select="goToDate" />

        <span class="flex items-center whitespace-nowrap">
          <ButtonBasic
            icon="chevron-left"
            :grouped="true"
            :disabled="!lastChangeDate"
            :to="
              lastChangeDate
                ? {
                    name: 'HierarchyEdit',
                    params: { id, date: lastChangeDate },
                    query: { selectedTab },
                  }
                : undefined
            "
            :tooltip="
              lastChangeDate
                ? Translator.trans('u2.unit_hierarchy.structure.latest_previous_change', {
                    lastChangeDate: getLongDate(new Date(lastChangeDate)),
                  })
                : undefined
            "
          />

          <ButtonBasic
            :grouped="true"
            :disabled="route.params.date === getDashedDate(new Date())"
            :to="{
              name: 'HierarchyEdit',
              params: { id, date: getDashedDate(new Date()) },
              query: { selectedTab },
            }"
            :tooltip="Translator.trans('u2_core.show_current_date')"
          >
            {{ Translator.trans('u2.date.today') }}
          </ButtonBasic>

          <ButtonBasic
            data-testId="next-change-date"
            icon="chevron-right"
            :grouped="true"
            :disabled="!nextChangeDate"
            :to="
              nextChangeDate
                ? {
                    name: 'HierarchyEdit',
                    params: { id, date: nextChangeDate },
                    query: { selectedTab },
                  }
                : undefined
            "
            :tooltip="
              nextChangeDate
                ? Translator.trans('u2.unit_hierarchy.structure.next_change', {
                    nextChangeDate: getLongDate(new Date(nextChangeDate)),
                  })
                : undefined
            "
          />
        </span>

        <ButtonBasic
          icon="attributes"
          :tooltip="Translator.trans('u2_core.edit_attributes')"
          :to="{ name: 'HierarchyAttributesEdit', params: { id: id } }"
        >
          {{ Translator.trans('u2_core.attributes') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'HierarchyNew' }"
          :tooltip="Translator.trans('u2_core.add_new_hierarchy')"
        />

        <ButtonSpacer />

        <ButtonDelete @click="deleteUnitHierarchy" />

        <ButtonSave :disabled="isInactive" @click="isConfirmationDialogOpen = true" />
      </PageHeader>
    </template>

    <template #default>
      <FormErrors :errors="errors['tree'] as Array<string>" />

      <div class="flex justify-between gap-2">
        <div
          v-if="smAndLarger"
          class="min-w-[25%] flex-1 rounded-sm bg-gray-100 p-4 text-sm sm:text-base"
        >
          <div class="flex justify-end">
            <ButtonBasic icon="arrow-uturn-left" :disabled="!canUndoTreeChanges" @click="undo">
              <span v-if="smAndLarger">{{ Translator.trans('u2.undo') }}</span>
            </ButtonBasic>
            <ButtonBasic icon="arrow-uturn-right" :disabled="!canRedoTreeChanges" @click="redo">
              <span v-if="smAndLarger">{{ Translator.trans('u2.redo') }}</span>
            </ButtonBasic>
          </div>
          <AppSortableTree
            v-model="treeData"
            data-testid="unit-hierarchy-structure"
            drag-and-drop-group="unit-hierarchy-structure"
            class="h-full overflow-auto pt-1"
            :readonly="isInactive"
            @click-node="toggleSelectNode"
            @drag-start="deselectAll"
          />
        </div>

        <div
          class="sticky top-(--sticky-header-height) flex min-w-[50%] flex-1 flex-col sm:h-[calc(100vh-var(--sticky-header-height)-var(--app-header-height)-(--spacing(8)))]"
        >
          <TabsRoot v-model="activeTab" :default-value="smAndLarger ? 'pool' : 'changes'">
            <TabsList class="flex">
              <TabsTrigger value="structure" class="sm:hidden">
                <div
                  :class="[
                    'h-full px-4 py-3',
                    { 'rounded-t bg-gray-100': activeTab === 'structure' },
                    { 'hover:rounded-t hover:bg-gray-50': activeTab !== 'structure' },
                  ]"
                >
                  <span class="font-medium">{{ Translator.trans('u2_core.structure') }}</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="pool">
                <div
                  :class="[
                    'flex h-full items-center gap-x-2 px-4 py-3',
                    { 'rounded-t bg-gray-100': activeTab === 'pool' },
                    { 'hover:rounded-t hover:bg-gray-50': activeTab !== 'pool' },
                  ]"
                >
                  <SvgIcon v-if="smAndLarger" icon="inbox" size="large" class="text-gray-500" />
                  <span class="font-medium whitespace-nowrap">{{
                    Translator.trans('u2.unit_pool')
                  }}</span>
                </div>
              </TabsTrigger>
              <TabsTrigger value="changes">
                <div
                  :class="[
                    'flex h-full items-center gap-x-2 px-4 py-3',
                    { 'rounded-t bg-gray-100': activeTab === 'changes' },
                    { 'hover:rounded-t hover:bg-gray-50': activeTab !== 'changes' },
                  ]"
                >
                  <SvgIcon v-if="smAndLarger" icon="history" size="large" class="text-gray-500" />
                  <span class="font-medium">{{ Translator.trans('u2_core.changes') }}</span>
                </div>
              </TabsTrigger>
            </TabsList>
            <div
              :class="[
                {
                  'rounded-tl-none':
                    activeTab === 'structure' || (activeTab === 'pool' && smAndLarger),
                },
                'h-full rounded-sm bg-gray-100',
              ]"
            >
              <TabsContent as="template" value="structure">
                <div class="p-4">
                  <div class="flex justify-end">
                    <ButtonBasic
                      icon="arrow-uturn-left"
                      :disabled="!canUndoTreeChanges"
                      @click="undo"
                    />
                    <ButtonBasic
                      icon="arrow-uturn-right"
                      :disabled="!canRedoTreeChanges"
                      @click="redo"
                    />
                  </div>
                  <AppSortableTree
                    v-if="!smAndLarger"
                    v-model="treeData"
                    drag-and-drop-group="unit-hierarchy-structure"
                    class="h-full flex-1 overflow-auto text-sm sm:text-base"
                    :readonly="isInactive"
                  />
                </div>
              </TabsContent>

              <TabsContent as="template" value="pool">
                <AppSortableTreePool
                  v-model="poolData"
                  drag-and-drop-group="unit-hierarchy-structure"
                  class="h-full p-4"
                  data-testid="unit-pool"
                  :readonly="isInactive"
                  @select="addNodes"
                />
              </TabsContent>

              <TabsContent as="template" value="changes">
                <section class="h-full p-4">
                  <div class="mt-2 h-full overflow-auto">
                    <template v-if="changeList">
                      <template v-if="additions.length > 0">
                        <div class="font-medium text-gray-800">
                          {{
                            Translator.trans('u2_core.added_on_given_date', {
                              date: longDate,
                            })
                          }}
                        </div>
                        <AppList :items="additions" class="mt-1 list-none pl-2.5">
                          <template #default="{ listItem }">
                            <li class="leading-tight">
                              <div class="inline-flex items-center gap-x-1 text-sm text-gray-600">
                                <SvgIcon
                                  :icon="pascalToDashed(listItem['@type']!) as Icon"
                                  size="small"
                                />
                                {{ listItem.refId }} - {{ listItem.name }}
                              </div>
                            </li>
                          </template>
                        </AppList>
                      </template>
                      <template v-if="moved.length > 0">
                        <div class="font-medium text-gray-800">
                          {{
                            Translator.trans('u2_core.moved_on_given_date', {
                              date: longDate,
                            })
                          }}
                        </div>
                        <AppList :items="moved" class="mt-1 list-none pl-2.5">
                          <template #default="{ listItem }">
                            <li class="leading-tight">
                              <div class="inline-flex items-center gap-x-1 text-sm text-gray-600">
                                <SvgIcon
                                  :icon="pascalToDashed(listItem['@type']!) as Icon"
                                  size="small"
                                />
                                {{ listItem.refId }} - {{ listItem.name }}
                              </div>
                            </li>
                          </template>
                        </AppList>
                      </template>
                      <template v-if="removals.length > 0">
                        <div class="font-medium text-gray-800">
                          {{
                            Translator.trans('u2_core.removed_on_given_date', {
                              date: longDate,
                            })
                          }}
                        </div>
                        <AppList :items="removals" class="mt-1 list-none pl-2.5" compact>
                          <template #default="{ listItem }">
                            <li class="leading-tight">
                              <div class="inline-flex items-center gap-x-1 text-sm text-gray-600">
                                <SvgIcon
                                  :icon="pascalToDashed(listItem['@type']!) as Icon"
                                  size="small"
                                />
                                {{ listItem.refId }} - {{ listItem.name }}
                              </div>
                            </li>
                          </template>
                        </AppList>
                      </template>
                      <p v-if="hasNoChanges">
                        <em>{{
                          Translator.trans('u2_core.no_changes_on_given_date', {
                            date: longDate,
                          })
                        }}</em>
                      </p>
                    </template>
                    <AppLoader v-else />
                  </div>
                </section>
              </TabsContent>
            </div>
          </TabsRoot>
        </div>
      </div>

      <ConfirmationDialog
        v-if="isConfirmationDialogOpen"
        :accept-text="Translator.trans('u2.save')"
        :title="Translator.trans('u2.confirm')"
        @confirm="save"
        @close="isConfirmationDialogOpen = false"
      >
        {{
          Translator.trans('u2.save_unit_hierarchy_confirmation', {
            date: longDate,
          })
        }}
      </ConfirmationDialog>

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @close="cancelDelete"
        @confirm="confirmDelete"
      >
        {{ Translator.trans('u2_core.delete_entry.confirmation') }}
      </ConfirmationDialog>
    </template>
  </AppPage>
</template>
