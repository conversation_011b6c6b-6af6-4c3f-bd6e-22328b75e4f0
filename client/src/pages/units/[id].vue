<script setup lang="ts">
import AsideSection from '@js/components/AsideSection.vue'
import UnitAuditLogs from '@js/components/unit/UnitAuditLogs.vue'
import { computed, ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import SvgIcon from '@js/components/SvgIcon.vue'
import {
  isLegalUnit,
  isOrganisationalGroup,
  isPermanentEstablishment,
  isUnit,
} from '@js/model/unit'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityAttachmentsAside from '@js/components/entity/EntityAttachmentsAside.vue'
import LegalUnitEditor from '@js/components/unit/LegalUnitEditor.vue'
import NewByTypeDialog from '@js/components/unit/NewByTypeDialog.vue'
import OrganisationalGroupEditor from '@js/components/unit/OrganisationalGroupEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import PermanentEstablishmentEditor from '@js/components/unit/PermanentEstablishmentEditor.vue'
import Translator from '@js/translator'
import UnitAssignedUserGroupsAside from '@js/components/unit/UnitAssignedUserGroupsAside.vue'
import UnitAssignedUsersAside from '@js/components/unit/UnitAssignedUsersAside.vue'
import UnitEditor from '@js/components/unit/UnitEditor.vue'
import UnitInformationAside from '@js/components/unit/UnitInformationAside.vue'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import * as UnitApi from '@js/api/unitApi'
import type { Icon } from '@js/utilities/name-lists'
import type { AnyUnit } from '@js/model/unit'
import type { RouteLocation } from 'vue-router'
import { useAuthStore } from '@js/stores/auth'

const props = defineProps<{
  unit: AnyUnit
}>()
const { unit } = toRefs(props)

useHead({ title: `${unit.value.refId} ${unit.value.name} - ${unit.value['@type']}` })

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    to.meta.unit = (await UnitApi.fetchUnitById(Number(to.params.id))).data
  }
})

const isNewByTypeDialogOpen = ref(false as boolean)
function openNewByTypeDialog() {
  isNewByTypeDialogOpen.value = true
}

const unitIcon = computed<Icon>(() => {
  switch (props.unit['@type']) {
    case 'Unit':
      return 'unit'
    case 'LegalUnit':
      return 'legal-unit'
    case 'PermanentEstablishment':
      return 'permanent-establishment'
    case 'OrganisationalGroup':
      return 'organisational-group'
    default:
      throw new Error(`Unknown unit type: ${props.unit['@type']}`)
  }
})

const router = useRouter()
const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()

async function deleteUnit() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await UnitApi.deleteUnit(unit.value)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await router.push({ name: 'UnitList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

function onSave() {
  notificationsStore.addSuccess(Translator.trans('u2_core.success'))
}
const organisationalGroupEditor = useTemplateRef<InstanceType<typeof OrganisationalGroupEditor>>(
  'organisationalGroupEditor'
)
const unitEditor = useTemplateRef<InstanceType<typeof UnitEditor>>('unitEditor')
const permanentEstablishmentEditor = useTemplateRef<
  InstanceType<typeof PermanentEstablishmentEditor>
>('permanentEstablishmentEditor')

const legalUnitEditor = useTemplateRef<InstanceType<typeof LegalUnitEditor>>('legalUnitEditor')

const saveState = computed(() => {
  if (isLegalUnit(unit.value)) {
    return legalUnitEditor.value?.state
  }
  if (isPermanentEstablishment(unit.value)) {
    return permanentEstablishmentEditor.value?.state
  }
  if (isOrganisationalGroup(unit.value)) {
    return organisationalGroupEditor.value?.state
  }
  if (isUnit(unit.value)) {
    return unitEditor.value?.state
  }

  return 'ready'
})

const { hasRole } = useAuthStore()
const isUserGroupAdmin = computed(() => hasRole('ROLE_USER_GROUP_ADMIN'))
const canManageUnit = computed(() => hasRole('ROLE_UNIT_MANAGER'))
</script>

<template>
  <AppPageWithAside v-if="unit">
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle :title="Translator.trans('u2.unit')">
            <template #image>
              <SvgIcon :icon="unitIcon" size="x-large" class="rounded-sm border text-gray-500" />
            </template>
            <template #subtitle>
              <span class="text-gray-500">
                {{ unit.refId }}
              </span>
              {{ unit.name }}
            </template>
          </PageHeaderTitle>
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'UnitList' }"
          :tooltip="Translator.trans('u2.unit_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          id="button-new-unit-by-type"
          :disabled="!canManageUnit"
          :tooltip="Translator.trans('u2.select_unit_type_to_create')"
          @click="openNewByTypeDialog"
        />

        <ButtonSave form="unit" :state="saveState" />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              :disabled="!canManageUnit"
              icon="delete"
              :text="Translator.trans('u2.delete')"
              @click="deleteUnit"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #asideBefore>
      <UnitInformationAside :unit="unit" />
    </template>

    <OrganisationalGroupEditor
      v-if="isOrganisationalGroup(unit)"
      ref="organisationalGroupEditor"
      :unit="unit"
      @saved="onSave"
    />

    <PermanentEstablishmentEditor
      v-if="isPermanentEstablishment(unit)"
      ref="permanentEstablishmentEditor"
      :unit="unit"
      @saved="onSave"
    />

    <LegalUnitEditor v-if="isLegalUnit(unit)" ref="legalUnitEditor" :unit="unit" @saved="onSave" />

    <UnitEditor v-if="unit['@type'] === 'Unit'" ref="unitEditor" :unit="unit" @saved="onSave" />

    <template #asideAfter>
      <EntityAttachmentsAside :disabled="!unit['u2:extra']?.canAddAttachment" :resource="unit" />
      <template v-if="isUserGroupAdmin">
        <UnitAssignedUsersAside
          v-if="unit"
          id="authorised-users"
          :headline="Translator.trans('u2_core.assigned_users')"
          :unit="unit"
        />
        <UnitAssignedUserGroupsAside
          v-if="unit"
          id="authorised-user-groups"
          :unit="unit"
          :headline="Translator.trans('u2.assigned_user_groups')"
        />
      </template>
      <AsideSection
        icon="history"
        :headline="Translator.trans('u2_core.changes')"
        :collapsed="true"
        unmount-on-hide
      >
        <UnitAuditLogs :unit="unit" />
      </AsideSection>
    </template>

    <NewByTypeDialog v-if="isNewByTypeDialogOpen" @close="isNewByTypeDialogOpen = false" />

    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @confirm="confirmDelete"
      @close="cancelDelete"
    >
      {{ Translator.trans('u2_core.delete_entry.confirmation') }}
    </ConfirmationDialog>
  </AppPageWithAside>
</template>
