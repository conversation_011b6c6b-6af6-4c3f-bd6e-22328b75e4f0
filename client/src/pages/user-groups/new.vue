<script setup lang="ts">
import { useHead } from '@vueuse/head'
import { useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import AppMessage from '@js/components/AppMessage.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import UserGroupEditor from '@js/components/user-group/UserGroupEditor.vue'
import type { UserGroup } from '@js/model/userGroup'

const router = useRouter()
useHead({ title: Translator.trans('u2.new_user_group') })

const userGroupEditor = useTemplateRef('userGroupEditor')
const onSave = (userGroup: UserGroup) => {
  router.push({ name: 'UserGroupEdit', params: { id: userGroup.id } })
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2.new_user_group')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'UserGroupList' }"
          :tooltip="Translator.trans('u2_core.user_group_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="user_group" :state="userGroupEditor?.state" />
      </PageHeader>
    </template>

    <template #default>
      <UserGroupEditor ref="userGroupEditor" @saved="onSave" />
    </template>
    <template #asideAfter>
      <AsideSection icon="user" :headline="Translator.trans('u2_core.users')">
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection icon="unit" :headline="Translator.trans('u2.unit.plural')">
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection icon="unit" :headline="Translator.trans('u2_core.roles')">
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
