<script setup lang="ts">
import { fetchUserGroupById } from '@js/api/userGroupApi'
import { computed, ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import UserGroupAssignedAuthorizationsAside from '@js/components/user-group/UserGroupAssignedAuthorizationsAside.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import UserGroupAssignedRolesAside from '@js/components/user-group/UserGroupAssignedRolesAside.vue'
import UserGroupAssignedUnitsAside from '@js/components/user-group/UserGroupAssignedUnitsAside.vue'
import UserGroupAssignedUsersAside from '@js/components/user-group/UserGroupAssignedUsersAside.vue'
import UserGroupEditor from '@js/components/user-group/UserGroupEditor.vue'
import { useUserGroupStore } from '@js/stores/user-group'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{
  id: number
}>()
const userGroup = ref()
const router = useRouter()
useHead({
  title: computed(() =>
    Translator.trans('u2_core.user_group_with_given_group_name', {
      group_name: userGroup.value?.name,
    })
  ),
})
async function deleteUserGroup() {
  const userGroupStore = useUserGroupStore()
  await userGroupStore.deleteUserGroup(userGroup.value.id)
  if (userGroupStore.getUserGroupById(userGroup.value.id)) {
    return
  }

  useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))
  await router.push({ name: 'UserGroupList' })
}
const authStore = useAuthStore()
const isUserGroupAdmin = computed(() => authStore.hasRole('ROLE_USER_GROUP_ADMIN'))

function onSave() {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}
const { id } = toRefs(props)
userGroup.value = (await fetchUserGroupById(id.value)).data

const userGroupEditor = useTemplateRef('userGroupEditor')

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    try {
      userGroup.value = (await fetchUserGroupById(Number(to.params.id))).data
    } catch (error) {
      if (
        !isAxiosError(error) ||
        (error.response && !(await handleAxiosErrorResponse(error.response)))
      ) {
        throw error
      }
    }
  }
})
</script>

<template>
  <AppPageWithAside v-if="userGroup">
    <template #header>
      <PageHeader
        :title="
          Translator.trans('u2_core.user_group_with_given_group_name', {
            group_name: userGroup.name,
          })
        "
      >
        <ButtonBasic
          icon="list"
          :to="{ name: 'UserGroupList' }"
          :tooltip="Translator.trans('u2_core.user_group_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'UserGroupNew' }"
          :tooltip="Translator.trans('u2_core.add_new_user_group')"
        />

        <ButtonSpacer />

        <ButtonDelete id="button-user-group-delete" :confirm="true" @click="deleteUserGroup" />

        <ButtonSave form="user_group" :state="userGroupEditor?.state" />
      </PageHeader>
    </template>

    <template #default>
      <UserGroupEditor ref="userGroupEditor" :user-group="userGroup" @saved="onSave" />
    </template>
    <template #asideAfter>
      <UserGroupAssignedUsersAside
        v-if="userGroup"
        id="authorised-users"
        :disabled="!isUserGroupAdmin"
        :user-group="userGroup"
      />
      <UserGroupAssignedUnitsAside :disabled="!isUserGroupAdmin" :user-group="userGroup" />
      <UserGroupAssignedRolesAside :disabled="!isUserGroupAdmin" :user-group="userGroup" />
      <UserGroupAssignedAuthorizationsAside :user-group="userGroup" />
    </template>
  </AppPageWithAside>
</template>
