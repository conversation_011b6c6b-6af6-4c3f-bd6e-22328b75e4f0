<script setup lang="ts">
import AppError from '@js/pages/error/AppError.vue'
import Translator from '@js/translator'

defineProps<{ message?: string }>()
</script>

<template>
  <AppError code="404" :title="Translator.trans('u2.page_not_found')" icon="search">
    <p v-if="message">
      <strong>{{ message }}</strong>
    </p>

    <p>{{ Translator.trans('u2.http_404') }}</p>
  </AppError>
</template>
