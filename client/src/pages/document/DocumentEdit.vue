<script setup lang="ts">
import { downloadDocumentOecdReport } from '@js/api/documentApi'
import { deleteTaskByShortNameAndId } from '@js/api/taskApi'
import urlToShortName from '@js/assets/router/urlToShortName'
import type { Task } from '@js/model/task'
import { storeToRefs } from 'pinia'
import { computed, defineAsyncComponent, onUnmounted, ref } from 'vue'
import { isAxiosError } from 'axios'
import { saveAs } from 'file-saver'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import { useQueryClient } from '@tanstack/vue-query'
import invariant from 'tiny-invariant'
import DocumentSectionAddButton from '@js/components/document/DocumentSectionAddButton.vue'
import DocumentPageContent from '@js/components/document/DocumentPageContent.vue'
import { useDocumentStore } from '@js/stores/document'
import DocumentSection from '@js/components/document/DocumentSection.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AppSidebar from '@js/components/AppSidebar.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import DropdownMenuDivider from '@js/components/buttons/DropdownMenuDivider.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import InfoBox from '@js/components/InfoBox.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import ScrollTo from '@js/utilities/scroll-to'
import SvgIcon from '@js/components/SvgIcon.vue'
import TaskComments from '@js/components/task/TaskComments.vue'
import TaskInformationAside from '@js/components/task/TaskInformationAside.vue'
import TaskReviewAside from '@js/components/task/TaskReviewAside.vue'
import TransitionButton from '@js/components/task/TransitionButton.vue'
import Translator from '@js/translator'
import { useTaskInfoStore } from '@js/stores/task-info'
import { useNotificationsStore } from '@js/stores/notifications'
import { usePageStore } from '@js/stores/page'
import { useStatusStore } from '@js/stores/status'
import { useTaskStore } from '@js/stores/task'
import { useWorkflowStore } from '@js/stores/workflow'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import DocumentAppendixSection from '@js/components/document/DocumentAppendixSection.vue'
import { queries } from '@js/query'
import TableOfContents from '@js/components/document/TableOfContents.vue'

// Lazy-load dialogs
const BMFDialog = defineAsyncComponent(() => import('@js/components/BMFDialog.vue'))
const Elma5Dialog = defineAsyncComponent(() => import('@js/components/Elma5Dialog.vue'))

const router = useRouter()
const shortNameRouteParam = urlToShortName(router.currentRoute.value.fullPath)
const taskIdRouteParam = Number(router.currentRoute.value.params.id)

const taskInfoStore = useTaskInfoStore()
const taskStore = useTaskStore()
const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
// Suspense: load task info and task
try {
  await taskInfoStore.fetchTaskInformation(shortNameRouteParam, taskIdRouteParam)
  invariant(taskInfoStore.taskId, 'Task ID is not defined')
  await taskStore.fetchTaskById(taskInfoStore.taskId)
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

const { task } = storeToRefs(taskStore)
useHead(() => {
  return {
    title: task.value
      ? `${task.value['u2:extra'].readableTaskType} #${task.value['u2:extra'].taskTypeId}`
      : undefined,
  }
})

const statusStore = useStatusStore()

const documentStore = useDocumentStore()
documentStore.enabled = true

const queryClient = useQueryClient()
documentStore.fetchData().then(() => {
  // If we are going directly to an anchor (hash in the url), scroll to that anchor in the content
  if (window.location.hash) {
    ScrollTo.scrollTo(window.location.hash)
  }
})

const workflowStore = useWorkflowStore()
if (task.value) {
  workflowStore.fetchWorkflowByBindingId(task.value.taskType)
}

const hasSections = computed(() => documentStore.sections && documentStore.sections.length > 0)

const showBMFDialog = ref(false)
const showElmaDialog = ref(false)

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()

async function deleteTask(task: Task) {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await deleteTaskByShortNameAndId({
        id: task['u2:extra'].taskTypeId,
        shortName: task['u2:extra'].shortName,
      })
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))
      await router.push(task['u2:extra'].listPath)
    } catch (error) {
      await resolveNotification(error)
    }
  }
}

const pageStore = usePageStore()
async function refresh(task: Task) {
  await queryClient.invalidateQueries({
    queryKey: queries.document.editDocumentData({
      id: task['u2:extra'].taskTypeId,
      shortName: task['u2:extra'].shortName,
    }).queryKey,
  })

  pageStore.loading = true

  Promise.all([documentStore.fetchData(), taskInfoStore.refresh(), taskStore.refresh()]).finally(
    () => {
      pageStore.loading = false
    }
  )
}

const notificationsStore = useNotificationsStore()
async function downloadXml(task: Task) {
  try {
    const { data } = await downloadDocumentOecdReport(task['u2:extra'].taskTypeId)

    saveAs(new Blob([data.xml]), data.name)
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)

    if (error.response.data.violations) {
      error.response.data.violations.forEach((violation: { message: string }) => {
        notificationsStore.addError(violation.message)
      })
    }
  }
}

onUnmounted(() => {
  documentStore.enabled = false
})
</script>

<template>
  <AppSidebar v-if="!documentStore.isLoading" v-slot="{ isCollapsed }">
    <div v-if="isCollapsed" class="pt-3 pr-2 pl-2">
      <h3 class="text-center whitespace-nowrap">
        <SvgIcon icon="content" />
      </h3>
      <TableOfContents
        v-if="hasSections"
        class="print:hidden"
        :section-to-tree-position-map="documentStore.numberingBySectionId"
        :collapsed="true"
        @section-click="documentStore.expandWithParents($event)"
      />
    </div>
    <div v-else class="pt-3 pl-3 print:break-after-page">
      <div
        class="print:text-u2 hidden print:visible print:m-0 print:mb-3 print:block print:border-b print:border-gray-200 print:p-0 print:pb-3 print:text-4xl print:leading-tight print:font-bold"
      >
        {{ documentStore.documentName }}
      </div>
      <h3 class="print:hidden">{{ Translator.trans('u2_tpm.contents') }}</h3>
      <TableOfContents
        v-if="hasSections"
        :section-to-tree-position-map="documentStore.numberingBySectionId"
        :collapsed="false"
        @section-click="documentStore.expandWithParents($event)"
      />
    </div>
  </AppSidebar>
  <AppPageWithAside v-if="!documentStore.isLoading && task" class="mx-3.5">
    <template #header>
      <PageHeader
        :show-page-controls-placeholder="pageStore.loading"
        class="-ml-6 bg-white pl-(--main-content-padding) print:ml-0 print:bg-transparent print:pl-0"
      >
        <template #title>
          <EntityPageHeaderTitle
            :id="task['u2:extra'].taskTypeId"
            :title="task['u2:extra'].readableTaskType"
            :subtitle="documentStore.documentName"
          />
        </template>

        <TransitionButton
          v-if="statusStore.status"
          :status="statusStore.status"
          :task="task"
          :transitions="workflowStore.availableTransitions"
          @success="refresh"
        />

        <ButtonSpacer />

        <ButtonBasic icon="list" :to="task['u2:extra'].listPath">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonBasic
          :disabled="!task['u2:extra'].canViewConfiguration"
          :to="task['u2:extra'].editPath"
          icon="config"
          :tooltip="Translator.trans('u2.edit_configuration')"
        >
          {{ Translator.trans('u2.configuration') }}
        </ButtonBasic>

        <ButtonNew :disabled="!task['u2:extra'].canWrite" :to="task['u2:extra'].newPath" />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              icon="delete"
              :disabled="!task['u2:extra'].canDelete"
              :text="Translator.trans('u2.delete')"
              @click="deleteTask(task)"
            />
            <DropdownMenuDivider text="Export" />
            <ButtonDropdownItem
              icon="document-pdf"
              :to="task['u2:extra'].pdfPath"
              target="_blank"
              text="PDF"
            />
            <ButtonDropdownItem
              icon="document"
              :to="task['u2:extra'].xmlPath"
              target="_blank"
              text="XML"
            />

            <div
              v-if="task['u2:extra'].shortName === 'tpm-country-by-country-report'"
              id="cbc-export-options"
            >
              <ButtonDropdownItem icon="download" text="BMF" @click="showBMFDialog = true" />
              <ButtonDropdownItem
                icon="download"
                target="_blank"
                text="OECD"
                @click="downloadXml(task)"
              />
              <ButtonDropdownItem icon="download" text="ELMA5" @click="showElmaDialog = true" />
            </div>
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #asideBefore>
      <TaskInformationAside :task="task" class="print:ml-0" />
      <TaskReviewAside :task="task" class="print:ml-0" />
    </template>

    <template v-if="!documentStore.isLoading" #default>
      <DocumentPageContent>
        <template v-if="(documentStore.hierarchicalSections?.length ?? 0) > 0">
          <DocumentSectionAddButton
            :disabled="!task['u2:extra'].canEditConfiguration"
            class="sm:-mt-6 md:-mt-9 print:hidden"
            @saved="refresh"
          />

          <DocumentSection
            v-for="section in documentStore.hierarchicalSections"
            :key="section.section.id"
            :hierarchical-section="section"
            :user-can-edit-configuration="documentStore.userCanEditConfiguration ?? false"
            :user-can-edit-content="documentStore.userCanEditContent ?? false"
            @saved="refresh"
          />

          <DocumentAppendixSection
            id="section-attachments"
            :readonly="true"
            :document-id="task['u2:extra'].taskTypeId"
            :short-name="task['u2:extra'].shortName"
          />
        </template>

        <InfoBox
          v-else
          :title="Translator.trans('u2_tpm.this_document_has_no_content')"
          icon="document-empty"
        >
          <ButtonBasic
            class="text-base"
            icon="add"
            :disabled="!documentStore.userCanEditContent"
            @click="
              async () => {
                if (await documentStore.createInitialSection()) {
                  await refresh(task)
                }
              }
            "
          >
            {{ Translator.trans('u2_tpm.add_first_section') }}
          </ButtonBasic>
        </InfoBox>
      </DocumentPageContent>

      <TaskComments id="comments" />

      <BMFDialog
        v-if="showBMFDialog"
        :entity-id="task['u2:extra'].taskTypeId"
        @close="showBMFDialog = false"
      />

      <Elma5Dialog
        v-if="showElmaDialog"
        :country-by-country-report-id="task['u2:extra'].taskTypeId"
        @close="showElmaDialog = false"
      />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{ Translator.trans('u2_core.delete_entry.confirmation') }}
      </ConfirmationDialog>
    </template>
  </AppPageWithAside>
</template>
