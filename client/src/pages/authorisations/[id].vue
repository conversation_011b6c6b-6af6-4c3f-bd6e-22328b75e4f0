<script setup lang="ts">
import * as AuthorizationApi from '@js/api/authorizationApi'
import invariant from 'tiny-invariant'
import { ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AuthorizationAssignedUserGroupsAside from '@js/components/user/AuthorizationAssignedUserGroupsAside.vue'
import AuthorizationAssignedUsersAside from '@js/components/user/AuthorizationAssignedUsersAside.vue'
import AuthorizationEditor from '@js/components/user/AuthorizationEditor.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Authorization } from '@js/model/authorization'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{
  id: number
}>()
useHead({ title: () => `${Translator.trans('u2_core.authorisation.authorisation')} #${props.id}` })

const authorization = ref<Authorization>()

const router = useRouter()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()

async function deleteAuthorization() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await AuthorizationApi.deleteAuthorizationById(props.id)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      await router.push({ name: 'AuthorizationList' })
    } catch (error) {
      await resolveNotification(error)
    }
  }
}

const notificationsStore = useNotificationsStore()
function onSave() {
  notificationsStore.addSuccess(
    Translator.trans('u2_core.authorisation.update_authorisation.success')
  )

  invariant(authorization.value)
  router.push({ name: 'AuthorizationEdit', params: { id: authorization.value.id } })
}

const authorizationEditor = useTemplateRef('authorizationEditor')

const { id } = toRefs(props)

try {
  authorization.value = (await AuthorizationApi.fetchAuthorizationById(id.value)).data
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    try {
      authorization.value = (
        await AuthorizationApi.fetchAuthorizationById(Number(to.params.id))
      ).data
    } catch (error) {
      if (
        !isAxiosError(error) ||
        (error.response && !(await handleAxiosErrorResponse(error.response)))
      ) {
        throw error
      }
    }
  }
})
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle
            :id="id"
            :title="Translator.trans('u2_core.authorisation.authorisation')"
          />
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'AuthorizationList' }"
          :tooltip="Translator.trans('u2_core.authorisation.authorisation_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'AuthorizationNew' }"
          :tooltip="Translator.trans('u2_core.add_new_period')"
        />

        <ButtonSpacer />

        <ButtonSave :state="authorizationEditor?.state" form="authorization" />

        <ButtonSpacer />
        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              icon="delete"
              :text="Translator.trans('u2.delete')"
              @click="deleteAuthorization"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #default>
      <AuthorizationEditor
        v-if="authorization"
        ref="authorizationEditor"
        :authorization="authorization"
        @saved="onSave"
      />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{ Translator.trans('u2_core.delete_entry.confirmation') }}
      </ConfirmationDialog>
    </template>
    <template v-if="authorization" #asideAfter>
      <AuthorizationAssignedUsersAside
        v-if="authorization"
        id="authorised-users"
        :headline="Translator.trans('u2_core.authorisation.authorised_users')"
        :authorization="authorization"
      />
      <AuthorizationAssignedUserGroupsAside
        v-if="authorization"
        id="authorised-user-groups"
        :headline="Translator.trans('u2_core.authorisation.authorised_groups')"
        :authorization="authorization"
      />
    </template>
  </AppPageWithAside>
</template>
