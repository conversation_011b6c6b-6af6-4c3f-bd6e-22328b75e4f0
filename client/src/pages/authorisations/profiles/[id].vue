<script setup lang="ts">
import * as AuthorizationProfileApi from '@js/api/authorizationProfileApi'
import invariant from 'tiny-invariant'
import { ref, toRefs, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AuthorizationProfileAssignedUserGroupsAside from '@js/components/user/AuthorizationProfileAssignedUserGroupsAside.vue'
import AuthorizationProfileAssignedUsersAside from '@js/components/user/AuthorizationProfileAssignedUsersAside.vue'
import AuthorizationProfileEditor from '@js/components/user/AuthorizationProfileEditor.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { AuthorizationProfile } from '@js/model/authorization'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{
  id: number
}>()
useHead({
  title: () => `${Translator.trans('u2_core.authorisation.authorisation_profile')} #${props.id}`,
})

const router = useRouter()
const authorizationProfile = ref<AuthorizationProfile>()
const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
async function deleteAuthorizationProfile() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await AuthorizationProfileApi.deleteAuthorizationProfileById(props.id)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await router.push({ name: 'AuthorizationList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const authorizationProfileEditor = useTemplateRef('authorizationProfileEditor')

function onSave() {
  notificationsStore.addSuccess(
    Translator.trans('u2_core.authorisation.update_authorisation_profile.success')
  )

  invariant(authorizationProfile.value)
  router.push({ name: 'AuthorizationProfileEdit', params: { id: authorizationProfile.value.id } })
}

const { id } = toRefs(props)

try {
  authorizationProfile.value = (
    await AuthorizationProfileApi.fetchAuthorizationProfileById(id.value)
  ).data
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

onBeforeRouteUpdate(async (to: RouteLocation, from: RouteLocation) => {
  if (to.params.id !== from.params.id) {
    try {
      authorizationProfile.value = (
        await AuthorizationProfileApi.fetchAuthorizationProfileById(props.id)
      ).data
    } catch (error) {
      if (
        !isAxiosError(error) ||
        (error.response && !(await handleAxiosErrorResponse(error.response)))
      ) {
        throw error
      }
    }
  }
})
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle
            :id="id"
            :title="Translator.trans('u2_core.authorisation.authorisation_profile')"
          />
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'AuthorizationList' }"
          :tooltip="Translator.trans('u2_core.authorisation.authorisation_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'AuthorizationProfileNew' }"
          :tooltip="Translator.trans('u2_core.add_new_period')"
        />

        <ButtonSpacer />

        <ButtonSave :state="authorizationProfileEditor?.state" form="authorization_profile" />

        <ButtonSpacer />
        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              icon="delete"
              :text="Translator.trans('u2.delete')"
              @click="deleteAuthorizationProfile"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #default>
      <AuthorizationProfileEditor
        v-if="authorizationProfile"
        ref="authorizationProfileEditor"
        :authorization-profile="authorizationProfile"
        @saved="onSave"
      />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{ Translator.trans('u2_core.delete_entry.confirmation') }}
      </ConfirmationDialog>
    </template>
    <template #asideAfter>
      <AuthorizationProfileAssignedUsersAside
        v-if="authorizationProfile"
        id="authorised-users"
        :headline="Translator.trans('u2_core.authorisation.authorised_users')"
        :authorization-profile="authorizationProfile"
      />
      <AuthorizationProfileAssignedUserGroupsAside
        v-if="authorizationProfile"
        id="authorised-user-groups"
        :authorization-profile="authorizationProfile"
        :headline="Translator.trans('u2_core.authorisation.authorised_groups')"
      />
    </template>
  </AppPageWithAside>
</template>
