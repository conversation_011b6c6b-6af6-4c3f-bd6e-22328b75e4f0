<script setup lang="ts">
import { useTemplateRef } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import AppMessage from '@js/components/AppMessage.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AsideSection from '@js/components/AsideSection.vue'
import AuthorizationProfileEditor from '@js/components/user/AuthorizationProfileEditor.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import type { AuthorizationProfile } from '@js/model/authorization'

const router = useRouter()
const notificationsStore = useNotificationsStore()
useHead({ title: Translator.trans('u2_core.authorisation.add_authorisation_profile') })
const onSave = (authorizationProfile: AuthorizationProfile) => {
  notificationsStore.addSuccess(
    Translator.trans('u2_core.authorisation.create_authorisation_profile.success')
  )

  router.push({ name: 'AuthorizationProfileEdit', params: { id: authorizationProfile.id } })
}
const authorizationProfileEditor = useTemplateRef('authorizationProfileEditor')
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.authorisation.add_authorisation_profile')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'AuthorizationList' }"
          :tooltip="Translator.trans('u2_core.authorisation.authorisation_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave :state="authorizationProfileEditor?.state" form="authorization_profile" />
      </PageHeader>
    </template>

    <template #asideBefore>
      <slot name="aside-before" />
    </template>
    <template #default>
      <AuthorizationProfileEditor ref="authorizationProfileEditor" @saved="onSave" />
    </template>
    <template #asideAfter>
      <AsideSection
        icon="user"
        :headline="Translator.trans('u2_core.authorisation.authorised_users')"
      >
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection
        icon="users"
        :headline="Translator.trans('u2_core.authorisation.authorised_groups')"
      >
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
