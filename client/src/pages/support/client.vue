<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useHead } from '@vueuse/head'
import AppPage from '@js/components/page-structure/AppPage.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import InformationGrid from '@js/components/InformationGrid.vue'
import InformationGridRow from '@js/components/InformationGridRow.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import InfoBox from '@js/components/InfoBox.vue'
import Translator from '@js/translator'
import { fetchAbout, fetchAppStatus } from '@js/api/appApi'

useHead({ title: Translator.trans('u2_core.client_system_information') })

const navigator = window.navigator
const appVersion = ref<string>('Loading...')
const apiStatus = ref<boolean | null>(null)
const apiLatency = ref<number | null>(null)

// Determine browser type for icon selection
const browserIcon = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  if (userAgent.includes('chrome')) return 'display'
  if (userAgent.includes('firefox')) return 'info'
  if (userAgent.includes('safari')) return 'info'
  if (userAgent.includes('edge')) return 'display'
  return 'display'
})

// Get browser name
const browserName = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  if (userAgent.includes('chrome')) return 'Chrome'
  if (userAgent.includes('firefox')) return 'Firefox'
  if (userAgent.includes('safari') && !userAgent.includes('chrome')) return 'Safari'
  if (userAgent.includes('edge')) return 'Edge'
  return 'Unknown Browser'
})

// Get OS name
const osName = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  if (userAgent.includes('win')) return 'Windows'
  if (userAgent.includes('mac')) return 'macOS'
  if (userAgent.includes('linux')) return 'Linux'
  if (userAgent.includes('android')) return 'Android'
  if (userAgent.includes('ios') || userAgent.includes('iphone') || userAgent.includes('ipad'))
    return 'iOS'
  return 'Unknown OS'
})

// Get screen information
const screenInfo = computed(() => {
  return {
    width: window.screen.width,
    height: window.screen.height,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
    colorDepth: window.screen.colorDepth,
    pixelDepth: window.screen.pixelDepth,
    pixelRatio: window.devicePixelRatio,
  }
})

// Get browser capabilities
const browserCapabilities = computed(() => {
  return {
    localStorage: typeof localStorage !== 'undefined',
    sessionStorage: typeof sessionStorage !== 'undefined',
    cookies: navigator.cookieEnabled,
    webSockets: typeof WebSocket !== 'undefined',
    webWorkers: typeof Worker !== 'undefined',
    serviceWorkers: 'serviceWorker' in navigator,
    indexedDB: typeof indexedDB !== 'undefined',
    webGL: (() => {
      try {
        const canvas = document.createElement('canvas')
        return !!(
          window.WebGLRenderingContext &&
          (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
        )
      } catch {
        return false
      }
    })(),
    webRTC: 'RTCPeerConnection' in window,
    language: navigator.language,
    languages: navigator.languages?.join(', ') || navigator.language,
  }
})

// Check API status
async function checkApiStatus() {
  try {
    const startTime = performance.now()
    await fetchAppStatus()
    apiLatency.value = Math.round(performance.now() - startTime)
    apiStatus.value = true
  } catch (error) {
    apiStatus.value = false
    console.error('API status check failed:', error)
  }
}

// Get app version
async function getAppVersion() {
  try {
    const response = await fetchAbout()
    appVersion.value = response.data.version || 'Unknown'
  } catch (error) {
    appVersion.value = 'Error fetching version'
    console.error('Failed to fetch app version:', error)
  }
}

onMounted(async () => {
  await Promise.all([checkApiStatus(), getAppVersion()])
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.client_system_information')">
        <ButtonBasic
          :to="{ name: 'SupportInfo' }"
          icon="info"
          :tooltip="Translator.trans('u2_core.back_to_the_support_information_page')"
        >
          {{ Translator.trans('u2_core.support') }}
        </ButtonBasic>
      </PageHeader>
    </template>

    <div class="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
      <!-- App Version Card -->
      <div>
        <InfoBox title="App Version" icon="info">
          {{ appVersion }}
        </InfoBox>
      </div>

      <!-- API Status Card -->
      <div>
        <InfoBox title="API Status" icon="info">
          <div class="flex items-center">
            <span v-if="apiStatus === null" class="text-gray-500">Checking...</span>
            <span v-else-if="apiStatus" class="text-good font-medium"
              >Connected ({{ apiLatency }}ms)</span
            >
            <span v-else class="text-bad font-medium">Disconnected</span>
          </div>
        </InfoBox>
      </div>
    </div>

    <!-- User Agent Card -->
    <div class="mt-6 rounded-lg border border-gray-100 bg-white p-6 shadow-xs">
      <div class="mb-4 flex items-center">
        <SvgIcon icon="display" size="large" class="mr-3 text-gray-400" />
        <h2 class="text-xl font-medium text-gray-700">
          {{ Translator.trans('u2_core.user_agent') }}
        </h2>
      </div>
      <div class="overflow-x-auto rounded-md bg-gray-50 p-4">
        <code class="text-sm break-all text-gray-600">{{ navigator.userAgent }}</code>
      </div>
    </div>

    <!-- Browser Capabilities Card -->
    <div class="mt-6 rounded-lg border border-gray-100 bg-white p-6 shadow-xs">
      <div class="mb-4 flex items-center">
        <SvgIcon icon="info" size="large" class="mr-3 text-gray-400" />
        <h2 class="text-xl font-medium text-gray-700">
          {{ Translator.trans('u2_core.further_browser_information') }}
        </h2>
      </div>

      <InformationGrid class="mt-2">
        <InformationGridRow label="Browser">
          <div class="flex items-center">
            <SvgIcon :icon="browserIcon" size="small" class="mr-2 text-gray-500" />
            <span>{{ browserName }}</span>
          </div>
        </InformationGridRow>
        <InformationGridRow :label="Translator.trans('u2_core.operating_system')">
          <div class="flex items-center">
            <SvgIcon icon="info" size="small" class="mr-2 text-gray-500" />
            <span>{{ osName }}</span>
          </div>
        </InformationGridRow>
        <InformationGridRow label="Language">
          <div class="flex items-center">
            <SvgIcon icon="info" size="small" class="mr-2 text-gray-500" />
            <span>{{ browserCapabilities.language }}</span>
          </div>
        </InformationGridRow>
        <InformationGridRow label="Online Status">
          <div class="flex items-center">
            <SvgIcon icon="info" size="small" class="mr-2 text-gray-500" />
            <span class="font-medium" :class="navigator.onLine ? 'text-good' : 'text-bad'">
              {{ navigator.onLine ? 'Online' : 'Offline' }}
            </span>
          </div>
        </InformationGridRow>
        <InformationGridRow :label="Translator.trans('u2_core.cookies_enabled')">
          <div class="flex items-center">
            <SvgIcon icon="info" size="small" class="mr-2 text-gray-500" />
            <span class="font-medium" :class="navigator.cookieEnabled ? 'text-good' : 'text-bad'">
              {{ navigator.cookieEnabled ? Translator.trans('u2.yes') : Translator.trans('u2.no') }}
            </span>
          </div>
        </InformationGridRow>
      </InformationGrid>
    </div>

    <!-- Screen Information Card -->
    <div class="mt-6 rounded-lg border border-gray-100 bg-white p-6 shadow-xs">
      <div class="mb-4 flex items-center">
        <SvgIcon icon="display" size="large" class="mr-3 text-gray-400" />
        <h2 class="text-xl font-medium text-gray-700">Screen Information</h2>
      </div>

      <InformationGrid class="mt-2">
        <InformationGridRow label="Resolution">
          <span>{{ screenInfo.width }} × {{ screenInfo.height }} pixels</span>
        </InformationGridRow>
        <InformationGridRow label="Available Resolution">
          <span>{{ screenInfo.availWidth }} × {{ screenInfo.availHeight }} pixels</span>
        </InformationGridRow>
        <InformationGridRow label="Pixel Ratio">
          <span>{{ screenInfo.pixelRatio }}</span>
        </InformationGridRow>
        <InformationGridRow label="Color Depth">
          <span>{{ screenInfo.colorDepth }} bits</span>
        </InformationGridRow>
      </InformationGrid>
    </div>

    <!-- Browser Capabilities Card -->
    <div class="mt-6 rounded-lg border border-gray-100 bg-white p-6 shadow-xs">
      <div class="mb-4 flex items-center">
        <SvgIcon icon="info" size="large" class="mr-3 text-gray-400" />
        <h2 class="text-xl font-medium text-gray-700">Browser Capabilities</h2>
      </div>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        <div
          v-for="(value, key) in browserCapabilities"
          :key="key"
          class="rounded-md bg-gray-50 p-3"
        >
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-600">{{ key }}</span>
            <span
              v-if="typeof value === 'boolean'"
              class="ml-2 rounded-full px-2 py-1 text-xs font-medium"
              :class="
                value
                  ? 'bg-good-transparent text-good-darker'
                  : 'bg-bad-transparent text-bad-darker'
              "
            >
              {{ value ? 'Yes' : 'No' }}
            </span>
            <span v-else class="ml-2 text-xs text-gray-500">{{ value }}</span>
          </div>
        </div>
      </div>
    </div>
  </AppPage>
</template>
