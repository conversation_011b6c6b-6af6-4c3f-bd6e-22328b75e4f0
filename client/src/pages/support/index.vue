<script setup lang="ts">
import { fetchSupportInfo } from '@js/api/supportInfoApi'
import { computed, onBeforeMount, ref } from 'vue'
import { useHead } from '@vueuse/head'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'

useHead({ title: Translator.trans('u2_core.support') })
const authStore = useAuthStore()
const isCurrentUserAdmin = computed(() => authStore.hasRole('ROLE_ADMIN'))
const supportInfoHtml = ref<string | null | undefined>()

async function loadData() {
  const { data: customSupportInfo } = await fetchSupportInfo()

  supportInfoHtml.value = customSupportInfo.html
}

onBeforeMount(async () => await loadData())
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.support')">
        <ButtonBasic
          :to="{ name: 'SupportClientInfo' }"
          icon="display"
          :tooltip="Translator.trans('u2_core.show_information_about_your_system')"
        >
          {{ Translator.trans('u2_core.client_information') }}
        </ButtonBasic>

        <template v-if="isCurrentUserAdmin">
          <ButtonSpacer />

          <ButtonEdit
            :to="{ name: 'SupportEdit' }"
            :tooltip="Translator.trans('u2_core.edit_the_support_information')"
            :show-text="true"
          />
        </template>
      </PageHeader>
    </template>

    <section class="support-info tinymce-html">
      <AppLoader v-if="supportInfoHtml === undefined" />

      <!-- eslint-disable-next-line vue/no-v-html -- the support info is purified on the backend -->
      <div v-else-if="supportInfoHtml" v-html="supportInfoHtml" />

      <div v-else-if="supportInfoHtml === null" style="text-align: center">
        <img style="height: 75px; width: 75px" src="/assets/img/u2-logo.svg?url" alt="U²" />

        <p>
          {{ Translator.trans('u2_core.go_to_support_platform') }}
          <br />
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>

        <address class="leading-normal">
          Universal Units GmbH<br />
          Uhlandstr. 14<br />
          10623 Berlin
        </address>

        <p>
          <a
            href="https://universalunits.com"
            :title="Translator.trans('u2_core.go_to_the_universal_units_website')"
            target="_blank"
          >
            universalunits.com
          </a>
        </p>
      </div>
    </section>
  </AppPage>
</template>

<style scoped>
@reference "@css/app.css";

:deep(.support-info) {
  color: theme('colors.gray.700');
  line-height: theme('lineHeight.loose');
  padding: 10px;

  @media print {
    font-size: 11pt !important;
  }

  /* Make the headers look like one lower as we can’t control which is going to be used */
  h1 {
    /* h2 */
    color: theme('colors.gray.700');
    font-size: theme('fontSize.3xl');
  }

  h2 {
    /* h3 */
    border-bottom: none;
    color: theme('colors.gray.700');
    font-size: theme('fontSize.2xl');
  }

  h3 {
    /* h4 */
    color: theme('colors.gray.500');
    font-size: theme('fontSize.xl');
  }

  h4 {
    /* h5 */
    color: theme('colors.gray.700');
    font-size: theme('fontSize.xl');
  }

  h5 {
    /* h6 */
    color: theme('colors.gray.400');
    font-size: theme('fontSize.xl');
    text-transform: uppercase;
  }

  p {
    line-height: inherit;
  }
}
</style>
