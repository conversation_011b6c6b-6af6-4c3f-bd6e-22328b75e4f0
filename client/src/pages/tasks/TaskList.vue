<script setup lang="ts">
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { useHead } from '@vueuse/head'
import { isAxiosError } from 'axios'
import { computed, ref } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import type { RouteLocation } from 'vue-router'
import { vClosePopper } from 'floating-vue'
import AppChip from '@js/components/AppChip.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import DropdownMenuDivider from '@js/components/buttons/DropdownMenuDivider.vue'
import AppCheckbox from '@js/components/form/AppCheckbox.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import PeriodStatus from '@js/components/period/PeriodStatus.vue'
import AppTable from '@js/components/table/AppTable.vue'
import UqlFilterControls from '@js/components/table/UqlFilterControls.vue'
import UqlTableControls from '@js/components/table/UqlTableControls.vue'
import BulkDeleteConfirmationDialog from '@js/components/task/list/BulkDeleteConfirmationDialog.vue'
import BulkTransitionDialog from '@js/components/task/list/BulkTransitionDialog.vue'
import SavedFilterControl from '@js/components/task/list/SavedFilterControl.vue'
import NewByTypeDialog from '@js/components/task/NewByTypeDialog.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import Routing from '@js/Routing'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import { useTaskListStore } from '@js/stores/task-list'
import { useTaskListInfoStore } from '@js/stores/task-list-info'
import Translator from '@js/translator'
import UserLabel from '@js/components/UserLabel.vue'
import type { TableHeader, TableQuery } from '@js/types'
import type { SortingDirection } from '@js/utilities/getNextSortingDirection'
import type { SavedFilter } from '@js/model/saved-filter'
import useLocaleNumberFormatter from '@js/composable/useLocaleNumberFormatter'

const route = useRoute()
const taskListInfoStore = useTaskListInfoStore()

const router = useRouter()
const taskListStore = useTaskListStore()
const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
try {
  await taskListStore.fetchData(route)
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}
const isBulkDeleteDialogOpen = ref(false)
const isBulkTransitionDialogOpen = ref(false)
const taskListInfo = computed(() => {
  return taskListInfoStore.taskListInfo
})
useHead(() => {
  return { title: taskListInfo.value?.itemPluralName }
})

const editRouteName = `${(route.name as string)?.slice(0, -4)}Edit`

const authStore = useAuthStore()

const notificationsStore = useNotificationsStore()
onBeforeRouteUpdate(async (to: RouteLocation) => await taskListStore.fetchData(to))
const headers = computed<Array<TableHeader>>(() => {
  return [
    {
      align: 'center',
      isSortable: false,
      id: 'select',
      required: true,
    },
    ...taskListStore.columns.map((column) => {
      return {
        id: column.id,
        type: column.type,
        name: column.label ? column.label : column.name,
        isSortable: column.sortable,
        hidden: column.hidden,
        label: column.label && column.label !== column.name ? column.name : undefined,
        unit: column.vars?.unit,
      }
    }),
    {
      isSortable: false,
      id: 'actions',
      required: true,
    },
  ]
})

const tableQuery = computed<TableQuery>(() => {
  return {
    page: taskListStore.pagination.currentPage,
    sort: Object.fromEntries(
      taskListStore.sort.map(({ column_id: columnId, direction }) => {
        return [columnId, direction as SortingDirection]
      })
    ),
    filter: {},
  }
})

function bulkDeleteConfirm() {
  if (taskListStore.selectedRecordIds.length <= 0) {
    notificationsStore.addWarning(Translator.trans('u2.no_records_selected_delete'))
    return
  }

  isBulkDeleteDialogOpen.value = true
}

function bulkEdit() {
  if (!taskListInfo.value) {
    return
  }
  if (taskListStore.selectedRecordIds.length === 1) {
    router.push({ name: editRouteName, params: { id: taskListStore.selectedRecordIds[0] } })
    return
  }
  if (taskListStore.selectedRecordIds.length <= 0) {
    notificationsStore.addWarning(Translator.trans('u2.no_records_selected_edit'))
    return
  }

  router.push({
    name: 'TaskBulkEdit',
    params: { shortName: taskListInfo.value.shortName },
    query: { selection: taskListStore.selectedRecordIds.join(',') },
  })
}

function bulkTransitionConfirm() {
  if (taskListStore.selectedRecordIds.length <= 0) {
    notificationsStore.addWarning(Translator.trans('u2.no_records_selected_transition'))
    return
  }

  isBulkTransitionDialogOpen.value = true
}

const isNewByTypeDialogOpen = ref(false)
const pageHeaderTitle = computed(() => {
  return taskListInfo.value
    ? `${taskListInfo.value.itemPluralName}${pageHeaderSubtitle.value ? ':' : ''}`
    : undefined
})
const pageHeaderSubtitle = computed(
  () => taskListInfo.value?.savedFilterInformation.savedFilter?.name
)

function selectRecord(itemId: number) {
  taskListStore.selectedRecordIds.push(itemId)
}

function deselectRecord(itemId: number) {
  taskListStore.selectedRecordIds = taskListStore.selectedRecordIds.filter(
    (item) => item !== itemId
  )
}
function selectAllVisibleRecords() {
  taskListStore.records.forEach((item) => selectRecord(item.Id))
}
function deselectAllVisibleRecords() {
  taskListStore.records.forEach((item) => deselectRecord(item.Id))
}
async function deselectAllRecords() {
  taskListStore.selectedRecordIds = []
  await router.push({
    query: {
      ...route.query,
      r: '',
    },
  })
}

async function handleFilterSaved(updatedSavedFilter: SavedFilter) {
  notificationsStore.addSuccess(
    Translator.trans('u2.saved_filter.success_the_given_filter_has_been_saved', {
      saved_filter_name: updatedSavedFilter.name,
    })
  )

  await router.push({
    query: {
      ...route.query,
      f: updatedSavedFilter.id.toString(),
    },
  })
}

const numberFormatter = useLocaleNumberFormatter()
</script>

<template>
  <AppPageWide v-if="taskListInfo">
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle :title="pageHeaderTitle">
            <template #subtitle>
              {{ pageHeaderSubtitle }}
              <span v-if="taskListInfo.savedFilterInformation?.changed" class="text-gray-500">
                ({{ Translator.trans('u2.edited') }})
              </span>
            </template>
          </PageHeaderTitle>
        </template>

        <ButtonNew
          v-if="taskListInfo.hasMultipleOptionsForNew"
          id="task-new-select-type"
          :disabled="!taskListInfo.canCreate"
          @click="isNewByTypeDialogOpen = true"
        />

        <ButtonNew
          v-else
          id="new-button"
          :to="{ path: taskListInfo.newPath }"
          :disabled="!taskListInfo.canCreate"
        />

        <button-spacer />

        <ButtonBasic icon="filter" :to="{ name: 'SavedFilterList' }">
          {{ Translator.trans('u2.saved_filters') }}
        </ButtonBasic>

        <SavedFilterControl @saved="handleFilterSaved" />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              v-if="taskListInfo.hasDocument"
              :disabled="!authStore.hasRole('ROLE_ADMIN')"
              icon="templates"
              :to="{ name: 'TemplateList' }"
              :tooltip="Translator.trans('u2_tpm.show_template_list')"
              :text="Translator.trans('u2_tpm.templates')"
            />

            <DropdownMenuDivider text="Bulk Actions" />

            <ButtonDropdownItem
              id="bulk-edit"
              icon="edit"
              :data-short-name="taskListInfo.shortName"
              :disabled="!taskListInfo.canWrite"
              :show-text="true"
              :tooltip="Translator.trans('u2.edit_selected_records')"
              :text="Translator.trans('u2.edit')"
              @click="bulkEdit"
            />

            <ButtonDropdownItem
              id="bulk-transition"
              icon="transition"
              :tooltip="Translator.trans('u2.transition_selected_records')"
              :text="Translator.trans('u2.transition')"
              @click="bulkTransitionConfirm"
            />

            <ButtonDropdownItem
              icon="delete"
              :disabled="!taskListInfo.canDelete"
              :tooltip="Translator.trans('u2.delete_selected_records')"
              :text="Translator.trans('u2.delete')"
              @click="bulkDeleteConfirm"
            />

            <ButtonDropdownItem
              icon="upload"
              :disabled="taskListInfo.hasDocument || !taskListInfo.canWrite"
              :to="{
                name: 'ImportStart',
                params: { configurationKeySlug: taskListInfo.shortName },
              }"
              :text="Translator.trans('u2.import.import')"
            />

            <DropdownMenuDivider text="Export" />

            <ButtonDropdownItem
              icon="document-csv"
              :to="Routing.generate('u2_tasktype_listcsv', { shortName: taskListInfo.shortName })"
              target="_blank"
              text="CSV"
            />
            <ButtonDropdownItem
              icon="document-json"
              :to="Routing.generate('u2_tasktype_listjson', { shortName: taskListInfo.shortName })"
              target="_blank"
              text="JSON"
            />
            <ButtonDropdownItem
              icon="document-xls"
              :to="Routing.generate('u2_tasktype_listxlsx', { shortName: taskListInfo.shortName })"
              target="_blank"
              text="Excel"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>
    <template #beforeWideContent>
      <UqlFilterControls class="print:hidden" />
      <UqlTableControls class="print:hidden" />
      <div class="hidden print:block" aria-hidden="true">
        <p>
          <strong>{{ Translator.trans('u2_core.filter_query') }}:</strong>
          <kbd class="font-mono">{{ taskListStore.advancedFilterState.uql }}</kbd>
        </p>
      </div>
    </template>
    <template #default>
      <AppTable
        :headers="headers"
        :query="tableQuery"
        :selected="taskListStore.selectedColumns"
        :items="taskListStore.records"
        sticky-header
        unique-item-key="Id"
        :has-controls="false"
        @sort="taskListStore.changeSort($event.property, $event.direction)"
      >
        <!-- header slots -->
        <template #item-ReviewCount-header="{ header }">
          <SvgIcon v-tooltip="header.name" icon="review" class="align-middle text-current" />
        </template>

        <template #item-Files-header="{ header }">
          <SvgIcon v-tooltip="header.name" icon="document" class="align-middle text-current" />
        </template>

        <template #item-select-header>
          <VDropdown>
            <ButtonBasic icon="config" color="black" class="m-auto print:hidden" />

            <template #popper>
              <div class="flex flex-col gap-1 p-1">
                <ButtonBasic v-close-popper button-style="text" @click="selectAllVisibleRecords">
                  {{ Translator.trans('u2_table.select_all_visible') }}
                </ButtonBasic>
                <ButtonBasic v-close-popper button-style="text" @click="deselectAllVisibleRecords">
                  {{ Translator.trans('u2_table.deselect_all_visible') }}
                </ButtonBasic>
                <ButtonBasic v-close-popper button-style="text" @click="deselectAllRecords">
                  {{ Translator.trans('u2_table.deselect_all') }}
                </ButtonBasic>
              </div>
            </template>
          </VDropdown>
        </template>

        <!-- header type slots -->

        <template #itemtype-currency-header="{ header }">
          <SvgIcon v-tooltip="header.name" icon="currency" class="align-middle text-current" />
        </template>

        <!-- cell slots -->

        <template #item-select="{ item }">
          <AppCheckbox
            :key="item.Id"
            :model-value="taskListStore.selectedRecordIds.includes(item.Id)"
            @update:model-value="$event ? selectRecord(item.Id) : deselectRecord(item.Id)"
          />
        </template>

        <template #item-actions="{ item }">
          <ButtonEdit :to="{ name: editRouteName, params: { id: item.Id } }" :show-text="false" />
        </template>

        <!-- cell type slots -->

        <template #itemtype-id="{ value }">
          <router-link v-if="value" :to="{ name: editRouteName, params: { id: value } }">
            {{ value }}
          </router-link>
        </template>

        <template #itemtype-count="{ value }">
          <AppChip color="gray">{{ value }}</AppChip>
        </template>

        <template #itemtype-currency="{ value }">
          <span v-if="value" v-tooltip="value.name">{{ value.iso }}</span>
        </template>

        <template #itemtype-money="{ value }">
          <template v-if="value">
            <em
              v-if="typeof value === 'object'"
              v-tooltip="Translator.trans('u2_core.exchange_rate_unavailable')"
            >
              {{ Translator.trans('u2_core.n_a') }}
            </em>
            <span v-else>{{ numberFormatter.format(value) }}</span>
          </template>
        </template>

        <template #itemtype-money_full="{ value }">
          {{
            value.amount ? numberFormatter.format(value.amount) : Translator.trans('u2_core.n_a')
          }}
          {{ value.currency }}
        </template>

        <template #itemtype-number="{ value }">
          <template v-if="value">
            {{ numberFormatter.format(value, '0,0') }}
          </template>
        </template>

        <template #itemtype-percentage="{ value }">
          <em
            v-if="typeof value === 'object'"
            v-tooltip="Translator.trans('u2_core.exchange_rate_unavailable')"
          >
            {{ Translator.trans('u2_core.n_a') }}
          </em>
          <span v-else>{{ numberFormatter.format(value * 100, '0.0000') }}</span>
        </template>

        <template #itemtype-period="{ value }">
          <PeriodStatus v-if="value" :period-name="value.name" :is-closed="value.closed" />
        </template>

        <template #itemtype-user="{ value }">
          <UserLabel v-if="value" :user="typeof value === 'object' ? value.id : value" />
        </template>

        <template #itemtype-workflow_status="{ value }">
          <StatusBadge v-if="value" :status="{ name: value.name, type: value.type }" />
          <SvgIcon
            v-else
            v-tooltip="Translator.trans('u2_core.workflow.no_status_has_been_set')"
            icon="no"
            size="small"
            class="align-text-top text-gray-700"
          />
        </template>
      </AppTable>

      <BulkTransitionDialog
        v-if="isBulkTransitionDialogOpen"
        :short-name="taskListInfo.shortName"
        @close="isBulkTransitionDialogOpen = false"
        @saved="taskListStore.legacyTableSubmit"
      />

      <BulkDeleteConfirmationDialog
        v-if="isBulkDeleteDialogOpen"
        :short-name="taskListInfo.shortName"
        @close="isBulkDeleteDialogOpen = false"
        @deleted="deselectAllRecords"
      />

      <NewByTypeDialog
        v-if="isNewByTypeDialogOpen"
        :new-path="taskListInfo.newPath"
        :options-for-new="taskListInfo.optionsForNew"
        @close="isNewByTypeDialogOpen = false"
      />
    </template>
  </AppPageWide>
</template>
