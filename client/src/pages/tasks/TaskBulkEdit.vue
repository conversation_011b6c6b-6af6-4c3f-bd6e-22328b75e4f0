<script setup lang="ts">
import { fetchTaskBulkEditForm } from '@js/api/taskApi'
import type { TaskShortName } from '@js/model/task'
import { computed, ref, useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { StatusCodes } from 'http-status-codes'
import { useHead } from '@vueuse/head'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import TwigComponent from '@js/components/form/TwigComponent.vue'
import useAjaxFormSubmit from '@js/composable/useAjaxFormSubmit'
import * as TaskApi from '@js/api/taskApi'

const router = useRouter()
// There is a navigation guard for this page that ensures the shortName param is a TaskShortName
const shortName = router.currentRoute.value.params.shortName as TaskShortName

const itemPluralName = ref()
const recordCount = ref()
const title = computed(() =>
  Translator.trans('u2_core.bulk_edit_given_item_with_given_count', {
    item_name: itemPluralName.value,
    count: recordCount.value,
  })
)
useHead({ title })

const html = ref()
const authorisationItem = ref()
const listPath = ref()
const newPath = ref()
const formName = ref()
const loading = ref(true)
async function loadData() {
  loading.value = true
  const selectionQuery = router.currentRoute.value.query.selection
  const selectedTaskTypeIds = Array.isArray(selectionQuery)
    ? selectionQuery.join(',')
    : selectionQuery
  try {
    const response = await fetchTaskBulkEditForm(shortName, selectedTaskTypeIds ?? '')

    if (response.status === StatusCodes.OK) {
      html.value = response.data.html
      authorisationItem.value = response.data.authorisationItem
      itemPluralName.value = response.data.itemPluralName
      recordCount.value = response.data.recordCount
      listPath.value = response.data.listPath
      newPath.value = response.data.newPath
      formName.value = response.data.formName
    } else {
      html.value = response.data.html
    }
  } catch (error) {
    if (isAxiosError(error) && error.response?.status === StatusCodes.FORBIDDEN) {
      await router.push({ name: 'Error403' })
      return
    }
    throw error
  } finally {
    loading.value = false
  }
}

loadData()

const bulkEditForm = useTemplateRef('bulkEditForm')
const { submitAjaxForm, isFormValid } = useAjaxFormSubmit(bulkEditForm, (formData) => {
  return TaskApi.bulkEditTask({
    shortName,
    formData,
  })
})

const isConfirmationDialogOpen = ref(false)
async function save() {
  loading.value = true
  if (isFormValid()) {
    const response = await submitAjaxForm()
    if (response.status === StatusCodes.OK) {
      await router.push({ path: listPath.value })
    } else if ('html' in response.data) {
      html.value = response.data.html
    }
    isConfirmationDialogOpen.value = false
  }
  loading.value = false
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader v-if="!loading" :title="title">
        <ButtonBasic v-if="listPath" icon="list" :to="{ path: listPath }">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonBasic
          icon="upload"
          :to="{ name: 'ImportStart', params: { configurationKeySlug: shortName } }"
        >
          {{ Translator.trans('u2.import.import') }}
        </ButtonBasic>

        <ButtonNew v-if="newPath" :to="{ path: newPath }" />

        <ButtonSpacer />

        <ButtonSave
          data-testid="button-bulk-edit-save"
          :disabled="loading"
          @click="isConfirmationDialogOpen = true"
        />
      </PageHeader>
    </template>

    <TwigComponent v-if="html" ref="bulkEditForm" :html="html" :name="formName" />
    <AppLoader v-else class="h-56" />

    <ConfirmationDialog
      v-if="isConfirmationDialogOpen"
      :title="Translator.trans('u2_core.confirm_bulk_edit')"
      :accept-text="Translator.trans('u2.save')"
      @close="isConfirmationDialogOpen = false"
      @confirm="save"
    >
      {{
        Translator.trans('u2_core.bulk_edit_given_amount_of_selected_records.confirmation', {
          count: recordCount,
        })
      }}
    </ConfirmationDialog>
  </AppPageWithAside>
</template>
