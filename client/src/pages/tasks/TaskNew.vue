<script setup lang="ts">
import urlToShortName from '@js/assets/router/urlToShortName'
import { computed, ref, useTemplateRef } from 'vue'
import { isAxiosError } from 'axios'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import invariant from 'tiny-invariant'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import * as TaskApi from '@js/api/taskApi'
import TaskEditForm from '@js/components/task/TaskEditForm.vue'
import Translator from '@js/translator'
import useDomSavePrompt from '@js/composable/useDomSavePrompt'
import { useTaskInfoStore } from '@js/stores/task-info'
import { useNotificationsStore } from '@js/stores/notifications'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import AsideSection from '@js/components/AsideSection.vue'
import AppMessage from '@js/components/AppMessage.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import { shortNameToTaskType, taskTypes } from '@js/model/task'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'

const router = useRouter()
const shortName = urlToShortName(router.currentRoute.value.fullPath)
const transactionType = router.currentRoute.value.query.type as string | undefined
const taskInfoStore = useTaskInfoStore()
const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
const hasError = ref(false)
try {
  await taskInfoStore.fetchTaskMetadata(shortName)
} catch (error) {
  hasError.value = true
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

const isDocumentTaskType = computed(() => {
  const currentTaskType = shortNameToTaskType(shortName)

  return (
    taskTypes.MasterFile === currentTaskType ||
    taskTypes.LocalFile === currentTaskType ||
    taskTypes.CountryByCountryReport === currentTaskType
  )
})

const listPath = computed(() => {
  return taskInfoStore.extra?.listPath
})
const title = computed((): string => {
  return (
    Translator.trans('u2.new_item_with_given_name', {
      item_name: taskInfoStore.readableName,
    }) + (transactionType !== undefined ? ` (${transactionType})` : '')
  )
})
const { clear: clearSavePrompt } = useDomSavePrompt()
useHead({ title })
const { formHtml, isFormDisabled, saveForm, saveState } = (function () {
  const formHtml = ref('')
  const isFormDisabled = ref(true)
  const isFetchingForm = ref(true)
  const isSavingForm = ref(false)
  async function fetchForm() {
    // Prevent fetching form if fetchTaskMetadata returned an error (e.g. 403)
    if (hasError.value) {
      return
    }
    isFetchingForm.value = true
    const { data } = await TaskApi.fetchTaskNewForm(shortName, transactionType)
    formHtml.value = data.html
    isFormDisabled.value = data.disabled
    isFetchingForm.value = false
  }
  fetchForm()
  const formRef = useTemplateRef<InstanceType<typeof TaskEditForm>>('formRef')
  const notificationsStore = useNotificationsStore()
  async function saveForm() {
    const form = formRef.value?.$el.querySelector('form')
    if ('reportValidity' in form && !form.checkValidity()) {
      form.reportValidity()
      return
    }
    try {
      isSavingForm.value = true
      const formData = new window.FormData(form)
      const response = await TaskApi.createTask(shortName, formData, transactionType)
      clearSavePrompt()
      await router.push(response.data.redirect)
      notificationsStore.addByType(response.data.messages)
    } catch (error) {
      await resolveNotification(error)
      invariant(isAxiosError(error) && error.response)
      formHtml.value = error.response.data.html
    } finally {
      isSavingForm.value = false
    }
  }
  const saveState = computed(() => {
    if (isFetchingForm.value) {
      return 'loading'
    }
    if (isSavingForm.value) {
      return 'saving'
    }
    return 'ready'
  })
  return {
    formHtml,
    isFormDisabled,
    saveForm,
    saveState,
  }
})()
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle :title="title" />
        </template>

        <ButtonBasic :to="{ path: listPath }" icon="list">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave :state="saveState" :disabled="isFormDisabled" @click="saveForm" />
      </PageHeader>
    </template>
    <template #default>
      <AppLoader v-if="saveState === 'loading'" class="h-96" />
      <TaskEditForm v-else :key="formHtml" ref="formRef" :html="formHtml" />
    </template>

    <template #asideBefore>
      <AsideSection :headline="Translator.trans('u2.information')" icon="info">
        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection icon="review" :headline="Translator.trans('u2_core.reviews')">
        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>

        <template #button>
          <ButtonBasic
            :disabled="true"
            icon="yes-ok"
            :tooltip="Translator.trans('u2.new_record.save_first')"
          />
        </template>
      </AsideSection>
    </template>

    <template #asideAfter>
      <AsideSection icon="list" :headline="Translator.trans('u2_core.workflow.checklist')">
        <template #button>
          <ButtonBasic :disabled="true" :tooltip="Translator.trans('u2.new_record.save_first')">
            {{ Translator.trans('u2_core.show') }}
          </ButtonBasic>
        </template>
        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection
        v-if="isDocumentTaskType"
        icon="user"
        :headline="Translator.trans('u2.user_permissions')"
      >
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>
        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection
        v-if="isDocumentTaskType"
        icon="users"
        :headline="Translator.trans('u2.group_permissions')"
      >
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>
        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection icon="document" :headline="`${Translator.trans('u2_core.attachments')} (0)`">
        <template #button>
          <ButtonNew
            button-style="text"
            :disabled="true"
            :show-text="false"
            :tooltip="Translator.trans('u2.new_record.save_first')"
          />
        </template>
        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
