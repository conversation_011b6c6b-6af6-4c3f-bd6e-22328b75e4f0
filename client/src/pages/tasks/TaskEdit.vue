<script setup lang="ts">
import { downloadDocumentOecdReport } from '@js/api/documentApi'
import * as TaskApi from '@js/api/taskApi'
import urlToShortName from '@js/assets/router/urlToShortName'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import DropdownMenuDivider from '@js/components/buttons/DropdownMenuDivider.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import EntityAttachmentsAside from '@js/components/entity/EntityAttachmentsAside.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import EntityPermissionsAside from '@js/components/entity/EntityPermissionsAside.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import TaskChecklistAside from '@js/components/task/TaskChecklistAside.vue'
import TaskComments from '@js/components/task/TaskComments.vue'
import TaskEditForm from '@js/components/task/TaskEditForm.vue'
import TaskInformationAside from '@js/components/task/TaskInformationAside.vue'
import TaskLayoutCollectionAside from '@js/components/task/TaskLayoutCollectionAside.vue'
import TaskReviewAside from '@js/components/task/TaskReviewAside.vue'
import TransitionButton from '@js/components/task/TransitionButton.vue'
import useAjaxFormSubmit from '@js/composable/useAjaxFormSubmit'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { PermissionMasks } from '@js/model/permission'
import { taskTypes } from '@js/model/task'
import { useCommentsStore } from '@js/stores/comments'
import { useNotificationsStore } from '@js/stores/notifications'
import { useStatusStore } from '@js/stores/status'
import { useTaskStore } from '@js/stores/task'
import { useTaskInfoStore } from '@js/stores/task-info'
import { useWorkflowStore } from '@js/stores/workflow'
import Translator from '@js/translator'
import { fetchStates } from '@js/types'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { isAxiosError } from 'axios'
import { saveAs } from 'file-saver'
import { StatusCodes } from 'http-status-codes'
import { storeToRefs } from 'pinia'
import invariant from 'tiny-invariant'
import { computed, defineAsyncComponent, ref, useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'

// Lazy-load dialogs
const BMFDialog = defineAsyncComponent(() => import('@js/components/BMFDialog.vue'))
const Elma5Dialog = defineAsyncComponent(() => import('@js/components/Elma5Dialog.vue'))
const NewByTypeDialog = defineAsyncComponent(
  () => import('@js/components/task/NewByTypeDialog.vue')
)
const TaskDuplicateDialog = defineAsyncComponent(
  () => import('@js/components/task/TaskDuplicateDialog.vue')
)

const router = useRouter()
const shortName = urlToShortName(router.currentRoute.value.fullPath)
const taskTypeId = Number(router.currentRoute.value.params.id)
const taskInfoStore = useTaskInfoStore()
const taskStore = useTaskStore()
const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
// Suspense: load task info and task
try {
  await taskInfoStore.fetchTaskInformation(shortName, taskTypeId)
  invariant(taskInfoStore.taskId, 'Task ID is not defined')
  await taskStore.fetchTaskById(taskInfoStore.taskId)
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

const { task } = storeToRefs(taskStore)

useHead(() => {
  return {
    title: task.value
      ? `${task.value['u2:extra'].displayName} - ${task.value['u2:extra'].readableTaskType}`
      : undefined,
  }
})

const workflowStore = useWorkflowStore()
if (task.value) {
  workflowStore.fetchWorkflowByBindingId(task.value.taskType)
}

const commentsStore = useCommentsStore()
commentsStore.fetchComments()

const { fetchForm, formHtml, isFormDisabled, saveForm, saveState } = (function () {
  const formHtml = ref('')
  const isFormDisabled = ref(true)
  const isFetchingForm = ref(true)

  async function fetchForm() {
    if (!task.value) {
      return
    }
    isFetchingForm.value = true
    const { data } = await TaskApi.fetchTaskEditForm({
      shortName: task.value['u2:extra'].shortName,
      id: task.value['u2:extra'].taskTypeId,
    })
    formHtml.value = data.html
    isFormDisabled.value = data.disabled
    isFetchingForm.value = false
  }

  fetchForm()

  const form = useTemplateRef<InstanceType<typeof TaskEditForm>>('form')
  const {
    submitAjaxForm,
    isFormValid,
    isSubmitting: isSavingForm,
  } = useAjaxFormSubmit(form, (formData) => {
    invariant(task.value?.['u2:extra'])
    return TaskApi.updateTask({
      shortName: task.value['u2:extra'].shortName,
      id: task.value['u2:extra'].taskTypeId,
      formData,
    })
  })

  async function saveForm() {
    if (!isFormValid()) {
      return
    }
    const response = await submitAjaxForm()
    if (response.status === StatusCodes.OK) {
      refresh()
      return
    }
    formHtml.value = response.data.html
  }

  const saveState = computed(() => {
    if (isFetchingForm.value) {
      return 'loading'
    }
    if (isSavingForm.value) {
      return 'saving'
    }

    return 'ready'
  })

  return {
    fetchForm,
    formHtml,
    isFormDisabled,
    saveForm,
    saveState,
  }
})()

const isPageControlLoading = computed(
  () => workflowStore.fetchStates.transitions !== fetchStates.resolved
)

const showBMFDialog = ref(false)
const showElmaDialog = ref(false)
const isNewByTypeDialogOpen = ref(false)
const isDuplicateDialogOpen = ref(false)

const statusStore = useStatusStore()

const newPath = computed(() => task.value?.['u2:extra'].newPath)
const optionsForNew = computed(() => taskInfoStore.optionsForNew)
const isTaskWithLayouts = computed(() => task.value?.taskType === taskTypes.UnitPeriod)

function refresh() {
  if (!task.value) {
    return
  }

  taskInfoStore.refresh()
  taskStore.refresh()
  fetchForm()
  workflowStore.fetchWorkflowByBindingId(task.value.taskType)
  commentsStore.fetchComments()
}

const { resolveNotification } = useHandleAxiosErrorResponse()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const notificationsStore = useNotificationsStore()
async function deleteTask() {
  if (!task.value) {
    return
  }
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await TaskApi.deleteTaskByShortNameAndId({
        shortName: task.value['u2:extra'].shortName,
        id: task.value['u2:extra'].taskTypeId,
      })
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await router.push(task.value['u2:extra'].listPath)
    } catch (error) {
      await resolveNotification(error)
    }
  }
}

async function downloadXml() {
  if (!task.value) {
    return
  }

  try {
    const { data } = await downloadDocumentOecdReport(task.value['u2:extra'].taskTypeId)

    saveAs(new Blob([data.xml]), data.name)
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)

    if (error.response.data.violations) {
      error.response.data.violations.forEach((violation: { message: string }) => {
        notificationsStore.addError(violation.message)
      })
    }
  }
}
</script>

<template>
  <AppPageWithAside v-if="task">
    <template #header>
      <PageHeader :show-page-controls-placeholder="isPageControlLoading">
        <template #title>
          <EntityPageHeaderTitle
            :id="task['u2:extra'].taskTypeId"
            :title="task['u2:extra'].readableTaskType"
          />
        </template>

        <TransitionButton
          v-if="statusStore.status"
          :status="statusStore.status"
          :task="task"
          :transitions="workflowStore.availableTransitions"
          @success="refresh"
        />

        <ButtonSpacer />

        <ButtonBasic :to="{ path: task['u2:extra'].listPath }" icon="list">
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonBasic
          v-if="task['u2:extra'].hasDocument"
          :to="task['u2:extra'].editDocumentPath"
          icon="content"
          :tooltip="Translator.trans('u2.edit_content')"
          :disabled="!task['u2:extra'].canViewDocument"
        >
          {{ Translator.trans('u2.content') }}
        </ButtonBasic>

        <ButtonNew
          v-if="task['u2:extra'].hasMultipleOptionsForNew"
          :disabled="!task['u2:extra'].canWrite"
          @click="isNewByTypeDialogOpen = true"
        />

        <ButtonNew
          v-else
          id="task-new"
          :to="task['u2:extra'].newPath"
          :disabled="!task['u2:extra'].canWrite"
        />

        <ButtonSave
          id="task-save"
          :state="saveState"
          :disabled="isFormDisabled"
          @click="saveForm"
        />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              id="task-duplicate"
              icon="duplicate"
              :disabled="!task['u2:extra'].canWrite"
              :text="Translator.trans('u2.duplicate')"
              @click="isDuplicateDialogOpen = true"
            />
            <ButtonDropdownItem
              id="task-delete"
              icon="delete"
              :disabled="!task['u2:extra'].canDelete"
              :text="Translator.trans('u2.delete')"
              @click="deleteTask"
            />
            <template v-if="task['u2:extra'].hasDocument">
              <DropdownMenuDivider text="Export" />
              <ButtonDropdownItem
                icon="document-pdf"
                :to="task['u2:extra'].pdfPath"
                target="_blank"
                text="PDF"
              />
              <ButtonDropdownItem
                icon="document"
                :to="task['u2:extra'].xmlPath"
                target="_blank"
                text="XML"
              />

              <div
                v-if="task['u2:extra'].shortName === 'tpm-country-by-country-report'"
                id="cbc-export-options"
              >
                <ButtonDropdownItem icon="download" text="BMF" @click="showBMFDialog = true" />
                <ButtonDropdownItem
                  icon="download"
                  target="_blank"
                  text="OECD"
                  @click="downloadXml"
                />
                <ButtonDropdownItem icon="download" text="ELMA5" @click="showElmaDialog = true" />
              </div>
            </template>
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>
    <template #asideBefore>
      <TaskInformationAside :task="task" />
      <TaskLayoutCollectionAside v-if="isTaskWithLayouts" :task="task" />
      <TaskReviewAside :task="task" />
    </template>
    <template #default>
      <AppLoader v-if="saveState === 'loading'" class="h-96" />

      <TaskEditForm v-else :key="formHtml" ref="form" :html="formHtml" />

      <TaskComments id="comments" />

      <!-- Dialogs -->
      <NewByTypeDialog
        v-if="isNewByTypeDialogOpen && newPath"
        :new-path="newPath"
        :options-for-new="optionsForNew ?? {}"
        @close="isNewByTypeDialogOpen = false"
      />

      <Elma5Dialog
        v-if="showElmaDialog"
        :country-by-country-report-id="task['u2:extra'].taskTypeId"
        @close="showElmaDialog = false"
      />

      <BMFDialog
        v-if="showBMFDialog"
        :entity-id="task['u2:extra'].taskTypeId"
        @close="showBMFDialog = false"
      />

      <TaskDuplicateDialog
        v-if="isDuplicateDialogOpen"
        :task-name="task['u2:extra'].name"
        :task-id="task.id"
        @close="isDuplicateDialogOpen = false"
      />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{
          Translator.trans('u2_core.delete_given_entity_type_with_given_name.confirmation', {
            entity_type_name: task['u2:extra'].readableTaskType,
            entity_name: String(task['u2:extra'].taskTypeId),
          })
        }}
      </ConfirmationDialog>
    </template>
    <template #asideAfter>
      <TaskChecklistAside />

      <EntityPermissionsAside
        v-if="task['u2:extra'].hasDocument && task['u2:extra'].canViewDocumentPermissions"
        :resource="task"
        :edit-permission-mask="PermissionMasks.MANAGE"
        @updated="refresh"
      />

      <EntityAttachmentsAside :disabled="!task['u2:extra'].canAttach" :resource="task" />
    </template>
  </AppPageWithAside>
</template>
