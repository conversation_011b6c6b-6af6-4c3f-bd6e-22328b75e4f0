<script setup lang="ts">
import * as SavedFilterApi from '@js/api/savedFilterApi'
import { useQueries } from '@tanstack/vue-query'
import { computed, ref } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { useConfirmDialog, watchDebounced } from '@vueuse/core'
import { isAxiosError } from 'axios'
import { useHead } from '@vueuse/head'
import { queries } from '@js/query'
import TableControls from '@js/components/table/TableControls.vue'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import { getReadableTaskTypeByShortName, getTaskType } from '@js/model/task'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import HelpPanel from '@js/components/help/HelpPanel.vue'
import HelpPanelToggler from '@js/components/help/HelpPanelToggler.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import SavedFilterFavouriteButton from '@js/components/saved-filter/SavedFilterFavouriteButton.vue'
import SavedFilterShareDialog from '@js/components/task/saved-filter/SavedFilterShareDialog.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import useTable from '@js/composable/useTable'
import { getIdFromIri } from '@js/utilities/api-resource'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { ApiQuery } from '@js/composable/useTable'
import type { SavedFilter } from '@js/model/saved-filter'

useHead({ title: Translator.trans('u2.saved_filters') })
const isShareDialogOpen = ref(false)
const router = useRouter()

const sharePath = ref()
const openShareDialog = (savedFilter: (typeof items.value)[number]) => {
  sharePath.value = savedFilter.sharePath

  isShareDialogOpen.value = true
}
function closeShareDialog() {
  sharePath.value = undefined
  isShareDialogOpen.value = false
}

const notificationsStore = useNotificationsStore()
const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()

const deleteSavedFilter = async (savedFilter: (typeof items.value)[number]) => {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await SavedFilterApi.deleteSavedFilterById(savedFilter.id)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await fetchData()
    } catch (error) {
      resolveNotification(error)
    }
  }
}

const {
  columns,
  query,
  changePage,
  changePageSize,
  setSelectedColumns,
  fromLocationQuery,
  apiQuery,
  toLocationQuery,
  sort,
} = useTable(
  [
    {
      required: true,
      name: '',
      id: 'icon',
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.id'),
      id: 'id',
      selectedByDefault: true,
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.name'),
      id: 'name',
      selectedByDefault: true,
    },
    {
      isSortable: false,
      align: 'left',
      name: Translator.trans('u2_core.uql'),
      id: 'uql',
      selectedByDefault: false,
    },
    {
      isSortable: true,
      name: Translator.trans('u2.shared_with_all_users'),
      id: 'public',
      selectedByDefault: false,
      type: 'boolean',
      align: 'center',
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2.description'),
      id: 'description',
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.type'),
      id: 'tableViewConfigurationFullyQualifiedClass',
      selectedByDefault: true,
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.owner'),
      id: 'owner',
      type: 'user',
      selectedByDefault: true,
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.created_by'),
      id: 'createdBy',
      type: 'user',
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.created_at'),
      id: 'createdAt',
      type: 'date',
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.updated_by'),
      id: 'updatedBy',
      type: 'user',
    },
    {
      isSortable: true,
      align: 'left',
      name: Translator.trans('u2_core.updated_at'),
      id: 'updatedAt',
      type: 'date',
    },
    {
      isSortable: false,
      align: 'left',
      name: Translator.trans('u2_core.subscriptions'),
      id: 'subscriptions',
      selectedByDefault: true,
    },
    {
      name: '',
      align: 'right',
      id: 'actions',
      required: true,
    },
  ],
  { sort: { id: 'DESC' }, filter: { search: '' } },
  { cacheKey: 'favourite-saved-filters' }
)

const items = computed(() => {
  return savedFilters.value.map((savedFilter, index) => {
    const routeLocation = {
      name: getTaskType(savedFilter.taskShortName) + 'List',
      query: { f: savedFilter.id },
    }

    return {
      ...savedFilter,
      index,
      owner: savedFilter.owner ? getIdFromIri(savedFilter.owner) : undefined,
      createdBy: savedFilter.createdBy ? getIdFromIri(savedFilter.createdBy) : undefined,
      updatedBy: savedFilter.updatedBy ? getIdFromIri(savedFilter.updatedBy) : undefined,
      routeLocation,
      sharePath: window.location.hostname + router.resolve(routeLocation).href,
      tableViewConfigurationFullyQualifiedClass: getReadableTaskTypeByShortName(
        savedFilter.taskShortName
      ),
    }
  })
})

const savedFilters = ref<Array<SavedFilter>>([])
const totalItems = ref(0)
const fetchData = async (query?: ApiQuery) => {
  const { data } = await SavedFilterApi.fetchSavedFiltersByQuery(query)

  savedFilters.value = data['hydra:member']
  totalItems.value = data['hydra:totalItems']
}

watchDebounced(
  query,
  (newValue) => {
    router.push({ query: toLocationQuery(newValue) })
  },
  { deep: true, debounce: 400 }
)

const route = useRoute()

const subscriptionQueryDefinitions = computed(() => {
  if (savedFilters.value.length === 0 || !query.value.selectedColumns?.includes('subscriptions')) {
    return []
  }

  return savedFilters.value.map(
    (savedFilter) => queries.savedFilters.single(savedFilter.id)._ctx.subscriptions
  )
})
const subscriptionQueries = useQueries({ queries: subscriptionQueryDefinitions })

const newQuery = fromLocationQuery(route.query)
if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
  query.value = newQuery
}

const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
try {
  await fetchData(apiQuery.value)
} catch (error) {
  if (
    !isAxiosError(error) ||
    (error.response && !(await handleAxiosErrorResponse(error.response)))
  ) {
    throw error
  }
}

onBeforeRouteUpdate(async (to) => {
  const newQuery = fromLocationQuery(to.query)
  if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
    query.value = newQuery
  }

  try {
    await fetchData(apiQuery.value)
  } catch (error) {
    if (
      !isAxiosError(error) ||
      (error.response && !(await handleAxiosErrorResponse(error.response)))
    ) {
      throw error
    }
  }
})
</script>

<template>
  <AppPageWide>
    <template #header>
      <PageHeader>
        <template #title>
          <div class="flex items-baseline gap-x-1">
            <PageHeaderTitle :title="Translator.trans('u2.saved_filters')" />
            <HelpPanelToggler help-panel-id="saved-filter-help" />
          </div>
        </template>
      </PageHeader>
    </template>
    <template #beforeWideContent>
      <HelpPanel
        id="saved-filter-help"
        class="mb-2"
        :title="Translator.trans('u2_core.creating_new_filters')"
      >
        <!-- eslint-disable-next-line vue/no-v-html -->
        <div v-html="Translator.trans('u2_core.go_to_the_corresponding_list_page')" />
      </HelpPanel>
      <AppSearch v-model="query.filter.search" class="w-96 max-w-full" />

      <TableControls
        :selected="query.selectedColumns"
        :headers="columns"
        :total-items="totalItems"
        :query="query"
        @page-change="changePage"
        @page-size-change="changePageSize"
        @new-column-selection="setSelectedColumns"
      />
    </template>
    <AppTable
      :query="query"
      :headers="columns"
      :selected="query.selectedColumns"
      :items="items ?? []"
      :total-items="totalItems"
      sticky-header
      :has-controls="false"
      @sort="sort"
    >
      <template #item-icon="{ item }">
        <SavedFilterFavouriteButton :saved-filter="savedFilters[item.index]" />
      </template>
      <template #item-name="{ item }">
        <router-link class="font-bold" :to="item.routeLocation">{{ item.name }}</router-link>
      </template>
      <template #item-id="{ item }">
        <router-link class="font-bold" :to="item.routeLocation">{{ item.id }}</router-link>
      </template>
      <template #item-description="{ item }">
        {{ item.description }}
      </template>
      <template #item-subscriptions="{ item }">
        <em v-if="subscriptionQueries[item.index].isLoading">
          {{ Translator.trans('u2.loading') }}
        </em>

        <ul v-else-if="subscriptionQueries[item.index].data?.['hydra:member']" class="compact-list">
          <li
            v-for="subscription in subscriptionQueries[item.index].data?.['hydra:member']"
            :key="subscription.id"
          >
            <router-link
              :to="{ name: 'SavedFilterSubscriptionEdit', params: { id: subscription.id } }"
              >{{ subscription.name }}
            </router-link>
          </li>
        </ul>
        <em v-else>{{ Translator.trans('u2_core.no_subscriptions') }}</em>
      </template>
      <template #item-actions="{ item }">
        <ButtonBasic
          :to="item.routeLocation"
          icon="table"
          :tooltip="Translator.trans('u2_core.show_the_filtered_table')"
        />

        <ButtonBasic
          data-test="share-button"
          :data-dialog-title="Translator.trans('u2.share_filter')"
          icon="share"
          :tooltip="Translator.trans('u2.share_filter_link')"
          @click="openShareDialog(item)"
        />

        <ButtonEdit
          :disabled="!item.canEdit"
          :to="{ name: 'SavedFilterEdit', params: { id: item.id } }"
          :tooltip="Translator.trans('u2_core.edit_filter')"
        />

        <ButtonDelete
          :disabled="!item.canDelete"
          :tooltip="Translator.trans('u2_core.delete_filter')"
          @click="deleteSavedFilter(item)"
        />
      </template>
    </AppTable>

    <transition
      enter-active-class="ease-out duration-200"
      leave-active-class="ease-in duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <SavedFilterShareDialog
        v-if="isShareDialogOpen"
        :path="sharePath"
        @close="closeShareDialog"
      />
    </transition>

    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @confirm="confirmDelete"
      @close="cancelDelete"
    >
      {{ Translator.trans('u2_core.delete_filter.confirmation') }}
    </ConfirmationDialog>
  </AppPageWide>
</template>
