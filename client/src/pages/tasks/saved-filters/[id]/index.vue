<script setup lang="ts">
import { computed, ref, useTemplateRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { isAxiosError } from 'axios'
import { useHead } from '@vueuse/head'
import { useQueryClient } from '@tanstack/vue-query'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import EntityPageHeaderTitle from '@js/components/entity/EntityPageHeaderTitle.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import { queries } from '@js/query'
import SavedFilterAssignedUserGroupsAside from '@js/components/saved-filter/SavedFilterAssignedUserGroupsAside.vue'
import SavedFilterAssignedUsersAside from '@js/components/saved-filter/SavedFilterAssignedUsersAside.vue'
import SavedFilterEditor from '@js/components/saved-filter/SavedFilterEditor.vue'
import SavedFilterShareDialog from '@js/components/task/saved-filter/SavedFilterShareDialog.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import { getTaskType } from '@js/model/task'
import * as SavedFilterApi from '@js/api/savedFilterApi'
import type { SavedFilter, SavedFilterSubscription } from '@js/model/saved-filter'

const props = defineProps<{
  id: number
}>()
useHead({ title: () => Translator.trans('u2.saved_filter') + ' #' + props.id })

const auth = useAuthStore()

const isShareDialogOpen = ref(false)

const router = useRouter()
const route = useRoute()
const shareLocation = computed(() => {
  if (savedFilter.value) {
    return router.resolve({
      name: getTaskType(savedFilter.value.taskShortName) + 'List',
      query: { f: savedFilter.value.id },
    }).href
  }
  return undefined
})

const sharePath = computed(() =>
  shareLocation.value
    ? window.location.hostname + router.resolve(shareLocation.value).href
    : undefined
)

function openShareDialog() {
  isShareDialogOpen.value = true
}

const savedFilter = ref<SavedFilter>()
const subscriptions = ref<Array<SavedFilterSubscription>>([])
const loading = ref(true)
const savedFilterEditor =
  useTemplateRef<InstanceType<typeof SavedFilterEditor>>('savedFilterEditor')
const saveState = computed(() => {
  if (loading.value) {
    return 'loading'
  }
  return savedFilterEditor.value?.state
})
async function loadData() {
  loading.value = true
  try {
    await Promise.all([
      SavedFilterApi.fetchSavedFilterById(props.id).then((response) => {
        savedFilter.value = response.data
        showPublicVisibilityInfo.value = savedFilter.value.public
      }),
      SavedFilterApi.fetchSavedFilterSubscriptions(props.id).then((response) => {
        subscriptions.value = response.data['hydra:member']
      }),
    ])
  } catch (error) {
    if (isAxiosError(error) && error.response?.status === 403) {
      router.replace({
        name: 'Error403',
        params: { pathMatch: route.path.substring(1).split('/') },
        query: route.query,
        hash: route.hash,
      })
      return
    }
    throw error
  } finally {
    loading.value = false
  }
}

const showPublicVisibilityInfo = ref(false)
const handlePublicChanged = (newValue: boolean) => {
  showPublicVisibilityInfo.value = newValue
}
const queryClient = useQueryClient()
function onSave() {
  queryClient.invalidateQueries({
    ...queries.savedFilters.all,
    refetchType: 'all',
  })
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}

loadData()
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <EntityPageHeaderTitle :id="id" :title="Translator.trans('u2.saved_filter')" />
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'SavedFilterList' }"
          :tooltip="Translator.trans('u2.saved_filters_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonBasic
          v-if="shareLocation"
          icon="table"
          :to="shareLocation"
          :tooltip="Translator.trans('u2_core.show_the_filtered_table')"
        >
          {{ Translator.trans('u2_core.show') }}
        </ButtonBasic>

        <ButtonSpacer v-if="sharePath" />

        <ButtonBasic
          v-if="sharePath"
          id="button-share-saved-filter"
          :data-dialog-title="Translator.trans('u2.share_filter')"
          icon="share"
          :tooltip="Translator.trans('u2.share_filter_link')"
          @click="openShareDialog"
        >
          {{ Translator.trans('u2.share') }}
        </ButtonBasic>

        <ButtonSave :state="saveState" form="saved_filter" />
      </PageHeader>
    </template>

    <SavedFilterEditor
      v-if="savedFilter"
      ref="savedFilterEditor"
      :saved-filter="savedFilter"
      :disabled="loading"
      @saved="onSave"
      @public-changed="handlePublicChanged"
    />
    <AppLoader v-else class="mt-10" />

    <template #asideAfter>
      <template v-if="savedFilter">
        <AsideSection icon="mail" :headline="Translator.trans('u2_core.subscriptions')">
          <template #button>
            <ButtonNew
              :disabled="!auth.hasRole('ROLE_ADMIN')"
              :to="{ name: 'SavedFilterSubscriptionNew', params: { filterId: savedFilter.id } }"
              :tooltip="Translator.trans('u2_core.add_subscription')"
              button-style="text"
              :show-text="false"
            />
          </template>

          <AppEmptyState v-if="subscriptions?.length === 0">
            <template #title>
              {{ Translator.trans('u2_core.no_subscriptions') }}
            </template>
            {{ Translator.trans('u2.saved_filters.no_subscriptions_to_this_filter') }}
            <template v-if="auth.hasRole('ROLE_ADMIN')" #action>
              <ButtonBasic
                :to="{ name: 'SavedFilterSubscriptionNew', params: { filterId: savedFilter.id } }"
                >{{ Translator.trans('u2_core.add_subscription') }}</ButtonBasic
              >
            </template>
          </AppEmptyState>

          <div
            v-for="subscription in subscriptions"
            v-else
            :key="subscription.id"
            class="flex items-center gap-2"
          >
            <div class="w-full">{{ subscription.name }}</div>
            <span>
              <SvgIcon
                v-tooltip="`${Translator.trans('u2_core.users')}: ${subscription.userCount}`"
                icon="user"
                :class="['align-text-top', { 'text-gray-400': subscription.userCount === 0 }]"
              />
            </span>
            <span>
              <SvgIcon
                v-tooltip="`${Translator.trans('u2_core.groups')}: ${subscription.groupCount}`"
                icon="users"
                :class="['align-text-top', { 'text-gray-400': subscription.groupCount === 0 }]"
              />
            </span>
            <ButtonEdit
              :to="{ name: 'SavedFilterSubscriptionEdit', params: { id: subscription.id } }"
              :tooltip="
                Translator.trans('u2_core.edit_given_subscription', {
                  subscription_name: subscription.name,
                })
              "
            />
          </div>
        </AsideSection>

        <SavedFilterAssignedUsersAside
          :saved-filter="savedFilter"
          :show-public-visibility-info="showPublicVisibilityInfo"
        />

        <SavedFilterAssignedUserGroupsAside
          :saved-filter="savedFilter"
          :show-public-visibility-info="showPublicVisibilityInfo"
        />
      </template>
    </template>

    <transition
      enter-active-class="ease-out duration-200"
      leave-active-class="ease-in duration-200"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <SavedFilterShareDialog
        v-if="isShareDialogOpen"
        :path="sharePath"
        @close="isShareDialogOpen = false"
      />
    </transition>
  </AppPageWithAside>
</template>
