<script setup lang="ts">
import { requestPasswordReset } from '@js/api/passwordApi'
import { deleteUserById } from '@js/api/userApi'
import AsideSection from '@js/components/AsideSection.vue'
import UserAuditLogs from '@js/components/user/UserAuditLogs.vue'
import { useRouteParams } from '@vueuse/router'
import invariant from 'tiny-invariant'
import { computed, ref, useTemplateRef } from 'vue'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { StatusCodes } from 'http-status-codes'
import { useConfirmDialog } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import useUserQuery from '@js/composable/useUserQuery'
import { createErrorLocation } from '@js/router/helpers'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import UserAssignedAuthorizationsAside from '@js/components/user/UserAssignedAuthorizationsAside.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import UserAssignedRolesAside from '@js/components/user/UserAssignedRolesAside.vue'
import UserAssignedUnitsAside from '@js/components/user/UserAssignedUnitsAside.vue'
import UserAssignedUserGroupsAside from '@js/components/user/UserAssignedUserGroupsAside.vue'
import UserEditor from '@js/components/user/UserEditor.vue'
import UserInformationAside from '@js/components/user/UserInformationAside.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { isUserWithAllProperties } from '@js/model/user'
import type { UserWithAllProperties } from '@js/model/user'

const userId = useRouteParams<string, number>('id', undefined, {
  transform(value) {
    return Number(value)
  },
})

const { data } = useUserQuery(userId)
const user = computed<UserWithAllProperties | undefined>(() => {
  if (data.value && isUserWithAllProperties(data.value)) {
    return data.value
  }
  return undefined
})

useHead({ title: () => `${Translator.trans('u2_core.user')} ${user.value?.username}` })
const userEditor = useTemplateRef('userEditor')

onBeforeRouteUpdate((to, from) => {
  if (to.params.id !== from.params.id) {
    if (authStore.hasRole('ROLE_USER_GROUP_ADMIN') || authStore.user?.id === +to.params.id) {
      return
    }
    return createErrorLocation(to, StatusCodes.FORBIDDEN)
  }
})

const router = useRouter()

const authStore = useAuthStore()
const isUserGroupAdmin = computed(() => authStore.hasRole('ROLE_USER_GROUP_ADMIN'))
const isCurrentUser = computed(() => authStore.user?.id === user.value?.id)
const isFormLoaded = ref(false)
const isFormDisabled = computed(() => {
  if (isCurrentUser.value) {
    return false
  }
  return !isUserGroupAdmin.value
})
const notificationsStore = useNotificationsStore()

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()
async function deleteUser() {
  invariant(user.value)
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      await deleteUserById(user.value.id)
      notificationsStore.addSuccess(Translator.trans('u2.success_removed'))
      await router.push({ name: 'UserList' })
    } catch (error) {
      await resolveNotification(error)
    }
  }
}

function onSave() {
  notificationsStore.addSuccess(Translator.trans('u2_core.success'))
}

async function sendPasswordReset() {
  invariant(user.value)
  try {
    await requestPasswordReset(user.value.username)
    notificationsStore.addSuccess(
      Translator.trans('u2.authentication.password_reset.success.reset_password_email_sent', {
        email_address: user.value.contact.email,
      })
    )
  } catch (error) {
    await resolveNotification(error, async (response) => {
      if (response.status === StatusCodes.NOT_FOUND) {
        notificationsStore.addError(
          Translator.trans('u2.authentication.password_reset.error.user_could_not_be_found')
        )
        return true
      }

      notificationsStore.addError(
        Translator.trans('u2.authentication.password_reset.error.email_sending_failed')
      )

      return false
    })
  }
}
</script>

<template>
  <AppPageWithAside v-if="user">
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle :title="Translator.trans('u2_core.user')" :subtitle="user.username">
            <template #image>
              <UserAvatar />
            </template>
          </PageHeaderTitle>
        </template>
        <ButtonBasic
          :to="{ name: 'UserList' }"
          icon="list"
          :disabled="!isUserGroupAdmin"
          :tooltip="Translator.trans('u2_core.user_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonNew
          :to="{ name: 'UserNew' }"
          :disabled="!isUserGroupAdmin"
          :tooltip="Translator.trans('u2_core.add_new_user')"
        />

        <ButtonSave :disabled="isFormDisabled" :state="userEditor?.state" form="user" />

        <ButtonDropdownEllipsis>
          <template #items>
            <ButtonDropdownItem
              v-if="isCurrentUser"
              :to="{ name: 'ChangePassword' }"
              icon="key"
              :text="Translator.trans('u2_core.change_password')"
            />
            <ButtonDropdownItem
              v-else-if="isUserGroupAdmin"
              icon="key"
              :text="Translator.trans('u2_core.reset_password')"
              @click="sendPasswordReset"
            />
            <ButtonDropdownItem
              :disabled="!isUserGroupAdmin"
              icon="delete"
              :text="Translator.trans('u2.delete')"
              @click="deleteUser"
            />
          </template>
        </ButtonDropdownEllipsis>
      </PageHeader>
    </template>

    <template #asideBefore>
      <UserInformationAside :user="user" />
    </template>
    <template #default>
      <UserEditor ref="userEditor" :user="user" @saved="onSave" @loaded="isFormLoaded = true" />

      <ConfirmationDialog
        v-if="isConfirmDeleteRevealed"
        @confirm="confirmDelete"
        @close="cancelDelete"
      >
        {{ Translator.trans('u2_core.delete_entry.confirmation') }}
      </ConfirmationDialog>
    </template>
    <template v-if="user" #asideAfter>
      <UserAssignedUserGroupsAside
        v-if="user"
        id="assigned-user-groups"
        :disabled="!isUserGroupAdmin"
        :user="user"
      />
      <UserAssignedUnitsAside
        v-if="user"
        id="assigned-units"
        :disabled="!isUserGroupAdmin"
        :user="user"
      />
      <UserAssignedRolesAside
        v-if="user"
        id="assigned-roles"
        :disabled="!isUserGroupAdmin"
        :user="user"
      />
      <UserAssignedAuthorizationsAside :user="user" />
      <AsideSection
        v-if="isUserGroupAdmin"
        icon="history"
        :headline="Translator.trans('u2_core.changes')"
        :collapsed="true"
        unmount-on-hide
      >
        <UserAuditLogs :user="user" />
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
