<script setup lang="ts">
import { computed, useTemplateRef } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import AppMessage from '@js/components/AppMessage.vue'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import UserEditor from '@js/components/user/UserEditor.vue'
import type { User } from '@js/model/user'

const authStore = useAuthStore()
const isFormDisabled = computed(() => !authStore.hasRole('ROLE_USER_GROUP_ADMIN'))
useHead({ title: Translator.trans('u2.new_user') })

const userEditor = useTemplateRef('userEditor')

const router = useRouter()

const onSave = async (updatedUser: User) => {
  await router.push({
    name: 'UserEdit',
    params: {
      id: updatedUser.id,
    },
  })
  const notificationsStore = useNotificationsStore()
  notificationsStore.addSuccess(Translator.trans('u2_core.add_new_user.success'))
  notificationsStore.addNotice(
    Translator.trans(
      'u2.authentication.password_reset.notice.click_on_reset_password_to_send_an_email'
    )
  )
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <PageHeaderTitle :title="Translator.trans('u2.new_user')">
            <template #image>
              <UserAvatar />
            </template>
          </PageHeaderTitle>
        </template>

        <ButtonBasic
          icon="list"
          :to="{ name: 'UserList' }"
          :tooltip="Translator.trans('u2_core.user_list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave :disabled="isFormDisabled" :state="userEditor?.state" form="user" />
      </PageHeader>
    </template>

    <template #asideBefore>
      <slot name="aside-before" />
    </template>
    <template #default>
      <UserEditor ref="userEditor" @saved="onSave" />
    </template>
    <template #asideAfter>
      <AsideSection icon="user" :headline="Translator.trans('u2_core.groups')">
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection icon="unit" :headline="Translator.trans('u2.unit.plural')">
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>

      <AsideSection icon="config" :headline="Translator.trans('u2_core.roles')">
        <template #button>
          <ButtonEdit :disabled="true" />
        </template>

        <AppMessage>
          {{ Translator.trans('u2.new_record.save_first') }}
        </AppMessage>
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
