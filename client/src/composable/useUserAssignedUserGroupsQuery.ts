import { computed, toRef, unref } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { MaybeRefOrGetter } from 'vue'
import type { User } from '@js/model/user'

export default function useUserAssignedUserGroupsQuery(
  id: MaybeRefOrGetter<User['id'] | undefined | null>
) {
  const idRef = toRef(id as User['id'])
  const queryConfiguration = {
    ...queries.users.single(idRef)._ctx.userGroups,
    enabled: computed(() => !!unref(idRef)),
  }

  const query = useQuery(queryConfiguration)
  return {
    ...query,
    userGroups: computed(() => query.data.value?.['hydra:member'] ?? []),
  }
}
