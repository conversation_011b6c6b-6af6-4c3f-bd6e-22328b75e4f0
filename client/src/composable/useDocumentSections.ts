import invariant from 'tiny-invariant'
import { computed } from 'vue'
import { mapSectionIdToNumbering } from '@js/helper/document/mapSectionIdToNumbering'
import { mapSectionIdToParentSection } from '@js/helper/document/mapSectionIdToParentSection'
import { mapSectionIdToSubSections } from '@js/helper/document/mapSectionIdToSubSections'
import { transformSectionsToHierarchy } from '@js/helper/document/transformSectionsToHierarchy'
import { newSectionTitleIdentifier } from '@js/model/document'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'
import type { Ref } from 'vue'

export default function useDocumentSections(
  sectionIdToRenderedContentDataArray: Ref<Array<{ id: DocumentSection['id']; content: string }>>,
  sections: Ref<Array<DocumentSection>>
) {
  const subsectionsBySection = computed(() => {
    return mapSectionToSubSections(sections.value)
  })

  const numberingBySection = computed(() => {
    return mapSectionToNumbering(sections.value)
  })

  const renderedContentBySection = computed(() => {
    const map = new Map<DocumentSection, string | undefined>()

    for (const section of sections.value ?? []) {
      if (newSectionTitleIdentifier === section.name) {
        map.set(
          section,
          `
          <div class="mceNonEditable new-section-placeholder">
            <span>*** ${Translator.trans('u2_structureddocument.new_section_content')} ***</span>
          </div>`
        )
        continue
      }

      const sectionWithRenderedContent = (sectionIdToRenderedContentDataArray.value ?? []).find(
        (idAndContent) => idAndContent.id === section.id
      )
      map.set(section, sectionWithRenderedContent?.content)
    }

    return map
  })

  function hasSubsections(section: DocumentSection) {
    return (subsectionsBySection.value.get(section)?.length ?? 0) > 0
  }

  function buildSectionNameWithNumbering(section: DocumentSection) {
    return getSectionNumber(section) + ' ' + section.name
  }

  const hierarchicalSections = computed(() => {
    return transformSectionsToHierarchy(
      sections.value.map((section) => {
        return {
          section,
          renderedContent: renderedContentBySection.value.get(section),
          tocId: getSectionNumber(section),
        }
      })
    )
  })
  function getSectionNumber(section: DocumentSection) {
    return numberingBySection.value.get(section)
  }

  const sectionToParentSection = computed(() => {
    return mapSectionToParentSection(hierarchicalSections.value)
  })

  return {
    buildSectionNameWithNumbering,
    getSectionNumber,
    hasSubsections,
    hierarchicalSections,
    numberingBySection,
    renderedContentBySection,
    sectionToParentSection,
    subsectionsBySection,
  }
}
