import { computed } from 'vue'
import { mapSectionIdToNumbering } from '@js/helper/document/mapSectionIdToNumbering'
import { mapSectionIdToParentSection } from '@js/helper/document/mapSectionIdToParentSection'
import { mapSectionIdToSubsections } from '@js/helper/document/mapSectionIdToSubsections'
import { transformSectionsToHierarchy } from '@js/helper/document/transformSectionsToHierarchy'
import { newSectionTitleIdentifier } from '@js/model/document'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'
import type { Ref } from 'vue'

export default function useDocumentSections(
  sectionIdToRenderedContentDataArray: Ref<Array<{ id: DocumentSection['id']; content: string }>>,
  sections: Ref<Array<DocumentSection>>
) {
  const subsectionsBySectionId = computed(() => {
    return mapSectionIdToSubsections(sections.value)
  })

  const numberingBySectionId = computed(() => {
    return mapSectionIdToNumbering(sections.value)
  })

  const renderedContentBySection = computed(() => {
    const map = new Map<DocumentSection, string | undefined>()

    for (const section of sections.value ?? []) {
      if (newSectionTitleIdentifier === section.name) {
        map.set(
          section,
          `
          <div class="mceNonEditable new-section-placeholder">
            <span>*** ${Translator.trans('u2_structureddocument.new_section_content')} ***</span>
          </div>`
        )
        continue
      }

      const sectionWithRenderedContent = (sectionIdToRenderedContentDataArray.value ?? []).find(
        (idAndContent) => idAndContent.id === section.id
      )
      map.set(section, sectionWithRenderedContent?.content)
    }

    return map
  })

  function hasSubsections(section: DocumentSection) {
    return (subsectionsBySectionId.value.get(section.id)?.length ?? 0) > 0
  }

  const hierarchicalSections = computed(() => {
    return transformSectionsToHierarchy(
      sections.value.map((section) => {
        return {
          section,
          renderedContent: renderedContentBySection.value.get(section),
          tocId: getSectionNumberById(section.id),
        }
      })
    )
  })
  function getSectionNumberById(sectionId: DocumentSection['id']) {
    return numberingBySectionId.value.get(sectionId)
  }

  const sectionIdToParentSection = computed(() => {
    return mapSectionIdToParentSection(hierarchicalSections.value)
  })

  return {
    getSectionNumberById,
    hasSubsections,
    hierarchicalSections,
    numberingBySectionId,
    renderedContentBySection,
    sectionIdToParentSection,
    subsectionsBySectionId,
  }
}
