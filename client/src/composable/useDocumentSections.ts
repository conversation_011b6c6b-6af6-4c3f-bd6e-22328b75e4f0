import invariant from 'tiny-invariant'
import { computed } from 'vue'
import { mapSectionIdToNumbering } from '@js/helper/document/mapSectionIdToNumbering'
import { mapSectionIdToParentSection } from '@js/helper/document/mapSectionIdToParentSection'
import { mapSectionIdToSubSections } from '@js/helper/document/mapSectionIdToSubSections'
import { transformSectionsToHierarchy } from '@js/helper/document/transformSectionsToHierarchy'
import { newSectionTitleIdentifier } from '@js/model/document'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'
import type { Ref } from 'vue'

export default function useDocumentSections(
  sectionIdToRenderedContentDataArray: Ref<Array<{ id: DocumentSection['id']; content: string }>>,
  sections: Ref<Array<DocumentSection>>
) {
  // Single source of truth: canonical section objects by ID
  const sectionsById = computed(() => {
    const map = new Map<DocumentSection['id'], DocumentSection>()
    sections.value.forEach((section) => {
      map.set(section.id, section)
    })
    return map
  })

  const subsectionIdsBySectionId = computed(() => {
    return mapSectionIdToSubSections(sections.value)
  })

  const numberingBySectionId = computed(() => {
    return mapSectionIdToNumbering(sections.value)
  })

  const renderedContentBySectionId = computed(() => {
    const map = new Map<DocumentSection['id'], string | undefined>()

    for (const section of sections.value ?? []) {
      if (newSectionTitleIdentifier === section.name) {
        map.set(
          section.id,
          `
          <div class="mceNonEditable new-section-placeholder">
            <span>*** ${Translator.trans('u2_structureddocument.new_section_content')} ***</span>
          </div>`
        )
        continue
      }

      const sectionWithRenderedContent = (sectionIdToRenderedContentDataArray.value ?? []).find(
        (idAndContent) => idAndContent.id === section.id
      )
      map.set(section.id, sectionWithRenderedContent?.content)
    }

    return map
  })

  const sectionIdToParentSectionId = computed(() => {
    return mapSectionIdToParentSection(hierarchicalSections.value)
  })

  // Helper functions that work with section IDs
  function hasSubsections(section: DocumentSection) {
    return (subsectionIdsBySectionId.value.get(section.id)?.length ?? 0) > 0
  }

  function buildSectionNameWithNumbering(section: DocumentSection) {
    return getSectionNumber(section) + ' ' + section.name
  }

  const hierarchicalSections = computed(() => {
    return transformSectionsToHierarchy(
      sections.value.map((section) => {
        return {
          section,
          renderedContent: renderedContentBySectionId.value.get(section.id),
          tocId: getSectionNumber(section),
        }
      })
    )
  })

  function getSectionNumber(sectionId: DocumentSection['id']) {
    return numberingBySectionId.value.get(sectionId)
  }

  function getSectionById(id: DocumentSection['id']) {
    const section = sectionsById.value.get(id)
    invariant(section, `Section with ID ${id} not found`)
    return section
  }

  const getParentSectionId = (
    sectionId: DocumentSection['id']
  ): DocumentSection['id'] | undefined => {
    return sectionIdToParentSectionId.value.get(sectionId)
  }

  const getSubsectionIds = (sectionId: DocumentSection['id']): Array<DocumentSection['id']> => {
    return subsectionIdsBySectionId.value.get(sectionId) ?? []
  }

  return {
    buildSectionNameWithNumbering,
    getSectionNumber,
    hasSubsections,
    hierarchicalSections,
    sectionsById,
    numberingBySectionId,
    renderedContentBySectionId,
    sectionIdToParentSectionId,
    subsectionIdsBySectionId,
    getSectionById,
    getParentSectionId,
    getSubsectionIds,
  }
}
