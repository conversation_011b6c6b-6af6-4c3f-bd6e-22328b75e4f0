import { fetchLinkedEntities } from '@js/api/fileApi'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { fetchStates } from '@js/types'
import { getReadableTaskTypeByShortName, isTask } from '@js/model/task'
import {
  isCountryByCountryReportSection,
  isDocumentTemplateSection,
  isLocalFileSection,
  isMasterFileSection,
} from '@js/model/document'
import { getIdFromIri } from '@js/utilities/api-resource'
import { isAnyUnit } from '@js/model/unit'
import Translator from '@js/translator'
import type { SetNonNullable } from 'type-fest'
import type { FileEntity, FileLinkedEntity } from '@js/model/file'
import type { FetchState } from '@js/types'
import type { Ref } from 'vue'

export default function useFileLinkedEntities(file: Ref<FileEntity>) {
  const linkedEntities = ref<Array<FileLinkedEntity>>([])
  const loadingState = ref<FetchState>(fetchStates.idle)

  const router = useRouter()
  const linksByType = computed(() => {
    return linkedEntities.value
      .filter(
        (record): record is SetNonNullable<FileLinkedEntity, 'linkedEntity'> =>
          record.linkedEntity !== null
      )
      .map((fileLinkedEntity) => {
        const linkedEntity = fileLinkedEntity.linkedEntity
        if (isAnyUnit(linkedEntity)) {
          return {
            humanReadableType: Translator.trans('u2.unit.plural'),
            url: router.resolve({ name: 'UnitEdit', params: { id: linkedEntity.id } }).path,
            name: linkedEntity.displayName,
          }
        }

        if (isTask(linkedEntity)) {
          return {
            humanReadableType: getReadableTaskTypeByShortName(linkedEntity['u2:extra'].shortName),
            url: linkedEntity['u2:extra'].editPath,
            name: linkedEntity['u2:extra'].displayName,
          }
        }

        if (isMasterFileSection(linkedEntity)) {
          return {
            humanReadableType: Translator.trans('u2_tpm.master_file.plural'),
            url: router.resolve({
              name: 'MasterFileEdit',
              params: { id: getIdFromIri(linkedEntity.document) },
            }).href,
            name: linkedEntity.displayName,
          }
        }

        if (isLocalFileSection(linkedEntity)) {
          return {
            humanReadableType: Translator.trans('u2_tpm.local_file.plural'),
            url: router.resolve({
              name: 'LocalFileEdit',
              params: { id: getIdFromIri(linkedEntity.document) },
            }).href,
            name: linkedEntity.displayName,
          }
        }

        if (isCountryByCountryReportSection(linkedEntity)) {
          return {
            humanReadableType: Translator.trans('u2_tpm.country_by_country_report.plural'),
            url: router.resolve({
              name: 'CountryByCountryReportEdit',
              params: { id: getIdFromIri(linkedEntity.document) },
            }).href,
            name: linkedEntity.displayName,
          }
        }

        if (isDocumentTemplateSection(linkedEntity)) {
          return {
            humanReadableType: Translator.trans('u2_structureddocument.document_templates'),
            url: router.resolve({
              name: 'TemplateConfigure',
              params: { id: getIdFromIri(linkedEntity.document) },
            }).href,
            name: linkedEntity.displayName,
          }
        }

        throw new Error('Unknown linked entity type ' + linkedEntity['@type'])
      })
      .reduce((linksByType, link) => {
        if (linksByType[link.humanReadableType]) {
          linksByType[link.humanReadableType].links.push({
            url: link.url,
            name: link.name,
          })

          return linksByType
        }

        linksByType[link.humanReadableType] = {
          humanReadableType: link.humanReadableType,
          links: [
            {
              url: link.url,
              name: link.name,
            },
          ],
        }

        return linksByType
      }, Object.create({}))
  })

  return {
    unauthorizedEntityCount: computed(
      () => linkedEntities.value.filter((linkedEntity) => linkedEntity.linkedEntity === null).length
    ),
    loadingState,
    linksByType,
    fetchLinkedEntities: async () => {
      loadingState.value = fetchStates.loading

      const { data } = await fetchLinkedEntities(file.value)

      linkedEntities.value = data['hydra:member']

      loadingState.value = fetchStates.resolved
    },
  }
}
