import * as PeriodApi from '@js/api/periodApi'
import { queries } from '@js/query'
import { resetInfiniteQuery } from '@js/query/helpers'
import queryClient from '@js/queryClient'
import { useMutation } from '@tanstack/vue-query'
import type { Period } from '@js/api/periodApi'

export function invalidatePeriodQueries(id: Period['id']) {
  resetInfiniteQuery(queries.periods.single(id)._ctx.auditLogInfinite.queryKey)
  queryClient.invalidateQueries({
    queryKey: queries.periods.single(id).queryKey,
  })
}
export default function usePeriodUpdateMutation() {
  return useMutation({
    mutationFn: (variables: { period: Partial<Omit<Period, 'id'>> & { id: Period['id'] } }) => {
      return PeriodApi.updatePeriod(variables.period)
    },
    onSuccess: (response, variables) => {
      invalidatePeriodQueries(variables.period.id)
    },
  })
}
