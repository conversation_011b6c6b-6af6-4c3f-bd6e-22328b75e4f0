import { useTaskInfoStore } from '@js/stores/task-info'
import { useTaskStore } from '@js/stores/task'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import urlToShortName from '@js/assets/router/urlToShortName'
import { isAxiosError } from 'axios'
import { storeToRefs } from 'pinia'
import invariant from 'tiny-invariant'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { TaskShortName } from '@js/model/task'

/**
 * Composable for fetching task data that's commonly used across task-related pages.
 * Handles the standard pattern of:
 * 1. Extracting route parameters
 * 2. Fetching task information from taskInfoStore
 * 3. Fetching task data from taskStore
 * 4. Error handling
 *
 * @returns The reactive task ref from the store
 */
export async function useTaskData(route: RouteLocationNormalizedLoaded) {
  const shortName = urlToShortName(route.fullPath) as TaskShortName
  const taskTypeId = Number(route.params.id)

  const taskInfoStore = useTaskInfoStore()
  const taskStore = useTaskStore()
  const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()

  try {
    // Fetch task information first
    await taskInfoStore.fetchTaskInformation(shortName, taskTypeId)
    invariant(taskInfoStore.taskId, 'Task ID is not defined')

    // Then fetch the actual task data
    await taskStore.fetchTaskById(taskInfoStore.taskId)
  } catch (error) {
    if (
      !isAxiosError(error) ||
      (error.response && !(await handleAxiosErrorResponse(error.response)))
    ) {
      throw error
    }
  }

  const { task } = storeToRefs(taskStore)
  return { task }
}
