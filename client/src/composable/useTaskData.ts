import { useTaskInfoStore } from '@js/stores/task-info'
import { useTaskStore } from '@js/stores/task'
import urlToShortName from '@js/assets/router/urlToShortName'
import { storeToRefs } from 'pinia'
import invariant from 'tiny-invariant'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { TaskShortName } from '@js/model/task'

export function useTaskData(route: RouteLocationNormalizedLoaded) {
  const taskInfoStore = useTaskInfoStore()
  const taskStore = useTaskStore()

  async function fetchTask() {
    const shortName = urlToShortName(route.fullPath) as TaskShortName
    const taskTypeId = Number(route.params.id)
    await taskInfoStore.fetchTaskInformation(shortName, taskTypeId)
    invariant(taskInfoStore.taskId, 'Task ID is not defined')
    await taskStore.fetchTaskById(taskInfoStore.taskId)
  }

  const { task } = storeToRefs(taskStore)
  const { optionsForNew } = storeToRefs(taskInfoStore)

  return {
    fetchTask,
    optionsForNew,
    refreshTask: function () {
      taskInfoStore.refresh()
      taskStore.refresh()
    },
    task,
  }
}
