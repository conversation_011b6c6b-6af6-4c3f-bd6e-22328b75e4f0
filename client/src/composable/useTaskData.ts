import urlToShortName from '@js/assets/router/urlToShortName'
import { useTaskStore } from '@js/stores/task'
import { useTaskInfoStore } from '@js/stores/task-info'
import { storeToRefs } from 'pinia'
import invariant from 'tiny-invariant'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

export function useTaskData(route: RouteLocationNormalizedLoaded) {
  const taskInfoStore = useTaskInfoStore()
  const taskStore = useTaskStore()

  async function fetchTask() {
    await taskInfoStore.fetchTaskInformation(
      urlToShortName(route.fullPath),
      Number(route.params.id)
    )
    invariant(taskInfoStore.taskId, 'Task ID is not defined')
    await taskStore.fetchTaskById(taskInfoStore.taskId)
  }

  const { task } = storeToRefs(taskStore)
  const { optionsForNew } = storeToRefs(taskInfoStore)

  return {
    fetchTask,
    optionsForNew,
    refreshTask: function () {
      taskInfoStore.refresh()
      taskStore.refresh()
    },
    task,
  }
}
