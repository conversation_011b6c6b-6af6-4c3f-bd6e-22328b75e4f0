import { useQueryClient } from '@tanstack/vue-query'
import { isAxiosError } from 'axios'
import { StatusCodes } from 'http-status-codes'
import { acceptHMRUpdate, defineStore } from 'pinia'
import invariant from 'tiny-invariant'
import { computed, ref, watch } from 'vue'
import * as DocumentApi from '@js/api/documentApi'
import useDocumentEditDataQuery from '@js/composable/useDocumentEditDataQuery'
import useDocumentEditRenderedSectionsDataQuery from '@js/composable/useDocumentEditRenderedSectionsDataQuery'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { newSectionTitleIdentifier } from '@js/model/document'
import { queries } from '@js/query'
import { useCommentsStore } from '@js/stores/comments'
import { useTaskStore } from '@js/stores/task'
import { useWorkflowStore } from '@js/stores/workflow'
import useDocumentSections from '@js/composable/useDocumentSections'
import type { DocumentSection } from '@js/model/document'
import type { DocumentSectionPlacement } from '@js/api/documentApi'

export const useDocumentStore = defineStore('document', () => {
  const taskStore = useTaskStore()
  const taskParams = computed(() => {
    if (!taskStore.task) {
      return
    }
    return {
      id: taskStore.task['u2:extra'].taskTypeId,
      shortName: taskStore.task['u2:extra'].shortName,
    }
  })

  const workflowStore = useWorkflowStore()
  const commentsStore = useCommentsStore()
  const queryClient = useQueryClient()
  const { handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
  async function fetchData() {
    if (!taskStore.task) {
      return
    }

    try {
      await Promise.all([
        workflowStore.fetchWorkflowByBindingId(taskStore.task.taskType),
        commentsStore.fetchComments(),
        queryClient.fetchQuery(queries.document.editDocumentData(taskParams)),
        queryClient.fetchQuery(queries.document.editDocumentData(taskParams)._ctx.sections),
      ])
    } catch (error) {
      if (isAxiosError(error) && error.response) {
        handleAxiosErrorResponse(error.response)
        return
      }
      throw error
    }
  }

  const enabled = ref(false)
  const {
    data: pageData,
    isLoading: isDocumentDataLoading,
    isRefetching: isDocumentDataRefetching,
  } = useDocumentEditDataQuery(taskParams, { enabled })
  const { data: sectionIdToRenderedContentDataArrayFromServer } =
    useDocumentEditRenderedSectionsDataQuery(taskParams, { enabled })
  const isLoading = computed(() => isDocumentDataLoading.value)
  const isDataRefetchedExternally = ref(false)
  watch(
    isDocumentDataRefetching,
    () => {
      isDataRefetchedExternally.value = true
    },
    { once: true }
  )
  watch(pageData, (newPageData) => {
    if (newPageData) {
      pageData.value?.sections?.forEach((section) => {
        if (!section.include && !isDataRefetchedExternally.value) {
          collapsedSections.value.add(section.id)
        }
      })
    }
  })

  const sectionIdToSection = computed(() => {
    const map = new Map<DocumentSection['id'], DocumentSection>()
    for (const section of pageData.value?.sections ?? []) {
      map.set(section.id, section)
    }
    return map
  })
  const sections = computed(() => pageData.value?.sections ?? [])

  function getSectionById(id: DocumentSection['id']) {
    const section = sectionIdToSection.value.get(id)
    invariant(section, `Section with id ${id} must be defined`)
    return section
  }

  const attachments = computed(() => pageData.value?.attachments)
  const newSectionPath = computed(() => pageData.value?.newSectionPath)
  const userCanEditConfiguration = computed(() => pageData.value?.userCanEditConfiguration)
  const userCanEditContent = computed(() => pageData.value?.userCanEditContent)
  const documentName = computed(() => pageData.value?.documentName)

  const collapsedSections = ref<Set<DocumentSection['id']>>(new Set())
  function isSectionCollapsed(section: DocumentSection) {
    return collapsedSections.value.has(section.id)
  }

  const editedSections = ref<Set<DocumentSection['id']>>(new Set())
  function isSectionEdited(section: DocumentSection) {
    return editedSections.value.has(section.id)
  }

  const isDocumentEdited = computed(() => {
    return editedSections.value.size > 0
  })

  function collapseSection(section: DocumentSection) {
    collapsedSections.value.add(section.id)
  }
  function expandSection(section: DocumentSection) {
    collapsedSections.value.delete(section.id)
  }

  function collapseSectionWithSubsections(section: DocumentSection) {
    collapseSection(section)
    const subsections = subsectionsBySectionId.value.get(section.id) ?? []
    subsections.forEach((subsection) => {
      collapseSectionWithSubsections(subsection)
    })
  }

  function expandSectionWithSubsections(section: DocumentSection) {
    expandSection(section)
    const subsections = subsectionsBySectionId.value.get(section.id) ?? []
    subsections.forEach((subsection) => {
      expandSectionWithSubsections(subsection)
    })
  }

  function collapseAllSections() {
    sections.value?.forEach((section) => {
      collapseSectionWithSubsections(section)
    })
  }

  function expandAllSections() {
    sections.value?.forEach((section) => {
      expandSectionWithSubsections(section)
    })
  }

  function expandParent(section: DocumentSection) {
    const parent = sectionIdToParentSection.value.get(section.id)
    if (parent) {
      expandWithParents(parent)
    }
  }
  function expandWithParents(section: DocumentSection) {
    expandSection(section)
    expandParent(section)
  }

  const { resolveNotification } = useHandleAxiosErrorResponse()
  async function createNewSection(
    placement: DocumentSectionPlacement,
    referenceSectionId: DocumentSection['id']
  ) {
    const response = await DocumentApi.addDocumentSection(
      {
        '@type': sections.value[0]['@type'],
        name: newSectionTitleIdentifier,
      },
      {
        placement,
        referenceSectionId,
      }
    ).catch((error) => {
      resolveNotification(error)
      return false as const
    })

    return response ? response.status === StatusCodes.OK : false
  }

  async function createInitialSection() {
    if (!taskParams.value) {
      return
    }

    const response = await DocumentApi.addDocumentInitialSection({
      shortName: taskParams.value.shortName,
      documentId: taskParams.value.id,
      name: newSectionTitleIdentifier,
    }).catch((error) => {
      resolveNotification(error)
      return false as const
    })

    return response ? response.status === StatusCodes.OK : false
  }

  function getParentsBeforeNextLevel(section: DocumentSection): Array<DocumentSection> {
    if (section.level === 1) {
      return []
    }

    const sectionIndex = sections.value.findIndex(
      (currentSection) => currentSection.id === section.id
    )
    if (sectionIndex === -1) {
      return []
    }

    const topLevelSections = sections.value
      .slice(0, sectionIndex)
      .filter((s) => s.level < section.level)

    const parents = topLevelSections.reduceRight<Array<DocumentSection>>(
      (accumulator, currentParent) => {
        if (!accumulator.some((parent) => parent.level === currentParent.level)) {
          accumulator.push(currentParent)
        }
        return accumulator
      },
      []
    )

    const nextSection = sections.value[sectionIndex + 1]
    if (!nextSection || nextSection.level === 1) {
      return parents
    }

    return parents.filter((parent) => {
      return nextSection.level !== section.level && parent.level !== 1
    })
  }

  function getPossibleNewSectionConfigs(section?: DocumentSection): Array<{
    placement: DocumentSectionPlacement
    referenceSectionId: DocumentSection['id']
  }> {
    if (!section) {
      return [
        {
          placement: 'before',
          referenceSectionId: hierarchicalSections.value[0].section.id,
        },
      ]
    }

    const subsections = subsectionsBySectionId.value.get(section.id)
    invariant(subsections, 'Subsections must be defined')

    return subsections.length === 0
      ? [
          { placement: 'after', referenceSectionId: section.id },
          { placement: 'subsection-of', referenceSectionId: section.id },
          ...getParentsBeforeNextLevel(section).map((parent) => ({
            placement: 'after' as DocumentSectionPlacement,
            referenceSectionId: parent.id,
          })),
        ]
      : [
          {
            placement: 'before',
            referenceSectionId: subsections[0].id,
          },
        ]
  }

  const sectionIdToRenderedContentDataArray = computed(
    () => sectionIdToRenderedContentDataArrayFromServer.value ?? []
  )

  function buildSectionNameWithNumbering(sectionId: DocumentSection['id']) {
    return getSectionNumberById(sectionId) + ' ' + getSectionById(sectionId).name
  }

  const {
    getSectionNumberById,
    hasSubsections,
    hierarchicalSections,
    numberingBySectionId,
    renderedContentBySection,
    sectionIdToParentSection,
    subsectionsBySectionId,
  } = useDocumentSections(sectionIdToRenderedContentDataArray, sections)

  return {
    attachments,
    buildSectionNameWithNumbering,
    collapseAllSections,
    collapsedSections,
    collapseSection,
    collapseSectionWithSubsections,
    createInitialSection,
    createNewSection,
    documentName,
    editedSections,
    enabled,
    expandAllSections,
    expandSection,
    expandSectionWithSubsections,
    expandWithParents,
    fetchData,
    getPossibleNewSectionConfigs,
    getSectionNumberById,
    hasSubsections,
    hierarchicalSections,
    isDataRefetchedExternally,
    isDocumentEdited,
    isLoading,
    isSectionCollapsed,
    isSectionEdited,
    newSectionPath,
    numberingBySectionId,
    pageData,
    renderedContentBySection,
    sectionIdToSection,
    sectionIdToRenderedContentDataArray,
    sections,
    sectionIdToParentSection,
    subsectionsBySectionId,
    userCanEditConfiguration,
    userCanEditContent,
    getSectionById,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useDocumentStore, import.meta.hot))
}
