import { useSessionStorage } from '@vueuse/core'
import { acceptHMRUpdate, defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useRouteParams } from '@vueuse/router'
import invariant from 'tiny-invariant'
import { skipToken, useQuery } from '@tanstack/vue-query'
import Translator from '@js/translator'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import { splitFormulaIntoElements } from '@js/helper/datasheets/formula'
import { queries } from '@js/query'
import { getIdFromIri } from '@js/utilities/api-resource'
import { useLocalStorageBackedSessionStorage } from '@js/composable/useLocalStorageBackedSessionStorage'
import isEqual from 'lodash/isEqual'
import type { Field, LayoutItem } from '@js/model/datasheet'

interface FormulaElement {
  id: string
  isPreviousPeriod: boolean
  field?: Field
}

export const useFieldInspectorStore = defineStore('field-inspector', () => {
  const isEnabled = useLocalStorageBackedSessionStorage<boolean>(
    'layout:field-inspector-enabled',
    false
  )

  const showColours = useSessionStorage<boolean>('layout:field-inspector-show-colours', true)
  const showSelect = useSessionStorage<boolean>('layout:field-inspector-show-select', false)
  const showValues = useSessionStorage<boolean>('layout:field-inspector-show-value', false)
  const hoveredItemId = ref<LayoutItem['id'] | undefined>(undefined)

  const layoutId = useRouteParams<string, number>('sheetId', undefined, {
    transform(value) {
      return Number(value)
    },
  })

  const datasheetParameterStore = useDatasheetParametersStore()
  const { data: allFieldsData } = useQuery({
    refetchOnWindowFocus: false,
    ...queries.datasheets.single(layoutId)._ctx.fields,
    /*
     TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
       See: https://github.com/lukemorales/query-key-factory/issues/100
    */
    queryFn: computed(() => {
      return layoutId.value
        ? queries.datasheets.single(layoutId.value)._ctx.fields.queryFn
        : skipToken
    }),
  })

  const fields = computed(() => allFieldsData.value?.data?.['hydra:member'] ?? [])

  const fieldByItemId = computed<Map<LayoutItem['id'], Field>>((oldValue) => {
    const newValue = new Map<LayoutItem['id'], Field>(
      fields.value.map((field) => [getIdFromIri(field.item), field])
    )
    return oldValue && isEqual(oldValue, newValue) ? oldValue : newValue
  })

  const fieldById = computed<Map<Field['id'], Field>>((oldValue) => {
    const newValue = new Map<Field['id'], Field>(fields.value.map((field) => [field.id, field]))
    return oldValue && isEqual(oldValue, newValue) ? oldValue : newValue
  })

  const field = computed(() => {
    return datasheetParameterStore.parameters.field
      ? fieldById.value.get(datasheetParameterStore.parameters.field)
      : undefined
  })

  const fieldItemId = computed(() => (field.value ? getIdFromIri(field.value?.item) : undefined))
  const { data: fieldItem } = useQuery({
    ...queries.items.single(fieldItemId),
    /*
     TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
       See: https://github.com/lukemorales/query-key-factory/issues/100
    */
    queryFn: computed(() =>
      fieldItemId.value ? queries.items.single(fieldItemId).queryFn : skipToken
    ),
  })

  const formulaElementStrings = computed(() => {
    if (fieldItem.value?.formula) {
      return splitFormulaIntoElements(fieldItem.value.formula)
    }
    return []
  })

  const formulaElements = computed<Array<FormulaElement>>((oldValue) => {
    const newValue = formulaElementStrings.value.map((element) => {
      const isItemElement = element.startsWith('{') && element.endsWith('}')
      if (isItemElement) {
        const itemString = element.replace('{', '').replace('}', '')
        const itemId = Number(itemString.replace('P', ''))

        return {
          id: element,
          isPreviousPeriod: itemString.startsWith('P'),
          field: fieldByItemId.value.get(itemId),
        }
      }

      return {
        id: element,
        isPreviousPeriod: false,
        field: undefined,
      }
    })

    return oldValue && isEqual(oldValue, newValue) ? oldValue : newValue
  })

  const formulaElementByFieldId = computed<Map<Field['id'], FormulaElement>>((oldValue) => {
    const newValue = new Map(
      formulaElements.value
        .filter((element) => element.field !== undefined)
        .map((element) => {
          invariant(element.field)
          return [element.field.id, element]
        })
    )

    return oldValue && isEqual(oldValue, newValue) ? oldValue : newValue
  })

  function buildFieldInspectButtonTooltip(item: LayoutItem) {
    const fieldToInspect = fieldByItemId.value.get(item.id)
    if (!fieldToInspect) {
      return Translator.trans('u2.field_inspector.field_not_on_datasheet')
    }
    return fieldToInspect.id === field.value?.id
      ? Translator.trans('u2.inspect.already_inspecting')
      : Translator.trans('u2.inspect.tooltip')
  }

  return {
    hoveredItemId,
    formulaElements,
    formulaElementByFieldId,
    isEnabled,
    showValues,
    showColours,
    showSelect,
    layoutId,
    fieldByItemId,
    field,
    fieldItem,
    fields,
    fieldById,
    buildFieldInspectButtonTooltip,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useFieldInspectorStore, import.meta.hot))
}
