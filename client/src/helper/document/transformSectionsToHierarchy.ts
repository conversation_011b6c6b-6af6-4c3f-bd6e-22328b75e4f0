import type { DocumentSection } from '@js/model/document'

type SectionWithData = {
  section: DocumentSection
  renderedContent?: string
  tocId?: string
}

export type HierarchicalSection = SectionWithData & {
  subHierarchicalSections: Array<HierarchicalSection>
}

/**
 * This function assumes that the sections fetched from the backend are ordered by level and that
 * each section (except those at the top level) has a parent section at the previous level.
 */
export function transformSectionsToHierarchy(sections: Array<SectionWithData>) {
  const topLevelSections: Array<HierarchicalSection> = []
  const lastSectionAtEachLevel: Record<number, HierarchicalSection> = {}

  for (const section of sections) {
    const sectionWithSubsections: HierarchicalSection = {
      section: section.section, // Keep the original section reference
      renderedContent: section.renderedContent,
      tocId: section.tocId,
      subHierarchicalSections: [],
    }

    if (sectionWithSubsections.section.level === 1) {
      topLevelSections.push(sectionWithSubsections)
    } else {
      const parentSection = lastSectionAtEachLevel[sectionWithSubsections.section.level - 1]
      if (parentSection) {
        parentSection.subHierarchicalSections.push(sectionWithSubsections)
      }
    }

    lastSectionAtEachLevel[sectionWithSubsections.section.level] = sectionWithSubsections
  }

  return topLevelSections
}
