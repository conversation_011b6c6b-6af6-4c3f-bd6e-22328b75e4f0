import type { DocumentSection } from '@js/model/document'

function findSubsectionsAndTheirSubSections(
  section: DocumentSection,
  sectionsArray: Array<DocumentSection>,
  sectionIndex: number
) {
  const subSectionIds = []
  for (let i = sectionIndex + 1; i < sectionsArray.length; i++) {
    const subSection = sectionsArray[i]
    if (subSection.level <= section.level) {
      break
    }
    subSectionIds.push(subSection.id)
  }
  return subSectionIds
}

export function mapSectionIdToSubSections(
  sections: Array<DocumentSection>
): Map<DocumentSection['id'], Array<DocumentSection['id']>> {
  const subsectionIdsBySectionId = new Map<DocumentSection['id'], Array<DocumentSection['id']>>()

  for (const [index, section] of sections.entries()) {
    const subsectionIds = findSubsectionsAndTheirSubSections(section, sections, index)
    subsectionIdsBySectionId.set(section.id, subsectionIds)
  }

  return subsectionIdsBySectionId
}
