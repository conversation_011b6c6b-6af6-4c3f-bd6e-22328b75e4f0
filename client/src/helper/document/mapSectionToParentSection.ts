import type { DocumentSection } from '@js/model/document'
import type { HierarchicalSection } from './transformSectionsToHierarchy'

export function mapSectionToParentSection(hierarchicalSections: Array<HierarchicalSection>) {
  const parentMap = new Map<DocumentSection, DocumentSection | undefined>()
  for (const topSection of hierarchicalSections) {
    parentMap.set(topSection.section, undefined)

    processSubsections(topSection, parentMap)
  }

  return parentMap
}

function processSubsections(
  parentSection: HierarchicalSection,
  parentMap: Map<DocumentSection, DocumentSection | undefined>
) {
  for (const subSection of parentSection.subHierarchicalSections) {
    parentMap.set(subSection.section, parentSection.section)
    processSubsections(subSection, parentMap)
  }
}
