import type { DocumentSection } from '@js/model/document'

function findSubsections(
  section: DocumentSection,
  sections: Array<DocumentSection>
): Array<DocumentSection> {
  const subsections: Array<DocumentSection> = []
  let isSectionCurrentSection = false

  for (const currentSection of sections) {
    if (!isSectionCurrentSection) {
      if (currentSection.id === section.id) {
        isSectionCurrentSection = true
      }
      continue
    }

    if (currentSection.level <= section.level) {
      break
    }

    subsections.push(currentSection)
  }

  return subsections
}

export function mapSectionIdToSubsections(
  sections: Array<DocumentSection>
): Map<DocumentSection['id'], Array<DocumentSection>> {
  const subsectionsBySection = new Map<DocumentSection['id'], Array<DocumentSection>>()

  for (const section of sections) {
    const subsections = findSubsections(section, sections)
    subsectionsBySection.set(section.id, subsections)
  }

  return subsectionsBySection
}
