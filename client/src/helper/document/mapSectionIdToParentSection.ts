import type { DocumentSection } from '@js/model/document'
import type { HierarchicalSection } from './transformSectionsToHierarchy'

export function mapSectionIdToParentSection(
  hierarchicalSections: Array<HierarchicalSection>
): Map<DocumentSection['id'], DocumentSection['id'] | undefined> {
  const parentIdMap = new Map<DocumentSection['id'], DocumentSection['id'] | undefined>()
  
  for (const topSection of hierarchicalSections) {
    parentIdMap.set(topSection.section.id, undefined)
    processSubsections(topSection, parentIdMap)
  }

  return parentIdMap
}

function processSubsections(
  parentSection: HierarchicalSection,
  parentIdMap: Map<DocumentSection['id'], DocumentSection['id'] | undefined>
) {
  for (const subSection of parentSection.subHierarchicalSections) {
    parentIdMap.set(subSection.section.id, parentSection.section.id)
    processSubsections(subSection, parentIdMap)
  }
}
