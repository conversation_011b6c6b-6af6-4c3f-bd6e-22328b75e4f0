{"author": "Universal Units GmbH", "license": "proprietary", "name": "u2", "type": "module", "engines": {"node": "22"}, "description": "u2 universal units", "devDependencies": {"@eslint/js": "^9.29.0", "@faker-js/faker": "^9.8.0", "@floating-ui/dom": "^1.7.1", "@formkit/auto-animate": "^0.8.2", "@lukemorales/query-key-factory": "^1.3.4", "@pinia/testing": "^1.0.2", "@rollup/plugin-inject": "^5.0.5", "@sentry/vue": "^9.30.0", "@storybook/addon-actions": "^8.6.14", "@storybook/addon-docs": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-storysource": "^8.6.14", "@storybook/preview-api": "^8.6.14", "@storybook/vue3": "^8.6.14", "@storybook/vue3-vite": "^8.6.14", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/vite": "^4.1.10", "@tanstack/vue-query": "^5.80.10", "@tanstack/vue-virtual": "^3.13.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@testing-library/vue": "^8.1.0", "@total-typescript/shoehorn": "^0.1.2", "@tsconfig/node22": "^22.0.2", "@types/bazinga-translator": "^3.0.2", "@types/file-saver": "^2.0.7", "@types/jquery": "^3.5.32", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.18", "@types/mime-types": "^3.0.1", "@types/node": "^22.15.32", "@types/numeral": "^2.0.5", "@types/papaparse": "^5.3.16", "@types/sortablejs": "^1.15.8", "@vee-validate/zod": "^4.15.1", "@vitejs/plugin-vue": "^5.2.4", "@vitest/ui": "^3.2.4", "@vue/language-server": "^2.2.10", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/components": "^13.4.0", "@vueuse/core": "^13.4.0", "@vueuse/head": "^2.0.0", "@vueuse/integrations": "^13.4.0", "@vueuse/router": "^13.4.0", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-common": "^3.0.4", "@zxcvbn-ts/language-de": "^3.0.2", "@zxcvbn-ts/language-en": "^3.0.2", "assert": "^2.1.0", "axios": "^1.10.0", "bazinga-translator": "^7.0.0", "blob-polyfill": "^9.0.20240710", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "core-js": "^3.43.0", "cron-validate": "^1.5.2", "date-fns": "^4.1.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-config-standard": "^17.1.0", "eslint-formatter-checkstyle": "^8.40.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import-x": "^4.15.2", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vitest": "^0.5.4", "eslint-plugin-vue": "^10.2.0", "eslint-typegen": "^2.2.0", "file-saver": "^2.0.5", "floating-vue": "^5.2.2", "flush-promises": "^1.0.2", "fuse.js": "^7.1.0", "globals": "^16.2.0", "http-status-codes": "^2.3.0", "intl-messageformat": "^10.7.16", "jest-serializer-vue": "^3.1.0", "jquery": "^3.7.1", "jsdom": "^26.1.0", "jsdom-testing-mocks": "^1.13.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mime-types": "^3.0.1", "minimist": "^1.2.8", "motion-v": "^1.3.0", "msw": "^2.10.2", "msw-storybook-addon": "^2.0.5", "numeral": "^2.0.6", "papaparse": "^5.5.3", "pegjs": "^0.10.0", "pinia": "^3.0.3", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-nesting": "^13.0.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "reka-ui": "^2.3.1", "sentry-testkit": "^6.2.2", "storybook": "^8.6.14", "storybook-vue3-router": "^5.0.0", "stylelint": "^16.21.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-order": "^7.0.0", "symfony-ts-router": "^1.0.6", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.10", "tiny-invariant": "^1.3.3", "tinymce": "^7.9.1", "type-fest": "^4.41.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vee-validate": "^4.15.1", "vite": "^6.3.5", "vite-svg-loader": "^5.1.0", "vitest": "^3.2.4", "vitest-fail-on-console": "^0.7.1", "vue": "^3.5.17", "vue-chartjs": "^5.3.2", "vue-component-type-helpers": "^2.2.10", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.5.1", "vue-tsc": "^2.2.10", "vuelidate": "^0.7.7", "vuelidate-messages": "^0.1.2", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.5"}, "private": true, "repository": {"type": "git", "url": "https://github.com/universalunits/u2.git"}, "scripts": {"preinstall": "npx only-allow pnpm", "build": "vite build", "build:watch": "vite build --watch", "dev-server": "vite", "eslint": "eslint", "eslint:fix": "eslint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "storybook": "storybook dev", "stylelint": "stylelint .", "stylelint:fix": "stylelint . --fix", "storybook:build": "storybook build -c .storybook -o .out", "test": "vitest --watch", "uqlparser": "node src/uql-parser.js", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "msw": {"workerDirectory": ["public"]}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac", "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "core-js", "esbuild", "msw", "unrs-resolver", "vue-demi"]}, "dependencies": {"xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}}