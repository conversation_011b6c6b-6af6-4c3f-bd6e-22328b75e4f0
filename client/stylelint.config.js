/** @type {import('stylelint').Config} */
const config = {
  extends: ['stylelint-config-standard', 'stylelint-config-recommended-vue'],
  plugins: ['stylelint-order'],
  ignoreFiles: ['**/*.(js|ts|svg|xsd)', 'patches/**/*'],
  rules: {
    'at-rule-no-deprecated': [
      true,
      {
        ignoreAtRules: ['apply'],
      },
    ],
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'apply',
          'layer',
          'plugin',
          'responsive',
          'reference',
          'screen',
          'source',
          'tailwind',
          'theme',
          'utility',
          'variants',
        ],
      },
    ],
    'custom-property-pattern': [
      '^([a-z][a-z0-9]*)(-{1,2}[a-z0-9*]+)*$',
      {
        message: (name) =>
          `Expected custom property name "${name}" to be kebab-case or a tailwind variable format`,
      },
    ],
    'declaration-property-value-no-unknown': null,
    'function-no-unknown': [
      true,
      {
        ignoreFunctions: ['theme', 'v-bind'],
      },
    ],
    'import-notation': 'string',
    'media-query-no-invalid': null,
    'no-descending-specificity': null,
    'order/properties-alphabetical-order': true,
    'selector-class-pattern': null,
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep'],
      },
    ],
  },
}

export default config
