import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { setupServer, wrapInSuspense } from '@tests/utils'
import { render, within } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import * as PeriodApi from '@js/api/periodApi'
import ExchangeRateList from '@js/components/exchange-rate/ExchangeRateList.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PeriodEdit from '@js/pages/periods/[id]/index.vue'
import PeriodEditor from '@js/components/period/PeriodEditor.vue'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'

const server = setupServer(
  http.get('/api/periods/1', async () => {
    return HttpResponse.json({ id: 1 }, { status: StatusCodes.OK })
  }),
  http.delete('/api/periods/1', async () => {
    return new Response(null, { status: StatusCodes.NO_CONTENT })
  })
)

describe('PeriodEdit Page', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('renders', async function () {
    const page = await mount(wrapInSuspense(PeriodEdit, { id: 1 }), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          PeriodEditor: true,
          ExchangeRateList: true,
        },
      },
    })

    await flushPromises()

    expect(page.findComponent(PeriodEditor).exists()).toBe(true)
    expect(page.findComponent(ExchangeRateList).exists()).toBe(true)
    expect(page.findComponent(PageHeader).exists()).toBe(true)
  })

  it('deletes a period', async () => {
    const user = userEvent.setup()
    const periodDelete = vi.spyOn(PeriodApi, 'deletePeriod')

    const ui = render(wrapInSuspense(PeriodEdit, { id: 1 }), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          PeriodEditor: true,
          ExchangeRateList: true,
        },
      },
    })

    const authStore = useAuthStore()
    authStore.$patch({
      user: { id: 1, roles: ['ROLE_PERIOD_MANAGER'], authorizations: [] },
    })

    const notificationsStore = useNotificationsStore()
    await flushPromises()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText(/u2.delete/))

    await user.click(within(ui.getByRole('dialog')).getByText(/u2.delete/))

    await flushPromises()

    // Then
    expect(periodDelete).toHaveBeenCalledWith(1)
    expect(notificationsStore.addSuccess).toHaveBeenCalledWith('u2.success_removed')
  })
})
