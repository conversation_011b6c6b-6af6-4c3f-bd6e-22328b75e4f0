import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { fireEvent, render, waitFor } from '@testing-library/vue'
import { createDocumentSection } from '@tests/__factories__/createDocumentSection'
import { createStatus } from '@tests/__factories__/createStatus'
import { createTask } from '@tests/__factories__/createTask'
import { createUser } from '@tests/__factories__/createUser'
import { createWorkflow } from '@tests/__factories__/createWorkflow'
import queryClient from '@tests/testQueryClient'
import { findResourceById, setupServer, wrapInSuspense } from '@tests/utils'
import { fromPartial } from '@total-typescript/shoehorn'
import { flushPromises } from '@vue/test-utils'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { expect } from 'vitest'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import invariant from 'tiny-invariant'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { queries } from '@js/query'
import DocumentEdit from '@js/pages/document/DocumentEdit.vue'
import * as DocumentApi from '@js/api/documentApi'
import type { Router } from 'vue-router'
import type { DocumentSection } from '@js/model/document'

const user1 = createUser({
  '@id': '/api/users/1',
})

const parentSection = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 1,
  level: 1,
  include: true,
  name: 'Parent section name',
})

const childSection1 = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 2,
  level: 2,
  include: true,
  name: 'Child section name 1',
})

const childSection2 = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 3,
  level: 3,
  include: true,
  name: 'Child section name 2',
})

const section = createDocumentSection({
  '@type': 'CountryByCountryReportSection',
  id: 4,
  level: 1,
  include: true,
  name: 'Section name',
})

const sections = [parentSection, childSection1, childSection2, section]

const task = createTask({
  '@id': '/api/tasks/my-task-id',
  '@type': 'Task',
  id: 'my-task-id',
  taskType: 'tpm_country_by_country_report',
  reviews: [],
  status: '/api/statuses/1',
  'u2:extra': {
    taskTypeId: 3,
    shortName: 'tpm-country-by-country-report',
    readableTaskType: 'My Document Name',
    createdBy: '/api/users/1',
    updatedBy: '/api/users/1',
    canViewDocument: false,
    canWrite: false,
    hasDocument: false,
    deletePath: 'my-delete-path',
    editDocumentPath: 'my-content-path',
    newPath: 'my-new-path',
    canDelete: true,
    canAttach: true,
    canAddReview: true,
    displayName: 'display name',
    canEditConfiguration: true,
    canViewConfiguration: true,
    canRemoveReview: true,
    canViewDocumentPermissions: false,
  },
})

const server = setupServer(
  http.get(
    `/legacy/${task['u2:extra'].shortName}/${task['u2:extra'].taskTypeId}/entity-information`,
    async () => {
      return HttpResponse.json(
        {
          id: task['u2:extra'].taskTypeId,
          shortName: task['u2:extra'].shortName,
          taskId: task.id,
        },
        { status: StatusCodes.OK }
      )
    }
  ),
  http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
    return HttpResponse.json(
      [
        { id: parentSection.id, content: 'Parent section content' },
        { id: childSection1.id, content: 'First child section content' },
        { id: childSection2.id, content: 'Second child section content' },
        { id: section.id, content: 'Section content' },
      ],
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/users', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get(user1['@id'], async () => {
    return HttpResponse.json({ ...user1 }, { status: StatusCodes.OK })
  }),
  http.get('/api/tasks/my-task-id/comments', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/tasks/my-task-id/check-states', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/workflows', async () => {
    const workflows = [createWorkflow()]
    const body = { 'hydra:member': workflows, 'hydra:totalItems': workflows.length }
    return HttpResponse.json(body, { status: StatusCodes.OK })
  }),
  http.get('/api/statuses/1', async () => {
    const status = createStatus({
      '@id': '/api/statuses/1',
      name: 'Task status',
      type: 'OPEN',
    })
    return HttpResponse.json(status, { status: StatusCodes.OK })
  }),
  http.get('/api/tasks/my-task-id', async () => {
    return HttpResponse.json(task, { status: StatusCodes.OK })
  }),
  http.get('/legacy/tpm-country-by-country-report/entity-metatdata', async () => {
    return HttpResponse.json({}, { status: StatusCodes.OK })
  })
)

async function clickSectionControlItem(
  ui: ReturnType<typeof render>,
  sectionName: DocumentSection['name'],
  itemName: RegExp
) {
  await userEvent.click(ui.getByLabelText(`u2.document.section_controls: ${sectionName}`))
  await userEvent.click(await ui.findByRole('menuitem', { name: itemName }))
}

describe('Document Edit Page', () => {
  beforeAll(() => {
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        currentRoute: ref({
          name: 'DocumentEdit',
          fullPath: `/tpm/country-by-country-report/${task['u2:extra'].taskTypeId}/edit-document`,
          params: { id: task['u2:extra'].taskTypeId.toString() },
        }),
      })
    })
    server.listen()
  })
  beforeEach(() => {
    vi.clearAllMocks()
    mockIntersectionObserver()
  })
  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('renders', async function () {
    const user = userEvent.setup()
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections: [],
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )
    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    expect(ui.getByRole('menuitem', { name: /u2.delete/ })).toBeInTheDocument()
    expect(ui.getByRole('menuitem', { name: /PDF/ })).toBeInTheDocument()
    expect(ui.getByRole('menuitem', { name: /XML/ })).toBeInTheDocument()

    expect(ui.getByRole('link', { name: /u2.new/ })).toBeInTheDocument()
    expect(ui.getByRole('link', { name: /u2.list/ })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: /u2.edit_configuration/ })).toBeInTheDocument()

    expect(ui.getByText('Document comments section stubbed content')).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2.information/ })).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2_core.reviews/ })).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: 'My Document Name # 3' })).toBeInTheDocument()

    expect(ui.getByRole('button', { name: /Task status/ })).toBeInTheDocument()
    expect(await ui.findByText('u2_tpm.add_first_section')).toBeInTheDocument()
  })

  it('shows a confirmation dialog when delete is clicked', async () => {
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const user = userEvent.setup()
    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          Elma5Dialog: true,
          TaskInformationAside: true,
          TaskComments: true,
          TaskReviewAside: true,
        },
      },
    })

    await flushPromises()

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    // When
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)
    await user.click(ui.getByText(/u2.delete/))

    // Then
    expect(ui.queryByRole('dialog')).toBeInTheDocument()

    // When
    await user.click(ui.getByRole('button', { name: /cross/ }))

    // Then
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('opens the elma5 dialog when clicking "ELMA5" button in the "Export" dropdown"', async () => {
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    // When
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          TaskInformationAside: true,
          TaskReviewAside: true,
          TaskComments: true,
          PageHeader: {
            template: '<div class="page-header-stub"><slot name="title"/><slot/></div>',
          },
        },
      },
    })
    await flushPromises()

    // Then
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    // When
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText('ELMA5'))

    // Then
    expect(await ui.findByRole('dialog')).toBeInTheDocument()
  })

  it('collapses and expands a section', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    expect(ui.getByText('Parent section content')).toBeInTheDocument()
    expect(ui.getByText('First child section content')).toBeInTheDocument()
    expect(ui.getByText('Second child section content')).toBeInTheDocument()

    const collapseButton = await ui.findByLabelText(`u2.collapse: ${parentSection.name}`)

    // When
    await user.click(collapseButton)

    // Then
    expect(ui.queryByText('Parent section content')).not.toBeVisible()
    expect(ui.queryByText('First child section content')).not.toBeVisible()
    expect(ui.queryByText('Second child section content')).not.toBeVisible()

    const expandButton = await ui.findByLabelText(`u2.expand: ${parentSection.name}`)

    // When
    await user.click(expandButton)

    // Then
    expect(ui.queryByText('Parent section content')).toBeVisible()
    expect(ui.getByText('First child section content')).toBeVisible()
    expect(ui.getByText('Second child section content')).toBeVisible()
  })

  it('expands and collapses all sections', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          AppSidebar: true,
          PageHeader: true,
          TaskInformationAside: true,
          TaskReviewAside: true,
          SectionMenu: true,
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    expect(await ui.findByText('Parent section content')).toBeInTheDocument()
    expect(ui.getByText('First child section content')).toBeVisible()
    expect(ui.getByText('Second child section content')).toBeVisible()
    expect(ui.getByText('Section content')).toBeVisible()

    const collapseButtonWrapper = await ui.findByLabelText(`u2.collapse: ${parentSection.name}`)

    // When
    await fireEvent.contextMenu(collapseButtonWrapper)

    // Then
    expect(await ui.findByText(/u2.document.collapse_all_sections/)).toBeInTheDocument()

    // When
    await user.click(ui.getByText(/u2.document.collapse_all_sections/))

    // Then
    expect(ui.getByText('Parent section content')).not.toBeVisible()
    expect(ui.getByText('First child section content')).not.toBeVisible()
    expect(ui.getByText('Second child section content')).not.toBeVisible()
    expect(ui.getByText('Section content')).not.toBeVisible()

    // When
    await fireEvent.contextMenu(collapseButtonWrapper)

    // Then
    expect(ui.getByText(/u2.document.expand_all_sections/)).toBeInTheDocument()

    // When
    await user.click(ui.getByText(/u2.document.expand_all_sections/))

    // Then
    expect(ui.getByText('Parent section content')).toBeVisible()
    expect(ui.getByText('First child section content')).toBeVisible()
    expect(ui.getByText('Second child section content')).toBeVisible()
    expect(ui.getByText('Section content')).toBeVisible()
  })

  it('does not collapse excluded section on external refetch', async () => {
    // Given
    const user = userEvent.setup()
    const excludedSection = createDocumentSection({
      '@type': 'CountryByCountryReportSection',
      level: 1,
      include: false,
    })
    server.use(
      http.get(
        '/legacy/tasktype/tpm-country-by-country-report/:id/edit-document/data',
        async ({ params }) => {
          invariant(Number(params.id) === task['u2:extra'].taskTypeId)
          return HttpResponse.json(
            {
              name: 'a name',
              newSectionPath: 'a path',
              numbering: {},
              attachments: [],
              sections: [excludedSection],
              userCanEditContent: true,
            },
            { status: StatusCodes.OK }
          )
        }
      ),

      http.get('/legacy/tasktype/tpm-country-by-country-report/:id/content', async ({ params }) => {
        invariant(Number(params.id) === task['u2:extra'].taskTypeId)

        return HttpResponse.json([{ id: excludedSection.id, content: 'Excluded content' }], {
          status: StatusCodes.OK,
        })
      })
    )
    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          AppSidebar: true,
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    expect(ui.getByText(excludedSection.name)).toBeVisible()
    expect(ui.getByText('Excluded content')).not.toBeVisible()

    const expandButton = await ui.findByLabelText(`u2.expand: ${excludedSection.name}`)

    // When
    await user.click(expandButton)

    // Then
    expect(ui.getByText('Excluded content')).toBeVisible()

    // When
    const taskParams = {
      id: task['u2:extra'].taskTypeId,
      shortName: task['u2:extra'].shortName,
    }
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections: [{ ...excludedSection, name: 'Excluded section updated name' }],
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )
    await queryClient.invalidateQueries(queries.document.editDocumentData(taskParams))
    await queryClient.invalidateQueries(queries.document.editDocumentData(taskParams)._ctx.sections)

    // Then
    expect(ui.getByText('Excluded section updated name')).toBeVisible()
    expect(ui.getByText('Excluded content')).toBeVisible()
  })

  it('excludes section and its subsections', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get(
        '/legacy/tasktype/tpm-country-by-country-report/:id/edit-document/data',
        async ({ params }) => {
          invariant(task['u2:extra'].taskTypeId === Number(params.id))
          return HttpResponse.json(
            {
              name: 'a name',
              newSectionPath: 'a path',
              numbering: {},
              attachments: [],
              sections: [
                { ...parentSection, required: false, include: true },
                childSection1,
                childSection2,
              ],
              userCanEditContent: true,
            },
            { status: StatusCodes.OK }
          )
        }
      ),
      http.get(
        '/legacy/structured-document/section/tpm-country-by-country-report-section/:id/exclude',
        async ({ params }) => {
          invariant(findResourceById(params.id, [parentSection]))

          return HttpResponse.json(undefined, { status: StatusCodes.OK })
        }
      )
    )

    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          PageHeader: true,
          AppSidebar: true,
          TaskInformationAside: true,
          TaskReviewAside: true,
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    // When
    const sectionMenuButton = await ui.findByLabelText(
      'u2.document.section_controls: Parent section name'
    )
    await user.click(sectionMenuButton)

    server.use(
      http.get(
        '/legacy/tasktype/tpm-country-by-country-report/:id/edit-document/data',
        async ({ params }) => {
          invariant(task['u2:extra'].taskTypeId === Number(params.id))
          return HttpResponse.json(
            {
              name: 'a name',
              newSectionPath: 'a path',
              numbering: {},
              attachments: [],
              sections: [
                { ...parentSection, include: false, required: false },
                childSection1,
                childSection2,
              ],
              userCanEditContent: true,
            },
            { status: StatusCodes.OK }
          )
        }
      ),
      http.get(
        '/legacy/structured-document/section/tpm-country-by-country-report-section/:id/exclude',
        async ({ params }) => {
          invariant(findResourceById(params.id, [parentSection]))

          return HttpResponse.json(undefined, { status: StatusCodes.OK })
        }
      )
    )

    await user.click(ui.getByRole('menuitem', { name: /u2_structureddocument.exclude/ }))

    // Then
    const sectionTitles = ui.getAllByRole('heading')
    expect(sectionTitles[0].textContent).toContain(parentSection.name)
    expect(sectionTitles[0].textContent).toContain('-')
    expect(ui.getByText('Parent section content')).not.toBeVisible()
    expect(ui.getByText(childSection1.name)).not.toBeVisible()
    expect(ui.getByText(childSection2.name)).not.toBeVisible()

    // When
    const expandButton = await ui.findByLabelText(`u2.expand: ${parentSection.name}`)
    await user.click(expandButton)

    // Then
    expect(ui.getByText('Parent section content')).toBeVisible()
    const sectionTitlesAfter = ui.getAllByRole('heading')
    expect(sectionTitlesAfter[1].textContent).toContain(childSection1.name)
    expect(sectionTitlesAfter[1].textContent).toContain('-.')
    expect(sectionTitlesAfter[2].textContent).toContain(childSection2.name)
    expect(sectionTitlesAfter[2].textContent).toContain('-.')

    expect(ui.getByText('First child section content')).toBeVisible()
    expect(ui.getByText('Second child section content')).toBeVisible()
  })

  it('prevents edits on a section', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections: [{ ...parentSection, editable: true }],
            userCanEditContent: true,
            userCanEditConfiguration: true,
          },
          { status: StatusCodes.OK }
        )
      }),
      http.get(
        '/legacy/structured-document/section/tpm-country-by-country-report-section/:id/prevent-edit',
        async ({ params }) => {
          invariant(parentSection.id === Number(params.id))
          return HttpResponse.json(undefined, { status: StatusCodes.OK })
        }
      )
    )

    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          PageHeader: true,
          AppSidebar: true,
          TaskInformationAside: true,
          TaskReviewAside: true,
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    // When
    const sectionControlsButton = await ui.findByLabelText(
      `u2.document.section_controls: ${parentSection.name}`
    )
    await user.click(sectionControlsButton)

    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections: [{ ...parentSection, include: true, editable: false }],
            userCanEditContent: true,
            userCanEditConfiguration: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const preventEditsButton = await ui.findByRole('menuitem', {
      name: /u2_structureddocument.prevent_edits/,
    })
    await user.click(preventEditsButton)

    // Make sure that the cog menu is closed because menu items have icons (Prevent edits has a lock icon)
    await waitFor(() => {
      expect(ui.queryAllByRole('menuitem')).toHaveLength(0)
    })

    // Then
    const sectionTitles = ui.getAllByRole('heading')
    expect(sectionTitles[0].textContent).toContain(parentSection.name)
    expect(sectionTitles[0]).toContain(
      ui.getByRole('graphics-symbol', {
        name: 'lock-closed',
      })
    )
  })

  it('cancels edits when cancel button is clicked', async () => {
    // Given
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const mockConfirm = vi.fn()
    window.confirm = mockConfirm
    mockConfirm.mockReturnValue(true)
    const section = createDocumentSection({
      name: 'Section',
      editable: true,
      level: 1,
    })

    const user = userEvent.setup()
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            name: 'a name',
            newSectionPath: 'a path',
            numbering: {},
            attachments: [],
            sections: [section],
            userCanEditConfiguration: true,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          PageHeader: true,
          AppSidebar: true,
          TaskInformationAside: true,
          TaskReviewAside: true,
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    // When
    const sectionTitle = ui.getByText(section.name)
    await user.click(sectionTitle)
    await user.clear(sectionTitle)
    await user.keyboard('123')

    // Then
    expect(ui.getByText('123')).toBeInTheDocument()
    expect(ui.queryByText(section.name)).toBeNull()

    // When
    await user.click(ui.container.querySelector('[data-mce-name="cancel"]') as HTMLButtonElement)

    // Then
    expect(mockConfirm).toHaveBeenCalledTimes(1)
    expect(ui.getByText(section.name)).toBeInTheDocument()
  })

  it('sets initial values of the section forms correctly on cancel after save', async () => {
    // Given
    const mockConfirm = vi.fn()
    window.confirm = mockConfirm
    mockConfirm.mockReturnValue(true)
    const documentApiSpy = vi.spyOn(DocumentApi, 'updateDocumentSection')
    const sectionContent = '<p>Section content</p>'
    const user = userEvent.setup()
    const sectionToEdit = createDocumentSection({
      '@type': 'CountryByCountryReportSection',
      id: 5,
      level: 1,
      name: 'Edit me',
    })
    server.use(
      http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
        return HttpResponse.json(
          {
            sections: [{ ...sectionToEdit, editable: true, content: sectionContent }],
            userCanEditConfiguration: true,
            userCanEditContent: true,
          },
          { status: StatusCodes.OK }
        )
      }),
      http.post(
        `/legacy/structured-document/section/tpm-country-by-country-report-section/${sectionToEdit.id}/edit`,
        async () => {
          sectionToEdit.name = '123'
          return HttpResponse.json({}, { status: StatusCodes.OK })
        }
      )
    )

    const ui = render(wrapInSuspense(DocumentEdit), {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
          }),
        ],
        stubs: {
          PageHeader: true,
          AppSidebar: true,
          TaskInformationAside: true,
          TaskReviewAside: true,
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Document comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    const sectionTitleText = ui.getByText(sectionToEdit.name)
    const newSectionTitle = '123'

    expect(ui.queryByRole('textbox', { name: sectionToEdit.name })).not.toBeInTheDocument()

    // When
    await user.click(sectionTitleText)

    // Then
    expect(await ui.findByRole('textbox', { name: sectionToEdit.name })).toBeInTheDocument()

    // When
    await user.clear(sectionTitleText)
    await user.keyboard(newSectionTitle)

    // TODO: Type something in the tinymce editor (content field) and check that the edited content is submitted

    await user.click(ui.container.querySelector('[data-mce-name="save"]') as HTMLButtonElement)

    await waitFor(() => {
      expect(ui.container.querySelector('[data-mce-name="save"]')).not.toBeInTheDocument()
    })

    // Then
    expect(documentApiSpy).toHaveBeenCalledWith({
      id: sectionToEdit.id,
      type: section['@type'],
      content: '<p>Section content</p>',
      name: newSectionTitle,
    })

    const sectionTitleTextAfterSave = ui.getByText(newSectionTitle)
    expect(ui.queryByRole('textbox', { name: newSectionTitle })).not.toBeInTheDocument()

    // When
    await user.click(sectionTitleTextAfterSave)

    // Then
    expect(await ui.findByRole('textbox', { name: newSectionTitle })).toBeInTheDocument()

    // When
    await user.clear(sectionTitleTextAfterSave)
    await user.keyboard('456')
    await user.click(ui.container.querySelector('[data-mce-name="cancel"]') as HTMLButtonElement)

    await waitFor(() => {
      expect(ui.container.querySelector('[data-mce-name="cancel"]')).not.toBeInTheDocument()
    })

    // Then
    expect(ui.queryByText('456')).not.toBeInTheDocument()
    const sectionTitleTextAfterCancel = ui.getByText(newSectionTitle)
    expect(sectionTitleTextAfterCancel).toBeInTheDocument()

    // When
    await user.click(sectionTitleTextAfterCancel)

    // Then
    expect(await ui.findByRole('textbox', { name: newSectionTitle })).toBeInTheDocument()
  })

  describe('new sections', function () {
    async function expectPageRefresh(ui: ReturnType<typeof render>) {
      expect(
        ui.container.querySelector(
          `[contenteditable][data-placeholder="*** u2_structureddocument.new_section ***"]`
        )
      ).toBeInTheDocument()
      expect(ui.getByText('*** u2_structureddocument.new_section_content ***')).toBeInTheDocument()
    }

    beforeEach(() => {
      server.use(
        http.post(
          `/legacy/structured-document/section/tpm-country-by-country-report-section/new/before/${parentSection.id}`,
          async () => {
            return HttpResponse.json([], { status: StatusCodes.OK })
          }
        ),
        http.post(
          `/legacy/structured-document/section/tpm-country-by-country-report-section/new/after/${parentSection.id}`,
          async () => {
            return HttpResponse.json([], { status: StatusCodes.OK })
          }
        ),
        http.post(
          `/legacy/structured-document/section/tpm-country-by-country-report-section/new/subsection-of/${parentSection.id}`,
          async () => {
            return HttpResponse.json([], { status: StatusCodes.OK })
          }
        ),
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: [{ ...parentSection, editable: true }],
                userCanEditContent: true,
                userCanEditConfiguration: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
          return HttpResponse.json([{ id: parentSection.id, content: 'Parent section content' }], {
            status: StatusCodes.OK,
          })
        })
      )
    })

    // eslint-disable-next-line vitest/expect-expect
    it('adds new section before', async () => {
      // Given
      const ui = render(wrapInSuspense(DocumentEdit), {
        global: {
          plugins: [
            createTestingPinia({
              stubActions: false,
            }),
          ],
          stubs: {
            PageHeader: true,
            AppSidebar: true,
            TaskInformationAside: true,
            TaskReviewAside: true,
            EntityPermissionsAside: {
              template: '<div>Entity permissions aside stubbed content</div>',
            },
            TaskComments: {
              template: '<div>Document comments section stubbed content</div>',
            },
          },
        },
      })

      await flushPromises()

      server.use(
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: [
                  { ...parentSection, editable: true },
                  createDocumentSection({
                    id: 20,
                    name: '***new-section***',
                    include: true,
                    editable: true,
                    level: 1,
                  }),
                ],
                userCanEditContent: true,
                userCanEditConfiguration: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
          return HttpResponse.json(
            [
              { id: parentSection.id, content: 'Parent section content' },
              { id: 20, content: '' },
            ],
            { status: StatusCodes.OK }
          )
        })
      )

      // When
      await clickSectionControlItem(
        ui,
        parentSection.name,
        /u2_structureddocument.add_section_before/
      )
      await flushPromises()

      // Then
      await expectPageRefresh(ui)
    })

    // eslint-disable-next-line vitest/expect-expect
    it('adds new section after', async () => {
      // Given
      const ui = render(wrapInSuspense(DocumentEdit), {
        global: {
          plugins: [
            createTestingPinia({
              stubActions: false,
            }),
          ],
          stubs: {
            PageHeader: true,
            AppSidebar: true,
            TaskInformationAside: true,
            TaskReviewAside: true,
            EntityPermissionsAside: {
              template: '<div>Entity permissions aside stubbed content</div>',
            },
            TaskComments: {
              template: '<div>Document comments section stubbed content</div>',
            },
          },
        },
      })

      await flushPromises()

      server.use(
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: [
                  { ...parentSection, editable: true },
                  createDocumentSection({
                    id: 20,
                    include: true,
                    editable: true,
                    name: '***new-section***',
                    level: 1,
                  }),
                ],
                userCanEditContent: true,
                userCanEditConfiguration: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
          return HttpResponse.json(
            [
              { id: parentSection.id, content: 'Parent section content' },
              { id: 20, content: '*** u2_structureddocument.new_section_content ***' },
            ],
            { status: StatusCodes.OK }
          )
        })
      )

      // When
      await clickSectionControlItem(
        ui,
        parentSection.name,
        /u2_structureddocument.add_section_after/
      )
      await flushPromises()

      // Then
      await expectPageRefresh(ui)
    })

    // eslint-disable-next-line vitest/expect-expect
    it('adds new subsection', async () => {
      // Given
      const ui = render(wrapInSuspense(DocumentEdit), {
        global: {
          plugins: [
            createTestingPinia({
              stubActions: false,
            }),
          ],
          stubs: {
            PageHeader: true,
            AppSidebar: true,
            TaskInformationAside: true,
            TaskReviewAside: true,
            EntityPermissionsAside: {
              template: '<div>Entity permissions aside stubbed content</div>',
            },
            TaskComments: {
              template: '<div>Document comments section stubbed content</div>',
            },
          },
        },
      })

      await flushPromises()

      server.use(
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: [
                  { ...parentSection, editable: true },
                  createDocumentSection({
                    id: 20,
                    include: true,
                    editable: true,
                    name: '***new-section***',
                    level: 1,
                  }),
                ],
                userCanEditContent: true,
                userCanEditConfiguration: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
          return HttpResponse.json(
            [
              { id: parentSection.id, content: 'Parent section content' },
              { id: 20, content: '*** u2_structureddocument.new_section_content ***' },
            ],
            { status: StatusCodes.OK }
          )
        })
      )

      // When
      await clickSectionControlItem(ui, parentSection.name, /u2_structureddocument.add_subsection/)
      await flushPromises()

      // Then
      await expectPageRefresh(ui)
    })

    // eslint-disable-next-line vitest/expect-expect
    it('adds an initial section', async () => {
      // Given
      const user = userEvent.setup()

      server.use(
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: [],
                userCanEditContent: true,
                userCanEditConfiguration: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.post(
          '/legacy/structured-document/section/tpm-country-by-country-report/create-initial-section-for-document/:documentId',
          async () => {
            return HttpResponse.json([], { status: StatusCodes.OK })
          }
        )
      )

      const ui = render(wrapInSuspense(DocumentEdit), {
        global: {
          plugins: [
            createTestingPinia({
              stubActions: false,
            }),
          ],
          stubs: {
            PageHeader: true,
            AppSidebar: true,
            TaskInformationAside: true,
            TaskReviewAside: true,
            EntityPermissionsAside: true,
            TaskComments: true,
          },
        },
      })

      await flushPromises()

      server.use(
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: [
                  createDocumentSection({
                    id: 20,
                    include: true,
                    editable: true,
                    name: '***new-section***',
                    level: 1,
                  }),
                ],
                userCanEditContent: true,
                userCanEditConfiguration: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
          return HttpResponse.json(
            [{ id: 20, content: '*** u2_structureddocument.new_section_content ***' }],
            { status: StatusCodes.OK }
          )
        })
      )

      // When
      await user.click(ui.getByRole('button', { name: /u2_tpm.add_first_section/ }))
      await flushPromises()

      // Then
      await expectPageRefresh(ui)
    })
  })
})
