import { countryApiBasePath } from '@js/api/countryApi'
import { datasheetApiBasePath } from '@js/api/datasheetApi'
import { itemCountryReportApiBasePath } from '@js/api/itemCountryReportApi'
import { periodApiBasePath } from '@js/api/periodApi'
import { unitHierarchyApiBasePath } from '@js/api/unitHierarchyApi'
import { createTestingPinia } from '@pinia/testing'
import {
  chooseOption,
  createHydraCollection,
  findResourceById,
  setupServer,
  wrapInSuspense,
} from '@tests/utils'
import { fireEvent, render } from '@testing-library/vue'
import { createPeriod } from '@tests/__factories__/createPeriod'
import { createUnitHierarchy } from '@tests/__factories__/createUnitHierarchy'
import { createItem } from '@tests/__factories__/createItem'
import { createItemCountryReport } from '@tests/__factories__/createItemCountryReport'
import { beforeAll } from 'vitest'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { userEvent } from '@testing-library/user-event'
import { mockIntersectionObserver, mockResizeObserver } from 'jsdom-testing-mocks'
import { createRouter, createWebHistory } from 'vue-router'
import { createDatasheetCollection } from '@tests/__factories__/createDatasheetCollection'
import { createCurrency } from '@tests/__factories__/createCurrency'
import { itemApi } from '@js/api/itemApi'
import routes from '@js/router/datasheetCollections'
import datasheetRoutes from '@js/router/datasheets'
import ItemCountryReport from '@js/pages/datasheets/breakdown/unit-hierarchy/by-country.vue'
import { systemSettingApi } from '@js/api/systemSettingApi'
import { currencyApi } from '@js/api/currencyApi'
import type { Router } from 'vue-router'

const period = createPeriod({ name: 'Period 1' })
const layoutCollection = createDatasheetCollection()
const anotherPeriod = createPeriod({ name: 'Period 2' })
const unitHierarchy = createUnitHierarchy()
const anotherUnitHierarchy = createUnitHierarchy()
const currency = createCurrency()

const formulaElementItem = createItem({ formula: null, formulaReadable: null })
const item = createItem({
  name: 'The name',
  editable: false,
})

const report = createItemCountryReport({ items: [item['@id']] })
const server = setupServer(
  http.get(unitHierarchyApiBasePath + '/:id', ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [unitHierarchy, anotherUnitHierarchy]), {
      status: StatusCodes.OK,
    })
  ),
  http.get(periodApiBasePath + '/:id', ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [period, anotherPeriod]), {
      status: StatusCodes.OK,
    })
  ),
  http.get(itemCountryReportApiBasePath + '/:id', ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [report]), { status: StatusCodes.OK })
  ),
  http.get(itemApi.basePath + '/:id', ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [item, formulaElementItem]), {
      status: StatusCodes.OK,
    })
  ),
  http.get(systemSettingApi.basePath, () =>
    HttpResponse.json(
      {
        applicationCurrency: currencyApi.basePath + '/' + currency.id,
      },
      { status: StatusCodes.OK }
    )
  ),
  http.get(`${currencyApi.basePath}/:id`, ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [currency]), { status: StatusCodes.OK })
  ),
  http.get(periodApiBasePath, () =>
    HttpResponse.json(createHydraCollection([period, anotherPeriod]), { status: StatusCodes.OK })
  ),
  http.get(countryApiBasePath, () =>
    HttpResponse.json(createHydraCollection(), { status: StatusCodes.OK })
  ),
  http.get(datasheetApiBasePath, () =>
    HttpResponse.json(createHydraCollection(), { status: StatusCodes.OK })
  ),
  http.get(unitHierarchyApiBasePath, () =>
    HttpResponse.json(createHydraCollection([unitHierarchy, anotherUnitHierarchy]), {
      status: StatusCodes.OK,
    })
  ),
  http.get('/api/items/:id/unit-hierarchy-country-breakdown-data', ({ params }) => {
    expect(Number(params.id)).toBe(item.id)
    return HttpResponse.json({}, { status: StatusCodes.OK })
  })
)

vi.unmock('vue-router')

describe('ItemCountryReport Page', () => {
  let router: Router
  beforeEach(async () => {
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
    mockIntersectionObserver()
    mockResizeObserver()
    router = createRouter({
      history: createWebHistory(),
      routes: [...routes, ...datasheetRoutes],
    })
    router.push(
      `/datasheets/breakdown/unit-hierarchy/by-country?layoutCollection=${layoutCollection.id}&report=${report.id}&period=${period.id}&unitHierarchy=${unitHierarchy.id}`
    )
    await router.isReady()
  })

  beforeAll(() => server.listen())

  afterAll(() => server.close())
  afterEach(() => server.resetHandlers())

  it('fetches new data after switching parameters', async function () {
    const user = userEvent.setup()

    const ui = render(wrapInSuspense(ItemCountryReport), {
      global: {
        plugins: [createTestingPinia(), router],
      },
    })

    // Ensure page has loaded
    expect(await ui.findByText(item.refId)).toBeInTheDocument()

    expect(ui.getByText('u2.datasheets.item_country_report')).toBeInTheDocument()
    expect(ui.getByText(period.name)).toBeInTheDocument()
    expect(ui.getByText(unitHierarchy.name)).toBeInTheDocument()

    // When
    await user.click(ui.getByLabelText('config'))
    await chooseOption(ui, 'group_view_parameters_form[period]', anotherPeriod.name)
    await chooseOption(ui, 'group_view_parameters_form[unitHierarchy]', anotherUnitHierarchy.name)
    const button = ui.getByRole('graphics-symbol', { name: 'save' })
    await user.click(button)

    // Then
    expect(ui.getByText(anotherPeriod.name)).toBeInTheDocument()
    expect(ui.getByText(anotherUnitHierarchy.name)).toBeInTheDocument()
  })

  it('shows a breakdown link in item #popupcard-dropdown-extra', async function () {
    const user = userEvent.setup()
    const routerPushSpy = vi.spyOn(router, 'push')

    const ui = render(wrapInSuspense(ItemCountryReport), {
      global: {
        plugins: [createTestingPinia(), router],
      },
    })

    // Ensure page has loaded
    expect(await ui.findByText(item.refId)).toBeInTheDocument()

    // When
    await user.click(ui.getByText(item.refId))
    const meatball = ui.getByLabelText('u2.open_menu')
    await fireEvent.click(meatball)

    await user.click(ui.getByText('u2.breakdown'))

    // When
    expect(routerPushSpy).toHaveBeenCalled()
  })
})
