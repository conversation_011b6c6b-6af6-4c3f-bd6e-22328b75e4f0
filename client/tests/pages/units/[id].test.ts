import { createUser } from '@tests/__factories__/createUser'
import { createLegalUnit } from '@tests/__factories__/createLegalUnit'
import axios from 'axios'
import { createTestingP<PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { createHydraCollection, setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { render, within } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import NewByTypeDialog from '@js/components/unit/NewByTypeDialog.vue'
import UnitAssignedUserGroupsAside from '@js/components/unit/UnitAssignedUserGroupsAside.vue'
import UnitAssignedUsersAside from '@js/components/unit/UnitAssignedUsersAside.vue'
import UnitEdit from '@js/pages/units/[id].vue'
import { useNotificationsStore } from '@js/stores/notifications'
import { useAuthStore } from '@js/stores/auth'

const unit = createLegalUnit({
  id: 10,
  'u2:extra': { canAddAttachment: true },
})

const server = setupServer(
  http.get('/api/users', async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get('/api/user-groups', async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get('/api/units/10/groups', async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get('/api/units/10/direct-users', async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get('/api/units/10/inherited-users', async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get('/legacy/user/1/rights-list', async () => {
    return HttpResponse.json({ html: '' }, { status: StatusCodes.OK })
  }),
  http.delete(unit['@id'], async () => {
    return new Response(null, { status: StatusCodes.NO_CONTENT })
  }),
  http.get('/api/units', async () => {
    return HttpResponse.json(createHydraCollection([unit, createLegalUnit()]), {
      status: StatusCodes.OK,
    })
  }),
  http.get('/api/users/:id', async () => {
    return HttpResponse.json(createUser(), { status: StatusCodes.OK })
  }),
  http.get(`/api/units/${unit.id}/logs`, async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  })
)

const axiosDeleteSpy = vi.spyOn(axios, 'delete')

describe('Unit Edit Page', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })
  beforeAll(() => server.listen())
  afterAll(() => server.close())
  afterEach(() => server.resetHandlers())

  it('renders', async () => {
    server.use(
      http.get(`/api/units/${unit.id}/logs`, async () => {
        return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
      }),
      http.get(`/api/legal-units/${unit.id}/attachments`, async () => {
        return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
      }),
      http.get('/api/configuration/file-types', async () => {
        return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
      })
    )
    const ui = render(UnitEdit, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          LegalUnitEditor: true,
        },
      },
      props: {
        unit,
      },
    })

    const authStore = useAuthStore()
    authStore.$patch({
      user: {
        roles: ['ROLE_USER_GROUP_ADMIN', 'ROLE_UNIT_MANAGER'],
      },
    })

    await flushPromises()

    const pageHeader = ui.container.querySelector('[id="page-header"]') as HTMLElement
    expect(
      within(pageHeader).getByRole('heading', { name: `u2.unit ${unit.refId} ${unit.name}` })
    ).toBeInTheDocument()

    expect(
      within(pageHeader).getByRole('graphics-symbol', { name: 'legal-unit' })
    ).toBeInTheDocument()

    const asideSections = ui.getAllByRole('complementary')

    expect(within(asideSections[0]).getByText('u2.information')).toBeInTheDocument()
    expect(within(asideSections[1]).getByText('u2_core.attachments (0)')).toBeInTheDocument()
    expect(within(asideSections[1]).getByText('u2_core.assigned_users')).toBeInTheDocument()
    expect(within(asideSections[1]).getByText('u2.assigned_user_groups')).toBeInTheDocument()
    expect(within(asideSections[1]).getByText('u2_core.changes')).toBeInTheDocument()
  })

  it('hides entity and user and group aside section if user is not user group admin', async () => {
    const page = mount(UnitEdit, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          LegalUnitEditor: true,
          UnitInformationAside: true,
          EntityAttachmentsAside: true,
          EntityAuditLogAside: true,
        },
      },
      props: {
        unit: {
          ...unit,
          'u2:extra': {
            canAddAttachment: true,
          },
        },
        unitProperties: [],
      },
    })

    await flushPromises()

    expect(page.findComponent(UnitAssignedUserGroupsAside).exists()).toBe(false)
    expect(page.findComponent(UnitAssignedUsersAside).exists()).toBe(false)
  })

  it('shows and hides new unit dialog', async () => {
    const page = mount(UnitEdit, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          UnitInformationAside: true,
          EntityAttachmentsAside: true,
          EntityAuditLogAside: true,
          LegalUnitEditor: true,
        },
      },
      props: {
        unit,
      },
    })

    const authStore = useAuthStore()
    authStore.$patch({
      user: {
        roles: ['ROLE_USER_GROUP_ADMIN', 'ROLE_UNIT_MANAGER'],
      },
    })

    await flushPromises()

    expect(page.findComponent(NewByTypeDialog).exists()).toBe(false)
    await page.find('#button-new-unit-by-type').trigger('click')

    const dialog = page.findComponent(NewByTypeDialog)
    expect(dialog.exists()).toBe(true)

    await dialog.vm.$emit('close')
    expect(page.findComponent(NewByTypeDialog).exists()).toBe(false)
  })

  it('deletes a unit', async () => {
    const user = userEvent.setup()
    const ui = render(UnitEdit, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          UnitInformationAside: true,
          EntityAttachmentsAside: true,
          EntityAuditLogAside: true,
          LegalUnitEditor: true,
        },
      },
      props: {
        unit,
      },
    })

    const authStore = useAuthStore()
    authStore.$patch({
      user: {
        roles: ['ROLE_USER_GROUP_ADMIN', 'ROLE_UNIT_MANAGER'],
      },
    })

    const notificationsStore = useNotificationsStore()
    await flushPromises()

    // When

    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText(/u2.delete/))

    await user.click(within(ui.getByRole('dialog')).getByText(/u2.delete/))

    // Then
    await flushPromises()
    expect(axiosDeleteSpy).toHaveBeenCalledWith(unit['@id'])
    expect(notificationsStore.addSuccess).toHaveBeenCalledWith('u2.success_removed')
  })
})
