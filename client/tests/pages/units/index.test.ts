import { flushPromises, mount } from '@vue/test-utils'
import { useRoute } from 'vue-router'
import { createTestingP<PERSON> } from '@pinia/testing'
import { fromPartial } from '@total-typescript/shoehorn'
import { HttpResponse, http } from 'msw'
import { setupServer, wrapInSuspense } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { render } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import { expect } from 'vitest'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import NewByTypeDialog from '@js/components/unit/NewByTypeDialog.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import UnitList from '@js/pages/units/index.vue'
import { useAuthStore } from '@js/stores/auth'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

const server = setupServer(
  http.get('/api/users', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/countries', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/currencies', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/configuration/branches', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/configuration/auditors', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/configuration/legal-forms', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/units', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('Unit list page', () => {
  beforeEach(async () => {
    mockIntersectionObserver()
  })

  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  beforeAll(() => {
    vi.mocked(useRoute).mockImplementation(() =>
      fromPartial<RouteLocationNormalizedLoaded>({
        query: { type: 'legal-unit' },
      })
    )
  })

  it('renders', async function () {
    const page = await mount(wrapInSuspense(UnitList), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppSelect: true,
          UqlTableControls: true,
          ButtonDropdown: true,
          ButtonDropdownItem: true,
        },
      },
    })
    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBeTruthy()
  })

  it('shows and hides new unit dialog', async () => {
    const page = await mount(wrapInSuspense(UnitList), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppSelect: true,
          ButtonDropdown: true,
          ButtonDropdownItem: true,
        },
      },
    })
    const authStore = useAuthStore()
    authStore.$patch({
      user: { id: 1, roles: ['ROLE_UNIT_MANAGER'] },
    })

    await flushPromises()
    expect(page.findComponent(NewByTypeDialog).exists()).toBe(false)

    await page.findComponent(ButtonNew).trigger('click')
    const dialog = page.findComponent(NewByTypeDialog)
    expect(dialog.exists()).toBe(true)

    await dialog.vm.$emit('close')
    expect(page.findComponent(NewByTypeDialog).exists()).toBe(false)
  })

  it('shows and hides import dialog', async () => {
    const user = userEvent.setup()
    const ui = render(wrapInSuspense(UnitList), {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    const authStore = useAuthStore()
    authStore.$patch({
      user: { id: 1, roles: ['ROLE_UNIT_MANAGER'] },
    })

    await flushPromises()

    // When
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)
    await user.click(ui.getByText(/u2.import.import/))

    // Then
    expect(ui.getByRole('dialog')).toBeInTheDocument()

    // When
    await user.click(ui.getByRole('button', { name: /cross/ }))

    // Then
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })
})
