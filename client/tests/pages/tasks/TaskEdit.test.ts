import { periodApiBasePath } from '@js/api/periodApi'
import { statusApiBasePath } from '@js/api/statusApi'
import { unitApiBasePath } from '@js/api/unitApi'
import { userApiBasePath } from '@js/api/userApi'
import { workflowApiBasePath } from '@js/api/workflowApi'
import { fireEvent, render } from '@testing-library/vue'
import { createWorkflow } from '@tests/__factories__/createWorkflow'
import { fromPartial } from '@total-typescript/shoehorn'
import { flushPromises, mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { HttpResponse, http } from 'msw'
import { createHydraCollection, findResourceById, setupServer, wrapInSuspense } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { createTask } from '@tests/__factories__/createTask'
import { createUnit } from '@tests/__factories__/createUnit'
import { createPeriod } from '@tests/__factories__/createPeriod'
import { createUser } from '@tests/__factories__/createUser'
import { createStatus } from '@tests/__factories__/createStatus'
import { userEvent } from '@testing-library/user-event'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import TaskEdit from '@js/pages/tasks/TaskEdit.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import type ButtonSave from '@js/components/buttons/ButtonSave.vue'
import type ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import type { Router } from 'vue-router'

const watcher = createUser()
const watcher2 = createUser()
const assignee = createUser()
const createdUpdatedBy = createUser()
const unit = createUnit()
const period = createPeriod()
const status = createStatus()
const task = createTask({
  taskType: 'tpm_master_file',
  watchers: [watcher['@id'], watcher2['@id']],
  assignee: assignee['@id'],
  status: status['@id'],
  unit: unit['@id'],
  period: period['@id'],
  reviews: [],
  checkStates: [],
  'u2:extra': {
    canAddReview: false,
    canAttach: true,
    canDelete: true,
    canViewConfiguration: false,
    canEditConfiguration: false,
    canViewDocumentPermissions: true,
    canRemoveReview: false,
    canViewDocument: true,
    canWrite: true,
    createdBy: createdUpdatedBy['@id'],
    hasDocument: true,
    hasMultipleOptionsForNew: true,
    hasUserReviewed: false,
    isImported: false,
    isUserWatching: false,
    taskTypeId: 3,
    updatedBy: createdUpdatedBy['@id'],
  },
})

const server = setupServer(
  http.get(
    '/legacy/tasktype/tpm-master-file/' + task['u2:extra'].taskTypeId + '/edit/form',
    async () => {
      return HttpResponse.json(
        {
          html: 'Form html fetched from backend',
          disabled: false,
        },
        { status: StatusCodes.OK }
      )
    }
  ),
  http.get(
    '/legacy/tpm-master-file/' + task['u2:extra'].taskTypeId + '/entity-information',
    async () => {
      return HttpResponse.json(
        {
          id: task['u2:extra'].taskTypeId,
          shortName: 'tpm-master-file',
          taskId: task.id,
        },
        { status: StatusCodes.OK }
      )
    }
  ),
  http.get('/api/users', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get(`${userApiBasePath}/:id`, ({ params }) =>
    HttpResponse.json(
      findResourceById(params.id, [watcher, watcher2, assignee, createdUpdatedBy]),
      { status: StatusCodes.OK }
    )
  ),
  http.get(`${statusApiBasePath}/:id`, ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [status]), { status: StatusCodes.OK })
  ),
  http.get(workflowApiBasePath, () =>
    HttpResponse.json(createHydraCollection([createWorkflow()]), { status: StatusCodes.OK })
  ),
  http.get(`${periodApiBasePath}/:id`, ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [period]), { status: StatusCodes.OK })
  ),
  http.get(`${unitApiBasePath}/:id`, ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [unit]), { status: StatusCodes.OK })
  ),
  http.get('/api/tasks/' + task.id + '/layout-collections', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/layout-collections', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),

  http.get(`/api/tasks/${task.id}/check-states`, async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get(`/api/workflows`, async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  }),
  http.get(`/legacy/tpm-master-file/entity-metatdata`, async () => {
    return HttpResponse.json({}, { status: StatusCodes.OK })
  }),
  http.get(`/api/tasks/${task.id}/comments`, async () => {
    return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
  })
)

describe('Task Edit Page', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  beforeEach(() => {
    vi.clearAllMocks()
    mockIntersectionObserver()
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        currentRoute: ref({
          fullPath: `/tpm/master-file/${task['u2:extra'].taskTypeId}/edit`,
          params: { id: task['u2:extra'].taskTypeId.toString() },
        }),
      })
    })
  })

  it('renders', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(task, { status: StatusCodes.OK })
      })
    )
    const ui = render(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          EntityAttachmentsAside: {
            template: '<div>Entity Attachment aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Task comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    // Then
    const meatball = await ui.findByLabelText('u2.open_menu')
    await fireEvent.click(meatball)

    expect(ui.getByRole('menuitem', { name: /u2.duplicate/ })).toBeInTheDocument()
    expect(ui.getByRole('menuitem', { name: /u2.delete/ })).toBeInTheDocument()
    expect(ui.getByRole('menuitem', { name: /PDF/ })).toBeInTheDocument()
    expect(ui.getByRole('menuitem', { name: /XML/ })).toBeInTheDocument()

    expect(ui.getByRole('button', { name: /u2.new/ })).toBeInTheDocument()
    expect(ui.getByRole('link', { name: /u2.list/ })).toBeInTheDocument()
    expect(ui.getByRole('link', { name: /u2.edit_content/ })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: /u2.save/ })).toBeInTheDocument()

    expect(ui.getByText('Task comments section stubbed content')).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2_core.workflow.checklist/ })).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2.information/ })).toBeInTheDocument()
    expect(ui.getByRole('heading', { name: /u2_core.reviews/ })).toBeInTheDocument()
    expect(
      ui.getByRole('heading', { name: task['u2:extra'].readableTaskType + ' # 3' })
    ).toBeInTheDocument()
    expect(ui.queryByText('u2_core.attachments')).not.toBeInTheDocument()

    expect(ui.getByRole('button', { name: `${status.name} arrow-up` })).toBeInTheDocument()
    expect(await ui.findByText('Form html fetched from backend')).toBeInTheDocument()
  })

  it('has a disabled save button if the form is disabled', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(task, { status: StatusCodes.OK })
      }),
      http.get('/legacy/tasktype/tpm-master-file/3/edit/form', async () => {
        return HttpResponse.json(
          { html: 'Form html fetched from backend', disabled: true },
          { status: StatusCodes.OK }
        )
      })
    )

    const page = mount(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          EntityPermissionsAside: {
            template: '<div>Entity permissions aside stubbed content</div>',
          },
          EntityAttachmentsAside: {
            template: '<div>Entity Attachment aside stubbed content</div>',
          },
          TaskComments: {
            template: '<div>Task comments section stubbed content</div>',
          },
        },
      },
    })

    await flushPromises()

    const btn = page.findComponent<typeof ButtonSave>('#task-save')

    expect(btn.props().disabled).toBe(true)
  })

  it('has a disabled edit content button if the user cannot view the content', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              canViewDocument: false,
            },
          },
          { status: StatusCodes.OK }
        )
      })
    )
    const ui = render(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()

    expect(ui.getByLabelText('u2.edit_content')).toHaveAttribute('aria-disabled', 'true')
    expect(ui.getByLabelText('u2.edit_content')).not.toHaveAttribute('href')
  })

  it('has a disabled new button if the user does not has write permission', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              canWrite: false,
            },
          },
          { status: StatusCodes.OK }
        )
      }),
      http.get('/editFormPath', async () => {
        return HttpResponse.json(
          { html: 'Form html fetched from backend', disabled: true },
          { status: StatusCodes.OK }
        )
      })
    )

    const ui = render(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()

    expect(ui.getByRole('button', { name: /u2.new/ })).toHaveAttribute('disabled', '')
  })

  it('has a disabled duplicate button if the user does not have write permission', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              canWrite: false,
            },
          },
          { status: StatusCodes.OK }
        )
      })
    )
    const page = mount(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()

    const meatball = page.findComponent(ButtonDropdownEllipsis)
    await meatball.find('button').trigger('click')
    const btn = page.findComponent<typeof ButtonBasic>('#task-duplicate')

    expect(btn.props().disabled).toBe(true)
  })

  it('shows a dialog when there are multiple options after clicking new', async () => {
    const user = userEvent.setup()
    server.use(
      http.get(
        '/legacy/tpm-master-file/' + task['u2:extra'].taskTypeId + '/entity-information',
        async () => {
          return HttpResponse.json(
            {
              id: task['u2:extra'].taskTypeId,
              shortName: 'tpm-master-file',
              taskId: task.id,
              optionsForNew: {},
            },
            { status: StatusCodes.OK }
          )
        }
      ),
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              hasMultipleOptionsForNew: true,
            },
          },
          { status: StatusCodes.OK }
        )
      })
    )
    const ui = render(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByLabelText('add'))

    // Then
    expect(await ui.findByRole('dialog')).toBeInTheDocument()
  })

  it('has a disabled duplicate button if the user cannot duplicate the task', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              canWrite: false,
            },
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const page = mount(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()

    const meatball = page.findComponent(ButtonDropdownEllipsis)
    await meatball.find('button').trigger('click')
    const btn = page.findComponent<typeof ButtonBasic>('#task-duplicate')
    expect(btn.props().disabled).toBe(true)
  })

  it('renders without document', async () => {
    const user = userEvent.setup()
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              hasDocument: false,
            },
          },
          { status: StatusCodes.OK }
        )
      })
    )
    const ui = render(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
        },
      },
    })

    await flushPromises()

    // Then
    expect(ui.queryByRole('button', { name: 'u2.edit_content' })).not.toBeInTheDocument()

    // When
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    //Then
    expect(ui.queryByText('Export')).not.toBeInTheDocument()
  })

  it('has a duplicate confirmation dialog when duplicate button is clicked', async () => {
    const user = userEvent.setup()
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(task, { status: StatusCodes.OK })
      })
    )
    const ui = render(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    // When
    const meatball = await ui.findByLabelText('u2.open_menu')
    await user.click(meatball)

    await user.click(ui.getByText('u2.duplicate'))

    // Then
    expect(await ui.findByRole('dialog')).toBeInTheDocument()
  })

  it('has a disabled delete button if the user cannot delete the task', async () => {
    server.use(
      http.get(`/api/tasks/${task.id}`, async () => {
        return HttpResponse.json(
          {
            ...task,
            'u2:extra': {
              ...task['u2:extra'],
              canDelete: false,
            },
          },
          { status: StatusCodes.OK }
        )
      })
    )
    const page = mount(wrapInSuspense(TaskEdit), {
      global: {
        plugins: [createTestingPinia({ stubActions: false })],
        stubs: {
          TaskEditForm: true,
          EntityAttachmentsAside: true,
          EntityPermissionsAside: true,
          TaskInformationAside: true,
          TaskChecklistAside: true,
          TaskComments: true,
          TaskReviewAside: true,
          TaskExportControl: true,
        },
      },
    })

    await flushPromises()

    const meatball = page.findComponent(ButtonDropdownEllipsis)
    await meatball.find('button').trigger('click')
    const btn = page.findComponent<typeof ButtonBasic>('#task-delete')
    expect(btn.props().disabled).toBe(true)
  })
})
