import choiceList from '@js/router/choice-list'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { defineComponent } from 'vue'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setupServer } from 'msw/node'
import sortBy from 'lodash/sortBy'
import { StatusCodes } from 'http-status-codes'
import { useRouter } from 'vue-router'
import { fromPartial } from '@total-typescript/shoehorn'
import { useAuthStore } from '@js/stores/auth'
import { useGlobalSearch } from '@js/composable/useGlobalSearch'
import type { Router } from 'vue-router'
import type { User } from '@js/model/user'
import type { SavedFilter } from '@js/model/saved-filter'
import type { Datasheet } from '@js/model/datasheet'
import type { DatasheetCollection } from '@js/model/datasheetCollection'

const savedFilters: Array<SavedFilter> = [
  fromPartial<SavedFilter>({
    id: 1,
    name: 'Test Filter',
    description: 'filter description',
    taskShortName: 'apm-transaction',
  }),
  fromPartial<SavedFilter>({
    id: 2,
    name: 'Another filter',
    description: 'another filter description',
    taskShortName: 'igt-igt2-transaction',
  }),
]

const user = fromPartial<User>({
  '@id': '/api/user/1',
  id: 1,
  username: 'jess',
})

const layoutCollection = fromPartial<DatasheetCollection>({
  id: 'layout-collection-1',
  name: 'Kiki 3',
})

const layout = fromPartial<Datasheet>({
  id: 1,
  name: 'Kiki 3 Layout',
})

const server = setupServer(
  http.get('/api/saved-filters', async () => {
    return HttpResponse.json({ 'hydra:member': savedFilters }, { status: StatusCodes.OK })
  }),
  http.get('/api/layout-collections', async () => {
    return HttpResponse.json({ 'hydra:member': [layoutCollection] }, { status: StatusCodes.OK })
  }),
  http.get('/api/layout-collections/layout-collection-1/layouts', async () => {
    return HttpResponse.json(
      {
        '@id': '/api/layout-collections/layout-collection-1/layouts',
        'hydra:member': [layout],
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/users/1/dashboards', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [
          {
            title: 'Dashboard 1',
            slug: 'demo',
            public: true,
            widgets: [],
          },
          {
            title: 'Dashboard 2',
            slug: 'd2',
            public: true,
            widgets: [],
          },
        ],
      },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/layouts', async () => {
    return HttpResponse.json(
      {
        'hydra:member': [
          {
            id: 1,
            group: 'Group 1',
            name: 'Kiki 3',
            createdBy: '/api/user/1',
            updatedBy: '/api/user/2',
          },
        ],
      },
      { status: StatusCodes.OK }
    )
  })
)

const createTestComponent = () => {
  return defineComponent({
    setup() {
      const { results, query } = useGlobalSearch()
      return {
        results,
        query,
      }
    },
    template: '<span>Test component</span>',
  })
}

describe('useGlobalSearch', () => {
  beforeAll(() => {
    server.listen()
    createTestingPinia({
      initialState: {
        auth: {
          user,
        },
      },
      fakeApp: true,
    })
  })

  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('finds saved filters matching a search query in the filter name', async () => {
    // Given
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        resolve: () => ({ href: 'mylink' }),
        getRoutes: () => [],
      })
    })

    const component = mount(createTestComponent(), {})

    // Then
    expect(component.vm.results).toEqual([])

    // When
    component.vm.query = 'Test'
    await flushPromises()

    // Then
    expect(component.vm.results).toContainEqual({
      icon: 'filter',
      name: `${savedFilters[0].name} - APM Transaction`,
      url: 'mylink',
      description: savedFilters[0].description,
    })

    // When
    component.vm.query = 'Another'

    // Then
    expect(component.vm.results).toContainEqual({
      icon: 'filter',
      name: `${savedFilters[1].name} - IGT 2 - Derivatives`,
      url: 'mylink',
      description: savedFilters[1].description,
    })
  })

  it('finds navigation items matching a search query', async () => {
    // Given
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        resolve: () => ({ href: 'mylink' }),
        getRoutes: () => [
          {
            meta: {
              auth: true,
              globalSearch: {
                icon: 'filter',
                name: () => 'Item xxx',
              },
            },
            path: '/route/1',
          },
          {
            meta: {
              auth: 'ROLE_USER_GROUP_ADMIN',
              globalSearch: {
                icon: 'cross',
                name: () => 'Second item xxx',
              },
            },
            path: '/route/2',
          },
          {
            meta: {
              globalSearch: {
                icon: 'list',
                name: () => 'Third item xxx',
              },
            },
            path: '/route/3xxx',
          },
          {
            meta: {
              auth: 'ROLE_USER',
              globalSearch: {
                icon: 'add',
                name: () => 'First item not in search',
              },
            },
            path: '/route/3',
          },
          {
            meta: {
              auth: 'ROLE_USER_GROUP_ADMIN',
            },
            path: '/route/:id/1',
          },
          {
            meta: {},
            path: '/route/1/edit',
          },
        ],
      })
    })

    useAuthStore().$patch({
      user: { ...user, roles: ['ROLE_USER_GROUP_ADMIN'] },
    })

    const component = mount(createTestComponent(), {})

    // Then
    expect(component.vm.results).toEqual([])

    // When
    component.vm.query = 'xxx'
    await flushPromises()

    // Then
    expect(sortBy(component.vm.results, ['name'])).toEqual(
      sortBy(
        [
          {
            icon: 'filter',
            name: 'Item xxx',
            url: '/route/1',
          },
          {
            icon: 'cross',
            name: 'Second item xxx',
            url: '/route/2',
          },
          {
            icon: 'list',
            name: 'Third item xxx',
            url: '/route/3xxx',
          },
        ],
        ['name']
      )
    )
  })

  it('finds dashboards matching a search query', async () => {
    // Given
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        resolve: () => ({ href: 'mylink' }),
        getRoutes: () => [],
      })
    })

    const component = mount(createTestComponent(), {})

    // Then
    expect(component.vm.results).toEqual([])

    // When
    component.vm.query = 'Dashboard'
    await flushPromises()

    // Then
    expect(component.vm.results).toContainEqual({
      icon: 'dashboard',
      name: 'Dashboard 1',
      url: 'mylink',
    })
  })

  it('finds layouts matching a search query', async () => {
    // Given
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        resolve: () => ({ href: 'mylink' }),
        getRoutes: () => [],
      })
    })

    const component = mount(createTestComponent(), {})

    // Then
    expect(component.vm.results).toEqual([])

    // When
    component.vm.query = 'Kiki'
    await flushPromises()

    // Then
    expect(component.vm.results).toContainEqual({
      icon: 'list',
      name: 'Kiki 3 Layout (Kiki 3)',
      url: 'mylink',
    })
    expect(component.vm.results.length).toBe(1)
  })

  it('finds choice field lists matching a search query', async () => {
    // Given
    vi.mocked(useRouter).mockImplementation(() => {
      return fromPartial<Router>({
        resolve: () => ({ href: 'mylink' }),
        getRoutes: () => choiceList,
      })
    })

    useAuthStore().$patch({
      user: { ...user, roles: ['ROLE_ADMIN'] },
    })

    const component = mount(createTestComponent(), {})

    // Then
    expect(component.vm.results).toEqual([])

    // When
    component.vm.query = 'specification'
    await flushPromises()

    // Then
    expect(component.vm.results).toContainEqual({
      icon: 'config',
      name: 'u2_tam.specification.plural',
      url: '/configuration/tasks/fields/specification',
    })
  })
})
