import { useTaskData } from '@js/composable/useTaskData'
import { useTaskStore } from '@js/stores/task'
import { useTaskInfoStore } from '@js/stores/task-info'
import urlToShortName from '@js/assets/router/urlToShortName'
import { createPinia, setActivePinia } from 'pinia'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { ref } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { TaskShortName } from '@js/model/task'

// Mock the dependencies
vi.mock('@js/assets/router/urlToShortName')
vi.mock('@js/stores/task')
vi.mock('@js/stores/task-info')

const mockUrlToShortName = vi.mocked(urlToShortName)
const mockUseTaskStore = vi.mocked(useTaskStore)
const mockUseTaskInfoStore = vi.mocked(useTaskInfoStore)

describe('useTaskData', () => {
  let mockTaskStore: any
  let mockTaskInfoStore: any
  let mockRoute: RouteLocationNormalizedLoaded

  beforeEach(() => {
    setActivePinia(createPinia())
    
    // Reset all mocks
    vi.clearAllMocks()

    // Mock task store
    mockTaskStore = {
      fetchTaskById: vi.fn(),
      refresh: vi.fn(),
      task: { value: { id: 123, name: 'Test Task' } }
    }
    mockUseTaskStore.mockReturnValue(mockTaskStore)

    // Mock task info store
    mockTaskInfoStore = {
      fetchTaskInformation: vi.fn(),
      refresh: vi.fn(),
      taskId: 123,
      optionsForNew: { value: { someOption: 'value' } }
    }
    mockUseTaskInfoStore.mockReturnValue(mockTaskInfoStore)

    // Mock route
    mockRoute = {
      fullPath: '/tasks/contract-management/123',
      params: { id: '123' },
      path: '/tasks/contract-management/123',
      name: 'task-edit',
      query: {},
      hash: '',
      matched: [],
      meta: {},
      redirectedFrom: undefined
    }

    // Mock urlToShortName
    mockUrlToShortName.mockReturnValue('contract-management' as TaskShortName)
  })

  describe('initialization', () => {
    it('should return the expected interface', () => {
      const result = useTaskData(mockRoute)

      expect(result).toHaveProperty('fetchTask')
      expect(result).toHaveProperty('task')
      expect(result).toHaveProperty('optionsForNew')
      expect(result).toHaveProperty('refreshTask')
      expect(typeof result.fetchTask).toBe('function')
      expect(typeof result.refreshTask).toBe('function')
    })

    it('should initialize stores correctly', () => {
      useTaskData(mockRoute)

      expect(mockUseTaskStore).toHaveBeenCalledOnce()
      expect(mockUseTaskInfoStore).toHaveBeenCalledOnce()
    })

    it('should extract route parameters correctly', async () => {
      const { fetchTask } = useTaskData(mockRoute)
      
      await fetchTask()

      expect(mockUrlToShortName).toHaveBeenCalledWith('/tasks/contract-management/123')
      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'contract-management',
        123
      )
    })
  })

  describe('fetchTask', () => {
    it('should fetch task information and task data in sequence', async () => {
      const { fetchTask } = useTaskData(mockRoute)
      
      await fetchTask()

      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'contract-management',
        123
      )
      expect(mockTaskStore.fetchTaskById).toHaveBeenCalledWith(123)
      
      // Verify the order of calls
      const fetchInfoCall = mockTaskInfoStore.fetchTaskInformation.mock.invocationCallOrder[0]
      const fetchTaskCall = mockTaskStore.fetchTaskById.mock.invocationCallOrder[0]
      expect(fetchInfoCall).toBeLessThan(fetchTaskCall)
    })

    it('should throw error if taskId is not defined after fetching task information', async () => {
      mockTaskInfoStore.taskId = undefined
      const { fetchTask } = useTaskData(mockRoute)

      await expect(fetchTask()).rejects.toThrow('Task ID is not defined')
      
      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalled()
      expect(mockTaskStore.fetchTaskById).not.toHaveBeenCalled()
    })

    it('should propagate errors from fetchTaskInformation', async () => {
      const error = new Error('Failed to fetch task information')
      mockTaskInfoStore.fetchTaskInformation.mockRejectedValue(error)
      
      const { fetchTask } = useTaskData(mockRoute)

      await expect(fetchTask()).rejects.toThrow('Failed to fetch task information')
      expect(mockTaskStore.fetchTaskById).not.toHaveBeenCalled()
    })

    it('should propagate errors from fetchTaskById', async () => {
      const error = new Error('Failed to fetch task')
      mockTaskStore.fetchTaskById.mockRejectedValue(error)
      
      const { fetchTask } = useTaskData(mockRoute)

      await expect(fetchTask()).rejects.toThrow('Failed to fetch task')
      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalled()
    })
  })

  describe('refreshTask', () => {
    it('should call refresh on both stores', () => {
      const { refreshTask } = useTaskData(mockRoute)
      
      refreshTask()

      expect(mockTaskInfoStore.refresh).toHaveBeenCalledOnce()
      expect(mockTaskStore.refresh).toHaveBeenCalledOnce()
    })
  })

  describe('reactive properties', () => {
    it('should return reactive task from task store', () => {
      const { task } = useTaskData(mockRoute)
      
      expect(task.value).toEqual({ id: 123, name: 'Test Task' })
    })

    it('should return reactive optionsForNew from task info store', () => {
      const { optionsForNew } = useTaskData(mockRoute)
      
      expect(optionsForNew.value).toEqual({ someOption: 'value' })
    })
  })

  describe('edge cases', () => {
    it('should handle route with different id parameter', async () => {
      const routeWithDifferentId = {
        ...mockRoute,
        params: { id: '456' },
        fullPath: '/tasks/contract-management/456'
      }
      
      const { fetchTask } = useTaskData(routeWithDifferentId)
      
      await fetchTask()

      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'contract-management',
        456
      )
    })

    it('should handle route with different short name', async () => {
      mockUrlToShortName.mockReturnValue('document-management' as TaskShortName)
      
      const { fetchTask } = useTaskData(mockRoute)
      
      await fetchTask()

      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'document-management',
        123
      )
    })

    it('should handle non-numeric id parameter', async () => {
      const routeWithStringId = {
        ...mockRoute,
        params: { id: 'abc' }
      }
      
      const { fetchTask } = useTaskData(routeWithStringId)
      
      await fetchTask()

      expect(mockTaskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'contract-management',
        NaN
      )
    })
  })
})
