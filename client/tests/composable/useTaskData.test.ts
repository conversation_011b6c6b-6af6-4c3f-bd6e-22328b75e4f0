import { useTaskData } from '@js/composable/useTaskData'
import { useTaskStore } from '@js/stores/task'
import { useTaskInfoStore } from '@js/stores/task-info'
import urlToShortName from '@js/assets/router/urlToShortName'
import { render } from '@testing-library/vue'
import { createTestingPinia } from '@pinia/testing'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { defineComponent, ref } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { TaskShortName } from '@js/model/task'

// Mock the dependencies
vi.mock('@js/assets/router/urlToShortName')

const mockUrlToShortName = vi.mocked(urlToShortName)

// Test component that uses the composable
const TestComponent = defineComponent({
  props: {
    route: {
      type: Object as () => RouteLocationNormalizedLoaded,
      required: true
    }
  },
  setup(props) {
    const composableResult = useTaskData(props.route)

    // Expose everything for testing
    return {
      ...composableResult,
      // Add a test method to trigger fetchTask
      triggerFetch: async () => {
        try {
          await composableResult.fetchTask()
          return { success: true }
        } catch (error) {
          return { success: false, error }
        }
      }
    }
  },
  template: `
    <div>
      <div data-testid="task-id">{{ task?.id || 'no-task' }}</div>
      <div data-testid="options">{{ optionsForNew?.someOption || 'no-options' }}</div>
      <button data-testid="fetch-btn" @click="triggerFetch">Fetch</button>
      <button data-testid="refresh-btn" @click="refreshTask">Refresh</button>
    </div>
  `
})

describe('useTaskData', () => {
  let mockRoute: RouteLocationNormalizedLoaded

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock route
    mockRoute = {
      fullPath: '/tasks/contract-management/123',
      params: { id: '123' },
      path: '/tasks/contract-management/123',
      name: 'task-edit',
      query: {},
      hash: '',
      matched: [],
      meta: {},
      redirectedFrom: undefined
    }

    // Mock urlToShortName
    mockUrlToShortName.mockReturnValue('contract-management' as TaskShortName)
  })

  describe('initialization', () => {
    it('should return the expected interface', () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      // Component should render without errors
      expect(getByTestId('task-id')).toBeDefined()
      expect(getByTestId('options')).toBeDefined()
      expect(getByTestId('fetch-btn')).toBeDefined()
      expect(getByTestId('refresh-btn')).toBeDefined()
    })

    it('should extract route parameters correctly when fetchTask is called', async () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskInfoStore = useTaskInfoStore()
      taskInfoStore.taskId = 123

      const fetchBtn = getByTestId('fetch-btn')
      await fetchBtn.click()

      expect(mockUrlToShortName).toHaveBeenCalledWith('/tasks/contract-management/123')
    })
  })

  describe('fetchTask', () => {
    it('should fetch task information and task data in sequence', async () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskStore = useTaskStore()
      const taskInfoStore = useTaskInfoStore()

      // Mock successful responses
      taskInfoStore.taskId = 123

      const fetchBtn = getByTestId('fetch-btn')
      await fetchBtn.click()

      expect(taskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'contract-management',
        123
      )
      expect(taskStore.fetchTaskById).toHaveBeenCalledWith(123)
    })

    it('should throw error if taskId is not defined after fetching task information', async () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskStore = useTaskStore()
      const taskInfoStore = useTaskInfoStore()

      // Mock taskId as undefined
      taskInfoStore.taskId = undefined

      const fetchBtn = getByTestId('fetch-btn')
      await fetchBtn.click()

      expect(taskInfoStore.fetchTaskInformation).toHaveBeenCalled()
      expect(taskStore.fetchTaskById).not.toHaveBeenCalled()
    })

    it('should propagate errors from fetchTaskInformation', async () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskStore = useTaskStore()
      const taskInfoStore = useTaskInfoStore()

      // Mock error
      const error = new Error('Failed to fetch task information')
      taskInfoStore.fetchTaskInformation.mockRejectedValue(error)

      const fetchBtn = getByTestId('fetch-btn')
      await fetchBtn.click()

      expect(taskStore.fetchTaskById).not.toHaveBeenCalled()
    })
  })

  describe('refreshTask', () => {
    it('should call refresh on both stores', async () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskStore = useTaskStore()
      const taskInfoStore = useTaskInfoStore()

      const refreshBtn = getByTestId('refresh-btn')
      await refreshBtn.click()

      expect(taskInfoStore.refresh).toHaveBeenCalledOnce()
      expect(taskStore.refresh).toHaveBeenCalledOnce()
    })
  })

  describe('reactive properties', () => {
    it('should display task data when available', () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({
            createSpy: vi.fn,
            initialState: {
              task: { task: { id: 123, name: 'Test Task' } }
            }
          })]
        }
      })

      expect(getByTestId('task-id')).toHaveTextContent('123')
    })

    it('should display options when available', () => {
      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({
            createSpy: vi.fn,
            initialState: {
              'task-info': { optionsForNew: { someOption: 'test-value' } }
            }
          })]
        }
      })

      expect(getByTestId('options')).toHaveTextContent('test-value')
    })
  })

  describe('edge cases', () => {
    it('should handle route with different id parameter', async () => {
      const routeWithDifferentId = {
        ...mockRoute,
        params: { id: '456' },
        fullPath: '/tasks/contract-management/456'
      }

      const { getByTestId } = render(TestComponent, {
        props: { route: routeWithDifferentId },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskInfoStore = useTaskInfoStore()
      taskInfoStore.taskId = 456

      const fetchBtn = getByTestId('fetch-btn')
      await fetchBtn.click()

      expect(taskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'contract-management',
        456
      )
    })

    it('should handle route with different short name', async () => {
      mockUrlToShortName.mockReturnValue('document-management' as TaskShortName)

      const { getByTestId } = render(TestComponent, {
        props: { route: mockRoute },
        global: {
          plugins: [createTestingPinia({ createSpy: vi.fn })]
        }
      })

      const taskInfoStore = useTaskInfoStore()
      taskInfoStore.taskId = 123

      const fetchBtn = getByTestId('fetch-btn')
      await fetchBtn.click()

      expect(taskInfoStore.fetchTaskInformation).toHaveBeenCalledWith(
        'document-management',
        123
      )
    })
  })
})
