import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { createFile } from '@tests/__factories__/createFile'
import { ref } from 'vue'
import { HttpResponse, http } from 'msw'
import { setupServer } from 'msw/node'
import { StatusCodes } from 'http-status-codes'
import useFileLinkedEntities from '@js/composable/useFileLinkedEntities'

describe('useFileLinkedEntities', () => {
  const file = createFile({
    '@id': '/api/files/1',
    id: 1,
    name: 'filename',
    accessType: 'smart',
    downloadPath: '/api/files/1',
    userPermissions: [],
    groupPermissions: [],
    types: [],
  })

  const server = setupServer(
    http.get('/api/files/1/linked-entities', async () => {
      return HttpResponse.json(
        {
          'hydra:member': [
            {
              linkedEntity: {
                '@id': '/api/tasks/123',
                '@type': 'Task',
                'u2:extra': {
                  shortName: 'tcm-other-deadline',
                  displayName: '1 Other Deadline for real',
                  editPath: '/tasks/123/edit',
                },
              },
            },
            {
              linkedEntity: {
                '@id': '/api/units/123',
                '@type': 'Unit',
                displayName: 'Unit 123',
              },
            },
            {
              linkedEntity: {
                '@id': '/api/master-file-sections/123',
                '@type': 'MasterFileSection',
                displayName: 'Master File 1: Section 123',
                document: '/api/master-file/1',
              },
            },
            { linkedEntity: null },
            { linkedEntity: null },
            { linkedEntity: null },
          ],
        },
        { status: StatusCodes.OK }
      )
    })
  )
  beforeAll(() => {
    server.listen()
    createTestingPinia()
  })

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('knows how many linked entities the user is not authorized to', async () => {
    const { fetchLinkedEntities, unauthorizedEntityCount } = useFileLinkedEntities(ref(file))
    expect(unauthorizedEntityCount.value).toBe(0)
    await fetchLinkedEntities()
    expect(unauthorizedEntityCount.value).toBe(3)
  })

  it('fetch linked entities', async () => {
    const { fetchLinkedEntities, linksByType } = useFileLinkedEntities(ref(file))
    expect(linksByType.value).toStrictEqual({})

    await fetchLinkedEntities()
    expect(linksByType.value).toStrictEqual({
      'u2_tcm.other_deadline': {
        humanReadableType: 'u2_tcm.other_deadline',
        links: [
          {
            name: '1 Other Deadline for real',
            url: '/tasks/123/edit',
          },
        ],
      },
      'u2.unit.plural': {
        humanReadableType: 'u2.unit.plural',
        links: [
          {
            name: 'Unit 123',
            url: undefined,
          },
        ],
      },
      'u2_tpm.master_file.plural': {
        humanReadableType: 'u2_tpm.master_file.plural',
        links: [
          {
            name: 'Master File 1: Section 123',
            url: undefined,
          },
        ],
      },
    })
  })
})
