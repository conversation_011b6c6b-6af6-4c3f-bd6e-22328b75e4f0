import { describe, expect, it } from 'vitest'
import { mapSectionIdToParentSection } from '@js/helper/document/mapSectionIdToParentSection'
import type { DocumentSection } from '@js/model/document'

describe('mapSectionIdToParentSectionId', () => {
  it('should correctly map sections to their parent section', () => {
    const sections = [
      { id: 0, level: 1 }, // 0
      { id: 1, level: 1 },
      { id: 2, level: 2 },
      { id: 3, level: 2 },
      { id: 4, level: 2 },
      { id: 5, level: 2 },
      { id: 6, level: 3 },
      { id: 7, level: 3 },
      { id: 8, level: 1 },
      { id: 9, level: 2 },
      { id: 10, level: 2 },
      { id: 11, level: 1 },
      { id: 12, level: 2 },
      { id: 13, level: 2 },
      { id: 14, level: 3 },
      { id: 15, level: 3 },
      { id: 16, level: 3 },
      { id: 17, level: 2 },
      { id: 18, level: 3 },
      { id: 19, level: 4 },
      { id: 20, level: 5 },
      { id: 21, level: 3 },
      { id: 22, level: 4 },
      { id: 23, level: 5 },
    ] as Array<DocumentSection>

    const hierarchicalSections = [
      {
        section: sections[0],
        subHierarchicalSections: [],
      },
      {
        section: sections[1],
        subHierarchicalSections: [
          {
            section: sections[2],
            subHierarchicalSections: [],
          },
          {
            section: sections[3],
            subHierarchicalSections: [],
          },
          {
            section: sections[4],
            subHierarchicalSections: [],
          },
          {
            section: sections[5],
            subHierarchicalSections: [
              {
                section: sections[6],
                subHierarchicalSections: [],
              },
              {
                section: sections[7],
                subHierarchicalSections: [],
              },
            ],
          },
        ],
      },
      {
        section: sections[8],
        subHierarchicalSections: [
          {
            section: sections[9],
            subHierarchicalSections: [],
          },
          {
            section: sections[10],
            subHierarchicalSections: [],
          },
        ],
      },
      {
        section: sections[11],
        subHierarchicalSections: [
          {
            section: sections[12],
            subHierarchicalSections: [],
          },
          {
            section: sections[13],
            subHierarchicalSections: [
              {
                section: sections[14],
                subHierarchicalSections: [],
              },
              {
                section: sections[15],
                renderedContent: undefined,
                subHierarchicalSections: [],
              },
              {
                section: sections[16],
                subHierarchicalSections: [],
              },
            ],
          },
          {
            section: sections[17],
            subHierarchicalSections: [
              {
                section: sections[18],
                subHierarchicalSections: [
                  {
                    section: sections[19],
                    subHierarchicalSections: [
                      {
                        section: sections[20],
                        subHierarchicalSections: [],
                      },
                    ],
                  },
                ],
              },
              {
                section: sections[21],
                subHierarchicalSections: [
                  {
                    section: sections[22],
                    subHierarchicalSections: [
                      {
                        section: sections[23],
                        subHierarchicalSections: [],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ]

    const expectedResult = new Map([
      [sections[0].id, undefined],
      [sections[1].id, undefined],
      [sections[2].id, sections[1]],
      [sections[3].id, sections[1]],
      [sections[4].id, sections[1]],
      [sections[5].id, sections[1]],
      [sections[6].id, sections[5]],
      [sections[7].id, sections[5]],
      [sections[8].id, undefined],
      [sections[9].id, sections[8]],
      [sections[10].id, sections[8]],
      [sections[11].id, undefined],
      [sections[12].id, sections[11]],
      [sections[13].id, sections[11]],
      [sections[14].id, sections[13]],
      [sections[15].id, sections[13]],
      [sections[16].id, sections[13]],
      [sections[17].id, sections[11]],
      [sections[18].id, sections[17]],
      [sections[19].id, sections[18]],
      [sections[20].id, sections[19]],
      [sections[21].id, sections[17]],
      [sections[22].id, sections[21]],
      [sections[23].id, sections[22]],
    ])

    const result = mapSectionIdToParentSection(hierarchicalSections)

    expect(result).toEqual(expectedResult)
  })
})
