import { describe, expect, it } from 'vitest'
import { mapSectionIdToNumbering } from '@js/helper/document/mapSectionIdToNumbering'
import type { DocumentSection } from '@js/model/document'

describe('mapSectionToTreePosition', () => {
  it('should correctly map sections to tree positions', () => {
    const sections = [
      { id: 0, level: 1, include: true }, // 0
      { id: 1, level: 1, include: true },
      { id: 3, level: 2, include: true },
      { id: 4, level: 2, include: true },
      { id: 5, level: 2, include: true },
      { id: 6, level: 2, include: true },
      { id: 7, level: 3, include: true },
      { id: 8, level: 3, include: true },
      { id: 9, level: 1, include: true },
      { id: 10, level: 2, include: true },
      { id: 11, level: 2, include: true },
      { id: 12, level: 2, include: true },
      { id: 13, level: 2, include: true },
      { id: 14, level: 2, include: true },
      { id: 15, level: 2, include: true },
      { id: 16, level: 2, include: true },
      { id: 17, level: 2, include: true },
      { id: 18, level: 2, include: true },
      { id: 19, level: 2, include: true },
      { id: 20, level: 2, include: true },
      { id: 21, level: 1, include: true },
      { id: 22, level: 2, include: true },
      { id: 23, level: 2, include: true },
      { id: 24, level: 3, include: true },
      { id: 25, level: 3, include: true },
      { id: 26, level: 3, include: true },
      { id: 27, level: 3, include: true },
      { id: 28, level: 4, include: false },
    ] as Array<DocumentSection>

    const expectedMap = new Map([
      [sections[0].id, '1'],
      [sections[1].id, '2'],
      [sections[2].id, '2.1'],
      [sections[3].id, '2.2'],
      [sections[4].id, '2.3'],
      [sections[5].id, '2.4'],
      [sections[6].id, '2.4.1'],
      [sections[7].id, '2.4.2'],
      [sections[8].id, '3'],
      [sections[9].id, '3.1'],
      [sections[10].id, '3.2'],
      [sections[11].id, '3.3'],
      [sections[12].id, '3.4'],
      [sections[13].id, '3.5'],
      [sections[14].id, '3.6'],
      [sections[15].id, '3.7'],
      [sections[16].id, '3.8'],
      [sections[17].id, '3.9'],
      [sections[18].id, '3.10'],
      [sections[19].id, '3.11'],
      [sections[20].id, '4'],
      [sections[21].id, '4.1'],
      [sections[22].id, '4.2'],
      [sections[23].id, '4.2.1'],
      [sections[24].id, '4.2.2'],
      [sections[25].id, '4.2.3'],
      [sections[26].id, '4.2.4'],
      [sections[27].id, '-.-.-.-'],
    ])

    const result = mapSectionIdToNumbering(sections)

    expect(result).toEqual(expectedMap)
  })
})
