import { describe, expect, it } from 'vitest'
import { mapSectionIdToSubsections } from '@js/helper/document/mapSectionIdToSubsections'
import type { DocumentSection } from '@js/model/document'

describe('mapSectionIdToSubSections', () => {
  it('should correctly map sections to their sub-sections with complex structure', () => {
    const sections = [
      { id: 0, level: 1, include: true }, // 0
      { id: 1, level: 1, include: true },
      { id: 2, level: 2, include: false },
      { id: 3, level: 2, include: true },
      { id: 4, level: 2, include: true },
      { id: 5, level: 2, include: true },
      { id: 6, level: 3, include: false },
      { id: 7, level: 3, include: true },
      { id: 8, level: 1, include: true },
      { id: 9, level: 2, include: true },
      { id: 10, level: 2, include: true },
      { id: 11, level: 1, include: true },
      { id: 12, level: 2, include: false },
      { id: 13, level: 2, include: true },
      { id: 14, level: 3, include: true },
      { id: 15, level: 3, include: true },
      { id: 16, level: 3, include: true },
    ] as Array<DocumentSection>

    const expectedResult = new Map([
      [sections[0].id, []],
      [
        sections[1].id,
        [sections[2], sections[3], sections[4], sections[5], sections[6], sections[7]],
      ],
      [sections[2].id, []],
      [sections[3].id, []],
      [sections[4].id, []],
      [sections[5].id, [sections[6], sections[7]]],
      [sections[6].id, []],
      [sections[7].id, []],
      [sections[8].id, [sections[9], sections[10]]],
      [sections[9].id, []],
      [sections[10].id, []],
      [sections[11].id, [sections[12], sections[13], sections[14], sections[15], sections[16]]],
      [sections[12].id, []],
      [sections[13].id, [sections[14], sections[15], sections[16]]],
      [sections[14].id, []],
      [sections[15].id, []],
      [sections[16].id, []],
    ])

    const result = mapSectionIdToSubsections(sections)

    expect(result).toEqual(expectedResult)
  })

  it('should correctly map sections to their deeply nested sub-sections', () => {
    const sections = [
      { id: 0, level: 1, include: true },
      { id: 1, level: 2, include: false },
      { id: 2, level: 2, include: true },
      { id: 3, level: 3, include: true },
      { id: 4, level: 3, include: true },
      { id: 5, level: 3, include: true },
      { id: 6, level: 2, include: true },
      { id: 7, level: 3, include: true },
      { id: 8, level: 4, include: true },
      { id: 9, level: 5, include: true },
      { id: 10, level: 3, include: true },
      { id: 11, level: 4, include: true },
      { id: 12, level: 5, include: true },
    ] as Array<DocumentSection>

    const expectedResult = new Map([
      [
        sections[0].id,
        [
          sections[1],
          sections[2],
          sections[3],
          sections[4],
          sections[5],
          sections[6],
          sections[7],
          sections[8],
          sections[9],
          sections[10],
          sections[11],
          sections[12],
        ],
      ],
      [sections[1].id, []],
      [sections[2].id, [sections[3], sections[4], sections[5]]],
      [sections[3].id, []],
      [sections[4].id, []],
      [sections[5].id, []],
      [
        sections[6].id,
        [sections[7], sections[8], sections[9], sections[10], sections[11], sections[12]],
      ],
      [sections[7].id, [sections[8], sections[9]]],
      [sections[8].id, [sections[9]]],
      [sections[9].id, []],
      [sections[10].id, [sections[11], sections[12]]],
      [sections[11].id, [sections[12]]],
      [sections[12].id, []],
    ])
    const result = mapSectionIdToSubsections(sections)

    expect(result).toEqual(expectedResult)
  })
})
