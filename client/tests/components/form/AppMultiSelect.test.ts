import { userEvent } from '@testing-library/user-event'
import { fireEvent, render, waitFor, within } from '@testing-library/vue'
import { expect } from 'vitest'
import { chooseOption } from '@tests/utils'
import flushPromises from 'flush-promises'
import isObject from 'lodash/isObject'
import AppMultiSelect from '@js/components/form/AppMultiSelect.vue'

const options = [
  {
    id: '1',
    name: 'Option 1',
    disabled: false,
  },
  {
    id: '2',
    name: 'Option 2',
    disabled: false,
  },
  {
    id: '3',
    name: 'Option 3',
    disabled: true,
  },
]

describe('AppMultiSelect', () => {
  beforeEach(() => {
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
  })

  it('uses a string as "id"', async () => {
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['borrower'],
        options: [
          { id: 'borrower', name: '<PERSON><PERSON><PERSON>', disabled: false, warning: false, help: false },
          { id: 'creditor', name: 'Creditor', disabled: false, warning: false, help: false },
        ],
      },
    })

    // When/Then
    expect(ui.getByLabelText('Borrower')).toBeInTheDocument()
  })

  it('uses a different value key', async () => {
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['borrower'],
        options: [
          {
            slug: 'borrower',
            name: 'Borrower',
          },
          {
            slug: 'lender',
            name: 'Lender',
          },
        ],
        valueKey: 'slug',
      },
    })

    // When/Then
    expect(ui.getByLabelText('Borrower')).toBeInTheDocument()
  })

  it('uses a key for sort', async () => {
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: [],
        sortKey: 'name',
        options: [
          {
            id: 'Option 1',
            name: 'Option 1',
          },
          {
            id: 'Option 2',
            name: 'Option 2',
          },
          {
            id: 'Option 10',
            name: 'Option 10',
          },
        ],
      },
    })

    // When/Then
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('u2.select_option')

    // When
    await user.click(combobox)
    await flushPromises()

    // Then
    const options = ui.getAllByRole('option')
    expect(options[0]).toHaveTextContent('Option 1')
    expect(options[1]).toHaveTextContent('Option 2')
    expect(options[2]).toHaveTextContent('Option 10')
  })

  it('uses a custom callback for the label if defined', async () => {
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1'],
        options,
        labelKey: (option) =>
          isObject(option) && 'id' in option ? (option.id as string) : undefined,
      },
    })

    // When/Then
    await waitFor(() => expect(ui.getByLabelText('1')).toBeInTheDocument())
  })

  it('uses a custom callback for the icon if defined', async () => {
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1'],
        options,
        iconKey: () => 'unit',
      },
    })

    // When/Then
    expect(ui.getByLabelText('unit')).toBeInTheDocument()
  })

  it('uses a custom callback for the disabled if defined', async () => {
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1'],
        options,
        disabledKey: (option) =>
          isObject(option) && 'disabled' in option ? (!option.disabled as boolean) : false,
      },
    })

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    await waitFor(() => {
      expect(ui.getByRole('option', { name: /Option 3/ })).not.toHaveAttribute('data-disabled', '')
    })
    expect(ui.getByRole('option', { name: /Option 2/ })).toHaveAttribute('data-disabled', '')
  })

  it('shows a search icon on search or empty option', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        name: 'combobox',
        modelValue: undefined,
        options,
      },
    })

    expect(ui.getByLabelText('search')).toBeInTheDocument()

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(ui.getByLabelText('search')).toBeInTheDocument()
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.value).toBe('')

    // When
    await user.type(combobox, 'O')
    expect(combobox.value).toBe('O')

    // Then
    expect(ui.getByLabelText('search')).toBeInTheDocument()
    expect(
      ui.getAllByRole('option').filter((option) => option.textContent?.trim() === 'Option 1').length
    ).toBe(1)

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user, true)

    // Then
    expect(ui.queryByLabelText('search')).not.toBeInTheDocument()
  })

  it('does not show placeholder after selecting something', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: [],
        options,
        placeholder: 'Select an option',
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('Select an option')

    // When
    await user.click(combobox)

    // Then
    expect(combobox.placeholder).toBe('Select an option')

    // When
    await user.type(combobox, 'O')

    // Then
    expect(combobox.placeholder).toBe('')

    // When
    await user.click(ui.getByRole('option', { name: 'Option 1' }))

    // When
    await user.keyboard('{tab}')

    // Then
    expect(ui.getByRole<HTMLInputElement>('combobox')?.placeholder).toBe('')
  })

  it('placeholder if something is selected', async () => {
    // Given
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1'],
        options,
        placeholder: 'Select an option',
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('')
  })

  it('highlights the next available selected value after deleting the current highlighted selected value', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    const selectedValue2 = ui.getByLabelText('Option 2')
    expect(selectedValue2).toBeInTheDocument()

    const selectedValue3 = ui.getByLabelText('Option 3')
    expect(selectedValue3).toBeInTheDocument()

    // When
    await user.click(selectedValue1)

    // Then
    expect(selectedValue1.classList.contains('brightness-90')).toBe(true)
    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(ui.queryByLabelText('Option 1')).not.toBeInTheDocument()
    expect(selectedValue2.classList.contains('brightness-90')).toBe(true)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)
  })

  it('allows to delete a selected value via pressing the x in the option', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    const selectedValue2 = ui.getByLabelText('Option 2')
    expect(selectedValue2).toBeInTheDocument()

    const selectedValue3 = ui.getByLabelText('Option 3')
    expect(selectedValue3).toBeInTheDocument()

    expect(selectedValue1.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)

    // When
    await user.click(within(selectedValue1).getByLabelText('u2.clear'))

    // Then
    expect(selectedValue1).not.toBeInTheDocument()
    expect(selectedValue2).toBeInTheDocument()
    expect(selectedValue3).toBeInTheDocument()

    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)
  })

  it('allows to delete the current highlighted selected value via pressing the x in the option highlights the next option', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    const selectedValue2 = ui.getByLabelText('Option 2')
    expect(selectedValue2).toBeInTheDocument()

    const selectedValue3 = ui.getByLabelText('Option 3')
    expect(selectedValue3).toBeInTheDocument()

    // When
    await user.click(selectedValue1)

    // Then
    expect(selectedValue1.classList.contains('brightness-90')).toBe(true)
    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)

    // When
    await user.click(within(selectedValue1).getByLabelText('u2.clear'))

    // Then
    expect(selectedValue1).not.toBeInTheDocument()
    expect(selectedValue2).toBeInTheDocument()
    expect(selectedValue3).toBeInTheDocument()

    // Then
    expect(ui.getByLabelText('Option 2').classList.contains('brightness-90')).toBe(true)

    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)
  })

  it('allows to delete the current highlighted selected value via backspace', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    const selectedValue2 = ui.getByLabelText('Option 2')
    expect(selectedValue2).toBeInTheDocument()

    const selectedValue3 = ui.getByLabelText('Option 3')
    expect(selectedValue3).toBeInTheDocument()

    // When
    await user.click(selectedValue1)

    // Then
    expect(selectedValue1.classList.contains('brightness-90')).toBe(true)
    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(ui.queryByLabelText('Option 1')).not.toBeInTheDocument()
    expect(selectedValue2.classList.contains('brightness-90')).toBe(true)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)
  })

  it('removes highlighting and enable input when clicked', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
        placeholder: 'Select an option',
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    const selectedValue2 = ui.getByLabelText('Option 2')
    expect(selectedValue2).toBeInTheDocument()

    const selectedValue3 = ui.getByLabelText('Option 3')
    expect(selectedValue3).toBeInTheDocument()

    // When
    await user.click(selectedValue1)

    // Then
    expect(selectedValue1.classList.contains('brightness-90')).toBe(true)
    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox?.placeholder).toBe('Select an option')
    expect(combobox?.readOnly).toBe(true)

    // When
    await fireEvent.click(combobox)

    // Then
    expect(selectedValue1.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue2.classList.contains('brightness-90')).toBe(false)
    expect(selectedValue3.classList.contains('brightness-90')).toBe(false)
    expect(combobox?.placeholder).toBe('Select an option')
    expect(combobox?.readOnly).toBe(false)
  })

  it('shows placeholder when a selected option is highlighting', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
        placeholder: 'Select an option',
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    // When
    await user.click(selectedValue1)

    // Then
    expect(selectedValue1.classList.contains('brightness-90')).toBe(true)
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox?.placeholder).toBe('Select an option')
    expect(combobox?.readOnly).toBe(true)
  })

  it('does not allow to type if a option highlighted', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1', '2', '3'],
        options,
      },
    })

    const selectedValue1 = ui.getByLabelText('Option 1')
    expect(selectedValue1).toBeInTheDocument()

    const selectedValue2 = ui.getByLabelText('Option 2')
    expect(selectedValue2).toBeInTheDocument()

    const selectedValue3 = ui.getByLabelText('Option 3')
    expect(selectedValue3).toBeInTheDocument()

    // When
    await user.click(selectedValue1)

    // Then
    expect(ui.getByRole<HTMLInputElement>('combobox').readOnly).toBe(true)
  })

  it('shows the given selected value', async () => {
    // Given
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['1'],
        options,
      },
    })

    // Then
    expect(ui.getByLabelText('Option 1')).toBeInTheDocument()
  })

  it('selects a value from the options', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: undefined,
        options,
        name: 'combobox',
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When /Then
    await chooseOption(ui, 'combobox', 'Option 1', user, true)
  })

  it('selects multiple values from the options', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: undefined,
        name: 'combobox',
        options: [
          { id: '1', name: 'Option 1' },
          { id: '2', name: 'Option 2' },
          { id: '3', name: 'Option 3' },
        ],
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user, true)
    await chooseOption(ui, 'combobox', 'Option 2', user, true)
    await chooseOption(ui, 'combobox', 'Option 3', user, true)

    // Then
    expect(ui.getByLabelText('Option 1')).toBeInTheDocument()
    expect(ui.getByLabelText('Option 2')).toBeInTheDocument()
    expect(ui.getByLabelText('Option 3')).toBeInTheDocument()
    expect(ui.getAllByRole('option').length).toBe(3)
  })

  it('selects multiple values from the options with hide selected enabled', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: undefined,
        name: 'combobox',
        hideSelected: true,
        options: [
          { id: '1', name: 'Option 1' },
          { id: '2', name: 'Option 2' },
          { id: '3', name: 'Option 3' },
        ],
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When/Then
    await chooseOption(ui, 'combobox', 'Option 1', user, true)
    await chooseOption(ui, 'combobox', 'Option 2', user, true)
    await chooseOption(ui, 'combobox', 'Option 3', user, true)

    // Then
    expect(ui.getByText('u2_table.no_results_that_match_search')).toBeInTheDocument()
  })

  it('shows a message that there are more records if the maximum number of records to display is exceeded', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: [],
        maximumNumberOfOptions: 5,
        options: [
          ...Array.from(Array(10).keys()).map((index) => ({
            id: `generated_${index}`,
            name: `generated_${index}`,
          })),
        ],
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('u2.select_option')

    await user.click(combobox)

    // Then
    expect(ui.getAllByRole('option').length).toBe(5)
    expect(ui.getByText('u2.search.refine_search')).toBeInTheDocument()

    // When
    await user.type(combobox, 'generated_9')

    // Then
    expect(combobox.value).toBe('generated_9')
    expect(ui.getAllByRole('option').length).toBe(1)
    expect(ui.queryByText('u2.search.refine_search')).not.toBeInTheDocument()
  })

  it('opens list when input is clicked', async () => {
    // Given
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await fireEvent.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
  })

  it('does not empty typed values when the input is clicked', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
      },
    })

    // When
    await fireEvent.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    await user.keyboard('{backspace}') // Empty the selected value

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.type(combobox, 'Oops I did it again')

    // Then
    expect(combobox.value).toBe('Oops I did it again')

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(combobox.value).toBe('Oops I did it again')
  })

  it('does not opens list when input is tabbed', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: [],
        options,
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox).not.toHaveFocus()

    // When
    await user.keyboard('{tab}')

    // Then
    expect(combobox).toHaveFocus()
    expect(
      combobox.parentElement?.parentElement?.classList
        .toString()
        .includes('border-skin-focus ring-skin-base hover:border-skin-focus ring-1')
    ).toBe(true)
    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('highlights the first matching option on search', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(combobox.value).toBe('')

    const option2 = ui.getByRole('option', { name: 'Option 2' })
    expect(option2).toBeActiveDescendant()
    expect(option2.attributes.getNamedItem('data-highlighted')).toBeDefined()

    // When
    await user.type(combobox, 'O')

    // Then
    const option1 = ui.getByRole('option', { name: /Option 1/ })
    expect(option1.attributes.getNamedItem('data-highlighted')).toBeDefined()
    expect(option2.attributes.getNamedItem('data-highlighted')).toBeNull()
    expect(option1).toBeActiveDescendant()
    expect(
      ui.getByRole('option', { name: /Option 2/ }).attributes.getNamedItem('data-highlighted')
    ).toBeNull()
  })

  it('closes the list on esc', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
      },
    })

    // When
    await fireEvent.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    await user.keyboard('{Escape}')

    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })
  })

  it('closes the list and restores a removed value on esc', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
        name: 'combobox',
      },
    })

    // When
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    await user.keyboard('{backspace}')
    // Ensure we can restore also after spamming backspace
    await user.keyboard('{backspace}')
    await user.keyboard('{backspace}')

    // Then
    expect(combobox.value).toBe('')

    // When
    await user.keyboard('{Escape}')

    // Then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(await ui.findByLabelText('Option 2')).toBeInTheDocument()

    // Let's try with selecting another value and then restoring
    await chooseOption(ui, 'combobox', 'Option 1', user, true)

    // When
    await user.keyboard('{Escape}')

    // then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(await ui.findByLabelText('Option 2')).toBeInTheDocument()
  })

  it('empties the selected value on pressing cross when', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
      },
    })

    const option2 = await ui.findByLabelText('Option 2')
    expect(option2).toBeInTheDocument()

    // When
    await user.click(ui.getByLabelText('u2.clear_all'))

    // Then
    expect(ui.queryByLabelText('Option 1')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('Option 2')).not.toBeInTheDocument()
    expect(ui.queryByLabelText('Option 3')).not.toBeInTheDocument()
  })

  it('sets the current hovered options as active', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
        name: 'combobox',
      },
    })

    expect(await ui.findByLabelText('Option 2')).toBeInTheDocument()

    // When
    await fireEvent.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(await ui.findByRole('option', { name: 'Option 1' })).toBeInTheDocument()

    // When
    const option1 = ui.getByRole('option', { name: 'Option 1' })
    await user.hover(option1)

    // Then
    expect(option1).toBeActiveDescendant()
    expect(option1.attributes.getNamedItem('data-highlighted')).toBeDefined()
  })

  it('does not allow disabled values to be selected', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: ['2'],
        options,
      },
    })

    expect(await ui.findByLabelText('Option 2')).toBeInTheDocument()

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    const option3 = ui.getByRole('option', { name: 'Option 3' })
    expect(option3).toHaveAttribute('aria-disabled', 'true')
    await user.click(option3)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(await ui.findByLabelText('Option 2')).toBeInTheDocument()
  })

  it('allows to create new options', async () => {
    const user = userEvent.setup()
    const ui = render(AppMultiSelect, {
      props: {
        modelValue: [],
        allowCreate: true,
        name: 'combobox',
        options,
      },
    })

    expect(ui.getByLabelText('search')).toBeInTheDocument()
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.value).toBe('')

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(ui.getAllByRole('option').length).toBe(3)
    expect(ui.queryByRole('option', { name: /Hello/ })).not.toBeInTheDocument()

    // When
    await user.type(combobox, 'Option 1')

    // Then
    expect(
      ui.getAllByRole('option').filter((option) => option.textContent?.trim() === 'Option 1').length
    ).toBe(1)

    // When
    await user.type(combobox, '21')

    // Then
    expect(ui.getByRole('option', { name: /Option 121/ })).toBeInTheDocument()

    // When
    await chooseOption(ui, 'combobox', 'Option 121', user, true)

    // Then
    expect(ui.getAllByRole('option').length).toBe(4)
  })
})
