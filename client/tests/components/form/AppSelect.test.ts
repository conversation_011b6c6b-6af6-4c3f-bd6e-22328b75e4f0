import { render, waitFor } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import { defineComponent, nextTick, ref } from 'vue'
import { chooseOption } from '@tests/utils'
import isObject from 'lodash/isObject'
import { expect } from 'vitest'
import flushPromises from 'flush-promises'
import AppSelect from '@js/components/form/AppSelect.vue'
import ButtonBase from '@js/components/buttons/ButtonBase.vue'
import AppDialog from '@js/components/AppDialog.vue'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import type { SelectOption } from '@js/types'

const options = [
  {
    id: '123',
    name: 'Option 1',
    disabled: false,
    icon: 'organisational-group',
  },
  {
    id: '456',
    name: 'Option 2',
    disabled: false,
    icon: 'legal-unit',
  },
  {
    id: '789',
    name: 'Option 3',
    disabled: true,
    icon: 'permanent-establishment',
  },
  {
    id: '111',
    name: 'xxx',
    disabled: true,
    icon: 'unit',
  },
]

describe('AppSelect', () => {
  beforeEach(() => {
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
  })

  it('uses a string as "id"', async () => {
    const ui = render(AppSelect, {
      props: {
        modelValue: 'borrower',
        options: [
          { id: 'borrower', name: 'Borrower', disabled: false, warning: false, help: false },
          { id: 'creditor', name: 'Creditor', disabled: false, warning: false, help: false },
        ],
      },
    })

    // When/Then
    await waitFor(() => {
      expect(ui.getByRole<HTMLInputElement>('combobox').value).toBe('Borrower')
    })
  })

  it('notifies the user that the select value is not in the available options', async () => {
    const user = userEvent.setup()

    const WrapperComponent = defineComponent({
      components: { AppSelect },
      setup() {
        const options = ref<Array<SelectOption>>([
          { id: 'borrower', name: 'Borrower', disabled: false },
          { id: 'creditor', name: 'Creditor', disabled: false },
          { id: 'horst', name: 'Horst', disabled: false },
        ])

        const modelValue = 'Moabit'

        return { options, modelValue }
      },
      template: `<AppSelect :options="options" v-model="modelValue"  />`,
    })

    const ui = render(WrapperComponent)

    // When/Then
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.value).toBe('u2.no_matching_option_found')

    // When
    await user.click(combobox)
    await flushPromises()

    // Then
    const options = ui.getAllByRole('option')
    expect(options[0]).toHaveTextContent('Borrower')
    expect(options[1]).toHaveTextContent('Creditor')
    expect(options[2]).toHaveTextContent('Horst')
  })

  it('uses a different value key', async () => {
    const ui = render(AppSelect, {
      props: {
        modelValue: 'borrower',
        options: [
          {
            slug: 'borrower',
            name: 'Borrower',
          },
          {
            slug: 'lender',
            name: 'Lender',
          },
        ],
        valueKey: 'slug',
      },
    })

    // When/Then
    await waitFor(() => {
      expect(ui.getByRole<HTMLInputElement>('combobox').value).toBe('Borrower')
    })
  })

  it('uses a key for sort', async () => {
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: null,
        sortKey: 'name',
        options: [
          {
            id: 'Option 1',
            name: 'Option 1',
          },
          {
            id: 'Option 2',
            name: 'Option 2',
          },
          {
            id: 'Option 10',
            name: 'Option 10',
          },
        ],
      },
    })

    // When/Then
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('u2.select_option')

    // When
    await user.click(combobox)
    await flushPromises()

    // Then
    const options = ui.getAllByRole('option')
    expect(options[0]).toHaveTextContent('Option 1')
    expect(options[1]).toHaveTextContent('Option 2')
    expect(options[2]).toHaveTextContent('Option 10')
  })

  it('uses a custom callback for the label if defined', async () => {
    const ui = render(AppSelect, {
      props: {
        modelValue: '123',
        options,
        labelKey: (option) =>
          isObject(option) && 'id' in option ? (option.id as string) : undefined,
      },
    })

    // When/Then
    await waitFor(() => {
      expect(ui.getByRole<HTMLInputElement>('combobox').value).toBe('123')
    })
  })

  it('uses a custom callback for the icon if defined', async () => {
    const ui = render(AppSelect, {
      props: {
        modelValue: '123',
        options,
        iconKey: () => 'unit',
      },
    })

    // When/Then
    expect(ui.getByLabelText('unit')).toBeInTheDocument()
  })

  it('uses a custom callback for the disabled if defined', async () => {
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '123',
        options,
        disabledKey: (option) =>
          isObject(option) && 'disabled' in option ? (!option.disabled as boolean) : false,
      },
    })

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    await waitFor(() => {
      expect(ui.getByRole('option', { name: /Option 3/ })).not.toHaveAttribute('data-disabled', '')
    })
    expect(ui.getByRole('option', { name: /Option 2/ })).toHaveAttribute('data-disabled', '')
  })

  it('opens the list and shows all options when typing into the combobox with `enableFilter` enabled', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '123',
        enableFilter: false,
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.keyboard('{tab}')

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await user.keyboard('{backspace}') // Empty the selected value

    await user.type(combobox, 'x')
    expect(combobox.value).toBe('x')

    // Then
    expect(ui.getByRole('option', { name: /Option 1/ })).toBeInTheDocument()
    expect(ui.getByRole('option', { name: /Option 2/ })).toBeInTheDocument()
    expect(ui.getByRole('option', { name: /Option 3/ })).toBeInTheDocument()
    expect(ui.getByRole('option', { name: /xxx/ })).toBeInTheDocument()
  })

  it('shows a message that there are more records if the maximum number of records to display is exceeded', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        enableFilter: true,
        maximumNumberOfOptions: 5,
        options: [
          ...Array.from(Array(10).keys()).map((index) => ({
            id: `generated_${index}`,
            name: `generated_${index}`,
          })),
        ],
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('u2.select_option')

    // WHen
    await user.click(combobox)

    // Then
    expect(ui.getAllByRole('option').length).toBe(5)
    expect(ui.getByText('u2.search.refine_search')).toBeInTheDocument()

    // When
    await user.type(combobox, 'generated_9')

    // Then
    await waitFor(() => {
      expect(combobox.value).toBe('generated_9')
    })
    expect(ui.getAllByRole('option').length).toBe(1)
    expect(ui.queryByText('u2.search.refine_search')).not.toBeInTheDocument()
  })

  it('allows to configure the maximum number of records to display', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        enableFilter: true,
        maximumNumberOfOptions: 10,
        options: [
          ...Array.from(Array(20).keys()).map((index) => ({
            id: `generated_${index}`,
            name: `generated_${index}`,
          })),
        ],
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('u2.select_option')

    // When
    await user.click(combobox)

    // Then

    // Count number of options shown
    expect(ui.getAllByRole('option').length).toBe(10)
    expect(ui.getByText('u2.search.refine_search')).toBeInTheDocument()
  })

  it('opens the list and shows filtered options when typing into the combobox', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '123',
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.keyboard('{tab}')

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await user.keyboard('{backspace}') // Empty the selected value

    await user.type(combobox, 'x')
    expect(combobox.value).toBe('x')

    // Then
    expect(ui.queryByRole('option', { name: /Option 1/ })).not.toBeInTheDocument()
    expect(ui.queryByRole('option', { name: /Option 2/ })).not.toBeInTheDocument()
    expect(ui.queryByRole('option', { name: /Option 3/ })).not.toBeInTheDocument()
    expect(ui.getByRole('option', { name: /xxx/ })).toBeInTheDocument()
  })

  it('shows the given selected value', async () => {
    // Given
    const ui = render(AppSelect, {
      props: {
        modelValue: '123',
        options,
      },
    })

    // When
    const combobox = ui.getByRole<HTMLInputElement>('combobox')

    // Then
    await waitFor(() => {
      expect(combobox.value).toBe('Option 1')
    })
  })

  it('shows no result found message', async () => {
    const user = userEvent.setup()

    // Given
    const ui = render(
      defineComponent({
        components: { AppSelect },
        setup() {
          const loading = ref(true)
          return {
            loading,
            options,
            triggerLoading: () => {
              nextTick(() => {
                loading.value = false
              })
            },
          }
        },
        template: `
        <AppSelect :loading="loading" @update:search-query="triggerLoading" />
      `,
      })
    )

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('u2.select_option')

    // When
    await user.click(combobox)

    // Then
    expect(combobox.placeholder).toBe('u2.select_option')

    // When
    await user.type(combobox, 'Z')

    // Then no message is shown while loading is still true
    expect(ui.queryByText('u2_table.no_results_that_match_search')).not.toBeInTheDocument()

    // When/Then - Waiting for loading to resolve. Then check for the message
    expect(await ui.findByText('u2_table.no_results_that_match_search')).toBeInTheDocument()
  })

  it('shows a placeholder if nothing is selected', async () => {
    // When
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        options,
        placeholder: 'Select an option',
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('Select an option')
  })

  it('does not show placeholder after selecting something', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        name: 'combobox',
        options,
        placeholder: 'Select an option',
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('Select an option')

    // When
    await user.click(combobox)

    // Then
    expect(combobox.placeholder).toBe('Select an option')

    // When
    await user.type(combobox, 'O')

    // Then
    expect(combobox.placeholder).toBe('')

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user)
    await user.keyboard('{tab}')

    // Then
    expect(ui.getByRole<HTMLInputElement>('combobox')?.placeholder).toBe('')
  })

  it('does show placeholder while search is empty', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        options,
        placeholder: 'Select an option',
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.placeholder).toBe('Select an option')

    // When
    await user.click(combobox)

    // Then
    expect(combobox.placeholder).toBe('Select an option')

    // When
    await user.type(combobox, 'O')

    // Then
    expect(combobox.placeholder).toBe('')

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(combobox.placeholder).toBe('Select an option')
  })

  it('selects a value from the options', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        options,
        name: 'combobox',
      },
    })

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user)

    // Then
    expect(ui.getByRole<HTMLInputElement>('combobox').value).toBe('Option 1')
  })

  it('removes selected value when model value is emptied', async () => {
    const user = userEvent.setup()

    const WrapperComponent = defineComponent({
      components: { AppSelect, ButtonBase },
      setup() {
        const modelValue = ref<string | undefined>('horst')

        return {
          options: [
            { id: 'borrower', name: 'Borrower', disabled: false },
            { id: 'creditor', name: 'Creditor', disabled: false },
            { id: 'horst', name: 'Horst', disabled: false },
          ],
          modelValue,
          emptyModelValue() {
            modelValue.value = undefined
          },
        }
      },
      template: `
        <AppSelect :options="options" v-model="modelValue"  />
        <ButtonBase @click="emptyModelValue">Empty model value</ButtonBase>
      `,
    })

    const ui = render(WrapperComponent)

    // When/Then
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.value).toBe('Horst')

    // When
    await user.click(ui.getByRole('button', { name: /Empty model value/ }))

    // Then
    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    await waitFor(() => {
      expect(combobox.value).toBe('')
    })
  })

  it('does not empty typed values when the input is clicked', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        options,
      },
    })

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    await user.keyboard('{backspace}') // Empty the selected value

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.type(combobox, 'Oops I did it again')

    // Then
    expect(combobox.value).toBe('Oops I did it again')

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(combobox.value).toBe('Oops I did it again')
  })

  it('hides the list box after selecting a option', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        name: 'combobox',
        options,
      },
    })

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user)

    // Then
    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('shows all opens after selecting a option and reopening the list box again', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        name: 'combobox',
        options,
      },
    })

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user)

    // then
    await user.click(ui.getByLabelText('arrow-down'))
    expect(ui.getAllByRole('option')).toHaveLength(4)
  })

  it('opens list when input is clicked', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByRole<HTMLInputElement>('combobox'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    const option2 = ui.getByRole('option', { name: /Option 2/ })
    expect(option2).toBeActiveDescendant()
    expect(option2.attributes.getNamedItem('data-highlighted')).toBeDefined()
  })

  it('does not opens list when input is tabbed', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
      },
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox).not.toHaveFocus()

    // When
    await user.keyboard('{tab}')

    // Then
    expect(combobox).toHaveFocus()
    expect(
      combobox.parentElement?.parentElement?.classList
        .toString()
        .includes('border-skin-focus ring-skin-base hover:border-skin-focus ring-1')
    ).toBe(true)
    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
  })

  it('highlights the first matching option on search', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await waitFor(() => {
      expect(combobox.value).toBe('Option 2')
    })

    expect(ui.queryByRole('listbox')).not.toBeInTheDocument()

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(combobox.value).toBe('Option 2')

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(combobox.value).toBe('')

    // When
    await user.type(combobox, 'O')

    // Then
    const option1 = ui.getByRole('option', { name: /Option 1/ })
    expect(option1).toBeActiveDescendant()
    expect(option1.attributes.getNamedItem('data-highlighted')).toBeDefined()
    expect(
      ui.getByRole('option', { name: /Option 2/ }).attributes.getNamedItem('data-highlighted')
    ).toBeNull()
  })

  it('shows a search icon on search or empty option', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        name: 'combobox',
        options,
      },
    })

    expect(ui.getByLabelText('search')).toBeInTheDocument()

    // When
    await user.click(ui.getByLabelText('arrow-down'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(ui.getByLabelText('search')).toBeInTheDocument()
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.value).toBe('')

    // When
    await user.type(combobox, 'O')
    expect(combobox.value).toBe('O')

    // Then
    expect(ui.getByLabelText('search')).toBeInTheDocument()

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user)

    // Then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })
    expect(ui.queryByLabelText('search')).not.toBeInTheDocument()
    expect(ui.getByRole<HTMLInputElement>('combobox').value).toBe('Option 1')
  })

  it('closes the list on esc', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
      },
    })

    // When
    await user.click(ui.getByLabelText('cross'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    await user.keyboard('{Escape}')

    // Then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })
  })

  it('highlights the selected value when tabbed into the field then the search and the caret are hidden', async () => {
    // Given
    const user = userEvent.setup()

    // Define a wrapper component to test slot props
    const WrapperComponent = defineComponent({
      components: { AppSelect },
      setup() {
        return { options, selected: '456' }
      },
      template: `
        <AppSelect
          :options="options"
          label-property="name"
          v-model="selected"
        >
          <template #selectedValue="{ option, isHighlighted }">
            {{ option.name }} is {{ isHighlighted ? 'highlighted' : 'not highlighted' }}
          </template>
        </AppSelect>
      `,
    })

    const ui = render(WrapperComponent)

    expect(ui.getByText(/Option 2 is not highlighted/)).toBeInTheDocument()

    // When
    await user.keyboard('{tab}')

    // Then
    expect(ui.getByText(/Option 2 is highlighted/)).toBeInTheDocument()
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.classList.contains('caret-transparent')).toBe(true)
  })

  it('when a selected value slot is clicked then the search and the caret are hidden and the list is open', async () => {
    // Given
    const user = userEvent.setup()

    // Define a wrapper component to test slot props
    const WrapperComponent = defineComponent({
      components: { AppSelect },
      setup() {
        return {
          options,
          selected: '456',
        }
      },
      template: `
        <AppSelect
          :options="options"
          label-property="name"
          v-model="selected"
          name="combobox"
        >
          <template #selectedValue="{ option, isHighlighted }">
            {{ option.name }} is {{ isHighlighted ? 'highlighted' : 'not highlighted' }}
          </template>
        </AppSelect>
      `,
    })

    const ui = render(WrapperComponent)

    const selectedValueSlot = ui.getByText(/Option 2 is not highlighted/)
    expect(selectedValueSlot).toBeInTheDocument()
    const combobox = ui.getByRole<HTMLInputElement>('combobox')

    // When
    await user.click(selectedValueSlot)

    // Then
    expect(ui.getByText(/Option 2 is highlighted/)).toBeInTheDocument()
    expect(combobox.classList.contains('caret-transparent')).toBe(true)

    expect(ui.getByRole('listbox')).toBeInTheDocument()
  })

  it('removes the highlighted value in the selected value slot when start typing', async () => {
    // Given
    const user = userEvent.setup()

    // Define a wrapper component to test slot props
    const WrapperComponent = defineComponent({
      components: { AppSelect },
      setup() {
        return { options, selected: '456' }
      },
      template: `
        <AppSelect
          :options="options"
          label-property="name"
          v-model="selected"
        >
          <template #selectedValue="{ option, isHighlighted }">
            {{ option.name }} is {{ isHighlighted ? 'highlighted' : 'not highlighted' }}
          </template>
        </AppSelect>
      `,
    })

    const ui = render(WrapperComponent)

    expect(ui.getByText(/Option 2 is not highlighted/)).toBeInTheDocument()

    // When
    await user.keyboard('{tab}')

    // Then
    expect(ui.getByText(/Option 2 is highlighted/)).toBeInTheDocument()
  })

  it('closes the list and restores a removed value on esc', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
        name: 'combobox',
      },
    })
    const combobox = ui.getByRole<HTMLInputElement>('combobox')

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(combobox.value).toBe('Option 2')

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(combobox.value).toBe('')

    // When
    await user.keyboard('{Escape}')

    // then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(combobox.value).toBe('Option 2')

    // Now ensure after selecting a new value and then restoring also still works

    // When
    await chooseOption(ui, 'combobox', 'Option 1', user)

    // When
    await user.tab()
    await user.click(combobox)

    // Then
    expect(combobox.value).toBe('Option 1')

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(combobox.value).toBe('')

    // When
    await user.keyboard('{Escape}')

    // then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(combobox.value).toBe('Option 1')
  })

  it('does not closes a dialog when restoring a removed value on esc', async () => {
    // Given
    mockIntersectionObserver()
    const user = userEvent.setup()

    const WrapperComponent = defineComponent({
      components: { AppSelect, ButtonBase, AppDialog },
      setup: () => {
        return {
          showDialog: ref(false),
          options,
        }
      },
      template: `<div><ButtonBase @click="showDialog = true">Dialog Trigger</ButtonBase><AppDialog title="Dialog" v-if="showDialog"><AppSelect :options="options" model-value="456" name="combobox"/></AppDialog></div>`,
    })

    const ui = render(WrapperComponent)

    await flushPromises()

    // When
    await user.click(ui.getByRole('button'))
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(combobox.value).toBe('Option 2')

    // When
    await user.keyboard('{backspace}')

    // Then
    expect(combobox.value).toBe('')

    // When
    await user.keyboard('{Escape}')

    // then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(combobox.value).toBe('Option 2')
    expect(ui.getByRole('dialog')).toBeInTheDocument()
  })

  it('empties the selected value on pressing cross and restores it on esc', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
        placeholder: 'Select an option',
        name: 'combobox',
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await user.type(combobox, 'xxx')
    expect(ui.getAllByRole('option').length).toBe(1)
    expect(combobox.placeholder).toBe('')

    // When
    await user.click(ui.getByLabelText('cross'))

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(combobox.value).toBe('')
    expect(combobox.placeholder).toBe('Select an option')

    expect(ui.getAllByRole('option').length).toBe(4)

    // When
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).toBeInTheDocument()
    })
    await user.keyboard('{Escape}')

    // Then
    expect(combobox.value).toBe('')
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(combobox.value).toBe('Option 2')
  })

  it('sets the current hovered options as active', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await waitFor(() => {
      expect(combobox.value).toBe('Option 2')
    })

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    const option1 = ui.getByRole('option', { name: /Option 1/ })
    await user.hover(option1)

    // Then
    expect(option1).toBeActiveDescendant()
    expect(option1.attributes.getNamedItem('data-highlighted')).toBeDefined()
  })

  it('does not allow disabled values to be selected', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        name: 'combobox',
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await waitFor(() => {
      expect(combobox.value).toBe('Option 2')
    })

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()

    // When
    const option3 = ui.getByRole('option', { name: /Option 3/ })
    expect(option3).toHaveAttribute('data-disabled', '')

    await chooseOption(ui, 'combobox', 'Option 3', user)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(combobox.value).toBe('Option 2')
  })

  it('allows to create new options', async () => {
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: undefined,
        allowCreate: true,
        name: 'combobox',
        options,
      },
    })

    expect(ui.getByLabelText('search')).toBeInTheDocument()
    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    expect(combobox.value).toBe('')

    // When
    await user.click(combobox)

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(ui.getAllByRole('option').length).toBe(4)
    expect(ui.queryByRole('option', { name: /Hello/ })).not.toBeInTheDocument()

    // When
    await user.type(combobox, 'Option 1')

    // Then
    expect(
      ui.getAllByRole('option').filter((option) => option.textContent?.trim() === 'Option 1').length
    ).toBe(1)

    // When
    await user.click(ui.getByLabelText('cross'))
    await user.type(combobox, 'Hello')

    // Then
    expect(ui.getByRole('option', { name: /Hello/ })).toBeInTheDocument()

    // When/Then
    await chooseOption(ui, 'combobox', 'Hello', user)

    // When
    await user.click(combobox)

    // Then
    expect(ui.queryByRole('listbox')).toBeInTheDocument()
    expect(ui.getAllByRole('option').length).toBe(5)
  })

  it('allows to create new options when one was previously selected', async () => {
    const user = userEvent.setup()
    const ui = render(AppSelect, {
      props: {
        modelValue: '456',
        allowCreate: true,
        name: 'combobox',
        options,
      },
    })

    const combobox = ui.getByRole<HTMLInputElement>('combobox')
    await waitFor(() => {
      expect(combobox.value).toBe('Option 2')
    })

    // When
    await user.keyboard('{tab}')
    await user.keyboard('{backspace}')

    // Then
    expect(ui.getByRole('listbox')).toBeInTheDocument()
    expect(ui.queryByRole('option', { name: /Hello/ })).not.toBeInTheDocument()

    // When
    await user.type(combobox, 'Hello')

    // Then
    expect(ui.getByRole('option', { name: /Hello/ })).toBeInTheDocument()

    // When
    await chooseOption(ui, 'combobox', 'Hello', user)

    // Then
    await waitFor(() => {
      expect(ui.queryByRole('listbox')).not.toBeInTheDocument()
    })
  })

  it('ensures that selected option is kept while searching with disabled internal filter', async () => {
    // Given
    const user = userEvent.setup()

    const ui = render(
      defineComponent({
        components: { AppSelect },
        setup() {
          const search = ref('')
          const options = ref<Array<SelectOption>>(
            Array.from(Array(2).keys()).map((index) => ({
              id: `generated_${index}` + (search.value ? '_' + search.value : ''),
              name: `generated_${index}` + (search.value ? '_' + search.value : ''),
            }))
          )
          return {
            options: options,
            modelValue: ref('generated_0'),
            loadNewOptions: () => {
              options.value = []
            },
          }
        },
        template: `<AppSelect :enable-filter="false"  v-model="modelValue" @update:search-query="loadNewOptions"  :options="options" required name="test"/><div id="selectedValue">Selected Value: {{ modelValue }}</div>`,
      })
    )

    await flushPromises()

    const combobox = ui.getByRole(`combobox`) as HTMLInputElement

    expect(ui.getByText('Selected Value: generated_0')).toBeInTheDocument()

    // When
    await user.click(combobox)

    // Then
    expect(await ui.findByRole('listbox')).toBeInTheDocument()

    // When
    await user.type(combobox, 'Option')

    // Then
    await waitFor(() => {
      expect(ui.queryByText('u2_table.no_results_that_match_search')).toBeInTheDocument()
    })

    expect(ui.getByText('Selected Value: generated_0')).toBeInTheDocument()
    expect(
      ui.queryByRole('graphics-symbol', {
        name: 'alert',
      })
    ).not.toBeInTheDocument()
  })
})
