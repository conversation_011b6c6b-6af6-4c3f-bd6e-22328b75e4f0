import flushPromises from 'flush-promises'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { render } from '@testing-library/vue'
import NewByTypeDialog from '@js/components/task/NewByTypeDialog.vue'

describe('NewByTypeDialog', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  it('Renders the task "new by type" dialog', async () => {
    // When
    const ui = render(NewByTypeDialog, {
      props: {
        newPath: '/some/path',
        optionsForNew: {
          type_1: 'Type name 1',
          type_2: 'Type name 2',
        },
      },
    })
    await flushPromises()

    // Then
    expect(ui.getByRole('dialog')).toBeInTheDocument()
    expect(ui.getByText('Type name 1')).toBeInTheDocument()
    expect(ui.getByText('Type name 2')).toBeInTheDocument()
  })
})
