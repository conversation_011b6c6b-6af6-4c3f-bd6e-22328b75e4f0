import TaskDuplicateDialog from '@js/components/task/TaskDuplicateDialog.vue'
import { createTestingPinia } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render } from '@testing-library/vue'
import { setupServer } from '@tests/utils'
import { fromPartial } from '@total-typescript/shoehorn'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { useRouter } from 'vue-router'
import type { Router } from 'vue-router'

describe('TaskDuplicateDialog', () => {
  const server = setupServer()
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('duplicates a task', async () => {
    const push = vi.fn()
    vi.mocked(useRouter).mockReturnValue(fromPartial<Router>({ push }))

    const taskId = '1abc'
    const taskName = 'Test Task'
    const redirect = `/tam/tax-audit-risk/24/edit`
    const user = userEvent.setup()
    server.use(
      http.post(`/legacy/task/${taskId}/duplicate`, () => {
        return HttpResponse.json(
          {
            redirect,
            messages: {},
          },
          { status: StatusCodes.CREATED }
        )
      })
    )
    const ui = render(TaskDuplicateDialog, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { taskId, taskName },
    })

    expect(await ui.findByRole('dialog')).toBeInTheDocument()
    expect(
      ui.getByText(`u2.duplicate.dialog.text {"document_name":"${taskName}"}`)
    ).toBeInTheDocument()

    // When
    await user.click(ui.getByText('u2.duplicate'))

    // Then
    expect(push).toHaveBeenCalledWith(redirect)
  })
})
