import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import LegalFormEditor from '@js/components/LegalFormEditor.vue'

describe('LegalForm editor', () => {
  it('renders', () => {
    // Given
    const component = mount(LegalFormEditor, {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(component.findComponent(BaseInputText).props('label')).toBe('u2_core.name')
    expect(component.findComponent(BaseToggle).props('label')).toBe('u2.enabled')
  })
})
