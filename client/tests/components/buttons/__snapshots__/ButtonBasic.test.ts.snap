// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ButtonBasic > configures color "'action'" on a "'outlined'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm border-action text-action hover:border-action-darker hover:bg-action-transparent hover:text-action-darker focus:border-action-darker focus:bg-action-transparent focus:text-action-darker outlined-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'action'" on a "'solid'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline border-transparent shadow-sm rounded-sm bg-action text-white hover:bg-blue-700 hover:text-white focus:bg-blue-700 focus:text-white active:bg-blue-500 colored-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'action'" on a "'text'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm text-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'bad'" on a "'outlined'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm border-bad text-bad hover:border-bad-darker hover:bg-bad-transparent hover:text-bad-darker focus:border-bad-darker focus:bg-bad-transparent focus:text-bad-darker outlined-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'bad'" on a "'solid'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent shadow-sm rounded-sm bg-bad hover:bg-bad-darker focus:bg-bad-darker text-white hover:text-white focus:text-white colored-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'bad'" on a "'text'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-bad hover:bg-bad-transparent hover:text-bad-darker focus:bg-bad-transparent focus:text-bad-darker rounded-sm text-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'good'" on a "'outlined'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm border-good text-good hover:border-good-lighter hover:bg-good-transparent hover:text-good-lighter focus:border-good-lighter focus:bg-good-transparent focus:text-good-lighter outlined-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'good'" on a "'solid'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline border-transparent shadow-sm rounded-sm bg-good hover:bg-good-darker focus:bg-good-darker active:bg-good-lighter text-white hover:text-white focus:text-white colored-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "'good'" on a "'text'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-good hover:bg-good-transparent hover:text-good-darker focus:bg-good-transparent focus:text-good-darker rounded-sm text-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "undefined" on a "'outlined'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm border-action text-action hover:border-action-darker hover:bg-action-transparent hover:text-action-darker focus:border-action-darker focus:bg-action-transparent focus:text-action-darker outlined-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "undefined" on a "'solid'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline border-transparent shadow-sm rounded-sm bg-action text-white hover:bg-blue-700 hover:text-white focus:bg-blue-700 focus:text-white active:bg-blue-500 colored-button"></button-base-stub>`;

exports[`ButtonBasic > configures color "undefined" on a "'text'" button 1`] = `<button-base-stub disabled="false" type="button" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm text-button"></button-base-stub>`;
