// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RenderedDatasheetTemplate > renders template with field placeholders 1`] = `
<div data-v-b79fbd6d="" data-v-6879554c="" class="relative py-1.5">
  <div data-v-b79fbd6d="" class="isolate overflow-x-auto">
    <div data-v-6879554c="" class="tax-accounting-table">
      <table data-v-6879554c="">
        <thead data-v-6879554c="">
          <tr data-v-6879554c="">
            <th data-v-6879554c=""><button id="reka-popover-trigger-v-1" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-300 bg-gray-200 inline transition-colors duration-700 hover:opacity-70 cursor-context-menu v-popper--has-tooltip" disabled="false"><!--v-if--> <span class="truncate"><span>field1</span></span></span></button>
              <!--teleport start-->
              <!--teleport end-->
              <!--v-if-->
              <!--v-if-->
            </th>
            <th data-v-6879554c=""><button id="reka-popover-trigger-v-2" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false"><!--v-if--> <span class="truncate"><span>field2</span></span></span></button>
              <!--teleport start-->
              <!--teleport end-->
              <!--v-if-->
              <!--v-if-->
            </th>
            <th data-v-6879554c=""><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded bg-alert border-orange-500 text-white inline cursor-pointer transition-colors duration-700 hover:opacity-70 v-popper--has-tooltip" tabindex="0"><!--v-if--> <span class="truncate">field3</span></span>
              <!--v-if-->
              <!--v-if-->
            </th>
          </tr>
        </thead>
      </table>
    </div>
  </div>
  <div data-v-b79fbd6d="" class="pointer-events-none absolute top-0 w-full"></div>
  <!--v-if-->
  <!--v-if-->
</div>
`;
