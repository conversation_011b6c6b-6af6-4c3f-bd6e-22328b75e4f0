import { mount } from '@vue/test-utils'
import { createDatasheetField } from '@tests/__factories__/createDatasheetField'
import { createItem } from '@tests/__factories__/createItem'
import { mockIntersectionObserver, mockResizeObserver } from 'jsdom-testing-mocks'
import { itemTypes } from '@js/model/datasheet'
import DatasheetFormFieldWrapper from '@js/components/datasheet/DatasheetFormFieldWrapper.vue'
import { createTestingPinia } from '@pinia/testing'
import { createDatasheet } from '@tests/__factories__/createDatasheet'
import { createDatasheetCollection } from '@tests/__factories__/createDatasheetCollection'
import { createPeriod } from '@tests/__factories__/createPeriod'
import { createUnit } from '@tests/__factories__/createUnit'
import { useRouteParams, useRouteQuery } from '@vueuse/router'
import { ref } from 'vue'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import flushPromises from 'flush-promises'
import { createHydraCollection, findResourceById, setupServer } from '@tests/utils'
import { HttpResponse, http } from 'msw'
import { datasheetApiBasePath } from '@js/api/datasheetApi'
import invariant from 'tiny-invariant'
import { StatusCodes } from 'http-status-codes'
import { itemApiBasePath } from '@js/api/itemApi'
import { getIdFromIri } from '@js/utilities/api-resource'
import { waitFor } from '@testing-library/vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'

describe('DatasheetFormFieldWrapper', () => {
  vi.mock('@vueuse/router')

  const server = setupServer()
  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  beforeEach(() => {
    mockResizeObserver()
    mockIntersectionObserver()
  })

  it('renders with default slot content', async () => {
    // Given
    const datasheet = createDatasheet()
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      datasheet,
      createItem({ type: itemTypes.money })
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      })
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div data-test="default-slot">Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    // Then
    expect(wrapper.find('[data-test="default-slot"]').exists()).toBe(true)
    expect(wrapper.find('[data-test="default-slot"]').text()).toBe('Default content')
  })

  it('renders with error class when errors are provided', async () => {
    // Given
    const datasheet = createDatasheet()
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      datasheet,
      createItem({ type: itemTypes.money })
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      })
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: ['Error 1', 'Error 2'],
      },
      slots: {
        default: '<div data-test="default-slot">Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })
    // Then
    expect(wrapper.find('.form-widget-item-value').classes()).toContain('has-errors')
    expect(wrapper.find('.form-widget-item-value').classes()).toContain('ring-1')
    expect(wrapper.find('.form-widget-item-value').classes()).toContain('ring-bad')
  })

  it('applies disabled class when disabled prop is true', async () => {
    // Given
    const datasheet = createDatasheet()
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      datasheet,
      createItem({ type: itemTypes.money })
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      })
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: true,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    await flushPromises()

    // Then
    expect(wrapper.find('.form-widget-item-value').attributes('tabindex')).toBe('0')
  })

  it('highlights field when showColors and fieldInFormula are true', async () => {
    // Given
    const item = createItem({ type: itemTypes.money })
    const datasheet = createDatasheet()
    const field = createDatasheetField({ disabled: false, type: itemTypes.money }, datasheet, item)
    const anotherFieldItem = createItem({ type: itemTypes.money, formula: `{${item.id}}` })
    const anotherField = createDatasheetField(
      { disabled: false, type: itemTypes.money },
      datasheet,
      anotherFieldItem
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field, anotherField]), {
          status: StatusCodes.OK,
        })
      }),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item, anotherFieldItem]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    await flushPromises()

    // Then
    expect(wrapper.find('.form-widget-item-value').classes()).not.toContain('colored-border')

    // When
    const fieldInspectorStore = useFieldInspectorStore()
    fieldInspectorStore.showColours = true

    const datasheetParameterStore = useDatasheetParametersStore()
    datasheetParameterStore.parameters.field = anotherField.id

    // Then
    await waitFor(() => {
      expect(wrapper.find('.form-widget-item-value').classes()).toContain('colored-border')
    })
  })

  it('adds selection styling when field is selected', async () => {
    // Given
    const item = createItem({ type: itemTypes.money })
    const datasheet = createDatasheet()
    const field = createDatasheetField({ disabled: false, type: itemTypes.money }, datasheet, item)
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), {
          status: StatusCodes.OK,
        })
      }),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    await flushPromises()

    // Then
    expect(wrapper.find('.form-widget-item-value').classes()).not.toContain('ring-skin-base')

    // When
    const datasheetParameterStore = useDatasheetParametersStore()
    datasheetParameterStore.parameters.field = field.id

    await flushPromises()

    // Then
    await waitFor(() => {
      expect(wrapper.find('.form-widget-item-value').classes()).toContain('ring-1')
    })
    expect(wrapper.find('.form-widget-item-value').classes()).toContain('ring-skin-base')
  })

  it('updates hoveredItemId on mouse over and leave', async () => {
    // Given
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      undefined,
      createItem({ type: itemTypes.money })
    )
    const datasheet = createDatasheet()
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      })
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    // When
    await wrapper.find('.form-widget-item-value').trigger('mouseover')

    // Then
    const fieldInspectorStore = useFieldInspectorStore()
    expect(fieldInspectorStore.hoveredItemId).toBe(getIdFromIri(field.item))

    // When
    await wrapper.find('.form-widget-item-value').trigger('mouseleave')

    // Then
    expect(fieldInspectorStore.hoveredItemId).toBeUndefined()
  })

  it('sets fieldId in store when clicked', async () => {
    // Given
    const item = createItem({ type: itemTypes.money })
    const field = createDatasheetField({ disabled: false, type: itemTypes.money }, undefined, item)
    const datasheet = createDatasheet()
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      }),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })
    const layoutParametersStore = useDatasheetParametersStore()

    // When
    await wrapper.find('.form-widget-item-value').trigger('click')

    // Then
    await flushPromises()
    expect(layoutParametersStore.parameters.field).toBe(field.id)
  })

  it('sets fieldId in store when focused', async () => {
    // Given
    const item = createItem({ type: itemTypes.money })
    const field = createDatasheetField({ disabled: false, type: itemTypes.money }, undefined, item)
    const datasheet = createDatasheet()
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      }),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })
    const layoutParametersStore = useDatasheetParametersStore()

    // When
    await wrapper.find('.form-widget-item-value').trigger('focusin')

    // Then
    await flushPromises()
    expect(layoutParametersStore.parameters.field).toBe(field.id)
  })

  it('applies and removes colorized background based on hover state', async () => {
    // Given
    const datasheet = createDatasheet()
    const item = createItem({ type: itemTypes.money })
    const field = createDatasheetField({ disabled: false, type: itemTypes.money }, datasheet, item)
    const anotherFieldItem = createItem({ type: itemTypes.money, formula: `{${item.id}}` })
    const anotherField = createDatasheetField(
      { disabled: false, type: itemTypes.money },
      datasheet,
      anotherFieldItem
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()
    const itemId = getIdFromIri(field.item)

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(anotherField.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field, anotherField]), {
          status: StatusCodes.OK,
        })
      }),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item, anotherFieldItem]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetFormFieldWrapper, {
      props: {
        field,
        disabled: false,
        errors: [],
      },
      slots: {
        default: '<div>Default content</div>',
      },
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    const fieldInspectorStore = useFieldInspectorStore()
    fieldInspectorStore.showColours = true

    const datasheetParameterStore = useDatasheetParametersStore()
    datasheetParameterStore.parameters.field = anotherField.id

    // Then
    expect(wrapper.find('.form-widget-item-value').classes()).not.toContain('colored-background')

    // When
    fieldInspectorStore.hoveredItemId = itemId

    // Then
    await waitFor(() => {
      expect(wrapper.find('.form-widget-item-value').classes()).toContain('colored-background')
    })

    // When
    await wrapper.find('.form-widget-item-value').trigger('mouseover')

    // Then
    await waitFor(() => {
      expect(wrapper.find('.form-widget-item-value').classes()).not.toContain('colored-background')
    })
  })
})
