// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`DatasheetCollectionGroupView Page > renders unit view with empty template 1`] = `
<div id="root">
  <div id="content" class="main-content max-w-full flex-auto">
    <div class="sticky left-0 z-20 w-[calc(100vw-var(--scrollbar-width))] max-w-full bg-white px-(--main-content-padding) sm:top-0 print:static print:w-11/12">
      <div data-v-72c28b49="">
        <section data-v-72c28b49="" id="page-header" class="page-header">
          <div data-v-72c28b49="">
            <div data-v-72c28b49="" class="pt-3 pl-1"></div>
            <div data-v-72c28b49="" class="m-0 flex w-full flex-wrap items-center gap-1 border-b border-gray-200 px-0 pb-3">
              <div data-v-72c28b49="" class="m-0 flex grow items-center">
                <div class="flex items-center gap-2">
                  <h1 class="m-0 text-2xl leading-tight transition-all ease-in-out sm:text-3xl"><span class="inline-block">Layout Collection collection-id</span> <span class="text-gray-700">Layout 1</span></h1>
                </div>
              </div>
              <div data-v-72c28b49="" id="page-controls" class="page-header-block flex flex-wrap items-center gap-1.5 px-0 print:hidden">
                <div class="flex grow justify-end print:hidden"><button type="button" disabled="" aria-disabled="true" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent mx-0 first:rounded-l last:rounded-r cursor-not-allowed first:border-l-transparent text-gray-500 hover:text-gray-500 focus:text-gray-500 border-l-gray-200 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 text-button">
                    <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="chevron-left"></svg></div>
                  </button> <button type="button" aria-label="u2.datasheets.select_a_datasheet" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker mx-0 bg-blue-100 first:rounded-l last:rounded-r border-l-blue-200 first:border-l-transparent component-button-dropdown text-button v-popper--has-tooltip" id="reka-dropdown-menu-trigger-v-0-0" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                    <!--v-if--> u2.datasheets.plural
                    <!--v-if-->
                  </button>
                  <!--teleport start-->
                  <!--teleport end--> <button type="button" disabled="" aria-disabled="true" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent mx-0 first:rounded-l last:rounded-r cursor-not-allowed first:border-l-transparent text-gray-500 hover:text-gray-500 focus:text-gray-500 border-l-gray-200 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 text-button">
                    <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="chevron-right"></svg></div>
                  </button>
                </div> <button type="button" aria-label="u2.open_menu" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm component-button-dropdown px-1 text-button" id="reka-dropdown-menu-trigger-v-0-1" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                  <!--v-if-->
                  <div class="inline-flex h-4 items-center overflow-visible text-inherit" tabindex="-1">
                    <div class="size-6 inline-block fill-current"><svg role="graphics-symbol img" aria-label="dots"></svg></div>
                  </div>
                  <!--v-if-->
                </button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-x-5 gap-y-2 border-b border-gray-200 bg-white py-2">
            <div class="flex grow flex-wrap items-center gap-3">
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold">u2_core.period: </span> <button id="reka-popover-trigger-v-0-6" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false"><!--v-if--> <span class="truncate"><span class="inline-flex items-center"><!--v-if--> <span>Period</span></span></span></span></button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold whitespace-nowrap">u2.unit_hierarchy:
        </span> <button id="reka-popover-trigger-v-0-8" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false" fallback="u2_core.none_selected"><!--v-if--> <span class="truncate"><span>Unit Hierarchy 1</span></span></span></button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold">u2_core.currency: </span> <span class="v-popper--has-tooltip">EUR</span></div>
              <div class="flex"><button type="button" aria-label="u2.change_parameters" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm print:hidden text-button v-popper--has-tooltip">
                  <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="config"></svg></div>
                </button> <a role="link" href="/datasheets/collections/collection-id/sheets/1?period=1&amp;unit=" class="inline-flex items-center gap-1 button justify-center border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm print:hidden text-button" aria-disabled="false">
                  <!--v-if-->
                  <!--v-if--> u2.switch_to_unit_view
                </a></div>
              <!--v-if-->
            </div>
            <div>
              <div class="flex gap-1">
                <!--v-if--> <button type="button" aria-label="u2.field_inspector.configure_field_inspector" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm text-action hover:border-action-darker hover:bg-action-transparent hover:text-action-darker focus:border-action-darker focus:bg-action-transparent focus:text-action-darker border-skin-base shadow-skin-base py-2.5 print:hidden outlined-button v-popper--has-tooltip" id="reka-popover-trigger-v-0-2" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed">
                  <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="view"></svg></div>
                </button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
            </div>
          </div>
          <!--v-if-->
        </section>
      </div>
    </div>
    <!--v-if-->
    <section class="page-content flex-auto px-(--main-content-padding) pb-(--main-content-padding) print:px-0">
      <div>
        <div></div>
        <div class="w-full flex-col items-center"><label class="form-label">Field 1
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label>
          <div data-v-b8f29289="" class="v-popper v-popper--theme-context-menu wrapper size-full" as-child="">
            <div data-v-b8f29289="" class="form-widget-item-value" tabindex="0">
              <div data-v-b8f29289="" class="v-popper v-popper--theme-menu v-popper--theme-dropdown flex size-full items-center justify-end">
                <div class="relative app-input-number disabled size-full"><input id="unit_hierarchy_view_form_Field 1_value" name="unit_hierarchy_view_form[Field 1][value]" type="text" class="" disabled="">
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-full flex-col items-center"><label class="form-label">Field 2
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label>
          <div data-v-b8f29289="" class="v-popper v-popper--theme-context-menu wrapper size-full" as-child="">
            <div data-v-b8f29289="" class="form-widget-item-value" tabindex="0">
              <div data-v-b8f29289="" class="v-popper v-popper--theme-menu v-popper--theme-dropdown flex size-full items-center justify-end"><textarea rows="1" disabled="" class="expanding-textarea resize-none size-full" id="unit_hierarchy_view_form_Field 2_comment" name="unit_hierarchy_view_form[Field 2][comment]" placeholder="u2.form.text_field_placeholder" style="min-height: 0px;"></textarea> </div>
            </div>
          </div>
        </div>
        <div class="w-full flex-col items-center"><label class="form-label">Field 3
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label>
          <div data-v-b8f29289="" class="v-popper v-popper--theme-context-menu wrapper size-full" as-child="">
            <div data-v-b8f29289="" class="form-widget-item-value" tabindex="0">
              <div data-v-b8f29289="" class="v-popper v-popper--theme-menu v-popper--theme-dropdown flex size-full items-center justify-end">
                <div class="relative app-input-number disabled size-full"><input id="unit_hierarchy_view_form_Field 3_diff" name="unit_hierarchy_view_form[Field 3][diff]" type="text" class="" disabled="">
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-full flex-col items-center"><label class="form-label">Field 4
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label>
          <div data-v-b8f29289="" class="v-popper v-popper--theme-context-menu wrapper size-full" as-child="">
            <div data-v-b8f29289="" class="form-widget-item-value" tabindex="0">
              <div data-v-b8f29289="" class="v-popper v-popper--theme-menu v-popper--theme-dropdown flex size-full items-center justify-end">
                <div class="relative app-input-number disabled size-full"><input id="unit_hierarchy_view_form_Field 4_localCurrencyValue" name="unit_hierarchy_view_form[Field 4][localCurrencyValue]" type="text" class="" disabled="">
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-full flex-col items-center"><label class="form-label">Field 5
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label>
          <div data-v-b8f29289="" class="v-popper v-popper--theme-context-menu wrapper size-full" as-child="">
            <div data-v-b8f29289="" class="form-widget-item-value" tabindex="0">
              <div data-v-b8f29289="" class="v-popper v-popper--theme-menu v-popper--theme-dropdown flex size-full items-center justify-end">
                <div class="relative size-full"><input id="unit_hierarchy_view_form_Field 5_isChecked" name="unit_hierarchy_view_form[Field 5][isChecked]" type="text" class="" disabled="">
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-full flex-col items-center"><label class="form-label">Field 6
            <!--v-if-->
            <!--v-if-->
            <!--v-if-->
          </label>
          <div data-v-b8f29289="" class="v-popper v-popper--theme-context-menu wrapper size-full" as-child="">
            <div data-v-b8f29289="" class="form-widget-item-value" tabindex="0">
              <div data-v-b8f29289="" class="v-popper v-popper--theme-menu v-popper--theme-dropdown flex size-full items-center justify-end">
                <div class="app-input-percent relative transition-colors bg-skin-disabled size-full">
                  <div class="relative app-input-number disabled"><input id="unit_hierarchy_view_form_Field 6_value" name="unit_hierarchy_view_form[Field 6][value]" type="text" class="" disabled="">
                    <!--v-if-->
                  </div> <span class="form-add-on-color absolute inset-y-0 right-0 mr-3 inline-flex items-center">
      %
    </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
`;

exports[`DatasheetCollectionGroupView Page > renders unit view with fields in the template and not in the template 1`] = `
<div id="root">
  <div id="content" class="main-content max-w-full flex-auto">
    <div class="sticky left-0 z-20 w-[calc(100vw-var(--scrollbar-width))] max-w-full bg-white px-(--main-content-padding) sm:top-0 print:static print:w-11/12">
      <div data-v-72c28b49="">
        <section data-v-72c28b49="" id="page-header" class="page-header">
          <div data-v-72c28b49="">
            <div data-v-72c28b49="" class="pt-3 pl-1"></div>
            <div data-v-72c28b49="" class="m-0 flex w-full flex-wrap items-center gap-1 border-b border-gray-200 px-0 pb-3">
              <div data-v-72c28b49="" class="m-0 flex grow items-center">
                <div class="flex items-center gap-2">
                  <h1 class="m-0 text-2xl leading-tight transition-all ease-in-out sm:text-3xl"><span class="inline-block">Layout Collection collection-id</span> <span class="text-gray-700">Layout 1</span></h1>
                </div>
              </div>
              <div data-v-72c28b49="" id="page-controls" class="page-header-block flex flex-wrap items-center gap-1.5 px-0 print:hidden">
                <div class="flex grow justify-end print:hidden"><button type="button" disabled="" aria-disabled="true" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent mx-0 first:rounded-l last:rounded-r cursor-not-allowed first:border-l-transparent text-gray-500 hover:text-gray-500 focus:text-gray-500 border-l-gray-200 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 text-button">
                    <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="chevron-left"></svg></div>
                  </button> <button type="button" aria-label="u2.datasheets.select_a_datasheet" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker mx-0 bg-blue-100 first:rounded-l last:rounded-r border-l-blue-200 first:border-l-transparent component-button-dropdown text-button v-popper--has-tooltip" id="reka-dropdown-menu-trigger-v-0-0" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                    <!--v-if--> u2.datasheets.plural
                    <!--v-if-->
                  </button>
                  <!--teleport start-->
                  <!--teleport end--> <button type="button" disabled="" aria-disabled="true" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent mx-0 first:rounded-l last:rounded-r cursor-not-allowed first:border-l-transparent text-gray-500 hover:text-gray-500 focus:text-gray-500 border-l-gray-200 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 text-button">
                    <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="chevron-right"></svg></div>
                  </button>
                </div> <button type="button" aria-label="u2.open_menu" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm component-button-dropdown px-1 text-button" id="reka-dropdown-menu-trigger-v-0-1" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                  <!--v-if-->
                  <div class="inline-flex h-4 items-center overflow-visible text-inherit" tabindex="-1">
                    <div class="size-6 inline-block fill-current"><svg role="graphics-symbol img" aria-label="dots"></svg></div>
                  </div>
                  <!--v-if-->
                </button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-x-5 gap-y-2 border-b border-gray-200 bg-white py-2">
            <div class="flex grow flex-wrap items-center gap-3">
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold">u2_core.period: </span> <button id="reka-popover-trigger-v-0-6" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false"><!--v-if--> <span class="truncate"><span class="inline-flex items-center"><!--v-if--> <span>Period</span></span></span></span></button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold whitespace-nowrap">u2.unit_hierarchy:
        </span> <button id="reka-popover-trigger-v-0-8" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false" fallback="u2_core.none_selected"><!--v-if--> <span class="truncate"><span>Unit Hierarchy 1</span></span></span></button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold">u2_core.currency: </span> <span class="v-popper--has-tooltip">EUR</span></div>
              <div class="flex"><button type="button" aria-label="u2.change_parameters" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm print:hidden text-button v-popper--has-tooltip">
                  <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="config"></svg></div>
                </button> <a role="link" href="/datasheets/collections/collection-id/sheets/1?period=1&amp;unit=" class="inline-flex items-center gap-1 button justify-center border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm print:hidden text-button" aria-disabled="false">
                  <!--v-if-->
                  <!--v-if--> u2.switch_to_unit_view
                </a></div>
              <!--v-if-->
            </div>
            <div>
              <div class="flex gap-1">
                <!--v-if--> <button type="button" aria-label="u2.field_inspector.configure_field_inspector" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm text-action hover:border-action-darker hover:bg-action-transparent hover:text-action-darker focus:border-action-darker focus:bg-action-transparent focus:text-action-darker border-skin-base shadow-skin-base py-2.5 print:hidden outlined-button v-popper--has-tooltip" id="reka-popover-trigger-v-0-2" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed">
                  <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="view"></svg></div>
                </button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
            </div>
          </div>
          <!--v-if-->
        </section>
      </div>
    </div>
    <!--v-if-->
    <section class="page-content flex-auto px-(--main-content-padding) pb-(--main-content-padding) print:px-0">
      <!--v-if-->
    </section>
  </div>
</div>
`;

exports[`DatasheetCollectionGroupView Page > renders unit view with template referencing all fields 1`] = `
<div id="root">
  <div id="content" class="main-content max-w-full flex-auto">
    <div class="sticky left-0 z-20 w-[calc(100vw-var(--scrollbar-width))] max-w-full bg-white px-(--main-content-padding) sm:top-0 print:static print:w-11/12">
      <div data-v-72c28b49="">
        <section data-v-72c28b49="" id="page-header" class="page-header">
          <div data-v-72c28b49="">
            <div data-v-72c28b49="" class="pt-3 pl-1"></div>
            <div data-v-72c28b49="" class="m-0 flex w-full flex-wrap items-center gap-1 border-b border-gray-200 px-0 pb-3">
              <div data-v-72c28b49="" class="m-0 flex grow items-center">
                <div class="flex items-center gap-2">
                  <h1 class="m-0 text-2xl leading-tight transition-all ease-in-out sm:text-3xl"><span class="inline-block">Layout Collection collection-id</span> <span class="text-gray-700">Layout 1</span></h1>
                </div>
              </div>
              <div data-v-72c28b49="" id="page-controls" class="page-header-block flex flex-wrap items-center gap-1.5 px-0 print:hidden">
                <div class="flex grow justify-end print:hidden"><button type="button" disabled="" aria-disabled="true" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent mx-0 first:rounded-l last:rounded-r cursor-not-allowed first:border-l-transparent text-gray-500 hover:text-gray-500 focus:text-gray-500 border-l-gray-200 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 text-button">
                    <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="chevron-left"></svg></div>
                  </button> <button type="button" aria-label="u2.datasheets.select_a_datasheet" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker mx-0 bg-blue-100 first:rounded-l last:rounded-r border-l-blue-200 first:border-l-transparent component-button-dropdown text-button v-popper--has-tooltip" id="reka-dropdown-menu-trigger-v-0-0" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                    <!--v-if--> u2.datasheets.plural
                    <!--v-if-->
                  </button>
                  <!--teleport start-->
                  <!--teleport end--> <button type="button" disabled="" aria-disabled="true" class="button inline-flex items-center justify-center gap-1 border px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent mx-0 first:rounded-l last:rounded-r cursor-not-allowed first:border-l-transparent text-gray-500 hover:text-gray-500 focus:text-gray-500 border-l-gray-200 bg-gray-100 hover:bg-gray-100 focus:bg-gray-100 text-button">
                    <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="chevron-right"></svg></div>
                  </button>
                </div> <button type="button" aria-label="u2.open_menu" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm component-button-dropdown px-1 text-button" id="reka-dropdown-menu-trigger-v-0-1" aria-haspopup="menu" aria-expanded="false" data-state="closed">
                  <!--v-if-->
                  <div class="inline-flex h-4 items-center overflow-visible text-inherit" tabindex="-1">
                    <div class="size-6 inline-block fill-current"><svg role="graphics-symbol img" aria-label="dots"></svg></div>
                  </div>
                  <!--v-if-->
                </button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-x-5 gap-y-2 border-b border-gray-200 bg-white py-2">
            <div class="flex grow flex-wrap items-center gap-3">
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold">u2_core.period: </span> <button id="reka-popover-trigger-v-0-6" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false"><!--v-if--> <span class="truncate"><span class="inline-flex items-center"><!--v-if--> <span>Period</span></span></span></span></button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold whitespace-nowrap">u2.unit_hierarchy:
        </span> <button id="reka-popover-trigger-v-0-8" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed"><span class="inline-flex max-w-full items-center gap-x-1 border px-2 rounded text-off-black border-gray-200 bg-white inline transition-colors duration-700 hover:opacity-70 cursor-context-menu" disabled="false" fallback="u2_core.none_selected"><!--v-if--> <span class="truncate"><span>Unit Hierarchy 1</span></span></span></button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
              <div class="flex items-center space-x-2 whitespace-nowrap"><span class="font-bold">u2_core.currency: </span> <span class="v-popper--has-tooltip">EUR</span></div>
              <div class="flex"><button type="button" aria-label="u2.change_parameters" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm print:hidden text-button v-popper--has-tooltip">
                  <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="config"></svg></div>
                </button> <a role="link" href="/datasheets/collections/collection-id/sheets/1?period=1&amp;unit=" class="inline-flex items-center gap-1 button justify-center border bg-transparent px-2 py-1.5 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 border-transparent text-action hover:bg-action-transparent hover:text-action-darker focus:bg-action-transparent focus:text-action-darker rounded-sm print:hidden text-button" aria-disabled="false">
                  <!--v-if-->
                  <!--v-if--> u2.switch_to_unit_view
                </a></div>
              <!--v-if-->
            </div>
            <div>
              <div class="flex gap-1">
                <!--v-if--> <button type="button" aria-label="u2.field_inspector.configure_field_inspector" aria-disabled="false" class="button inline-flex items-center justify-center gap-1 border bg-transparent px-2 leading-4 no-underline transition-colors duration-300 ease-in-out hover:no-underline focus:no-underline active:bg-blue-100 shadow-sm rounded-sm text-action hover:border-action-darker hover:bg-action-transparent hover:text-action-darker focus:border-action-darker focus:bg-action-transparent focus:text-action-darker border-skin-base shadow-skin-base py-2.5 print:hidden outlined-button v-popper--has-tooltip" id="reka-popover-trigger-v-0-2" aria-haspopup="dialog" aria-expanded="false" aria-controls="" data-state="closed">
                  <div class="size-4 inline-block fill-current"><svg role="graphics-symbol img" aria-label="view"></svg></div>
                </button>
                <!--teleport start-->
                <!--teleport end-->
              </div>
            </div>
          </div>
          <!--v-if-->
        </section>
      </div>
    </div>
    <!--v-if-->
    <section class="page-content flex-auto px-(--main-content-padding) pb-(--main-content-padding) print:px-0">
      <!--v-if-->
    </section>
  </div>
</div>
`;
