import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { render } from '@testing-library/vue'
import { createItem } from '@tests/__factories__/createItem'
import DatasheetItemFormulaElement from '@js/components/datasheet/DatasheetItemFormulaElement.vue'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import { vi } from 'vitest'
import flushPromises from 'flush-promises'
import { createHydraCollection, findResourceById, setupServer } from '@tests/utils'
import { mockIntersectionObserver, mockResizeObserver } from 'jsdom-testing-mocks'
import { createDatasheet } from '@tests/__factories__/createDatasheet'
import { createDatasheetCollection } from '@tests/__factories__/createDatasheetCollection'
import { createPeriod } from '@tests/__factories__/createPeriod'
import { createUnit } from '@tests/__factories__/createUnit'
import { useRouteParams, useRouteQuery } from '@vueuse/router'
import { ref } from 'vue'
import { HttpResponse, http } from 'msw'
import { datasheetApiBasePath } from '@js/api/datasheetApi'
import invariant from 'tiny-invariant'
import { StatusCodes } from 'http-status-codes'
import { createDatasheetField } from '@tests/__factories__/createDatasheetField'
import { itemTypes } from '@js/model/datasheet'
import { unitApiBasePath } from '@js/api/unitApi'
import { periodApiBasePath } from '@js/api/periodApi'
import { itemApiBasePath } from '@js/api/itemApi'
import { mount } from '@vue/test-utils'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'

describe('DatasheetItemFormulaElement', () => {
  vi.mock('@vueuse/router')

  const server = setupServer()
  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  beforeEach(() => {
    mockResizeObserver()
    mockIntersectionObserver()
  })

  it('renders a regular formula element', () => {
    // Given
    const item = createItem()
    const datasheet = createDatasheet()
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      datasheet,
      createItem({ type: itemTypes.money })
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      }),
      http.get(`${unitApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [unit]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${periodApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [period]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const ui = render(DatasheetItemFormulaElement, {
      props: {
        formulaElement: `{${item.id}}`,
        context: {
          unitId: unit.id,
          periodId: period.id,
        },
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    // Then
    expect(ui.container.querySelector('.datasheet-item-formula-element')).toBeTruthy()
    expect(ui.queryByText('P')).not.toBeInTheDocument() // Should not show P for regular items
  })

  it('renders a previous period formula element', () => {
    // Given
    const item = createItem()
    const datasheet = createDatasheet()
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      datasheet,
      createItem({ type: itemTypes.money })
    )
    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      }),
      http.get(`${unitApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [unit]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${periodApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [period]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const ui = render(DatasheetItemFormulaElement, {
      props: {
        formulaElement: `{P${item.id}}`,
        context: {
          unitId: unit.id,
          periodId: period.id,
        },
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    // Then
    expect(ui.container.querySelector('.datasheet-item-formula-element')).toBeTruthy()
    expect(ui.getByText('P')).toBeInTheDocument() // Should show P for previous period items
  })

  it('updates hover state on mouse events', async () => {
    // Given
    const item = createItem()
    const datasheet = createDatasheet()
    const field = createDatasheetField(
      {
        disabled: false,
        type: itemTypes.money,
      },
      datasheet,
      createItem({ type: itemTypes.money })
    )
    const datasheetCollection = createDatasheetCollection()
    const previousPeriod = createPeriod()
    const period = createPeriod({ previousPeriod: previousPeriod['@id'] })
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), { status: StatusCodes.OK })
      }),
      http.get(`${unitApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [unit]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${periodApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [period, previousPeriod]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetItemFormulaElement, {
      props: {
        formulaElement: `{${item.id}}`,
        context: {
          unitId: unit.id,
          periodId: period.id,
        },
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    const fieldInspectorStore = useFieldInspectorStore()
    await flushPromises()

    // When
    await wrapper.find('.datasheet-item-formula-element').trigger('mouseover')

    // Then
    expect(fieldInspectorStore.hoveredItemId).toBe(item.id)

    // When
    await wrapper.find('.datasheet-item-formula-element').trigger('mouseleave')

    // Then
    expect(fieldInspectorStore.hoveredItemId).toBeUndefined()
  })

  it('highlights field when colorized, showColors and fieldInFormula are true', async () => {
    // Given
    const item = createItem()
    const datasheet = createDatasheet()
    const field = createDatasheetField({ disabled: false, type: itemTypes.money }, datasheet, item)

    const datasheetCollection = createDatasheetCollection()
    const period = createPeriod()
    const unit = createUnit()

    vi.mocked(useRouteParams).mockImplementation(() => ref(datasheet.id))
    vi.mocked(useRouteQuery).mockImplementation(() => ref(field.id))

    server.use(
      http.get(`${datasheetApiBasePath}/:id/fields`, async ({ params }) => {
        invariant(findResourceById(params.id, [datasheet]))

        return HttpResponse.json(createHydraCollection([field]), {
          status: StatusCodes.OK,
        })
      }),
      http.get(`${unitApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [unit]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${periodApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [period]), {
          status: StatusCodes.OK,
        })
      ),
      http.get(`${itemApiBasePath}/:id`, async ({ params }) =>
        HttpResponse.json(findResourceById(params.id, [item]), {
          status: StatusCodes.OK,
        })
      )
    )

    // When
    const wrapper = mount(DatasheetItemFormulaElement, {
      props: {
        formulaElement: `{${item.id}}`,
        context: {
          unitId: unit.id,
          periodId: period.id,
        },
        colorize: true,
      },
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'datasheet-parameters': {
                parameters: {
                  layoutCollection: datasheetCollection.id,
                  layout: datasheet.id,
                  period: period.id,
                  unitHierarchy: unit.id,
                },
              },
            },
          }),
        ],
      },
    })

    const fieldInspectorStore = useFieldInspectorStore()
    fieldInspectorStore.showColours = false

    await flushPromises()

    // Then
    const labelElement = wrapper.find('.datasheet-item-formula-element').find('span')
    expect(labelElement.classes()).not.toContain('colored-border')

    // When
    fieldInspectorStore.showColours = true

    const datasheetParameterStore = useDatasheetParametersStore()
    datasheetParameterStore.parameters.field = field.id

    await flushPromises()

    // Then
    expect(labelElement.classes()).toContain('colored-border')
  })
})
