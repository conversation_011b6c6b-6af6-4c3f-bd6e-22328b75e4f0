import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { fireEvent, render, waitFor } from '@testing-library/vue'
import { createDocumentSection } from '@tests/__factories__/createDocumentSection'
import { createHierarchicalDocumentSection } from '@tests/__factories__/createHierarchicalDocumentSection'
import { createTask } from '@tests/__factories__/createTask'
import { createUnrestrictedAttachment } from '@tests/__factories__/createUnrestrictedAttachment'
import { setupServer } from '@tests/utils'
import axios from 'axios'
import flushPromises from 'flush-promises'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { expect } from 'vitest'
import { encodeDocumentWidget } from '@js/helper/document/encodeDocumentWidget'
import DocumentSection from '@js/components/document/DocumentSection.vue'
import type { DocumentImageWidget } from '@js/model/document'
import type { HierarchicalSection } from '@js/helper/document/transformSectionsToHierarchy'

describe('DocumentSection', () => {
  it('renders', async () => {
    const hierarchicalSection = createHierarchicalDocumentSection({
      renderedContent: 'Rendered content',
      section: createDocumentSection({ editable: true }),
      subHierarchicalSections: [
        createHierarchicalDocumentSection({
          section: createDocumentSection({ editable: true, name: 'Sub section' }),
          renderedContent: 'Sub rendered content',
        }),
      ],
    })

    const ui = render(DocumentSection, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              task: {
                task: createTask(),
              },
            },
          }),
        ],
      },
      props: {
        hierarchicalSection,
        userCanEditConfiguration: true,
        userCanEditContent: true,
      },
    })

    const sectionTitles = await ui.findAllByRole('heading')
    expect(sectionTitles[0].textContent).toContain(hierarchicalSection.section.name)
    expect(sectionTitles[1].textContent).toContain(
      hierarchicalSection.subHierarchicalSections[0].section.name
    )

    expect(
      ui.getByLabelText(`u2.document.section_controls: ${hierarchicalSection.section.name}`)
    ).toBeInTheDocument()
    expect(
      ui.getByLabelText(
        `u2.document.section_controls: ${hierarchicalSection.subHierarchicalSections[0].section.name}`
      )
    ).toBeInTheDocument()

    expect(ui.getByText(hierarchicalSection.renderedContent as string)).toBeInTheDocument()
    expect(
      ui.getByText(hierarchicalSection.subHierarchicalSections[0].renderedContent as string)
    ).toBeInTheDocument()

    expect(
      ui.getByLabelText(`u2.collapse: ${hierarchicalSection.section.name}`)
    ).toBeInTheDocument()
    expect(
      ui.getByLabelText(
        `u2.collapse: ${hierarchicalSection.subHierarchicalSections[0].section.name}`
      )
    ).toBeInTheDocument()

    expect(
      ui.queryByRole('graphics-symbol', {
        name: 'lock-closed',
      })
    ).not.toBeInTheDocument()
  })

  it('renders with lock icon when it is not editable', async () => {
    const ui = render(DocumentSection, {
      global: {
        stubs: {
          SvgIcon: false,
        },
        plugins: [
          createTestingPinia({
            initialState: {
              task: {
                task: createTask(),
              },
            },
          }),
        ],
      },
      props: {
        hierarchicalSection: createHierarchicalDocumentSection({
          section: createDocumentSection({ editable: false }),
        }),
        userCanEditConfiguration: true,
        userCanEditContent: true,
      },
    })

    expect(
      await ui.findByRole('graphics-symbol', {
        name: 'lock-closed',
      })
    ).toBeInTheDocument()
  })

  test('focuses interactive elements on tab', async () => {
    const user = userEvent.setup()
    const hierarchicalSection = createHierarchicalDocumentSection({
      section: createDocumentSection({
        editable: true,
        include: true,
      }),
      renderedContent: 'Rendered content',
    })
    const ui = render(DocumentSection, {
      global: {
        stubs: {
          SvgIcon: false,
        },
        plugins: [
          createTestingPinia({
            initialState: {
              task: {
                task: createTask(),
              },
            },
          }),
        ],
      },
      props: {
        hierarchicalSection,
        userCanEditConfiguration: true,
        userCanEditContent: true,
      },
    })

    // On collapse button
    await user.tab()
    expect(ui.getByLabelText(`u2.collapse: ${hierarchicalSection.section.name}`)).toHaveFocus()

    // On hidden edit button on the title
    await user.tab()
    expect(ui.getByLabelText(`u2.edit: ${hierarchicalSection.section.name}`)).toHaveFocus()

    // On section controls
    await user.tab()
    expect(
      ui.getByLabelText(`u2.document.section_controls: ${hierarchicalSection.section.name}`)
    ).toHaveFocus()

    // On hidden edit button on the content
    await user.tab()
    expect(ui.getByLabelText(`u2.edit_content: ${hierarchicalSection.section.name}`)).toHaveFocus()

    // Back to section controls
    await user.tab({ shift: true })
    expect(
      ui.getByLabelText(`u2.document.section_controls: ${hierarchicalSection.section.name}`)
    ).toHaveFocus()

    // Back to hidden edit button on the title
    await user.tab({ shift: true })
    expect(ui.getByLabelText(`u2.edit: ${hierarchicalSection.section.name}`)).toHaveFocus()

    // Back to collapse button
    await user.tab({ shift: true })
    expect(ui.getByLabelText(`u2.collapse: ${hierarchicalSection.section.name}`)).toHaveFocus()
  })

  describe('edit mode', () => {
    const attachment = createUnrestrictedAttachment({
      entity: '/api/master-file-sections/1',
      file: '/api/files/100',
    })
    const server = setupServer(
      http.get('/api/master-file-sections/1/attachments', async () => {
        return HttpResponse.json(
          {
            'hydra:member': [attachment],
            'hydra:totalItems': 0,
          },
          { status: StatusCodes.OK }
        )
      }),
      http.post(`/legacy/structured-document/section/tpm-master-file-section/1/edit`, async () => {
        return HttpResponse.json({}, { status: StatusCodes.OK })
      })
    )

    beforeAll(() => server.listen())

    afterEach(() => server.resetHandlers())

    afterAll(() => server.close())

    async function expectSectionEditorToBeOpen(
      ui: ReturnType<typeof render>,
      hierarchicalSection: HierarchicalSection
    ) {
      /* TODO: Use iframe_aria_text config option in tinymce for the iframe title: https://www.tiny.cloud/docs/tinymce/latest/accessibility/#iframe_aria_text
      At the moment the option adds an aria-label to the body of the iframe and it is not possible to access the body directly
      */
      const iframe: HTMLIFrameElement = await ui.findByTitle('Rich Text Area')
      expect(iframe.contentDocument?.body.innerHTML).toBe(hierarchicalSection.section.content)
      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'true'
      )
    }

    test('switches to edit mode when section title is clicked', async () => {
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          editable: true,
          include: true,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              stubActions: false,
              initialState: {
                task: {
                  task: createTask(),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )

      await user.click(ui.getByText(hierarchicalSection.section.name))

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveFocus()
      await expectSectionEditorToBeOpen(ui, hierarchicalSection)
    })

    test('switches to edit mode when section content is clicked', async () => {
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          editable: true,
          include: true,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              stubActions: false,
              initialState: {
                task: {
                  task: createTask(),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )

      await user.click(ui.getByText(hierarchicalSection.renderedContent as string))
      await expectSectionEditorToBeOpen(ui, hierarchicalSection)
    })

    test('switches to edit mode on enter', async () => {
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          editable: true,
          include: true,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              stubActions: false,
              initialState: {
                task: {
                  task: createTask(),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )
      expect(ui.queryByTitle('Rich Text Area')).not.toBeInTheDocument()
      expect(document.body).toHaveFocus()

      // When
      await user.tab()
      await user.tab()
      await user.keyboard('{enter}')

      // Then
      await expectSectionEditorToBeOpen(ui, hierarchicalSection)

      // When
      await user.click(ui.container.querySelector('[data-mce-name="cancel"]') as HTMLButtonElement)
      await user.tab()
      await user.tab()
      await user.tab()
      await user.keyboard('{enter}')

      // Then
      await expectSectionEditorToBeOpen(ui, hierarchicalSection)
    })

    test('expands a collapsed section on click when switching to edit mode', async () => {
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          id: 1,
          editable: true,
          include: true,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              stubActions: false,
              initialState: {
                task: {
                  task: createTask(),
                },
                document: {
                  collapsedSections: new Set([1]),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )
      expect(ui.queryByText(hierarchicalSection.renderedContent as string)).not.toBeVisible()

      await user.click(ui.getByText(hierarchicalSection.section.name))

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'true'
      )
      expect(ui.container.querySelector('.tinymce-borderless')).toBeVisible()
    })

    test('expands a collapsed section in edit mode when title is focused', async () => {
      // Given
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          id: 1,
          editable: true,
          include: true,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              stubActions: false,
              initialState: {
                task: {
                  task: createTask(),
                },
                document: {
                  collapsedSections: new Set([1]),
                  editedSections: new Set([1]),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      // Then
      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'true'
      )
      expect(ui.container.querySelector('.tinymce-borderless')).not.toBeVisible()
      expect(document.body).toHaveFocus()

      // When
      await user.tab()
      expect(ui.getByLabelText(`u2.expand: ${hierarchicalSection.section.name}`)).toHaveFocus()
      await user.tab()

      // Then
      expect(ui.getByText(hierarchicalSection.section.name)).toHaveFocus()
      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'true'
      )
      expect(ui.container.querySelector('.tinymce-borderless')).toBeVisible()
    })

    test('does not switch to edit mode if there is selection', async () => {
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          name: 'Section',
          editable: true,
          include: true,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              initialState: {
                task: {
                  task: createTask(),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      await user.pointer([
        {
          target: ui.getByText(hierarchicalSection.section.name),
          offset: 0,
          keys: '[MouseLeft>]', // press the left mouse button
        },
      ])
      await user.pointer({ offset: hierarchicalSection.section.name.length })
      await user.pointer({ keys: '[/MouseLeft]' }) // release the left mouse button

      const selection = document.getSelection()?.toString()
      expect(selection).toBe(hierarchicalSection.section.name)

      await user.click(ui.getByText(hierarchicalSection.section.name))

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )
      /* TODO: Use iframe_aria_text config option in tinymce for the iframe title: https://www.tiny.cloud/docs/tinymce/latest/accessibility/#iframe_aria_text
      At the moment the option adds an aria-label to the body of the iframe and it is not possible to access the body directly
      */
      expect(ui.queryByTitle('Rich Text Area')).not.toBeInTheDocument()

      await user.pointer([{ target: ui.getByText('Section'), offset: 0, keys: '[MouseLeft>]' }])
      await user.pointer({ offset: hierarchicalSection.section.name.length })
      await user.pointer({ keys: '[/MouseLeft]' })

      await user.click(ui.getByText(hierarchicalSection.renderedContent as string))

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )
      /* TODO: Use iframe_aria_text config option in tinymce for the iframe title: https://www.tiny.cloud/docs/tinymce/latest/accessibility/#iframe_aria_text
      At the moment the option adds an aria-label to the body of the iframe and it is not possible to access the body directly
      */
      expect(ui.queryByTitle('Rich Text Area')).not.toBeInTheDocument()
    })

    test('does not switch to edit mode if a link in the section is clicked', async () => {
      const user = userEvent.setup()
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          name: 'Section',
          editable: true,
          include: true,
        }),
        renderedContent: '<a href="#">Rendered content</a>',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              initialState: {
                task: {
                  task: createTask(),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      await user.click(ui.getByRole('link', { name: 'Rendered content' }))

      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )
      /* TODO: Use iframe_aria_text config option in tinymce for the iframe title: https://www.tiny.cloud/docs/tinymce/latest/accessibility/#iframe_aria_text
      At the moment the option adds an aria-label to the body of the iframe and it is not possible to access the body directly
      */
      expect(ui.queryByTitle('Rich Text Area')).not.toBeInTheDocument()

      // Check that the second click on the same link does not open the editor
      await user.click(ui.getByRole('link', { name: 'Rendered content' }))
      expect(ui.getByText(hierarchicalSection.section.name)).toHaveAttribute(
        'contenteditable',
        'false'
      )
      expect(ui.queryByTitle('Rich Text Area')).not.toBeInTheDocument()
    })

    test('saves correct data when image widget is deleted', async () => {
      const axiosSpy = vi.spyOn(axios, 'post')
      const user = userEvent.setup()
      const encodedWidget = encodeDocumentWidget({
        name: 'image',
        parameters: { id: '100', width: '1000px' },
      } as DocumentImageWidget)
      const hierarchicalSection = createHierarchicalDocumentSection({
        section: createDocumentSection({
          '@type': 'MasterFileSection',
          id: 1,
          editable: true,
          include: true,
          level: 1,
          content: `<widget>${encodedWidget}</widget>`,
        }),
        renderedContent: 'Rendered content',
      })
      const ui = render(DocumentSection, {
        global: {
          stubs: {
            SvgIcon: false,
          },
          plugins: [
            createTestingPinia({
              stubActions: false,
              initialState: {
                document: {
                  attachments: [attachment],
                },
                task: {
                  task: createTask(),
                },
              },
            }),
          ],
        },
        props: {
          hierarchicalSection,
          userCanEditConfiguration: true,
          userCanEditContent: true,
        },
      })

      // When
      await user.tab()
      await user.tab()
      await user.keyboard('{enter}')

      // Then
      /* TODO: Use iframe_aria_text config option in tinymce for the iframe title: https://www.tiny.cloud/docs/tinymce/latest/accessibility/#iframe_aria_text
      At the moment the option adds an aria-label to the body of the iframe and it is not possible to access the body directly
      */
      const iframe: HTMLIFrameElement = await ui.findByTitle('Rich Text Area')
      expect(iframe.contentDocument?.body.innerHTML).toContain('data-image-document-widget')

      await flushPromises()

      // When
      await user.click(
        iframe.contentDocument?.body.querySelector(
          '[data-image-document-widget] > img'
        ) as HTMLImageElement
      )
      const iframeBody = iframe.contentDocument?.body as HTMLBodyElement
      await fireEvent.keyDown(iframeBody, { key: 'Backspace', code: 'Backspace' })
      await fireEvent.keyUp(iframeBody, { key: 'Backspace', code: 'Backspace' })

      // Then
      expect(iframe.contentDocument?.body.innerHTML).toBe('<p></p>')

      // When
      await user.click(ui.container.querySelector('[data-mce-name="save"]') as HTMLButtonElement)

      await waitFor(() => {
        expect(ui.container.querySelector('[data-mce-name="save"]')).not.toBeInTheDocument()
      })

      // Then
      expect(axiosSpy).toHaveBeenCalledWith(
        '/legacy/structured-document/section/tpm-master-file-section/1/edit',
        expect.any(FormData)
      )
      const actualFormData = axiosSpy.mock.calls[0][1] as FormData
      expect(actualFormData.get('document_section_form[content]')).toBe('')
    })
  })
})
