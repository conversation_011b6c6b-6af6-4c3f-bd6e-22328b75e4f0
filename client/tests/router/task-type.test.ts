import routes from '@js/router/task-type'
import BaseLayout from '@js/layouts/BaseLayout.vue'

describe('task type route configuration', () => {
  it('ensures new task routes are setup correctly', () => {
    const apmTransactionNew = routes.find((route) => route.name === 'ApmTransactionNew')
    expect(apmTransactionNew?.path).toBe('/apm/transaction/new')
    expect(apmTransactionNew?.meta?.layout).toBe(BaseLayout)
    expect(apmTransactionNew?.meta?.title).toBeDefined()
    expect(typeof apmTransactionNew?.meta?.title).toBe('function')

    const contractNew = routes.find((route) => route.name === 'ContractNew')
    expect(contractNew?.path).toBe('/contract-management/contract/new')
    expect(contractNew?.meta?.layout).toBe(BaseLayout)
    expect(contractNew?.meta?.title).toBeDefined()
    expect(typeof contractNew?.meta?.title).toBe('function')

    const taxRateNew = routes.find((route) => route.name === 'TaxRateNew')
    expect(taxRateNew?.path).toBe('/tam/tax-rate/new')
    expect(taxRateNew?.meta?.layout).toBe(BaseLayout)
    expect(taxRateNew?.meta?.title).toBeDefined()
    expect(typeof taxRateNew?.meta?.title).toBe('function')
  })

  it('ensures edit task routes are setup correctly', () => {
    const apmTransactionEdit = routes.find((route) => route.name === 'ApmTransactionEdit')
    expect(apmTransactionEdit?.path).toBe('/apm/transaction/:id/edit')
    expect(apmTransactionEdit?.meta?.layout).toBe(BaseLayout)
    expect(apmTransactionEdit?.meta?.title).toBeUndefined()

    const contractEdit = routes.find((route) => route.name === 'ContractEdit')
    expect(contractEdit?.path).toBe('/contract-management/contract/:id/edit')
    expect(contractEdit?.meta?.layout).toBe(BaseLayout)

    const taxRateEdit = routes.find((route) => route.name === 'TaxRateEdit')
    expect(taxRateEdit?.path).toBe('/tam/tax-rate/:id/edit')
    expect(taxRateEdit?.meta?.layout).toBe(BaseLayout)
  })

  it('ensures list task routes are setup correctly', () => {
    const apmTransactionList = routes.find((route) => route.name === 'ApmTransactionList')
    expect(apmTransactionList?.path).toBe('/apm/transaction')
    expect(apmTransactionList?.meta?.layout).toBe(BaseLayout)
    expect(apmTransactionList?.meta?.layoutFormat).toBe('wide')
    expect(apmTransactionList?.meta?.auth).toBe('APM_TRANSACTION:READ')
    expect(apmTransactionList?.meta?.globalSearch).toBeDefined()
    expect(apmTransactionList?.meta?.globalSearch?.icon).toBe('list')
    expect(typeof apmTransactionList?.meta?.globalSearch?.name).toBe('function')

    const contractList = routes.find((route) => route.name === 'ContractList')
    expect(contractList?.path).toBe('/contract-management/contract')
    expect(contractList?.meta?.layout).toBe(BaseLayout)
    expect(contractList?.meta?.layoutFormat).toBe('wide')
    expect(contractList?.meta?.auth).toBe('CM_CONTRACT:READ')
    expect(contractList?.meta?.globalSearch).toBeDefined()

    const taxRateList = routes.find((route) => route.name === 'TaxRateList')
    expect(taxRateList?.path).toBe('/tam/tax-rate')
    expect(taxRateList?.meta?.layout).toBe(BaseLayout)
    expect(taxRateList?.meta?.layoutFormat).toBe('wide')
    expect(taxRateList?.meta?.auth).toBe('TAM_TAX_RATE:READ')
    expect(taxRateList?.meta?.globalSearch).toBeDefined()
  })

  it('ensures document task routes are setup correctly', () => {
    const apmTransactionDocument = routes.find((route) => route.name === 'ApmTransactionDocument')
    expect(apmTransactionDocument?.path).toBe('/apm/transaction/:id/edit-document')
    expect(apmTransactionDocument?.meta?.layout).toBe(BaseLayout)

    const contractDocument = routes.find((route) => route.name === 'ContractDocument')
    expect(contractDocument?.path).toBe('/contract-management/contract/:id/edit-document')
    expect(contractDocument?.meta?.layout).toBe(BaseLayout)

    const taxRateDocument = routes.find((route) => route.name === 'TaxRateDocument')
    expect(taxRateDocument?.path).toBe('/tam/tax-rate/:id/edit-document')
    expect(taxRateDocument?.meta?.layout).toBe(BaseLayout)
  })

  it('ensures bulk edit route is setup correctly', () => {
    const taskBulkEdit = routes.find((route) => route.name === 'TaskBulkEdit')
    expect(taskBulkEdit?.path).toBe('/bulk/:shortName/edit')
    expect(taskBulkEdit?.meta?.layout).toBe(BaseLayout)
    expect(typeof taskBulkEdit?.beforeEnter).toBe('function')
  })

  it('ensures all new routes have required properties', () => {
    const newRouteNames = [
      'ApmTransactionNew',
      'ContractNew',
      'CountryByCountryReportNew',
      'FinancialDataNew',
      'Igt1TransactionNew',
      'Igt2TransactionNew',
      'Igt3TransactionNew',
      'Igt4TransactionNew',
      'Igt5TransactionNew',
      'IncomeTaxPlanningNew',
      'LocalFileNew',
      'LossCarryForwardNew',
      'MainBusinessActivityNew',
      'MasterFileNew',
      'OtherDeadlineNew',
      'TaxAssessmentMonitorNew',
      'TaxAssessmentStatusNew',
      'TaxAuditRiskNew',
      'TaxAuthorityAuditObjectionNew',
      'TaxConsultingFeeNew',
      'TaxCreditNew',
      'TaxFilingMonitorNew',
      'TaxLitigationNew',
      'TaxRelevantRestrictionNew',
      'TaxRateNew',
      'TransactionNew',
      'TransferPricingNew',
      'UnitPeriodNew',
    ]

    newRouteNames.forEach((routeName) => {
      const route = routes.find((r) => r.name === routeName)
      expect(route).toBeDefined()
      expect(route?.path).toBeDefined()
      expect(route?.path).toContain('/new')
      expect(route?.meta?.layout).toBe(BaseLayout)
      expect(route?.meta?.title).toBeDefined()
      expect(typeof route?.meta?.title).toBe('function')
    })
  })

  it('ensures all edit routes have required properties', () => {
    const editRouteNames = [
      'ApmTransactionEdit',
      'ContractEdit',
      'CountryByCountryReportEdit',
      'FinancialDataEdit',
      'Igt1TransactionEdit',
      'Igt2TransactionEdit',
      'Igt3TransactionEdit',
      'Igt4TransactionEdit',
      'Igt5TransactionEdit',
      'IncomeTaxPlanningEdit',
      'LocalFileEdit',
      'LossCarryForwardEdit',
      'MainBusinessActivityEdit',
      'MasterFileEdit',
      'OtherDeadlineEdit',
      'TaxAssessmentMonitorEdit',
      'TaxAssessmentStatusEdit',
      'TaxAuditRiskEdit',
      'TaxAuthorityAuditObjectionEdit',
      'TaxConsultingFeeEdit',
      'TaxCreditEdit',
      'TaxFilingMonitorEdit',
      'TaxLitigationEdit',
      'TaxRelevantRestrictionEdit',
      'TaxRateEdit',
      'TransactionEdit',
      'TransferPricingEdit',
      'UnitPeriodEdit',
    ]

    editRouteNames.forEach((routeName) => {
      const route = routes.find((r) => r.name === routeName)
      expect(route).toBeDefined()
      expect(route?.path).toBeDefined()
      expect(route?.path).toContain('/:id/edit')
      expect(route?.meta?.layout).toBe(BaseLayout)
      expect(route?.meta?.title).toBeUndefined()
    })
  })

  it('ensures all list routes have required properties', () => {
    const listRouteNames = [
      'ApmTransactionList',
      'ContractList',
      'CountryByCountryReportList',
      'FinancialDataList',
      'Igt1TransactionList',
      'Igt2TransactionList',
      'Igt3TransactionList',
      'Igt4TransactionList',
      'Igt5TransactionList',
      'IncomeTaxPlanningList',
      'LocalFileList',
      'LossCarryForwardList',
      'MainBusinessActivityList',
      'MasterFileList',
      'OtherDeadlineList',
      'TaxAssessmentMonitorList',
      'TaxAssessmentStatusList',
      'TaxAuditRiskList',
      'TaxAuthorityAuditObjectionList',
      'TaxConsultingFeeList',
      'TaxCreditList',
      'TaxFilingMonitorList',
      'TaxLitigationList',
      'TaxRelevantRestrictionList',
      'TaxRateList',
      'TransactionList',
      'TransferPricingList',
      'UnitPeriodList',
    ]

    listRouteNames.forEach((routeName) => {
      const route = routes.find((r) => r.name === routeName)
      expect(route).toBeDefined()
      expect(route?.path).toBeDefined()
      expect(route?.path).not.toContain('/:id')
      expect(route?.meta?.layout).toBe(BaseLayout)
      expect(route?.meta?.layoutFormat).toBe('wide')
      expect(route?.meta?.auth).toBeDefined()
      expect(typeof route?.meta?.auth).toBe('string')
      expect(route?.meta?.globalSearch).toBeDefined()
      expect(route?.meta?.globalSearch?.icon).toBe('list')
      expect(typeof route?.meta?.globalSearch?.name).toBe('function')
    })
  })

  it('ensures all document routes have required properties', () => {
    const documentRouteNames = [
      'ApmTransactionDocument',
      'ContractDocument',
      'CountryByCountryReportDocument',
      'FinancialDataDocument',
      'Igt1TransactionDocument',
      'Igt2TransactionDocument',
      'Igt3TransactionDocument',
      'Igt4TransactionDocument',
      'Igt5TransactionDocument',
      'IncomeTaxPlanningDocument',
      'LocalFileDocument',
      'LossCarryForwardDocument',
      'MainBusinessActivityDocument',
      'MasterFileDocument',
      'OtherDeadlineDocument',
      'TaxAssessmentMonitorDocument',
      'TaxAssessmentStatusDocument',
      'TaxAuditRiskDocument',
      'TaxAuthorityAuditObjectionDocument',
      'TaxConsultingFeeDocument',
      'TaxCreditDocument',
      'TaxFilingMonitorDocument',
      'TaxLitigationDocument',
      'TaxRelevantRestrictionDocument',
      'TaxRateDocument',
      'TransactionDocument',
      'TransferPricingDocument',
      'UnitPeriodDocument',
    ]

    documentRouteNames.forEach((routeName) => {
      const route = routes.find((r) => r.name === routeName)
      expect(route).toBeDefined()
      expect(route?.path).toBeDefined()
      expect(route?.path).toContain('/:id/edit-document')
      expect(route?.meta?.layout).toBe(BaseLayout)
    })
  })

  it('ensures correct total number of routes', () => {
    expect(routes).toHaveLength(113)
  })

  it('ensures all routes use BaseLayout', () => {
    routes.forEach((route) => {
      expect(route.meta?.layout).toBe(BaseLayout)
    })
  })

  it('ensures route names are unique', () => {
    const routeNames = routes.map((route) => route.name)
    const uniqueRouteNames = new Set(routeNames)
    expect(routeNames).toHaveLength(uniqueRouteNames.size)
  })

  it('ensures route paths are unique', () => {
    const routePaths = routes.map((route) => route.path)
    const uniqueRoutePaths = new Set(routePaths)
    expect(routePaths).toHaveLength(uniqueRoutePaths.size)
  })
})
