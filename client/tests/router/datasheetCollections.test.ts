import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import routes, { buildDatasheetRoute } from '@js/router/datasheetCollections'
import ConfigurationDataLayout from '@js/layouts/ConfigurationDataLayout.vue'
import BaseLayout from '@js/layouts/BaseLayout.vue'

describe('datasheet collection route configuration', () => {
  it('ensures each datasheet collection routes are setup correctly', async () => {
    const datasheetEdit = routes.find((route) => route.name === 'DatasheetCollectionEdit')
    expect(datasheetEdit?.meta?.auth).toBe('ROLE_ADMIN')
    expect(datasheetEdit?.meta?.layout).toBe(ConfigurationDataLayout)
    expect(datasheetEdit?.path).toBe('/configuration/datasheets/collections/:id')

    const datasheetNew = routes.find((route) => route.name === 'DatasheetCollectionNew')
    expect(datasheetNew?.meta?.auth).toBe('ROLE_ADMIN')
    expect(datasheetNew?.meta?.layout).toBe(ConfigurationDataLayout)
    expect(datasheetNew?.path).toBe('/configuration/datasheets/collections/new')

    const datasheetList = routes.find((route) => route.name === 'DatasheetCollectionList')
    expect(datasheetList?.meta?.auth).toBe('ROLE_ADMIN')
    expect(datasheetList?.meta?.layout).toBe(ConfigurationDataLayout)
    expect(datasheetList?.path).toBe('/configuration/datasheets/collections')
  })

  it('ensures datasheet breakdown route is setup correctly', async () => {
    const datasheetCollectionGroupViewBreakdown = routes.find(
      (route) => route.name === 'DatasheetCollectionGroupViewBreakdown'
    )
    expect(datasheetCollectionGroupViewBreakdown?.meta?.layout).toBe(BaseLayout)
    expect(datasheetCollectionGroupViewBreakdown?.meta?.layoutFormat).toBe('wide')
    expect(datasheetCollectionGroupViewBreakdown?.path).toBe(
      '/datasheets/breakdown/unit-hierarchy/by-unit'
    )
  })

  it('ensures datasheet country report route is setup correctly', async () => {
    const itemCountryReport = routes.find((route) => route.name === 'ItemCountryReport')
    expect(itemCountryReport?.meta?.layout).toBe(BaseLayout)
    expect(itemCountryReport?.meta?.layoutFormat).toBe('wide')
    expect(itemCountryReport?.path).toBe('/datasheets/breakdown/unit-hierarchy/by-country')
  })

  it('ensures datasheet sheet view route is setup correctly', async () => {
    const datasheetCollectionSheetView = routes.find(
      (route) => route.name === 'DatasheetCollectionSheetView'
    )
    expect(datasheetCollectionSheetView?.meta?.layout).toBe(BaseLayout)
    expect(datasheetCollectionSheetView?.meta?.layoutFormat).toBe('wide')
    expect(datasheetCollectionSheetView?.path).toBe('/datasheets/collections/:id/sheets/:sheetId')
  })

  it('ensures no datasheet selected route is setup correctly', async () => {
    const datasheetCollectionSheetView = routes.find(
      (route) => route.name === 'DatasheetCollectionNoSheetSelected'
    )
    expect(datasheetCollectionSheetView?.path).toBe('/datasheets/collections/:id')
  })
})

describe('buildDatasheetRoute', () => {
  beforeEach(() => {
    createTestingPinia({
      initialState: {
        'datasheet-parameters': {
          parameters: {
            unitHierarchy: 1111,
            period: 2222,
            unit: 3333,
            layoutCollection: 4444,
          },
        },
      },
    })
  })

  it('builds route for unit view with layoutId', () => {
    const parameters = {
      layoutCollectionId: 'abc',
      layoutId: 2,
      unitId: 3,
      unitHierarchyId: undefined,
      periodId: 4,
      fieldId: 5,
    }
    const route = buildDatasheetRoute(parameters)
    expect(route.name).toBe('DatasheetCollectionSheetView')
    expect(route.params.id).toBe('abc')
    expect(route.params.sheetId).toBe(2)
    expect(route.query.unit).toBe(3)
    expect(route.query.unitHierarchy).toBeUndefined()
    expect(route.query.period).toBe(4)
    expect(route.query.field).toBe(5)
  })

  it('builds route for hierarchy view with layoutId', () => {
    const parameters = {
      layoutCollectionId: 'abc',
      layoutId: 2,
      unitId: undefined,
      hierarchyId: 3,
      periodId: 4,
      fieldId: 5,
    }
    const route = buildDatasheetRoute(parameters)
    expect(route.name).toBe('DatasheetCollectionSheetView')
    expect(route.params.id).toBe('abc')
    expect(route.params.sheetId).toBe(2)
    expect(route.query.unitHierarchy).toBe(3)
    expect(route.query.unit).toBeUndefined()
    expect(route.query.period).toBe(4)
    expect(route.query.field).toBe(5)
  })

  it('builds route for unit view without layoutId', () => {
    const parameters = {
      layoutCollectionId: 'abc',
      unitId: 3,
      unitHierarchyId: undefined,
      periodId: 4,
      fieldId: 5,
    }
    const route = buildDatasheetRoute(parameters)
    expect(route.name).toBe('DatasheetCollectionNoSheetSelected')
    expect(route.params.id).toBe('abc')
    expect(route.query.unit).toBe(3)
    expect(route.query.unitHierarchy).toBeUndefined()
    expect(route.query.period).toBe(4)
    expect(route.query.field).toBe(5)
  })

  it('builds route for hierarchy view without layoutId', () => {
    const parameters = {
      layoutCollectionId: 'abc',
      unitId: undefined,
      hierarchyId: 3,
      periodId: 4,
      fieldId: 5,
    }
    const route = buildDatasheetRoute(parameters)
    expect(route.name).toBe('DatasheetCollectionNoSheetSelected')
    expect(route.params.id).toBe('abc')
    expect(route.query.unitHierarchy).toBe(3)
    expect(route.query.unit).toBeUndefined()
    expect(route.query.period).toBe(4)
    expect(route.query.field).toBe(5)
  })
})
