/**
 * Polyfill for Blob in JSDOM
 * We should remove when it is no longer needed
 * @see https://github.com/jsdom/jsdom/issues/2555
 */
import 'blob-polyfill'
import '@js/zodErrorMap'
import { VueQueryPlugin } from '@tanstack/vue-query'
import SvgIconStub from '@tests/stubs/SvgIcon.stub.vue'
import { config } from '@vue/test-utils'
import FloatingVue from 'floating-vue'
// @ts-expect-error The jest-serializer-vue package does not currently have types
import vueSnapshotSerializer from 'jest-serializer-vue'
import { vi } from 'vitest'
import failOnConsole from 'vitest-fail-on-console'
import { defineComponent, ref } from 'vue'
import '@testing-library/jest-dom'
import testQueryClient from '@tests/testQueryClient'

failOnConsole({
  shouldFailOnDebug: true,
  shouldFailOnLog: true,
  shouldFailOnInfo: true,
  shouldFailOnWarn: true,
  shouldFailOnError: true,
  shouldFailOnAssert: true,
  /* Add delay after each test in order to fail on late thrown console messages */
  afterEachDelay: 0,
  silenceMessage: (errorMessage: string, methodName: string) => {
    if (methodName === 'info') {
      // Ignore the info that <Suspense> is an experimental feature and its API will likely change
      if (/<Suspense>/.test(errorMessage)) {
        return true
      }
    }

    return false
  },
})

expect.extend({
  toBeActiveDescendant(received: HTMLElement) {
    const { isNot } = this

    const { activeElement } = received.ownerDocument
    const id = activeElement && activeElement.getAttribute('aria-activedescendant')
    const isActiveDescendant = id === received.id

    return {
      pass: isActiveDescendant,
      message: () => `${received} is${isNot ? '' : ' not'} the active descendant`,
    }
  },
})

// Add Snapshot Serializer
expect.addSnapshotSerializer(vueSnapshotSerializer)

beforeAll(() => {
  document.body.insertAdjacentHTML('beforeend', '<div id="teleport-target"></div>')
  document.body.insertAdjacentHTML('beforeend', '<div id="teleport-target-notifications"></div>')
})

beforeEach(() => testQueryClient.clear())

window.matchMedia = vi.fn().mockImplementation(() => {
  return {
    matchMedia: vi.fn(),
    addEventListener: vi.fn(),
    addListener: vi.fn(),
    removeEventListener: vi.fn(),
  }
})

vi.mock('@js/queryClient', () => ({
  default: testQueryClient,
}))

vi.mock('@js/translator', () => {
  return {
    default: {
      trans: vi.fn((id, params) => {
        return params === undefined ? id : `${id} ${JSON.stringify(params)}`
      }),
      transChoice: vi.fn((id, number, params) => {
        return params === undefined ? id : `${id}-${number}-${JSON.stringify(params)}`
      }),
    },
  }
})

vi.mock('@formkit/auto-animate/vue', () => ({
  useAutoAnimate: vi.fn(() => [ref(), vi.fn()]),
}))

vi.mock('@formkit/auto-animate', () => {
  return {
    vAutoAnimate: vi.fn(),
    getTransitionSizes: vi.fn(),
    default: () => ({ autoAnimate: vi.fn(), enable: vi.fn(), disable: vi.fn() }),
  }
})

// Mock CSS imports with ?url suffix for TinyMCE
vi.mock('@css/tinymce.css?url', () => {
  return {
    default: false,
  }
})
vi.mock('@vueuse/head', () => ({
  createHead: vi.fn(),
  useHead: vi.fn(),
}))

/*
 TODO: Replace this global mock with individual mocks of each function where needed.
  At the moment this global mock causes issues where it needs to be explicit to be unmocked in order to test some functionality.
 */
vi.mock('vue-router', () => {
  return {
    onBeforeRouteLeave: () => vi.fn(),
    onBeforeRouteUpdate: () => vi.fn(),
    createRouter: () => ({
      beforeEach: vi.fn(),
      afterEach: vi.fn(),
      push: vi.fn(() => Promise.resolve(true)),
      resolve: () => vi.fn(),
      onError: vi.fn(),
    }),
    useLink: vi.fn(() => ({
      href: 'mylink',
      navigate: vi.fn(),
      isActive: vi.fn(),
      isExactActive: vi.fn(),
    })),
    useRoute: vi.fn(() => ({ query: {}, meta: {} })),
    useRouter: vi.fn(() => ({
      push: vi.fn(() => Promise.resolve(true)),
      resolve: () => vi.fn(),
      addRoute: () => vi.fn(),
      hasRoute: () => vi.fn(),
      removeRoute: () => vi.fn(),
    })),
  }
})

config.global.plugins = [
  ...config.global.plugins,
  [VueQueryPlugin, { queryClient: testQueryClient }],
  [FloatingVue],
]

config.global.stubs = {
  ...config.global.stubs,
  SvgIcon: SvgIconStub,
  RouterLink: defineComponent({
    name: 'RouterLink',
    template: '<a data-stub="RouterLink"><slot></slot></a>',
  }),
}

export default config
