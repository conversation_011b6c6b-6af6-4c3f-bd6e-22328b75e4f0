import { init } from '@sentry/vue'
import { userEvent } from '@testing-library/user-event'
import { waitFor } from '@testing-library/vue'
import { setupServer as mswSetupServer } from 'msw/node'
import sentryTestkit from 'sentry-testkit'
import invariant from 'tiny-invariant'
import { expect } from 'vitest'
import { Suspense, defineComponent, h } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import type { App } from 'vue'
import type { RequestHandler } from 'msw'
import type { RenderResult } from '@testing-library/vue'
import type { UserEvent } from '@testing-library/user-event'
import type { ApiResource, HydraCollectionResponseWithPagination } from '@js/types'

export function createMinimalRouter() {
  return createRouter({
    history: createWebHistory(),
    routes: [
      {
        name: 'AppHome',
        path: '/',
        component: { template: '<div></div>' },
      },
    ],
  })
}

export function wrapInSuspense(
  component: ReturnType<typeof defineComponent>,
  props: object = {}
): ReturnType<typeof defineComponent> {
  return defineComponent({
    render() {
      return h(
        'div',
        { id: 'root' },
        h(Suspense, null, {
          default() {
            return h(component, props)
          },
          fallback: h('div', 'fallback'),
        })
      )
    },
  })
}

/**
 * Wrap msw setupServer to allow global adjustments of the base functionality of the server.
 */
export function setupServer(...handlers: Array<RequestHandler>) {
  return mswSetupServer(...handlers)
}

/**
 * Removes whitespace between HTML elements and trims the string.
 */
export function cleanupWhitespaceInHtmlString(html: string) {
  return html.replace(/\s*>\s*<\s*/g, '><').replace(/\s+(?=<)|(?<=>)\s+/g, '')
}

export function createHydraCollection<T>(
  records: Array<T> = [],
  overrides: Record<string, unknown> = {}
) {
  return {
    '@id': '/api/some-resource',
    'hydra:member': records,
    'hydra:totalItems': records.length,
    ...overrides,
  }
}

export function createPaginatedHydraCollection<T extends ApiResource>(
  items: Array<T>,
  options: {
    page?: number
    itemsPerPage?: number
    totalItems?: number
    basePath: string
  }
): HydraCollectionResponseWithPagination<T> {
  const { page = 1, itemsPerPage = 10, totalItems = items.length, basePath } = options

  const hasNextPage = page * itemsPerPage < totalItems
  const hasPrevPage = page > 1

  return {
    'hydra:member': items,
    'hydra:totalItems': totalItems,
    '@id': basePath,
    'hydra:view': {
      '@id': `${basePath}?page=${page}`,
      '@type': 'hydra:PartialCollectionView',
      'hydra:first': `${basePath}?page=1`,
      'hydra:last': `${basePath}?page=${Math.ceil(totalItems / itemsPerPage)}`,
      ...(hasNextPage && { 'hydra:next': `${basePath}?page=${page + 1}` }),
      ...(hasPrevPage && { 'hydra:previous': `${basePath}?page=${page - 1}` }),
    },
  }
}

export function findResourceById(
  id: unknown,
  records: Array<ApiResource & { id: number | string }>
) {
  const matchedResult = records.find((record) => String(record.id) === id)

  if (!matchedResult) {
    console.error(
      'No return value setup for record with id ' + id + '. Available records:',
      records.map((record) => record['@id'])
    )
  }

  return matchedResult
}

export async function chooseOption(
  ui: RenderResult,
  label: string,
  toSelectOption: string,
  user?: UserEvent,
  isMultiSelect = false
) {
  expect(await ui.findByLabelText(`${label}-combobox`)).toBeInTheDocument()
  const combobox = ui.getByLabelText(`${label}-combobox`) as HTMLInputElement
  if (!user) {
    user = userEvent.setup()
  }

  if (ui.queryByRole('listbox') === null) {
    await user.click(combobox)
    expect(await ui.findByRole('listbox')).toBeInTheDocument()
  }

  const options = ui.getAllByRole('option')

  const desiredOption = options.find((option) => option.textContent?.trim() === toSelectOption)
  if (desiredOption === undefined) {
    throw new Error(
      `Option with value "${toSelectOption}" not found. Available options: ${options
        .map((option) => option.textContent)
        .join(', ')}`
    )
  }

  /*
    Tools like @testing-library/user-event or fireEvent simulate user interactions at the DOM level, bypassing any CSS-based constraints.
    If the element is in the DOM and has an event listener, user.click() will trigger it—even if that element would be visually and interactively
    "blocked" in a real browser due to pointer-events: none
   */
  if (
    desiredOption.hasAttribute('data-disabled') &&
    desiredOption.classList.contains('data-disabled:pointer-events-none')
  ) {
    return
  }

  await user.click(desiredOption)

  if (combobox.value === '' && toSelectOption !== '') {
    expect((combobox.parentNode as HTMLElement).innerHTML).toMatch(new RegExp(`${toSelectOption}`))
  } else {
    expect(combobox.value).toBe(toSelectOption)
  }

  if (isMultiSelect) {
    expect(ui.queryByRole('listbox')).toBeInTheDocument()
    return
  }

  await waitFor(() => {
    expect(ui.queryByRole('listbox')).toBeNull()
  })
}

export function setupSentryTestKit(app: App<Element>) {
  const testkitSentry = sentryTestkit()

  beforeEach(() => {
    /**
     * The init call must be within the `beforeEach` otherwise the tests fail when running more than two.
     * Maybe this is solved when this library supports sentry v8. See: https://github.com/zivl/sentry-testkit/issues/200
     */
    init({
      dsn: 'https://<EMAIL>/001',
      transport: testkitSentry.sentryTransport,
      integrations: [],
      app,
    })
    testkitSentry.testkit.reset()
  })

  return testkitSentry
}

/**
 * Dispatches scroll event when scrollTop value changes.
 * JSDOM doesn't support scrolling natively.
 * See: https://github.com/jsdom/jsdom/issues/2751
 */
export function enableScroll(element: HTMLElement) {
  let currentScrollTop = element.scrollTop
  Object.defineProperty(element, 'scrollTop', {
    configurable: true,
    get() {
      return currentScrollTop
    },
    set(newValue) {
      currentScrollTop = newValue
      element.dispatchEvent(new Event('scroll'))
    },
  })
}

/**
 * Mocks getBoundingClientRect for all elements
 * JSDOM doesn't implement this.
 * See: https://github.com/jsdom/jsdom/issues/3729
 */
export function mockGetBoundingClientRect(dimensions: Partial<DOMRect> = {}) {
  const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect

  const defaultRect: DOMRect = {
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: 0,
    height: 0,
    toJSON: vi.fn(),
  }

  const mock = vi.fn(() => ({ ...defaultRect, ...dimensions }))
  Element.prototype.getBoundingClientRect = mock

  return {
    mock,
    cleanup: function () {
      Element.prototype.getBoundingClientRect = originalGetBoundingClientRect
    },
  }
}

/**
 * Mocks offsetHeight for all elements
 * JSDOM doesn't implement this.
 * See: https://github.com/jsdom/jsdom/issues/135
 */
export function mockOffsetHeight(height: number) {
  const originalOffsetHeight = Object.getOwnPropertyDescriptor(
    HTMLElement.prototype,
    'offsetHeight'
  )

  Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
    configurable: true,
    get() {
      return height
    },
  })

  return {
    cleanup: function () {
      if (originalOffsetHeight) {
        Object.defineProperty(HTMLElement.prototype, 'offsetHeight', originalOffsetHeight)
      }
    },
  }
}

export function ensureDefined<T>(value: T | undefined): T {
  expect(value).toBeDefined()
  invariant(value !== undefined, 'Value is undefined')
  return value
}
