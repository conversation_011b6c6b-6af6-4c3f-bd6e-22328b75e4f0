import { resolve } from 'path'
import tailwindcss from '@tailwindcss/vite'
import { defineConfig } from 'vite'
import viteSvgLoader from 'vite-svg-loader'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  define: {
    'process.env.VERSION': JSON.stringify(process.env.VERSION || 'dev'),
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
  },
  plugins: [
    tailwindcss(),
    vue({ template: { compilerOptions: { whitespace: 'preserve' } } }),
    viteSvgLoader({
      defaultImport: 'component',
    }),
  ],
  esbuild: {
    target: 'esnext',
  },
  build: {
    manifest: true,
    emptyOutDir: true,
    outDir: 'public/build/',
    sourcemap: true,
    target: 'esnext', // To support top-level await
  },
  server: {
    strictPort: true,
    host: '0.0.0.0',
    port: 3000,
    hmr: {
      clientPort: 443,
    },
    // https: {
    //   key: fs.readFileSync(resolve(__dirname, '../certs/domain.key')),
    //   cert: fs.readFileSync(resolve(__dirname, '../certs/domain.crt')),
    // },
    cors: true,
    allowedHosts: ['.localhost', '.u2.web'],
  },
  optimizeDeps: {
    // ensure vite pre-bundles mime-types so the alias is applied
    include: ['mime-types'],
  },
  resolve: {
    alias: {
      '@js': resolve(__dirname, './src'),
      '@css': resolve(__dirname, './css'),
      '@icons': resolve(__dirname, './assets/icons'),
      '@assets': resolve(__dirname, './assets'),
      // This  is needed for compile function to work
      vue: 'vue/dist/vue.esm-bundler.js',
    },
  },
})
