.js-sortable-drag-handler {
  cursor: theme('cursor.grab');
}

.relative:has(.sortable-ghost) {
  cursor: grabbing;
}

.sortable-chosen {
  cursor: grabbing;
}

.sortable-chosen .js-sortable-drag-handler {
  cursor: grabbing;
}

.sortable-ghost {
  background: theme('backgroundColor.gray.50');
  border: theme('borderWidth.2') theme('borderColor.gray.300') dashed;
  border-radius: var(--radius-md);
  color: transparent;
  display: inline-block;

  span,
  svg,
  div,
  button {
    color: transparent;
  }
}
