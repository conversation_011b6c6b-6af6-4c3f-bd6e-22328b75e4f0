/* Global variables */
:root {
  --scrollbar-width: 0px;
  --app-header-height: 50px; /* logo height (34px) + top padding (8px) + bottom padding (8px) */
  --main-content-padding: --spacing(3.5);

  /* Colors */
  --app-color-off-black: var(--color-gray-950);

  @media print {
    --app-color-off-black: var(--color-black);
  }

  /* Media Queries */
  --minimum-width-for-full-menu: var(--breakpoint-sm);

  /* Menu */
  --main-menu-item-width: theme('width.64');

  /* Form */
  --app-input-border-width: theme('borderWidth.DEFAULT');
  --app-input-color-border: theme('borderColor.gray.300');
  --app-input-color-border-hover: theme('borderColor.gray.400');
  --app-input-color-border-focus: theme('borderColor.blue.600');
  --app-input-color-border-disabled: theme('borderColor.gray.300');
  --app-input-background-color-disabled: var(--color-gray-100);
  --app-input-color-ring: theme('borderColor.blue.600');
  --app-field-color-help-icon: var(--color-gray-500);
  --app-field-color-error: var(--color-bad);
  --app-field-color-label: var(--color-gray-600);
  --app-input-border-radius: var(--radius-sm);
  --app-input-shadow: var(--shadow-xs);
  --app-form-field-spacing: --spacing(4);

  /* Focus and outline styling */
  --app-outline-width: theme('outlineWidth.2');
  --app-outline-offset: theme('outlineOffset.2');
  --app-ring-width: theme('ringWidth.2');
  --app-ring-offset-width: theme('ringOffsetWidth.2');

  /* Common spacing values for form elements */
  --app-input-padding-y: --spacing(2);
  --app-input-padding-x: --spacing(3);
  --app-select-arrow-size: --spacing(6);
  --app-select-arrow-spacing: --spacing(2);
  --app-select-padding-right: --spacing(10);
  --app-textarea-height: --spacing(14);
}
