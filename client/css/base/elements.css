/**
 * `BOX-SIZING: BORDER-<PERSON>OX` FOR ALL THE ELEMENTS - set in tailwind css preflight
 * Instead of adding the border and padding to the content width, when we define now a width the border and padding will be added inside the total width, they don't make the element bigger:
 * `width: 100px` + `padding: 10px` = 100px final width (before, with `content-box`, it would have been 120px).
 * Allows us to have a relative (%) width with fixed padding and border.
 * Defining it in `html` and then inheriting so it’s easy to override for specific blocks/modules if needed (http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/).
 */
html {
  font-size: 100%;

  /* Prevent IE text size adjust after orientation change, without disabling user zoom */
  text-size-adjust: 100%;
}

body {
  background: var(--color-white);
  color: var(
    --app-color-off-black
  ); /* Not quite black to reduce eye strain: https://uxplanet.org/basicdesign-never-use-pure-black-in-typography-36138a3327a6 */

  height: 100%;
  margin: 0;
}

hr {
  border: 0;
  border-top: 1px solid var(--color-gray-400);
  margin: 5px 0;
  width: 100%;
}

img {
  /* Tailwind uses middle instead of baseline */
  vertical-align: baseline;
}
