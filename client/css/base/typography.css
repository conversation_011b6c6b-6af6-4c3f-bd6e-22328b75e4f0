/* Scale for title sizes: Major Second (1.125) */

body {
  font-family: var(--font-sans);
  font-size: var(--text-base);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: bold;

  /* Fix the kerning for headings */
  text-rendering: optimizelegibility;

  label {
    font-size: 1em;
    font-weight: bold;
  }
}

h1 {
  color: var(--color-u2);
  font-size: var(--text-4xl);
  margin: 5px 0 10px;

  .entity-id {
    color: var(--color-gray-700);

    span {
      color: var(--color-gray-400);
    }
  }
}

h2 {
  color: var(--color-gray-700);
  font-size: var(--text-3xl);
}

h3 {
  color: var(--color-gray-700);
  font-size: var(--text-2xl);
}

h4 {
  color: var(--color-gray-500);
  font-size: var(--text-xl);
}

h5 {
  color: var(--color-gray-700);
  font-size: var(--text-xl);
}

h6 {
  color: var(--color-gray-400);
  font-size: var(--text-xl);
  text-transform: uppercase;
}

p {
  line-height: var(--leading-normal);
  margin: 0 0 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

address {
  line-height: var(--leading-tight);
  margin: 0 0 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

ol,
ul {
  margin-bottom: 10px;
  margin-top: 0;
  padding-left: 2em;

  &:last-child {
    margin-bottom: 0;
  }
}

ol {
  list-style-type: decimal;
}

ul {
  list-style: disc;

  ul {
    list-style: circle;

    ul {
      list-style: square;
    }
  }

  nav & {
    list-style: none;
    margin: 0;
    padding: 0;
  }
}

li {
  line-height: var(--leading-loose);

  .compact-list & {
    line-height: var(--leading-tight);
  }
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome.
 */
b,
strong {
  font-weight: bold;
}

em,
i {
  font-style: italic;
}

a {
  color: var(--color-action);
  cursor: pointer;
  text-decoration: none;
  transition:
    background 0.3s ease,
    color 0.3s ease;

  &:hover,
  &:focus {
    color: var(--color-action-darker);
    text-decoration: underline;
  }

  &:active {
    color: var(--color-action-lighter);
  }

  &:hover,
  &:active {
    outline: 0;
  }

  &.no-border {
    &:hover,
    &:focus {
      text-decoration: none;
    }
  }

  &.not-colored {
    color: inherit;

    &:hover {
      color: var(--color-action-darker);
    }
  }
}

small {
  font-size: var(--text-sm);
}

code,
kbd {
  background: var(--color-gray-100);
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  padding: 2px 4px;

  pre & {
    font-size: var(--text-sm);
  }
}

kbd {
  background: var(--color-blue-100);
}

/**
 * Address styling not present in IE 8/9, Safari 5, and Chrome.
 */
abbr {
  &[title] {
    border-bottom: 1px dotted var(--color-gray-400);
    cursor: help;
  }
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/**
 * TEXT VALIDATION STATE
 */

.text-error {
  color: var(--color-bad);

  a&:hover,
  a&:focus {
    color: var(--color-bad-darker);
  }
}

.text-info {
  color: var(--color-action);

  a&:hover,
  a&:focus {
    color: var(--color-action-darker);
  }
}

.text-success {
  color: var(--color-good);

  a&:hover,
  a&:focus {
    color: var(--color-good-darker);
  }
}

.text-warning {
  color: var(--color-alert);

  a&:hover,
  a&:focus {
    color: var(--color-alert-darker);
  }
}

@media print {
  body {
    font-size: 9pt !important;
  }

  a {
    /* In print, show the URL for the link next to the link */
    &[href]::after {
      content: ' (' attr(href) ')';
      font-size: 1em !important;
      word-break: normal;
    }

    /* Do not show the URL if is not an actual URL but just a JavaScript trigger, placeholder or button */
    &[href^='javascript:']::after,
    &[href^='#']::after,
    &.button::after,
    table &::after {
      /* Do not show the URL if is inside a table */
      content: '' !important;
    }
  }

  abbr[title]::after {
    content: ' (' attr(title) ')';
  }

  small {
    font-size: 7pt !important;
  }

  code {
    font-size: 8pt !important;

    pre & {
      background-color: transparent !important;
      font-size: 8pt !important;
    }
  }

  pre {
    background-color: transparent !important;
  }
}
