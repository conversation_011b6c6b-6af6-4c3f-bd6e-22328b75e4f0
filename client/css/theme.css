@theme {
  --text-*: initial;
  --text-xs: 0.625rem;
  --text-xs--line-height: 1.125rem;
  --text-sm: 0.75rem;
  --text-sm--line-height: 1rem;
  --text-base: 0.875rem;
  --text-base--line-height: 1.25rem;
  --text-lg: 1rem;
  --text-lg--line-height: 1.5rem;
  --text-xl: 1.125rem;
  --text-xl--line-height: 1.75rem;
  --text-2xl: 1.25rem;
  --text-2xl--line-height: 1.75rem;
  --text-3xl: 1.5rem;
  --text-3xl--line-height: 2rem;
  --text-4xl: 1.875rem;
  --text-4xl--line-height: 2.25rem;
  --text-5xl: 2.25rem;
  --text-5xl--line-height: 2.5rem;
  --text-6xl: 3rem;
  --text-6xl--line-height: 1;
  --text-7xl: 3.75rem;
  --text-7xl--line-height: 1;
  --text-8xl: 4.5rem;
  --text-8xl--line-height: 1;
  --text-9xl: 6rem;
  --text-9xl--line-height: 1;
  --shadow-skin-base: var(--app-input-shadow);
  --color-off-black: var(--app-color-off-black);
  --color-orange-50: #fff7ed;
  --color-orange-100: #ffedd5;
  --color-orange-200: #fed7aa;
  --color-orange-300: #fdba74;
  --color-orange-400: #fb923c;
  --color-orange-500: #f97316;
  --color-orange-600: #ea580c;
  --color-orange-700: #c2410c;
  --color-orange-800: #9a3412;
  --color-orange-900: #7c2d12;
  --color-orange-950: #431407;
  --color-action: hsl(205deg 75% 50%);
  --color-action-darker: hsl(205deg 75% 40%);
  --color-action-lighter: hsl(205deg 75% 55%);
  --color-action-transparent: hsl(205deg 75% 40% / 12%);
  --color-alert: hsl(35deg 100% 50%);
  --color-alert-darker: hsl(35deg 100% 40%);
  --color-alert-lighter: hsl(35deg 100% 55%);
  --color-alert-transparent: hsl(35deg 100% 40% / 12%);
  --color-bad: hsl(355deg 100% 40%);
  --color-bad-darker: hsl(355deg 100% 30%);
  --color-bad-lighter: hsl(355deg 100% 45%);
  --color-bad-transparent: hsl(355deg 100% 30% / 12%);
  --color-good: hsl(150deg 85% 40%);
  --color-good-darker: hsl(150deg 85% 30%);
  --color-good-lighter: hsl(150deg 85% 45%);
  --color-good-transparent: hsl(150deg 85% 30% / 12%);
  --color-u2: #d22630;
  --color-u2-inverse: #165c7d;
  --color-u2-inverse-darker: #0e3c52;
  --color-u2-inverse-lighter: #1a6c93;
  --color-u2-inverse-transparent: hsl(199deg 71% 19% / 12%);
  --color-u2-darker: #a71e26;
  --color-u2-lighter: #db3740;
  --color-u2-transparent: hsl(356deg 70% 39% / 12%);
  --color-transparent-black: hsl(0deg 0% 0% / 12%);
  --color-transparent-white-80: hsl(0deg 0% 100% / 80%);
  --color-transparent-white-40: hsl(0deg 0% 100% / 40%);
  --color-transparent-white-20: hsl(0deg 0% 100% / 20%);
  --color-transparent-white-10: hsl(0deg 0% 100% / 10%);
  --color-transparent-white-25: hsl(0deg 0% 100% / 25%);
  --container-xxs: 16rem;
  --width-xl: 36rem;
  --width-2xl: 42rem;
  --width-3xl: 48rem;
  --min-height-120: 30rem;
  --background-color-skin-disabled: var(--app-input-background-color-disabled);
  --radius-skin-base: var(--app-input-border-radius);
  --border-width-skin-base-width: var(--app-input-border-width);
  --border-color-skin-base: var(--app-input-color-border);
  --border-color-skin-hover: var(--app-input-color-border-hover);
  --border-color-skin-focus: var(--app-input-color-border-focus);
  --border-color-skin-disabled: var(--app-input-color-border-disabled);
  --ring-color-skin-base: var(--app-input-color-ring);
  --text-color-skin-help-icon: var(--app-field-color-help-icon);
  --text-color-skin-form-errors: var(--app-field-color-error);
  --text-color-skin-label: var(--app-field-color-label);
}
