/* The padding around the content of the document sections so that we can see the cursor and edges of widgets */
:root {
  --document-section-padding: --spacing(1);
}

.document-section-view-mode-padding {
  padding-left: var(--document-section-padding);
  padding-right: var(--document-section-padding);
}

.document-section-content {
  ul {
    /* we override the padding to match the PDF as closely as possible */
    padding-left: 3.35em;
  }

  li {
    line-height: var(--leading-normal);
  }

  &.excluded img {
    filter: grayscale(0.75);
    opacity: 0.25;
  }

  [class*='icon-'] {
    font-size: var(--text-base);
  }
}

.document-section-content-editor {
  margin: var(--document-section-padding);

  /*
  When a widget placeholder is selected tinymce adds a bogus element or a visual caret element that are rendered at the end of the editor.
  Paragraphs get a bottom margin whenever there is something after them. This causes the height of the editor to grow and the content jumps.
  Setting the margin to 0 prevents the "jump"
  */
  p:has(+ .mce-offscreen-selection),
  p:has(+ .mce-visual-caret) {
    margin-bottom: 0;
  }
}
