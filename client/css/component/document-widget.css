.page-break-widget-container {
  align-items: center;
  display: flex;
  margin: --spacing(1) 0;
  min-height: --spacing(4);
}

.page-break-widget {
  border: 0;
  border-top: 1px dashed var(--color-gray-200);
  height: 0;
  margin: 0;
  overflow: visible;
  position: relative;
  width: 100%;

  &::after {
    background-color: var(--color-white);
    color: var(--color-gray-400);
    content: attr(data-page-break-text);
    display: inline-block;
    font-size: var(--text-xs);
    font-weight: bold;
    margin: 0 10px;
    padding: 0 5px;
    position: absolute;
    right: 50%;
    text-transform: uppercase;
    top: 50%;
    transform: translate(50%, -50%);
  }
}

.units-document-widget-block-item {
  border-left: 2px solid var(--color-gray-200);
  padding-left: --spacing(1.5);

  em {
    color: var(--color-gray-400);
    font-style: normal;
  }
}

@media print {
  .page-break-widget {
    break-after: always;
    visibility: hidden;
  }
}
