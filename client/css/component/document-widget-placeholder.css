.document-widget-placeholder-prefix {
  color: var(--color-gray-400);
  margin-right: 5px;
}

[data-document-widget] {
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-sm);
  color: var(--color-gray-700);
  display: flex;
  font-weight: bold;
  margin: 10px 0;
  padding: 15px 20px;
  text-transform: capitalize;
}

[data-image-document-widget] {
  display: inline-flex;

  > img {
    height: auto;
    vertical-align: bottom;
  }

  &:hover > [data-file-document-widget-edit] {
    background-color: var(--color-transparent-white-80);
  }

  &.image-not-attached,
  &.image-is-restricted {
    background: var(--color-gray-100);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-sm);
    padding-left: --spacing(1);
    padding-right: --spacing(1);

    > [data-image-document-widget-edit] {
      margin-left: --spacing(1);
      padding: 0;
      position: relative;
    }
  }
}

[data-inline-document-widget] {
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-sm);
  display: inline-block;
  padding: 0 3px;
}

[data-document-widget-settings] {
  display: none;
}

[data-document-widget-edit],
[data-image-document-widget-edit] {
  color: var(--color-gray-400);
  cursor: pointer;
  display: inline-block;
  margin-left: auto;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
  vertical-align: baseline;

  &:hover {
    color: var(--color-gray-700);
    cursor: pointer;
  }
}

[data-image-document-widget-edit] {
  background-color: var(--color-transparent-white-40);
  border-bottom-left-radius: var(--radius-sm);
  color: var(--color-gray-700);
  height: --spacing(4);
  margin-left: calc(-1 * (theme('width.5')));
  padding: --spacing(1) --spacing(0.5);
  vertical-align: top;
  width: theme('width.4');
}

[data-inline-document-widget] > [data-document-widget-edit] {
  margin-left: 3px;
}

.new-section-placeholder {
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-sm);
  color: var(--color-gray-700);
  display: flex;
  font-weight: var(--font-weight-medium);
  height: --spacing(52);
  padding-left: --spacing(1);
  padding-right: --spacing(1);
  place-items: center center;

  span {
    flex: auto;
    letter-spacing: var(--tracking-wide);
    text-align: center;
  }
}
