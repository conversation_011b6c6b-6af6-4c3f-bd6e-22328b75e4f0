table {
  border-collapse: collapse;
  border-spacing: 0;
  line-height: var(--leading-normal);
  padding: 0;

  td,
  th {
    padding: --spacing(1.5) 10px;
    vertical-align: middle;
  }

  th {
    font-weight: bold;
    text-align: center;
  }

  tbody tr {
    &.selected {
      background-color: var(--color-blue-100);
    }

    &.invalid {
      background-color: var(--color-red-200);
    }
  }
}

@media print {
  table {
    break-inside: auto;
    min-width: 100% !important;
  }

  tr {
    break-after: auto;
    break-inside: avoid;
  }
}
