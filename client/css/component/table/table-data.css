/* Basic table data style definitions */

.table-data-text {
  text-align: left;
}

.table-data-number,
.table-data-money,
.table-data-money-full,
.table-data-percentage {
  font-family: var(--font-mono);
  text-align: right;
  white-space: nowrap;
}

.table-data-other,
.table-data-review,
.table-data-document {
  text-align: center;
}

/* Specific cell table data definitions */

.table-data-action {
  text-align: center;
  white-space: nowrap;
  width: 1px;
}

.table-data-checkbox {
  text-align: center;
  width: 15px;
}

.table-data-count {
  text-align: center;
  white-space: nowrap;
  width: 1px;

  .badge {
    font-weight: normal;
  }
}

.table-data-currency {
  text-align: left;
}

.table-data-date,
.table-data-datetime {
  text-align: left;
  white-space: nowrap;
}

.table-data-description {
  text-align: left;
}

.table-data-email {
  text-align: left;
  white-space: nowrap;
}

.table-data-icon {
  text-align: center;
  white-space: nowrap;
  width: 1px;
}

.table-data-id {
  text-align: center;
  white-space: nowrap;
  width: 1px;
}

.table-data-source-id {
  text-align: center;
  white-space: nowrap;
  width: 1px;
}

.table-data-input {
  text-align: left;

  input[type='color'],
  input[type='date'],
  input[type='datetime'],
  input[type='datetime-local'],
  input[type='email'],
  input[type='month'],
  input[type='number'],
  input[type='password'],
  input[type='search'],
  input[type='tel'],
  input[type='text'],
  input[type='time'],
  input[type='url'],
  input[type='week'],
  select {
    max-width: none;
    width: 100%;
  }
}

.table-data-name {
  font-weight: bold;
  text-align: left;
}

.table-data-status {
  text-align: center;
  white-space: nowrap;
  width: 1px;
}

.table-data-time {
  text-align: right;
  white-space: nowrap;
}

.table-data-user {
  text-align: left;
}

.table-data-workflow-status {
  text-align: center;
  white-space: nowrap;
  width: 1px;
}

@media print {
  .table-data-action .button,
  .table-data-checkbox input {
    display: none !important;
  }
}
