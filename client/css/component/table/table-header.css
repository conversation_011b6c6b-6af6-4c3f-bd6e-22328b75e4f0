.table-header-sortable {
  cursor: pointer;
}

.table-header-sorted-asc {
  &::after {
    background-color: var(--color-off-black);
    content: '';
    display: inline-block;
    font-size: 1em;
    height: 1em;
    margin-left: --spacing(1);
    mask-image: url('@icons/arrow-up.svg?url');
    mask-size: cover;
    vertical-align: middle;
    width: 1em;
  }
}

.table-header-sorted-desc {
  &::after {
    background-color: var(--color-off-black);
    content: '';
    display: inline-block;
    font-size: 1em;
    height: 1em;
    margin-left: --spacing(1);
    mask-image: url('@icons/arrow-down.svg?url');
    mask-size: cover;
    vertical-align: middle;
    width: 1em;
  }
}

/* Basic table data head alignment */

.table-head-text {
  text-align: left;
}

.table-head-number,
.table-head-percentage,
.table-head-right-aligned,
.table-head-money,
.table-head-time {
  text-align: right;
}

/* Specific cell table data head alignment */
.table-head-other,
.table-head-action,
.table-head-files,
.table-head-boolean,
.table-head-checkbox,
.table-head-document,
.table-head-icon,
.table-head-id,
.table-head-review,
.table-head-status,
.table-head-workflow-status {
  text-align: center;
}

.table-head-currency,
.table-head-date,
.table-head-datetime,
.table-head-description,
.table-head-email,
.table-head-input,
.table-head-name,
.table-head-user {
  text-align: left;
}
