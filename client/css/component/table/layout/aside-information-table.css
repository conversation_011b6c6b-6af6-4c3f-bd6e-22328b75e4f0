.aside-information-table {
  font-size: var(--text-base);
  margin: 0;
  width: 100%;

  [class*='icon-']::before {
    white-space: nowrap;
  }

  td,
  th {
    padding: 5px;

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }

  td {
    padding-left: --spacing(6);
  }

  tbody tr:last-of-type {
    td,
    th {
      padding-bottom: 0;
    }
  }

  .table-data-name {
    padding-right: 10px;
    white-space: nowrap;
    width: 1px;
  }

  .table-data-workflow-status {
    > .status {
      background-color: var(--color-white);
    }
  }

  code {
    background-color: var(--color-white);
  }

  + p {
    margin-top: 10px;
  }
}
