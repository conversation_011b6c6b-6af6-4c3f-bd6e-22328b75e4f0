.data-table {
  thead tr {
    border-bottom: 1px solid var(--color-gray-300);
  }

  tbody tr {
    &:not(:first-child) {
      border-top: 1px solid var(--color-gray-200);
    }

    background-color: var(--color-white);

    @apply transition-colors;

    &:hover {
      background-color: var(--color-orange-200);
    }

    &.selected:hover {
      background-color: var(--color-blue-200);
    }

    &.invalid:hover {
      background-color: var(--color-red-300);
    }
  }

  tfoot tr {
    border-top: 1px solid var(--color-gray-300);
  }

  .no-border {
    border: none;
  }

  td,
  th {
    padding: 3px 10px;
  }

  thead {
    th,
    td {
      padding-bottom: --spacing(1.5);
      padding-top: --spacing(1.5);
    }
  }

  thead th {
    background-color: var(--color-gray-100);
  }

  tfoot {
    td,
    th {
      background-color: var(--color-gray-100);
    }
  }

  margin: 0;
  white-space: nowrap;

  input[type='color'],
  input[type='date'],
  input[type='datetime'],
  input[type='datetime-local'],
  input[type='email'],
  input[type='month'],
  input[type='number'],
  input[type='password'],
  input[type='search'],
  input[type='tel'],
  input[type='text'],
  input[type='time'],
  input[type='url'],
  input[type='week'],
  select {
    max-width: none;
    width: 100%;
  }
}

@media print {
  .data-table {
    white-space: normal;

    tfoot {
      td,
      th {
        font-size: 7pt !important;
      }
    }
  }
}
