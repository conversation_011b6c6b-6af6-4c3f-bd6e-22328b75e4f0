.bordered-table {
  &.out {
    border: 1px solid var(--color-gray-200);
  }

  &.horizontal {
    thead tr {
      border-bottom: 1px solid var(--color-gray-300);
    }

    tbody tr {
      &:not(:first-child) {
        border-top: 1px solid var(--color-gray-200);
      }
    }

    tfoot tr {
      border-top: 1px solid var(--color-gray-300);
    }

    .no-border {
      border: none;
    }
  }

  &.vertical {
    td {
      + td {
        border-left: 1px solid var(--color-gray-200);
      }
    }

    th {
      + th {
        border-left: 1px solid var(--color-gray-300);
      }
    }

    thead,
    tfoot {
      td + td {
        border-left: 1px solid var(--color-gray-300);
      }
    }

    .no-border {
      th,
      td {
        border: none;
      }
    }
  }
}
