.icon-calendar::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/calendar.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-calendar:hover::before {
  background-color: var(--color-action-darker);
}

.icon-delete::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1.25em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/delete.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-filter::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/filter.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-help::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/help.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-info::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/info.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-legal-unit::before {
  background: var(--color-gray-400) !important;
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/legal-unit.svg?url');
  mask-size: cover;
  width: 1em;
}

.icon-link::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/link.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-list::before {
  background-color: var(--color-action);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/help.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-lock-closed::before {
  background-color: var(--color-off-black);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/lock-closed.svg?url');
  mask-size: cover;
  vertical-align: baseline;
  width: 1em;
}

.icon-lock-closed-bad::before {
  background-color: var(--color-bad);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  mask-image: url('@icons/lock-closed.svg?url');
  mask-size: cover;
  vertical-align: baseline;
  width: 1em;
}

.icon-mail::before {
  background-color: var(--color-white);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/mail.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}

.icon-organisational-group::before {
  background: var(--color-gray-400) !important;
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/organisational-group.svg?url');
  mask-size: cover;
  width: 1em;
}

.icon-permanent-establishment::before {
  background: var(--color-gray-400) !important;
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/permanent-establishment.svg?url');
  mask-size: cover;
  width: 1em;
}

.icon-save-alt::before {
  background-color: var(--color-off-black);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/save-alt.svg?url');
  mask-size: cover;
  vertical-align: text-top;
  width: 1em;
}

.icon-user::before {
  background-color: var(--color-white);
  content: '';
  display: inline-block;
  font-size: 1em;
  height: 1em;
  margin-right: --spacing(1);
  mask-image: url('@icons/user.svg?url');
  mask-size: cover;
  vertical-align: middle;
  width: 1em;
}
