.tax-accounting-table {
  height: 100%;
  margin: 0;
  white-space: nowrap;

  th {
    border: theme('borderWidth.DEFAULT') solid theme('borderColor.gray.300');
  }

  tbody tr {
    background-color: var(--color-white);

    &:hover {
      background-color: var(--color-orange-200);
    }

    &.selected:hover {
      background-color: var(--color-blue-200);
    }

    &.invalid:hover {
      background-color: var(--color-red-300);
    }
  }

  tr:hover [disabled]:not([type='checkbox'], [type='radio']),
  tr:hover .bg-skin-disabled:not([type='checkbox']),
  tr input:not([disabled], [type='checkbox'], [type='radio']),
  tr:hover textarea {
    background: transparent;
    transition: none;
  }

  td {
    border: theme('borderWidth.DEFAULT') solid theme('borderColor.gray.200');
    height: theme('height.full');
    padding: theme('padding.2') theme('padding.3');
  }

  td:first-child {
    color: var(--color-gray-800);
    font-size: var(--text-base);
  }

  thead th {
    background-color: var(--color-gray-200);
    border: 1px solid var(--color-gray-300);
    text-align: center;
    white-space: normal;
    z-index: 2;

    &:empty {
      background-color: transparent;
      border-style: none;
    }
  }

  .table-data-input {
    padding: 0;
    text-align: right;

    .form-widget-item-value,
    .form-widget-item-value > div,
    .app-input-number {
      height: 100%;
      width: 100%;
    }

    .app-input-percent {
      height: 100%;
      width: 100%;
    }

    .app-input-number input {
      height: theme('height.full');
      min-width: theme('minWidth.full');
      width: theme('width.auto');
    }
  }

  .colored-row {
    input {
      background: transparent;
    }

    &.posting {
      background-color: var(--color-orange-100);

      &:hover {
        background-color: var(--color-orange-200);
      }
    }

    &.sum {
      .bg-skin-disabled {
        background-color: var(--color-blue-100);
      }

      background-color: var(--color-blue-100);

      &:hover {
        .bg-skin-disabled {
          background-color: var(--color-blue-200);
        }

        background-color: var(--color-blue-200);
      }
    }

    + .colored-row {
      border-top: 1px solid var(--color-white);
    }
  }

  @media (min-width: theme(--breakpoint-sm)) {
    thead th {
      position: sticky;
      top: var(--sticky-header-height);

      /* Adding pseudo element with gray border to mimic the sticky th border in Safari */

      &:not(:empty)::before {
        border: 1px solid var(--color-gray-300);
        content: '';
        height: calc(100% + 2px);
        left: -1px;
        position: absolute;
        top: -1px;
        width: calc(100% + 2px);
        z-index: -1;
      }
    }
  }

  @media print {
    thead th {
      background-color: transparent;
      position: static;

      &:not(:empty)::before {
        display: none;
      }
    }
  }
}
