.list-group {
  border-color: theme('borderColor.blue.200');
  border-radius: var(--radius-sm);
  border-width: theme('borderWidth.DEFAULT');
  list-style-type: none;
  overflow: hidden;
  padding: 0;
}

.list-group-item {
  background-color: var(--color-blue-100);
  border-color: theme('borderColor.blue.200');
  border-top-width: theme('borderWidth.DEFAULT');
  display: block;
  padding: --spacing(2) --spacing(3);

  &:first-child {
    border-top: 0;
  }

  &:hover,
  &:focus {
    background-color: var(--color-indigo-100);
    color: var(--color-action-darker);
    text-decoration: none;
  }
}
