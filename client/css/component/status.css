.status {
  background: var(--color-gray-200);
  border-color: var(--color-gray-300);
  border-radius: var(--radius-sm);
  border-width: theme('borderWidth.DEFAULT');
  color: var(--color-gray-400);
  font-size: var(--text-xs);
  font-weight: bold;
  line-height: theme('lineHeight.none');
  padding: --spacing(0.5) --spacing(1);
  text-transform: uppercase;
  white-space: nowrap;

  &.open {
    background: var(--color-blue-200);
    border-color: var(--color-blue-300);
    color: var(--color-blue-800);
  }

  &.in-progress {
    background: var(--color-orange-200);
    border-color: var(--color-orange-300);
    color: var(--color-orange-800);
  }

  &.complete {
    background: var(--color-green-300);
    border-color: var(--color-green-400);
    color: var(--color-green-900);
  }
}
