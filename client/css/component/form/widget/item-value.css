.form-widget-item-value {
  cursor: pointer;
  position: relative;
  z-index: 0;

  input,
  .add-on {
    cursor: pointer;
    font-family: var(--font-mono);
    font-size: var(--text-base);
    line-height: var(--leading-normal);
  }

  /* TODO: Replace add-on class with utility classes or components */
  .add-on {
    height: 20px;
  }

  input {
    text-align: right;

    &[type='checkbox'] {
      margin-right: 4px;
    }
  }

  .tax-accounting-table & {
    cursor: default;

    &:not(.form-widget-checkbox-item-value) {
      display: block;
      line-height: 0;
      min-width: 100px;
      width: 100%;
    }
  }
}
