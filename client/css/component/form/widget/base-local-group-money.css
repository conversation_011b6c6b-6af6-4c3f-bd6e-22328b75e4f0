.form-widget-blg-money-fields {
  align-items: center;
  display: flex;
  flex-direction: row;
  padding-bottom: 0;

  + .form-widget-blg-money-field-errors {
    border-top: none;
    padding-bottom: 0;
  }
}

.form-widget-blg-money-widget-wrapper {
  flex: 0 0 auto;
  max-width: calc(100% - 30px);
}

.form-widget-blg-money-extra {
  color: var(--color-gray-700);

  .table-data-input {
    width: 100%;
  }
}

@media (width <= 620px), (width >= 780px) and (width <= 900px) {
  .form-widget-blg-money-extra {
    tr {
      display: flex;
      flex-direction: column;
      float: left;
      width: 50%;
    }

    td {
      text-align: left;
    }

    .form-widget.with-prepend {
      flex-wrap: wrap;

      .add-on {
        border-bottom: none;
        border-bottom-left-radius: 0;
        border-right: 1px solid var(--color-gray-300);
        border-top-right-radius: var(--radius-sm);
      }

      input {
        border-bottom-left-radius: var(--radius-sm);
        width: calc(100% - 20px);
      }
    }
  }
}
