# Misc
.DEFAULT_GOAL = help
.PHONY: init help build build-deployer certs githooks git-lfs deps composer phpstan phpstan-baseline reset dump-initial-db trans-update \
        route-update pnpm analyze-bundle api-docs hadolint storybook-local static-analysis static-analysis-api static-analysis-client csfixer

# Variables
TENANT ?= dev

## —— The Universal Units App Makefile ——
help: ## Outputs this help screen
	@grep -E '(^[a-zA-Z0-9\./_-]+:.*?##.*$$)|(^##)' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}{printf "\033[32m%-30s\033[0m %s\n", $$1, $$2}' | sed -e 's/\[32m##/[33m/'

init: githooks git-lfs build deps reset ## Initialise the project

build: ## Build the project
	./develop build --pull
	./develop up -d

build-deployer: ## Build u2 deployer image
	cd .; \
  docker build -f deploy-container.dockerfile --pull -t u2deployer .

certs: ## Generate SSL certificates that can be used for HTTPS
	api/scripts/dev/gen-ssl-cert.sh
	api/scripts/dev/gen-jwt.sh

githooks: ## Install the githooks
	git config core.hooksPath .githooks

git-lfs:
	git-lfs pull

deps: composer pnpm ## Install dependencies

composer: ## Install php dependencies
	./develop composer install

pnpm: ## Install pnpm dependencies
	./develop pnpm install; \
  cd client; \
  pnpm install

reset: ## Reset the data and cache for a tenant
	./develop console doctrine:database:drop --if-exists --force --no-interaction --tenant=${TENANT}
	./develop console u2:clear-data --force --tenant=${TENANT}
	./develop console cache:pool:clear cache.app
	./develop console cache:pool:clear tenant.cache --tenant=${TENANT}
	./develop console doctrine:database:create --if-not-exists --no-interaction --tenant=${TENANT}
	./develop console u2:setup --demo --no-interaction --tenant=${TENANT}

reset-test: ## Reset the data and cache for a tenant
	./develop console doctrine:database:drop --if-exists --force --no-interaction --tenant=test
	./develop console u2:clear-data --force --tenant=test
	./develop console cache:pool:clear cache.app
	./develop console cache:pool:clear tenant.cache --tenant=test
	./develop console doctrine:database:create --if-not-exists --no-interaction --tenant=test
	./develop console u2:setup --no-interaction --tenant=test

dump-initial-db: ## Reset the data and cache for a tenant
	./develop exec db sh -c "mysqldump -u u2 -p --no-tablespaces --default-character-set=utf8mb4 u2_dev > /migrations/initial_db_dump.sql"

trans-update: ## Update translation files
	./develop console translation:extract de --sort --force --format=xlf20 --clean
	./develop console translation:extract en --sort --force --format=xlf20 --clean
	./develop console bazinga:js-translation:dump ./shared/translation --pattern="/{domain}.{_format}" --format=json --merge-domains
	./develop exec php sh -c "jq . shared/translation/de.json > shared/translation/de.json.tmp && mv shared/translation/de.json.tmp shared/translation/de.json"
	./develop exec php sh -c "jq . shared/translation/en.json > shared/translation/en.json.tmp && mv shared/translation/en.json.tmp shared/translation/en.json"

route-update: ## Update shared route files
	./develop console fos:js-routing:dump --format=json --target=shared/routing/routes.json
	./develop exec php sh -c "jq . shared/routing/routes.json > shared/routing/routes.json.tmp && mv shared/routing/routes.json.tmp shared/routing/routes.json"

api-docs: ## Dump the JSON representation of the OpenAPI documentation into a file
	./develop exec php sh -c 'bin/console api:openapi:export --output=openapi.json --spec-version=3 && echo "" >> openapi.json'


## —— Static Analysis ——
static-analysis: static-analysis-api static-analysis-client hadolint ## Runs all static analysis tools for the project

static-analysis-api: csfixer phpstan ## Runs all static analysis tools for the API

static-analysis-client: ## Runs all static analysis tools for the client
	cd client; \
	pnpm type-check && pnpm eslint:fix && pnpm stylelint:fix && pnpm prettier:fix

phpstan: ## Run phpstan with the project configuration
	./develop composer phpstan

phpstan-baseline: ## Update the phpstan baseline with the current violations
	./develop composer phpstan-baseline

csfixer: ## Run php-cs-fixer
	./develop composer php-cs-fixer

hadolint: ## Run hadolint on the Dockerfile
	docker run --rm -i hadolint/hadolint < Dockerfile

## —— Local Utilities ——
analyze-bundle: ## Create a report of the build time and the generated JS. Output at `var/logs/webpack.report.html`
	cd client; \
	pnpx vite-bundle-visualizer

storybook-local: ## Run storybook on local host
	cd client; \
	pnpm start-storybook -p 8021

spx: ## Reset the spx data
	./develop exec php sh -c "rm -rf /tmp/spx"
	open "https://dev.u2.localhost/legacy/?SPX_KEY=dev&SPX_UI_URI=/"

vendor-patches: ## Generate vendor patches
	./develop exec php sh -c "vendor/bin/vendor-patches generate"
